<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Screen2</title>
    <style lang="less">
      @font-face {
        font-family: DinMedium;
        src: url('./fonts/DinMedium.ttf');
      }

      @font-face {
        font-family: SourceHanSansCN;
        src: url('./fonts/SourceHanSansCN.ttf');
      }

      body {
        font-family: DinMedium, SourceHanSansCN, sans-serif;
      }

      * {
        margin: 0;
        padding: 0;
      }

      .pc_scr {
        width: 100%;
        height: 100%;
        background: url(../../image/pc_screen2_bg.png) no-repeat;
        color: @themeFontColor;
        background-size: cover;
        overflow: hidden;
      }

      .pc_advert_scr {
        width: 100%;
        height: 100%;
        color: @themeFontColor;
        background: url(../../image/pc_login_bg.png) no-repeat;
        background-size: cover;
        overflow: hidden;
      }

      .pc_scr1 {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .pc_advert_scr1 {
        width: 100%;
        height: 100%;
        padding: 40px 5px 40px 5px;
      }

      .pc_scr11 {
        width: 100%;
        background: #fff;
        height: 100%;
        float: left;
        /* padding: 30px; */
        overflow: hidden;
        position: relative;
        border-radius: 4px;
      }

      .pc_advert_scr11 {
        width: 380px;
        background: #fff;
        height: calc(100% - 168px);
        float: left;
        padding: 30px;
        overflow: scroll;
        position: relative;
        padding-bottom: 145px;
        border-radius: 4px;
      }

      .pc_scr11 table {
        font-size: 18px;
        color: @themeFontColor;
        text-align: center;
        line-height: 50px;
        padding-left: 10px;
        padding-right: 10px;
      }

      .pc_scr12 {
        position: absolute;
        width: calc(100% + 5px);
        z-index: 10;
      }

      .pc_scr13 {
        width: 100%;
        height: 50px;
        line-height: 50px;
        background: #fff;
        padding-left: 10px;
        padding-right: 10px;
      }

      .pc_scr14 {
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
      }

      .pc_scr14 table {
        width: 100%;
      }

      .pc_scr14 table tr {
        height: 62px;
        line-height: 62px;
      }

      .pc_scr14 table tr td {
        line-height: 26px;
      }

      .pc_advert_scr14 {
        width: 100%;
        height: 100%;
        overflow-y: scroll;
      }

      .pc_advert_scr14 table {
        width: 100%;
        margin-top: 60px;
      }

      .pc_advert_scr14 table tr {
        height: 45px;
      }

      .pc_scr15 {
        position: absolute;
        right: 10px;
        height: 100px;
        line-height: 100px;
        bottom: 0;
        text-align: right;
      }

      .pc_advert_scr15 {
        position: absolute;
        right: 10px;
        height: 100px;
        line-height: 100px;
        bottom: 0;
        text-align: right;
      }

      .pc_scr15_left {
        left: 0;
      }

      .pc_advert_scr15_left {
        left: 5px;
      }

      .pc_scr16 {
        font-size: 34px;
        font-weight: bold;
        margin-left: 11px;
      }

      .pc_advert_scr16 {
        font-size: 20px;
        font-weight: bold;
        margin-left: 5px;
      }

      .pc_scr19 {
        float: left;
        font-size: 20px;
        margin-left: 20px;
        line-height: 115px;
      }

      .pc_scr3 {
        width: 60px;
        height: 60px;
        background: #fff;
        border-radius: 50%;
        float: left;
        color: #b4995a;
        font-size: 30px;
        font-weight: bold;
        line-height: 60px;
        text-align: center;
        margin-top: 21px;
        margin-left: 84px;
      }

      .pc_scr31 {
        margin-left: 45px;
        float: left;
      }

      .pc_scr32 {
        margin-top: 24px;
        font-size: 20px;
        font-weight: bold;
        line-height: 20px;
      }

      .pc_scr33 {
        line-height: 16px;
        margin-top: 19px;
      }

      .pc_scr34 {
        width: 100%;
        text-align: center;
        font-size: 18px;
        color: #567486;
        line-height: 18px;
        margin-top: 19px;
      }

      .pc_scr35 {
        background: url(./img/pc_member_bg.png) no-repeat;
        /* background: #567486; */
        width: 416px;
        height: 110px;
        margin: 15px;
        border-radius: 8px;
      }

      .pc_scr36 {
        display: flex;
        color: @themeBackGroundColor;
      }

      .pc_scr37 {
        margin-top: 15px;
        line-height: 18px;
        font-size: 18px;
        color: #e8d7b0;
      }

      .pc_scr371 {
        margin-top: 12px;
        line-height: 16px;
        font-size: 16px;
        color: #e8d7b0;
      }

      .pc_scr38 {
        margin-top: 12px;
        line-height: 16px;
        font-size: 16px;
        font-weight: normal;
        color: #e8d7b0;
      }

      .pc_scr38 span {
        margin-right: 25px;
      }

      .pc_scr39 {
        width: 120px;
        height: 50px;
        background: linear-gradient(90deg, #d2ba8a 0%, #b49a5a 100%);
        border-radius: 4px;
        font-size: 20px;
        font-weight: bold;
        color: #ffffff;
        line-height: 50px;
        text-align: center;
        float: right;
        margin-top: 35px;
        margin-right: 40px;
        cursor: pointer;
      }

      .pc_scr40 {
        width: 70px;
        height: 70px;
        background: linear-gradient(90deg, #d2ba8a 0%, #b49a5a 100%);
        border-radius: 50%;
        float: left;
        font-size: 30px;
        font-weight: bold;
        color: #ffffff;
        line-height: 70px;
        text-align: center;
        margin: 20px;
      }

      .pc_prod_div {
        width: 90%;
        height: 88px;
        margin: 15px 10px 0 20px;
        font-size: 18px;
        line-height: 18px;
        font-weight: 600;
        color: @themeFontColor;
        border-bottom: 1px dashed #e7eaef;
      }

      .pc_prod_name {
        height: 50%;
      }

      .pc_prod_second {
        height: 50%;
        display: flex;
        justify-content: space-between;
        padding-top: 15px;
      }

      .pc_prod_amt {
        color: #ff6159;
      }

      .pc_src_footer {
        background-color: #f5f8fb;
        width: 100%;
        height: 144px;
      }

      .pc_src_reduceAmt {
        height: 50%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
      }

      .pc_src_reduceAmt_left {
        font-size: 20px;
        line-height: 24px;
        font-style: normal;
        font-weight: 600;
        color: @themeFontColor;
      }

      .pc_src_reduceAmt_right {
        font-size: 20px;
        line-height: 24px;
        font-weight: 600;
        color: #ff6159;
      }

      .pc_src_finalAmt {
        height: 50%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
      }

      .pc_src_finalAmt_left {
        font-size: 24px;
        line-height: 24px;
        font-weight: bold;
        color: @themeFontColor;
      }

      .pc_src_finalAmt_right {
        font-size: 36px;
        line-height: 36px;
        font-style: normal;
        font-weight: 700;
        color: #ff6159;
      }
    </style>
  </head>

  <body>
    <div id="screen2" style="width: 100%;"></div>

    <script>
      // 禁止鼠标右击查看源代码
      document.oncontextmenu = () => {
        return false;
      };
      document.ondrop = () => {
        return false;
      };
      document.ondragenter = () => {
        return false;
      };
      document.ondragover = () => {
        return false;
      };
      var autoScrollInterval;
      var flgBegin = false;
      var flgBack = false;
      var toTopTime;
      var beginTime;
      var screenHeight = window.screen.height;
      let weighUnitList = ['斤', '克', '两', '千克', '公斤', 'g', 'G', 'kg', 'Kg', 'kG', 'KG'];

      function scrollToTop() {
        // 延迟返回顶部
        if (flgBack) {
          return;
        }
        flgBack = true;
        toTopTime = setTimeout(() => {
          flgBack = false;
          flgBegin = false;
          document.getElementById('screen2_goods_list_id').scrollTop = 0;
        }, 3000);
      }
      function scrollBegin() {
        // 延迟开始滚动
        if (flgBegin) {
          return;
        }
        beginTime = setTimeout(() => {
          flgBegin = true;
        }, 3000);
      }
      function autoScroll() {
        var parent = document.getElementById('screen2_goods_list_id');
        var child1 = document.getElementById('prodListTab');
        autoScrollInterval = setInterval(function() {
          if (flgBegin) {
            parent.scrollTop++;
          } else {
            scrollBegin();
          }
          if (parent.scrollHeight - parent.scrollTop === parent.clientHeight) {
            scrollToTop();
          }
        }, 20);
      }
      function makeHtml(Screen2data) {
        var screen2ShowList = Screen2data.screen2ShowList === null ? [] : Screen2data.screen2ShowList;
        // 优惠金额
        var screen2ReducePrice = Screen2data.screen2ReducePrice;
        // 总金额
        var screen2TotalPrice = Screen2data.screen2TotalPrice;
        // 显示实收金额
        var screen2ReceiveMoney = Screen2data.screen2ReceiveMoney;
        // 主屏是否为退货状态
        var screen2ReturnGoods = Screen2data.screen2ReturnGoods;
        // 应付金额（可能被去0）
        var screen2ShowFinalPrice = Screen2data.screen2ShowFinalPrice;
        var screen2ShowMember = Screen2data.screen2ShowMember;
        var screen2MemberMoney = Screen2data.screen2MemberMoney;
        var screen2MemberPayType = Screen2data.screen2MemberPayType;
        var screen2ShowFollowErweima = Screen2data.screen2ShowFollowErweima;
        var screen2ErweimaName = Screen2data.screen2ErweimaName;
        var screen2ErweimaMobile = Screen2data.screen2ErweimaMobile;
        var screen2FollowErweima = Screen2data.screen2FollowErweima;
        var screen2isSinglePayWay = Screen2data.screen2isSinglePayWay;
        var screen2MemberName = Screen2data.screen2MemberName;
        var screen2MemberPoint = Screen2data.screen2MemberPoint;
        var screen2MemberPhone = Screen2data.screen2MemberPhone;

        var show_advert = show_advert;
        if (screen2ShowFollowErweima) {
          var erweima =
            '<div style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;display: ' +
            (screen2ShowFollowErweima ? '' : 'none') +
            '">' +
            '<div style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0, 0, 0, 0.5);"></div>' +
            '<div style="font-size: 16px;color: #567485;position: relative;z-index: 800;height: 557px;margin: 0 auto;' +
            'margin-top: 100px;background: #fff;width: 500px;overflow: hidden;border-radius: 12px;">' +
            '<div style="height: 100px;background: linear-gradient(90deg, #D2BA8A 0%, #B49A5A 100%);color: #FFF;">' +
            '<div class="pc_scr3">' +
            screen2ErweimaName.substring(0, 1) +
            '</div>' +
            '<div class="pc_scr31">' +
            '<div class="pc_scr32">' +
            screen2ErweimaName +
            '</div>' +
            '<div class="pc_scr33">' +
            screen2ErweimaMobile.substring(0, 3) +
            '****' +
            screen2ErweimaMobile.substring(7, 11) +
            '</div>' +
            '</div>' +
            '</div>' +
            '<div style="width: 335px;height: 335px;margin: 0 auto;margin-top: 33px;">' +
            '<img src="' +
            screen2FollowErweima +
            '" style="width: 335px;height: 335px;" />' +
            '</div>' +
            '<div class="pc_scr34">扫描二维码关注公众号，及时掌握账户变动信息</div>' +
            '</div>' +
            '</div>';
          document.getElementById('screen2').innerHTML = erweima;
        } else {
          var member_html =
            '<div class="pc_scr35" style="' +
            (screen2ShowMember ? '' : 'display: none') +
            '">' +
            '<div class="pc_scr36"><div class="pc_scr40">' +
            screen2MemberName.substring(0, 1) +
            '</div>' +
            '<div><div class="pc_scr37">' +
            screen2MemberName +
            '</div>' +
            '<div class="pc_scr371"> ' +
            screen2MemberPhone.substring(0, 3) +
            '****' +
            screen2MemberPhone.substring(7, 11) +
            '</div>' +
            '<div class="pc_scr38"><span>余额: ' +
            Number(screen2MemberMoney).toFixed(2) +
            '</span>' +
            '<span>积分:' +
            screen2MemberPoint +
            '</span></div></div>' +
            '</div></div>';

          var prod_html =
            '<div class="' +
            (show_advert ? 'pc_advert_scr' : 'pc_scr') +
            '">' +
            '<div class="' +
            (show_advert ? 'pc_advert_scr1' : 'pc_scr1') +
            '">' +
            '<div class="' +
            (show_advert ? 'pc_advert_scr11' : 'pc_scr11') +
            '">' +
            '<div id="screen2_goods_list_id" class="' +
            (show_advert ? 'pc_advert_scr14' : 'pc_scr14') +
            '">';
          var prod_contain_html = '';
          for (var i = 0; i < screen2ShowList.length; i++) {
            var unitName = screen2ShowList[i].unitName;
            prod_contain_html +=
              '<div class="pc_prod_div"><div class ="pc_prod_name">' +
              screen2ShowList[i].name +
              '</div><div class="pc_prod_second"><div class="pc_prod_saleAmt">￥' +
              (!screen2ShowMember || screen2MemberPayType == 1 || (screen2ShowList[i].isMemberDayGoods == true && screen2ShowMember)
                ? Number(screen2ShowList[i].salePrice).toFixed(2)
                : Number(screen2ShowList[i].vipPrice) == 0
                ? Number(screen2ShowList[i].salePrice).toFixed(2)
                : Number(screen2ShowList[i].vipPrice).toFixed(2)) +
              '</div>' +
              '<div class ="pc_prod_num"> ×' +
              (weighUnitList.indexOf(unitName) > -1 ? Number(screen2ShowList[i].number).toFixed(3) : Number(screen2ShowList[i].number).toFixed(0)) +
              '</div>' +
              '<div class="pc_prod_amt">￥' +
              Number(screen2ShowList[i].amt).toFixed(2) +
              '</div>' +
              '</div></div>';
          }
          var prod_footer = '</div></div></div></div>';

          // var head_html = '<div class="' + (show_advert ? 'pc_advert_scr' : 'pc_scr') + '">' +
          //'<div class="' + (show_advert ? 'pc_advert_scr1' : 'pc_scr1') + '">' +
          //'<div class="' + (show_advert ? 'pc_advert_scr11' : 'pc_scr11') + '">' +
          //'<div id="screen2_goods_list_id" class="' + (show_advert ? 'pc_advert_scr14' : 'pc_scr14') + '">' +
          //'<table class="prodListTab" id="prodListTab" border="0"
          //style="table-layout:fixed;" cellspacing cellpadding>';

          // var mid_html = '';
          // for (var i = 0; i < screen2ShowList.length; i ++) {
          //   var unitName = screen2ShowList[i].unitName;
          //   mid_html += '<tr>' +
          // <td style="width: 34%;text-align: left;word-break:break-all;">' + screen2ShowList[i].name + '</td>' +
          // '<td style="width: 16%;text-align: right;">' +
          // ((unitName == '克' || unitName == '千克' || unitName == '两' || unitName == '斤')
          //  ? Number(screen2ShowList[i].number).toFixed(3) : Number(screen2ShowList[i].number).toFixed(2)) +
          //  '</td>' +
          //  ((!screen2ShowMember || screen2MemberPayType == 1 ||
          //  (screen2ShowList[i].isMemberDayGoods == true && screen2ShowMember))
          //  ? '<td style="width: 16%;text-align: right;">' + Number(screen2ShowList[i].salePrice).toFixed(2) + '</td>'
          //  : '<td style="width: 16%;text-align: right;">' + (screen2ShowList[i].vip_price == 0
          //  ? Number(screen2ShowList[i].salePrice).toFixed(2)
          //  : Number(screen2ShowList[i].vip_price).toFixed(2)) + '</td>') +

          //  '<td style="width: 16%;text-align: right;">' + screen2ShowList[i].disc + '%</td>' +
          //  '<td style="width: 18%;text-align: right;">' + Number(screen2ShowList[i].amt).toFixed(2) + '</td>' +
          //  '</tr>'
          // };

          var foot_html =
            '<div class="pc_src_footer"><div class="' +
            (show_advert ? 'pc_src_ad_reduceAmt' : 'pc_src_reduceAmt') +
            '" style="visibility:' +
            (screen2ShowList[0]?.name !== '会员充值' ? '' : 'hidden') +
            '">' +
            '<div class="pc_src_reduceAmt_left" style="display:' +
            (!screen2ReturnGoods ? '' : 'none') +
            '">已优惠：</div> ' +
            '<div class="pc_src_reduceAmt_left" style="display:' +
            (screen2ReturnGoods ? '' : 'none') +
            '">其他减项：</div>' +
            '<div class="pc_src_reduceAmt_right" >-￥' +
            Number(screen2ReducePrice).toFixed(2) +
            '</div></div>' +
            '<div class="' +
            (show_advert ? 'pc_src_ad_reduceAmt' : 'pc_src_finalAmt') +
            '">' +
            '<div class="pc_src_finalAmt_left" style="display:' +
            (!screen2ReturnGoods ? '' : 'none') +
            '">应付金额：</div> ' +
            '<div class="pc_src_finalAmt_left" style="display:' +
            (screen2ReturnGoods ? '' : 'none') +
            '">应退金额：</div>' +
            '<div class="pc_src_finalAmt_right">￥' +
            Number(screen2ShowFinalPrice).toFixed(2) +
            '</div></div>' +
            '</div>';

          // var foot_html = '</table>' +
          //       '</div>' +
          //       '<div style="width:100%;height:10px;background: #e8e8e8;border-radius:4px;"></div>' +
          //       '<div ' +
          //         'class="' + (show_advert ? 'pc_advert_scr15 pc_advert_scr15_left' : 'pc_scr15') + '" ' +
          //         'style="bottom: 50px;text-align: right;bottom: 150px;width: 400px;display: ' +
          //           (screen2ShowMember && !screen2ReturnGoods ? '' : 'none') + '"' +
          //       '>' +
          //         '<span style="font-size: 20px;font-weight: bold;">会员卡现额： </span>' +
          //         '<span class="' + (show_advert ? 'pc_advert_scr16' : 'pc_scr16') + '">¥' +
          //         Number(screen2MemberMoney).toFixed(2) + '</span>' +
          //       '</div>' +

          //       '<div ' +
          //         'class="' + (show_advert ? 'pc_advert_scr15 pc_advert_scr15_left' : 'pc_scr15') + '" ' +
          //         'style="text-align: right;width: 400px;bottom: 100px;display: ' +
          //           (screen2ShowMember && !screen2ReturnGoods && screen2isSinglePayWay ? '' : 'none') + '" ' +
          //       '>' +
          //         '<span style="font-size: 20px;font-weight: bold;">会员卡剩余： </span>' +
          //         '<span ' +
          //           'class="' + (show_advert ? 'pc_advert_scr16' : 'pc_scr16') + '"' +
          //           'style="color: #fd5f51;"' +
          //         '>¥' + (Number(screen2MemberMoney) - Number(screen2ShowFinalPrice)).toFixed(2) + '</span>' +
          //       '</div>' +

          //       '<div class="' + (show_advert ? 'pc_advert_scr15' : 'pc_scr15') + '" style="bottom: 50px;">' +
          //         '<span style="font-size: 20px;font-weight: bold;display: ' +
          //           (!screen2ReturnGoods ? '' : 'none') + '">已优惠： </span>' +
          //         '<span style="font-size: 20px;font-weight: bold;display: ' +
          //           (screen2ReturnGoods ? '' : 'none') + '">其他减项： </span>' +
          //         '<span ' +
          //           'class="' + (show_advert ? 'pc_advert_scr16' : 'pc_scr16') + '"' +
          //           'style="color: #fd5f51;"' +
          //         '>¥' + Number(screen2ReducePrice).toFixed(2) + '</span>' +
          //       '</div>' +

          //       '<div class="' + (show_advert ? 'pc_advert_scr15' : 'pc_scr15') + '">' +
          //         '<span style="font-size: 20px;font-weight: bold;display: ' +
          //           (!screen2ReturnGoods ? '' : 'none') + '">应付金额： </span>' +
          //         '<span style="font-size: 20px;font-weight: bold;display: ' +
          //           (screen2ReturnGoods ? '' : 'none') + '">应退金额： </span>' +
          //         '<span class="' + (show_advert ? 'pc_advert_scr16' : 'pc_scr16') + '">¥' +
          //           Number(screen2ShowFinalPrice).toFixed(2) + '</span>' +
          //       '</div>' +
          //     '</div>' +
          //   '</div>' +
          // '</div>'
          // document.getElementById('screen2').innerHTML = member_html + head_html + mid_html + foot_html;
          document.getElementById('screen2').innerHTML = member_html + prod_html + prod_contain_html + prod_footer + foot_html;
          let goodlistObj = document.getElementById('screen2_goods_list_id');
          if (screen2ShowMember) {
            document.getElementsByClassName('pc_scr1')[0].style.minHeight = screenHeight - 390 + 'px';
            document.getElementsByClassName('pc_scr11')[0].style.minHeight = screenHeight - 290 + 'px';
            goodlistObj.style.height = screenHeight - 300 + 'px';
          } else {
            document.getElementsByClassName('pc_scr1')[0].style.minHeight = screenHeight - 250 + 'px';
            document.getElementsByClassName('pc_scr11')[0].style.minHeight = screenHeight - 150 + 'px';
            goodlistObj.style.height = screenHeight - 170 + 'px';
          }
          goodlistObj.scrollTop = goodlistObj.scrollHeight;
          clearInterval(autoScrollInterval);
          clearTimeout(toTopTime);
          clearTimeout(beginTime);
          flgBegin = false;
          flgBack = false;
          if (screen2ShowList.length !== 0) {
            autoScroll();
          }
        }
      }

      // var Screen2data = {
      //   screen2ShowFollowErweima: true,
      //   screen2ErweimaName: '张三',
      //   screen2ErweimaMobile: '13044556677',
      //   screen2FollowErweima: 'https://img01.jituwang.com/190516/256580-1Z5160A22327.jpg',
      //   screen2ShowList: [{"id":"1","sumQty":"1","sumAmt":"1.62","name":"农夫山泉天然水550ml",
      //   "costAmt":"0","url":"https://dev.zhangguizhinang.com/upload/products/466c137b191679c36be276bbb14bb937.jpg",
      //   "localUrl":"","code":"6921168509256","cur_stock":"272","unit_id":"2","unitName":"瓶","salePrice":"2",
      //   "pur_price":"0","vip_price":"1","init_price":"0","type_id":"6","type_name":"饮料","min_stock":"0",
      //   "max_stock":"0","img_fingerprint":"0aa90ecd05df4b1c5e497988b16cfebb","pinyin":"农夫山泉",
      //   "supplier_id":"5","init_stock":"0","init_amt":"0","has_image":"1",
      //   "fingerprint":"612d738f5ee9090b3b22a4490f68aab3","isMemberDayGoods":false,
      //   "amt":8,"number":4,"disc":"100"},
      //   {"id":"4","name":"桃李鸡蛋肉松面包120g","code":"6925648900274","unit_id":"","unitName":"","type_id":"2",
      //   "type_name":"其他类别","pur_price":"0","salePrice":"4","pinyin":"啊啊啊啊","init_price":"4","init_stock":"0",
      //   "init_amt":"0","vip_price":"0","cur_stock":"1","min_stock":"0","max_stock":"0","has_image":"0","url":"",
      //   "localUrl":"","img_fingerprint":"f0fb378d4c0fae5ba322326486226b60",
      //   "supplier_id":"0","company_name":"","fingerprint":"cdce153cbe7978cea5ab248dbfccb750",
      //   "status":"启用","isMemberDayGoods":false,"amt":12,"number":3,"disc":"100"}],
      //   // 优惠金额
      //   screen2ReducePrice: '10',
      //   // 总金额
      //   screen2TotalPrice: '30',
      //   // 显示实收金额
      //   screen2ReceiveMoney: '40',
      //   // 主屏是否为退货状态
      //   screen2ReturnGoods: false,
      //   // 应付金额（可能被去0）
      //   screen2ShowFinalPrice: '20',
      //   screen2ShowMember: true,
      //   screen2MemberMoney: '100',
      //   screen2MemberPayType: '1',
      //   show_advert: false
      // };
      // makeHtml(Screen2data);
    </script>
  </body>
</html>
