const message = {
  scan_fail: '扫描失败', // Scann<PERSON> failed:
  tel: 'Tel:18554040905',

  selected_product_delete_confirm: '确定要删除选中商品吗？',
  delete_purchase_confirm: '确定删除吗？', // '确定作废吗？'
  exit_confirm: '确认退出吗？',

  new_password_is_same_as_original_password: '新密码与原密码相同，请确认',
  new_passwords_are_different: '新密码两次输入不一致',
  new_password_min_length_not_met: '新密码（长度为8～20个字符）由数字、字母组成',
  original_password_min_length_not_met: '原密码长度不能小于6位',
  confirm_password_min_length_not_met: '确认密码（长度为8～20个字符）由数字、字母组成',
  password_edit_success: '密码修改成功',
  password_edit_failure: '密码修改失败',
  login_failure: '手机号或密码错误',
  login_network_failure: '服务器连接断开，请重新登录',
  login_network_title: '网络连接异常，请检查网络！',

  nothing_selected: '请先勾选商品',
  price_cannot_be_null: '售价不可为空',
  price_must_be_number: '售价应为纯数字',
  cost_must_be_number: '进价应为纯数字',
  amount_cannot_be_zero: '收款不可为零',
  amount_maximum_exceed: '输入金额不能大于100000',
  product_name_cannot_be_null: '商品名不可为空',
  classification_already_exists: '该分类已存在',
  classification_cannot_be_null: '分类名称不可为空',
  image_maxsize_exceeded: '图片大小不能超过5M',
  product_cannot_delete: '商品使用中，不允许删除',
  reselect_product: '请重新选择类别',

  product_create_success: '新增商品成功',
  product_create_failure: '新增商品失败,商品名称或条码已经存在，请重新输入！',
  batch_delete_product_success: '批量删除商品成功',
  batch_delete_product_failure: '批量删除商品失败',
  batch_ban_product_success: '批量删除商品成功',
  batch_ban_product_failure: '批量删除商品失败',
  batch_update_price_success: '批量修改价格成功!',
  batch_update_price_failure: '批量修改价格失败',
  sync_success: '数据同步成功',
  sync_failure: '数据同步失败',
  save_success: '保存成功',
  save_failure: '保存失败',
  delete_success: '删除成功',
  delete_failure: '删除失败',
  delete_returns_detail_success: '删除退货单详情成功',
  delete_returns_detail_failure: '删除退货单详情失败',
  delete_purchase_detail_success: '删除进货单详情成功',
  delete_purchase_detail_failure: '删除进货单详情失败',
  stock_update_success: '库存修正完成',
  stock_goodsnumber: '商品进货数量不能为0',
  search_failure: '检索失败',

  cash_box_in_error: '检测失败，请确认钱箱设备及配置',
  printer_error: '打印设备异常，请检查打印机',
  select_blue_printer: '请选择蓝牙打印机',
  blue_printer_error: '蓝牙打印机检测异常，请确认蓝牙设置',

  read_and_confirm_privacy_statement: '请先阅读隐私声明和用户服务协议并同意',
  confirm_privacy_statement: '请确认隐私声明',
  enter_price: '请填写商品售价',
  enter_product_name: '请填写商品名称',
  enter_user_name: '请输入真实姓名',
  enter_employee_number: '请输入员工号',
  enter_password_again: '请再次输入新密码（长度为8～20个字符）由数字、字母组成',
  enter_mobile_number: '请输入正确的手机号',
  enter_original_password: '请输入原密码',
  enter_new_password: '请输入新密码（长度为8～20个字符）由数字、字母组成', // 请输入新密码,请输入长度不小于6位的新密码
  enter_confirm_password: '请输入确认密码（长度为8～20个字符）由数字、字母组成',
  enter_password: '请输入密码（长度为8～20个字符）由数字、字母组成', // 请输入密码,请输入长度不小于8位的密码,请输入长度不大于32位的密码
  enter_user_password: '请输入密码',
  enter_store_address: '请输入门店地址',
  enter_store_name: '请输入门店名称',
  enter_verification_code: '请输入验证码',
  right_length_verification_code: '请输入六位验证码',
  select_printer: '请选择打印机', // 请选择小票打印机
  select_product: '请选择商品', // 请先选择商品
  select_port: '请选择端口',
  select_business_type: '请选择经营业态',
  scan_product: '请将扫描框对准条形码进行扫描',
  cash_box_not_set: '钱箱未设置，请在首页设置里配置钱箱',
  not_setting_small_printer: '未设置小票打印机，无法打印',
  not_setting_barcode_printer: '未设置条码打印机',
  not_setting_label_printer: '未设置标签打印机',
  moneybox_check_success_other_small_printer: '检查成功，若钱箱未弹出，请确认钱箱已解锁，或尝试其它小票打印机',
  moneybox_check_success_other_port: '检查成功，若钱箱未弹出，请确认钱箱已解锁，或尝试其它端口',

  get_aid_fail: '获取aid失败',
  get_devicecode_fail: '获取devicecode失败',
  password_error: '密码错误',
  not_employee_permissions: '暂无此权限，请联系管理员',
  not_refund_permissions: '暂无此权限，请联系管理员授权',
  stock_not_enough: '部分商品库存不足，请修改后结算',
  received_cannot_less_than_receivable: '实收不能小于应收',
  electronic_scale_in_error: '电子秤设备异常，请检查电子秤！',
  product_weight_cannot_zero: '商品重量不能为0',
  num_max: '数值不得大于99999',
  update_goods_fail: '修改商品失败,无修改权限',

  select_vip: '请选择会员',
  enter_name: '请输入姓名',
  enter_phone: '请输入手机号',
  enter_pay_code: '请输入6位支付码',

  no_import_data: '没有需要导入的数据',
  max_import_data: '最大只支持5000条数据导入',
  support_suffixs: '请选择{suffixs}类型的文件',
  supplierNameAlreadyExists: '该供应商名称已存在',

  enter_supplier_name: '请输入供应商名称',
  can_not_exceed_5m: '导入文件内容不能超过5M'
};

export default message;
