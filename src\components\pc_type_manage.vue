<style lang='less' scoped>
/*被拖拽对象的样式*/
.pc_item {
  padding: 6px;
  background-color: #fff;
  cursor: move;
  margin: 5px 10px 0px !important;
  border-radius: 8px;
  height: 39px;
  color: @themeFontColor;
  font-size: 16px;
  display: flex;
  align-items: center;
}
/*选中样式*/
.pc_chosen {
  background: @themeBackGroundColor;
  color: #fff;
  max-height: 600px;
}
.is_active {
  background: @themeBackGroundColor;
  color: #fff;
}
.pc_type_66 {
  position: relative;z-index: 800;height: 614px;margin: 0 auto;background: #FFF;
  width: 681px;overflow: hidden;border-radius: 6px;color:@themeFontColor;
}
.pc_type_67 {
  display: flex;
  justify-content: space-between;
  height: 62px;
  font-size: 18px;
  font-weight: 600;
  line-height: 60px;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 20px;
}
.pc_type_68 {
  border: 1px solid #E3E6EB;
  width: 320px;
  height: 462px;
}
.pc_type_addType {
  margin: 10px;
  background-color: @themeButtonBackGroundColor;
  font-size: 16px;
  font-weight: 500;
  color: @themeBackGroundColor;
  text-align: center;
  height: 39px;
  width: 300px;
  border-radius: 8px;
  line-height: 36px;
  cursor: pointer;
}
.pc_type_iconDiv {
  display: inline-block;
}
.pc_type_iconDiv i{
  font-size: 20px;margin-left: 5px;cursor:pointer;
}
.pc_type_addDiv {
  width: 450px;
  height: 547px;
  background-color: #fff;
  margin: 0 auto;
}
.pc_type_icon_container_div {
  margin: 10px 0px 10px 30px;
  width: 410px;
  height: 250px;
}
.pc_type_icon_div {
  width: 70px;
  height: 70px;
  display: inline-block;
  margin-right: 32px;
  margin-bottom: 20px;
  cursor: pointer;
  border: 1px solid #fff;
  border-radius: 4px;
}
.pc_type_icon_div img {
  border: 1px solid #fff;
}
/* .pc_type_icon_div img{
  filter: drop-shadow(#ffffff 80px 0);
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
}
.pc_type_icon_div img:hover{
  background-color: gray;
} */
.icon_choose {
  background-color: #CFA26B, linear-gradient(#CFA26B,#CFA26B) !important;
  background-blend-mode: lighten;
  color: #fff;
}
.icon_choose img{
  border: 1px solid #CFA26B !important;
  border-radius: 4px;
}
.pc_type_add_cancle {
  margin-left: 160px;
  width: 120px;
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  color: @themeBackGroundColor;
  border: 1px solid @themeBackGroundColor;
  line-height: 46px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  margin-top: 40px;
}
.pc_type_add_submit {
  margin-left: 20px;
  width: 120px;
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  background-color: @themeBackGroundColor;
  color: #fff;
  line-height: 46px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
}
.draggable_div {
  max-height: 400px;
  overflow-y: auto;
}
.saveTypeManage {
    width: 400px;
    height: 44px;
    text-align: center;
    line-height: 40px;
    background: @themeBackGroundColor;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    margin: 0 auto;
    font-size: 18px;
}
#addType {
  color: @themeBackGroundColor;
}
.com_pmcu22 {
  overflow: hidden;
  margin-top: 35px;
  font-size: 16px;
  letter-spacing: 4px;
}
.com_pmcu25 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
}
.com_pmcu26 {
  width: 372px;
  height: 217px;
  background: #fff;
  margin: 0 auto;
  margin-top: 115px;
  position: relative;
  border-radius: 4px;
}
.com_pmcu27 {
  height: 85px;
  line-height: 85px;
  text-align: center;
  font-size: 18px;
  color: #567485;
}
.com_pmcu32 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  margin-left: 92px;
  float: left;
  border-radius: 4px;
  text-align: center;
  font-size: 24px;
  letter-spacing: 0px;
  cursor: pointer;
  background: #567485;
  color: #fff;
}
.com_pmcu33 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  border: 1px solid #bda16a;
  text-align: center;
  margin-left: 14px;
  background: #bda16a;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 24px;
  letter-spacing: 0px;
}
</style>

<template>
  <div>
    <div
      v-show='showTypeManage'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);display:flex;align-items: center;'
      >
        <div class="pc_type_66">
          <!-- <div>{{drag?'拖拽中':'拖拽停止'}}</div> -->
          <div class="pc_type_67" style="border: none;"><span>分类管理</span>
          <span @click="closeTypeManage" style="font-size: 36px;font-weight: 200;cursor: pointer;">×</span></div>
          <!--使用draggable组件-->
          <div style="display:flex;justify-content: between-space;margin-top:10px;">
            <div class="pc_type_68" style="margin-left: 20px;border-radius: 4px 0 0 4px;">
              <div class="pc_type_addType" @click="showAddTypeBox(1)"><span style="font-size: 20px;">+</span>&nbsp;&nbsp;新增分类</div>
              <draggable v-model="typeArr" chosenClass="pc_chosen" forceFallback="true" fallbackTolerance="12"
                delay="50" animation="500" @end="onEnd" class="draggable_div">
                <transition-group>
                  <div class="pc_item" :class="typeSelectedId === type.id ? 'is_active' : ''" @click="typeItemClick(type)"
                    v-for="type in typeArr" :key="type.id">
                    <div style="width: 40px;display: inline-block;">
                      <!-- <img :src="type.icon" v-show="typeSelectedId === type.id"
                        style="width: 39px;margin-top: -6px;margin-left: -10px;margin-left: -6px;"> -->
                    </div>
                    <div style="width: 145px;display: inline-block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                      {{type.name}}
                    </div>
                    <div class="pc_type_iconDiv" v-show="typeSelectedId === type.id && typeSelectedName !== '其他分类'">
                      <i class="el-icon-edit" @click="showEditTypeBox(1, type)"></i>
                      <i class="el-icon-delete" @click="deleteType(1, type)"></i>
                      <i class="el-icon-sort" @click="showMarkedWord"></i>
                      <!-- <img src="../../image/gg_edit_grey.png" /> -->
                    </div>
                  </div>
                </transition-group>
              </draggable>
            </div>
            <div class="pc_type_68" style="margin-right: 20px;border-radius: 0 4px 4px 0;">
              <div class="pc_type_addType" @click="showAddTypeBox(2)"><span style="font-size: 20px;">+</span>&nbsp;&nbsp;新增子分类</div>
              <draggable v-model="subTypeArr" chosenClass="pc_chosen" forceFallback="true" fallbackTolerance="12"
                delay="50" animation="500"  @end="onSubEnd" class="draggable_div">
                <transition-group>
                  <div class="pc_item" :class="subTypeSelectedId === stype.id ? 'is_active' : ''" @click="subTypeItemClick(stype)"
                    v-for="stype in subTypeArr" :key="stype.id">
                    <div style="width: 40px;display: inline-block;"></div>
                    <div style="width: 145px;display: inline-block;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
                      {{stype.name}}
                    </div>
                    <div class="pc_type_iconDiv" v-show="subTypeSelectedId === stype.id">
                      <i class="el-icon-edit" @click="showEditTypeBox(2, stype)"></i>
                      <i class="el-icon-delete" @click="deleteType(2, stype)"></i>
                      <i class="el-icon-sort" @click="showMarkedWord"></i>
                    </div>
                  </div>
                </transition-group>
              </draggable>
            </div>
          </div>
          <div style="overflow: hidden;margin-top: 15px;font-size: 24px;">
            <div class="saveTypeManage"
              @click='saveTypeManage'
            >确定</div>
          </div>
        </div>
      </div>
      <!-- 新增分类 -->
      <div
        v-show="showAddType"
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 500;'
      >
        <div
          style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);display:flex;align-items: center;'
        >
          <div class="pc_type_addDiv" style="height: 235px;">
            <div class="pc_type_67" id="addType"><span>新增{{ level === 1 ? '' : '子'}}分类</span>
            <span @click="closeTypeAdd" style="font-size: 36px;font-weight: 200;cursor: pointer;color: #567485">×</span></div>
            <div style="margin: 10px 20px;">
              <el-input id="type_add_input" ref="type_add_input" v-model.trim="type_add"
                maxlength="20" @focus="selectText('type_add_input')"
              @input="type_add = typeNameInput(type_add)" placeholder="10个字(20个字符)以内"></el-input>
            </div>
            <!-- <div v-show="level === 1">
              <span style="margin-left: 20px;font-size:16px;color: #c4c4c4;">请选择一个合适的图标作为商品的默认图片</span>
              <div class="pc_type_icon_container_div">
                <div class="pc_type_icon_div" :class="iconSelected === item.index ? 'icon_choose' : ''"
                  v-for="item in iconArr" :key="item.index" @click="iconClick(item)">
                  <img :src="item.image">
                </div>
              </div>
            </div> -->
            <div class="pc_type_add_cancle" @click="closeTypeAdd">取消</div>
            <div class="pc_type_add_submit" @click="addType">确定</div>
          </div>
        </div>
      </div>
      <!-- 编辑分类 -->
      <div
        v-show="showEditType"
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 500;'
      >
        <div
          style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);display:flex;align-items: center;'
        >
          <div class="pc_type_addDiv" style="height: 235px;">
            <div class="pc_type_67" id="addType"><span>编辑{{ level === 1 ? '' : '子'}}分类</span>
            <span @click="closeTypeEdit" style="font-size: 36px;font-weight: 200;cursor: pointer;color: #567485">×</span></div>
            <div style="margin: 10px 20px;"><el-input ref="editTypeInput" id="editTypeInput"
              @input="type_edit = typeNameInput(type_edit)"
              v-model.trim="type_edit" maxlength="20" placeholder="10个字(20个字符)以内"></el-input></div>
            <div class="pc_type_add_cancle" @click="closeTypeEdit">取消</div>
            <div class="pc_type_add_submit" @click="editType">确定</div>
          </div>
        </div>
      </div>
    </div>
    <!--删除类别或单位的最终确认提示弹出框-->
    <div v-show="show_delete_category" class="com_pmcu25" style="z-index: 500;">
      <div class="com_pmcu26" style="width: 450px;height: 300px;">
        <div class="com_pmcu27" style="font-size: 24px;font-weight: 700;color: #567485;">提示</div>
        <div style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;margin-top: 12px;color: #567485">
          该分类下有商品存在，
        </div>
        <div style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;color: #567485">
          商品也将一并删除。
        </div>
        <div class="com_pmcu22" style="margin-top: 39px;display: flex;justify-content: center;">
          <div
            class="com_pmcu32"
            style="width: 180px; height: 50px;margin: 0px;float: none; line-height: 50px;"
            @click="show_delete_category = false;"
          >取消删除</div>
          <div style="width:30px"></div>
          <div
            class="com_pmcu33"
            style="width: 180px; height: 50px;margin: 0px;float: none; line-height: 50px;"
            @click="continue_delete_category()"
          >一并删除</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import draggable from 'vuedraggable';

export default {
  components: {
    draggable
  },
  data() {
    return {
      level: 1,
      drag: false,
      typeSelectedId: '',
      typeSelectedName: '',
      typeSelectedFingerprint: '',
      subTypeSelectedFingerprint: '',
      editItem: {},
      subTypeSelectedId: '',
      subTypeSelectedName: '',
      // 定义要被拖拽对象的数组
      typeArr: [],
      subTypeArr: [],
      showAddType: false,
      showEditType: false,
      type_add: '',
      type_edit: '',
      iconArr: [{index: 1, name: '卫衣', image: require('../image/pc_typeIcon_1.png')},
        {index: 2, name: 'T恤', image: require('../image/pc_typeIcon_2.png')},
        {index: 3, name: '长裤', image: require('../image/pc_typeIcon_3.png')},
        {index: 4, name: '短裤', image: require('../image/pc_typeIcon_4.png')},
        {index: 5, name: '手套', image: require('../image/pc_typeIcon_5.png')},
        {index: 6, name: '背包', image: require('../image/pc_typeIcon_6.png')},
        {index: 7, name: '手提包', image: require('../image/pc_typeIcon_7.png')},
        {index: 8, name: '帽子', image: require('../image/pc_typeIcon_8.png')},
        {index: 9, name: '母婴', image: require('../image/pc_typeIcon_9.png')},
        {index: 10, name: '高跟鞋', image: require('../image/pc_typeIcon_10.png')},
        {index: 11, name: '童鞋', image: require('../image/pc_typeIcon_11.png')},
        {index: 12, name: '饰品', image: require('../image/pc_typeIcon_12.png')}
      ],
      iconUrl: '',
      iconSelected: '',
      addVal: '',
      editVal: '',
      unit: '',
      optionsList: [],
      inputing: false,
      adding: false,
      editing: false,
      setIndex: '',
      showDelete: false,
      showEdit: false,
      inputing_unit: false,
      show_add_unit: false,
      show_editing_unit: false,
      setDetail: '', // 储存要编辑的分类/单位 信息
      addClick: false,
      brandParentId: '',
      show_delete_category: false,
      typeFingerprint: '',
      typeLevel: ''
    };
  },
  watch: {},
  methods: {
    ...mapActions([SET_SHOW]),
    closeTypeManage() {
      this.SET_SHOW({ showTypeManage: false });
    },
    getBLen(str) { // 获取字节数
      if (str === null) return 0;
      if (typeof str !== 'string') {
        str += '';
      }
      // eslint-disable-next-line no-control-regex
      return str.replace(/[^\x00-\xff]/g, "00").length;
    },
    typeNameInput(name) { // 分类名长度输入限制
      let newName = name.replace(/[$':;]/g, '');
      if (this.getBLen(newName) > 20) {
        newName = this.getMaxTypeName(newName);
      }
      return newName;
    },
    getMaxTypeName(name) { // 递归找出符合位数限制的最大值
      if (this.getBLen(name) <= 20) return name;
      return this.getMaxTypeName(name.substr(0, name.length - 1));
    },
    typeItemClick(item) { // 一级分类项点击
      console.log(item, '拖拽item click');
      this.typeSelectedId = item.id;
      this.typeSelectedName = item.name;
      this.typeSelectedFingerprint = item.fingerprint;
      this.subTypeArr = item.list;
      this.subTypeSelectedId = '';
      this.subTypeSelectedName = '';
      this.subTypeSelectedFingerprint = '';
      this.level = 1;
    },
    subTypeItemClick(item) { // 二级分类项点击
      this.subTypeSelectedId = item.id;
      this.subTypeSelectedName = item.name;
      this.subTypeSelectedFingerprint = item.fingerprint;
      this.level = 2;
    },
    // 拖拽结束事件
    onEnd() {
      this.drag = false;
      let idList = [];
      for (var i = 0; i < this.typeArr.length; i++) {
        idList.push({id: '', sortno: ''});
        idList[i].id = this.typeArr[i].id;
        idList[i].sortno = i;
      }
      typeService.orderbyUpdate(idList, () => {});
    },
    // 二级分类拖拽结束事件
    onSubEnd() {
      this.drag = false;
      let idList = [];
      for (var i = 0; i < this.subTypeArr.length; i++) {
        idList.push({id: '', sortno: ''});
        idList[i].id = this.subTypeArr[i].id;
        idList[i].sortno = i;
      }
      typeService.orderbyUpdate(idList, () => {});
    },
    showMarkedWord() {
      demo.$toast('拖动调整分类顺序');
    },
    getAllCategory(flag) {
      typeService.search(res => {
        var json = demo.t2json(res);
        console.log(json, 'type json+');
        if (json.length > 0) {
          this.typeArr = json;
          this.reloadSubTypeArr();
          this.setTypeChosen(flag); // 首次进入设置分类选择
        }
      });
    },
    reloadSubTypeArr() { // 重载二级分类
      if (this.typeSelectedId !== '') {
        this.typeArr.forEach(item => {
          if (this.typeSelectedId === item.id) {
            this.subTypeArr = item.list;
          }
        });
      } else {
        this.subTypeArr = [];
      }
    },
    setTypeChosen(flag) { // 设置默认分类选中状态
      if (flag && this.goodsCurTypeFingerprint) {
        let tp = this.typeArr.filter(item => {
          console.log(this.goodsCurTypeFingerprint, 'fff');
          if (item.fingerprint !== this.goodsCurTypeFingerprint && item.list && item.list.length) {
            let sub = item.list.filter(subItem => {
              return subItem.fingerprint === this.goodsCurTypeFingerprint;
            });
            if (sub.length) {
              this.subTypeSelectedId = sub[0].id;
              this.subTypeSelectedName = sub[0].name;
              this.subTypeSelectedFingerprint = sub[0].fingerprint;
              this.level = 2;
              return item;
            }
          }
          return item.fingerprint === this.goodsCurTypeFingerprint;
        });
        if (tp && tp.length) {
          this.typeSelectedId = tp[0].id;
          this.typeSelectedName = tp[0].name;
          this.typeSelectedFingerprint = tp[0].fingerprint;
          this.level = 1;
          this.subTypeArr = tp[0].list;
        }
        this.SET_SHOW({ goodsCurTypeFingerprint: '' });
      }
    },
    continue_delete_category() { // 删除分类
      this.show_delete_category = false;
      goodService.deleteByTypes({fingerprint: [this.typeFingerprint], isDelType: '1'}, res => {
        demo.msg('success', '删除商品类别成功');
        this.getAllCategory();
        if (this.level === 1) {
          this.typeSelectedId = '';
          this.typeSelectedName = '';
          this.typeSelectedFingerprint = '';
        } else {
          this.subTypeSelectedId = '';
          this.subTypeSelectedName = '';
          this.subTypeSelectedFingerprint = '';
        }
      });
      this.typeFingerprint = '';
      this.typeLevel = '';
    },
    deleteType(level, item) { // 删除分类前检查是否可删除
      console.log(item, 'deleteType item');
      this.typeFingerprint = item.fingerprint;
      this.typeLevel = level;
      typeService.use(this.typeFingerprint, info => {
        if (info.length > 0) {
          if (this.$employeeAuth('delete_products')) {
            this.show_delete_category = true;
          } else {
            demo.msg('warning', '商品使用中，不允许删除');
          }
        } else {
          this.continue_delete_category();
        }
      });
    },
    closeTypeAdd() { // 关闭新增分类
      this.showAddType = false;
      this.iconSelected = '';
      this.type_add = '';
    },
    closeTypeEdit() { // 关闭编辑分类
      this.showEditType = false;
      this.typeSelectedId = '';
      this.typeSelectedName = '';
      this.typeSelectedFingerprint = '';
      this.subTypeSelectedId = '';
      this.subTypeSelectedName = '';
      this.subTypeSelectedFingerprint = '';
    },
    iconClick(item) { // 已废弃
      console.log(item);
      this.iconSelected = item.index;
      this.iconUrl = item.image;
    },
    showAddTypeBox(level) { // 新增一级/二级分类
      if (level === 2 && this.typeSelectedId === '') {
        demo.msg('warning', '请先选择父分类');
        return;
      }
      this.showAddType = true;
      this.level = level;
      setTimeout(() => {
        this.$refs.type_add_input.focus();
      }, 0);
    },
    showEditTypeBox(level, item) { // 编辑一级/二级分类
      console.log(item, 'showEditTypeBox item');
      this.level = level;
      this.showEditType = true;
      this.type_edit = item.name;
      this.editItem = item;
      setTimeout(() => {
        document.getElementById('editTypeInput').select();
      }, 0);
    },
    addType() { // 新增分类前检查
      var that = this;
      if (this.type_add === '') {
        demo.msg('warning', '请输入分类名称');
        return;
      }
      if (this.type_add === '全部分类' || this.type_add === '称重分类') {
        demo.msg('warning', '分类已存在！');
        return;
      }
      if (this.typeSelectedName === '其他分类' && this.level === 2) {
        demo.msg('warning', '其他分类不能添加子分类!');
        this.type_add = '';
        this.showAddType = false;
        return;
      }
      if (this.addClick === true) {
        return;
      }
      this.addClick = true;
      setTimeout(() => {
        that.addClick = false;
      }, that.clickInterval);
      var data = {
        name: this.type_add
      };
      if (this.level === 1) { // 新增分类
        data.icon = this.iconUrl;
        data.parentFingerprint = '';
      } else { // 新增子分类
        data.parentFingerprint = this.typeSelectedFingerprint;
      }
      console.log(data, 'addType data');
      typeService.insert(data, () => {
        demo.msg('success', '新增分类成功');
        that.getAllCategory();
      }, err => {
        demo.msg('warning', err);
      });
      this.type_add = '';
      this.showAddType = false;
      this.iconSelected = '';
    },
    editType() { // 编辑分类前检查
      if (this.type_edit === '') {
        demo.msg('warning', '请输入分类名称');
        return;
      }
      if (this.type_edit === '全部分类' || this.type_edit === '称重分类') {
        demo.msg('warning', '分类已存在！');
        return;
      }
      let subData = {
        name: this.type_edit,
        fingerprint: this.editItem.fingerprint,
        parentFingerprint: this.editItem.parentFingerprint
      };
      typeService.insert(subData, () => {
        demo.msg('success', '修改分类成功');
        this.getAllCategory();
        this.showEditType = false;
        this.type_edit = '';
        this.item = '';
      }, err => {
        demo.msg('warning', err);
      });
    },
    // 递归获取所有类型的fingerprint
    getTypeFingerprint(allType, arr) {
      arr.forEach(item => {
        allType.push(item.fingerprint);
        if (item.list.length > 0) {
          this.getTypeFingerprint(allType, item.list);
        }
      });
      return allType;
    },
    saveTypeManage() {
      // 判断编辑商品对话框是否打开 防止用户在编辑商品中通过按照类型删除商品所引发的错误
      // 获取父类型和子类型的fingerprint是否包含当前当前商品类型 不包含表示该类型已经删除 直接关闭编辑商品对话框
      let noHasTypeId = !this.getTypeFingerprint([], this.typeArr).includes(this.goodsDetail.typeFingerprint);
      if (this.showAddGoods && this.goodsDetail.toString() !== '' && noHasTypeId) {
        this.SET_SHOW({ showAddGoods: false });
      }
      if (this.typeSelectedId === '' && this.subTypeSelectedId === '') {
        this.SET_SHOW({ showTypeManage: false });
        return;
      }
      if (this.batchUpdateGoodsType) {
        if (this.subTypeSelectedId !== '') {
          this.SET_SHOW({ batchUpdateGoodsTypeId: this.subTypeSelectedId });
          this.SET_SHOW({ batchUpdateGoodsTypeFingerprint: this.subTypeSelectedFingerprint });
        } else if (this.typeSelectedId !== '') {
          this.SET_SHOW({ batchUpdateGoodsTypeId: this.typeSelectedId });
          this.SET_SHOW({ batchUpdateGoodsTypeFingerprint: this.typeSelectedFingerprint });
        } else {
          console.log('');
        }
        this.SET_SHOW({ showTypeManage: false });
        this.SET_SHOW({ batchUpdateGoodsType: false });
        return;
      }
      if (this.subTypeSelectedId !== '') {
        this.SET_SHOW({ addGoodsCategoryId: this.subTypeSelectedId });
        this.SET_SHOW({ addGoodsCategory: this.typeSelectedName + '/' + this.subTypeSelectedName });
        this.SET_SHOW({ addGoodsCategoryfingerprint: this.subTypeSelectedFingerprint });
      } else if (this.typeSelectedId !== '') {
        this.SET_SHOW({ addGoodsCategoryId: this.typeSelectedId });
        this.SET_SHOW({ addGoodsCategory: this.typeSelectedName });
        this.SET_SHOW({ addGoodsCategoryfingerprint: this.typeSelectedFingerprint });
      } else {
        console.log('');
      }
      this.SET_SHOW({ showTypeManage: false });
    }
  },
  created() {
    this.getAllCategory(true);
  },
  beforeDestroy() {
    this.SET_SHOW({ goodsCurTypeFingerprint: '' });
  },
  computed: mapState({
    showTypeManage: state => state.show.showTypeManage,
    goodsCurTypeFingerprint: state => state.show.goodsCurTypeFingerprint,
    // selManageType: state => state.show.selManageType,
    clickInterval: state => state.show.clickInterval,
    batchUpdateGoods: state => state.show.batchUpdateGoods,
    batchUpdateGoodsType: state => state.show.batchUpdateGoodsType,
    batchUpdateGoodsTypeId: state => state.show.batchUpdateGoodsTypeId,
    batchUpdateGoodsTypeFingerprint: state => state.show.batchUpdateGoodsTypeFingerprint,
    showAddGoods: state => state.show.showAddGoods,
    goodsDetail: state => state.show.goodsDetail
  })
};
</script>
