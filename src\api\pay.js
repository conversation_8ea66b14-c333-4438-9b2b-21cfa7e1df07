import rest from '@/config/rest';
// 支付接口查询
export function getPayStatus(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.pcRefundQuery, params, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 40000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 查询是否开通扫码付
export function getaid(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.getaid, params, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 40000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 查询支付宝认证状态
export function getZfbAuth() {
  return new Promise((resolve, reject) => {
    demo.$http
      .get(rest.getZfbAuth + '?aid=pay', {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 40000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 扫码付下单接口
export function qcTradePay(pay_data) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.qcTradePay, pay_data, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 20000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 支付状态查询接口
export function qcQuery(pay_data, timeout = 20000) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.qcQuery, pay_data, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: timeout
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
