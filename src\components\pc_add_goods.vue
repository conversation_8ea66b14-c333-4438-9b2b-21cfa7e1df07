<style lang="less">
.tipMsg {
  font-size: 14px;
  color: #FF6159;
}
.tipMsgBorder {
  border: 1px solid #FF6159!important;
}
.suggestMsg {
  margin-left: 92px;
  line-height: 44px;
  color: #B2C3CD;
  font-size: 16px;
  width: 200px;
}
.pic-uploader-good-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #bbc0c7;
  border-radius: 8px;
}
.avatar-pic {
  width: 100px;
  height: 100px;
  display: block;
}
.el-textarea__inner:focus {
  border-color: #bda16a;
}
.com_pad1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  color: #567485;
  overflow: auto;
  display: flex;
  align-items: center;
}
.com_pad11 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 120;
  top: 0;
  left: 0;
}
.com_pad12 {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 718px;
  border-radius: 10px;
  z-index: 150;
  overflow: hidden;
  height: 96vh;
}
.com_pad13 {
  position: relative;
}
.com_pad14 {
  position: absolute;
  margin-left: 38px;
  line-height: 48px;
  font-size: 15px;
  color: #bda16a;
  cursor: pointer;
}
.com_pad15 {
  font-size: 18px;
  line-height: 18px;
  margin-top: 25px;
  margin-left: 25px;
  font-weight: bold;
}
.com_pad16 {
  position: absolute;
  right: 38px;
  top: 12px;
}
.com_pad16 img {
  width: 20px;
  height: 20px;
  cursor: pointer;
}
.com_pad17 {
  margin-top: 0px;
  height: 100px;
}
.com_pad18 {
  float: left;
  font-size: 18px;
  margin-left: 52px;
}
.com_pad19 {
  overflow: hidden;
  margin-bottom: 24px;
}
.com_pad19 input {
  width: 200px;
  height: 44px;
  line-height: 44px;
  background: #fff;
  border: 1px solid #E5E8EC;
  border-radius:4px;
  float: left;
  text-indent: 20px;
  font-weight: normal;
  color: #567485;
}
.add_rightDiv input {
  margin-left: 20px;
}
.select-container {
  width: 200px;
  height: 44px;
  border: 1px solid #E5E8EC;
  border-radius: 4px;
  float: right;
  margin-right: 52px;
  cursor: pointer;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  align-items: center;
  position: relative;
}
.select-right {
  flex: 1;
  height: 44px;
  overflow: hidden;
}
.select-right .el-input__inner {
  border: none;
  font-size: 18px;
  margin-left: -18px;
}
.calc {
  font-size: 16px;
  color: #bda169;
  padding: 0 14px;
  cursor: pointer;
}
.bzq-container {
  margin-top: 52px;
  display: flex;
  justify-content: flex-end;
}
.bzq {
  width: 252px;
  font-size: 16px;
  color: #b2c3cd;
}

.stockInputDiv .el-input__inner{
  width: 200px;
  border-radius: 4px 0 0 4px;
}
.stockInputDiv1 .el-input__inner{
  width: 200px;
  border-radius: 4px;
}
.com_padlo19 {
  margin-top: 20px;
  overflow: hidden;
  margin-bottom: 3px;
}
.com_padlo19 input {
  width: 524px;
  height: 44px;
  margin-left: 9px;
  line-height: 44px;
  // margin-bottom: 24px;
  border: 1px solid #E5E8EC;
  background: #fff;
  border-radius:4px;
  float: left;
  text-indent: 20px;
  font-weight: normal;
  color: #567485;
}
.com_padst19 input {
  margin-left: 9px;
}
.com_pad2 {
  color: #b2c3cd;
  font-size: 18px;
  width: 84px;
  font-weight: 700;
  float: left;
  line-height: 24px;
  margin-top: 8px;
  i {
    color: #bda16a;
    font-size: 20px;
    cursor: pointer;
  }
}
.com_pad_upload {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  position: relative;
  .text {
    font-size:14px;
    color:#999;
    margin-left:10px;
    width:500px;
  }
}
.com_flex {
  display: flex;
}
.type_span {
  color:#567485;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 160px;
}
.com_padsub2{
  color: #b2c3cd;
  font-size: 18px;
  width: 104px;
  font-weight: bold;
  float: left;
  line-height: 24px;
  margin-top: 8px;
}
.profit_div {
  color: #b2c3cd;
  font-size: 18px;
  width: 360px;
  font-weight: bold;
  float: left;
  line-height: 24px;
  margin-top: 8px;
}
#ad6 {
  font-size: 18px;
  text-indent: 5px;
  float: left;
  border-radius:4px 0 0 4px;
}
.com_pad21 {
  width: 200px;
  float: left;
  height: 40px;
  line-height: 36px;
  background: #fff;
  border: 1px solid #E5E8EC;
  border-radius:4px;
  margin-top: 0;
  text-indent: 20px;
  font-weight: normal;
  color: #567485;
  cursor: pointer;
}
.com_pad22 {
  margin-right: 8px;
  color: #999;
  float: right;
}
.com_pad23 {
  font-size: 16px;
  margin-left: 30px;
  margin-top: 20px;
}
.com_pad24 {
  width: 612px;
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid #e5e5e5;
  text-indent: 10px;
  position: relative;
}
.com_pad24 input {
  width: 300px;
  text-align: right;
  line-height: 25px;
  border: none;
  position: absolute;
  right: 20px;
  top: 10px;
}
.com_pad25 {
  width: 217px;
  margin-top: 12px;
  margin-left: 80px;
}
#ad8 {
  height: 128px;
  background: #f5f8fb;
  font-size: 18px;
}
.com_pad26 {
  width: 100%;
  height: 40px;
  line-height: 40px;
  background: #f56c6c;
  color: #fff;
  text-align: center;
  font-size: 15px;
  position: absolute;
  bottom: 0;
  left: 0;
}
.com_pad26:hover {
  background: #f78989;
  cursor: pointer;
}
.com_pad271 {
  overflow: hidden;
  margin-top: 45px;
  font-size: 24px;
}
.com_pad28 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.com_pad29 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  background: @themeBackGroundColor !important;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.com_pad3 {
  width: 612px;
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid #e5e5e5;
  text-indent: 10px;
  position: relative;
}
.com_pad31 {
  width: 60px;
  height: 44px;
  margin-left: -20px;
  background-image: @themeBackGroundColor;
  border-radius: 0 5px 5px 0;
  float: left;
  color: #fff;
  text-align: center;
  line-height: 44px;
  cursor: pointer;
}
.com_pad32 {
  color: #ff6159;
  font-size: 30px;
  font-weight: bold;
  float: left;
  margin-top: 5px;
  margin-left: 8px;
}
.com_pad34 {
  width: 120px;
  height: 44px;
  margin-top: 16px;
  margin-left: 10px;
  float: right;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  line-height: 42px;
  border-radius: 4px;
  cursor: pointer;
}
.com_pad35 {
  width: 120px;
  height: 44px;
  margin-top: 16px;
  margin-left: 20px;
  float: right;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
}
.com_pad36 {
  width: 66px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 0px 4px 4px 0px;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 41px;
  text-align: center;
  color: #FFFFFF;
  float: left;
  cursor: pointer;
}
.com_pad37{
  width: 122px;
  height: 21px;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 43px;
  color: #B2C3CD;
  margin-left: 30px;
}
.com_pad38 {
  overflow: hidden;
  margin-top: 20px;
  font-size: 24px;
  display: flex;
  justify-content: center;
}
.com_pad39 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #567485;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: #567485;
}
.com_pad40 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  margin-bottom: 10px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 5px;
  cursor: pointer;
}
.com_pad41_relative {
  position: relative;z-index: 800;height: 100%;width: 100%;
  display: flex;justify-content: center;align-items: center;
}
.com_pad41 {
  height: 260px;color: #567485;border-radius: 10px;
  background: #FFF;width: 450px;overflow: hidden;
}
.com_pad42 {
  width: 100%;text-align: center;font-size: 24px;margin-top: 35px;
  font-weight: normal;line-height: 40px;padding: 0 45px;
}
.com_pad43 {
  height: 50px;display: flex;justify-content: space-between;align-items: center;
  border-bottom: 1px solid #E3E6EB;margin: 0 30px;
}
.pic-add-upload .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.pic-add-upload .el-upload:hover {
  border-color: #3dbfff;
}
.pic-add-upload .el-upload {
  border-color: #bda16a;
}
.pic-add-upload .el-upload:hover {
  border-color: #bda16a;
}
.pic-uploader-good-plus-icon {
  font-size: 28px;
  color: #8c939d;
  width: 90px;
  height: 90px;
  line-height: 90px;
  text-align: center;
}
.pc_spec_img_10 {
  width: 100px;
  height: 25px;
  background: black;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  text-align: center;
  line-height: 25px;
  color: #fff;
  position: absolute;
  top: 75px;
  left: 0px;
  opacity: 0.6;
  font-weight: bold;
  i {
    font-size: 18px;margin-right: 5px;
  }
}
.pc_spec_img_10:hover {
  background: #F64C4C;
}
.pc_add_line_div {
  width: 285px;
  /* margin-bottom: 25px; */
  display: flex;
  justify-content: space-between;
}
.com_pats_19 {
  overflow: hidden;
  margin-bottom: 16px;
  width: 610px;
  display: flex;
  justify-content: space-between;
}
.com_pats_ub2{
  color: #b2c3cd;
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  width: 104px;
  font-weight: bold;
  float: left;
  line-height: 24px;
  margin-top: 8px;
}
#save{
  margin-right: 56px;
  background-color: @themeBackGroundColor;
  margin-left:20px;
}
#saveAdd{
  background-color: #fff;
  color: @themeBackGroundColor;
  border:1px solid @themeBackGroundColor;
  margin-left:20px;
}
#cancel{
  background-color: #fff;
  color :@themeBackGroundColor;
  border:1px solid @themeBackGroundColor;
}
.manufactureDateClass {
  & > .el-input__suffix {
    right: 20px;
  }
}
.prod-codes-dialog {
  position: relative;z-index: 800;height: 100%;width: 100%;
  display: flex;justify-content: center;align-items: center;
  &--container {
    width: 630px;color: #567485;
    background: #FFF;overflow: hidden;border-radius: 10px;
    overflow-y: auto;
  }
  &--header {
    margin: 0 20px;display:flex;justify-content: space-between;
    align-items: center;border-bottom: 1px solid #E3E6EB;
  }
  &--close {
    font-size: 36px;color: #567485;cursor:pointer;
  }
  &--scroll {
    overflow-y: auto;
    max-height: 240px;
    // background-color: #f5f8fb;
  }
  &--body {
    padding: 10px 20px 0px;
    font-family: HarmonyOSSansSC, sans-serif;
    /deep/.el-form-item {
      margin-bottom: 33px;
    }
    /deep/.el-form-item__label {
      width: 110px;
      color: #B2C3CD;
      font-size: 18px;
    }
    /deep/.el-form-item__error {
      margin-left: 110px;
    }
    /deep/.el-input-group__append {
      padding: 0;
      font-size: 16px;
      background: #fff !important;
      border-left: none;
      color: #BDA169;
      cursor: pointer;
      span {
        display: block;height: 42px;width: 80px;padding: 0px;text-align: center;line-height: 42px;
      }
    }
  }
  &--itemLabel {
    display: inline-block;
    position: relative;
    width: 107px;
    color: #B2C3CD;
    font-size: 18px;
    i {
      color: #bda16a;
      font-size: 20px;
      cursor: pointer;
    }
  }
  &--curStock {
    display: inline-block;
    margin-left: 6px;
    span {
      font-weight: 500;
      line-height: 26px;
      color: #b2c3cd;
      font-size: 18px;
    }
    /deep/.el-input__inner {
      padding: 0 5px;
    }
  }
}
.split-codes-dialog {
  position: relative;z-index: 800;height: 100%;width: 100%;
  display: flex;justify-content: center;align-items: center;
  &--container {
    width: 740px;color: #567485;
    background: #FFF;overflow: hidden;border-radius: 10px;
    overflow-y: auto;
  }
  &--header {
    margin: 0 20px;display:flex;justify-content: space-between;
    align-items: center;border-bottom: 1px solid #E3E6EB;
  }
  &--close {
    font-size: 36px;color: #567485;cursor:pointer;
  }
  &--scroll {
    overflow-y: auto;
    max-height: 360px;
    // background-color: #f5f8fb;
  }
  &--body {
    padding: 10px 20px 0px;
    font-family: HarmonyOSSansSC, sans-serif;
    /deep/.el-form-item {
      margin-bottom: 33px;
    }
    /deep/.el-form-item__label {
      width: 110px;
      color: #B2C3CD;
      font-size: 18px;
    }
    /deep/.el-form-item__error {
      margin-left: 110px;
    }
    /deep/.el-input-group__append {
      padding: 0 15px;
      font-size: 16px;
      background-color: #fff;
      border-left: none;
      color: #BDA169;
      cursor: pointer;
    }
  }
  &--module {
    margin-bottom: 10px;
    .code_span {
      font-size: 18px;
      color: #b2c3cd;
    }
  }
  &--itemLabel {
    display: inline-block;
    position: relative;
    width: 107px;
    color: #B2C3CD;
    font-size: 18px;
    i {
      color: #bda16a;
      font-size: 20px;
      cursor: pointer;
    }
  }
  .border-warning {
    /deep/.el-input__inner {
      border-color: red;
    }
  }
}
.add-extend-code--btn {
  width: 164px;height: 44px;background: #FAFAFA;border-radius: 4px;
  display: flex;flex-direction: row;align-items: center;font-size: 16px;
  padding: 12px 20px;justify-content: space-around;cursor: pointer;
  color: #bda16a;margin-left: 110px;margin-top: 12px;
}
.delete-extend-code--btn {
  width: 44px;height: 44px;background: #FAFAFA;border-radius: 4px;
  flex-direction: row;align-items: center;font-size: 20px;
  padding: 9px 12px;justify-content: center;cursor: pointer;
  color: #FF4D4F;margin-left: 6px;margin-top: 2px;display: inline;
}
.submit-code--btn {
  width: 444px;height: 56px;background: #bda16a;border-radius: 4px;
  font-weight: bold;text-align: center;font-size: 20px;
  cursor: pointer;color: #fff;line-height: 56px;margin: 25px auto 10px;
}
.delete-code--div__inline {
  display: inline-block;
  div {
    display: flex;
    align-items: center;
    // padding-top: 5px;
  }
}
.zgzn_checkbox_uncheck {
  display: inline-block;
  position: relative;
  cursor: pointer;
  line-height: 16px;
  margin-left: 20px;
  margin-right: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 16px;
  height: 16px;
  background-color: #fff;
  z-index: 1;
  transition: border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46);
}
.zgzn_checkbox_checked {
  display: inline-block;
  position: relative;
  cursor: pointer;
  line-height: 16px;
  margin-left: 20px;
  margin-right: 5px;
  border-radius: 2px;
  box-sizing: border-box;
  font-family: -webkit-pictograph, sans-serif;
  color: #fff;
  font-weight: bold;
  text-align: center;
  width: 16px;
  height: 16px;
  background-color: #bda16a;
  z-index: 1;
  transition: border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46);
}
.mark-del_div {
  display: inline-block;
  color: #F64C4C;
  background-color: #FEF2F2;
  border-radius: 4px;
  width: 108px;
  height: 26px;
  font-size: 14px;
  cursor: pointer;
  justify-content: space-around;
  i {
    margin-right: 5px;
  }
}
.com_rgd1 {
  position: relative;z-index: 800;height: 100%;width: 100%;
  display: flex;justify-content: center;align-items: center;
}
.com_rgd2 {
  color: #567485;border-radius: 10px;position: relative;
  background: #FFF;width: 500px;overflow: hidden;
}
.com_rgd3 {
  width: 100%;font-size: 20px;margin-top: 10px;padding-right: 25px;
  font-weight: normal;line-height: 30px;padding-left: 40px;
  /deep/.el-checkbox__input {
    margin-top: -5px;
  }
}
.com_rgd3_c {
  max-height: 260px;
  overflow-y: auto;
  margin-right: 6px;
}
.com_rgd4 {
  overflow: hidden;
  margin: 20px 0px;
  font-size: 20px;
  display: flex;
  justify-content: space-around;

}
.com_rgd5 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.com_rgd6 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  background: @warningRed;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.supplier-name {
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  margin-right: 4px;
}
.diff {
  border-radius: 6px;
  margin-top: 21vh !important;
}
.diff .el-dialog__header {
  padding: 0;
}
.diff .el-dialog__body {
  padding: 0;
}
</style>
<template>
  <!--新增商品弹出框-->
  <div v-show="showAddGoods" class="com_pad1">
    <v-ExistGoods></v-ExistGoods>
    <div class="com_pad11"></div>
    <!-- 删除商品 -->
    <div
      v-show="show_delete"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 700;background: rgba(0,0,0,.5);;"
      @click="show_delete = false;isDelStock = false;"
    >
      <div class="com_pad41_relative">
        <div
          class="com_pad41"
        >
          <div style="width: 100%;text-align: center;font-size: 24px;margin-top: 40px;
            font-weight: bold;line-height: 22px;">提示
          </div>
          <div class="com_pad42">
            确定要删除商品吗？
          </div>
          <div class="com_pad271">
            <div class="com_pad28" @click="show_delete = false;isDelStock = false">取消</div>
            <div class="com_pad29" @click="continueDelete()">删除</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 一品多码重复商品删除弹窗 -->
    <div
      v-if="showRepeatCodeDel"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 1000;background: rgba(0,0,0,.5);"
    >
      <div class="com_rgd1">
        <div class="com_rgd2">
          <div
            style="width: 100%;text-align: center;font-size: 24px;margin-top: 20px;
            font-weight: bold;line-height: 22px;">删除商品
          </div>
          <i class="el-icon-close"
            style="position: absolute;top: 30px;right: 30px;
            font-size: 24px;font-weight: bold;cursor: pointer;"
            @click="showRepeatCodeDel = false;">
          </i>
          <div class="com_rgd3"
            style="font-size: 24px;margin-top: 25px;">
            {{deleteGoodsInfo[0] && deleteGoodsInfo[0].code ? deleteGoodsInfo[0].code : ''}}
          </div>
          <div class="com_rgd3_c">
            <div
              v-for="item in deleteGoodsInfo"
              :key="item.id"
              class="com_rgd3"
              style="">
              {{'--' + item.name + ' （库存: ' + item.curStock + '）'}}
            </div>
          </div>
          <div class="com_rgd3"
            style="margin-top: 25px;color: red;text-align: center;font-size: 24px;">
            删除后无法恢复，确定继续吗？
          </div>
          <div class="com_rgd3"
            style="margin-top: 25px;text-align: center;font-size: 20px;">
            <el-checkbox v-model="isMergeStock" label=""></el-checkbox>
            将删除商品库存合并到一品多码商品
          </div>
          <div class="com_rgd4">
            <div class="com_rgd6" @click="repeatGoodsDelete()">删除</div>
            <div class="com_rgd5" @click="showRepeatCodeDel = false;">取消</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 关闭一品多码弹窗 -->
    <div
      v-show="showCodesGoodsCloseDiolag"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 700;background: rgba(0,0,0,.5);"
    >
      <div class="com_pad41_relative">
        <div
          class="com_pad41"
          style="height: 300px;position: relative;"
        >
          <div style="width: 100%;text-align: center;font-size: 24px;margin-top: 40px;
            font-weight: bold;line-height: 22px;">关闭一品多码
          </div>
          <i class="el-icon-close"
            style="position: absolute;top: 30px;right: 30px;
            font-size: 24px;font-weight: bold;cursor: pointer;"
            @click="showCodesGoodsCloseDiolag = false;">
          </i>
          <div class="com_pad42">
            关闭后可将扩展条码新增为一品一码商品，是否新增？
          </div>
          <div class="com_pad271">
            <div class="com_pad28" style="font-size: 20px;"
              @click="multiGoodsClose">
              直接关闭
            </div>
            <div class="com_pad29" style="font-size: 20px;" @click="splitCodesGoods()">去新增</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增为一品一码 -->
    <div
      v-show="splitCodesGoodsDialog"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;
        z-index: 700;background: rgba(0,0,0,.5);">
      <div class="split-codes-dialog">
        <div class="split-codes-dialog--container">
          <div class="split-codes-dialog--header">
            <div>
              <span style="font-size: 18px;font-weight: bold;">新增为一品一码</span>
            </div>
            <div @click="splitCodesGoodsDialog = false">
              <span class="split-codes-dialog--close">×</span>
            </div>
          </div>
          <div class="split-codes-dialog--body">
            <div class="split-codes-dialog--scroll">
              <div
                class="split-codes-dialog--module"
                v-for="(item, index) in splitRuleForm"
                :key="item.code">
                <span class="code_span">{{item.code}}</span>
                <div>
                  <div style="width: 270px;display: inline-block;">
                    <span style="color: red;">*</span>商品名称
                  </div>
                  <div style="width: 100px;display: inline-block;">
                    <span style="color: red;">*</span>零售价
                  </div>
                  <div style="width: 100px;display: inline-block;">进货价</div>
                  <div style="width: 100px;display: inline-block;">会员价</div>
                  <div style="width: 100px;display: inline-block;">库存数量</div>
                </div>
                <div class="split-codes-dialog--inputs">
                  <el-input
                    :id="'splitName' + index"
                    v-model.trim="item.name"
                    @focus="selectText('splitName' + index)"
                    autocomplate="off"
                    placeholder="请输入商品名"
                    @blur="checkItemName(item)"
                    :class="item.nameErrorMsg ? 'border-warning' : ''"
                    style="width: 270px;"/>
                  <el-input
                    :id="'splitSalePrice' + index"
                    v-model.trim="item.salePrice"
                    @focus="selectText('splitSalePrice' + index)"
                    autocomplate="off"
                    placeholder="请输入"
                    @blur="checkItemCode(item)"
                    :class="item.showPriceRequire ? 'border-warning' : ''"
                    @input="item.salePrice = $priceLimit(item.salePrice)"
                    style="width: 100px;"/>
                  <el-input
                    :id="'splitPurPrice' + index"
                    v-model.trim="item.purPrice"
                    @focus="selectText('splitPurPrice' + index)"
                    @input="item.purPrice = $pricePurPriceLimit(item.purPrice)"
                    autocomplate="off"
                    placeholder="请输入"
                    style="width: 100px;" />
                  <el-input
                    :id="'splitVipPrice' + index"
                    v-model="item.vipPrice"
                    @focus="selectText('splitVipPrice' + index)"
                    @input="item.vipPrice = $priceLimit(item.vipPrice)"
                    autocomplate="off"
                    placeholder="请输入"
                    style="width: 100px;" />
                  <el-input
                    :id="'splitCurStock' + index"
                    v-model="item.curStock"
                    @focus="selectText('splitCurStock' + index)"
                    autocomplate="off"
                    placeholder="请输入"
                    @input="item.curStock = $stockLimit({data: item.curStock, max: 99999, min: -99999, decimals: 3})"
                    style="width: 100px;" />
                </div>
                <div style="color: red;">
                  <div v-if="item.nameErrorMsg || item.showPriceRequire" style="width: 270px;display: inline-block;">
                    {{!item.nameErrorMsg && item.showPriceRequire ? ' ' : item.nameErrorMsg}}
                  </div>
                  <div v-if="item.showPriceRequire" style="width: 100px;display: inline-block;">
                    请填写零售价
                  </div>
                </div>
              </div>
            </div>
            <div class="submit-code--btn" @click="splitCodesSubmit">
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 一品多码编辑 -->
    <div
      v-show="showMulticodeManage"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;
      z-index: 700;background: rgba(0,0,0,.5);">
      <div class="prod-codes-dialog">
        <div class="prod-codes-dialog--container">
          <div class="prod-codes-dialog--header">
            <div>
              <span style="font-size: 18px;font-weight: bold;">一品多码</span>
            </div>
            <div @click="showMulticodeManage = false">
              <span class="prod-codes-dialog--close">×</span>
            </div>
          </div>
          <div class="prod-codes-dialog--body">
            <el-form :model="codesRuleForm" ref="codesForm" label-position="left">
              <el-form-item prop="code" :rules="rules.code">
                <span>
                  <span class="prod-codes-dialog--itemLabel">
                    <span>主条码</span>
                    <el-popover
                      popper-class="pc_pay192 pop_padding"
                      placement="top"
                      width="210"
                      trigger="click"
                      content="用于各种操作界面及报表展示。">
                      <i class="el-icon-question" slot="reference"></i>
                    </el-popover>
                  </span>
                </span>
                <el-input
                  id="emc1"
                  v-model.trim="codesRuleForm.code"
                  @focus="selectText('emc1')"
                  autocomplate="off"
                  :style="showCurStock ? 'width: 260px;' : 'width: 400px;'"
                  maxlength="16"
                  @keydown.native.enter="extendCodeFocus"
                  @change="checkCode(codesRuleForm.code)"
                  @input="codesRuleForm.code = $barCodeLimit(codesRuleForm.code)">
                  <template slot="append">
                    <span
                      @click="autoMakeCode('code')">
                      自动生成
                    </span>
                  </template>
                </el-input>
                <div v-if="showCurStock" class="prod-codes-dialog--curStock">
                  <span>库存数量:</span>
                  <el-input
                    v-model.trim="oneGoods.curStock"
                    disabled
                    style="width: 100px;" />
                </div>
                <template slot="error" slot-scope="slot">
                  <div class="el-form-item__error">
                    <span>
                      {{isShowDelCheckbox(slot.error) && !$employeeAuth('delete_products') ? '条码已被使用，请重新输入' : slot.error}}
                    </span>
                    <div v-if="isShowDelCheckbox(slot.error) && $employeeAuth('delete_products')"
                      class="delete-code--div__inline">
                      <div class="mark-del_div" @click="deleteRepeatGood(true)">
                        <span><i class="el-icon-delete"></i>删除占用商品</span>
                      </div>
                    </div>
                  </div>
                </template>
              </el-form-item>
              <div class="prod-codes-dialog--scroll" ref="scrollDiv">
                <el-form-item
                  v-for="(item, index) in codesRuleForm.extendCodes"
                  :label="'扩展条码' + (index + 1)"
                  :key="item.key"
                  :prop="'extendCodes.' + index + '.value'"
                  :rules="rules.extendCode">
                  <el-input
                    :id="'etc' + index"
                    v-model.trim="item.value"
                    :ref="'extendInput' + index"
                    @keydown.native.enter="addExtendCode(index + 1)"
                    maxlength="16"
                    autocomplate="off"
                    @input="item.value = $barCodeLimit(item.value)"
                    style="width: 400px;" />
                    <!-- <template slot="append">
                      <span @click="autoMakeCode('extendCodes.' + index + '.value', item )">自动生成</span>
                    </template>
                  </el-input> -->
                  <div v-if="codesRuleForm.extendCodes.length > 1" class="delete-extend-code--btn"
                    @click.prevent="removeExtendCode(item)">
                    <i class="el-icon-delete"></i>
                  </div>
                  <template slot="error" slot-scope="slot">
                    <div class="el-form-item__error">
                      <span>
                        {{isShowDelCheckbox(slot.error) && !$employeeAuth('delete_products') ? '条码已被使用，请重新输入' : slot.error}}
                      </span>
                      <div v-if="isShowDelCheckbox(slot.error) && $employeeAuth('delete_products')"
                        class="delete-code--div__inline">
                        <div class="mark-del_div" @click="deleteRepeatGood(false, item)">
                          <span><i class="el-icon-delete"></i>删除占用商品</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-form-item>
              </div>
              <el-form-item label="">
                <div v-show="this.codesRuleForm.extendCodes.length < 20"
                  class="add-extend-code--btn" @click="addExtendCode">
                  <i class="el-icon-plus"></i>添加扩展条码
                </div>
                <div class="submit-code--btn" @click="codesSubmit">
                  确定
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div class="com_pad12">
      <div class="com_pad43">
        <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;"
          v-show="goodsDetail.toString() === ''">
          新增商品
        </div>
        <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;"
          v-show="goodsDetail.toString() !== ''">
          编辑商品
        </div>
        <div
          style="font-size: 30px;color: #8298A6;cursor: pointer;"
          @click="closeAddGoods()"
        >×</div>
      </div>
      <div style="overflow-x: hidden;height: calc(100% - 125px)">
        <div class="com_pad18">
          <div class="com_pats_19">
              <div class="pc_add_line_div">
                <div class="com_pad2" style="width: 150px;margin-top: 20px;">
                  一品多码
                  <el-popover
                    popper-class="pc_pay192 pop_padding"
                    placement="top-start"
                    width="240"
                    trigger="hover"
                    :close-delay="50"
                    content="适用于一种商品有多个不同的条形码">
                    <i class="el-icon-question" slot="reference"></i>
                  </el-popover>
                </div>
                <el-switch v-model="oneGoods.isCodesGoods" active-color="#bda169"
                  :active-value="1" :inactive-value="0" style="margin-top:20px;"
                  @change="isCodesChange">
                </el-switch>
              </div>
          </div>
          <div class="com_padlo19" style="width:619px;margin-left:-9px;">
            <div class="com_pad2">
              <div style="float: left;">
                <span style="color:red">*</span>商品名称
              </div>
            </div>
            <input
              id="ad2"
              type="text"
              @focus="selectText('ad2')"
              @change="checkExist('name')"
              v-model.trim="oneGoods.name"
              :maxlength="goodsNameLength"
              :class="nameExist? 'tipMsgBorder' : ''"
              @input="oneGoods.name = oneGoods.name.replace(/[$']/g, '')"
            />
            <div class="tipMsg" style="margin-left:93px"
              :style="nameExist ? 'visibility: inherit' : 'visibility: hidden'">*{{nameMsg}}</div>
          </div>
          <div class="com_pad19" style="margin-bottom: 3px;width:613px;">
            <div class="com_pad2">商品条码</div>
            <div
              v-if="oneGoods.isCodesGoods"
              class="com_pad21"
              style="height: 44px;line-height: 42px;padding-left: 2px;"
              @click="multicodeClick()">
              <span style="color:#567485">
                {{ oneGoods | codesFilter }}
              </span>
              <span class="com_pad22"></span>
            </div>
            <input
              v-else
              id="prodBarcode"
              type="text"
              @focus="selectText('prodBarcode')"
              v-model="oneGoods.code"
              maxlength="16"
              @change="checkCode(oneGoods.code)"
              @input="oneGoods.code = $barCodeLimit(oneGoods.code)"
              :class="codeExist? 'tipMsgBorder' : ''"
              @keydown.enter="inputSelectHandler('prodBarcode')"
            />
            <div class="com_padsub2">
              <div style="float: left;margin-left:30px;">商品简称</div>
            </div>
            <div class="add_rightDiv">
              <input
                id="ad9"
                type="text"
                @focus="selectText('ad9')"
                v-model.trim="oneGoods.pinyin"
                maxlength="4"
                @compositionstart='pinyin = true'
                @compositionend='pinyin = false'
                @input="oneGoods.pinyin = oneGoods.pinyin.replace(/[$']/g, '')"
              />
            </div>
            <div class="tipMsg" style="margin-left:85px;"
              :style="codeExist ? 'visibility: inherit' : 'visibility: hidden'">*商品条码重复</div>
          </div>
          <div class="com_pad19">
            <div class="com_pad2">商品分类</div>
            <div
              class="com_pad21 com_flex"
              @click="showTypeManageDialog()">
              <span class="type_span">{{ oneGoods.typeName }}</span>
              <span class="com_pad22">〉</span>
            </div>
            <div class="com_padsub2">
              <div style="float: left;margin-left:30px">
                单位
              </div>
            </div>
            <div
              class="com_pad21"
              style="margin-left: 20px;"
              @click="showSelectManage(oneGoods.unitName)">
              <span>{{ !oneGoods.unitName ? '请选择' : oneGoods.unitName }}</span>
              <span class="com_pad22">〉</span>
            </div>
          </div>
          <div class="com_pad19" style="margin-left:-9px;" :style="suggestPrice !== '' ? 'margin-bottom: 4px;' : ''">
            <div class="com_pad2">
              <div style="float: left;"><span style="color:red">*</span>零售价</div>
            </div>
            <div class="com_padst19">
              <input
                id="ad3"
                type="text"
                @focus="selectText('ad3')"
                :style="suggestPrice !== ''
                  ? 'font-size: 18px;width: 134px;border-radius:4px 0 0 4px;': 'font-size: 18px;'"
                @blur="formatOneGoods()"
                v-model.trim="oneGoods.salePrice"
                @input="oneGoods.salePrice = $priceLimit(oneGoods.salePrice)"
              />
              <div class="com_pad36" v-show="suggestPrice !== ''" @click="useSuggestPrice">
                使用
              </div>
              <div class="profit_div"
                v-if="!$employeeAuth('purchase_price')">
                <div style="margin-left:30px;">
                  <span style="font-size: 18px;">毛利率：{{profitRate}}</span>
                  <span style="font-size: 14px;">（毛利：{{profit}}）</span>
                </div>
              </div>
              <div class="suggestMsg" v-show="suggestPrice !== ''">建议零售价{{suggestPrice}}</div>
            </div>
          </div>
          <div class="com_pad19">
            <div class="com_pad2" v-if="!$employeeAuth('purchase_price')">进货价</div>
            <input
              v-if="!$employeeAuth('purchase_price')"
              id="ad5"
              type="text"
              @focus="selectText('ad5')"
              style="font-size: 18px;"
              @blur="formatOneGoods()"
              v-model.trim="oneGoods.purPrice"
              @input="oneGoods.purPrice = $pricePurPriceLimit(oneGoods.purPrice)"
            />
            <div class="com_padsub2" :style="!$employeeAuth('purchase_price') ? '' : 'width: 64px;'">
              <div style="float:left;"
                :style="!$employeeAuth('purchase_price') ? 'margin-left:30px;' : ''">会员价</div>
            </div>
            <div class="add_rightDiv">
              <input
                id="ad4"
                type="text"
                @focus="selectText('ad4')"
                style="font-size: 18px;"
                @blur="formatOneGoods()"
                @input="oneGoods.vipPrice = $priceLimit(oneGoods.vipPrice)"
                v-model.trim="oneGoods.vipPrice"
              />
            </div>
          </div>
          <div
            v-show="!addProdFromStock"
            class="com_pad19" style="margin-bottom: 24px;">
            <div class="com_pad2">库存数量</div>
            <div
              :class="goodsDetail.toString() !== '' ? 'stockInputDiv' : 'stockInputDiv1'"
              :style="(oneGoods.status === '禁用' || addProdFromStock) ? 'opacity:0.6;background-color:#F5F7FA' : ''">
              <el-input
                id="ad6"
                type="text"
                @focus="selectText('ad6')"
                style="float: left; width: 200px"
                @blur="formatOneGoods()"
                @input="editStockInput()"
                v-model="oneGoods.curStock"
                :disabled="oneGoods.status === '禁用' || addProdFromStock"
              ></el-input>
            </div>
            <div class="com_padsub2">
              <div style="float: left;margin-left:30px">
                供应商
              </div>
            </div>
            <div
              class="com_pad21"
              style="margin-left: 20px;display:flex;align-items:center"
              @click="showSupplierSelectDialog">
              <el-tooltip effect="dark" :content="selectedSuppliersDetail.name" placement="top" v-if="selectedSuppliersDetail.name">
                <div class="supplier-name">{{ selectedSuppliersDetail.name }}</div>
              </el-tooltip>
              <div class="supplier-name" v-else>请选择</div>
              <div class="com_pad22">〉</div>
            </div>
            <!-- <div v-if="addProdFromStock && goodsDetail.toString() === ''" class="com_padsub2">
              <div style="float:left;margin-left:30px;">进货数量</div>
            </div>
            <div v-if="addProdFromStock && goodsDetail.toString() === ''" class="add_rightDiv">
              <input
                id="ad7"
                type="text"
                @focus="selectText('ad7')"
                style="font-size: 18px;"
                @blur="formatOneGoods()"
                @input="oneGoods.addStockNum = $stockLimit({data: oneGoods.addStockNum, max: 99999, min: -99999, decimals: 3})"
                v-model="oneGoods.addStockNum"
              />
            </div> -->
          </div>
          <div class="com_pad19" style="margin-bottom: 5px;">
            <div class="com_pad2">库存上限</div>
            <div class="add_rightDiv">
              <input
                id="ad10"
                type="text"
                @focus="selectText('ad10')"
                style="font-size: 18px;margin-left: 0px;"
                @blur="formatOneGoods()"
                @input="oneGoods.maxStock = $stockLimit({data: oneGoods.maxStock.replace(/-/g, ''), max: 99999.999, min: 0, decimals: 3})"
                v-model="oneGoods.maxStock"
              />
            </div>
            <div class="com_padsub2">
              <div style="float:left;margin-left:30px;">库存下限</div>
            </div>
            <div class="add_rightDiv">
              <input
                id="ad11"
                type="text"
                @focus="selectText('ad11')"
                style="font-size: 18px;"
                @blur="formatOneGoods()"
                v-model="oneGoods.minStock"
                @input="oneGoods.minStock = $stockLimit({data: oneGoods.minStock.replace(/-/g, ''), max: 99999.999, min: 0, decimals: 3})"
              />
            </div>
          </div>
          <div style="font-size: 14px; color: #F64C4C;margin-bottom: 10px;">当商品的库存超出所填写的上限和下限值时，系统会预警；都为0时不预警。</div>
          <div class="com_pad19" style="margin-bottom: 20px;">
            <div class="com_pad2">生产日期</div>
            <div style="width: 200px; float: left;">
              <el-date-picker
                id="ad14"
                type="date"
                class="manufactureDateClass"
                @focus="selectText('ad14')"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择生产日期"
                v-model="oneGoods.manufactureDate"
                @change="calcBZQ"
              >
              </el-date-picker>
            </div>
            <div class="com_padsub2">
              <div style="float:left;margin-left:30px;width: 104px;">保质期(天)</div>
            </div>
            <div>
              <div class="select-container">
                <div class="select-right">
                  <el-input id="ad13"
                    placeholder="请输入"
                    style="font-size: 18px;"
                    @focus="selectText('ad13')"
                    @input="oneGoods.expiryDays = $intMaxMinLimit({data: oneGoods.expiryDays, max: 9999, min: 0});calcBZQ()"
                    v-model="oneGoods.expiryDays"
                    @change="calcBZQ">
                  </el-input>
                </div>
                <div class="calc" @click="showBzqDiffDialog = true">计算</div>
              </div>
            </div>
            <div class="bzq-container">
              <div class="bzq">有效期至：<span>{{calcGuaranteeDate}}</span></div>
            </div>
          </div>
          <div class="com_pats_19" v-if="hasBarCodeScales">
            <div class="pc_add_line_div">
              <div class="com_pad2">是否传秤</div>
              <div>
                <el-switch v-model="oneGoods.isBarcodeScalesGoods" active-color="#bda169"
                  :active-value="1" :inactive-value="0" style="margin-top:10px;"></el-switch>
              </div>
            </div>
            <div class="com_pats_ub2" style="width: 294px;" v-if="oneGoods.isBarcodeScalesGoods === 1">
              <div>模式</div>
              <el-select v-model="oneGoods.weighModelVal"
                style="width: 202px;line-height: 32px;background: #fff;margin-top: -8px;font-weight:normal;"
                placeholder="请选择传秤模式">
                <el-option v-for="wm in weighModelOption" :key="wm.value" :label="wm.name" :value="wm.value"></el-option>
              </el-select>
            </div>
          </div>
          <div class="com_pad19" v-if="hasBarCodeScales && oneGoods.isBarcodeScalesGoods === 1">
            <div class="com_pad2">
              <div>皮重</div>
            </div>
            <div>
              <input
                id="ad12"
                type="text"
                @focus="selectText('ad12')"
                style="font-size: 18px;"
                @blur="formatOneGoods()"
                v-model.trim="oneGoods.tare"
                @input="oneGoods.tare = $stockLimit({data: oneGoods.tare, max: 99999, min: 0, decimals: 3})"
              />
            </div>
          </div>
          <div class="com_pad19" style="margin-bottom: 2px;">
            <div class="com_pad2" style="margin-top:20px;">上传图片</div>
            <div class="com_pad_upload">
              <el-upload
                class="pic-uploader com_pad17"
                :action="uploadImage"
                :headers="{'Authorization': token ? token : configToken}"
                accept=".jpg, .jpeg, .png"
                :data="uploadData"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img
                  alt=""
                  v-show="oneGoods.image"
                  :src="$getSrcUrl(oneGoods.image)"
                  @error="$imgError($event)"
                  class="avatar-pic"
                />
                <div v-show="oneGoods.image" class="pc_spec_img_10" @click.stop="delProdImg">
                  <i class="el-icon-delete"></i>
                  删除
                </div>
                <em
                  v-show="!oneGoods.image"
                  class="el-icon-plus pic-uploader-good-icon"
                ></em>
              </el-upload>
              <div class="text">
                <span>点击图片进行替换<br/>图片比例建议1:1（正方形）仅支持JPG或PNG格式<br/>图片大小不超过2M</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="com_pad34"
        id="save"
        @click="beforeSubmitGoods()"
      >
        保存
      </div>
      <div
        v-show="goodsDetail.toString() == ''"
        class="com_pad34"
        id="saveAdd"
        @click="beforeSubmitGoods(true)"
      >
        保存并新增
      </div>
      <div
        class="com_pad34"
        id="cancel"
        @click="closeAddGoods()"
      >
        取消
      </div>
      <div
        v-show="goodsDetail.toString() !== '' && goodsDetail.status !== '禁用'"
        @click="deleteGoods()"
        :style="banBtnStyle"
        class="com_pad35"
      >
        删除
      </div>
    </div>
    <!-- 已存在商品是否编辑确认弹窗 -->
    <!-- <div
      v-show="showEditGoodsConfirm"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
      ></div>
      <div
        style="position: relative;z-index: 800;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 15px;"
        >提示</div>
        <div
          style="width: 100%;height: 111px;display:flex;align-items:center;justify-content:center;text-align: center;
            font-size: 25px;color: #567485;margin-top: 15px;font-weight: 100;padding: 0 20px;"
        ><span>{{ existMsg }}</span></div>
        <div class="com_pad38">
          <div class="com_pad40" @click="cancelToEdit()">确认</div>
        </div>
      </div>
    </div> -->

    <!-- 选择保质期和有效期弹窗 -->
    <el-dialog v-if="showBzqDiffDialog" :visible.sync="showBzqDiffDialog" width="420px" append-to-body :show-close="false" custom-class="diff">
      <date-diff :manufactureDate="oneGoods.manufactureDate ? oneGoods.manufactureDate.substring(0, 10) : ''"
        @dismissDialog="showBzqDiffDialog = false" @callback="getDiff"/>
    </el-dialog>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import pyCode from '@/common/pyCode';
import { compressImage } from '@/utils/compressImage';
import { timeStampToDate } from '@/utils/util';
import DateDiff from '@/common/components/DateDiff';

export default {
  components: {
    DateDiff
  },
  data() {
    // 主条码校验
    let validateCode = (rule, value, callback) => {
      if (value === '' || value === null) {
        callback(new Error('请填写商品主条码'));
      } else {
        // step0:判断是否是传秤条码格式
        if ((/^20\d{5}$/.test(value))) {
          callback(new Error('请避免使用20开头的7位条码'));
          return;
        }
        // step1:判断和页面其余条码是否重复
        let codesArr = this.codesRuleForm.extendCodes;
        for (let i = 0; i < codesArr.length; i++) {
          if (codesArr[i].value === value) {
            callback(new Error('主条码与扩展条码重复'));
            return;
          }
        }
        // step2:判断是否为编辑时自身原条码或原扩展条码
        console.log(this.goodsDetail, 'step2222');
        if (this.goodsDetail.toString() !== '' &&
        (value === this.goodsDetail.code ||
        this.goodsDetail.extendCodes.map(item => { return item.extBarcode; }).indexOf(value) !== -1)) {
          callback();
          return;
        }
        // step3:判断数据库中是否已录入该条码
        goodService.checkGoodCodeAndGoodExtBarcode({code: value}, res => {
          console.log('主条码校验成功回调+');
          var json = demo.t2json(res);
          if (json.length === 0) { // 条码未被使用
            callback();
          } else if (json[0] && (json[0].isDel === 0 || json[0].isGoods === 0)) {
            // 条码重复的商品不可删除  isGoods: 0:扩展条码 1:主条码      isDel: 0:一品多码商品 1:普通商品
            callback(new Error('条码已被一品多码商品使用，请重新输入'));
          } else { // 条码重复的商品可删除
            this.codesRuleForm.codeError = _.cloneDeep(res);
            console.log(this.codesRuleForm, 'this.codesRuleForm.codeError+');
            callback(new Error('条码已被使用，请重新输入 或'));
          }
        });
      }
    };
    let validateExtendCode = (rule, value, callback) => {
      // step0:判断是否是传秤条码格式
      if ((/^20\d{5}$/.test(value))) {
        callback(new Error('请避免使用20开头的7位条码'));
        return;
      }
      // step1:扩展条码页面允许为空，保存时处理
      let curIndex = +rule.field.split('.')[1];
      let codesArr = this.codesRuleForm.extendCodes;
      if (value === '') {
        if (codesArr.filter(item => { return item.value !== ''; }).length === 0) {
          callback(new Error('请至少填写一条扩展条码'));
          return;
        } else {
          callback();
          return;
        }
      }
      // step2:判断与主条码是否重复
      if (value === this.codesRuleForm.code) {
        callback(new Error('扩展条码与主条码重复'));
        return;
      }
      // step3:判断和页面其余扩展条码是否重复
      for (let i = 0; i < codesArr.length; i++) {
        if (codesArr[i].value === value && i !== curIndex) {
          callback(new Error('扩展条码与扩展条码重复'));
          return;
        }
      }
      // step4:判断是否为编辑时自身原条码或原扩展条码
      console.log(this.goodsDetail, 'step2222');
      if (this.goodsDetail.toString() !== '' &&
        (value === this.goodsDetail.code ||
        this.goodsDetail.extendCodes.map(item => { return item.extBarcode; }).indexOf(value) !== -1)) {
        callback();
        return;
      }
      // step5:判断数据库中是否已录入该条码
      goodService.checkGoodCodeAndGoodExtBarcode({code: value}, res => {
        console.log('扩展条码校验成功回调+');
        var json = demo.t2json(res);
        if (json.length === 0) { // 条码未被使用
          callback();
        } else if (json[0] && (json[0].isDel === 0 || json[0].isGoods === 0)) {
          // 条码重复的商品不可删除  isGoods: 0:扩展条码 1:主条码      isDel: 0:一品多码商品 1:普通商品
          callback(new Error('条码已被一品多码商品使用，请重新输入'));
        } else { // 条码重复的商品可删除
          this.codesRuleForm.extendCodes[curIndex].codeError = _.cloneDeep(res);
          console.log(this.codesRuleForm, 'extendCodeError+');
          callback(new Error('条码已被使用，请重新输入 或'));
        }
      });
      // }
    };
    return {
      qualityGuaranteeDate: '',
      calcGuaranteeDate: '-',
      showBzqDiffDialog: false, // 计算保质期天数弹窗
      tempProduceDate: '', // 生产日期（日期）
      tempZhiDate: '', // 有效期至（日期）
      tempDiffDate: '-', // 计算出的保质期天数
      oneGoods: {
        id: '',
        majorCode: '',
        code: '',
        name: '',
        pinyin: '',
        typeId: '2',
        typeName: '其他分类',
        typeFingerprint: md5('其他分类'),
        unitName: '',
        unitId: '',
        unitFingerprint: '',
        salePrice: '0.00',
        vipPrice: '0.00',
        purPrice: '0.00',
        curStock: '0',
        addStockNum: '0',
        minStock: '0',
        maxStock: '0',
        remark: '',
        image: '',
        has_image: 0,
        supplier_id: 0,
        initStock: 0,
        initPrice: 0,
        initAmt: 0,
        configToken: '',
        isBarcodeScalesGoods: 0, // 是否传秤
        weighModelVal: 0, // 传秤模式
        expireDate: 0, // 保质期
        tare: 0, // 皮重
        img_fingerprint: '',
        first_letters: '',
        expiryDays: null,
        manufactureDate: null,
        isCodesGoods: 0, // 是否为一品多码商品
        extendCodes: [], // 一品多码
        specs: {unlock: {}}
      },
      nameMsg: '商品名称重复',
      stop_code: false,
      multipleSelection: [],
      textarea: '',
      imageUrl: '',
      file: '',
      //      旧库存数据
      old_curStock: '',
      stockOverflow: false,
      show_delete: false,
      uploadData: {},
      uploadImage: this.$rest.uploadImage,
      submit_click: false,
      banBtnStyle: '',
      typeIdFromGoods: '2',
      typeNameFromGoods: '其他分类',
      typeFingerprintFromGoods: md5('其他分类'),
      // 传秤相关
      weighModelOption: [{value: 0, name: '称重'}, {value: 1, name: '计件'}, {value: 2, name: '定重'}],
      lastTime: '',
      codeString: '',
      changeWait: false,
      addModalBoxMargin: 'margin-top: 49px',
      pinyin: false,
      showEditGoodsConfirm: false,
      existType: '',
      hasBarCodeScales: false,
      panel_height: 'height: 722px;',
      isDelStock: false,
      markToDeleteGoods: [],
      // 一品多码相关
      showMulticodeManage: false,
      codesRuleForm: {
        code: '',
        extendCodes: []
      },
      showRepeatCodeDel: false, // 一品多码条码占用商品删除
      deleteGoodsInfo: [],
      isMergeStock: false, // 是否将删除商品库存合并到一品多码商品的库存
      showCodesGoodsCloseDiolag: false,
      splitCodesGoodsDialog: false,
      splitRuleForm: [],
      splitClick: null,
      rules: {
        code: [{validator: validateCode, trigger: 'blur'}],
        extendCode: [{validator: validateExtendCode, trigger: 'blur'}]
      },
      deleteExtendCode: null
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),

    /**
     * 计算保质期显示
     */
    calcBZQ() {
      // 根据天数计算 显示日期
      if (!this.oneGoods.manufactureDate || !+this.oneGoods.expiryDays) {
        this.calcGuaranteeDate = '-';
        return;
      }
      const produceTime = new Date(this.oneGoods.manufactureDate.substring(0, 10)).getTime();
      this.calcGuaranteeDate = timeStampToDate(produceTime + (+this.oneGoods.expiryDays * 24 * 3600 * 1000));
    },

    /**
     * 日期选择后回调
     */
    getDiff(obj) {
      this.showBzqDiffDialog = false;
      this.oneGoods.manufactureDate = obj.manufactureDate;
      this.oneGoods.expiryDays = obj.diff;
      this.calcBZQ();
    },

    /**
     * 选择供应商弹窗
     */
    showSupplierSelectDialog() {
      this.SET_SHOW({ showSettingGoodsSupplierDialog: true });
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    checkCode(code) {
      if (!code || (this.goodsDetail.toString() !== '' && this.goodsDetail.code === code)) {
        this.SET_SHOW({ codeExist: false });
        return;
      }
      let oTimeout = null;
      if (!this.changeWait) {
        this.changeWait = true;
        oTimeout = setTimeout(() => {
          this.SET_SHOW({ codeExist: false });
          var th = code.length;
          if (th === 13 || th === 12 || th === 8) { // 8 12 13位的条码直接去查
            this.searchCode(code);
          } else {
            this.checkExist('code', code);
          }
          this.changeWait = false;
        }, 100);
      } else {
        clearTimeout(oTimeout);
      }
    },
    // code失焦检索是否已存在该商品
    // 如果存在，提示存在并清空
    // 如果不存在，请求新商品接口，将获取到的信息展示出来
    searchCode(code) {
      // 如果没有打开新增画面，则不进行查询
      if (this.showAddGoods === false) {
        return false;
      }
      // 扫码枪扫完商品后搜索商品
      goodService.existGoods({'code': code}, res => {
        var json = demo.t2json(res);
        // 本地库
        if (json.length !== 0) {
          this.SET_SHOW({ codeExist: true });
        } else {
          this.connectCheck(code);
        }
      });
    },
    connectCheck(code) {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，云搜索商品功能暂时无法使用');
        return;
      }
      this.selectCodeLib(code);
    },
    selectCodeLib(code) { // 从条码库获取商品信息
      var params = {
        code: code || this.oneGoods.code,
        sysUid: $userinfo.sysUid,
        sysSid: $userinfo.sysSid
      };
      demo.$http
        .post(this.$rest.getProducts, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: this.token
          }
        })
        .then(res1 => {
          if (res1.data.data === null || ['4', ''].includes(res1.data.code)) {
            this.SET_SHOW({ codeExist: false });
            if (res1.data.code !== 200) {
              demo.msg('warning', res1.data.msg);
            }
            return;
          }
          var json1 = res1.data.data;
          if (json1.code !== '') {
            this.oneGoods.pinyin = this.oneGoods.pinyin || json1.pinyin;
            this.SET_SHOW({ suggestPrice: Number(json1.salePrc).toFixed(2) !== '0.00' ? Number(json1.salePrc).toFixed(2) : '' });
            this.oneGoods.purPrice = this.oneGoods.purPrice || Number(json1.purPrc).toFixed(2);
            this.oneGoods.image = this.oneGoods.image || json1.image;
            this.oneGoods.sync_g = json1.syncG;
            if (json1.picsList.length !== 0 && !json1.image) {
              this.oneGoods.image = this.oneGoods.image || json1.image;
              this.oneGoods.sync_g = json1.syncG;
            }
            if (this.oneGoods.name.trim() === '') {
              this.oneGoods.name = json1.name;
              this.checkExist('name');
            }
          }
        })
        .catch(error => { console.error(error); });
    },
    checkExist(flag, code) {
      this.setFlag(flag);
      let data = _.clone(this.oneGoods);
      this.sonarFlag(flag, data);
      if (flag === 'name') {
        this.SET_SHOW({ nameExist: false });
      }
      if (code) {
        data.code = code;
      }
      goodService.existGoods(data, res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          if (json[0].errMsg === '商品名重复') {
            this.SET_SHOW({ nameExist: true });
            this.nameMsg = '商品名称重复，请修改商品名称';
            return false;
          }
          if (json[0].errMsg === '商品条码重复') {
            this.SET_SHOW({ codeExist: true });
            return false;
          }
        } else {
          if (flag === 'code') {
            this.SET_SHOW({ codeExist: false });
          }
        }
      });
    },
    deleteGoods() {
      if (!this.$employeeAuth('delete_products')) {
        return;
      }
      this.show_delete = true;
    },
    sonarFlag(flag, data) {
      if (flag === 'name') {
        data.fingerprint = '';
        data.code = '';
      }
      if (flag === 'code') {
        data.fingerprint = '';
        data.name = '';
      }
    },
    setFlag (flag) {
      let key = flag + 'Exist';
      let setMap = {};
      setMap[key] = false;
      this.SET_SHOW(setMap);
    },
    continueDelete() {
      goodService.delete({fingerprint: `'${this.oneGoods.fingerprint}'`}, () => {
        this.SET_SHOW({ returnGoodsDetail: [] });
        demo.msg('success', '删除商品成功');
        this.show_delete = false;
        this.closeAddGoods();
      });
    },
    repeatGoodsDelete() {
      let fingerprint = [];
      let mergeStock = 0;
      for (var i = 0; i < this.deleteGoodsInfo.length; i++) {
        fingerprint.push(this.deleteGoodsInfo[i].fingerprint);
        if (this.isMergeStock) {
          mergeStock += +this.deleteGoodsInfo[i].curStock;
        }
      }
      // 勾选了合并库存
      goodService.delete({fingerprint: `'${fingerprint.join("','")}'`}, () => {
        demo.msg('success', '删除商品成功');
        if (this.isMergeStock) {
          this.oneGoods.curStock = Number(this.oneGoods.curStock) + mergeStock;
        }
        this.showRepeatCodeDel = false;
        this.$refs.codesForm.validate();
      });
    },
    delProdImg() {
      this.$set(this.oneGoods, 'image', '');
    },
    judgeIsStock() {
      if (this.isStock === true) {
        this.SET_SHOW({
          addStockGoods: JSON.parse(JSON.stringify(this.oneGoods))
        });
      }
      this.SET_SHOW({ stockCode: '' });
    },
    getScaleInfo(data) {
      data['isBarcodeScalesGoods'] = this.oneGoods.isBarcodeScalesGoods;
      data['weighModelVal'] = this.oneGoods.weighModelVal ? this.oneGoods.weighModelVal : 0;
      data['expireDate'] = this.oneGoods.expiryDays ? this.oneGoods.expiryDays : 0;
      data['tare'] = this.oneGoods.tare ? this.oneGoods.tare : 0;
    },
    beforeSubmitGoods(showAddGoodsDialog) {
      if (this.submit_click) { // 防连点
        return;
      }
      this.submit_click = true;
      setTimeout(() => {
        this.submit_click = false;
      }, this.clickInterval);
      if (this.hasBarCodeScales && this.oneGoods.isBarcodeScalesGoods === 1 && !this.oneGoods.code) { // 传秤商品自动取号
        goodService.createBarcode(1)
          .then(code => {
            console.log(code, 'goodService.createBarcode res');
            this.oneGoods.code = code[0];
            this.checkExist('code');
            this.beforeSubmitCheck(showAddGoodsDialog);
          });
      } else {
        this.beforeSubmitCheck(showAddGoodsDialog);
      }
    },
    beforeSubmitCheck(showAddGoodsDialog) {
      var codeAndNameExists = this.codeExist || this.nameExist;
      if (codeAndNameExists) {
        demo.msg('warning', '请先修改重复商品信息！');
        return;
      }
      if (this.oneGoods.name === '') {
        demo.msg('warning', this.$msg.enter_product_name);
        return;
      }
      var checkBarCodeScalesAndGoods = this.hasBarCodeScales && this.oneGoods.isBarcodeScalesGoods === 1;
      if (checkBarCodeScalesAndGoods) { // 传秤商品校验
        let kgList = ['千克', '公斤', 'kg', 'Kg', 'kG', 'KG'];
        if (kgList.indexOf(this.oneGoods.unitName) === -1 && this.oneGoods.weighModelVal !== 1) {
          demo.msg('warning', '传秤商品称重单位必须为千克!');
          return;
        }
        if (this.oneGoods.code !== '') {
          if (!(/^20\d{5}$/.test(this.oneGoods.code))) {
            demo.msg('warning', '传秤商品条码必须为20开头的7位纯数字!');
            return;
          }
        }
      }
      if (Number(this.oneGoods.minStock) > Number(this.oneGoods.maxStock)) {
        demo.msg('warning', '商品库存上限应大于库存下限！');
        return;
      }
      if (Number(this.oneGoods.curStock) < -99999.999 || Number(this.oneGoods.curStock) > 99999.999) {
        demo.msg('warning', '商品库存应在-99999.999到99999.999之间，最多三位小数');
        return;
      }
      this.submitGoods(showAddGoodsDialog);
    },
    submitGoods(showAddGoodsDialog) { // 保存
      if (this.goodsDetail.toString() === '') {
        // 新增接口
        this.SET_SHOW({ returnGoodsDetail: this.oneGoods });
        let detail = _.cloneDeep(this.oneGoods);
        this.oneGoods.action = 'insert';
        this.oneGoods.initStock = detail.curStock;
        this.oneGoods.initPrice = detail.salePrice;
        this.oneGoods.initAmt = (Number(detail.purPrice) * Number(detail.curStock)).toFixed(2);
        this.SET_SHOW({ oneGoods: detail });
        this.oneGoods.manufactureDate = this.oneGoods.manufactureDate || null;
        this.oneGoods.expiryDays = this.oneGoods.expiryDays || null;
        this.oneGoods.supplierFingerprint = this.selectedSuppliersDetail.fingerprint || null;
        let data = {
          name: this.oneGoods.name,
          majorCode: this.oneGoods.majorCode,
          pinyin: this.oneGoods.pinyin,
          typeFingerprint: this.oneGoods.typeFingerprint,
          unitFingerprint: this.oneGoods.unitFingerprint,
          ingredient: '',
          level: '',
          remark: this.oneGoods.remark,
          image: this.oneGoods.image,
          isVipDisc: 1,
          lockSpecs: {},
          items: [this.oneGoods]
        };
        if (this.hasBarCodeScales) { // 打开了条码秤开关并且为传秤商品
          this.getScaleInfo(data);
        }
        console.log(data, '新增商品data');
        goodService.insert(data,
          () => {
            this.judgeIsStock();
            demo.msg('success', '新增商品成功!');
            this.closeAddGoods(showAddGoodsDialog);
          },
          err => {
            // this.show_loading = false;
            demo.msg('warning', err);
          });
      } else {
        this.updateGoodsSubmit();
      }
    },

    updateGoodsSubmit() { // 修改接口
      const good = JSON.parse(JSON.stringify(this.oneGoods));
      let myIsBarcodeScalesGoods = good.isBarcodeScalesGoods;
      good.action = 'update';
      good.preCurStock = this.goodsDetail.curStock;
      good.supplierFingerprint = this.selectedSuppliersDetail.fingerprint || null;
      if (this.deleteExtendCode) {
        good.extendCodes = this.deleteExtendCode;
      }
      let data = {
        id: good.id,
        name: good.name,
        majorCode: good.majorCode,
        majorFingerprint: good.majorFingerprint,
        code: good.code,
        fingerprint: good.fingerprint,
        image: good.image,
        pinyin: good.pinyin,
        typeFingerprint: good.typeFingerprint,
        unitFingerprint: good.unitFingerprint,
        remark: good.remark ? good.remark : '',
        isVipDisc: 1,
        lockSpecs: {},
        items: [good]
      };
      if (this.hasBarCodeScales) { // 打开了条码秤开关并且为传秤商品
        this.getScaleInfo(data);
        if (myIsBarcodeScalesGoods === 1) {
          this.$emit('openBarCodeScales');
        }
      }
      console.log(data, '修改商品data');
      goodService.update(data,
        () => {
          this.deleteExtendCode = null;
          console.log('修改商品成功!');
          demo.msg('success', '修改商品成功!');
          this.closeAddGoods();
        },
        err => {
          // this.show_loading = false;
          demo.msg('warning', err);
        })
    },

    /**
     * 关闭新增商品弹窗
     */
    closeAddGoods(showAddGoodsDialog) {
      this.SET_SHOW({ suggestPrice: '' });
      this.SET_SHOW({ goodsDetail: [] });
      this.SET_SHOW({ stockCode: '' });
      this.SET_SHOW({ addGoodsUnitId: '' });
      this.SET_SHOW({ addGoodsUnit: '' });
      this.SET_SHOW({ addGoodsUnitfingerprint: '' });
      this.oneGoods = {
        id: '',
        majorCode: '',
        code: '',
        name: '',
        pinyin: '',
        typeId: '2',
        typeName: '其他分类',
        typeFingerprint: md5('其他分类'),
        unitName: '',
        unitId: '',
        unitFingerprint: '',
        salePrice: '0.00',
        vipPrice: '0.00',
        purPrice: '0.00',
        curStock: '0',
        addStockNum: '0',
        minStock: '0',
        maxStock: '0',
        remark: '',
        image: '',
        has_image: 0,
        supplier_id: 0,
        initStock: 0,
        initPrice: 0,
        initAmt: 0,
        isBarcodeScalesGoods: 0,
        weighModelVal: 0,
        expireDate: 0,
        tare: 0,
        img_fingerprint: '',
        first_letters: '',
        isCodesGoods: 0,
        extendCodes: [],
        specs: {unlock: {}},
        supplier: ''
      };
      if (showAddGoodsDialog) {
        if (this.typeIdFromGoods !== '2') {
          this.oneGoods.typeId = this.typeIdFromGoods;
          this.oneGoods.typeName = this.typeNameFromGoods;
          this.oneGoods.typeFingerprint = this.typeFingerprintFromGoods;
        }
        $('#prodBarcode').focus();
        return;
      }
      this.SET_SHOW({ addGoodsCategoryId: '2', addGoodsCategory: '其他分类', addGoodsCategoryfingerprint: md5('其他分类') });
      this.typeIdFromGoods = '2';
      this.typeNameFromGoods = '其他分类';
      this.typeFingerprintFromGoods = md5('其他分类');
      this.SET_SHOW({ showAddGoods: false, addGoodsCategoryFrom: '' });
    },
    handleAvatarSuccess(res, file) {
      // this.oneGoods.image = this.$rest.upload + '/' + $config.systemName + '/' + $config.subName + '/upload' + file.response.data;
      this.oneGoods.image = this.$rest.upload + '/' + file.response.data;
      if (this.oneGoods.image.trim() !== '') {
        this.oneGoods.has_image = 1;
      }
    },
    beforeAvatarUpload(file) {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，上传失败！');
        return;
      }
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        demo.msg('warning', '上传图片只能是 JPG/PNG 格式!');
        return false;
      } else if (!isLt2M) {
        demo.msg('warning', '上传图片大小不能超过 2MB!');
        return false;
      } else {
        try {
          compressImage(file, 0.5).then(compressedFile => {
            return compressedFile;
          }).catch(() => {
            return false;
          });
        } catch (error) {
          console.error('图片压缩失败:', error);
          // 如果压缩失败，取消上传
          return false;
        }
      }
    },

    showTypeManageDialog() {
      this.SET_SHOW({ showTypeManage: true, goodsCurTypeFingerprint: this.oneGoods.typeFingerprint });
    },
    showSelectManage(name) {
      this.SET_SHOW({ showSelManage: true });
      let manageName = '单位';
      this.SET_SHOW({ selManageTypeName: manageName });
      this.SET_SHOW({ selectName: name });
      this.SET_SHOW({ agreeChooseCategory: true });
    },
    /**
     *  传秤相关
     */
    formatOneGoods() {
      this.oneGoods.salePrice = this.formatReturn('salePrice');
      this.oneGoods.vipPrice = this.formatReturn('vipPrice');
      this.oneGoods.purPrice = this.formatPurPriceReturn('purPrice');
      this.oneGoods.curStock = this.oneGoods.curStock === '' ? 0 : (this.stockOverflow ? this.oneGoods.curStock : this.formatReturn('curStock'));
      this.oneGoods.addStockNum = this.oneGoods.addStockNum === '' ? 0 : this.formatReturn('addStockNum');
      this.oneGoods.minStock = this.formatReturn('minStock');
      this.oneGoods.maxStock = this.formatReturn('maxStock');
    },
    editStockInput() {
      this.stockOverflow = false;
      this.oneGoods.curStock = this.$stockLimit({data: this.oneGoods.curStock, max: 99999.999, min: -99999.999, decimals: 3})
    },
    formatReturn(k) {
      if (isNaN(this.oneGoods[k]) === true) {
        return '0';
      }
      if (k === 'salePrice' || k === 'vipPrice' || k === 'purPrice') {
        return Number(this.oneGoods[k]) > 999999.99 ? 999999.99 : this.$toDecimalFormat(Math.abs(Number(this.oneGoods[k])), 2, true);
      } else {
        return this.formatCurStock(k);
      }
    },
    // 进货价格式化返回
    formatPurPriceReturn(k) {
      let value = this.oneGoods[k];
      if (isNaN(value)) {
        return '0.00';
      }
      let val = Math.abs(value);
      let dotIndex = String(val).indexOf('.');
      let dotAfterLength = dotIndex === -1 ? 0 : String(val).length - dotIndex - 1;
      let indexL = dotAfterLength <= 2 ? 2 : Math.min(dotAfterLength, 6);
      return Math.min(val, 999999.999999).toFixed(indexL);
    },
    formatCurStock (k) {
      if (Number(this.oneGoods[k]) > 99999.999) {
        return '99999.999';
      } else if (Number(this.oneGoods[k]) < 0 && (k === 'minStock' || k === 'maxStock')) {
        return '0.000';
      } else {
        if (Number(this.oneGoods[k]) < 0) {
          return '-' + Number((Math.abs(this.oneGoods[k]) + '').replace(/^()*(\d+)\.(\d\d\d).*$/, '$1$2.$3'));
        } else {
          return Number((this.oneGoods[k] + '').replace(/^()*(\d+)\.(\d\d\d).*$/, '$1$2.$3'));
        }
      }
    },
    classChoose() {
      // 禁用启用按钮样式获取
      var classStr = 'margin-right:220px;';
      if (!this.$employeeAuth('delete_products')) {
        classStr += 'opacity: 40%;';
      }
      classStr += 'border:1px solid #FF5555;color:#FF5555';
      this.banBtnStyle = classStr;
    },
    useSuggestPrice() {
      this.oneGoods.salePrice = this.suggestPrice;
    },
    getAddModelMargin() {
      let marginTop = document.body.clientHeight > 750 ? (document.body.clientHeight - 750) / 2 : 0;
      this.addModalBoxMargin = 'margin-top:' + marginTop + 'px;';
    },
    /**
     *  一品多码相关
     */
    multicodeClick() {
      this.codesRuleForm.code = this.oneGoods.code;
      // 表单扩展码需要key值
      this.codesRuleForm.extendCodes = this.oneGoods.extendCodes && this.oneGoods.extendCodes.length
        ? this.returnExtendCodesFmt() : [{key: Date.now(), value: ''}];
      this.showMulticodeManage = true;
      setTimeout(() => {
        this.$refs.codesForm.clearValidate();
      });
    },
    returnExtendCodesFmt() {
      let arr = [];
      for (let i = 0; i < this.oneGoods.extendCodes.length; i++) {
        let item = this.oneGoods.extendCodes[i];
        if (item.action !== 'delete') {
          arr.push({key: new Date().getTime() - i, value: this.oneGoods.extendCodes[i].extBarcode});
        }
      }
      if (arr.length === 0) {
        arr.push({key: Date.now(), value: ''});
      }
      return arr;
    },
    addExtendCode(nextIndex) {
      if (this.codesRuleForm && this.codesRuleForm.extendCodes.length > 19) {
        return;
      }
      this.codesRuleForm.extendCodes.push({
        key: Date.now(),
        value: ''
      });
      this.$nextTick(() => {
        this.$refs.scrollDiv.scrollTop = this.$refs.scrollDiv.scrollHeight;
        if (nextIndex && this.$refs['extendInput' + (nextIndex)]) {
          this.$refs['extendInput' + (nextIndex)][0].focus();
        }
      });
    },
    removeExtendCode(item) {
      let index = this.codesRuleForm.extendCodes.indexOf(item);
      if (index !== -1) {
        this.codesRuleForm.extendCodes.splice(index, 1)
      }
    },
    extendCodeFocus() {
      this.$refs['extendInput0'][0].focus();
    },
    isCodesChange(value) { // 一品多码开关
      if (value === 1 && this.oneGoods.isBarcodeScalesGoods === 1) {
        this.oneGoods.isCodesGoods = 0;
        console.log(this.oneGoods.isCodesGoods);
        demo.msg('warning', '请先关闭商品传秤开关');
        return;
      }
      if (!value && this.goodsDetail.toString() !== '' &&
        this.oneGoods.extendCodes && this.oneGoods.extendCodes.length) {
        if (!this.$employeeAuth('create_products')) {
          this.multiGoodsClose();
        } else {
          this.showCodesGoodsCloseDiolag = true;
          this.oneGoods.isCodesGoods = 1;
        }
      }
    },
    splitCodesGoods() {
      if (this.splitClick) {
        clearTimeout(this.splitClick);
        return;
      }
      this.splitClick = setTimeout(() => {
        this.splitClick = false;
      }, 1000);
      console.log('splitCodesGoods执行一次');
      let codesArr = this.oneGoods.extendCodes.map(item => { return item.extBarcode.action === 'insert'; });
      var params = {
        list: codesArr.join()
      };
      demo.$http
        .get(this.$rest.getBarcodes, {
          params: params
        }, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: this.token
          }
        })
        .then(res => {
          if (res.data.data === null || ['4', ''].includes(res.data.code)) {
            if (res.data.code !== 200) {
              demo.msg('warning', res.data.msg);
            }
            return;
          }
          var json = res.data.data;
          let barcodeObj = {};
          json.forEach(item => {
            barcodeObj[item.code] = item.name;
          });
          let arr = [];
          this.oneGoods.extendCodes.forEach(item => {
            if (item.action !== 'delete') {
              let obj = _.cloneDeep(this.oneGoods);
              arr.push(Object.assign(obj, {
                action: 'insert',
                name: barcodeObj[item.extBarcode] || '',
                code: item.extBarcode,
                curStock: 0
              }));
            }
          })
          this.splitRuleForm = arr;
          //
          this.showCodesGoodsCloseDiolag = false;
          this.splitCodesGoodsDialog = true;
        })
        .catch(error => {
          const arr = [];
          this.oneGoods.extendCodes.forEach(item => {
            if (item.action !== 'delete') {
              let obj = _.cloneDeep(this.oneGoods);
              arr.push(Object.assign(obj, {
                action: 'insert',
                name: '',
                code: item.extBarcode,
                curStock: 0
              }));
            }
          })
          this.splitRuleForm = arr;
          this.showCodesGoodsCloseDiolag = false;
          this.splitCodesGoodsDialog = true;
        });
    },
    splitCodesSubmit() {
      for (let item of this.splitRuleForm) {
        if (item.name === '') {
          this.$set(item, 'nameErrorMsg', '请填写商品名称');
          return;
        }
        if (!item.hasOwnProperty('nameErrorMsg') || item.nameErrorMsg !== '') {
          this.checkItemName(item);
          return;
        }
        if (item.salePrice === '') {
          this.$set(item, 'showPriceRequire', true);
          return;
        }
      }
      console.log('一品多码拆成一品一码商品验证通过');
      this.oneGoods.multiToSingleGoods = this.splitRuleForm;
      this.oneGoods.isCodesGoods = 0;
      this.splitCodesGoodsDialog = false;
    },
    multiGoodsClose() {
      this.showCodesGoodsCloseDiolag = false;
      this.oneGoods.isCodesGoods = 0;
      this.oneGoods.extendCodes.forEach(item => {
        item.action = 'delete';
      });
      this.deleteExtendCode = JSON.parse(JSON.stringify(this.oneGoods.extendCodes));
      this.oneGoods.extendCodes = [];
    },
    checkItemName(item) {
      delete item.nameErrorMsg;
      if (item.name === '') {
        this.$set(item, 'nameErrorMsg', '请填写商品名称');
      } else if (this.splitRuleForm.filter(sp => { return sp.name === item.name; }).length > 1) {
        this.$set(item, 'nameErrorMsg', '商品名称重复，请修改商品名称');
      } else {
        let data = _.cloneDeep(item);
        this.sonarFlag('name', data);
        goodService.existGoods(data, res => {
          var json = demo.t2json(res);
          if (json.length > 0) {
            if (json[0].errMsg === '商品名重复') {
              this.$set(item, 'nameErrorMsg', '商品名称重复，请修改商品名称');
            }
          } else {
            this.$set(item, 'nameErrorMsg', '');
          }
        }, () => {
          this.$set(item, 'nameErrorMsg', '');
        });
      }
    },
    checkItemCode(item) {
      this.$set(item, 'showPriceRequire', item.salePrice === '');
    },
    autoMakeCode(prop) {
      goodService.makeCode(1, false, res => {
        this.codesRuleForm.code = res[0];
        this.$refs.codesForm.validateField(prop);
      }, () => {
        demo.msg('warning', '自动生成条码失败，请手动输入');
        this.$refs.codesForm.validateField(prop);
      });
    },
    deleteRepeatGood(flag, item) {
      this.deleteGoodsInfo = flag ? this.codesRuleForm.codeError : item.codeError;
      this.showRepeatCodeDel = true;
    },
    isShowDelCheckbox(errMsg) {
      return errMsg === '条码已被使用，请重新输入 或';
    },
    returnPreFingerprint() {
      return this.oneGoods.fingerprint ? this.oneGoods.fingerprint : '';
    },
    codesSubmit() {
      console.log(this.codesRuleForm);
      this.$refs.codesForm.validate(valid => {
        if (valid) {
          this.oneGoods.code = this.codesRuleForm.code;
          let extendCodesArr = [];
          this.codesRuleForm.extendCodes.forEach(item => {
            if (item.value) {
              extendCodesArr.push(item.value);
            }
          });
          let preExtendCodes = this.goodsDetail.toString() === '' ? [] : this.goodsDetail.extendCodes;
          let preExtendArr = [];
          preExtendCodes.forEach(item => {
            preExtendArr.push(item.extBarcode);
            if (extendCodesArr.indexOf(item.extBarcode) === -1) {
              item.action = 'delete';
            }
          });
          extendCodesArr.forEach(code => {
            if (preExtendArr.indexOf(code) === -1) {
              preExtendCodes.push({
                action: 'insert',
                goodFingerprint: this.returnPreFingerprint(),
                extBarcode: code
              });
            }
          });
          this.oneGoods.extendCodes = preExtendCodes;
          console.log(this.oneGoods, 'codesSubmit+');
          this.checkCode(this.oneGoods.code);
          this.showMulticodeManage = false;
        } else {
          return false;
        }
      });
    }
  },
  watch: {
    showAddGoods() {
      // 每次打开弹窗保质期默认选择天数
      this.selectValue = 1;
      this.qualityGuaranteeDate = '';
      this.calcGuaranteeDate = '-';
      this.SET_SHOW({ nameExist: false });
      this.SET_SHOW({ codeExist: false });
      if (!this.showAddGoods) {
        this.closeAddGoods();
        return;
      }
      if (this.midGoodsName !== '') {
        this.oneGoods.name = this.midGoodsName;
        this.SET_SHOW({midGoodsName: ''});
      }
      if (this.scanGoodsCode !== '') {
        this.oneGoods.code = this.scanGoodsCode;
        this.checkCode(this.oneGoods.code);
        this.SET_SHOW({scanGoodsCode: ''});
      }
      var that = this;
      setTimeout(function() {
        if (that.goodsDetail.toString() !== '') {
          $('#ad3').focus();
        } else {
          $('#prodBarcode').focus();
        }
        if ((that.isStock === true || that.isStockInventory) && that.stockCode !== '') {
          that.oneGoods.code = that.stockCode;
          that.searchCode();
        }
      }, 10);
      // 获取禁用按钮样式
      this.classChoose();
      if (this.goodsDetail.length === 0) {
        this.stop_code = false;
      }
    },
    goodsDetail() {
      if (this.goodsDetail.toString() !== '') {
        this.oneGoods = _.cloneDeep(this.goodsDetail);
        this.oneGoods.minStock = Number(this.oneGoods.minStock).toFixed(3);
        this.oneGoods.maxStock = Number(this.oneGoods.maxStock).toFixed(3);
        // 将原库存数存下来,方便进行调整时的接口上传
        this.old_curStock = _.cloneDeep(this.goodsDetail).curStock;
        if (Number(this.oneGoods.curStock) > 99999.999 || Number(this.oneGoods.curStock) < -99999.999) {
          this.stockOverflow = true;
        }
        if (this.oneGoods.status === '禁用' && this.oneGoods.code.length > 0) {
          this.stop_code = true;
        } else {
          this.stop_code = false;
        }
        console.log('====商品详情：', this.oneGoods);
        this.calcBZQ();
        this.SET_SHOW({ selectedSuppliersDetail: {
          id: this.oneGoods.supplierId,
          fingerprint: this.oneGoods.supplierFingerprint,
          name: this.oneGoods.supplierName,
          contacter: this.oneGoods.supplierContacter,
          mobile: this.oneGoods.supplierMobile
        } });
        return;
      }
      this.SET_SHOW({ selectedSuppliersDetail: '' });
    },
    async showTypeManage() {
      if (!this.showTypeManage) {
        const isExis = await typeService.typeDelCheck(this.addGoodsCategoryfingerprint)
        if (!isExis) {
          this.oneGoods.typeId = '2';
          this.oneGoods.typeName = '其他分类';
          this.oneGoods.typeFingerprint = md5('其他分类');
        } else {
          this.typeIdFromGoods = this.addGoodsCategoryId;
          this.typeNameFromGoods = this.addGoodsCategory;
          this.typeFingerprintFromGoods = this.addGoodsCategoryfingerprint;
        }
      }
    },
    addGoodsCategoryId() {
      if (this.addGoodsCategoryFrom === 'goods') {
        this.typeIdFromGoods = this.addGoodsCategoryId;
        this.typeNameFromGoods = this.addGoodsCategory;
        this.typeFingerprintFromGoods = this.addGoodsCategoryfingerprint;
        this.SET_SHOW({ addGoodsCategoryFrom: '' });
      }
      this.oneGoods.typeId = this.addGoodsCategoryId;
      this.oneGoods.typeName = this.addGoodsCategory;
      this.oneGoods.typeFingerprint = this.addGoodsCategoryfingerprint;
    },
    addGoodsUnit() {
      this.oneGoods.unitId = this.addGoodsUnitId;
      this.oneGoods.unitName = this.addGoodsUnit;
      this.oneGoods.unitFingerprint = this.addGoodsUnitfingerprint;
    },
    //    格式化所有数据
    oneGoods() {
      this.formatOneGoods();
      if (this.oneGoods.image !== '') {
        this.oneGoods.has_image = 1;
      } else {
        this.oneGoods.has_image = 0;
      }
    },
    'oneGoods.name'() {
      this.oneGoods.first_letters = pyCode.getPyCode(this.oneGoods.name);
    },
    'oneGoods.isBarcodeScalesGoods'() {
      if (this.oneGoods.isCodesGoods === 1 && this.oneGoods.isBarcodeScalesGoods === 1) {
        this.oneGoods.isBarcodeScalesGoods = 0;
        demo.msg('warning', '请先关闭商品一品多码开关');
        return;
      }
      if (this.oneGoods.isBarcodeScalesGoods === 1) {
        if (!this.oneGoods.weighModelVal) {
          this.oneGoods.weighModelVal = 0;
        }
        if (!this.oneGoods.expireDate) {
          this.oneGoods.expireDate = 0;
        }
        if (!this.oneGoods.tare) {
          this.oneGoods.tare = 0;
        }
      }
    }
  },
  filters: {
    codesFilter(item) {
      return item.extendCodes && item.extendCodes.length > 0 ? item.code + '，...' : item.code;
    }
  },
  computed: {
    showCurStock() {
      return this.goodsDetail.toString() !== '' || Number(this.oneGoods.curStock) !== 0;
    },
    profit() {
      let salePrice = isNaN(this.oneGoods.salePrice) ? 0 : this.oneGoods.salePrice;
      let purPrice = isNaN(this.oneGoods.purPrice) ? 0 : this.oneGoods.purPrice;
      return (salePrice - purPrice).toFixed(2);
    },
    profitRate() {
      let salePrice = isNaN(this.oneGoods.salePrice) ? 0 : this.oneGoods.salePrice;
      let purPrice = isNaN(this.oneGoods.purPrice) ? 0 : this.oneGoods.purPrice;
      if (!Number(salePrice)) {
        return '0.00%';
      }
      let rate = (salePrice - purPrice) / salePrice * 100;
      if (rate < -99999.99) {
        rate = -99999.99;
      } else if (rate > 99999.99) {
        rate = 99999.99;
      }
      return (isNaN(rate) || Math.abs(rate) === Infinity ? '0.00' : rate.toFixed(2)) + '%';
    },
    ...mapState({
      showAddGoods: state => state.show.showAddGoods,
      showTypeManage: state => state.show.showTypeManage,
      addGoodsCategory: state => state.show.addGoodsCategory,
      addGoodsCategoryId: state => state.show.addGoodsCategoryId,
      addGoodsCategoryfingerprint: state => state.show.addGoodsCategoryfingerprint,
      addGoodsCategoryFrom: state => state.show.addGoodsCategoryFrom,
      addGoodsUnit: state => state.show.addGoodsUnit,
      addGoodsUnitId: state => state.show.addGoodsUnitId,
      addGoodsUnitfingerprint: state => state.show.addGoodsUnitfingerprint,
      isStock: state => state.show.isStock,
      isStockInventory: state => state.show.isStockInventory,
      goodsDetail: state => state.show.goodsDetail,
      stockCode: state => state.show.stockCode,
      token: state => state.show.token,
      loginInfo: state => state.show.loginInfo,
      sysUid: state => state.show.sys_uid,
      sysSid: state => state.show.sys_sid,
      midGoodsName: state => state.show.midGoodsName,
      scanGoodsCode: state => state.show.scanGoodsCode,
      goodsNameLength: state => state.show.goodsNameLength,
      clickInterval: state => state.show.clickInterval,
      suggestPrice: state => state.show.suggestPrice,
      showExistGoods: state => state.show.showExistGoods,
      nameExist: state => state.show.nameExist,
      codeExist: state => state.show.codeExist,
      codeTimer: state => state.show.codeTimer,
      zgznActive: state => state.show.zgznActive,
      addProdFromStock: state => state.show.addProdFromStock,
      selectedSuppliersDetail: state => state.show.selectedSuppliersDetail
    })
  },
  created() {
    let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
    this.hasBarCodeScales = dataInfo.hasBarCodeScales ? demo.t2json(dataInfo.hasBarCodeScales) : false;
    this.configToken = $config.token;
  },
  mounted() {
    this.uploadData = {
      type: 1,
      sys_uid: this.sysUid,
      sys_sid: this.sysSid
    };
    let that = this;
    that.getAddModelMargin();
    window.onresize = () => {
      if (that.addGoodMargin) {
        clearTimeout(that.addGoodMargin);
      }
      that.addGoodMargin = setTimeout(() => {
        that.getAddModelMargin();
      }, 300);
    };
  },
  beforeDestroy() {
    window.onresize = null;
    this.SET_SHOW({ addProdFromStock: false });
  }
};
</script>
