<style lang="less" scoped>
.pc_fog {
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
}
.pc_fog1 {
  width: 1100px;
  margin: 0 auto;
  margin-top: 12%;
  color: @themeFontColor;
}
.pc_fog1 img {
  float: left;
}
.pc_fog11 {
  float: left;
  margin-left: 10px;
  margin-top: 120px;
}
.pc_fog12 {
  overflow: hidden;
}
.pc_fog12 div {
  width: 70px;
  text-align: center;
  line-height: 61px;
  float: left;
  font-size: 16px;
  font-weight: bold;
}
.pc_fog12 input {
  width: 320px;
  height: 61px;
  background: #f5f8fb;
  border-radius: 10px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  font-weight: bold;
}
.pc_fog13 {
  font-size: 14px;
  font-weight: bold;
  margin-left: 70px;
  line-height: 14px;
  margin-top: 30px;
}
.pc_fog13 span {
  cursor: pointer;
}
.pc_fog14 {
  width: 165px;
  height: 135px;
  background-image: linear-gradient(90deg, #D8B774 0%, #DEB071 100%);
  border-radius: 10px;
  text-align: center;
  line-height: 135px;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
}
.pc_fog15 {
  margin-top: 25px;
  cursor: pointer;
}
.pc_fog15 img {
  float: left;
}
.pc_fog15 div {
  float: left;
  margin-left: 10px;
  line-height: 14px;
  font-size: 14px;
  font-weight: bold;
  margin-top: 5px;
}
.pc_fog2 {
  font-size: 14px;
  line-height: 30px;
  color: #333;
}
.pc_fog3 {
  font-size: 14px;
  line-height: 30px;
  color: #333;
  margin-top: 5px;
}
.pc_fog4 {
  width: 100%;
  margin-top: 15px;
  height: 48px;
  line-height: 46px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: linear-gradient(90deg, #69d6fe, #16aaff);
  border-radius: 6px;
  cursor: pointer;
}
.pc_van_botton {
  width: 147px;
  height: 60px;
  background: #f5f8fb;
  border: 1px solid #e3e6eb;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  color: @themeFontColor;
  margin-left: 10px;
}
#getverifycode {
  background: @themeBackGroundColor;
  color: @butFontColor;
}
#resetPasswordCheck {
    background: @themeBackGroundColor;
    border: none;
    width: 253px;
    margin-left: 15px;
    font-size: 20px;
    color: #FFF;
}
</style>
<template>
  <div class='pc_fog'>
    <div class='pc_fog1'>
      <!--左侧logo图-->
      <img alt="" src='../../image/zgzn-pos/pc_login_logo.png' style='margin-top: 130px;margin-left: 60px;' />
      <!--右侧手机号密码-->
      <div class='pc_fog11' style='margin-left: 100px;margin-top: 60px;'>
        <div class='pc_fog12'>
          <div><span style="color:red">*</span>手机号</div>
          <input
            v-model='phoneno'
            type='text'
            v-input-phone
            maxlength="11"
            placeholder='请输入手机号'
            style='float: left;'
          />
          <van-button
            v-if='ischeck'
            slot='button'
            type='default'
            class='pc_van_botton'
            id="getverifycode"
            @click='getverifycode'
          >发送验证码</van-button>
          <van-button
            v-else
            slot='button'
            disabled
            class='pc_van_botton'
            style='float: left;'
            type='default'
          >
            <van-count-down
              ref='countDown'
              :time='60 * 1000'
              format='已发送( ss s)'
              @finish='finished'
              style='width: 100%;text-align: center;font-size: 18px;font-weight: bold;color: #567485;height: 60px;line-height: 60px;margin: 0;padding: 0;'
            />
          </van-button>
        </div>
        <div class='pc_fog12' style='margin-top: 13px;'>
          <div><span style="color:red">*</span>验证码</div>
          <input v-model='verifycode' placeholder='请输入验证码' maxlength="6"
            @input="verifycode = $allNumberLimit(verifycode)" style='width: 467px;' type='text' />
        </div>
        <div class='pc_fog12' style='margin-top: 13px;'>
          <div><span style="color:red">*</span>新密码</div>
          <input
            v-model='password'
            type='password'
            style='width: 467px;'
            maxlength="20"
            placeholder='请输入密码（长度为8～20个字符）由数字、字母组成'
          />
        </div>
        <van-row style='margin-top:5vh;'>
          <van-col style='float:left;'>
            <van-button
              round
              type='default'
              @click='backToLogin'
              class='pc_van_botton'
              style='width: 253px;font-size: 20px;margin-left: 15px;'
            >返回</van-button>
          </van-col>

          <van-col style='float:left;'>
            <van-button
              round
              type='info'
              @click='resetPasswordCheck'
              class='pc_van_botton'
              id="resetPasswordCheck"
            >确定</van-button>
          </van-col>
        </van-row>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import {
  Row,
  Col,
  Field,
  Button,
  Cell,
  CellGroup,
  Grid,
  GridItem,
  CountDown
} from 'vant';
export default {
  components: {
    [Field.name]: Field,
    [Row.name]: Row,
    [Col.name]: Col,
    [Button.name]: Button,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [CountDown.name]: CountDown
  },
  data() {
    return {
      oHeight: '',
      ischeck: true,
      phoneno: '',
      verifycode: '',
      password: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    backToLogin() {
      this.SET_SHOW({ isForgetpwd: false, isLogin: true });
    },
    // 清除验证码倒计时
    finished() {
      this.ischeck = true;
    },
    iosfocus(flag) {
      if (/iPhone/i.test(navigator.userAgent)) {
        if (flag == 1) {
          setTimeout(function() {
            document
              .querySelector('.register')
              .scrollTo(0, _this.oHeight - $(document).height() - 100);
          }, 200);
        }
      }
    },

    // 验证手机号
    chkPhoneno() {
      if (/^1[3|4|5|6|7|8|9]\d{9}$/.test(this.phoneno)) {
        return true;
      } else {
        return false;
      }
    },

    // 获取验证码
    getverifycode() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      let _this = this;
      if (!_this.chkPhoneno()) {
        demo.msg('warning', _this.$msg.enter_mobile_number);
        return;
      }
      _this.ischeck = false;
      var Content_Type = 'application/json';
      // 当前手机号是否已注册
      this.$http
        .post(this.$rest.pc_checkUserIsExit, {
          subName: $config.subName,
          phone: this.phoneno
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          if (res.data.code === 200) {
            if (!res.data.data) { // 用户存在
              demo.msg('warning', '用户不存在，请先完成注册!');
              _this.finished();
            } else {
              var params = {
                phone: _this.phoneno,
                flag: 1,
                systemName: $config.systemName,
                subName: $config.subName
              };
              demo.$http
                .post(_this.$rest.sendVerifyCode, params, {
                  headers: { 'Content-Type': Content_Type }
                })
                .then(function(res) {
                  if (res.data.code !== 200) {
                    demo.msg('error', res.data.msg);
                    _this.finished();
                  } else {
                    demo.msg('success', '验证码已发送，请注意查收');
                  }
                })
                .catch(function(error) {
                  _this.finished();
                  console.error(error);
                });
            }
          } else {
            _this.finished();
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(error => {
          console.error(error);
        });
    },
    checkPassword () {
      // 密码由8位以上数字，字母
      // const re = new RegExp(`(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,20}$`);
      const re = new RegExp(`^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9]{8,20}$`);
      return re.test(this.password.trim());
    },
    resetPasswordCheck() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      let _this = this;
      if (!_this.chkPhoneno()) {
        demo.msg('warning', _this.$msg.enter_mobile_number);
        return;
      }
      if (_this.verifycode.trim() === '') {
        demo.msg('warning', _this.$msg.enter_verification_code);
        return;
      }
      if (_this.verifycode.trim().length < 6) {
        demo.msg('warning', _this.$msg.right_length_verification_code);
        return;
      }
      if (!this.checkPassword()) {
        demo.msg('warning', _this.$msg.enter_new_password);
        return;
      }
      this.resetPassword();
    },
    // 重置密码
    resetPassword() {
      let _this = this;
      // 验证码正确性校验
      let verData = {
        phone: this.phoneno,
        code: this.verifycode,
        systemName: $config.systemName,
        subName: $config.subName,
        flag: '1'
      };
      this.$http.post(this.$rest.checkVerifyCode, verData, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          console.log(res);
          if (res.data.code === 200) { // 验证码校验完成后重置密码
            var params = {
              username: _this.phoneno,
              password: md5(_this.password),
              systemName: $config.systemName,
              subName: $config.subName
            };
            demo.$http
              .post(_this.$rest.resetPassword, params, {
                headers: { 'Content-Type': 'application/json' }
              })
              .then(function(res) {
                if (res.data.code === 200) {
                  if (res.data.data) {
                    _this.SET_SHOW({ password: md5(_this.password) });
                    demo.msg('success', '重置密码成功!');
                    _this.SET_SHOW({ isLogin: true, isForgetpwd: false });
                  }
                } else {
                  demo.msg('warning', res.data.msg);
                  _this.finished();
                }
              })
              .catch(function(error) {
                console.error(error);
              });
          } else {
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(err => {
          console.log(err, '验证码校验异常');
        });
    }
  },
  computed: mapState({
    showTipsDialog: state => state.show.showTipsDialog
  }),
  watch: {
    showTipsDialog() {
      if (this.showTipsDialog) {
        external.closeMainForm();
      }
    }
  }
};
</script>
