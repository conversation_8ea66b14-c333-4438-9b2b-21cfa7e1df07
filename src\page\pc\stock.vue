<style lang="less" scoped>
/deep/ .sto1 .el-input__inner {
  color: #B2C3CD;
}
.scroll-bar {
  direction: rtl;
}
/deep/ .el-dialog__header {
  display: none;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-input.is-active .el-input__inner,
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-input__inner {
  color: @themeFontColor;
  font-size: 16px;
  height: 40px;
}
/deep/ .el-table--scrollable-x .el-table__body-wrapper {
  overflow-x: hidden;
}
/deep/ .el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-upload--picture-card:hover,
/deep/ .el-upload:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  display: block !important;
  font-weight: bold;
  font-size: 16px;
}
.submit-sure {
  width: 530px;
  background: #BDA16A;
  margin: 0 auto;
  cursor: pointer;
  height: 54px;
  line-height: 54px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #FFF;
  border-radius: 6px;
  margin-top: 40px;
}
.submit-close {
  float: right;
  font-size: 24px;
  color: #567485;
  margin-top: 25px;
  cursor: pointer;
}
.pc_sto1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
  display: flex;
  align-items: center;
}
.pc_sto11 {
  width: 600px;
  min-height: 289px;
  background: #fff;
  margin: 0 auto;
  padding-bottom: 10px;
  border-radius: 6px;
}

.new_good_add_wrap {
  width: 920px;
  height:700px;
  .add_content {
    height: 520px;
    margin-left: 20px;
    margin-top: 20px;
    width: 880px;
    border-radius: 5px;
    border: 1px solid rgba(227, 230, 235, 100);
  }
  .add_good_table {
    overflow-y: auto;
    overflow-x: hidden;
    height: 444px;
  }
}
.pc_sto12 {
  width: 93%;
  height: 60px;
  border-bottom: 1px solid rgba(227, 230, 235, 100);;
  font-size: 18px;
  margin-left: 20px;
  line-height: 60px;
  text-align: left;
  font-weight:bold;
  color: @themeFontColor;
}
.pc_sto13 {
  float: right;
  font-size: xx-large;
  color: rgba(178, 195, 205, 100);
  margin-top: -4px;
  cursor:pointer;
  margin-right: -4px;
}
.pc_sto14 {
  width: 93%;
  margin-left: 20px;
  margin-top: 16px;
  height: 46px;
}
.el-alert--warning.is-light {
  height: 44px;
}
.el-alert__title {
  font-size: 16px;
  line-height: 24px;
}
.pc_sto15 {
  width: 93%;
  margin-left: 20px;
  margin-top: 20px;
  font-size: 16px;
  a {
    span {
      color: @themeBackGroundColor;
      cursor:pointer;
      text-decoration: none;
      text-decoration-line:underline;
    }
  }
}
.pc_sto16 {
  width: 93%;
  margin-left: 20px;
  margin-top: 30px;
  height: 57px;
  line-height: 18px;
  background-color: rgba(249, 251, 251, 100);
  border: 1px solid rgba(227, 230, 235, 100);
}
.pc_sto16 div {
  display: flex;
  margin-top: 15px;
  line-height: 23px;
}
.pc_sto16 span {
  color: @themeFontColor;
  margin-left: 10px;
  font-size: 16px;
}
.pc_sto16 img {
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin-left: 15px;
}
.pc_sto17 {
  width: 93%;
  margin-left: 20px;
  margin-top: 30px;
  margin-bottom: 20px;
  text-align:justify;
}
.pc_sto18 {
  display: inline-flex;
  width: 320px;
  height: 44px;
  border-radius: 4px;
  // margin-left: 14px;
  border: 1px solid  @themeBackGroundColor;
  background: @themeBackGroundColor;
  cursor: pointer;
}
.pc_sto19 {
 margin-left: 8px;
 color: rgba(178, 195, 205, 100);
 font-size: 14px;
}
.downloadfaildata:hover {
  text-decoration: underline;
}
.pc_sto2 {
  width: 118px;
  position: fixed;
  right: -6px;
  top: 100px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 46px);
}
.pc_sto22 {
  height: 59px;
  line-height: 59px;
}
.pc_sto23 {
  height: calc(100% - 49px);
  overflow: scroll;
}
.pc_sto23 div {
  border-bottom: 1px solid #fff;
}
.pc_sto24 {
  float: left;
  height: 70px;
  font-size: 15px;
  margin-left: 15px;
  width: 240px;
}
.pc_sto25_btn {
  margin-top: 26px;
  display: flex;
  flex-wrap: wrap;
}
.pc_sto25 {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 115px;
  width: 100%;
  overflow-x: scroll;
  background: #fff;
  z-index: 1;
  color: @themeFontColor;
  border-top: 1px dashed #e3e6eb;
}
.pc_sto27 {
  float: left;
  font-weight: bold;
  text-indent: 40px;
}
.pc_sto28 {
  position: absolute;
  width: 100%;
  color: #ff773b;
  right: 0;
}
.pc_sto28 div {
  float: left;
  width: 20%;
  text-align: center;
}
.pc_sto28 span {
  color: @themeFontColor;
}
.pc_sto29 {
  width: 100%;
  min-width: 830px;
  overflow: hidden;
  font-size: 16px;
  margin-top: 10px;
}
.pc_sto3 {
  width: 33%;
  overflow: hidden;
  float: left;
}
.pc_sto31 {
  width: 105px;
  text-indent: 20px;
  float: left;
  line-height: 40px;
  font-weight: 700;
}
.pc_sto32 {
  float: left;
  width: calc(100% - 105px);
  // min-width: 170px;
}
.pc_sto33 {
  float: left;
  width: calc(100% - 123px);
}
.pc_sto34 {
  overflow: hidden;
  cursor: pointer;
  margin-top: 12px;
  width: 370px;
  direction: ltr;
}
.pc_sto34 img {
  margin-right: 10px;
  width: 55px;
  height: 55px;
  float: left;
  border: 0px;
}
.pc_sto35 {
  float: left;
  width: calc(100% - 158px);
  font-size: 16px;
  overflow: hidden;
  color: @themeFontColor;
  border-bottom: 1px solid #e3e6eb;
}
.pc_sto35 div {
  line-height: 22px;
  overflow: hidden;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pc_sto35_flex {
  display: flex;
  justify-content: left;
  padding-top: 4px;
  font-weight: bold;
  div {
    width: 100px;
  }
}
.pc_sto36 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_sto37 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeFontColor;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
}
.pc_sto38 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 5px;
  cursor: pointer;
}
.pc_sto39 {
  width: 93px;
  position: fixed;
  right: -9px;
  top: 153px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 152px);
  z-index: 1;
}
.pc_sto4 {
  height: 90%;
  overflow: scroll;
}
.pc_sto4 div {
  border-bottom: 1px solid #fff;
}
.pc_sto41 {
  height: 47px;
  line-height: 47px;
  border-radius: 5px;
  color: #fff;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
}
.pc_sto42 {
  width: 93px;
  position: fixed;
  right: -11px;
  top: 153px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 130px);
  z-index: 1;
}
.pc_sto43 {
  width: 370px;
  overflow: hidden;
}
.pc_sto44 {
  width: 370px;
  margin: 0 auto;
  margin-top: 60px;
  color: #969696;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
.pc_sto44 img {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  margin-top: 0px;
}
.pc_sto_search_input {
  width: 210px;height: 39px;outline: none;margin-left: 10px;
  font-size: 16px;border: none;padding: 0px 10px 0px 0px;
}
.pc_sto45{
  width: 20px;
  height: 20px;
  margin-top: 9px;
  cursor: pointer;
}
.pc_sto46 {
  float: right;width: 70px;height: 40px;border: solid 1px @themeBackGroundColor;border-radius: 20px;
  text-align: center;line-height: 38px;font-size: 16px;color: @themeBackGroundColor;margin-right: 15px;cursor: pointer;
}
.upgrade1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.upgrade11 {
  position: relative;
  width: 450px;
  min-height: 280px;
  background: #fff;
  border-radius: 4px;
  margin: 250px auto 0px;
  padding-bottom: 10px;
}

.upgrade12 {
  width: 93%;
  font-size: 24px;
  margin-left: 20px;
  line-height: 100px;
  text-align: center;
  font-weight: bold;
  color: @themeFontColor;
}
.upgrade13{
  display:-webkit-box;
  text-overflow:ellipsis;
  overflow:hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient:vertical;
}
/deep/.bg_highlight_dark {
  background-color:#fff;
  transition: 0.5s;
  animation:bgchange 3s linear 0s 1;
}
  @keyframes bgchange{
    0% {
      background-color: #dad8d8;
    }
    100% {
      background-color: #fff;
    }
  }
/deep/ .el-table .cell {
  padding-right: 2px;padding-left: 2px;
}
/deep/ .el-table .cell.el-tooltip {
  white-space: normal;
}
.pc_sto_add_goods {
  width: 95px;
  height: 42px;
  background: @themeBackGroundColor;
  border-radius: 8px;
  font-size: 18px;
  line-height: 42px;
  text-align: center;
  color: #FFFFFF;
  font-weight: bold;
  margin-left: 20px;
}
.pc_sto_cat {
  float: left;
  width: 110px;
  height: 40px;
  border-radius: 4px 0 0 4px;
  text-align: center;
  color: #FFF;
  line-height: 40px;
  font-weight: normal;
  font-size: 16px;
  /deep/.el-input__inner {
    border-radius: 4px 0 0 4px;
    border-right: 0px;
    background: #CFA26B;
    color: #FFFFFF;
    font-weight: 500;
    text-align: center;
  }
  /deep/ .el-input__icon {
    color: #FFFFFF;
  }
}
.pc_sto_center {
  float: left;
  width: 100%;
  font-size: 16px;
  color: @themeFontColor;
  .pc_sto_header_center {
    margin: 20px 25px 0 20px;
    height: 50px;
    display: flex;
    justify-content: space-between;
    .header_code {
      width: 15%;
    }
    .header_saleprice, .header_vipprice, .header_purchase_price, .header_stock, .header_date, .header_unit {
      text-align: center;
    }
    .header_saleprice, .header_vipprice {
      width: 10%;
    }
    .header_purchase_price, .header_stock {
      width: 12%;
    }
    .header_date {
      width: 16%;
    }
    .header_unit {
      width: 7%;
    }
    .header_total {
      width: 7%;
      text-align: right;
    }
  }
  .pc_sto_header_center_add {
    margin: 20px 0 0;
    justify-content:flex-start
  }
  .pc_sto_center_top {
    font-weight: 700;
    font-size: 16px;
    color: @themeFontColor;
    &_code, &_name, &_type, &_unit, &_price, &_vip {
      padding-left: 16px;
    }
    &_code {
      width: 140px;
    }
    &_name {
      width: 200px;
      span {
        color: red;
      }
    }
    &_type {
      width: 166px;
    }
    &_unit {
      width: 120px;
    }
    &_price {
      width: 127px;
      span {
        color: red;
      }
    }
    &_vip {
      width: 127px;
    }
  }
  .pc_sto_center_bottom {
    height: 40px;
    display: flex;
    justify-content: space-between;
    div {
      font-size: 16px;
      color: @themeFontColor;
      font-weight: bold;
      line-height: 40px;
    }
  }
  .pc_sto_data_sub {
    display: flex;
    justify-content:space-between;
    border-bottom: 1px dashed #E5E8EC;
    margin-bottom: 16px;
    padding-bottom:10px;
    line-height:39px;
    &_name, &_type, &_unit, &_price, &_vip {
      padding-left: 15px;
    }
    &_code {
      width: 140px;
      padding-left: 16px;
    }
    &_name {
      width: 200px;
      input {
        width:170px;
        text-align: left;
        height: 40px;
        line-height: 38px;
        padding: 0 8px;
        border: solid 1px #e3e6eb;
        border-radius: 5px;
      }
    }
    &_type {
      width: 166px;
      /deep/.el-input__inner {
        padding: 0 15px 0 8px
      }
    }
    &_unit {
      width: 120px;
      /deep/.el-input__inner {
        padding: 0 15px 0 8px
      }
    }
    &_price {
      width: 127px;
    }
    &_vip {
      width: 127px;
    }
  }
  // 进货商品列表
  .pc_sto_data_sub_list {
    display: flex;
    justify-content: space-between;
    .list_name {
      width: 15%;
      color: #B2C3CD;
    }
    .list_saleprice, .list_vipprice {
      width: 10%;
      color: #567485;
      text-align: center;
    }
    .edit_price {
      text-decoration: underline;
      cursor: pointer;
    }
    .list_purchase_price, .list_stock {
      width: 12%;
    }
    .dateEditor {
      width: 16%;
    }
    .list_unit {
      width: 7%;
      color: #567485;
      text-align: center;
    }
    .list_total {
      width: 7%;
      text-align: right;
    }
  }
  .printBtn {
    cursor: pointer;
    color: #b4995a;
  }
  .disPrintBtn {
    color: #B6BFE7;
    cursor: auto;
  }
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .header_title{
    color: @themeFontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.select_search_input {
  width: 90%;
  margin: 10px auto;
}
.pc_stock0 {
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .pc_stock1 {
    width: 240px;
    height: 144px;
    border: 1px solid #E3E6EB;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.pc_stock41 {
  padding: 30px;
  .pc_stock40 {
    color: @themeFontColor;
    font-size: 16px;
    .el-input {
      font-size: 18px;
    }
    .pc_stock39 {
      font-weight: 500;
      line-height: 22px;
      display: inline-block;
      margin-right: 64px;
    }
    .pc_stock42 {
      font-size: 18px;
      background: #FFFFFF;
      border: 1px solid #CFA26B;
      border-radius: 4px;
      text-align: center;
      font-weight: 500;
      line-height: 40px;
      width: 119px;
      height: 44px;
      display: inline-block;
      color: #CFA26B;
      margin-left: 120px;
      cursor: pointer;
    }
    .pc_stock43 {
      border: 1px solid #CFA26B;
      font-size: 18px;
      background: #CFA26B;
      border-radius: 4px;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      color: #FFFFFF;
      width: 119px;
      height: 44px;
      display: inline-block;
      margin-left: 18px;
      cursor: pointer;
    }
    .pc_stock44 {
      display: inline-block;
      .el-input__suffix {
        padding-right: 10px;
        padding-top: 7px;
        color: @themeFontColor;
      }
      .el-input__inner {
        padding-right: 35px;
      }
    }
    .pc_stock45 {
      display: inline-block;
      .el-input__prefix {
        display: none;
      }
    }
  }
}
.pc_stock_36 {
  width: 110px;
  position: fixed;
  right: -2px;
  top: 207px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 278px);
  z-index: 2;
  overflow-y: auto;
  overflow-x: hidden;
}
.parent_menu_active_div {
  background: @linearBackgroundColor !important;
  width: 110px;
  margin-left: -10px;
  padding-left: 10px;
  height: 56px;
  border-bottom: 1px solid #fff;
}
.pc_sto_img {
  width: 55px;
  height: 55px;
  font-size: 16px;
  font-weight: bold;
  line-height: 27px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 8px;
  overflow: hidden;
  letter-spacing: 6px;
  margin-right: 10px;
}
.pc_sto_img2 {
  width: 55px;
  height: 55px;
  font-size: 16px;
  font-weight: bold;
  line-height: 54px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
  margin-right: 10px;
  letter-spacing: 6px;
  text-indent: 8px;
}
.buyOrder {
  background: @themeBackGroundColor;
}
.buyOrder1 {
  background: #FF7875
}
#deleteOneGoods {
  color: @themeBackGroundColor;
}
#headerFont {
  color: @themeFontColor;
  height: 90px;
  border-bottom: 1px solid #e3e6eb;
  display: flex;
  justify-content: space-between;
}
#headerFont1 {
  color: @themeFontColor;
  height: 110px;
  border-bottom: 1px solid #e3e6eb;
  display: flex;
  justify-content: space-between;
}
.isBuyOrder {
  background: @stockBackGround;
}
.notBuyOrder {
  background: @unStockBackGround;
}
.editType {
  width: 104px;
  height: 50px;
  background-color: @inventoryColor;
  color: @areaColor;
  line-height:50px;
  font-weight: 700;
  position:fixed;
  top:157px;
  border-radius: 4px 4px 0 0;
}
.search {
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.prod_print_link {
  color: #CBAB63;
  cursor: pointer;
  text-decoration-line: underline;
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .header_title{
    color: @themeFontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.pc_stock {
  /deep/.el-dialog__header {
    display: none;
  }
  /deep/.el-dialog__body {
    padding: 0;
  }
}
.pc_stock
.pc_stock41 {
  padding: 30px;
  .pc_stock40 {
    color: @themeFontColor;
    font-size: 16px;
    .el-input {
      font-size: 18px;
    }
    .pc_stock39 {
      font-weight: 500;
      line-height: 22px;
      display: inline-block;
      margin-right: 64px;
    }
    .pc_stock42 {
      font-size: 18px;
      background: #FFFFFF;
      border: 1px solid #B4995A;
      border-radius: 4px;
      text-align: center;
      font-weight: 500;
      line-height: 40px;
      width: 119px;
      height: 44px;
      display: inline-block;
      color: #B4995A;
      margin-left: 120px;
      cursor: pointer;
    }
    .pc_stock43 {
      border: 1px solid #B4995A;
      font-size: 18px;
      background: #B4995A;
      border-radius: 4px;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      color: #FFFFFF;
      width: 119px;
      height: 44px;
      display: inline-block;
      margin-left: 18px;
      cursor: pointer;
    }
    .pc_stock44 {
      display: inline-block;
      .el-input__suffix {
        padding-right: 10px;
        padding-top: 7px;
        color: @themeFontColor;
      }
      .el-input__inner {
        padding-right: 35px;
      }
    }
    .pc_stock45 {
      display: inline-block;
      .el-input__prefix {
        display: none;
      }
    }
  }
}
.pc_sto47 {
  line-height: 50px;font-size: 16px;font-weight: bold;color: #567485;width: 750px;margin: 0 auto;overflow: hidden;
}
.pc_sto47 div {
  float: left;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pc_sto48 {
  width: 130px;height: 45px;text-align: center;line-height: 43px;font-size: 18px;color: #FFF;cursor: pointer;
  background: #BDA16A;border: 1px solid #BDA16A;border-radius: 6px;float: right;margin-right: 25px;
}
.pc_sto49 {
  width: 130px;height: 45px;text-align: center;line-height: 43px;font-size: 18px;color: #BDA16A;cursor: pointer;
  background: #FFF;border: 1px solid #BDA16A;border-radius: 6px;float: right;margin-right: 25px;
}
.pc_sto5 {
  position: relative;
  z-index: 800;
  height: 182px;
  font-size: 16px;
  margin-top: 0;
  margin-left: calc(100% - 927px);
  background: #fff;
  width: 710px;
  overflow: hidden;
  border-radius: 5px;
  border: 1px solid #B2C3CD;
}
.pc_sto5 div {
  margin-left: 20px;
  line-height: 16px;
  margin-top: 9px;
  color: #567485;
}
.pc_sto51 {
  margin-top: 125px;
  border-width: 0px 7px 5px 7px;
  border-color: transparent transparent #454A7B transparent;
  border-style:solid;
  height: 0;
  width: 0;
  margin-left: calc(100% - 642px);
}
.pc_sto52 {
  width: 750px;
  height: 96px;
  border: 1px solid #B2C3CD;
  background: #F9FBFB;
  color: #567485;
  border-radius: 8px;
  margin: 0 auto;
  margin-top: 15px;
}
.pc_sto52 div {
  line-height: 14px;margin-top: 15px;margin-left: 10px;
}
.manufactureDateClass1 {
  /deep/ & > .el-input__inner {
    background: #f5f8fb;
    width: 145px;
  }
  & > .el-input__suffix {
    right: 20px;
  }
}
.dateEditor {
  /deep/.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 145px;
  }
}
.pc_sto10 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 400;
}
.pc_sto101 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0,0,0,.5);;
}
.pc_sto102{
  position: relative;
  z-index: 800;
  height: 260px;
  margin: 0 auto;
  margin-top: 240px;
  background: #FFF;
  width: 450px;
  overflow: hidden;
  border-radius: 5px;
}
.pc_sto103{
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: #567485;
  margin-top: 40px;
}
.pc_sto104{
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: #567485;
  margin-top: 25px;
  font-weight: 100;
}
.tab_error_container {
  color: red;
  font-size: 12px;
  height: 24px;
  line-height: 34px;
}
.dialog-title__icon {
  margin-left: 10px;
  cursor: pointer;width: 16px;height: 16px;
  border: 2px solid @themeBackGroundColor;text-align: center;
  line-height: 12px;color: @themeBackGroundColor;
  font-size: 12px;font-weight: bold;
  border-radius: 50%;float: right;margin-top: 12px;margin-right: 10px;
}
.upgrade-dialg {
  display: flex;
  justify-content: center;
  align-items: center;
}
.upgrade-dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
}
.dialg-close {
  width: 150px;
  background: @areaColor;height: 54px;line-height: 54px;text-align: center;
  font-size: 18px;font-weight: bold;
  color: @butFontColor;border-radius: 6px;margin-top: 40px;cursor: pointer;
}
.dialg-submit {
  width: 150px;
  background: @themeBackGroundColor;margin-left: 24px;
  height: 54px;line-height: 54px;text-align: center;
  font-size: 18px;font-weight: bold;
  color: @butFontColor;border-radius: 6px;margin-top: 40px;cursor: pointer;
}
.upgrade-dialg__title {
  display: flex;
  justify-content: center;
  align-items: center;
  color: @themeBackGroundColor;
  font-size: 16px;
}
.upgrade-dialg__top {
  position: absolute;
  width: 100%;
  height: 100%;
  margin-left: 15px;
  z-index: 400;
  top: 40px;
  left: 495px;
}
.upgrade-dialg__buy {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 400;
  margin-left: 15px;
  top: 10px;
  left: 495px;
}
.upgrade-dialg__bottom {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10px;
  left: 480px;
  z-index: 400;
}
.title-marign {
  margin-left: 32px
}
.dialog-title__image {
  width: 20px;
  height: 20px;
  display: inline-block;
  margin-top: 10px;
  margin-right: 5px;
}
.dialog-title__name {
  margin-top: 10px;
}
.dialog-title__detail {
  position: relative;
  z-index: 800;
  height: 70px;
  padding: 12px 5px 0 12px;
  font-size: 16px;
  margin-top: 0;
  margin-left: calc(100% - 940px);
  background: @butFontColor;
  width: 420px;
  overflow: hidden;
  border-radius: 5px;
  border: 1px solid @text;
}
.detail-color {
  color: @areaFontColor;
}
.bottom-color {
  color: @warningRed !important;
}
.bottom-title {
  margin-top: 15px !important;
}
.bottom-title__color {
  color: @text !important;
}
</style>
<style lang="less" rel="stylesheet/less">
.el-cascader-panel .el-radio{
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}
.el-cascader-panel .el-radio__input{
  visibility: hidden;
}
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
.goodListWrap {
  padding-right: 15px;
}
</style>
<template>
  <div
    v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)"
    style="height: 100%;position: relative;overflow: hidden;width: 100%;background: #F5F8FB;padding: 12px !important;"
    class="pc_stock"
  >
    <v-AddGoods></v-AddGoods>
    <!-- 新版管理 -->
    <v-SelManage></v-SelManage>
    <!-- 重复商品选择 -->
    <v-RepeatGoodsChoose
      :repeatData="repeatData"
      :repeatCode="repeatCode"
      @repeatChooseEmit="repeatChoose">
    </v-RepeatGoodsChoose>
      <!-- 进货/退货价格平均值 -->
    <div
      v-show="showAverage"
      style="
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 400;
        background: rgba(0, 0, 0, 0.5);
      "
    >
      <div
        style="
          position: relative;
          z-index: 800;
          height: 580px;
          margin: 0 auto;
          margin-top: 60px;
          background: #fff;
          width: 800px;
          overflow: hidden;
          border-radius: 5px;
        "
      >
        <div
          style="
            overflow: hidden;
            width: calc(100% - 48px);
            height: 62px;
            font-size: 18px;
            color: #567485;
            margin: 0 auto;
            font-weight: bold;
            line-height: 18px;
            border-bottom: 1px solid #e3e6eb;
          "
        >
          <div style="float: left; margin-top: 27px">进货价加权平均</div>
          <div
            @click="showAverage = false"
            style="
              float: right;
              font-size: 24px;
              color: #567485;
              margin-top: 25px;
            "
          >
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="pc_sto52" v-show="buyOrder">
          <div style="margin-top: 12px;">公式：(原进价*现有库存+本次进价*本次进货数)/(现有库存+本次进货数)</div>
          <div style="margin-top: 18px;">例如：10元成本价进了10件商品，卖得剩下5件，花了15元成本价又进货10件，通过系统的进货价加权平均计算</div>
          <div style="margin-top: 10px;">(10*5+15*10)/(5+10)=13.33</div>
        </div>
        <div class="pc_sto47" v-show="buyOrder">
          <div style="width: 140px;text-align: left;">商品</div>
          <div style="width: 110px;">原进价</div>
          <div style="width: 110px;">现有库存</div>
          <div style="width: 110px;">本次进价</div>
          <div style="width: 110px;">本次进货数</div>
          <div style="width: 140px;margin-left: 30px;text-align: center;">平均后进价</div>
        </div>
        <div class="pc_sto52" v-show="!buyOrder">
          <div style="margin-top: 12px;">公式：(原进价*原库存-本次退货进价*本次退货数)/(原库存-本次退货数)</div>
          <div style="margin-top: 18px;">例如：店铺库存有10件商品，进货价为10元。本次需要退货5件，退货商品的进货价12元，通过系统的进货价加权平均</div>
          <div style="margin-top: 10px;">计算(10*10-12*5)/(10-5)=8元</div>
        </div>
        <div class="pc_sto47" v-show="!buyOrder">
          <div style="width: 140px;text-align: left;">商品</div>
          <div style="width: 110px;">原进价</div>
          <div style="width: 110px;">原库存</div>
          <div style="width: 110px;">本次退货进价</div>
          <div style="width: 110px;">本次退货数</div>
          <div style="width: 140px;margin-left: 30px;text-align: center;">平均后进价</div>
        </div>
        <div
          style="
            height: 260px;
            overflow-y: scroll;
            width: 756px;
            margin-left: 25px;
          "
        >
          <div class="pc_sto47" v-for="(sub, index) in sub_list" :key="sub.id">
            <div style="width: 140px;text-align: left;">{{sub.name}}</div>
            <div style="width: 110px;">{{sub.old_pur_price}}</div>
            <div style="width: 110px;">{{setMaxDecimal(sub.buy_number, 3)}}</div>
            <div style="width: 110px;">{{formatPurPriceReturn(sub.price)}}</div>
            <div style="width: 110px;">{{setMaxDecimal(sub.qty, 3)}}</div>
            <div style="width: 140px;margin-left: 30px;">
              <!-- <el-input
                v-model="sub.averagePrice"
                placeholder="请输入进价"
                class="pc_sto32"
                clearable
                @blur="checkAverage(index)"
                style="font-size: 20px;width: 140px;"
                :style="Number(sub.averagePrice) < 0 ? 'color: #FF6159 !important' : ''"
              ></el-input> -->
              <input type="text"
                v-model="sub.averagePrice"
                placeholder="请输入进价"
                class="pc_sto32"
                @input="sub.averagePrice = $pricePurPriceLimit(sub.averagePrice)"
                @blur="checkAverage(index)"
                style="font-size: 16px;width: 140px;height: 40px;line-height: 40px;border-radius: 5px;text-indent: 8px;margin-top: 5px;text-align: right;"
                :style="Number(sub.averagePrice) < 0 ? 'border: 1px solid #FF6159;' : 'border: 1px solid #567485;'">
            </div>
          </div>
        </div>
        <div style="overflow: hidden;margin-top: 30px;">
          <div @click="subOrder(isAverageN)" class="pc_sto48">保存</div>
          <div @click="averageCancel" class="pc_sto49">取消</div>
        </div>
      </div>
    </div>
    <!-- v-show="stockDelList" -->
    <div v-show="showTempConfirm" class="pc_sto10">
      <div class="pc_sto101" @click="cancelExit()"></div>
      <div class="pc_sto102">
        <div class="pc_sto103">提示</div>
        <div class="pc_sto104">是否继续上一次的进货单</div>
        <div class="pc_sto36">
          <div class="pc_sto37"  @click="showTempConfirm = false;">重新进货</div>
          <div class="pc_sto38" @click="continueOrder()">继续进货</div>
        </div>
      </div>
    </div>
    <!-- 结算/退货切换清空列表 -->
<!--     <div
      v-show="stockDelList"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="cancelExit()"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >单据未保存，确定要离开本页面？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="cancelExit()">取消</div>
          <div class="pc_sto38" @click="continueExit()">确定</div>
        </div>
      </div>
    </div> -->
    <!-- 前往供应商管理前确认要清空列表 -->
<!--     <div
      v-show="stockDelList2"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="cancelToSupplier()"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >单据未保存，确定要离开本页面？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="cancelToSupplier()">取消</div>
          <div class="pc_sto38" @click="continueToSupplier()">确定</div>
        </div>
      </div>
    </div> -->
    <!-- 返回Home页前确认要清空列表 -->
<!--     <div
      v-show="stockDelList3"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="cancelToHome()"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >单据未保存，确定要离开本页面？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="cancelToHome()">取消</div>
          <div class="pc_sto38" @click="continueToHome()">确定</div>
        </div>
      </div>
    </div> -->
    <!-- 切换明细前确认要清空列表 -->
    <div v-show="show_if_add_goods" class="pc_sto10">
      <div class="pc_sto101" @click="show_if_add_goods = false"></div>
      <div class="pc_sto102">
        <div class="pc_sto103">提示</div>
        <div
          class="pc_sto104"
        >系统暂无此商品，是否新增？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="show_if_add_goods = false">取消</div>
          <div class="pc_sto38" @click="show_if_add_goods = false;continueAddGoods()">新增</div>
        </div>
      </div>
    </div>
    <!-- 编辑售价弹窗 -->
    <div v-show="showEditSalePrice" class="pc_sto10">
      <div class="pc_sto101"
        @click="showEditSalePrice = false"
      ></div>
      <div style="position: relative;z-index: 800;height: 335px;margin: 0 auto;margin-top: 200px;
        background: #fff;width: 550px;overflow: hidden;border-radius: 5px;">
        <div style="overflow: hidden;width: calc(100% - 48px);height: 62px;font-size: 18px;color: #567485;
          margin: 0 auto;font-weight: bold;line-height: 18px;border-bottom: 1px solid #e3e6eb;">
          <div style="float: left; margin-top: 27px">修改售价</div>
          <div @click="showEditSalePrice = false" style="float: right;font-size: 24px;color: #567485;margin-top: 25px;cursor:pointer;">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div style="overflow: hidden; margin-top: 40px">
          <div style="float: left;margin-left: 64px;line-height: 42px;width: 105px;color: #b2c3cd;font-size: 18px;font-weight: bold;">
            <span style="color: #ff6159">*</span>零售价
          </div>
          <div style="float: left">
            <input ref="salePriceInput" type="text" v-model.trim="inputSalePrice" v-focus-select="'focusSelect'" @blur="formatpriceReturn('inputSalePrice')" style="width: 317px;text-indent: 15px;background: #fff;border: solid 1px #e3e6eb;height: 42px;
              line-height: 40px;border-radius: 5px;color: #567485;font-size: 18px;" @input="inputSalePrice = $priceLimit(inputSalePrice)"/>
          </div>
        </div>
        <div style="overflow: hidden; margin-top: 16px">
          <div style="float: left;margin-left: 64px;line-height: 42px;width: 105px;color: #b2c3cd;font-size: 18px;font-weight: bold;">
            会员价
          </div>
          <div style="float: left">
            <input ref="vipPriceInput" type="text" v-model.trim="inputMemberPrice" v-focus-select="'focusSelect'" style="width: 317px;text-indent: 15px;background: #fff;border: solid 1px #e3e6eb;
                height: 42px;line-height: 40px;border-radius: 5px;color: #567485;font-size: 18px;"
              @input="inputMemberPrice = $priceLimit(inputMemberPrice)"
              @blur="formatpriceReturn('inputMemberPrice')"
            />
          </div>
        </div>
        <div style="width: 420px;background: #bda16a;margin-left: 64px;height: 54px;line-height: 54px;text-align: center;
            font-size: 18px;font-weight: bold;color: #fff;border-radius: 6px;margin-top: 40px;cursor: pointer;"
          @click="saveSalePrice()"
        >确定</div>
      </div>
    </div>
    <!-- 编辑金额弹窗 -->
    <div
      v-if="showEditPrice"
      class="pc_sto10"
    >
      <div class="pc_sto101"
        @click="showEditPrice = false"
      ></div>
      <div style="position: relative;z-index: 800;height: 535px;margin: 0 auto;margin-top: 150px;background: #FFF;
        width: 710px;overflow: hidden;border-radius: 5px;">
        <div style=" overflow: hidden; width: calc(100% - 48px);height: 62px;font-size: 18px;color: #567485;
          margin: 0 auto;font-weight: bold;line-height: 18px;border-bottom: 1px solid #e3e6eb;">
          <div style="float: left; margin-top: 27px">修改总额</div>
          <div @click="showEditPrice = false" class="submit-close">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div style="overflow: hidden; width: calc(100% - 48px);background: #F5F8FB;height: 125px; font-size: 18px; color: rgb(86, 116, 133);
          margin: 0px auto; font-weight: bold;margin-top: 16px;">
          <div style="overflow: hidden;margin-left: 8px;margin-top: 10px;">
            <div style="float: left;line-height: 42px;width: 105px;color: #567485;font-size: 18px;font-weight: bold;">总额</div>
            <div style="float: left;">
              <input
                id="inputPrice"
                type="text"
                @focus="inputPriceFocus($event)"
                @input="onInputTotalPriceChange"
                v-model="inputPrice"
                v-focus-select="'autoFocus,focusSelect'"
                @blur="formatpriceReturn('inputPrice')"
                style="width: 490px;text-indent: 15px;background: #FFF;border: solid 1px #e3e6eb;height: 42px;line-height: 40px;
                  border-radius: 5px;color: #567485;font-size: 18px;"
              />
            </div>
          </div>
          <div style="overflow: hidden;margin-left: 8px;margin-top: 16px;">
            <div style="float: left;line-height: 42px;width: 105px;color: #567485;font-size: 18px;font-weight: bold;">数量</div>
            <div style="float: left;">
              <input
                id="inputBuyNumber"
                type="text"
                v-model="inputBuyNumber"
                v-focus-select="'focusSelect'"
                style="width: 490px;text-indent: 15px;background: #FFF;border: solid 1px #e3e6eb;height: 42px;line-height: 40px;border-radius: 5px;color: #567485;font-size: 18px;"
                @input="onInputNumberChange"
              />
            </div>
          </div>
          <!-- <div style="color: #B2C3CD;font-size: 14px;margin-left: 65px;margin-top: 20px;"><i class="el-icon-warning"></i>&nbsp;计算得出的进价会保留到分</div> -->
          <!-- <div style="overflow: hidden;margin-left: 8px;margin-top: 16px;">
            <div style="float: left;width: 105px;color: #567485;font-size: 18px;font-weight: bold;">
              <div>尾数处理</div>
              <div style="font-size: 12px;font-weight: normal;color: #B2C3CD;">进价保留到分</div>
            </div>
            <div style="overflow: hidden;float: left;margin-top: 13px;font-weight: normal;">
              <div @click="chooseEditPrice = 0;countPrice();" style="overflow: hidden;float: left;cursor: pointer;">
                <div style="width: 19px;height: 19px;border: 2px solid #BDA16A;border-radius: 50%;float: left;" v-show="chooseEditPrice === 0">
                  <div style="width: 11px;height: 11px;margin-top: 2px;margin-left: 2px;background: #BDA16A;border-radius: 50%;"></div>
                </div>
                <div style="width: 19px;height: 19px;border: 2px solid #B2C3CD;border-radius: 50%;float: left;" v-show="chooseEditPrice !== 0"></div>
                <div style="float: left;margin-left: 10px;color: #567485;font-size: 16px;line-height: 19px;">尾数进位到分</div>
              </div>
              <div @click="chooseEditPrice = 2;countPrice();" style="overflow: hidden;float: left;margin-left: 54px;cursor: pointer;">
                <div style="width: 19px;height: 19px;border: 2px solid #BDA16A;border-radius: 50%;float: left;" v-show="chooseEditPrice === 2">
                  <div style="width: 11px;height: 11px;margin-top: 2px;margin-left: 2px;background: #BDA16A;border-radius: 50%;"></div>
                </div>
                <div style="width: 19px;height: 19px;border: 2px solid #B2C3CD;border-radius: 50%;float: left;" v-show="chooseEditPrice !== 2"></div>
                <div style="float: left;margin-left: 10px;color: #567485;font-size: 16px;line-height: 19px;">四舍五入到分</div>
              </div>
              <div @click="chooseEditPrice = 1;countPrice();" style="overflow: hidden;float: left;margin-left: 54px;cursor: pointer;">
                <div style="width: 19px;height: 19px;border: 2px solid #BDA16A;border-radius: 50%;float: left;" v-show="chooseEditPrice === 1">
                  <div style="width: 11px;height: 11px;margin-top: 2px;margin-left: 2px;background: #BDA16A;border-radius: 50%;"></div>
                </div>
                <div style="width: 19px;height: 19px;border: 2px solid #B2C3CD;border-radius: 50%;float: left;" v-show="chooseEditPrice !== 1"></div>
                <div style="float: left;margin-left: 10px;color: #567485;font-size: 16px;line-height: 19px;">尾数舍弃</div>
              </div>
            </div>
          </div> -->
        </div>
        <div style="overflow: hidden; width: calc(100% - 48px);background: #F4EFE5;height: 165px; font-size: 14px; color: rgb(86, 116, 133); margin: 0px auto; font-weight: normal;margin-top: 16px;">
          <div style="margin-top: 14px;margin-left: 8px;">1.单个商品的进价会重新计算并保留6位小数（尾数四舍五入）；</div>
          <div style="margin-left: 8px;">2.进货金额会重新计算并保留2位小数（尾数四舍五入）；</div>
          <div style="margin-left: 8px;">3.若整单进货金额有差，可以修改整单的【折后应付】</div>
          <div style="height: 5px;"></div>
          <div style="overflow: hidden;margin-top: 8px;color: #BDA16A;font-size: 15px;line-height: 15px;margin-left: 8px;">
            <div style="float: left;">进价：</div><div style="font-weight: bold;float: left;width: 90px;text-align: right;">{{alertPurPrice}}</div>
          </div>
          <div style="overflow: hidden;margin-top: 8px;color: #BDA16A;font-size: 15px;line-height: 15px;margin-left: 8px;">
            <div style="float: left;">数量：</div><div style="font-weight: bold;float: left;width: 90px;text-align: right;">{{alertBuyNumber}}</div>
          </div>
          <div style="overflow: hidden;margin-top: 8px;color: #BDA16A;font-size: 15px;line-height: 15px;margin-left: 8px;">
            <div style="float: left;">总额：</div><div style="font-weight: bold;float: left;width: 90px;text-align: right;">{{alertPrice}}</div>
            <div style="float: left;margin-left: 30px;">（总额=进价×数量，点击确定后数值会返回进货单内显示）</div>
          </div>
        </div>
        <div class="submit-sure" @click="savePrice()">确定</div>
      </div>
    </div>
    <!--本页面清空按钮，清空前进行确认-->
    <div  v-show="sure_clean_list"  class="pc_sto10">
      <div  class="pc_sto101"  @click="sure_clean_list = false"></div>
      <div  class="pc_sto102">
        <div  class="pc_sto103">提示</div>
        <div  class="pc_sto104">确定清空商品列表?</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="sure_clean_list = false">取消</div>
          <div
            class="pc_sto38"
            @click="allEmpty"
          >确定</div>
        </div>
      </div>
    </div>

     <!-- 点击批量进货，清空前进行确认-->
     <div  v-show="sure_quantity"  class="pc_sto10">
      <div  class="pc_sto101"  @click="sure_quantity = false"></div>
      <div  class="pc_sto102">
        <div  class="pc_sto103">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 20px;color: #567485;margin-top: 25px;font-weight: 100;"
        ><span style=" display: inline-block; width: 250px;">批量进货将会清空商品列表,确定继续吗?</span></div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="sure_quantity = false">取消</div>
          <div
            class="pc_sto38"
            @click="leftList = [];originalLeftList = [];sure_quantity = false;pursDisc = 100;pursRemark = '';countTotal();batchImport()"
          >确定</div>
        </div>
      </div>
    </div>
    <div
      class='pc_sto1'
      v-show='show_import'
    >
      <div class='pc_sto11'>
        <div class='pc_sto12'>
          批量进货<span class='pc_sto13' @click="show_import = false;upload_complete = false;">×</span>
        </div>
        <div class="pc_sto14">
          <div style="margin: 20px 0px;">
            <el-popover
              popper-class="pc_pay192 popper_self"
              placement="top-start"
              trigger="click">
              <div style="text-align:left;">
                <div style="font-family:Noto Sans SC, sans-serif;">
                  <span>1.商品条数过多时建议分批导入,每次建议不超过200条。</span><br/>
                  <span>2.商品数据复制到导入模板文件时请右键>选择性粘贴>粘贴为数值,避免格式问题引发错误。</span><br/>
                  <span>3.数字列以及条码列中请勿包含全角数字、全角小数点或空格。</span><br/>
                  <span>4.商品条码可以包含数字、字母以及-。</span><br/>
                </div>
              </div>
              <span slot="reference" style="color: #ff8484; cursor: pointer; font-size: 16px;text-decoration: underline;">
                <i class="el-icon-question" style="font-size: 18px;"></i>如何避免导入失败?</span>
            </el-popover>
          </div>
        </div>
        <div class="pc_sto15">
          <span id="#createTime" >如果您还没下载导入模板, </span>
            <a href="./excels/purchase_template.xlsx" download="批量进货导入模板.xlsx">
              <span>
              请点击下载批量进货导入模板</span>
            </a>
        </div>
        <div class="pc_sto17">
          <div
            @click="batchClick()"
            class="pc_sto18"
          >
            <div style="width: 17px;height: 20px;float: left;margin-left: 114px;margin-top: 8px;">
              <img src="../../image/pc_member_upload.png" style="width: 17px; height: 20px"/>
            </div>
            <div style="margin-left: 8px;line-height: 42px;color: #fff;float: left;">
              上传文件
            </div>
          </div>
          <span class="pc_sto19">仅支持导入.xls和.xlsx的文件格式</span>
        </div>
          <!-- 隐藏上传input -->
          <input style="display: none;" class="input-file" type="file" @change="batchUpload"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <!-- 上传中... -->
        <div v-show="upload_loading" style="width: 93%;height: 140px;margin: 20px auto;text-align: center;
            background-color: rgba(249, 251, 251, 100);border: 1px solid rgba(227, 230, 235, 100);">
          <div style="margin-top: 50px;">
            <div style="width: 25px; height: 25px; display: inline;">
              <img alt="" src="../../image/pc_member_loading.png" style="width: 25px; height: 25px" />
            </div>
            <div style="line-height: 16px;font-weight: bold;display: inline;font-size: 16px;margin-left: 4px;">
              正在导入，请稍后...
            </div>
          </div>
        </div>
        <!-- 上传完成 -->
        <div v-show="upload_complete" style="width: 93%;margin: 20px auto;text-align: left;background-color: rgba(249, 251, 251, 100);
            border: 1px solid rgba(227, 230, 235, 100);padding-bottom: 20px">
          <div v-show="Number(upload_data.wrong) === 0" style="margin-top: 12px;margin-left: 30px">
            共{{ upload_data.total }}条数据，成功导入{{upload_data.total}}条数据
          </div>
          <div v-show="Number(upload_data.wrong) !== 0" style="margin-top: 10px;margin-left: 10px">
            发现{{ upload_data.wrong }}条错误数据，导致所有数据上传失败
            <span v-show="upload_data.errDown">，具体请</span>
            <span v-show="upload_data.errDown" style="color: #d5aa76; cursor: pointer" class="downloadfaildata"
              @click="downloadfaildata">查看错误提示</span>
            <span v-show="upload_data.errDown">，修正后再次上传。</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 导入全部成功，提示弹窗-->
    <div  v-show="sure_import"  class="pc_sto10">
      <div  class="pc_sto101"  @click="sure_import = false"></div>
      <div  class="pc_sto102" style="height: 360px;">
        <div  class="pc_sto103" style="margin-top: 25px;">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 16px;color: #567485;margin-top: 25px;font-weight: 100;"
        >
          <span style=" display: inline-block; width: 325px;font-size: 24px;">
            检测到导入模板中有一品多码商品,进货数量将自动加合（在系统中以主条码显示）,确定导入吗?
          </span>
        </div>
        <div class="pc_sto36" style="margin-top: 40px;">
          <div class="pc_sto37" @click="sure_import = false;upload_complete = false;upload_loading = false;">取消</div>
          <div
            class="pc_sto38"
            @click="ypdmInput()"
          >导入</div>
        </div>
      </div>
    </div>
    <!--导入全部（仅商品缺失）-->
    <div
      class='pc_sto1'
      v-show='show_open'
    >
      <div class='pc_sto11 new_good_add_wrap'>
        <div class='pc_sto12'>
          以下商品缺失,请先新增<span class='pc_sto13' @click="show_open = false;">×</span>
        </div>
        <div class="pc_sto_center add_content" style="">
        <div class="pc_sto_header_center pc_sto_header_center_add">
          <div class="pc_sto_center_top pc_sto_center_top_code">条码</div>
          <div class="pc_sto_center_top pc_sto_center_top_name">
            <span>*</span>商品名称
          </div>
          <div class="pc_sto_center_top pc_sto_center_top_type">分类</div>
          <div class="pc_sto_center_top pc_sto_center_top_unit">单位</div>
          <div class="pc_sto_center_top pc_sto_center_top_price">
            <span>*</span>零售价
          </div>
          <div class="pc_sto_center_top pc_sto_center_top_vip">会员价</div>
        </div>
        <div id="mainTab" class="add_good_table">
          <div :class="da.isHighlight ? 'bg_highlight_dark' : ''" :id="da.code" :key="index"
            v-for="(da, index) in missingItem">
            <div class="pc_sto_data_sub">
              <div class="pc_sto_data_sub_code">
                {{da.code ? da.code : '-'}}
              </div>
              <div v-if="da.errorMsg && da.errorMsg.code" class="tab_error_container">
                {{ da.errorMsg.code }}
              </div>
              <div class="pc_sto_data_sub_name">
                <input
                  :id="'daName' + index"
                  type="text"
                  @focus="selectText('daName' + index)"
                  v-model.trim="da.name"
                  @input="da.name = da.name.replace(/[$']/g, '')"
                />
                <div
                  v-if="da.errorMsg && da.errorMsg.name"
                  class="tab_error_container">
                  {{ da.errorMsg.name }}
                </div>
              </div>
              <div class="pc_sto_data_sub_type">
                <el-cascader
                  v-model="da.typeFingerprintArr"
                  :options="typeOption"
                  :props="{ checkStrictly: true }"
                  style="width: 136px;"
                  placeholder="请选择">
                </el-cascader>
                <div
                  v-if="da.errorMsg && da.errorMsg.typeFingerprint"
                  class="tab_error_container">
                  {{ da.errorMsg.typeFingerprint }}
                </div>
              </div>
              <div class="pc_sto_data_sub_unit">
                <el-select
                  v-model="da.unitFingerprint"
                  placeholder="请选择"
                  style="width:90px;"
                >
                  <el-option
                    v-for="unit in unitOption"
                    :key="unit.fingerprint"
                    :label="unit.name"
                    :value="unit.fingerprint"
                  >
                  </el-option>
                </el-select>
                <div v-if="da.errorMsg && da.errorMsg.unitFingerprint" class="tab_error_container">
                  {{ da.errorMsg.unitFingerprint }}
                </div>
              </div>
              <div class="pc_sto_data_sub_price">
                <cj-input v-model="da.salePrice"
                  type="price"
                  focus-select
                  font-size="14"
                  width="95"
                  height="40"></cj-input>
                <div v-if="da.errorMsg && da.errorMsg.salePrice" class="tab_error_container">
                  {{ da.errorMsg.salePrice }}
                </div>
              </div>
              <div class="pc_sto_data_sub_vip">
                <cj-input v-model="da.vipPrice"
                  type="price"
                  focus-select
                  font-size="14"
                  width="95"
                  height="40"></cj-input>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="pc_sto38" style="width: 120px;height: 44px;margin-top: 26px;margin-right: 16px; border-radius:4px;text-align: center;line-height: 44px;font-size: 16px; float:right;"
      @click="branchInsert()">
        批量新增</div>
      <div class="pc_sto46" style="width: 120px;height: 44px;margin-top: 26px;margin-right: 10px; border-radius:4px;" @click="show_open = false;">
        取消</div>
        </div>
        </div>
    <!-- 新增商品提示-->
    <div v-show="sure_addGoods" class="pc_sto10">
      <div class="pc_sto101" @click="sure_addGoods = false"></div>
      <div class="pc_sto102">
        <div class="pc_sto103">提示</div>
        <div
          class="pc_sto104"
        >确定批量新增列表中的商品吗？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="sure_addGoods = false">取消</div>
          <div class="pc_sto38" @click="sure_addGoods = false; batchAddGoods()">新增</div>
        </div>
      </div>
    </div>
        <!--本页面清空按钮，清空前进行确认-->
        <div v-show="show_success"  class="pc_sto10">
      <div class="pc_sto101"  @click="show_success = false"></div>
      <div class="pc_sto102">
        <div class="pc_sto103">提示</div>
        <div class="pc_sto104">商品已新增成功，请重新上传文件</div>
        <div class="pc_sto36">
          <div
            class="pc_sto38"
            style="margin-left: 160px;"
            @click="show_success  = false;batchImport();handleGoodList()"
          >确定</div>
        </div>
      </div>
    </div>
    <!-- 左侧部分 -->
    <div style="width: calc(100% - 414px);float: left;position: relative;height: calc(100% - 50px);border-radius: 5px;
        border: 1px solid #e3e6eb;overflow: hidden;background: #FFF;">
      <div :id="screenWidth < 1024 ? 'headerFont1' : 'headerFont'"
        :class="buyOrder ? 'isBuyOrder' : 'notBuyOrder'"
        >
        <div class="pc_sto24">
          <div style="line-height: 15px;margin-top: 24px;">
            <span v-show="buyOrder">进货单号：{{pursCode}}</span>
          </div>
          <div style="margin-top: 13px;line-height: 15px;">
            <span v-show="buyOrder">进货日期：{{create_time.split(" ")[0]}}</span>
            <span v-show="!buyOrder" style="font-size: 16px;line-height: 38px;">退货日期：{{create_time.split(" ")[0]}}</span>
          </div>
        </div>

        <div class="pc_sto25_btn">
          <div>
            <div
            style="float: right;width: 70px;height: 40px;border-radius: 20px;text-align: center;line-height: 40px;font-size: 16px;color: #FFF;margin-right: 5px;cursor: pointer;"
            :class="buyOrder ? 'buyOrder' : 'buyOrder1'"
             @click="handleSubmit"
          >提交</div>
          <div
            class="pc_sto46"
            v-show="leftList.length > 0 && buyOrder"
            @click="sure_clean_list = true"
          >清空</div>
          <div
            v-show="buyOrder"
            class="pc_sto46"
            style="width: 100px;"
            @click="leftList.length > 0 && buyOrder ? sure_quantity = true : batchImport()"
          >批量进货</div>
          <div
            class="pc_sto46"
            style="width: 120px;color: #FF7875;border: solid 1px #FF7875;"
            v-show="leftList.length > 0 && !buyOrder"
            @click="isAverage && buyOrder ? toShowAverage(1) : handleSubmit(1)"
          >提交并新增</div>
          </div>
        </div>
      </div>
      <div class="pc_sto_center" :style="'height:' + table_height + 'px;'">
        <div class="pc_sto_header_center">
          <div class="pc_sto_center_top header_code">品名/条码</div>
          <div class="pc_sto_center_top header_saleprice">零售价</div>
          <div class="pc_sto_center_top header_vipprice">会员价</div>
          <div v-if="!$employeeAuth('purchase_price')"
            class="pc_sto_center_top header_purchase_price">
            进价
          </div>
          <div class="pc_sto_center_top header_stock">数量</div>
          <div class="pc_sto_center_top header_date" v-show="buyOrder">生产日期</div>
          <div class="pc_sto_center_top header_unit">单位</div>
          <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top header_total">总额</div>
          <div class="pc_sto_center_top" v-show="!buyOrder" style="width: 20%;text-align: right;">供应商</div>
        </div>
        <div id="mainTab" style="margin: 0 10px 0 20px;overflow: auto;" :style="'height:' + (table_height - 110) + 'px;'">
          <div class="goodListWrap" :class="da.isHighlight ? 'bg_highlight_dark' : ''"
            :id="da.fingerprint" :key="index" v-for="(da, index) in leftList">
            <div style="margin-bottom: 12px;font-size: 16px;display: flex;justify-content:space-between;">
              <div style="font-weight: bold;color: #567485;width: 59%;">{{da.name}}</div>
              <div style="width: 14%;text-align: right;" v-show="!buyOrder">&nbsp;</div>
              <div style="width: 36%;display:flex;justify-content: space-between;">
                <div class="printBtn" :class="{disPrintBtn: !da.code}" @click="print(da, 'barcode')">打印条码</div>
                <div class="printBtn" :class="{disPrintBtn: !da.code}" @click = "print(da, 'tip')">打印标价签</div>
                <div id="deleteOneGoods" style="width: 32px;cursor: pointer;" @click="deleteOneGoods(index)">删除</div>
              </div>
            </div>
            <div class="pc_sto_data_sub pc_sto_data_sub_list">
              <div class="list_name">
                {{ (da.goodsCode || da.code) ?  (da.goodsCode || da.code) : '-'}}
              </div>
              <div class="list_saleprice" :class="{edit_price: $employeeAuth('import_products')}"
                @click="editSalePrice(da, index, true)">
                {{$toDecimalFormat(da.salePrice, 2, true)}}
              </div>
              <div class="list_vipprice" :class="{edit_price: $employeeAuth('import_products')}"
                @click="editSalePrice(da, index, false)">{{ $toDecimalFormat(da.vipPrice, 2, true) }}</div>
              <div class="list_purchase_price" v-if="!$employeeAuth('purchase_price')">
                <input
                  :id="'m' + index"
                  @focus="selectText('m' + index)"
                  type="text"
                  @blur="formatLeftList()"
                  v-model="da.purPrice"
                  style="text-align: center;width: 100%;background: #f5f8fb;border: solid 1px #e3e6eb;height: 40px;line-height: 38px;border-radius: 5px;"
                  @input="da.purPrice = $pricePurPriceLimit(da.purPrice)"
                />
              </div>
              <div class="list_stock">
                <input
                  :id="'n' + index"
                  @focus="selectText('n' + index)"
                  type="text"
                  @blur="formatLeftList()"
                  v-model="da.curStock"
                  style="width: 100%;text-align: center;height: 40px;line-height: 38px;background: #f5f8fb;border: solid 1px #e3e6eb;border-radius: 5px;"
                  @input="da.curStock = onCurStockChange(da.curStock,da.unitName)"
                />
              </div>
              <div v-show="buyOrder" class="dateEditor">
                <el-date-picker
                  type="date"
                  class="manufactureDateClass1"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="生产日期"
                  v-model="da.manufactureDate"
                >
                </el-date-picker>
              </div>
              <div class="list_unit">{{da.unitName ? da.unitName : '-'}}</div>
              <div class="list_total" v-show="!$employeeAuth('purchase_price')" :id="'mn' + index"
                :class="{edit_price: $employeeAuth('import_products')}"
                @click="editPrice(da, index)">
                {{ da.total_money }}
              </div>
              <div class="list_maker" v-if="!buyOrder" style="display: inline-block;width: 18%;text-align: right;color: #B2C3CD;">
                <el-select v-model="da.supplierFingerprint" placeholder="请选择" class="sto1">
                  <el-option
                    style="font-size: 16px;"
                    v-for="mk in makerlist"
                    :key="mk.value"
                    :label="mk.label"
                    v-model="mk.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
          </div>
        </div>
        <div style="padding: 0px 20px 0 20px;background: #F5F7FA;">
          <div class="pc_sto_center_bottom">
            <div class="pc_sto_center_top" style="width: 30%;">合计：</div>
            <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top" :style="buyOrder ? 'width: 20%;' : 'width: 14%;'"></div>
            <div class="pc_sto_center_top" :style="buyOrder ? 'width: 20%;' : 'width: 14%;'" style="text-align: center;">{{$toDecimalFormat(this.total_number, 3)}}</div>
            <div class="pc_sto_center_top" style="width: 8%;"></div>
            <div v-show="!$employeeAuth('purchase_price')" class="pc_sto_center_top" style="width: 10%;text-align: right;padding-right:9px;">
              {{$toDecimalFormat(this.total_money, 2)}}
            </div>
            <div v-show="!buyOrder" style="width: 12%;"></div>
          </div>
        </div>
      </div>
      <div class="pc_sto25">
        <div class="pc_sto29">
          <div class="pc_sto3" v-if="buyOrder">
            <div class="pc_sto31">供应商：</div>
            <el-select
              v-model="companiesId"
              placeholder="请选择"
              class="pc_sto32"
              popper-class="pc_fixed_dropdown"
              @visible-change="clearDrop($event)"
              @change="flagChange()"
              >
              <div class="select_search_input">
                <el-input
                  ref="dropDownInput"
                  v-model="selSearchValue"
                  size="small"
                  placeholder="搜索供应商"
                  prefix-icon="el-icon-search"
                  @input="dropDownSearch"
                  clearable></el-input>
              </div>
              <div slot="empty" class="select_search_input">
                <el-input
                  ref="dropDownInput_empty"
                  v-model="selSearchValue"
                  size="small"
                  placeholder="搜索供应商"
                  prefix-icon="el-icon-search"
                  @input="dropDownSearch"
                  clearable></el-input>
                  <div style="text-align:center;margin: 20px auto;font-size: 14px;
                    color: #567485;height: 213px;line-height: 213px;">
                    无搜索内容
                  </div>
              </div>
              <el-option
                style="font-size: 16px;"
                v-for="mk in makerlistShow"
                :key="mk.value"
                :label="mk.label"
                v-model="mk.value"
              ></el-option>
            </el-select>
          </div>
          <div class="pc_sto3">
            <div class="pc_sto31">账户：</div>
            <el-select v-model="acctsId" placeholder="请选择" class="pc_sto32">
              <el-option
                style="font-size: 16px;"
                v-for="ac in account_list"
                :key="ac.value"
                :label="ac.label"
                :value="ac.value"
              ></el-option>
            </el-select>
          </div>
          <div class="pc_sto3" :style="buyOrder ? '':'width: 66%'">
            <div class="pc_sto31">说明：</div>
            <el-input
              id="sbt1"
              @focus="selectText('sbt1')"
              v-model="pursRemark"
              placeholder="请输入内容"
              class="pc_sto32"
              style="font-size: 16px;"
              maxlength="50"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="pursRemark = pursRemark.replace(/[$']/g, '')"
            ></el-input>
          </div>
        </div>
        <div class="pc_sto29">
          <div class="pc_sto3">
            <div class="pc_sto31">折扣率：</div>
            <el-input
              id="sbt2"
              @focus="selectText('sbt2')"
              v-model="pursDisc"
              placeholder="请输入内容"
              class="pc_sto32"
              @input="pursDisc = $discountRateLimit(pursDisc)"
              @blur="changePursDisc()"
              style="font-size: 16px;"
            ></el-input>
            <div style="float: left;width: 18px;line-height: 40px;font-style:oblique;margin-left: -30px;position: relative;z-index: 1;color: #B1C3CD;">%</div>
          </div>
          <div class="pc_sto3" v-show="!$employeeAuth('purchase_price')">
            <div class="pc_sto31">折后应付：</div>
            <el-input
              id="sbt3"
              @focus="selectText('sbt3')"
              v-model="pursDisc_amt"
              placeholder="请输入内容"
              class="pc_sto32"
              maxlength="10"
              @blur="changePursDiscAmt()"
              style="font-size: 16px;"
              @input="pursDisc_amt = $priceLimit(pursDisc_amt)"
            ></el-input>
          </div>
          <div class="pc_sto3" v-show="!$employeeAuth('purchase_price')">
            <div class="pc_sto31">本单实付：</div>
            <el-input
              v-model="pursPay_amt"
              placeholder="请输入内容"
              class="pc_sto32"
              :disabled="true"
              style="font-size: 20px;"
            ></el-input>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 400px;float: left;height: calc(100% - 50px);margin-left: 12px;border-radius: 4px 0 0 4px;
      padding:18px 0 20px 18px;background-color: white;border: 1px solid #E3E6EB">
      <div style="overflow: hidden;display: flex;">
        <div class="search"
          style="width: 260px;height: 42px;background-color: #ffffff;border: solid 1px #e3e6eb;border-radius: 4px;display: flex;"
        >
          <input
            id="search_keyword"
            type="text"
            placeholder="商品名称/条码/首字母/扫码"
            v-model="keyword"
            class="pc_sto_search_input"
            style=""
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="keyword = keyword.replace(/[$']/g, '')"
            @keydown.enter="inputSelectHandler('search_keyword')"
          />
          <img alt="" v-show="keyword != ''"
            @click="inputFocus('search_keyword')"
            src="../../image/pc_clear_input.png"
            class="pc_sto45" />
        </div>
        <div @click="addGoods()" :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
        class="pc_sto_add_goods">新增商品</div>
      </div>
      <div id="stock_right_list_id" class="scroll-bar" style="height: calc(100% - 54px);overflow: scroll;margin-top: 10px;">
        <div class="pc_sto34" v-for="(td,index) in right_list" v-bind:key="index" @click="pushLeft(td)">
          <img alt="" v-if="td.url !== '' && td.url !== null" v-lazy="$getSrcUrl(td.url)" onerror="javascript:this.src='../../image/pc_no_cloth_img.png';this.onerror=null;"/>
          <img alt="" v-if="(td.url === '' || td.url === null) && td.image !== '' && td.image !== null" v-lazy="$getSrcUrl(td.image)" onerror="javascript:this.src='../../image/pc_no_cloth_img.png';this.onerror=null;"/>
          <img v-if="(td.url === '' || td.url === null) && (td.image === '' || td.image === null) && (td.pinyin === '' || td.pinyin === null)" src="../../image/pc_no_cloth_img.png"/>
          <div v-if="(td.url === '' || td.url === null) && (td.image === '' || td.image === null) && td.pinyin !== '' && td.pinyin.length > 2" class="pc_sto_img">
            {{td.pinyin.length > 2 ? td.pinyin.substring(0,2) : ''}}<br><span style="margin-left: 8px;">{{td.pinyin.substring(2,(td.pinyin.length > 4 ? 4 : td.pinyin.length))}}</span>
          </div>
          <div v-show="(td.image == '' || td.image == null) && td.pinyin !== '' && td.pinyin.length <= 2 && td.pinyin.length > 0"
            class="pc_sto_img2">
            {{td.pinyin}}
          </div>
          <div class="pc_sto35">
            <div style="margin-top: 2px;">
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="td.name">
                <div slot="reference">
                  {{td.name}}
                </div>
              </el-popover>
            </div>
            <div class="pc_sto35_flex">
              <el-popover
                v-if="!$employeeAuth('purchase_price')"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="'进价：' + '¥' + formatPurPriceReturn(td.purPrice)">
                <div slot="reference">
                  <span style="font-size: 12px;">{{ '进价 '}}</span>
                  {{'¥' + formatPurPriceReturn(td.purPrice)}}
                </div>
              </el-popover>
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="`库存：${$toDecimalFormat(td.curStock, 3, true)}`">
                <div slot="reference" style="margin-left: 5px;">
                  <span style="font-size: 12px;">库存 </span>
                  {{$toDecimalFormat(td.curStock, 3, true)}}
                </div>
              </el-popover>
            </div>
            <div style="height: 16px;"></div>
          </div>
        </div>
        <div
          v-show="right_list.length == 0 && !loading"
          class="pc_sto43"
        >
          <div class="pc_sto44">
            <img alt="" src="../../image/pc_no_goods.png" />
            <div>暂无商品信息</div>
          </div>
        </div>
        <div style="width: 100%;height: 20px;"></div>
      </div>
    </div>
    <div class="pc_stock_36">
      <type-menu ref="typeMenu"
        v-model="type"
        :edit-top="157"
        @edit="editType"></type-menu>
    </div>
    <div
      class='upgrade1'
      v-show='showZeroTips'
    >
      <div
        class='upgrade11'
      >
        <div class='upgrade12'><span>提示</span></div>
        <div style="color: #4C567C;text-align: center;font-size: 24px;">
          <div>进货数量不能为0，</div>
          <div>请确认进货数量再保存</div>
        </div>
        <div class="upgrade12">
          <div style="width: 130px;
            border-radius: 4px;
            font-size: 20px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            line-height: 50px;
            text-align: center;
            cursor: pointer;
            margin-top: 34px;
            margin-left: 33%;
          "
          class="buyOrder"
          @click="showZeroTips = false">确认</div>
        </div>
      </div>
    </div>
    <div class='upgrade1' v-show="isShowSupplier" @click.stop="showSupplier = false; showNotice = false">
      <div class='upgrade11' @click.stop="showSupplier = false; showNotice = false">
        <div class='upgrade12'><span>选择{{buyOrder ? '进货' : '退货'}}方式</span></div>
        <div class="upgrade-dialg" v-if="buyOrder">
          <div
          class="upgrade-dialg__title"
          @click="isAverage = !isAverage"
          v-show="!$employeeAuth('purchase_price') && buyOrder"
        >
          <div>
            <img
              alt="选择"
              v-show="!isAverage"
              src="../../image/pc_goods_checkbox1.png"
              class="dialog-title__image"
            />
            <img
              alt="选择"
              v-show="isAverage"
              src="../../image/pc_goods_checkbox2.png"
              class="dialog-title__image"
            />
          </div>
          <div class="dialog-title__name">进货价加权平均</div>
          </div>
          <div
            v-show="!$employeeAuth('purchase_price') && buyOrder"
            @click.stop="showNotice = true"
            class="dialog-title__icon">
            ?
          </div>
        </div>
        <div class="upgrade-dialg">
          <div
          class="upgrade-dialg__title title-marign"
          @click="isSupplier = !isSupplier"
          v-show="$employeeAuth('import_products')"
        >
          <div>
            <img
              alt="选择"
              v-show="!isSupplier"
              src="../../image/pc_goods_checkbox1.png"
              class="dialog-title__image"
            />
            <img
              alt="选择"
              v-show="isSupplier"
              src="../../image/pc_goods_checkbox2.png"
              class="dialog-title__image"
            />
          </div>
          <div class="dialog-title__name">{{buyOrder ? '进货' : '退货'}}商品更新供应商</div>
          </div>
          <div
            v-show="$employeeAuth('import_products')"
            @click.stop="showSupplier = true"
            class="dialog-title__icon">
            ?
          </div>
        </div>
        <div class="upgrade-dialog">
          <div class="dialg-close" @click="isShowSupplier = false">取消</div>
          <div class="dialg-submit" @click="submitSupplier">确定</div>
        </div>
        <!-- 是否更新供应商弹窗 -->
        <div v-show="showSupplier" :class="buyOrder ? 'upgrade-dialg__top' : 'upgrade-dialg__buy'">
          <div class="pc_sto51"></div>
          <div class="dialog-title__detail">
            <div class="detail-color">勾选后，将更新本次{{buyOrder ? '进货' : '退货'}}商品的供应商信息（可在商品、报表中查看供应商信息）</div>
          </div>
        </div>
        <!-- showNotice -->
        <div v-show="showNotice" class="upgrade-dialg__bottom">
          <div class="pc_sto51"></div>
          <div class="pc_sto5" v-show="buyOrder">
            <div class="bottom-color">说明：未勾选则会按照本次进货价格更新商品进货价，之后的销售单利润会按照变更后的进货</div>
            <div class="bottom-color">价计算。（负库存商品无法参与平均进价；变更前的报表数据不会受到影响）</div>
            <div class="bottom-title">进货价加权平均：</div>
            <div class="bottom-title">掌柜智囊的进货价按照入库进货价及数量，系统内商品现有库存的进货价及数量加权平均计算。</div>
            <div class="bottom-title bottom-title__color">例如：10元成本价进了10件商品，卖得剩下5件，花了15元成本价又进货10件，通过系统的进</div>
            <div class="bottom-title__color">货价加权平均计算(10*5+15*10)/(5+10)=13.33</div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="showChosePrint"
      width="440px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">{{printIndex === 'barcode' ? '打印条码' : '打印标价签'}}</div>
        <div
          class="icon_close"
          @click="showChosePrint = false"
        >×</div>
      </div>
      <div class="pc_stock41">
        <div v-show="printIndex === 'barcode' ? !setLabelDefault : !setTipDefault"
          class="pc_stock40" style="margin-bottom: 20px;">
          <div class="pc_stock39">打印份数</div>
          <el-input-number
            v-model="addPrint"
            @blur="setNum"
            placeholder="请输入打印份数"
            :min="1"
            :max="999"
            v-input-int-max-min="{max: 999,min: 0}"
            style="width: 248px;font-size: 18px;"
          ></el-input-number>
        </div>
        <div v-show="printIndex === 'barcode' && labelItem[3].flag && cols !== '40'" class="pc_stock40" style="margin-bottom: 20px;">
          <div class="pc_stock39">生产日期</div>
          <div class="pc_stock45">
            <el-date-picker
              v-model="commodityDate"
              type="date"
              placeholder="请输入生产日期"
              value-format='yyyy-MM-dd'
              style="width: 248px;"
            >
            </el-date-picker>
          </div>
        </div>
        <div v-show="printIndex === 'barcode' && labelItem[4].flag && cols !== '40'" class="pc_stock40">
          <div class="pc_stock39" style="margin-right: 80px;">保质期</div>
          <div class="pc_stock44">
            <el-input
              v-model="commodityEndDate"
              placeholder="请输入保质期"
              v-input-int-max-min="{max: 9999, min: 0}"
              style="width: 248px;"
            >
              <em slot="suffix" style="font-style: normal;line-height: 40px;">天</em>
            </el-input>
          </div>
        </div>
        <div class="pc_stock40" style="margin-top: 30px;">
          <div class="pc_stock42" @click="showChosePrint = false">取消</div>
          <div class="pc_stock43" @click="submitPrint">确定</div>
        </div>
     </div>
    </el-dialog>
    <!-- 是否继续进货确认弹窗 -->
    <confirm-dialog :visible.sync="continueShow"
      cancel-text="继续进货"
      confirm-text="查看进货明细"
      :btn-font-size="20"
      :closeOnClickModal="false"
      message="进货单已提交，<br/>是否查看进货明细"
      @cancel="handleContinue"
      @confirm="setOthers"></confirm-dialog>
    <!-- 加权平均提示弹窗 -->
    <confirm-dialog :visible.sync="averageTipsShow"
      confirm-text="继续进货"
      :btn-font-size="20"
      :closeOnClickModal="false"
      :message="`当前${isAverage ? '已' : '未'}勾选“进货价加权平<br/>均”，是否继续`"
      @cancel="averageTipsShow = false"
      @confirm="averageTipsClick"></confirm-dialog>
    <!-- 批量新增时单位提示 -->
    <confirm-dialog :visible.sync="batchAddStock"
      :btn-font-size="20"
      :closeOnClickModal="false"
      message="称重商品需填写相应的称重单<br />位，否则会按非称重商品处<br />理，确定继续吗？"
      @cancel="batchAddStock = false"
      @confirm="emitBatchAddStock"></confirm-dialog>
    <!-- 批量新增时无进货价权限提示 -->
    <confirm-dialog :visible.sync="isPurchasePrice"
      :btn-font-size="20"
      :closeOnClickModal="false"
      message="您没有查看进价权限，批量进<br />货模板中的进货价在提交进货<br />单时将不会生效"
      @cancel="isPurchasePrice = false"
      @confirm="emitIsPurchasePrice"></confirm-dialog>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style';
import stringUtils from '../../common/stringUtils';
import FileSaver from 'file-saver';
import logList from '@/config/logList';
import CjInput from '@/common/components/CjInput'
import ConfirmDialog from '@/common/components/ConfirmDialog';
import TypeMenu from '@/common/components/TypeMenu';
import goodsSearch from '@/mixins/goodsSearch';
import { date } from 'frontend-utils';
import { eleToView } from '@/utils/util.js';
export default {
  mixins: [goodsSearch],
  components: {
    CjInput,
    ConfirmDialog,
    TypeMenu
  },
  data() {
    return {
      loading: false,
      isSubmitOrAdd: '1', // 是否提交并新增
      editCode: '',
      changeFlag: false, // 供应商请选择flag
      batchAddStock: false, // 批量新增单位提示
      editUnitCode: '',
      printting: false,
      isPurchasePrice: false, // 进货价弹出提示
      printFlag: 1,
      codeId: '1',
      goodsName: '',
      salePrice: '',
      majorCode: '',
      importData: [],
      missingItem: [],
      codeList: [
        {
          value: '1',
          label: '条码'
        },
        {
          value: '2',
          label: '货号'
        }
      ],
      printOne: false,
      showZeroTips: false,
      right_list: [],
      table_num: 0,
      account_list: [
        {
          value: '1',
          label: '现金'
        },
        {
          value: '4',
          label: '微信'
        },
        {
          value: '5',
          label: '支付宝'
        }
      ],
      showEditPrice: false,
      showEditSalePrice: false,
      inputSalePrice: "",
      inputMemberPrice: "",
      inputIndex: "",
      alertPurPrice: '',
      alertBuyNumber: '',
      alertPrice: '',
      alertUnitName: '',
      inputPrice: '',
      printClick: false,
      subPrintClick: false,
      inputBuyNumber: '',
      chooseEditPrice: 0,
      type: '', // 默认热销分类
      show_weighing_category: false,
      // 右侧搜索关键字
      keyword: '',
      can_getProdList: true,
      limit: 50,
      pagenum: 1,
      leftList: [],
      total_number: 0,
      total_money: 0,
      // 供应商ID
      companiesId: 0,
      // 供应商选择检索框内容
      selSearchValue: '',
      // 供应商列表
      makerlist: [],
      // 供应商显示列表
      makerlistShow: [],
      // 账户付款方式
      acctsId: '1',
      // 备注说明
      pursRemark: '',
      // 折扣率
      pursDisc: '100',
      // 折后应付
      pursDisc_amt: 0,
      // 本单实付
      pursPay_amt: 0,
      // 进退货状态
      // 1进货2退货
      pursTye: 1,
      // 先产生一个进货单号，然后进行保存
      pursCode: '',
      // 要上传的左侧列表
      sub_list: [],
      table_height: 0,
      // 扫码枪变量
      caseFormat: false,
      lastTime: '',
      // 设置定时器变量
      scanTimeer: null,
      printRow: [],
      addPrint: 1,
      showChosePrint: false,
      commodityEndDate: '',
      commodityDate: '',
      printIndex: 'barcode',
      averageTimer: null,
      supplierTimer: null,
      // 是否清空新增进货单弹框
      sure_clean_list: false,
      // 是否是批量进货弹窗
      sure_quantity: false,
      show_import: false,
      // 确认导入弹窗
      sure_import: false,
      proceed: false,
      // 缺失商品新增弹窗
      show_open: false,
      // 批量新增商品提示
      sure_addGoods: false,
      // 商品新增成功提示
      show_success: false,
      // 批量导入
      upload_loading: false,
      upload_complete: false,
      upload_data: {
        total: 0,
        correct: 0,
        errDown: false,
        wrong: 0
      },
      tmpDown: '',
      // 是否允许监听type改变
      watch_type: true,
      // 弹出是否要新增商品
      show_if_add_goods: false,
      create_time: '',
      stop_scrollFn: false,
      ready_subOrder: false,
      pinyin: false,
      category_value: '',
      isAverageN: 0,
      isAverage: false,
      isSupplier: false,
      showAverage: false,
      showNotice: false,
      showSupplier: false,
      isShowSupplier: false,
      originalLeftList: [],
      purchaseTemp: [],
      showTempConfirm: false,
      repeatCode: '',
      repeatData: [],
      typeOption: [],
      unitOption: [],
      continueShow: false, // 是否显示继续进货弹窗
      averageTipsShow: false, // 是否显示加强平均弹窗
      oldPursDisc: '100', // 用于对比打折率是否变化
      oldPursDiscAmt: 0 // 用于对比折后应付是否变化
    };
  },
  created() {
    // 开启扫码枪监听
    external.scanerHookStart();
    // 监听屏幕高度变化，给table设置高度，方便屏幕固定
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
    if (this.buyOrder) {
      this.pursTye = 1;
    } else {
      this.pursTye = 2;
    }
    // 获取进货价加权平均
    this.getIsAverage();
  },
  mounted() {
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    this.getAllUnit();
    this.getPursCode();
    document.getElementById('stock_right_list_id').addEventListener('scroll', this.scrollFn);
    // 加载供应商列表
    this.getMakerListData();
    this.loadOrderTemp();
    this.reSearchLeft();
    this.getAllCategory();
  },
  watch: {
    showAverage() {
      if (this.showAverage) {
        this.makeSubList();
        if (this.sub_list.length === 0) {
          demo.msg('warning', '没有需要平均的进价，直接提交');
          this.subOrder();
        }
      }
    },
    // 监听总额变化
    watchTotalPrice: {
      handler(newValue) {
        let totalPrice = 0.00;
        let buyNumber = 0;
        let price = this.alertPurPrice ? this.alertPurPrice : 0;
        if (!demo.isNullOrTrimEmpty(newValue[0]) && !isNaN(Number(newValue[0]))) {
          totalPrice = Number(newValue[0]).toFixed(2);
        }
        if (!demo.isNullOrTrimEmpty(newValue[1]) && !isNaN(Number(newValue[1]))) {
          buyNumber = Number(newValue[1]);
        }
        if (totalPrice !== 0 && buyNumber !== 0) {
          price = totalPrice / buyNumber;
        }
        price = demo.$conversion(price, 1000000);
        this.alertPurPrice = price;
        this.alertBuyNumber = buyNumber;
        this.alertPrice = totalPrice;
      },
      deep: true,
      immediate: false
    },
    pursTye() {
      console.log(this.pursTye, 'pursTye');
      this.right_list = [];
      this.limit = 50;
      this.pagenum = 1;
      this.handleGoodList();
    },
    addStockGoods() {
      if (this.addStockGoods != [] && this.addStockGoods.length != 0) {
        var data = {
          pset: "",
          type: "",
          condition: this.addStockGoods.name,
          limit: 1,
          offset: 0,
          ifexact: 1
        };
        var that = this;
        goodService.search(data, function (res) {
          that.pushLeft(demo.t2json(res)[0]);
          that.SET_SHOW({ addStockGoods: [] });
        });
      }
    },
    showAddGoods() {
      if (this.showAddGoods === false) {
        this.type = '';
        this.category_value = '';
        // 分类列表查询是否有称重商品
        this.$refs.typeMenu.pdWeighingCategory();
        this.reSearchLeft();
      }
    },
    scanerObj() {
      if (this.showAddGoods || this.showTypeManage || !this.zgznActive || this.showAverage) {
        return;
      }
      let scanerCode = demo.t2json(this.scanerObj).Result.replace(/[^\w-]|_/ig, '');
      if (scanerCode.length > 16) {
        demo.msg('warning', '商品条码最长16位!');
      } else if (scanerCode.length < 4) {
        demo.msg('warning', '商品条码最短4位!');
      } else {
        this.onScanInput(scanerCode);
        this.formatLeftList();
      }
    },
    type() {
      if (this.type === '-99') {
        this.getHotList();
      } else {
        this.reSearchLeft();
      }
    },
    leftList() {
      console.log(this.leftList, 'leftList');
      this.SET_SHOW({ stockListLength: this.leftList.length });
    },
    // 判断进退货，true进货,false退货
    buyOrder() {
      if (!this.buyOrder) {
        this.saveOrderTemp();
      }
      this.leftList = [];
      this.originalLeftList = [];
      this.sure_clean_list = false;
      this.sure_quantity = false;
      this.pursDisc = 100;
      this.pursRemark = '';
      this.total_money = 0;
      this.total_number = 0;
      this.pursDisc_amt = '0.00';
      this.pursPay_amt = '0.00';
      if (this.buyOrder) {
        this.pursTye = 1;
        this.loadOrderTemp();
      } else {
        this.pursTye = 2;
        demo.actionLog(logList.clickHomePurchaseBackGoods);
      }
    },
    keyword() {
      this.watch_type = false;
      this.can_getProdList = false;
      this.pagenum = 1;
      var that = this;
      setTimeout(function() {
        that.watch_type = true;
        that.can_getProdList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.stop_scrollFn = true;
        that.getProdList();
        setTimeout(function() {
          that.stop_scrollFn = false;
        }, 50);
      }, that.delayedTime);
    },
    isAverage() {
      this.setIsAverageGlobal();
    },
    isSupplier() {
      this.setIsSupplierGlobal();
    },
    showTypeManage() {
      if (!this.showTypeManage) {
        // 刷新分类列表
        this.$refs.typeMenu.getAllCategory();
      }
    },
    missingItem: {
      deep: true,
      handler: function() {
        // do nothing
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 清空数据
    allEmpty() {
      this.companiesId = '';
      this.leftList = [];
      this.originalLeftList = [];
      this.sure_clean_list = false;
      this.pursDisc = 100;
      this.pursRemark = '';
      this.countTotal();
      this.changeFlag = false; // 清空请选择flag
    },
    // 进货弹出框
    submitSupplier() {
      if (this.isAverage && this.buyOrder) {
        // 当前未勾选进价平均点击勾选并使用/当前已勾选进价平均点击继续进货
        this.isAverage = true;
        this.toShowAverage(0);
      } else {
        // 当前未勾选进价平均点击继续进货
        if (this.isSubmitOrAdd === '1') {
          this.subOrder(0);
        } else {
          this.subOrder(1);
        }
      }
      this.isShowSupplier = false;
    },
    // 更新供应商信息
    updateSupplier(n) {
      if (n) {
        const data = this.leftList.map(item => ({ fingerprint: item.fingerprint, supplierFingerprint: item.supplierFingerprint }));
        console.log(data);
        goodSupplierService.batchSetGoodsSupplier(data, res => {
          console.log(res);
          this.handleGoodList();
          // this.getProdList();
        }, err => { console.log(err); })
      } else {
        const fingerprintList = this.leftList.map(item => item.fingerprint);
        const supplierFingerprint = this.companiesId;
        const data = {
          fingerprintList: fingerprintList,
          supplierFingerprint: supplierFingerprint
        }
        goodSupplierService.batchUpdateGoodsSupplier(data, res => {
          console.log(res);
        }, err => { console.log(err); })
      }
    },
    // 获取所有类别
    getAllCategory() {
      this.typeOption = [];
      typeService.search(res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          this.typeArr = _.cloneDeep(json);
          json.forEach(item => {
            let obj = {
              label: item.name,
              value: item.fingerprint
            }
            if (item.list.length !== 0) {
              let arr = [];
              item.list.forEach(subItem => {
                let subObj = {
                  label: subItem.name,
                  value: subItem.fingerprint
                }
                arr.push(subObj);
              });
              obj['children'] = arr;
            }
            this.typeOption = this.typeOption.concat(obj);
          });
        }
      });
    },
    inputPriceFocus(event) {
      event.currentTarget.select();
    },
    // 补零方法
    formatpriceReturn(i) {
      const value = this[i];
      if (isNaN(value)) {
        this[i] = '0.00';
      }
      this[i] = Number(value).toFixed(2);
    },
    // 称重数量
    onCurStockChange(daCurStock, daUnitName) {
      var that = this;
      // 是否称重
      const isWeighing = that.weighUnitList.indexOf(daUnitName) !== -1;
      // 称重商品 数量最大值
      const dictWeighNumMax = 99999.999;
      // 非称重商品 数量最大值
      const dictNumMax = 99999;
      // 进货 数量范围 1 ~ 99999.999/99999
      const max = isWeighing ? dictWeighNumMax : dictNumMax;
      daCurStock = demo.$stockLimit({
        data: daCurStock,
        max: max,
        min: -max,
        decimals: that.weighUnitList.indexOf(daUnitName) !== -1 ? 3 : 0
      });
      return daCurStock;
    },
    // 总额校验
    onInputTotalPriceChange() {
      var that = this;
      const dictPriceMax = 999999.99;
      // 进货 数量范围 1 ~ 99999.999/99999
      let max = dictPriceMax;
      let min = 0;
      if (!that.buyOrder) {
        max = 0;
        min = Number(-dictPriceMax);
      }
      that.inputPrice = demo.$totalLimit({
        data: that.inputPrice,
        max,
        min,
        decimals: 2
      });
    },
    // 数量校验
    onInputNumberChange() {
      var that = this;
      // 是否称重
      const isWeighing = that.weighUnitList.indexOf(that.alertUnitName) !== -1;
      // 称重商品 数量最大值
      const dictWeighNumMax = 99999.999;
      // 非称重商品 数量最大值
      const dictNumMax = 99999;
      // 进货 数量范围 1 ~ 99999.999/99999
      let max = isWeighing ? dictWeighNumMax : dictNumMax;
      let min = 0.001;
      // 退货
      if (!that.buyOrder) {
        min = -max;
        max = -0.001;
      }
      // 小数点后几位
      const decimals = isWeighing ? 3 : 0;
      console.log(max, min, decimals);
      that.inputBuyNumber = demo.$totalLimit({
        data: that.inputBuyNumber,
        max,
        min,
        decimals
      });
    },
    saveOrderTemp() { // 销毁前保存进货单草稿
      let dataInfo = {
        leftList: _.cloneDeep(this.leftList),
        total_number: this.total_number,
        total_money: this.total_money,
        acctsId: this.acctsId,
        companiesId: this.companiesId,
        pursRemark: this.pursRemark,
        pursDisc: this.pursDisc,
        pursDisc_amt: this.pursDisc_amt,
        pursPay_amt: this.pursPay_amt,
        isAverage: this.$employeeAuth('purchase_price') ? this.isAverageGlobal : this.isAverage,
        isSupplier: !this.$employeeAuth('purchase_price') ? this.isSupplierGlobal : this.isSupplier
      };
      this.makeSubList();
      dataInfo.originalLeftList = _.cloneDeep(this.originalLeftList);
      let param = {
        pageName: 'purchase',
        info: JSON.stringify(dataInfo)
      };
      snapshotService.saveSnapshot(param,
        () => {
          // do nothing avoid sonar
          console.log('saveSnapshot success');
        },
        err => {
          // do nothing avoid sonar
          console.error(err);
        });
    },
    loadOrderTemp() {
      snapshotService.searchSnapshot({pageName: 'purchase'}, res => {
        if (res === '[]') {
          return;
        }
        let data = demo.t2json(res)[0];
        if (data && demo.t2json(data.info).leftList.length !== 0) {
          this.purchaseTemp = demo.t2json(data.info);
          this.showTempConfirm = true;
        }
      });
    },
    continueOrder() {
      if (this.purchaseTemp.originalLeftList) {
        this.purchaseTemp.originalLeftList = demo.t2json(this.purchaseTemp.originalLeftList);
        this.makeSubList();
      }
      Object.keys(this.purchaseTemp).forEach(key => {
        this[key] = this.purchaseTemp[key];
        // 将暂存的折扣率和折扣应付赋予对比值
        this.oldPursDisc = this.pursDisc;
        this.oldPursDiscAmt = this.pursDisc_amt;
      });
      this.makerlistShow = _.cloneDeep(this.makerlist);
      if (!this.makerlist.some(make => make.value === this.companiesId)) {
        this.companiesId = this.makerlist[0].value;
      }
      if (this.$employeeAuth('purchase_price')) {
        this.isAverage = false;
      }
      if (!this.$employeeAuth('import_products')) {
        this.isSupplier = false;
      }
      this.showTempConfirm = false;
    },
    isWeightProd(unit) {
      return this.weighUnitList.indexOf(unit) !== -1;
    },
    checkAverage(n) {
      if (isNaN(this.sub_list[n].averagePrice)) {
        this.sub_list[n].averagePrice = this.isWeightProd(this.sub_list[n].unitName) ? '0.000' : '0.00';
        return;
      }
      this.sub_list[n].averagePrice = this.formatPurPriceReturn(this.sub_list[n].averagePrice);
    },
    reSearchLeft() {
      if (!this.watch_type) {
        return;
      }
      this.stop_scrollFn = true;
      // this.keyword = '';
      this.pagenum = 1;
      this.right_list = [];
      this.getProdList();
      setTimeout(() => {
        this.stop_scrollFn = false;
        if (this.keyword_timer) {
          clearTimeout(this.keyword_timer);
        }
      }, 0);
    },
    stockCodeGet() {
      let code = this.majorCode;
      if (this.majorCode.length > 7) {
        code = this.majorCode.substring(0, 1) + '    ' + this.majorCode.substring(1, 7) + '    ' + this.majorCode.substring(7);
      }
      return code;
    },
    printSonar(printList, n) {
      printList.forEach(item => {
        if (!this.labelItem[0].flag) {
          item.name = '';
        }
        if (!this.labelItem[1].flag) {
          item.salePrice = '';
        }
        if (!this.labelItem[2].flag) {
          item.code = '';
        }
        if (n === 2 && this.labelItem[2].flag && this.codeId === '2') {
          item.code = item.majorCode;
        }
      });
      let printLists = _.flatMap(printList, this.duplicateLab);
      console.log(printLists, 'printLists');
      this.printOne = false;
      if (this.cols === '60') {
        external.printLabel(
          this.settingLabelPrinter,
          JSON.stringify(printLists)
        );
      } else {
        external.printLabel40(
          this.settingLabelPrinter,
          JSON.stringify(printLists)
        );
      }
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    inputFocus(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    // 打印条码
    print(row, n) {
      // 如果商品没有条码时不能打印条码和标价签
      if (!row.code || this.printClick) {
        return;
      }
      this.printClick = true;
      setTimeout(() => {
        this.printClick = false;
      }, this.clickInterval);
      let flg = (n === 'barcode') && !this.settingLabelPrinter;
      let flgTip = (n === 'tip') && !this.settingTipPrinter;
      if (flg || flgTip) {
        demo.msg('warning', '请设置打印机');
        this.SET_SHOW({ isPrintSetting: true });
        return;
      }
      this.setChooseList(row);
      this.printRow = [row];
      try {
        this.sonarPrint(n);
      } catch (e) {
        demo.msg('warning', this.$msg.printer_error);
      }
    },
    sonarPrint(n) {
      if (n === 'barcode') {
        if (!this.setLabelDefault || (this.labelItem[3].flag && this.cols !== '40') || (this.labelItem[4].flag && this.cols !== '40')) {
          this.commodityEndDate = '';
          this.commodityDate = '';
          this.addPrint = 1;
          this.printIndex = n;
          this.showChosePrint = true;
        } else {
          this.printLabels();
        }
      } else if (n === 'tip') {
        this.printIndex = n;
        this.printTip();
      }
    },
    printTip() {
      if (!this.setTipDefault) {
        this.commodityEndDate = '';
        this.commodityDate = '';
        this.addPrint = 1;
        this.showChosePrint = true;
      } else {
        this.printTipSubmit();
      }
    },
    setNum() {
      if (!this.addPrint) {
        this.addPrint = 1;
      }
    },
    submitPrint() {
      if (this.subPrintClick) {
        return;
      }
      this.subPrintClick = true;
      setTimeout(() => {
        this.subPrintClick = false;
      }, 1000);
      this.showChosePrint = false;
      if (this.printIndex === 'barcode') {
        if (!this.setLabelDefault) {
          this.SET_SHOW({ labelPrinterCopies: this.addPrint });
        }
        this.printLabels();
      } else {
        if (!this.setTipDefault) {
          this.SET_SHOW({ tipPrinterCopies: this.addPrint });
        }
        this.printTipSubmit();
      }
    },
    printTipSubmit() {
      let deepPrint = this.printRow;
      if (!this.printVipPrice) {
        _.forEach(deepPrint, function(item) {
          item.vip_price = '';
          item.vip_name = '';
        });
      }
      let printLists = _.flatMap(deepPrint, this.duplicateTip);
      printLists.map(item => {
        item.salePrice = Number(item.salePrice).toFixed(2);
        item.unit_name = item.unitName;
        item.sale_price_fontsize = this.sale_price_fontsize;
        if (Number(item.vip_price) === 0) {
          item.vip_price = '';
          item.vip_name = '';
        } else {
          item.vip_price = Number(item.vip_price).toFixed(2);
          item.vip_price_fontsize = this.vip_price_fontsize;
        }
      });
      external.printtip(
        this.settingTipPrinter,
        JSON.stringify(printLists)
      );
    },
    duplicateLab(n) {
      let arr = [];
      for (let i = 0; i < this.labelPrinterCopies; i++) {
        arr.push(n);
      }
      return arr;
    },
    duplicateTip(n) {
      let arr = [];
      for (let i = 0; i < this.tipPrinterCopies; i++) {
        arr.push(n);
      }
      return arr;
    },
    printLabels() {
      let emptyObj = {Title: ' ', Text: ' ', Size: 6};
      let printData = {
        printname: this.settingLabelPrinter,
        Width: this.getLabPrintWidth(),
        Height: this.getLabPrintHeight(),
        Landscape: this.getIsLandscape(),
        Offset: 0.01,
        DefaultSize: 10,
        others: []
      };
      let printList = _.cloneDeep(this.printRow[0]);
      let printObj = _.cloneDeep(printData);
      this.setPrintDataOthers(printObj, printList, emptyObj);
      for (let i = 0; i < this.addPrint; i++) {
        console.log(printObj, 'printObj+');
        external.printLabelAndBarcodeInMM(printObj);
      }
    },
    setPrintDataOthers(printObj, item, emptyObj) {
      if (this.labelItem[0].flag) {
        printObj.others.push({Title: '', Text: item.name.substr(0, 20), Size: 10});
      }
      if (this.labelItem[1].flag) {
        printObj.others.push({Title: '', Text: item.sale_price === '' ? '' : '售价:¥' + item.sale_price, Size: 10});
      }
      printObj.others.push(emptyObj);
      if (this.labelItem[2].flag && item.code) {
        printObj.others.push({
          barcode: item.code,
          barcode_Width: this.cols === '40' ? 40 : 60,
          barcode_Height: this.cols === '40' ? 40 : 60
        });
      }
      let dateStr = '';
      dateStr += this.labelItem[3].flag && this.cols !== '40' ? '生产日期:' + this.commodityDate + ' '
        : '                    ';
      dateStr += this.labelItem[4].flag && this.cols !== '40' ? '保质期:' + this.commodityEndDate + '天' : '';
      printObj.others.push({
        Title: '',
        Text: dateStr,
        Size: 8
      });
      if (this.cols === '64') {
        printObj.others.unshift(emptyObj);
      }
    },
    getLabPrintWidth() {
      return this.cols !== '60' ? this.labelPrintMap[this.cols].width
        : this.labelPrintMap[this.cols].height;
    },
    getLabPrintHeight() {
      return this.cols !== '60' ? this.labelPrintMap[this.cols].height
        : this.labelPrintMap[this.cols].width;
    },
    getIsLandscape() {
      return this.cols === '60';
    },
    setChooseList(row) {
      if (row.vip_price != 0) {
        row.vip_name = '会员价';
        row.vip_price = Number(row.vipPrice).toFixed(2);
      }
      row.sale_name = '售价';
      row.sale_price = Number(row.salePrice).toFixed(2);
      row.store_name = this.username;
    },
    deleteOneGoods(n) {
      this.leftList.splice(n, 1);
      this.originalLeftList.splice(n, 1);
      this.formatLeftList();
    },
    continueToHome() {
      this.SET_SHOW({ isHome: true });
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isCheck: false });
      this.SET_SHOW({ isDetail: false });
      this.SET_SHOW({ isStock: false });
      this.SET_SHOW({ isPay: false });
      this.SET_SHOW({ isSetting: false });
      this.cancelToHome();
      this.judgeExitIsAddGoods();
    },
    cancelToHome() {
      this.SET_SHOW({ stockDelList3: false });
    },
    continueToSupplier() {
      this.SET_SHOW({ buyOrder: false });
      this.SET_SHOW({ isStock: false });
      this.SET_SHOW({ isSupplierManage: true });
      this.SET_SHOW({ isCheck: false });
      this.cancelToSupplier();
    },
    cancelToSupplier() {
      this.SET_SHOW({ stockDelList2: false });
    },
    continueExit() {
      this.leftList = [];
      this.originalLeftList = [];
      this.cancelExit();
      this.SET_SHOW({ buyOrder: !this.buyOrder });
    },
    cancelExit() {
      this.SET_SHOW({ stockDelList: false });
    },
    // pursDisc 折扣率
    changePursDisc() {
      if (this.pursDisc > 100) {
        this.pursDisc = 100;
      }
      // 如果当前输入框内的折扣率没有变化时不进行反算折后应付
      if (Number(this.pursDisc) === Number(this.oldPursDisc)) {
        return;
      }
      // 折扣率变化时将变化后的折扣率和折后金额赋予对比值
      this.oldPursDisc = this.pursDisc =
        isNaN(this.pursDisc) || Number(this.pursDisc) < 0 ? '100' : Number(this.pursDisc).toFixed(2);
      this.pursDisc_amt = (
        Number(this.total_money) *
        (Number(this.pursDisc) / 100)
      ).toFixed(2);
      this.oldPursDiscAmt = this.pursPay_amt = this.pursDisc_amt;
    },
    // pursDisc_amt 折后应付
    changePursDiscAmt() {
      if (!this.buyOrder) {
        this.pursDisc_amt = Math.abs(this.pursDisc_amt) * -1;
      }
      if (isNaN(this.pursDisc_amt)) {
        this.pursDisc_amt = 0;
      }
      // 如果当前输入框内的折后应付没有变化时不进行反算折扣率
      if (Number(this.pursDisc_amt) === Number(this.oldPursDiscAmt)) {
        return;
      }
      // 折扣应付变化时将变化后的折扣率和折后金额赋予对比值
      this.oldPursDisc = this.pursDisc = isNaN(this.total_money) ||
          Number(this.pursDisc_amt) / Number(this.total_money) == Infinity
        ? '100'
        : ((Number(this.pursDisc_amt) / Number(this.total_money)) * 100).toFixed(2);
      if (Number(this.pursDisc_amt) === 0) {
        this.pursDisc = 100;
      }
      this.pursDisc_amt = Number(this.pursDisc_amt).toFixed(2);
      this.oldPursDiscAmt = this.pursPay_amt = this.pursDisc_amt;
    },
    countTotal() {
      this.total_number = 0;
      this.total_money = 0;
      for (var i = 0; i < this.leftList.length; i++) {
        this.total_number += Number(this.leftList[i].curStock);
        this.total_money += Number(this.leftList[i].total_money);
      }
      // 表格数据变化,折后应付 = 总金额 x 折扣率
      this.pursDisc_amt = (
        Number(this.total_money) *
        (Number(this.pursDisc) / 100)
      ).toFixed(2);
      this.oldPursDiscAmt = this.pursPay_amt = this.pursDisc_amt;
    },
    getAllUnit() {
      unitService.search(res => {
        var json = demo.t2json(res);
        this.unitOption = json;
        console.log(this.unitOption, 'this.unitOption+');
      });
    },
    branchInsert() { // 批量新增
      this.batchAddStock = true;
    },
    // 无进货价权限弹窗
    emitIsPurchasePrice() {
      this.isPurchasePrice = false;
      this.show_import = true;
    },
    // 批量新增确定回调
    emitBatchAddStock() {
      this.show_open = false;
      const params = _.cloneDeep(this.missingItem).map(item => {
        return {
          ...item,
          vipPrice: item.vipPrice || 0,
          typeFingerprint: item.typeFingerprintArr.length === 2 ? item.typeFingerprintArr[1] : item.typeFingerprintArr[0]
        };
      });
      console.log('batchInsertCheck 调用前params：', _.cloneDeep(params));
      goodService.batchInsertCheck(params).then(res => {
        this.batchAddGoods();
        this.show_import = false;
      }).catch(error => {
        CefSharp.PostMessage(`进货批量新增商品校验失败:${JSON.stringify(error)}`);
        if (error == 'Error: checkFail') {
          this.missingItem = params;
          this.show_open = true;
        }
      })
      this.batchAddStock = false;
    },
    // 批量进货清空商品列表
    sureClean() {
      this.sure_quantity = true;
    },
    // 批量进货，准备选excel
    batchImport() {
      demo.actionLog(logList.clickHomePurchaseBatchImportGoods);
      if (!this.$employeeAuth('purchase')) {
        return;
      };
      if (this.$employeeAuth('purchase_price')) {
        this.isPurchasePrice = true;
        return;
      }
      this.show_import = true;
    },
    batchClick() {
      this.upload_complete = false;
      this.upload_loading = false;
      document.querySelector('.input-file').click();
    },
    batchUpload(files, callback) {
      this.tmpDown = '';
      if (!event.currentTarget.files.length) {
        return;
      }
      const that = this;
      // 拿取文件对象
      var file = event.currentTarget.files[0];
      var filename = file.name;
      var suffixs = ['xls', 'xlsx'];
      if (
        suffixs.indexOf(
          filename.substr(filename.lastIndexOf('.') + 1).toLowerCase()
        ) === -1
      ) {
        demo.msg(
          'warning',
          that.$msg.support_suffixs.format({ suffixs: suffixs.join('、') })
        );
        return;
      }
      // 用FileReader来读取
      var reader = new FileReader();
      // 重写FileReader上的readAsBinaryString方法
      FileReader.prototype.readAsBinaryString = function (file) {
        reader.onload = function () {
          // 读取成Uint8Array，再转换为Unicode编码 （Unicode占两个字节）
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          var binary = '';
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          var wb = XLSX.read(binary, {
            type: 'binary'
          });
          const sheet2JsonOpts = {
            defval: '' // 给defval赋值为空的字符串
          };
          // 预读导入文件的表头检查是否被修改过
          const firstRow = XLSX.utils.sheet_to_json(
            wb.Sheets[wb.SheetNames[0]],
            {
              header: ["code", "name", "price", "qty", "salePrice", "vipPrice", "manufactureDate"],
              range: 'A1:G1',
              defval: ''
            },
            sheet2JsonOpts
          );
          if (firstRow.length) {
            let sheetMap = {code: "条码（必填）", name: "商品名称", price: "进货价（必填）", qty: "进货数量（必填）", salePrice: '零售价（修改商品售价）', vipPrice: '会员价（修改商品会员价）', manufactureDate: "生产日期"};
            let keyList = Object.keys(sheetMap);
            for (let i = 0; i < keyList.length; i++) {
              let prop = keyList[i];
              if (sheetMap[prop] !== firstRow[0][prop]) {
                demo.msg('warning', '模板错误，请下载最新模板');
                return;
              }
            }
          }
          let outdata = XLSX.utils.sheet_to_json(wb.Sheets['批量进货导入模板'], {header: [ "code", "name", "price", "qty", "salePrice", "vipPrice", "manufactureDate" ]}, sheet2JsonOpts);
          for (let i = 0; i < outdata.length; i++) {
            const item = outdata[i];
            item.name = that.nameImportFormat(item.name);
            item.code = (item.code && that.$convertFullWidthToHalfWidth(item.code)) || '';
            item.qty = that.prcFormat(that.$convertFullWidthToHalfWidth(item.qty), 3);
            item.price = that.prcFormat(that.$convertFullWidthToHalfWidth(item.price), 6);
            item.salePrice = (item.salePrice && that.prcFormat(that.$convertFullWidthToHalfWidth(item.salePrice))) || 0
            item.vipPrice = (item.vipPrice && that.prcFormat(that.$convertFullWidthToHalfWidth(item.vipPrice))) || 0
            item.manufactureDate = that.$convertFullWidthToHalfWidth(item.manufactureDate);
            if (demo.isExcelDateTimeNumber(item.manufactureDate)) { // 判断是否为excell 时间格式
              item.manufactureDate = new Date(demo.excelTimeToTimestamp(item.manufactureDate)).format('yyyy-MM-dd');
            } else {
              const regex = /^(200[0-9]|20[1-9][0-9]|2099)[-\/]?(0?[1-9]|1[0-2])[-\/]?(0?[1-9]|[12][0-9]|3[01])$/; // eslint-disable-line
              if (regex.test(item.manufactureDate)) {
                item.manufactureDate = item.manufactureDate.toString().replace(regex, (match, year, month, day) => {
                  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                });
              }
            }
          }
          console.log('批量导入进货数据：', outdata);
          const headerLine = {code: "条码（必填）", name: "商品名称", price: "进货价（必填）", qty: "进货数量（必填）", salePrice: '零售价（修改商品售价）', vipPrice: '会员价（修改商品会员价）', manufactureDate: "生产日期"};
          if (JSON.stringify(headerLine) != JSON.stringify(outdata[0])) {
            demo.msg('warning', '导入模板错误!');
            return;
          }
          console.log(outdata[0], '!!!');
          // var goods = stringUtils.fieldMapping(outdata, that.field_mapping, '错误原因');
          if (outdata.length <= 1) {
            demo.msg('warning', that.$msg.no_import_data);
            return;
          }
          if (outdata.length > 201) {
            demo.msg('warning', '单次批量导入数据条数不能大于200条!');
            return;
          }
          that.upload_loading = true;
          // 上传
          outdata.forEach((i) => {
            i.code = i.code.toString()
          });
          console.log(outdata, 'outdata+++');
          that.manualSync(outdata.slice(1));
        };
        reader.readAsArrayBuffer(file);
      };
      reader.readAsBinaryString(file);
      // 清空input，使下次选择文件生效
      document.querySelector('.input-file').value = '';
    },
    nameImportFormat(name) {
      return name ? String(name).replace(/[$]/g, '').replaceAll(/'/g, '‘') : '';
    },
    prcFormat(num, precision = 2) {
      if (this.isNumeric(num)) {
        return Number(num) == Number(num).toFixed(precision) ? this.limitDecimalPlaces(num, precision) : num
      } else {
        return num
      }
    },
    isNumeric(str) {
      return /^\d+(\.\d+)?$/.test(str);
    },
    limitDecimalPlaces(number, precision = 2) {
      let parts = number.toString().split('.');
      if (parts.length > 1 && parts[1].length > precision) {
        return parseFloat(`${parts[0]}.${parts[1].slice(0, precision)}`);
      }
      return number;
    },
    manualSync (outdata) {
      // 如果正在执行云同步，则不继续执行
      syncService.clearDataInfo(
        msg => {
          if (demo.isNullOrTrimEmpty(msg) || typeof (msg) === "object") {
            msg = this.$msg.sync_success;
          }
          this.importData = outdata;
          console.log(outdata, '~~~~~~~~~~');
          purchaseService.batchImport(outdata).then(res => {
            console.log(res, '批量导入成功res');
            if (res === 1) { // 导入内容包含一品多码商品
              this.sure_import = true;
            } else {
              this.upload_loading = false;
              this.upload_data.total = outdata.length;
              demo.msg('success', `共${this.upload_data.total}条数据，成功导入${this.upload_data.total}条数据`, 6000);
              this.show_import = false;
              this.upload_data.wrong = 0;
              outdata.forEach((i) => {
                console.log('i.name:', _.cloneDeep(i));
                this.importPushLeft(i);
              });
              this.getProdList();
              // 刷新分类列表
              this.$refs.typeMenu.getAllCategory();
            }
          }).catch(error => {
            console.log(error, '?????');
            if (error == 'Error: 导入模板错误' || error == 'Error: 入参错误' || error == 'Error: 没有导入数据' || error == 'Error: 导入数据超过200条限制') {
              demo.msg('warning', error);
              this.upload_loading = false;
              this.upload_complete = false;
            }
            if (error == 'Error: checkFail') {
              demo.msg('warning', '批量导入失败,具体原因请下载错误数据查看');
              this.upload_loading = false;
              this.upload_complete = true;
              this.upload_data.errDown = true;
              this.downloadMater(outdata);
              console.log(outdata.filter(err => err.errorMsg));
              this.upload_data.wrong = outdata.filter(err => err.errorMsg).length - 1;
            }
            if (error == 'Error: goodsNotExist') {
              this.upload_loading = false;
              this.upload_complete = false;
              this.show_open = true;
              this.missingItem = outdata.filter(i => i.goodsExist === 0).map(item => {
                return {
                  ...item,
                  typeFingerprint: md5('其他分类'),
                  typeFingerprintArr: [md5('其他分类')],
                  // typeId: '2',
                  // typeName: '其他分类',
                  unitFingerprint: '',
                  // unitName: '',
                  // unitId: '',
                  salePrice: item.salePrice || '0.00',
                  vipPrice: item.vipPrice || '0.00'
                }
              });
              console.log(this.missingItem, 'FSWNYMCA');
            }
          });
        },
        err => {
          console.log(err, '!!!!');
          if (demo.isNullOrTrimEmpty(err) || typeof (err) === "object") {
            err = this.$msg.sync_failure;
          }
          this.upload_loading = false;
          this.upload_complete = false;
          demo.msg('warning', err);
        }
      );
    },
    returnManufactureDate(manufactureDate) {
      if (manufactureDate) {
        if (typeof manufactureDate === 'number') {
          return new Date(demo.excelTimeToTimestamp(manufactureDate)).format('yyyy-MM-dd');
        } else if (typeof manufactureDate === 'string') {
          return manufactureDate.replace(/\//g, '-');
        } else {
          return '';
        }
      } else {
        return '';
      }
    },
    ypdmInput() { // 一品多码
      this.sure_import = false;
      this.show_import = false;
      this.upload_loading = false;
      this.upload_data.total = this.importData.length;
      demo.msg('success', `共${this.upload_data.total}条数据，成功导入${this.upload_data.total}条数据`, 6000);
      this.upload_data.wrong = 0;
      this.importData.forEach((i) => {
        i.buy_number = i.curStock;
        this.originalLeftList.push(_.cloneDeep(i));
        i.curStock = i.qty;
        i.purPrice = i.price;
        this.leftList.push(_.cloneDeep(i));
      });
      this.formatLeftList();
      this.getProdList();
      // 刷新分类列表
      this.$refs.typeMenu.getAllCategory();
    },
    returnTypeFingerprint(type, item) { // 获取批量导入未入库商品的type fingerprint
      let tmp;
      if (type === 'string') {
        if (item.typeFingerprint) {
        } else {
          tmp = md5('其他分类');
        }
      }
      return tmp;
    },
    judgeisParam(param) {
      if (param || param === 0) {
        return param;
      }
      return '';
    },
    downloadMater(info) {
      info.unshift({code: "条码（必填）", errorMsg: "错误原因", manufactureDate: "生产日期", name: "商品名称", price: "进货价（必填）", qty: "进货数量（必填）", salePrice: "零售价（修改商品售价）", vipPrice: "会员价（修改商品会员价）"});
      console.log(info, '错误原因文件~~');
      const that = this;
      let data = info.map((it) => {
        return {
          code: that.judgeisParam(it.code),
          name: that.judgeisParam(it.name),
          price: that.judgeisParam(it.price),
          qty: that.judgeisParam(it.qty),
          salePrice: that.judgeisParam(it.salePrice),
          vipPrice: that.judgeisParam(it.vipPrice),
          manufactureDate: that.judgeisParam(it.manufactureDate),
          errorMsg: that.judgeisParam(it.errorMsg)
        };
      });
      data[0].errorMsg = '错误原因';
      console.log('data', data);
      const defaultCellStyle = {
        font: { name: '宋体' }
      };
      const wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
        defaultCellStyle: defaultCellStyle,
        showGridLines: false
      };
      const wb = { SheetNames: ['批量进货导入模板', '备注'], Sheets: {}, Props: {} };
      wb.Sheets['批量进货导入模板'] = XLSX.utils.json_to_sheet(data, {skipHeader: true, hidden: [], skipHidden: false});
      wb.Sheets['批量进货导入模板']['!cols'] = [{wch: 20}, {wch: 16}, {wch: 16}, {wch: 16}, {wch: 25}, {wch: 28}, {wch: 20}];
      let borderAll = {
        top: { style: 'thin', color: '000000' },
        bottom: { style: 'thin', color: '000000' },
        left: { style: 'thin', color: '000000' },
        right: { style: 'thin', color: '000000' }
      };
      let style1 = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: borderAll
      };
      let columns = ['A1', 'C1', 'D1'];
      columns.forEach((column) => {
        wb.Sheets['批量进货导入模板'][column].s = style1;
      });
      let style2 = {
        fill: {
          fgColor: { rgb: 'B7DEE8' }
        },
        font: { name: '宋体' },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: borderAll
      };
      columns = ['B1', 'E1', 'F1', 'G1'];
      columns.forEach((column) => {
        wb.Sheets['批量进货导入模板'][column].s = style2;
      });
      let style3 = {
        font: { name: '宋体' },
        border: borderAll
      };
      columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
      for (let i = 2; i <= data.length; i++) {
        columns.forEach((column) => {
          wb.Sheets['批量进货导入模板'][column + i].s = style3;
        });
      }
      this.addSheet(wb, borderAll)
      // 创建二进制对象写入转换好的字节流
      let tmpDown = new Blob([stringUtils.s2ab(XLSXStyle.write(wb, wopts))], {
        type: 'application/octet-stream'
      });
      console.log(tmpDown);
      this.tmpDown = tmpDown;
    },
    // sheet1为导出的错误数据
    // 添加sheet2为备注，把规则添加到导出错误数据的excel里，方便用户根据备注修改错误数据
    addSheet(wb, borderAll) {
      const data = [
        {A: '字段', B: '默认值', C: '输入内容说明', D: ''},
        {A: '条码', B: '', C: '最多16位，仅限字母、数字和中横线（建议13位)', D: '必填项目'},
        {A: '商品名称', B: '', C: '选填，最多60字', D: '选填项目'},
        {A: '进货价', B: '', C: '不小于0且不大于999999.999999，必须为数字，最多六位小数', D: '必填项目'},
        {A: '进货数量', B: '', C: '大于0且不大于99999.999，必须为数字，最多三位小数；\n非称重商品不可输入小数', D: '必填项目'},
        {A: '零售价', B: '', C: '不小于0且不大于999999.99，必须为数字，最多两位小数', D: '选填项目'},
        {A: '会员价', B: '', C: '不小于0且不大于999999.99，必须为数字，最多两位小数', D: '选填项目'},
        {A: '生产日期', B: '', C: '格式为：YYYY-MM-DD 例如：2022-07-09（月和日为个位数时需要在数字前补充“0”）', D: '选填项目'},
        {A: '', B: '', C: '', D: ''},
        {A: '', B: '', C: '', D: ''},
        {A: '导入说明（必读）', B: '', C: '', D: ''},
        {A: '1、商品条数过多时建议分批导入，每次建议不超过200条；', B: '', C: '', D: ''},
        {A: '2、一品多码商品进货数自动加合（在系统中以主条码显示），进货金额、零售价、会员价生产日期取一品多码第一条显示。', B: '', C: '', D: ''}
      ]
      wb.Sheets['备注'] = XLSX.utils.json_to_sheet(data, {skipHeader: true, hidden: [], skipHidden: false});
      wb.Sheets['备注']['!cols'] = [{wch: 19}, {wch: 12}, {wch: 72}, {wch: 12}];
      // styleBlue、styleYellow、styleRed代表蓝色部分样式、黄色部分样式、红色部分样式
      // 这种写法好处是改变其中一个，其余的相同赋该变量值的都会进行改变
      // 这也是缺点，就是单点其中一个做修改，其余的也会影响
      // 红色变量注释了，就是因为其中一个不需要边框，去掉的时候，凡事红色式样相关的边框都消失了，产生了问题
      let styleBlue = {
        fill: {
          fgColor: { rgb: 'B6DDE8' }
        },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colBlue = ['A1', 'A3', 'A6', 'A7', 'A8', 'B1', 'C1']
      colBlue.forEach(el => {
        wb.Sheets['备注'][el].s = styleBlue;
      });
      let styleYellow = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colYellow = ['A2', 'A4', 'A5']
      colYellow.forEach(el => {
        wb.Sheets['备注'][el].s = styleYellow;
      });
      // let styleRed = {
      //   font: { color: {rgb: 'FF0000'} }
      // };
      const colRed = ['A11', 'D2', 'D4', 'D5']
      colRed.forEach(el => {
        wb.Sheets['备注'][el].s = {
          font: { color: {rgb: 'FF0000'} }
        };
      })
      wb.Sheets['备注']['!merges'] = [
        {s: {r: 11, c: 0}, e: {r: 11, c: 3}},
        {s: {r: 12, c: 0}, e: {r: 12, c: 3}}
      ]
      for (let i = 1; i < 9; i++) {
        this.addBorder('A', i, wb, borderAll)
        this.addBorder('B', i, wb, borderAll)
        this.addBorder('C', i, wb, borderAll)
        this.addBorder('D', i, wb, borderAll)
      }
      wb.Sheets['备注']['A11'].s.border = {}
    },
    addBorder(column, i, wb, borderAll) {
      if (wb.Sheets['备注'][column + i].s === undefined) {
        wb.Sheets['备注'][column + i].s = {border: borderAll}
      } else {
        wb.Sheets['备注'][column + i].s.border = borderAll
      }
    },
    downloadfaildata() {
      if (this.tmpDown !== '') {
        const time = date().format('YYYYMMDDHHmmss');
        FileSaver.saveAs(this.tmpDown, `批量进货导入失败的数据${time}.xlsx`);
      }
    },
    // 选择分类
    showType(code) {
      console.log('code', code);
      this.editCode = code;
      this.SET_SHOW({ showTypeManage: true });
    },
    addGoods() {
      demo.actionLog(logList.clickHomePurchaseAddGoods);
      if (!this.$employeeAuth('create_products')) {
        return;
      }
      this.SET_SHOW({ addProdFromStock: true });
      this.SET_SHOW({ suggestPrice: '' });
      this.SET_SHOW({ stockCode: '' });
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ addGoodsCategory: '其他分类' });
      this.SET_SHOW({ addGoodsUnit: '' });
    },
    pushLeft(td) {
      console.log(td, 'pushLeft');
      let deepTd = _.cloneDeep(td);
      deepTd.buy_number = Number(deepTd.curStock);
      if (this.leftList.length === 0) {
        deepTd.isHighlight = true;
        deepTd.curStock = deepTd.qty || 0;
        this.leftList.push(_.cloneDeep(deepTd));
        this.originalLeftList.push(_.cloneDeep(deepTd));
        if (this.buyOrder && !this.companiesId && this.leftList[0].supplierFingerprint && !this.changeFlag) {
          this.companiesId = this.leftList[0].supplierFingerprint;
        }
        this.formatLeftList();
        return;
      }
      this.leftList.forEach(item => {
        item.isHighlight = false;
      });
      for (var i = 0; i < this.leftList.length; i++) {
        if (this.leftList[i].fingerprint === td.fingerprint) {
          this.leftList[i].curStock = this.leftList[i].qty || (Number(this.leftList[i].curStock) + (this.pursTye === 1 ? 1 : -1));
          const unit = this.weighUnitList.indexOf(this.leftList[i].unitName) !== -1;
          if (Number(this.leftList[i].curStock) > 99999 && !unit) {
            // 非称重
            this.leftList[i].curStock = 99999;
            demo.msg('warning', '数量最多5位，称重商品只精确到小数点后3位')
          } else if (Number(this.leftList[i].curStock) > 99999.999 && unit) {
            // 称重
            this.leftList[i].curStock = 99999.999;
            demo.msg('warning', '数量最多5位，称重商品只精确到小数点后3位')
          } else if (Number(this.leftList[i].curStock) < -99999.999 && unit) {
            // 退货称重
            this.leftList[i].curStock = -99999.999;
            demo.msg('warning', '数量最多5位，称重商品只精确到小数点后3位')
          } else if (Number(this.leftList[i].curStock) < -99999 && !unit) {
            // 退货非称重
            this.leftList[i].curStock = -99999;
            demo.msg('warning', '数量最多5位，称重商品只精确到小数点后3位')
          }
          this.leftList[i].isHighlight = true;
          this.table_num++;
          this.formatLeftList();
          this.scorllToAdd(deepTd.fingerprint);
          return;
        }
      }
      if (!deepTd.qty) {
        if (this.pursTye === 1) {
          deepTd.curStock = 0;
        } else if (this.pursTye === 2) {
          deepTd.curStock = -1;
        } else {
          console.log('其他');
        }
      }
      td.isHighlight = true;
      this.leftList.push(_.cloneDeep(deepTd));
      this.leftList.map(item => {
        if (item.supplierFingerprint && !this.companiesId && !this.changeFlag) {
          this.companiesId = item.supplierFingerprint;
        }
      })
      this.originalLeftList.push(_.cloneDeep(deepTd));
      this.formatLeftList();
      this.scorllToAdd(deepTd.fingerprint);
    },
    // 滚动到新扫码的商品位置
    scorllToAdd(id) {
      this.$nextTick(() => {
        eleToView(id);
      });
    },
    importPushLeft(td) {
      let deepTd = _.cloneDeep(td);
      this.originalLeftList.push(_.cloneDeep(deepTd));
      deepTd.buy_number = Number(deepTd.curStock);
      deepTd.curStock = deepTd.qty || 0;
      deepTd.purPrice = !this.$employeeAuth('purchase_price') ? deepTd.price : deepTd.purPrice;
      this.leftList.push(_.cloneDeep(deepTd));
      this.formatLeftList();
      this.scorllToAdd(deepTd.fingerprint);
    },
    toShowAverage(n) {
      if (this.leftList.findIndex((e) => +e.curStock === 0) != -1) {
        this.showZeroTips = true;
        return;
      }
      if (this.leftList.length === 0) {
        demo.msg('warning', '请先选择商品！');
        return;
      }
      this.isAverageN = n;
      this.showAverage = true;
    },
    setIsAverageGlobal() {
      if (this.averageTimer) {
        clearTimeout(this.averageTimer);
      }
      let flag = this.$employeeAuth('purchase_price') ? this.isAverageGlobal : this.isAverage;
      this.averageTimer = setTimeout(() => {
        this.SET_SHOW({isAverageGlobal: flag});
        let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
        dataInfo.isAverage = flag;
        settingService.put([{ key: 'info', value: JSON.stringify(dataInfo), remark: '公共参数' }]);
      }, 350);
    },
    setIsSupplierGlobal() {
      if (this.supplierTimer) {
        clearTimeout(this.supplierTimer);
      }
      // 供应商是否勾选
      let flag = !this.$employeeAuth('import_products') ? this.isSupplierGlobal : this.isSupplier;
      this.supplierTimer = setTimeout(() => {
        this.SET_SHOW({isSupplierGlobal: flag});
        let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
        dataInfo.isSupplier = flag;
        settingService.put([{ key: 'info', value: JSON.stringify(dataInfo), remark: '公共参数' }]);
      }, 350);
    },
    makeSubList() {
      this.sub_list = [];
      let mid;
      let ops;
      for (let i = 0; i < this.leftList.length; i++) {
        mid = _.cloneDeep(this.leftList[i]);
        ops = _.cloneDeep(this.originalLeftList[i]);
        mid.price = mid.purPrice;
        mid.qty = Number(mid.curStock).toString();
        mid.disc = '1';
        mid.amt = (Number(mid.purPrice) * Number(mid.curStock)).toString();
        mid.goodFingerprint = mid.fingerprint;
        mid.old_pur_price = this.formatPurPriceReturn(ops.purPrice);
        this.getAveragePrice(ops, mid);
        this.sub_list.push(_.cloneDeep(mid));
      }
    },
    getAveragePrice(ops, mid) {
      if (this.buyOrder) {
        let stock = Number(mid.qty) + Number(mid.buy_number);
        let isPositiveStock = Number(mid.buy_number) > 0;
        if (isPositiveStock) {
          const purPrice = Number(ops.purPrice);
          const buyNumber = Number(mid.buy_number);
          const amt = Number(mid.amt);
          const conversionVal = ((mid.buy_number * ops.purPrice + mid.curStock * mid.purPrice) /
            (mid.buy_number + Number(mid.curStock))).toFixed(6);
          mid.averagePrice = this.formatPurPriceReturn(conversionVal);
          let logTxt = `getAveragePrice:purPrice=${purPrice}，buyNumber=${buyNumber}，amt=${amt}，
            stock=${stock}，conversionVal=${conversionVal}，averagePrice=${mid.averagePrice}`;
          CefSharp.PostMessage(logTxt);
        } else {
          mid.averagePrice = this.formatPurPriceReturn(Number(mid.purPrice).toFixed(6));
          let logTxt = `getAveragePrice:purPrice=${mid.purPrice}，mid.averagePrice=${mid.averagePrice}`;
          CefSharp.PostMessage(logTxt);
        }
      }
    },
    /**
     * 确认提交
     * @param {*} n 是否为退货，1退货，非1为进货
     * @param {*} boolean 是否为勾选了加权平均但选择本次不使用
     */
    subOrder(n, boolean) {
      if (this.ready_subOrder) return;
      this.ready_subOrder = true;
      if (this.showAverage && this.buyOrder) {
        for (let element of this.sub_list) {
          if (element.averagePrice < 0) {
            demo.msg('warning', '平均后进价不能为负数！');
            return;
          }
        }
      }
      if (!this.isAverage || !this.buyOrder || boolean) {
        this.makeSubList();
      }
      this.subOrderAct(n, boolean);
    },
    // 加权平均弹窗关闭
    averageCancel() {
      this.showAverage = false
      this.ready_subOrder = false
    },
    /**
     * 请求进货service
     * @param {Number} n 是否为退货，1退货，非1为进货
     * @param {Boolean} boolean 是否为勾选了加权平均但选择本次不使用
     */
    subOrderAct(n, boolean) {
      let param = {
        inOut: this.buyOrder ? '1' : '2',
        disc: Number(this.pursDisc) / 100,
        remark: this.pursRemark,
        accountId: this.acctsId,
        finalAmt: this.pursDisc_amt,
        purchaseItems: this.sub_list,
        isAverage: this.isAverage && this.buyOrder && !boolean ? this.isAverage : false,
        isSupplier: this.isSupplier && this.buyOrder && !boolean ? this.isSupplier : false
      };
      console.log(_.cloneDeep(param), '进货请求数据');
      if (this.buyOrder) {
        param.supplierFingerprint = this.companiesId ? this.companiesId : '';
        purchaseService.insert(_.cloneDeep(param), res => {
          if (this.isSupplier) {
            this.isSupplier = true;
            this.updateSupplier();
          }
          demo.msg('success', this.$msg.save_success);
          this.showAverage = false;
          this.pagenum = 1; // 重置右侧分页数据
          this.limit = 50; // 重置右侧分页数据
          this.right_list = []; // 重置右侧分页数据
          this.handleGoodList(); // 重新获取右侧商品列表
          if (this.$employeeAuth('show_shift_records')) {
            this.continueShow = true;
          } else {
            this.handleContinue(true);
          }
        }, err => {
          console.log(err);
          this.ready_subOrder = false;
        });
      } else {
        purchaseService.returnBack(_.cloneDeep(param), res => {
          demo.msg('success', this.$msg.save_success);
          if (this.isSupplier) {
            this.isSupplier = true;
            this.updateSupplier(true);
          }
          this.setOthers(res, param, n);
        }, err => {
          console.log(err);
          this.ready_subOrder = false;
        });
      }
    },
    // 重置页面数据重新进货,boolean是否跳转明细后的清空
    handleContinue(boolean) {
      this.leftList = []; // 清空右侧进货列表
      this.companiesId = ''; // 重置供应商选择
      this.changeFlag = false; // 清空请选择flag
      this.acctsId = '1'; // 重置账号选择
      this.pursRemark = ''; // 重置说明
      this.pursDisc = '100'; // 重置折扣率
      this.pursDisc_amt = 0; // 重置折扣应付
      this.oldPursDisc = '100'; // 重置折扣率对比值
      this.oldPursDiscAmt = 0; // 重置折扣应付对比值
      this.pursPay_amt = 0; // 重置本单实付
      this.ready_subOrder = false; // 重置可提交状态
      this.total_number = 0; // 重置合计数量
      this.total_money = 0; // 重置总额
      this.continueShow = false; // 关闭继续进货弹窗
      this.originalLeftList = [];
      this.getPursCode(); // 重新回去进货单号
      if (!boolean && this.buyOrder) {
        demo.actionLog({ page: 'stock', action: 'continuePurchase', description: `进货_是否查看进货明细_继续进货` });
      };
    },
    // 跳转进货/退货明细
    setOthers(res, param, n) {
      this.$log.info('stock', param);
      this.ready_subOrder = false;
      if (this.$employeeAuth('show_shift_records') && n !== 1) {
        this.SET_SHOW({ isStock: false });
        this.SET_SHOW({ isDetail: true });
        this.SET_SHOW({ pc_detail_tab: 2 });
        this.SET_SHOW({ fromDetail: 0 });
      }
      if (n === 1) {
        this.right_list.forEach(item => {
          for (let element of param.purchaseItems) {
            if (item.fingerprint === element.goodFingerprint) {
              item.curStock = Number(item.curStock) + Number(element.qty);
            }
          }
        });
        // 关闭进价平均对话框
        this.showAverage = false;
      }
      this.handleContinue(true);
      if (this.buyOrder) {
        demo.actionLog({ page: 'stock', action: 'continuePurchase', description: `进货_是否查看进货明细_查看进货明细` });
      };
    },
    // 进货价格式化返回
    formatPurPriceReturn(value) {
      if (isNaN(value)) {
        return '0.00';
      }
      let val = Math.abs(value);
      let dotIndex = String(val).indexOf('.');
      let dotAfterLength = dotIndex === -1 ? 0 : String(val).length - dotIndex - 1;
      let indexL = dotAfterLength <= 2 ? 2 : Math.min(dotAfterLength, 6);
      return Math.min(val, 999999.999999).toFixed(indexL);
    },
    // 获取单号
    getPursCode() {
      var that = this;
      var data = { type: 'JHD', table: 'purchases' };
      orderService.get(data, function(res) {
        var json = demo.t2json(res);
        that.pursCode = json[0].code;
      });
    },
    sonarList(n) {
      // 进货数为正，退货为负
      var a = 0;
      var b = n === 1 ? 0 : -1;
      var c = n === 1 ? 1 : -1;
      for (var i = 0; i < this.leftList.length; i++) {
        this.leftList[i].purPrice =
            isNaN(this.leftList[i].purPrice) == true
              ? a
              : this.formatPurPriceReturn(this.leftList[i].purPrice);
        this.leftList[i].curStock =
          isNaN(this.leftList[i].curStock) == true ||
          Math.abs(this.leftList[i].curStock) <= a
            ? b
            : ((Math.abs(this.leftList[i].curStock) * c) + '').replace(/^()*(\d+)\.(\d\d\d).*$/, '$1$2.$3');
        this.leftList[i].total_money = (
          this.leftList[i].purPrice * this.leftList[i].curStock
        ).toFixed(2);
      }
    },
    formatLeftList() {
      console.log(this.leftList, 'leftList', this.pursTye);
      if (this.pursTye === 1) {
        this.sonarList(1);
        this.pursDisc_amt = Math.abs(this.pursDisc_amt).toFixed(2);
      } else if (this.pursTye === 2) {
        this.sonarList(2);
        this.pursDisc_amt = (Math.abs(this.pursDisc_amt) * -1).toFixed(2);
      } else {
        console.log('其他');
      }
      this.countTotal();
    },
    leftListToDown() {
      this.$nextTick(() => {
        this.$refs.leftList.bodyWrapper.scrollTop = this.$refs.leftList.bodyWrapper.scrollHeight;
      });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      that.table_height = $(window).height() - 280;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 280;
      }, that.delayedTime);
    },
    onScanInput(str) {
      // 扫码枪扫完商品后搜索商品
      var that = this;
      var data = { pset: '', type: '', condition: this.$barCodeLimit(str), selectDel: false, limit: 100, offset: '0', isGroup: false };
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        if (json.length > 1) { // 存在重复条码商品
          that.repeatCode = that.$barCodeLimit(str);
          that.repeatData = _.cloneDeep(json);
          that.SET_SHOW({ showRepeatChoose: true });
        } else if (json.length === 1 && (json[0].code === str || that.extendCodesContain(json[0], that.$barCodeLimit(str)))) {
          that.pushLeft(json[0]);
        } else {
          if (that.showAddGoods) {
            return;
          }
          // 不直接显示，通过点击弹出框的新增，确定要新增商品
          if (!that.$employeeAuth('create_products')) {
            demo.msg('warning', '暂无此商品，请联系店主增加商品');
            return;
          }
          if (that.showAddGoods === false) {
            that.show_if_add_goods = true;
            that.stockCode = str;
          }
        }
      });
    },
    extendCodesContain(good, code) {
      return good.extendCodes && good.extendCodes.length &&
        good.extendCodes.map(item => { return item.extBarcode; }).indexOf(code) !== -1;
    },
    repeatChoose(repeatData, repeatIndex) {
      this.pushLeft(repeatData[repeatIndex]);
      this.SET_SHOW({ showRepeatChoose: false });
    },
    getMakerListData () {
      // 获取供应商列表数据
      console.log('调用getMakerListData');
      var that = this;
      supplierService.getSupplierDropDownList(res => {
        this.makerlist = [{
          value: null,
          label: '请选择'
        }];
        let makers = demo.t2json(res);
        console.log(makers, 'makerlist');
        for (let mk of makers) {
          this.makerlist.push({
            value: mk.fingerprint,
            label: mk.name
          });
        }
        that.companiesId = this.makerlist[0].value;
        this.makerlistShow = _.cloneDeep(this.makerlist);
      });
    },
    /**
     * 进货列表点击售价或者会员价显示编辑弹窗
     * da 点击的商品对象
     * index 点击的商品下标
     * boolean true点击零售价，零售价自动获取焦点，false点击会员价，会员价自动获取焦点
     */
    editSalePrice(da, index, boolean) {
      if (!this.$employeeAuth('import_products')) {
        return;
      }
      this.inputIndex = index;
      this.inputSalePrice = Number(da.salePrice).toFixed(2);
      this.inputMemberPrice = Number(da.vipPrice).toFixed(2);
      this.showEditSalePrice = true;
      // 根据点击的零售价或会员价使相应输入框获取焦点
      const refValue = this.$refs[boolean ? 'salePriceInput' : 'vipPriceInput'];
      this.$nextTick(() => {
        refValue.focus();
      })
    },
    // 进货列表修改零售价/会员价暂存
    saveSalePrice() {
      const item = this.leftList[this.inputIndex];
      item.salePrice = Number(this.inputSalePrice).toFixed(2);
      item.vipPrice = Number(this.inputMemberPrice).toFixed(2);
      console.warn(item, 'item')
      this.showEditSalePrice = false
    },
    savePrice() {
      this.leftList[this.inputIndex].curStock = Number(this.alertBuyNumber)
      this.leftList[this.inputIndex].purPrice = this.formatPurPriceReturn(this.alertPurPrice);
      this.leftList[this.inputIndex].total_money = Number(this.inputPrice).toFixed(2);
      this.countTotal();
      this.showEditPrice = false;
      console.log(this.leftList);
    },
    editPrice(da, index) {
      this.inputIndex = index;
      this.inputPrice = (Number(da.curStock) * Number(da.purPrice)).toFixed(2);
      this.inputBuyNumber = da.curStock;
      this.alertPurPrice = da.purPrice;
      this.alertBuyNumber = da.curStock;
      this.alertPrice = this.inputPrice;
      this.alertUnitName = da.unitName;
      this.showEditPrice = true;
    },
    dropDownSearch() {
      let that = this;
      this.makerlistShow = this.makerlist.filter(that.filterSearch);
      this.$nextTick(() => {
        if (this.makerlistShow.length === 0) {
          this.$refs.dropDownInput_empty.focus();
        } else {
          this.$refs.dropDownInput.focus();
        }
      });
    },
    flagChange(e) {
      if (demo.isNullOrTrimEmpty(e)) {
        this.changeFlag = true;
      }
    },
    clearDrop(e) {
      if (e) {
        this.selSearchValue = '';
        this.makerlistShow = _.cloneDeep(this.makerlist);
      }
      if (!e && demo.isNullOrTrimEmpty(this.companiesId)) {
        this.flagChange(this.companiesId);
      }
    },
    filterSearch(item) {
      return item.label.includes(this.selSearchValue);
    },
    getIsAverage() {
      if (demo.isNullOrTrimEmpty(this.isAverageGlobal) || demo.isNullOrTrimEmpty(this.isSupplierGlobal)) {
        if ($setting.info) {
          let dataInfo = demo.t2json($setting.info);
          if (dataInfo.isAverage || dataInfo.isSupplier) {
            this.isAverage = this.$employeeAuth('purchase_price') ? false : demo.t2json(dataInfo.isAverage);
            // 获取供应商
            this.isSupplier = !this.$employeeAuth('import_products') ? false : demo.t2json(dataInfo.isSupplier);
            this.SET_SHOW({ isAverageGlobal: demo.t2json(dataInfo.isAverage) }, { isSupplierGlobal: demo.t2json(dataInfo.isSupplier) });
          }
        }
      } else {
        this.isAverage = this.$employeeAuth('purchase_price') ? false : this.isAverageGlobal;
        this.isSupplier = !this.$employeeAuth('import_products') ? false : this.isSupplierGlobal;
      }
    },
    // 批量新增商品
    batchAddGoods() {
      var params = _.cloneDeep(this.missingItem).map(item => {
        return {
          ...item,
          vipPrice: item.vipPrice || 0,
          typeFingerprint: item.typeFingerprintArr.length === 2 ? item.typeFingerprintArr[1] : item.typeFingerprintArr[0]
        };
      });
      goodService.batchInsert(params).then(res => {
        this.show_success = true;
        this.handleGoodList();
      }).catch(error => {
        CefSharp.PostMessage(`进货批量新增商品失败:${JSON.stringify(error)}`);
        if (error == 'Error: checkFail') {
          this.missingItem = params.filter(i => i.goodsExist === 0).map(item => {
            return {
              ...item
              // typeFingerprint: this.returnTypeFingerprint('string', item),
              // typeFingerprintArr: this.returnTypeFingerprint('array', item)
            }
          });
          console.log('batchInsert执行后params:', params);
          this.show_open = true;
        }
      })
    },
    continueAddGoods() {
      this.SET_SHOW({ addProdFromStock: true });
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ stockCode: this.stockCode });
    },
    judgeExitIsAddGoods() {
      if (this.showAddGoods) {
        this.SET_SHOW({ goodsDetail: [] });
        this.SET_SHOW({ stockCode: '' });
        this.SET_SHOW({ addGoodsUnitId: '' });
        this.SET_SHOW({ addGoodsUnit: '' });
        this.SET_SHOW({ addGoodsCategoryId: '2' });
        this.SET_SHOW({ addGoodsCategory: '其他分类' });
        this.SET_SHOW({ showAddGoods: false });
      }
    },
    handleOpen(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleClose(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleSelect(key) {
      this.type = key.split('_')[0];
    },
    editType() {
      this.SET_SHOW({ showTypeManage: true });
      demo.actionLog(logList.clickHomePurchaseEditCategory);
    },
    // 进货/退货提交
    handleSubmit(e) {
      if (this.buyOrder) {
        if (this.leftList.length === 0) {
          demo.msg('warning', this.$msg.select_product);
          return;
        }
        if (this.pursDisc_amt === '') {
          demo.msg('warning', '请输入折后应付');
          return;
        }
        if (this.leftList.findIndex(e => +e.curStock === 0) != -1) {
          this.showZeroTips = true;
          return false;
        }
        if (!this.$employeeAuth('purchase_price') || this.$employeeAuth('import_products')) {
          this.isShowSupplier = true;
          return;
        }
        // 当前用户没有隐藏进货价权限时才会显示加权平均弹窗
        // if (!this.$employeeAuth('purchase_price')) {
        //   this.averageTipsShow = true;
        //   return;
        // }
        this.subOrder(0)
      } else {
        if (this.$employeeAuth('import_products')) {
          this.isShowSupplier = true;
          this.isSubmitOrAdd = e === 1 ? '0' : '1';
          return;
        }
        if (e === 1) {
          // 提交并新增
          this.subOrder(1);
        } else {
          // 提交
          this.subOrder(0);
        }
      }
    },
    /**
     * 提交时加权平均提示弹窗
     */
    averageTipsClick() {
      if (this.isAverage) {
        // 当前未勾选进价平均点击勾选并使用/当前已勾选进价平均点击继续进货
        this.isAverage = true;
        this.toShowAverage(0)
      } else {
        // 当前未勾选进价平均点击继续进货
        this.subOrder(0)
      }
      this.averageTipsShow = false
    }
  },
  computed: mapState({
    // 监听总额和数量
    watchTotalPrice() {
      const { inputPrice, inputBuyNumber } = this;
      return [
        inputPrice,
        inputBuyNumber
      ];
    },
    isHome: state => state.show.isHome,
    weighUnitList: state => state.show.weighUnitList,
    buyOrder: state => state.show.buyOrder,
    stockDelList: state => state.show.stockDelList,
    stockDelList2: state => state.show.stockDelList2,
    stockDelList3: state => state.show.stockDelList3,
    addStockGoods: state => state.show.addStockGoods,
    delayedTime: state => state.show.delayedTime,
    isAverageGlobal: state => state.show.isAverageGlobal,
    isSupplierGlobal: state => state.show.isSupplierGlobal,
    clickInterval: state => state.show.clickInterval,
    settingLabelPrinter: state => state.show.setting_label_printer,
    settingTipPrinter: state => state.show.setting_tip_printer,
    labelPrinterCopies: state => state.show.labelPrinterCopies,
    screenWidth: state => state.show.screenWidth,
    tipPrinterCopies: state => state.show.tipPrinterCopies,
    username: state => state.show.username,
    setLabelDefault: state => state.show.setLabelDefault,
    setTipDefault: state => state.show.setTipDefault,
    labelPrintMap: state => state.show.labelPrintMap,
    sale_price_fontsize: state => state.show.sale_price_fontsize,
    vip_price_fontsize: state => state.show.vip_price_fontsize,
    labelItem: state => state.show.labelItem,
    printVipPrice: state => state.show.printVipPrice,
    cols: state => state.show.print_cols_label,
    loginInfo: state => state.show.loginInfo,
    showAddGoods: state => state.show.showAddGoods,
    codeTimer: state => state.show.codeTimer,
    scanerObj: state => state.show.scanerObj,
    zgznActive: state => state.show.zgznActive,
    addProdFromStock: state => state.show.addProdFromStock,
    showTypeManage: state => state.show.showTypeManage
  }),
  beforeDestroy() {
    clearInterval(this.scanTimeer);
    this.scanTimeer = null;
    if (this.buyOrder) {
      this.saveOrderTemp();
    }
    external.scanerHookStop();
  },
  destroyed() {
    this.SET_SHOW({ stockListLength: 0 });
    this.SET_SHOW({ buyOrder: true });
    this.SET_SHOW({ showRepeatChoose: false });
  }
};
</script>
