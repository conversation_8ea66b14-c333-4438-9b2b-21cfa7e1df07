<template>
  <div v-show="show" class="dialogWrap" :class="[customClass]">
    <div class="dialogMask" @click="maskClick"></div>
    <div class="dialogContent"
      :style="{width: `${width}px`}">
      <!-- 标题插槽 -->
      <slot v-if="$slots.title" name="title"></slot>
      <div v-else class="dialogTitle"
        :style="{fontSize: `${titleFontSize}px`}">{{ title }}</div>
      <!-- 信息插槽 -->
      <slot v-if="$slots.message" name="message"></slot>
      <div v-else class="dialogMessage"
        :style="{fontSize: `${messageFontSize}px`}"
        v-html="message"></div>
      <!-- 底部插槽 -->
      <slot v-if="$slots.footer" name="footer"></slot>
      <div v-else class="dialogBtnWrap"
        :style="{fontSize: `${btnFontSize}px`}">
        <div v-if="showCancel" class="dialogCancel"
          @click="handleCancel">{{ cancelText }}</div>
        <div class="dialogConfirm" @click="handleConfirm">{{ confirmText }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ConfirmDialog',
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    // 对话框宽度
    width: {
      type: Number,
      default: 450
    },
    // 对话框标题
    title: {
      type: String,
      default: '提示'
    },
    // 标题字体大小
    titleFontSize: {
      type: Number,
      default: 25
    },
    // 对话框信息，支持html
    message: {
      type: String,
      default: ''
    },
    // 信息字体大小
    messageFontSize: {
      type: Number,
      default: 25
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 取消按钮文案
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文案
    confirmText: {
      type: String,
      default: '确定'
    },
    // 按钮字体大小
    btnFontSize: {
      type: Number,
      default: 24
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    },
    // 是否可以通过点击 modal 关闭 Dialog
    closeOnClickModal: {
      type: Boolean,
      default: true
    },
    // 是否开启节流
    throttle: {
      type: Boolean,
      default: true
    },
    // 节流时间ms
    throttleTime: {
      type: Number,
      default: 5000
    }
  },
  data() {
    return {
      show: false, // 是否显示弹窗
      loading: false,
      timer: null
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    // 点击遮罩
    maskClick() {
      if (this.closeOnClickModal) {
        this.$emit('cancel');
        this.$emit('update:visible', false);
      }
    },
    // 点击取消按钮
    handleCancel() {
      this.$emit('cancel');
      this.$emit('update:visible', false);
    },
    // 点击确定按钮
    handleConfirm() {
      if (this.throttle) {
        if (this.loading) return;
        this.loading = true;
        this.$emit('confirm');
        if (this.timer) clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          this.loading = false;
          this.timer = null;
        }, this.throttleTime);
      } else {
        this.$emit('confirm');
      }
    }
  }
};
</script>
<style lang="less" scoped>
.dialogWrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1001;
  .dialogMask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
  }
  .dialogContent {
    position: relative;
    z-index: 800;
    min-height: 235px;
    margin: 0 auto;
    margin-top: 240px;
    background: #fff;
    overflow: hidden;
    border-radius: 5px;
    padding-bottom: 30px;
    .dialogTitle {
      width: 100%;
      text-align: center;
      color: @themeFontColor;
      margin-top: 25px;
      font-weight: bolder;
    }
    .dialogMessage {
      width: 100%;
      text-align: center;
      color: @themeFontColor;
      margin-top: 25px;
      font-weight: 100;
    }
    .dialogBtnWrap {
      overflow: hidden;
      margin-top: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      .dialogCancel,
      .dialogConfirm {
        min-width: 138px;
        height: 50px;
        text-align: center;
        line-height: 48px;
        background: @themeFontColor;
        color: #fff;
        border-radius: 4px;
        cursor: pointer;
        padding: 0 10px;
      }
      .dialogCancel {
        margin-right: 30px;
      }
      .dialogConfirm {
        background: @themeBackGroundColor;
      }
    }
  }
}
</style>
