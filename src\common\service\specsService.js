import dao from '../dao/dao';
import store from '@/store';
const specsService = {
  getMaxSpecSyncTime() {
    return new Promise((resolve, reject) => {
      dao.exec(`select  COALESCE(max(sync_at),'2000-01-01 00:00:00') as syncAt  from specs`, (res) => {
        resolve(demo.t2json(res)[0]);
      }, (err) => {
        reject(err);
      });
    });
  },

  getOnLineSpecs(syncAt) {
    return new Promise((resolve) => {
      if (pos.network.isConnected()) {
        demo.$http.post(demo.$rest.getSpecs, syncAt, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 300000
        }).then(res => {
          res.status === 200 ? resolve(res.data.data) : resolve([]);
        }).catch(() => resolve([]));
      } else {
        resolve([]);
      }
    });
  },

  getOfflineSpecs() {
    return new Promise((resolve, reject) => {
      dao.exec(`select id, "name", parent_id, 
      is_show_on_spec, is_edit_self, is_add_sub, is_deleted as is_del,
      is_show_on_product, is_lock, create_at, revise_at, 
      sync_at from specs  where is_deleted=0 and version=0 order by id`, (data) => {
        resolve(data);
      }, (err) => {
        reject(err);
      });
    });
  },

  insertSpecBatch(data) {
    return new Promise((resolve) => {
      if (data.length > 0) {
        let specsBatch = `REPLACE into "specs"(id,"name", parent_id, 
        is_show_on_spec, is_edit_self, is_add_sub, 
        is_show_on_product, is_lock,is_deleted, version, create_at, revise_at, 
        sync_at)  values` + data.map(item => {
          return `('${item.id}','${item.name}','${item.parentId}',
        '${item.isShowOnSpec}','${item.isEditSelf}',
        '${item.isAddSub}','${item.isShowOnProduct}',
        '${item.isLock}','${item.isDel}','${item.version}','${item.createAt}',
        '${item.reviseAt}','${item.syncAt}')`;
        }).toString();
        dao.exec(specsBatch, () => {
          resolve();
        }, () => {
          resolve();
        });
      } else {
        resolve();
      }
    });
  },

  /**
   * this.$store.dispatch('info/getSpec').then(res=>{})
   * @returns
   */
  getSpecs() {
    return new Promise((resolve) => {
      this.getMaxSpecSyncTime(
      ).then(syncTime => {
        return this.getOnLineSpecs(syncTime);
      }).then(data => {
        return this.insertSpecBatch(data);
      }).then(() => {
        return this.getOfflineSpecs();
      }).then(res => {
        resolve(this.arrToThree(res));
      });
    });
  },

  arrToThree(arr) {
    return arr.reduce((result, val) => {
      +val['parentId'] === 0 ? result[val['id']] = {...val, 'item': {}} : result[val['parentId']]['item'][val['id']] = val;
      return result;
    }, {});
  },
  getSpecById (obj) {
    const specs = store.state.info.specs;
    return Object.keys(obj).map(key => {
      const parentId = key;
      const parentName = specs[key] ? specs[key]['name'] : '';
      const id = (specs[key] && specs[key]['item'][obj[key]]) ? specs[key]['item'][obj[key]]["id"] : '';
      const name = (specs[key] && specs[key]['item'][obj[key]]) ? specs[key]['item'][obj[key]]["name"] : '';
      return {parentId, parentName, id, name};
    });
  },
  specStringToArray (goods) {
    return goods.map(good => {
      if (good.specs) {
        const specs = JSON.parse(good.specs);
        let {unlock, lock} = specs;
        lock = lock ? this.getSpecById(lock) : {};
        unlock = unlock ? this.getSpecById(unlock) : {};
        return {...good, specs: { unlock, lock }};
      } else {
        return {...good};
      }
    });
  }
};

window.specsService = specsService;
export default specsService;
