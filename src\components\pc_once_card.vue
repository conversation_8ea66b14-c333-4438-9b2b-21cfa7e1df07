<style lang="less" scoped>
/deep/ .tab1 .el-table th > .cell {
    padding-left: 2px;
}
/deep/ .el-date-editor .el-range-separator {
  width: 7%;
}
/deep/ .el-input__icon.el-icon-date {
  display: none;
}
/deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
}
/deep/ .pc_rep11 > div:first-of-type > .el-input--suffix > .el-input__inner {
  height: 44px;
  width: 300px;
}
/deep/ .date_picker_container {
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 22px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  float: left;
  > .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date .el-input__inner {
    border: none;
    background: #FFFFFF;
    color: @themeFontColor;
    font-size: 16px;
    padding-left: 0px;
    padding-right: 0px;
    text-align: center;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-table th, .el-table tr {
    font-size: 16px;
    background: #F5F7FA;
    height: 50px;
}
/deep/ .el-input__inner {
  border-radius: 22px;
  height: 42px;
}
/deep/ .el-table__footer-wrapper td{
  font-size: 100%;
}
/deep/ .el-table__header-wrapper{
  font-weight: bold !important;
  font-size: 16px !important;
  color: @themeFontColor !important;
}
/deep/ .el-pagination button:disabled {
  background-color: #F5F8FB;
}
.pager_container {
  // width: 100%;
  // display: flex;
  // justify-content: flex-end;
  // padding: 10px;
  // display: flex;
  /deep/ .btn-next {
    background-color: #F5F8FB;
  }
  /deep/ .btn-prev {
    background-color: #F5F8FB;
  }
  /deep/ .number {
    background-color: #F5F8FB;
  }
  /deep/ .number.active{
    background-color: @themeBackGroundColor !important;
  }
}
.pager_container_new {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px;
  font-weight: normal;
  font-size: 18px;
  color: @themeFontColor;
  /deep/ .btn-next {
    background-color: #F5F8FB;
  }
  /deep/ .btn-prev {
    background-color: #F5F8FB;
  }
  /deep/ .number {
    background-color: #F5F8FB;
  }
  /deep/ .number.active{
    background-color: @themeBackGroundColor !important;
  }
}
.selType_div {
  float: left;
  width: 120px;
  margin-left: 10px;
  /deep/.el-input__inner {
    height: 44px;
    font-size: 16px;
  }
}
/deep/.tableBox_card .el-table--border, .el-table--group {
  border: 0px solid #EBEEF5;
}
/deep/.tab_content .el-table thead.is-group th {
  background: #F5F7FA;
  border-right: 1px solid #EBEEF5;
}
.tab_content .el-table--enable-row-transition .el-table__body td {
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
}
.tab_content .el-table .cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 23px;
  padding-right: 10px;
}
/deep/.tab_content{
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  border-bottom: 0px;
}
.isInPutIng {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div class="once_card_content"  v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="bottom">
      <!--次卡统计-->
      <div
        class="tab_content tab1"
        v-if="tab_index == 1"
      >
        <el-table
          :data="tableData"
          style="width: 100%"
          :height="table_height"
          :empty-text="!loading ? '暂无数据' : ' '"
          border
          stripe
          :header-cell-style="headerStyle"
          :summary-method="getSummaries"
          show-summary
          ref="table"
          class="tableBox_card"
        >
          <el-table-column
            prop="cardname"
            label="次卡名称"
            width="250"
            show-overflow-tooltip
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="购买金额"
            prop="remark1"
            min-width="95"
            show-overflow-tooltip
            align="right"
          >
            <template slot-scope="scope">{{parseNaN(scope.row.price,2)}}</template>
          </el-table-column>
          <el-table-column
            prop="times"
            label="次数"
            show-overflow-tooltip
            min-width="95"
            align="center"
          >
          </el-table-column>
          <el-table-column label="剩余统计">
            <el-table-column
              prop="restcards"
              label="次卡张数"
              show-overflow-tooltip
              min-width="95"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="resttimes"
              label="总次数"
              show-overflow-tooltip
              min-width="95"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="对应总价"
              prop="remark2"
              show-overflow-tooltip
              min-width="95"
              align="center"
            >
              <template slot-scope="scope">{{parseNaN(scope.row.restprice,2)}}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="已用统计">
            <el-table-column
              prop="usedcards"
              label="次卡张数"
              min-width="95"
              show-overflow-tooltip
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="usedtimes"
              label="总次数"
              min-width="95"
              show-overflow-tooltip
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="remark3"
              label="对应总价"
              min-width="95"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">{{parseNaN(scope.row.usedprice,2)}}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="购买统计">
            <el-table-column
              prop="saledcards"
              label="次卡张数"
              min-width="95"
              align="center"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="saledtimes"
              label="总次数"
              show-overflow-tooltip
              min-width="95"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop='remart4'
              label="对应总价"
              show-overflow-tooltip
              min-width="95"
              align="center"
            >
              <template slot-scope="scope">{{parseNaN(scope.row.saledprice,2)}}</template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <div class="pager_container">
          <el-pagination
            :key="pageKey"
            layout="prev, pager, next, slot"
            :total="statisticsTotal"
            @current-change="handleCurrentChange"
            :page-size="page_size"
            :current-page="current_page"
          >
            <!-- slot -->
            <vCjPageSize
              @sizeChange="handleSizeChange"
              :pageSize.sync="page_size"
              :currentPage.sync="current_page"
              :pageKey.sync="pageKey">
            </vCjPageSize>
          </el-pagination>
        </div>
      </div>
      <!--次卡销售明细/次卡使用明细-->
      <div
        v-if="tab_index == 2 || tab_index == 3"
        class="detail"
      >
        <div class="pc_rep11">
          <div class='search_input_box' style='float: left;'
            :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng1'"
            >
            <input
              @focus='inputing_keyword = true'
              @blur='inputing_keyword = false'
              type='text'
              placeholder='请输入姓名/手机号/刷卡'
              :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
              id='search_input_keyword_once_card'
              v-model='keyword'
              @input="keyword = $vipNameFormat(keyword)"
            />
            <img
              alt=""
              class='search_input_delete'
              v-show="keyword != ''"
              @click="focusInput('search_input_keyword_once_card')"
              src='../image/pc_clear_input.png'
            />
          </div>
          <div
            @click="focusDate = true"
            class="date_picker_container"
            :class="focusDate ? 'isInPutIng' : 'isInPutIng1'"
          >
            <el-date-picker
              v-model="date_range[0]"
              type="date"
              placeholder="开始日期"
              value-format='yyyy-MM-dd'
              @blur="focusDate = false"
            >
            </el-date-picker>
            <div style="font-size: 16px;color: #567485">至</div>
            <el-date-picker
              v-model="date_range[1]"
              type="date"
              placeholder="结束日期"
              value-format='yyyy-MM-dd'
              @blur="focusDate = false"
            >
            </el-date-picker>
          </div>
          <div
            @click="search"
            class="pc_rep14"
          >查询</div>
          <div
            class="pc_rep14"
            style="width:110px;float:right"
            @click="exportExcelData"
          >导出表格</div>
        </div>
        <div
          class="tab_content tab2"
          style="margin-top:10px"
        >
          <el-table
            :data="tableData2"
            style="width: 100%"
            :height="table_height"
            ref="tableData2Tab"
            stripe
            :empty-text="!loading ? '暂无数据' : ' '"
            :header-cell-style="headerStyleSale"
            :cell-style="CellStyleSale"
            class="tableBox_card"
          >
            <el-table-column
              prop="vipname"
              min-width="14%"
              label="会员"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="mobile"
              min-width="13%"
              label="手机号"
            >
            </el-table-column>
            <el-table-column
              min-width="21%"
              label="次卡名称"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="scope">
                {{scope.row.cardname ? scope.row.cardname : '-'}}
              </template>
            </el-table-column>
            <el-table-column
              min-width="13%"
              :prop="tab_index === 2 ? 'saledtimes' : 'createtime'"
              :label="tab_index == 2 ? '可用次数' : '消费时间'"
              align="center"
            >
            </el-table-column>
            <el-table-column
              :prop="tab_index === 2 ? 'price' : 'usedtimes'"
              :label="tab_index == 2 ? '购买金额' : '当前使用次数'"
              min-width="13%"
            >
            </el-table-column>
            <el-table-column
              min-width="13%"
              :prop="tab_index === 2 ? 'createtime' : 'resttimes'"
              :label="tab_index == 2 ? '购买时间' : '剩余次数'"
              align="center"
            >
            </el-table-column>
            <el-table-column
              min-width="13%"
              prop="expiredtime"
              label="到期时间"
              align="center"
            >
            </el-table-column>
          </el-table>
          <div class="pager_container">
          <!-- <div class="pager_container_new"> -->
            <!-- <div style="margin-left: 15px;">
              共 <span style="color: #BDA56E">{{detailTotal}}</span> 条记录
            </div>
            <div> -->
            <el-pagination
              :key="pageKey"
              layout="prev, pager, next, slot"
              :total="detailTotal"
              @current-change="handleCurrentChange"
              :page-size="page_size"
              :current-page="current_page"
            >
              <!-- slot -->
              <vCjPageSize
                @sizeChange="handleSizeChange"
                :pageSize.sync="page_size"
                :currentPage.sync="current_page"
                :pageKey.sync="pageKey">
              </vCjPageSize>
            </el-pagination>
            <!-- </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { showToast } from '../utils/util.js';
import { Table, TableColumn, Pagination } from 'element-ui';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';

export default {
  components: {
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      inputing_keyword: false,
      tab_index: 1,
      table_height: screen.availHeight - 157,
      page_size: 10,
      current_page: 1,
      pageKey: 0,
      statisticsTotal: 0,
      detailTotal: 0,
      keyword: '',
      date_range: [],
      tableData: null,
      tableData2: null,
      focusDate: false,
      totaldata: {
        cardname: '',
        price: 0,
        times: '',
        restcards: '',
        resttimes: '',
        restprice: 0,
        saledcards: '',
        saledtimes: '',
        saledprice: 0,
        usedcards: '',
        usedtimes: '',
        usedprice: 0
      }
      // changeType: null,
      // changeTypeArr: [{label: '全部变动', value: null}, {label: '次卡消费', value: 0}, {label: '已撤销', value: 1}],
      // operaType: null,
      // operaTypeArr: [{label: '全部类型', value: null}, {label: '销售', value: 0}, {label: '删除', value: 1}]
    };
  },
  created () {
    // this.page_size = Math.floor((this.table_height - 90) / 50);
    this.clickTab(1);
    this.SET_SHOW({ cardNo: '' });
  },
  updated () {
    if (this.tab_index === 1) {
      this.$nextTick(() => {
        this.$refs['table'].doLayout();
      });
    }
  },
  watch: {
    tabIndexOnceCard () {
      this.page_size = 10;
      this.clickTab(this.tabIndexOnceCard);
    },
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      this.keyword = this.cardNo;
      this.SET_SHOW({ cardNo: '' });
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    headerStyle () {
      return 'text-align:center';
    },
    headerStyleSale (column) {
      if (column.columnIndex === 0) {
        return 'text-align: left';
      } else {
        return this.tab_index === 2 && column.columnIndex === 4 ? 'text-align:right' : 'text-align:center';
      }
    },
    CellStyleSale (column) {
      if (column.columnIndex === 0) {
        return 'text-align: left';
      } else if (column.columnIndex === 4) {
        return this.tab_index === 2 ? 'text-align: right' : 'text-align: center';
      } else {
        return 'text-align: center';
      }
    },
    /**
     * 点击页码
     */
    handleCurrentChange (page) {
      this.current_page = page;
      if (this.tab_index === 1) {
        this.getTableStatisticsData();
      }
      if (this.tab_index === 2 || this.tab_index === 3) {
        this.getDetail(this.tab_index);
      }
    },
    handleSizeChange() {
      if (this.tab_index === 1) {
        this.getTableStatisticsData();
      }
      if (this.tab_index === 2 || this.tab_index === 3) {
        this.getDetail(this.tab_index);
      }
    },
    /**
     * tab点击
     */
    clickTab (index) {
      if (index === 1) {
        demo.actionLog(logList.clickTimesCardStatistics);
      }
      if (index === 2) {
        demo.actionLog(logList.clickTimesCardSaleDetail);
      }
      if (index === 3) {
        demo.actionLog(logList.clickTimesCardUseDetail);
      }
      this.tab_index = index;
      this.keyword = '';
      var new_date = new Date().format('yyyy-MM-dd');
      let date_from = new_date;
      this.date_range = [
        date_from,
        new_date
      ];
      this.current_page = 1;
      // this.changeType = null;
      // this.operaType = null;
      if (index === 1) {
        this.initTable(124);
        this.getTableStatisticsData();
      }
      if (index === 2 || index === 3) {
        this.initDataPicker();
        this.initTable(183);
        this.getDetail(index);
      }
    },
    initDataPicker () {
      let today = new Date();
      let date_to = today.format('yyyy-MM-dd');
      let date_from = new Date(today.setDate(today.getDate() - 1)).format('yyyy-MM-dd');
      this.date_range.push(date_from);
      this.date_range.push(date_to);
    },
    /**
     * 获取次卡统计表格数据
     */
    getTableStatisticsData () {
      this.loading = true;
      const param = {
        phone: this.sysUid,
        sysSid: this.sysSid,
        systemName: $config.systemName,
        pageNumber: this.current_page,
        pageSize: this.page_size
      };
      // this.reportFormLog(_.cloneDeep(param), '次卡统计查询');
      demo.$http
        .post(this.$rest.timesCardStatistics, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          const res = rs.data;
          console.log('==次卡统计结果', res);
          if (res.code === '0') {
            this.tableData = res.data.datalist.list;
            this.statisticsTotal = res.data.datalist.total;
            this.totaldata = res.data.totaldata;
            return;
          }
          showToast(this, res.msg);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },

    /**
     * 获取次卡销售明细/次卡使用明细
     */
    getDetail (index, logFlg) {
      this.tableData2 = [];
      this.dateReverse();
      this.loading = true;
      const param = {
        phone: this.sysUid,
        sysSid: this.sysSid,
        systemName: $config.systemName,
        searchStr: this.keyword,
        from: this.date_range && this.date_range[0] ? this.date_range[0] : '',
        to: this.date_range && this.date_range[1] ? this.date_range[1] : '',
        pageNumber: this.current_page,
        pageSize: this.page_size
        // useType: index === 2 ? this.operaType : this.changeType
      };
      // console.log('==明细参数', param);
      let url;
      if (index === 2) {
        url = this.$rest.timesCardSalesDetails;
      }
      if (index === 3) {
        url = this.$rest.timesCardUseDetails;
      }
      // if (logFlg) {
      //   this.reportFormLog(_.cloneDeep(param), index === 2 ? '次卡销售明细查询' : '次卡使用明细查询');
      // }
      demo.$http
        .post(url, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          const res = rs.data;
          console.log('==次卡明细结果', res);
          if (res.code === '0') {
            this.tableData2 = res.data.list.map(e => {
              e.createtime = e.createtime && e.createtime.length > 10
                ? e.createtime.substr(0, 10) : e.createtime;
              return e;
            });
            this.detailTotal = res.data.total;
            return;
          }
          showToast(this, res.msg);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },

    /**
    * 初始化表格高度
    */
    initTable (value) {
      this.table_height = document.body.clientHeight - value;
      if (this.tab_index !== 1) {
        this.$nextTick(() => {
          this.$refs['tableData2Tab'].doLayout();
        });
      }
    },
    /**
     * 总计
     */
    getSummaries ({ columns }) {
      const sums = [];
      columns.forEach((column, index) => {
        switch (column.property) {
          case 'cardname':
            sums[index] = this.parseNaN(this.totaldata.cardname);
            break;
          case 'remark1':
            sums[index] = this.parseNaN(this.totaldata.price, 2);
            break;
          case 'times':
            sums[index] = this.parseNaN(this.totaldata.times);
            break;
          case 'restcards':
            sums[index] = this.parseNaN(this.totaldata.restcards);
            break;
          case 'resttimes':
            sums[index] = this.parseNaN(this.totaldata.resttimes);
            break;
          case 'remark2':
            sums[index] = this.parseNaN(this.totaldata.restprice, 2);
            break;
          case 'usedcards':
            sums[index] = this.parseNaN(this.totaldata.usedcards);
            break;
          case 'usedtimes':
            sums[index] = this.parseNaN(this.totaldata.usedtimes);
            break;
          case 'remark3':
            sums[index] = this.parseNaN(this.totaldata.usedprice, 2);
            break;
          case 'saledcards':
            sums[index] = this.parseNaN(this.totaldata.saledcards);
            break;
          case 'saledtimes':
            sums[index] = this.parseNaN(this.totaldata.saledtimes);
            break;
          case 'remart4':
            sums[index] = this.parseNaN(this.totaldata.saledprice, 2);
            break;
        }
      });
      return sums;
    },
    /**
     * 解析转化NaN显示'-'
     */
    parseNaN (value, fixedNum = 0) {
      return isNaN(Number(value)) ? value : Number(value).toFixed(fixedNum);
    },
    /**
     * 模糊搜索
     */
    search () {
      this.current_page = 1;
      this.getDetail(this.tab_index, true);
    },
    itemFormat(e) {
      e.createtime = e.createtime && e.createtime.length > 10 ? e.createtime.substr(0, 10) : e.createtime;
      e.saledtimes = isNaN(e.saledtimes) ? e.saledtimes : Number(e.saledtimes);
      e.price = isNaN(e.price) ? e.price : Number(e.price);
    },
    sonarData(res) {
      if (res.code === '0') {
        if (res.data.list && res.data.list.length > 0) {
          // 处理日期，只显示yyyy-MM-dd
          for (let e of res.data.list) {
            this.itemFormat(e);
          }
          this.parseResult(res.data.list);
          return;
        }
        demo.msg('warning', '暂无可导出的数据');
        return;
      }
      showToast(this, res.msg);
    },
    dateReverse() {
      if (new Date(this.date_range[0]).getTime() > new Date(this.date_range[1]).getTime() && this.date_range[1]) {
        [this.date_range[0], this.date_range[1]] = [this.date_range[1], this.date_range[0]]; // 俩元素互换位置
      }
    },
    /**
     * 导出Excel数据
     */
    exportExcelData () {
      this.dateReverse();
      const param = {
        phone: this.sysUid,
        sysSid: this.sysSid,
        systemName: $config.systemName,
        searchStr: this.keyword,
        from: this.date_range && this.date_range[0] ? this.date_range[0] : '',
        to: this.date_range && this.date_range[1] ? this.date_range[1] : '',
        pageNumber: 1,
        pageSize: 65535
      };
      if (this.tab_index === 2) {
        demo.actionLog(logList.clickTimesCardSaleDetailExportExcel);
      } else {
        demo.actionLog(logList.clickTimesCardUseDetailExportExcel);
      }
      demo.$http
        .post(this.tab_index === 2 ? this.$rest.timesCardSalesDetails : this.$rest.timesCardUseDetails, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          const res = rs.data;
          console.log('导出Excel结果:', res);
          this.sonarData(res);
        });
    },
    /**
     * 解析Excel结果
     */
    parseResult (list) {
      let field_mapping = {
        会员: 'vipname',
        手机号: 'mobile',
        次卡名称: 'cardname'
      };
      if (this.tab_index === 2) {
        field_mapping.可用次数 = 'saledtimes';
        field_mapping.购买金额 = 'price';
        field_mapping.购买时间 = 'createtime';
        // field_mapping.操作类型 = 'useType';
      }
      if (this.tab_index === 3) {
        field_mapping.消费时间 = 'createtime';
        field_mapping.当前使用次数 = 'usedtimes';
        field_mapping.剩余次数 = 'resttimes';
      }
      field_mapping.到期时间 = 'expiredtime';
      this.$makeExcel(list, field_mapping, (this.tab_index === 2 ? '次卡销售明细' : '次卡使用明细') + new Date().format('yyyyMMddhhmmss'));
    }
  },

  computed: mapState({
    sysUid: (state) => state.show.sys_uid,
    sysSid: (state) => state.show.sys_sid,
    delayedTime: state => state.show.delayedTime,
    tabIndexOnceCard: (state) => state.show.tabIndexOnceCard,
    cardNo: state => state.show.cardNo
  })
};
</script>

<style lang='less'>
.tableBox_card td{
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  font-weight: 400;
  color: @themeFontColor;
}
.el-table__footer-wrapper td{
  margin-top: -1px;
  font-weight: bold !important;
  color: @themeFontColor !important;
}

.once_card_content{
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #F5F8FB;
}

.tab_container{
  display: flex;
  align-items: center;
  height: 40px;
  border: 1px solid #D1B989;
  border-radius: 4px;
}
.tab{
  width: 150px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  color: #B39959;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
}
.bottom{
  width: 100%;
  flex: 1;
  background: #F5F8FB;
  padding: 10px;
  display: flex;
  flex-direction: column;
}

.pager_container{
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 10px;
}
.pc_rep11 {
  margin-top: 5px;
  overflow: hidden;
  .search_input_box{
    input::-webkit-input-placeholder {
      /* WebKit browsers */
      color: @text;
    }
  }
}
.pc_rep14 {
  cursor: pointer;
  width: 100px;
  height: 44px;
  margin-left: 10px;
  line-height: 44px;
  font-weight: 700;
  border-radius: 22px;
  text-align: center;
  color: #FFFFFF;
  font-size: 18px;
  background: @themeBackGroundColor;
  float: left;
}
</style>
