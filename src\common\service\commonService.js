import dao from '../dao/dao';

const commonService = {
  /**
   * 生成guid
   * @returns
   */
  guid: function () {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      var r = Math.random() * 16 | 0;
      var v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  },

  /**
   * 退出时：获取当天库存有变动的商品库存，添加actionLog
   * @param {*} onSuccess
   * @param {*} onFail
   */
  actionLogsGoodsStock: function (onSuccess, onFail) {
    // 获取当天库存有变动的商品
    dao.exec(sqlApi.getGoodsByStockChanged, suc => {
      var result = {};
      result.goods = demo.t2json(suc);

      // 获取 sales、sale_items、purchases、purchase_items、inventories、inventory_items 的条数
      dao.exec(sqlApi.getSalePurInvCount, suc1 => {
        Object.assign(result, demo.t2json(suc1)[0]);
        onSuccess(result);
      }, onFail);
    }, onFail);
  },

  refreshClerks: () => {
    dao.exec(sqlApi.getClerks, res => {
      window.$clerks = res;
    });
  }
};

window.commonService = commonService;
export default commonService;
