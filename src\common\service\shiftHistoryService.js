import dao from '../dao/dao';
import shiftSql from '../dao/shiftSql';
import stringUtils, { StringBuilder } from "../stringUtils";
const shiftHistoryService = {
  /**
   * 交接班报表
   * @param {*} data {"uid": 15032, "beginDate": "2020-10-01", "endDate": "2020-10-31", "limit": 10, "offset": 0}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  get: function (data, onSuccess, onFail) {
    if (data != null) {
      data.wheres = 'where 1=1 ';
      if (data.uid != null && data.uid.toString().trim() !== '') {
        data.wheres += 'and uid=' + data.uid + ' ';
      }
      if (data.beginDate != null && data.beginDate.toString().trim() !== '') {
        data.wheres += "and strftime('%Y-%m-%d', begin_date) >= '" + data.beginDate + "' ";
      }
      if (data.endDate != null && data.endDate.toString().trim() !== '') {
        data.wheres += "and strftime('%Y-%m-%d', begin_date) <= '" + data.endDate + "' ";
      }
    }

    var result = {};
    dao.exec(
      shiftSql.shiftHistoryReports.format(data),
      res1 => {
        result.datas = demo.t2json(res1);
        dao.exec(
          shiftSql.shiftHistoryReportsTotal.format(data),
          res2 => {
            result.total = demo.t2json(res2)[0];

            dao.exec(
              shiftSql.shiftHistoryReportsCount.format(data),
              res3 => {
                result.count = demo.t2json(res3)[0]['totalCount'];

                onSuccess(JSON.stringify(result));
              },
              onFail
            );
          },
          onFail
        );
      },
      onFail
    );
  },
  /**
   * 交接班报表
   * @param {*} data {"uid": 1, "opt":"search || import" ,"showDelete":true, "beginDate": "2020-10-01", "endDate": "2020-10-31", "limit": 10, "offset": 0}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  query(data, onSuccess, onFail) {
    const errStr = this.importCheck(data);
    if (!demo.isNullOrTrimEmpty(errStr)) {
      onFail(errStr);
      return;
    }
    const where = new StringBuilder('where 1=1');
    const deleteWhere = ` and (info1 is null or (info1 not like '%"isDel":1%' and info1 not like '%"isDel": 1%'))`;
    if (!demo.isNullOrTrimEmpty(data.showDelete) && !data.showDelete) {
      where.append(deleteWhere);
    }
    if (!demo.isNullOrTrimEmpty(data.uid)) {
      where.append(` and uid=${data.uid}`);
    }
    if (!demo.isNullOrTrimEmpty(data.beginDate)) {
      where.append(` and strftime('%Y-%m-%d', begin_date) >= '${data.beginDate}'`);
    }
    if (!demo.isNullOrTrimEmpty(data.endDate)) {
      where.append(` and strftime('%Y-%m-%d', begin_date) <= '${data.endDate}'`);
    }
    data.wheres = where.toString();

    Promise.all([
      this.shiftHistoryReports(data),
      this.shiftHistoryReportsTotal(`${where.toString()} ${deleteWhere}`),
      this.shiftHistoryReportsCount(data.wheres),
      this.shiftHistoryReportsCount(`${where.toString()} ${deleteWhere}`)
      // this.shiftHistoryScanAmtReportsTotal(`${where.toString()} ${deleteWhere}`)
    ]).then(res => {
      const result = {};
      result.datas = res[0];
      result.total = res[1][0];
      result.deleteCount = res[2][0]['totalCount'];
      result.excludeDeleteCount = res[3][0]['totalCount'];
      // result.total.scanAmtTotal = res[4];
      onSuccess(result);
    }).catch(onFail);
  },
  // 交接班导出表格前检查
  importCheck(data) {
    if (demo.isNullOrTrimEmpty(data.opt) || data.opt !== 'import') {
      return '';
    }
    // 导出表格31天限制
    if (demo.isNullOrTrimEmpty(data.beginDate) || demo.isNullOrTrimEmpty(data.endDate)) {
      return '开始时间或结束时间不能为空';
    }
    if ((new Date(data.endDate) - new Date(data.beginDate)) / (1000 * 60 * 60 * 24) > 31) {
      return '日期间隔不能超过31天';
    }
  },
  shiftHistoryReports(data) {
    return new Promise((resolve, reject) => {
      const fill = `（补）`;
      dao.asyncExec(shiftSql.shiftHistoryReports.format(data)).then(res => {
        res.forEach(item => {
          if (!demo.isNullOrTrimEmpty(item.info1)) {
            const info1 = JSON.parse(item.info1);
            // 是否是新增交接班
            if (info1.flag === 'insert') {
              item.beginDate += fill;
              item.endDate += fill;
            }
          }
        });
        resolve(res);
      }).catch(reject);
    });
  },
  shiftHistoryReportsTotal(wheres) {
    return new Promise((resolve, reject) => {
      dao.asyncExec(shiftSql.shiftHistoryReportsTotal.format({ wheres })).then(resolve).catch(reject);
    });
  },
  shiftHistoryReportsCount(wheres) {
    return new Promise((resolve, reject) => {
      dao.asyncExec(shiftSql.shiftHistoryReportsCount.format({ wheres })).then(resolve).catch(reject);
    });
  },
  shiftHistoryScanAmtReportsTotal(wheres) {
    return new Promise((resolve, reject) => {
      const select = `info1`
      dao.asyncExec(shiftSql.selectShiftHistory.format({ wheres, select })).then(res => {
        // 扫码付金额
        let scanAmt = 0;
        res.forEach(item => {
          if (!demo.isNullOrTrimEmpty(item.info1)) {
            const info1 = JSON.parse(item.info1);
            scanAmt += Number(demo.isNullOrTrimEmpty(info1.scanAmt) ? 0 : info1.scanAmt);
          }
        });
        resolve(scanAmt);
      }).catch(reject);
    });
  },
  // 交接班记录删除/恢复  {"isDel": 0, "id":1}
  deleteOrReply(params) {
    return new Promise(async (resolve, reject) => {
      let info1 = demo.t2json(await dao.asyncExec(shiftSql.selectShiftHistory.format({ wheres: `where id = ${params.id}`, select: `info1` })))[0].info1;
      if (demo.isNullOrTrimEmpty(info1)) {
        info1 = `{}`;
      }
      info1 = JSON.parse(info1);
      info1.isDel = +params.isDel;
      info1 = JSON.stringify(info1);
      params.info1 = info1;
      params.uid = demo.$store.state.show.loginInfo.uid;
      dao.asyncExec(shiftSql.shiftHistoryReportsDeleteOrReply.format(params)).then(resolve).catch(reject);
    });
  },
  /**
   * 交接班-补班时获取结束时间
   * http://yapi.trechina.cn/project/1152/interface/api/55703
   * @param {*} params {shiftHistoryId: 1}
   * @returns
   */
  getNextById(params) {
    return new Promise((resolve, reject) => {
      const now = new Date().format('yyyy-MM-dd hh:mm:ss');
      const shiftHistoryId = +params.shiftHistoryId;
      if (shiftHistoryId > 0) {
        dao.asyncExec(shiftSql.getNextShiftById.format(shiftHistoryId)).then(res => {
          if (res.length === 0) {
            resolve(now);
            return;
          }

          const shift = res[0];
          const beginDate = new Date(shift.beginDate);
          if (stringUtils.isValidDate(beginDate)) {
            resolve(beginDate.format('yyyy-MM-dd hh:mm:ss'));
            return;
          }

          resolve(now);
        }).catch(reject);
      } else {
        resolve(now);
      }
    });
  },
  /**
   * 更新本地交接班记录
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateChangeShiftsRecord: function (data, onSuccess, onFail) {
    dao.exec(shiftSql.changeAddShiftHistories.format(data), onSuccess, onFail);
  },

  /**
   * 插入本地交接班记录
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  insertShiftHistories: function (data, onSuccess, onFail) {
    dao.exec(shiftSql.insertShiftHistories.format(data), onSuccess, onFail);
  },
  /**
   * 交接班-新增交接班、交接班、补班、校准
   * http://yapi.trechina.cn/project/1152/interface/api/55472
   * const params = {action: 1, uid: 1, shiftHistoryId: null, start: '2023-03-21 08:00:00', end: '2023-03-21 17:00:00'}
   * @param {*} params action => 1：新增交接班、2：交接班、3：补班、4：校准
   */
  save(params) {
    return new Promise((resolve, reject) => {
      // debugger
      const date = new Date();
      const now = date.format('yyyy-MM-dd hh:mm:ss.S');
      const action = +params.action;

      let shiftHistory;
      let employee;
      this.saveCheck(params, date)
        .then(res => {
          employee = res.employee;
          shiftHistory = res.shift;
          return this.statistics(params);
        })
        .then(statistics => {
          return this.saveDo(params, employee, shiftHistory, statistics, action, now);
        })
        .then(resolve)
        .catch(reject);
    });
  },
  saveCheck(params, date) {
    return new Promise(async (resolve, reject) => {
      params.action = +params.action;
      params.uid = +params.uid;
      params.shiftHistoryId = +params.shiftHistoryId;

      const actionBool = /^[1-4]$/.test(params.action);
      if (!actionBool) {
        reject(new Error('action传参错误'));
        return;
      }
      const uidBool = params.action === 1 && demo.isNullOrTrimEmpty(params.uid);
      if (uidBool) {
        reject(new Error('uid传参错误'));
        return;
      }
      const shiftIdBool = params.action > 1 && !(params.shiftHistoryId > 0);
      if (shiftIdBool) {
        reject(new Error('shiftHistoryId传参错误'));
        return;
      }

      const res = {};
      if (params.action > 1) {
        const shifts = await dao.asyncExec(shiftSql.getShiftById.format(params.shiftHistoryId));
        if (shifts.length === 0) {
          reject(new Error('shiftHistoryId不存在'));
          return;
        }
        const shift = shifts[0];
        res.shift = shift;
        params.uid = +shift.uid;
        params.start = new Date(shift.beginDate).format('yyyy-MM-dd hh:mm:ss');
        if (params.action === 4) {
          params.end = new Date(shift.endDate).format('yyyy-MM-dd hh:mm:ss');
        }
      }

      const startDate = new Date(params.start);
      const endDate = new Date(params.end);
      const dateBool = stringUtils.isValidDate(startDate) && stringUtils.isValidDate(endDate) && startDate <= endDate && endDate <= date;
      if (params.action < 4 && !dateBool) {
        reject(new Error('日期传参错误'));
        return;
      }

      const employee = _.filter($clerks, i => +i.uid === params.uid)[0];
      if (employee === undefined) {
        reject(new Error('员工不存在'));
        return;
      }
      res.employee = employee;

      resolve(res);
    });
  },
  saveDo(params, employee, shiftHistory, statistics, action, now) {
    return new Promise((resolve, reject) => {
      const uid = demo.$store.state.show.loginInfo.uid;

      let remark = JSON.stringify(statistics);
      if (action === 3 || (action === 4 && (shiftHistory.remark + '').includes('&@（补）'))) {
        remark += '&@（补）';
      }

      const info1 = {};
      const info2 = [];
      if (action === 1) {
        info1.flag = 'insert';
        info1.isDel = 0;
      } else {
        if (!demo.isNullOrTrimEmpty(shiftHistory.info1)) {
          const info1Obj = JSON.parse(shiftHistory.info1);
          Object.assign(info1, info1Obj);
        }

        if (!demo.isNullOrTrimEmpty(shiftHistory.info2)) {
          const info2Array = JSON.parse(shiftHistory.info2);
          if (Array.isArray(info2Array)) {
            info2.push(...info2Array);
          }
        }
      }

      info1.action = action;
      // info1.scanAmt = statistics[4].money;
      if (!info1.hasOwnProperty('isDel')) {
        info1.isDel = 0;
      }

      const shift = {
        // [销售总额,应收现金，会员充值，支付统计]
        uid: +params.uid,
        employeeNumber: demo.isNullOrTrimEmpty(employee.employee_number || employee.employeeNumber)
          ? null : (employee.employee_number || employee.employeeNumber),
        name: employee.name,
        beginDate: params.start,
        endDate: params.end,
        // 销售统计
        salesAmt: statistics[0].money,
        // 支付统计
        payAmt: statistics[3].money,
        // 会员统计
        vipCharge: statistics[1].money,
        // 现金统计
        cashAmt: statistics[2].money,
        remark,
        info1: JSON.stringify(info1),
        reviseBy: uid,
        reviseAt: now
      };

      let sql;
      if (action === 1) {
        shift.createBy = uid;
        shift.createAt = now;
        shift.fingerprint = commonService.guid();
        sql = shiftSql.shiftHistoryInsert;
      } else {
        shift.id = shiftHistory.id;
        shift.createBy = shiftHistory.createBy;
        shift.createAt = shiftHistory.createAt;
        shift.fingerprint = shiftHistory.fingerprint;
        sql = shiftSql.shiftHistoryUpdate;
      }
      info2.push(shift);
      shift.info2 = JSON.stringify(info2);
      dao.exec(sql.format(demo.sqlConversion(shift)), resolve, reject);
    });
  },
  /**
   * 交接班-交接班、校准、补班统计
   * http://yapi.trechina.cn/project/1152/interface/api/55472
   * const params = {uid: 1, start: '2023-03-21 08:00:00', end: '2023-03-21 17:00:00'}
   * @param {*} params
   */
  statistics: function (params) {
    return new Promise((resolve, reject) => {
      if (demo.isNullOrTrimEmpty(params.uid)) {
        reject(new Error('uid传参错误'));
        return;
      }
      const date = new Date();
      const startDate = new Date(params.start);
      const endDate = new Date(params.end);
      const dateBool = stringUtils.isValidDate(startDate) && stringUtils.isValidDate(endDate) && startDate <= endDate && endDate <= date;
      if (!dateBool) {
        reject(new Error('日期传参错误'));
        return;
      }
      this.localStatistics(params).then(res => resolve(res)).catch(err => reject(err))
    });
  },
  /**
   *
   * @param {*} params
   * @returns [销售总额,应收现金，会员充值，支付统计]
   */
  localStatistics(params) {
    return new Promise(async (resolve, reject) => {
      try {
        const sales = await dao.asyncExec(shiftSql.getByShiftSales.format(params));
        // 组合支付拆分为多种支付方式
        const mixedPaySales = await this.localBlendsStatistics(_.filter(sales, ['accountId', 99]).map(p => p.fingerprint));
        const vipChangeMoney = await this.getVipChangeMoney(params);
        // 销售总额
        const salessMoneyCount = this.getSalessMoneyCount(sales, vipChangeMoney);
        // 组合支付拆分去除组合支付
        const splitPaySales = _.filter([...sales, ...mixedPaySales], sale => {
          return sale.accountId !== 99
        });
        // 应收现金
        const cashPay = this.getCashPay(splitPaySales, vipChangeMoney);
        // 会员充值
        const vipPay = this.getVipPay(vipChangeMoney);
        // 支付统计
        const statisticsPay = this.getStatisticsPay(splitPaySales, vipChangeMoney);
        resolve([salessMoneyCount, vipPay, cashPay, statisticsPay])
      } catch (e) {
        reject(e)
      }
    })
  },
  // 销售总额
  getSalessMoneyCount(sales, vipChangeList) {
    // 销售总额=商品销售+（-商品退款）
    let saleTotalMoney = _.sumBy(sales, 'payAmt');
    // 笔数相当于数据条数-- 销售单
    let saleTotalCount = sales.length;
    // 商品销售=所有支付方式的订单实收金额汇总以及订单笔数汇总
    // （包含组合支付，不包含退款）
    // inOut 1：2   1：销售 2:退款
    const inSales = _.filter(sales, ['inOut', 1]);
    const payLocalsMoney = _.sumBy(inSales, 'payAmt');
    const payLocalsCount = inSales.length;
    const outSales = _.filter(sales, ['inOut', 2]);
    const backLocalsMoney = _.sumBy(outSales, 'payAmt');
    const backLocalsCount = outSales.length;

    const cardList = _.filter(vipChangeList, ['originId', 5])
    const cardMoney = Math.abs(_.sumBy(cardList, 'money'));
    const cardListCount = cardList.length;
    saleTotalMoney = saleTotalMoney + cardMoney;
    saleTotalCount = saleTotalCount + cardListCount;

    return {
      title: '销售总额',
      prefix: '总',
      money: saleTotalMoney.toFixed(2),
      count: saleTotalCount,
      total: `${saleTotalMoney.toFixed(2) + '元（' + saleTotalCount + '笔'}）`,
      desc: [{
        title: '商品销售',
        money: payLocalsMoney.toFixed(2),
        count: payLocalsCount,
        total: `${payLocalsMoney.toFixed(2) + '元（' + payLocalsCount + '笔'}）`
      }, {
        title: '商品退款',
        money: backLocalsMoney.toFixed(2),
        count: backLocalsCount,
        total: `${backLocalsMoney.toFixed(2) + '元（' + backLocalsCount + '笔'}）`
      },
      {
        title: '次卡销售',
        money: cardMoney.toFixed(2),
        count: cardListCount,
        total: `${cardMoney.toFixed(2) + '元（' + cardListCount + '笔'}）`
      }
      ]
      // .filter(d => d.count)
    };
  },

  getCashPay(mixedPaySales, vipchangeMoney) {
    return this.getAccountPay(mixedPaySales, vipchangeMoney, '现', 1)
  },
  // 收款方式
  getAccountPay(mixedPaySales, vipchangeMoney, prefix, accountId = 1) {
    // 应收现金=商品销售+（-商品退款）+会员卡充值
    const loaclSales = _.filter(mixedPaySales, ['accountId', accountId]);
    const cloudVipSales = _.filter(vipchangeMoney, vip => {
      return (vip.type === 1 || vip.type === 4) && vip.acctId === accountId
    }).map(info => {
      return {
        payAmt: info.money,
        inOut: info.type === 4 ? 2 : 1,
        accountId: info.acctId
      }
    });
    const cardList = _.filter(vipchangeMoney, vip => {
      return vip.type === 2 && vip.acctId === accountId && vip.originId === 5
    });
    const cardMoney = Math.abs(_.sumBy(cardList, 'money'));
    const cardListCount = cardList.length;
    const allSale = [...loaclSales, ...cloudVipSales];
    let cashSaleMoney = _.sumBy(allSale, 'payAmt');
    const payLocals = _.filter(loaclSales, ['inOut', 1]);
    const payLocalsMoney = _.sumBy(payLocals, 'payAmt');
    const payLocalsCount = payLocals.length;
    const backLocals = _.filter(loaclSales, ['inOut', 2])
    const backLocalsMoney = _.sumBy(backLocals, 'payAmt');
    const backLocalsCount = backLocals.length;
    const cloudVipSalesMoney = _.sumBy(cloudVipSales, 'payAmt');
    const cloudVipSalesCount = cloudVipSales.length;
    const allSaleCount = allSale.length + cardListCount
    cashSaleMoney = cashSaleMoney + cardMoney;
    return {
      title: this.statisticsAccounts[accountId],
      prefix,
      money: Number(cashSaleMoney).toFixed(2),
      count: allSaleCount,
      total: `${Number(cashSaleMoney).toFixed(2)}元（${allSaleCount}笔）`,
      desc: [
        {
          title: '商品销售',
          money: payLocalsMoney.toFixed(2),
          count: payLocalsCount,
          total: `${payLocalsMoney.toFixed(2) + '元（' + payLocalsCount + '笔'}）`
        }, {
          title: '商品退款',
          money: backLocalsMoney.toFixed(2),
          count: backLocalsCount,
          total: `${backLocalsMoney.toFixed(2) + '元（' + backLocalsCount + '笔'}）`
        },
        {
          title: accountId !== 6 ? '会员卡充值' : '充值撤销',
          money: cloudVipSalesMoney.toFixed(2),
          count: cloudVipSalesCount,
          total: `${cloudVipSalesMoney.toFixed(2) + '元（' + cloudVipSalesCount + '笔'}）`
        },
        {
          title: '次卡销售',
          money: cardMoney.toFixed(2),
          count: cardListCount,
          total: `${cardMoney.toFixed(2) + '元（' + cardListCount + '笔'}）`
        }
      ]
      // .filter(d => d.count)
    };
  },
  // 会员充值
  getVipPay(vipchangeMoney) {
    // 会员充值=会员卡充值+会员退款充值+（-充值撤销）
    const vipList = _.filter(vipchangeMoney, vip => {
      return vip.type !== 2;
    });
    // 会员卡充值
    const payVips = _.filter(vipList, ['type', 1]);
    const payVipsMoney = _.sumBy(payVips, 'money');
    const payVipsLength = payVips.length;
    // 充值撤销
    const backVips = _.filter(vipList, ['type', 4]);
    const backVipsMoney = _.sumBy(backVips, 'money');
    const backVipsLength = backVips.length;
    // 退款到会员卡['type', 3]'originId':4
    const backVipLocals = _.filter(vipList, {'type': 3, 'originId': 4, 'acctId': 6});
    const backVipLocalsMoney = _.sumBy(backVipLocals, 'money');
    const backVipLocalsLength = backVipLocals.length;
    const allvip = [...payVips, ...backVips, ...backVipLocals];
    const vipTotalMoney = _.sumBy(allvip, 'money');
    return {
      title: '会员充值',
      prefix: '会',
      money: vipTotalMoney.toFixed(2),
      count: allvip.length,
      total: `${vipTotalMoney.toFixed(2) + '元（' + allvip.length + '笔'}）`,
      desc: [
        {
          payType: '会员卡充值',
          money: payVipsMoney.toFixed(2),
          count: payVipsLength
        }, {
          payType: '充值撤销',
          money: backVipsMoney.toFixed(2),
          count: backVipsLength
        },
        {
          payType: '退款到会员卡',
          money: Math.abs(backVipLocalsMoney).toFixed(2),
          count: backVipLocalsLength
        }
      ]
      // .filter(d => d.count)
    }
  },
  // 支付统计
  getStatisticsPay(loaclSales, vipChangeMoney) {
    const cloudVipSales = _.filter(vipChangeMoney, vip => {
      return vip.type === 1 || vip.type === 4
    }).map(info => {
      return {
        payAmt: info.money,
        inOut: info.type === 4 ? 2 : 1,
        accountId: info.acctId
      }
    })
    const cardList = _.filter(vipChangeMoney, vip => {
      return vip.originId === 5
    }).map(info => {
      return {
        payAmt: Math.abs(info.money),
        inOut: 1,
        accountId: info.acctId
      }
    })
    const cardMoney = _.sumBy(cardList, 'payAmt');
    const cardCount = cardList.length;
    const allSales = [...loaclSales, ...cloudVipSales];
    let allMoney = (_.sumBy(allSales, 'payAmt') + cardMoney).toFixed(2);
    let allCount = cardCount + allSales.length;
    // 支付统计=应收现金+扫码付支付宝+扫码付微信+线下POS+线下支付宝+线下微信+会员卡支付
    const StatisticsPay =
     {
       title: '支付统计',
       prefix: '支',
       money: allMoney,
       count: allCount,
       total: `${allMoney + '元（' + allCount + '笔'}）`,
       desc: [
         this.getAccountPay(loaclSales, vipChangeMoney, '', 1),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 8),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 7),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 3),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 5),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 4),
         this.getAccountPay(loaclSales, vipChangeMoney, '', 6)
       ]
     }
    return StatisticsPay;
  },

  // 获取交接班数据_会员充值
  getVipChangeMoney(params) {
    if (demo.isNullOrTrimEmpty(params.uid)) {
      params.uid = demo.$store.state.show.loginInfo.uid;
    }
    params.systemName = $config.systemName;
    params.phone = demo.$store.state.show.sys_uid;
    params.sysSid = demo.$store.state.show.sys_sid;
    demo.$store.commit('SET_SHOW', { loadingMsg: '正在获取交接班数据，请稍后……', showAddOrderLoding: true });
    return new Promise((resolve) => {
      demo.$http.post(demo.$rest.getVipChangeMoney, params, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        res.data.code === "0" ? resolve(res.data.data) : resolve([]);
        demo.$store.commit('SET_SHOW', { showAddOrderLoding: false });
      }).catch(() => {
        demo.$store.commit('SET_SHOW', { showAddOrderLoding: false });
        resolve([]);
      });
      demo.$store.commit('SET_SHOW', { showAddOrderLoding: false });
    })
  },
  // 获取组合支付相关方式
  localBlendsStatistics(saleFingerprints) {
    return new Promise((resolve, reject) => {
      if (saleFingerprints.length === 0) {
        resolve([]);
        return;
      }
      const fingerprints = `'${saleFingerprints.join('\',\'')}'`;
      dao.asyncExec(shiftSql.getBlendsByShift.format(fingerprints))
        .then(resolve)
        .catch(reject);
    });
  },
  /**
   *  获取交接班数据
   * @param {*} data {fingerprint}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getShiftOneHistories(data, onSuccess, onFail) {
    dao.exec(shiftSql.getShiftOneHistories.format(data), res => {
      onSuccess(res);
    }, onFail);
  },
  getShiftHistoriesEndDate(data, onSuccess, onFail) {
    dao.exec(shiftSql.getShiftHistoryReportsEndDate.format(data), res => {
      if (res.length > 0) {
        const { endDate } = res[0];
        onSuccess([{endDate: endDate + '（补）'}]);
      } else {
        onSuccess([]);
      }
    }, onFail);
  },
  statisticsAccounts: {
    1: '应收现金',
    3: '线下-POS',
    4: '线下-微信',
    5: '线下-支付宝',
    6: '会员卡支付',
    7: '扫码付-微信',
    8: '扫码付-支付宝',
    9: '扫码点餐（小程序付款）',
    99: '组合支付'
  },
  vipPutMoney: {
    1: '会员充值',
    2: '会员消费',
    3: '会员消费退款',
    4: '会员充值撤销'
  }
};
window.shiftHistoryService = shiftHistoryService;
export default shiftHistoryService;
