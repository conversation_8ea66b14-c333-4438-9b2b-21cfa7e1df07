<style lang="less" scoped>
.siz_set {
  width: 100%;height: 100%;background: #F5F8FB;font-size: 16px;color: @themeFontColor;overflow: hidden;
}
.siz_set1 {
  width: calc(100% - 20px);margin: 10px auto;height: calc(100% - 70px);
  background: #FFF;overflow: hidden;border: 1px solid #E3E6EB;
}
.siz_set11 {
  width: calc(100% - 260px);margin-left: 12px;float: left;
}
.siz_set12 {
  width: 120px;height: 40px;line-height: 40px;font-weight: bold;text-align: center;float: left;margin-right: 16px;
  margin-top: 15px;background: @themeButtonBackGroundColor;color: @themeBackGroundColor;border-radius: 18px;cursor: pointer;
}
.siz_set13 {
  overflow: hidden;margin-top: 2px;
}
.siz_set14 {
  width: 130px;text-indent: 3px;float: left;font-weight: bold;
  color: @themeFontColor;line-height: 20px;margin-top: 28px;
}
.siz_set15 {
  width: calc(100% - 130px);float: left;
}
.siz_set16 {
  float: left;height: 32px;line-height: 32px;padding-left: 12px;padding-right: 12px;background: #F2F2F2;
  margin-right: 10px;border-radius: 16px;margin-top: 10px;text-align: center;cursor: pointer;
}
.siz_set17 {
  float: left;height: 32px;line-height: 32px;background: @themeBackGroundColor;color: #FFF;
  border-radius: 16px;margin-top: 10px;width: 60px;text-align: center;cursor: pointer;
}
.siz_set18 {
  width: 100%;height: 1px;background: #E5E8EC;margin-top: 20px;
}
.siz_set19 {
  position: relative;float: left;width: 248px;border-right: 1px solid #E3E6EB;height: 100%;
}
.siz_set2 {
  min-height: 54px;overflow: hidden;text-overflow: ellipsis;font-size: 16px;cursor: pointer;
}
.siz_set21 {
  min-height: 54px;overflow: hidden;text-overflow: ellipsis;font-size: 16px;cursor: pointer;
  background: @themeBackGroundColor;color: #FFF;
}
.siz_set22 {
  float: right;
}
.siz_set22 img {
  float: left;margin-right: 12px;cursor: pointer;
}
/deep/ .el-table th, .el-table tr {
  background-color: #F5F7FA;
}
.siz_set23 {
  display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;
  float: left;margin-left: 17px;width: 160px;word-wrap:break-word;
}
.siz_set24 {
  width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;
}
.siz_set25 {
  width: 450px;height: 245px;background: #FFF;margin: 0 auto;margin-top: 300px;
}
.siz_set26 {
  line-height: 20px;width: 100%;color: @themeBackGroundColor;overflow: hidden;
}
.siz_set27 {
  float: left;margin-left: 25px;font-size: 20px;margin-top: 30px;font-weight: bold;
}
.siz_set28 {
  float: right;font-size: 25px;margin-right: 28px;margin-top: 28px;color: @themeFontColor;
}
.siz_set29 {
  width: calc(100% - 64px);margin-top: 27px;margin-left: 32px;
}
.siz_set3 {
  line-height: 14px;font-size: 14px;margin-top: 10px;margin-left: 32px;overflow: hidden;
}
.siz_set31 {
  float: left;width: 14px;height: 14px;text-align: center;line-height: 14px;color: #FFF;
  background: #F9AE1B;border-radius: 50%;font-size: 12px;text-indent: 2px;
}
.siz_set32 {
  float: left;margin-left: 5px;color: #F9AE1B;
}
.siz_set33 {
  overflow: hidden;margin-top: 27px;
}
.siz_set34 {
  width: 112px;height: 47px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: @themeBackGroundColor;
  line-height: 45px;text-align: center;font-size: 16px;float: left;margin-left: 170px;
}
.siz_set35 {
  width: 112px;height: 47px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: #FFF;
  line-height: 45px;text-align: center;font-size: 16px;background: @themeBackGroundColor;float: left;margin-left: 20px;
}
.siz_set36 {
  width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;
}
.siz_set37 {
  width: 450px;height: 335px;background: #FFF;margin: 0 auto;margin-top: 220px;border-radius: 6px;
}
.siz_set38 {
  line-height: 20px;width: 100%;color: @themeBackGroundColor;overflow: hidden;
}
.siz_set39 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;font-weight: bold;margin-top: 40px;
}
.siz_set4 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;margin-top: 50px;
}
.siz_set41 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;margin-top: 18px;
}
.siz_set42 {
  text-align: center;line-height: 18px;font-size: 18px;color: #F4725F;margin-top: 18px;
}
.siz_set43 {
  overflow: hidden;margin-top: 55px;
}
.siz_set44 {
  width: 140px;height: 50px;border: 1px solid @themeFontColor;border-radius: 4px;color: #FFF;line-height: 45px;cursor: pointer;
  text-align: center;font-size: 20px;background: @themeFontColor;float: left;margin-left: 70px;font-weight: bold;
}
.siz_set45 {
  width: 140px;height: 50px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: #FFF;line-height: 45px;cursor: pointer;
  text-align: center;font-size: 20px;background: @themeBackGroundColor;float: left;margin-left: 30px;font-weight: bold;
}
.siz_set46 {
  height: calc(100% - 200px);overflow-y: scroll;
}
.siz_set47 {
  position: fixed;bottom: 94px;width: 224px;left: 22px;height: 48px;
  border-radius: 4px;background: #F5F7FA;color: @themeFontColor;cursor: pointer;
}
.siz_set48 {
  float: left;margin-left: 63px;margin-top: 10px;
}
.siz_set49 {
  position: fixed;bottom: 36px;width: 224px;left: 22px;height: 48px;
  border-radius: 4px;background: @themeButtonBackGroundColor;color: @themeBackGroundColor;cursor: pointer;
}
.siz_set5 {
  float: left;margin-left: 10px;line-height: 48px;
}
.siz_set51 {
  float: left;font-size: 16px;width: 18px;height: 18px;margin-top: 26px;color: #FFF;
  background: #FF6159;line-height: 16px;text-align: center;border-radius: 50%;
}
.siz_set52 {
  float: left;font-size: 16px;color: #FF6159;margin-left: 7px;margin-top: 27px;line-height: 16px;
}
.siz_set53 {
  float: left;width: 100%;font-size: 16px;color: @themeFontColor;
}
</style>
<template>
  <div class="siz_set">
    <div v-if="showEditName" class="siz_set24">
      <div class="siz_set25">
        <div class="siz_set26">
          <div class="siz_set27">
            {{title}}
          </div>
          <div @click="showEditName = false" class="siz_set28">
            <i class="el-icon-close"></i>
          </div>
          <el-input
            class="siz_set29"
            placeholder="请输入内容"
            v-model.trim="name"
            maxlength="20"
            id="alert_name"
            @input="name = name.replace(/[\$\'\:\;]/g,'')"
            @focus="selectText('alert_name')"
            clearable>
          </el-input>
          <div class="siz_set3">
            <div class="siz_set31">i</div>
            <div class="siz_set32">20个字符以内，不限制字符格式，首尾不能为空格!</div>
          </div>
          <div class="siz_set33">
            <div @click="showEditName = false" class="siz_set34">
              取消
            </div>
            <div @click="subEditName()" class="siz_set35">
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showDeleteName" class="siz_set36">
      <div class="siz_set37">
        <div class="siz_set38">
          <div class="siz_set39">
            提示
          </div>
          <div class="siz_set4">
            该规格及其包含的规格值将被删除，
          </div>
          <div class="siz_set41">
            确定继续删除吗？
          </div>
          <div class="siz_set43">
            <div @click="showDeleteName = false" class="siz_set44">
              取消
            </div>
            <div @click="subDeleteName()" class="siz_set45">
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="siz_set1">
      <div class="siz_set19">
        <div style="height: 12px;"></div>
        <div class="siz_set46">
          <div v-for="(li, index) in list" :key="index" @click="left_index = index;detail()"
            :class="left_index === index ? 'siz_set21' : 'siz_set2'"
            :style="li.name.length > 10 ? 'height: 76px;' : 'height: 54px;'">
            <div class="siz_set23"
              :style="li.name.length > 10 ? 'line-height: 24px;margin-top: 15px;' : 'line-height: 54px;'">{{li.name}}</div>
            <div class="siz_set22" :style="li.name.length > 10 ? 'margin-top: 28px;' : 'margin-top: 17px;'">
              <img v-if="left_index === index && li.isEditSelf === 1" src="../../image/gg_edit_white.png" @click="editName(list[index], 'left')" />
              <img v-if="left_index === index && li.isEditSelf === 1" src="../../image/gg_delete_white.png" @click="deleteName(list[index])" />
              <img v-if="left_index !== index && li.isEditSelf === 1" src="../../image/gg_edit_grey.png" @click="editName(list[index], 'left')" />
              <img v-if="left_index !== index && li.isEditSelf === 1" src="../../image/gg_delete_grey.png" @click="deleteName(list[index])" />
            </div>
          </div>
        </div>
        <div class="siz_set47" @click="addSize()">
          <div class="siz_set48"><img src="../../image/gg_plus.png" /></div>
          <div class="siz_set5">新增规格</div>
        </div>
        <div @click="toSizeTemplate()" class="siz_set49">
          <div class="siz_set48"><img src="../../image/gg_new.png" /></div>
          <div class="siz_set5">快捷新增</div>
        </div>
      </div>
      <div class="siz_set11">
        <div style="overflow: hidden;">
          <div class="siz_set12" @click="editSize(list[left_index])">
            添加规格值
          </div>
          <div class="siz_set12" v-show="specsList.length > 0"
            @click="deleteMore()" style="background: #FFE8E8;color: #FF6159;">
            批量删除
          </div>
        </div>
        <div style="margin-top: 16px;">
          <el-table
            ref="multipleTable"
            empty-text=" "
            :data="specsList"
            stripe
            :height="table_height"
            tooltip-effect="dark"
            class="siz_set53"
            :row-style="{height: '66px'}"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column label="规格值名称" prop="name" align="left"></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <span v-if="scope.row.isEditSelf === 1" @click="editName(scope.row, 'right')" style="color: #d5aa76;cursor: pointer;">编辑</span>
                <span v-if="scope.row.isEditSelf === 0" style="color: #BBB;">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data() {
    return {
      name: '',
      show: false,
      left_index: 0,
      showEditName: false,
      table_height: document.body.clientHeight - 170,
      list: [],
      oneDetail: {},
      title: '',
      choose_list: [],
      showDeleteName: false,
      specsList: []
    };
  },
  created() {
  },
  mounted() {
    this.getSpecs();
  },
  watch: {
    showSizeColor() {
      if (this.showSizeColor === 'close') {
        this.getSpecs();
      }
    },
    specs() {
      // this.list = this.specs;
      console.log(this.list, 8999);
      var arr1 = [];
      var arr2 = [];
      for (let i in this.specs) {
        arr2 = [];
        for (let j in this.specs[i].item) {
          arr2.push({j, ...this.specs[i].item});
        }
        arr1.push({i, ...this.specs[i]});
      }
      this.list = arr1;
      // this.left_index = 0;
      this.detail();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // focus(str) {
    //   setTimeout(() => {
    //     document.getElementById(str).focus();
    //   }, 0);
    // },
    select(str) {
      setTimeout(() => {
        document.getElementById(str).select();
      }, 0);
    },
    editName(det, str) {
      this.title = str === 'left' ? '编辑规格名称' : '编辑规格值';
      this.name = det.name;
      console.log(det, 333222);
      this.showEditName = true;
      this.select('alert_name');
      this.oneDetail = det;
    },
    checkName() {
      for (var i = 0; i < this.specsList.length; i++) {
        if (this.specsList[i].name === this.name) {
          return false;
        }
      }
      return true;
    },
    subEditName() {
      if (this.name === '') {
        demo.msg('warning', '输入内容不能为空！');
        $('#alert_name').focus();
        return;
      }
      // console.log(this.specsList, 9999);
      if (this.title === '编辑规格值' && !this.checkName()) {
        demo.msg('warning', '规格值没有修改，或规格值重复，请重新输入！');
        $('#alert_name').focus();
        return;
      }
      var mid = {
        name: this.name.replace(/;/g, '').replace(/'/g, '').replace(/"/g, ''),
        id: this.oneDetail.id
      }
      var sub_data = [];
      sub_data.push(mid);
      demo.$http.post(demo.$rest.updateSpecById, sub_data).then(res => {
        console.log(res, 309303);
        if (res.data.code === 200) {
          this.getSpecs();
          demo.msg('success', this.title + '成功！');
          this.showEditName = false;
        } else {
          demo.msg('success', res.data.msg);
        }
      }).catch((error) => {
        demo.msg('warning', '编辑规格名称失败，请稍后再试！');
        console.log(error, '更新单条规格名称失败');
      });
    },
    deleteName(det) {
      this.name = det.name;
      console.log(det, 333222);
      this.showDeleteName = true;
      this.oneDetail = det;
    },
    subDeleteName() {
      var delList = [];
      delList.push(this.oneDetail.id);
      var data = {
        pset: '',
        isGroup: false,
        type: 0,
        condition: '',
        limit: 1,
        selectDel: false,
        getDel: false,
        offset: 0,
        specs: delList
      };
      console.log(data, 'goodSearch data++');
      var that = this;
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        console.log('json', json)
        if (json.length === 0) {
          that.continueDeleteName();
        } else {
          demo.msg('warning', '存在规格正被商品使用，请删除商品后重新操作。');
        }
      });
    },
    continueDeleteName() {
      this.oneDetail.isDel = 1;
      // this.oneDetail.isEditSelf = 1;
      var sub_data = [];
      sub_data.push(this.oneDetail);
      demo.$http.post(demo.$rest.updateSpecs, sub_data).then(res => {
        console.log(res, 309303);
        if (res.data.code === 200) {
          demo.msg('success', '删除规格成功！');
          this.getSpecs();
          this.left_index = 0;
          this.showDeleteName = false;
        } else {
          demo.msg('success', res.data.msg);
        }
      }).catch((error) => {
        demo.msg('warning', '删除规格失败，请稍后再试！');
        console.log(error, '删除规格失败');
      });
    },
    // 表格的checkbox，选择以后val会实时响应
    handleSelectionChange(val) {
      this.choose_list = val;
      console.log(this.choose_list, 11111);
    },
    deleteMore() {
      if (this.choose_list.length === 0) {
        demo.msg('warning', '请先选择要删除的规格值！');
        return;
      }
      // 是否走更新接口
      var toPost = false;
      var delList = [];
      for (var i = 0; i < this.choose_list.length; i++) {
        // 如果有删除权限，isEditSelf === 1，赋值isDel === 1
        // 如果没删除权限，isEditSelf === 0，赋值isDel === 0
        delList.push(this.choose_list[i].id);
        if (this.choose_list[i].isEditSelf === 1) {
          toPost = true;
          this.choose_list[i].isDel = 1;
        }
      }
      // 如果没有可以删除的数据，则不走请求接口
      if (toPost === false) {
        demo.msg('warning', '所选批量删除规格均不可删除');
        return;
      }
      this.searchIfDel(delList);
    },
    searchIfDel(delList) {
      var data = {
        pset: '',
        isGroup: false,
        type: 0,
        condition: '',
        limit: 1,
        selectDel: false,
        getDel: false,
        offset: 0,
        specs: delList
      };
      console.log(data, 'goodSearch data++');
      var that = this;
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        console.log('json', json)
        if (json.length === 0) {
          that.continueDeleteMore();
        } else {
          demo.msg('warning', '存在规格正被商品使用，请删除商品后重新操作。');
        }
      });
    },
    continueDeleteMore() {
      demo.$http.post(demo.$rest.updateSpecs, this.choose_list).then(res => {
        if (res.data.code === 200) {
          demo.msg('success', '批量删除规格成功！');
          this.getSpecs();
          this.showDeleteName = false;
        } else {
          demo.msg('success', res.data.msg);
        }
      }).catch((error) => {
        demo.msg('warning', '批量删除规格失败，请稍后再试！');
        console.log(error, '批量删除规格失败');
      });
    },
    detail() {
      var arr = [];
      for (let i in this.list[this.left_index].item) {
        arr.push({i, ...this.list[this.left_index].item[i]});
      }
      // console.log(this.list, 111);
      // console.log(this.left_index, 222);
      // this.specsList = this.list[this.left_index].item;
      // console.log(this.specsList, 13111);
      this.specsList = arr;
    },
    getSpecs() {
      this.$store.dispatch('info/getSpec');
    },
    toSizeTemplate() {
      this.SET_SHOW({isSizeSetting: false});
      this.SET_SHOW({isSizeTemplate: true});
    },
    addSize() {
      this.SET_SHOW({showSizeColor: 'new'});
    },
    editSize(n) {
      console.log(n, 999888);
      this.SET_SHOW({sizeColorDetail: n});
      this.SET_SHOW({showSizeColor: 'edit'});
    }
  },
  computed: {
    ...mapState({
      showSizeColor: state => state.show.showSizeColor,
      specs: state => state.info.specs
    })
  }
};
</script>
