<template>
  <!-- 积分变动明细 -->
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left_container">
        <div
          class="top_left"
          style="margin-left:0"
        >
          <div class='search_input_box'
            :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng1'"
            >
            <input
              @focus='inputing_keyword = true'
              @blur='inputing_keyword = false'
              type='text'
              placeholder='请输入姓名/手机号/卡号/刷卡'
              :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
              id='search_input_keyword_point'
              v-model='keyword'
              @input="keyword = $vipNameFormat(keyword)"
            />
            <img
              alt=""
              class='search_input_delete'
              v-show="keyword != ''"
              @click="focusInput('search_input_keyword_point')"
              src='../image/pc_clear_input.png'
            />
          </div>
          <div
            class="top_left"
            style="margin-left:0"
          >
            <div
              class="date_picker_container"
              @click="focusDate = true"
              :class="focusDate ? 'isInPutIng' : 'isInPutIng1'"
            >
              <el-date-picker
                v-model="fromDate"
                type="date"
                placeholder="开始日期"
                style="height:44px"
                @blur="focusDate = false;currentPage = 1;httpRequest('查询')"
                value-format='yyyy-MM-dd'
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485">至</div>
              <el-date-picker
                v-model="toDate"
                type="date"
                placeholder="结束日期"
                style="height:44px"
                @blur="focusDate = false;currentPage = 1;httpRequest('查询')"
                value-format='yyyy-MM-dd'
              >
              </el-date-picker>
            </div>
          </div>
          <el-select
            v-model="deposit_value"
            placeholder="全部变动"
            style="width:130px;margin-left:12px;"
            @change="currentPage = 1;httpRequest('查询')"
          >
            <el-option
              v-for="item in deposit_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="top_right">
        <div class="btn_export_excel" @click="httpRequest('导出表格', true)">导出表格</div>
      </div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        :header-cell-style="headerStyleSale"
        ref="tableDataRef"
        stripe
        :empty-text="!loading ? '暂无数据' : ' '"
      >
        <el-table-column prop="vipName" min-width="20%" label="会员姓名" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="tradeTime" min-width="25%" label="变动时间" align="center"></el-table-column>
        <el-table-column prop="user_no" min-width="25%" label="收银员" align="right"></el-table-column>
        <el-table-column prop="useType" min-width="15%" label="变动类型" align="center"></el-table-column>
        <el-table-column prop="point" min-width="15%" label="积分变动" align="center"></el-table-column>
        <el-table-column prop='remark' min-width="30%" label="变动明细" align="left" :show-overflow-tooltip="true">
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
        >
          <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="pageSize"
            :currentPage.sync="currentPage"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      keyword: '',
      inputing_keyword: false,
      deposit_type: [
        {
          value: 0,
          label: '全部变动'
        }, {
          value: 1,
          label: '注册获取'
        },
        {
          value: 2,
          label: '消费获取'
        },
        {
          value: 3,
          label: '充值获取'
        },
        {
          value: 4,
          label: '导入会员'
        },
        {
          value: 5,
          label: '抵扣现金'
        },
        {
          value: 6,
          label: '兑换礼品'
        },
        {
          value: 7,
          label: '积分修改'
        }
        // {
        //   value: 8,
        //   label: '积分清空'
        // }
      ],
      deposit_value: 0, // 寄取类型
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      tableHeight: 0,
      tableData: [],
      pageKey: 0,
      currentPage: 1,
      pageSize: 10,
      sumpoint: 0,
      total: 0
    };
  },
  created () {
    this.tableHeight = screen.availHeight - 178;
    // this.pageSize = Math.floor((this.tableHeight - 70) / 50);
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.httpRequest('查询');
    this.SET_SHOW({ cardNo: '' });
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    headerStyleSale ({ columnIndex }) {
      if (columnIndex === 2) {
        return 'text-align: right';
      }
      return columnIndex > 0 ? 'text-align:center' : 'text-align:left';
    },
    /**
     * 点击页码
     */
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage;
      this.httpRequest('查询');
    },
    handleSizeChange() {
      this.httpRequest('查询');
    },
    httpRequest(str) {
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      var url = this.$rest.pc_getVipPointUseDetail;
      let subData;
      this.loading = true;
      if (str === '查询') {
        subData = {
          'sysSid': this.sysSid,
          'fromDate': this.fromDate,
          'toDate': this.toDate,
          'phone': this.phone,
          // 'uid': this.sysUid,
          'systemName': $config.systemName,
          'currentPage': this.currentPage,
          'pageSize': this.pageSize,
          'useType': this.deposit_value,
          'keyWord': this.keyword
        };
      } else {
        subData = {
          'sysSid': this.sysSid,
          'fromDate': this.fromDate,
          'toDate': this.toDate,
          'phone': this.phone,
          // 'uid': this.sysUid,
          'systemName': $config.systemName,
          'currentPage': 1,
          'pageSize': 65535,
          'useType': this.deposit_value,
          'keyWord': this.keyword
        };
      }
      var that = this;
      demo.$http.post(url, subData, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          that.parseResult(res, str);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    /**
     * 对应sonar问题
     */
    parseResult (res, str) {
      let that = this;
      if (res.data.code === '0' && str === '查询') {
        if (res.data.data.vipPointDetails.length === 0) {
          // demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          console.log(res.data.data, 'res.data.data+++++');
          that.tableData = [];
          that.total = that.tableData.length;
          that.sumpoint = 0;
          return;
        }
        clerkService.getEmployeeNumberByUid(res.data.data.vipPointDetails, ['user'], () => { // 根据uid找到uid_oid
          console.log(res.data.data, 'res.data.data+++++');
          that.tableData = res.data.data.vipPointDetails;
          that.total = res.data.data.totals.sumtotal;
          that.sumpoint = res.data.data.totals.sumpoint;
          that.$nextTick(() => {
            that.$refs['tableDataRef'].doLayout();
          });
        });
      } else if (res.data.code === '0' && str === '导出表格') {
        demo.actionLog(logList.clickVipRechargeDetailExportExcel);
        if (res.data.data.vipPointDetails.length > 0) {
          clerkService.getEmployeeNumberByUid(res.data.data.vipPointDetails, ['user'], () => { // 根据uid找到uid_oid
            var field_mapping = {
              会员: 'vipName',
              变动时间: 'tradeTime',
              收银员: 'user_no',
              变动类型: 'useType',
              积分变动: 'point',
              变动明细: 'remark'
            };
            that.$makeExcel(res.data.data.vipPointDetails, field_mapping, '积分变动明细' + new Date().format('yyyyMMddhhmmss'));
          });
        } else {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          that.tableData = [];
        }
      } else {
        demo.msg('warning', res.data.msg);
      }
    }
  },
  watch: {
    keyword() {
      var that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.currentPage = 1;
        that.httpRequest('查询');
      }, 700);
    },
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      this.keyword = this.cardNo;
      this.SET_SHOW({ cardNo: '' });
    }
  },
  mounted() {
    setTimeout(() => {
      $('#search_input_keyword_point').focus();
    }, 0);
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    delayedTime: state => state.show.delayedTime,
    loginInfo: state => state.show.loginInfo,
    cardNo: state => state.show.cardNo
  })
};
</script>

<style lang='less' scoped>
.isInPutIng {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
/deep/ .el-table th {
  padding: 0px;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .top_left_container > .top_left > .el-select > .el-input--suffix > .el-input__inner {
  padding-left: 25px;
}
/deep/ .date_picker_container > .el-input--suffix > .el-input__inner {
  background: #FFF;
}
/deep/ .el-select .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  color: @themeFontColor;
  background: #FFF;
  font-size: 16px;
  padding-right: 0;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    color: @themeFontColor;
    padding-left: 0px;
    padding-right: 0px;
    text-align: center;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
.ppu_det {
  width: 100px;height: 44px;background: @themeBackGroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: bold;border-radius: 22px;margin-left: 10px;cursor: pointer;
}
.deposit_record_container{
  background: #F5F8FB;
  .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    .top_left_container{
      display: flex;
      align-items: center;
      .top_left{
        display: flex;
        align-items: center;
        margin-left: 50px;
        .search_input_box {
          input::-webkit-input-placeholder {
            /* WebKit browsers */
            color: @text;
          }
        }
        .date_picker_container{
          width: 300px;
          height: 44px;
          background: #FFFFFF;
          border: 1px solid #E3E6EB;
          border-radius: 50px;
          margin-left: 10px;
          display: flex;
          align-items: center;
        }
        .searchIpt_div {
          /deep/ .el-input.el-input--suffix{
            /deep/ .el-input__inner{
              height: 44px;
              font-size: 16px;
              line-height: 24px;
            }
          }
        }
      }
    }
    .top_right{
      display: flex;
      align-items: center;
      .btn_export_excel{
        width: 130px;
        height: 44px;
        line-height: 44px;
        background: @linearBackgroundColor;
        color: white;
        text-align: center;
        font-size: 18px;
        font-weight: 700;
        border-radius: 50px;
        cursor: pointer;
      }
    }
  }
  .table_container{
    height: calc(100vh - 122px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
      .table_bottom{
        height: 54px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: 16px;
        padding: 0 10px;
        background: white;
        color: @themeFontColor;
        span{
        color: @themeBackGroundColor;
      }
    }
  }
}
</style>
