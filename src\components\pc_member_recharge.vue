<style lang="less">
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(../image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.pc_rec1{
  position: fixed;width: 100%;height: 100%;background: rgba(80,80,80,.55);top: 0;left: 0;z-index: 200;overflow: auto;
}
.pc_rec11{
  position: fixed;width: 100%;height: 100%;z-index: 5;
}
.pc_rec12{
  width: 900px;height: 675px;background: #FFF;margin: 0 auto;margin-top: 15px;border-radius: 3px;overflow: hidden;position: relative;z-index: 10;
}
.pc_rec13{
  line-height: 25px;font-size: 25px;width: 100%;color: #688392;text-align: center;margin-top: 16px;
}
.pc_rec14 {
  overflow: hidden;
  margin:12px;
  margin-bottom: 10px;
}
.pc_rec15 {
  color: @themeFontColor;
  font-size: 17px;
  width: 300px;
  font-weight: 700;
  float: left;
  line-height: 38px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}
.pc_rec16 {
  color: @themeFontColor;
  font-size: 17px;
  width: 100px;
  font-weight: 700;
  float: left;
  line-height: 15px;
  margin-top: 12px;
}
.pc_rec17  {
  width: 112px;
  float: right;
  font-weight: bold;
  // border-radius: 5px;
  color: @themeFontColor;
}
.pc_rec18{
  width: 202px;
  height: 38px;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 38px;
  border-radius: 5px;
  color: #fff;
  font-size: 16px;
  font-weight: 700;
  float: left;
  margin-left: 0px;
  margin-top: 2px;
  cursor: pointer;
}
.pc_rec19 {
  overflow: hidden;
  margin:12px;
  margin-bottom: 10px;
}
.pc_rec19 input {
  width: 110px;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  font-weight: bold;
  border: 1px solid @themeBackGroundColor;
  border-radius: 5px;
  color: @themeFontColor;
}
.pc_rec2 {
  color: #B2C3CD;
  font-size: 18px;
  width: 100px;
  font-weight: bold;
  float: left;
  line-height: 18px;
  margin-top: 12px;
}
.pc_rec21 {
  margin-left: 30px;line-height: 50px;float: left;
  span {
    color: @themeBackGroundColor;
  }
}
.pc_rec22 {
  float: right;margin-right: 30px;margin-top: 8px;
}
.pc_rec23 {
  position: absolute;
  bottom: 0;
  height: 50px;
  color: @themeFontColor;
  font-weight: bold;
  font-size: 16px;
  width: 100%;
  z-index: 1;
}
.pc_rec24 {
  position: absolute;font-size: 40px;color: @themeFontColor;cursor: pointer;right: 30px;top: -3px;font-weight: 300;
}
.pc_rec25 {
  width: 72px;
  height: 32px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  box-sizing: border-box;
  border-radius: 4px;font-weight: 500;
  font-size: 16px;
  line-height: 32px;
  color: @themeFontColor;
  text-align: center;
  cursor: pointer;
}
.el-divider--horizontal {
  margin: 14px 0 !important;
}
.pc_rec26 {
  overflow: hidden;
  margin-top: 40px;
  font-size: 24px;
}
.pc_rec27 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.pc_rec28 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
</style>

<template>
  <div>
    <div
      v-show='show_cancelVipDetail'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;'
        @click='show_cancelVipDetail = false'
      ></div>
      <div style='position: relative;z-index: 800;height: 300px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;color:#567485'>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: bold;line-height: 22px;'>提示</div>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 35px;font-weight: normal;line-height: 40px;padding: 0 45px;'>仅支持撤销最后一次充值，<br />是否要撤销？</div>
        <div class='pc_rec26'>
          <div
            class='pc_rec27'
            @click='show_cancelVipDetail = false'
          >取消</div>
          <div
            class='pc_rec28'
            @click='continueCancel();show_cancelVipDetail = false'
          >确定</div>
        </div>
      </div>
    </div>
    <div v-show="showInputRecharge" class="pc_rec1">
      <div class="pc_rec11"></div>
      <div class="pc_rec12">
        <!--标题-->
        <div class="pc_rec13">会员充值</div>
        <div class="pc_rec24" @click="close_window()">×</div>
        <el-divider></el-divider>
        <div style="position: relative">
          <!-- 表格上方内容 -->
          <div style="overflow: hidden;">
            <div style="float: left;margin-left: 30px;">
              <div class="pc_rec14">
                <div class="pc_rec15">会员姓名：{{member_detail.name}}</div>
              </div>
              <div class="pc_rec14">
                <div class="pc_rec15">手机号：{{member_detail.mobile}}</div>
              </div>
              <div class="pc_rec14">
                <div class="pc_rec15">当前余额：{{isNaN(member_money) ? '0' : Number(member_money).toFixed(2)}}</div>
              </div>
            </div>
            <div style="float: left;margin-left: 60px;">
              <div class="pc_rec19">
                <div class="pc_rec16"><span style="color:red">*</span>充值金额：</div>
                <el-input
                  class="pc_rec17"
                  v-model="addMemberMoney"
                  @input="addMemberMoney = $priceLimit(addMemberMoney)"
                  @blur="check_add()" maxlength="9">
                </el-input>
              </div>
              <div class="pc_rec19" :style="$employeeAuth('recharge_give_money') ? '':'opacity: 40%'">
                <div class="pc_rec16"><span style="color:red">&nbsp;</span>赠送金额：</div>
                <el-input
                  class="pc_rec17"
                  v-model="gift_money"
                  maxlength="9"
                  @input="gift_money = $priceLimit(gift_money)"
                  @blur="check_gift()" :disabled="!$employeeAuth('recharge_give_money')">
                </el-input>
              </div>
              <div class="pc_rec19" v-loading="no_click">
                <div class="pc_rec2" @click="add_vipDetails">
                  <div class="pc_rec18">充值</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 表格部分 -->
          <div style="width: 900px;border-top: 1px solid #DCDFE6;margin-top: 21px;">
            <el-table
              :data="tableData"
              style="width: 100%;font-size: 16px;margin-top: 5px;color: #567485;"
              height="338px">
              <el-table-column
                width="30">
              </el-table-column>
              <el-table-column
                prop="createTime"
                label="日期"
                width="200">
              </el-table-column>
              <el-table-column
                label="充值/消费"
                align="right"
                width="100">
                <template slot-scope="scope">{{ scope.row.money.toFixed(2) }}</template>
              </el-table-column>
              <el-table-column
                label="赠送金额"
                align="right"
                width="100">
                <template slot-scope="scope">{{ scope.row.giveMoney == null ? '0.00' : scope.row.giveMoney.toFixed(2) }}</template>
              </el-table-column>
              <el-table-column
                prop="originName"
                label="来源"
                width="130">
              </el-table-column>
              <el-table-column
                prop="acctName"
                label="收款账户"
                width="130">
              </el-table-column>
              <el-table-column
                prop="createUser_no"
                width="80"
                label="操作员"
                >
              </el-table-column>
              <el-table-column
                label="">
                <template slot-scope="scope">
                  <!-- {{ scope.row.giveMoney == null ? '0.00' : scope.row.giveMoney.toFixed(2) }} -->
                  <div v-show="scope.row.id == lastTradId && $employeeAuth('recharge_give_money_revoke')" class="pc_rec25" @click="cancelVipDetails(scope.row)">撤销</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="pc_rec23">
          <div class="pc_rec21">
            共<span>{{total}}</span>条记录
          </div>
          <div class="pc_rec22">
            <el-pagination
              layout="prev, pager, next"
              @current-change="handleCurrentChange"
              :total="total"
              :current-page="pagenum"
              :page-size="pageSize"
              :page-count="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {
      pagenum: 1,
      addMemberMoney: '',
      gift_money: '',
      total: 0,
      tableData: [],
      show_cancelVipDetail: false,
      pageSize: 6,
      no_click: false,
      outTradeNo: '',
      lastTradId: '',
      cancel_vipData: {},
      member_money: ''
    };
  },
  watch: {
    settlement() {
      if (this.settlement === false && this.showInputRecharge === true) {
        this.addMemberMoney = '';
        this.gift_money = '';
        this.pagenum = 1;
        // this.getLastPay();
        // this.show_detail();
        // this.get_member_money();
        this.getInfoList();
      }
    },
    showInputRecharge() {
      if (this.showInputRecharge === true) {
        this.addMemberMoney = '';
        this.gift_money = '';
        this.outTradeNo = '';
        this.lastTradId = '';
        this.pagenum = 1;
        // this.getLastPay();
        // this.show_detail();
        // this.get_member_money();
        this.getInfoList();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    continueCancel() {
      if (this.no_click) {
        return;
      }
      this.no_click = true;
      var that = this;
      setTimeout(function() {
        that.no_click = false;
      }, 100);
      this.SET_SHOW({
        finalPayParam: {
          from: 'member_cancel',
          rightKeyword: '',
          cancel_vipData: this.cancel_vipData
        }
      });
      this.SET_SHOW({settlement: true});
    },
    // 撤销
    cancelVipDetails(data) {
      console.log(data, 8990);
      this.show_cancelVipDetail = true;
      this.cancel_vipData = {
        phone: this.sys_uid,
        systemName: $config.systemName,
        uid: this.loginInfo.uid,
        sysSid: this.sys_sid,
        vipId: data.vipId,
        outTradeNo: data.outTradeNo,
        type: 4,
        originId: 4,
        acctId: data.acctId,
        localOperateTime: data.createtime,
        storeName: this.username,
        money: data.money,
        checkPoint: 0
      };
    },
    getLastPay() {
      if (this.member_detail.id === '' || this.member_detail.id === undefined) {
        return;
      }
      var that = this;
      var vipdata = {
        'systemName': $config.systemName,
        'phone': this.sys_uid,
        'vipId': this.member_detail.id,
        'sysSid': this.sys_sid
      };
      if (pos.network.isConnected()) {
        demo.$http.post(that.$rest.pc_getLastPay, vipdata, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (rs) {
            console.log(rs, 998833337);
            that.outTradeNo = rs.data.data === null ? '' : rs.data.data.outTradeNo;
            that.lastTradId = rs.data.data === null ? '' : rs.data.data.id;
          });
      } else {
        that.member_money = '';
        demo.msg('error', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    get_member_money() {
      if (this.member_detail.id === '' || this.member_detail.id === undefined) {
        return;
      }
      var that = this;
      var vipdata = {
        'systemName': $config.systemName,
        'phone': that.sys_uid,
        'id': that.member_detail.id,
        'uid': that.loginInfo.uid
      };
      if (pos.network.isConnected()) {
        demo.$http.post(that.$rest.pc_getVipById, vipdata, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (rs) {
            that.member_money = rs.data.data.hasMoney;
          });
      } else {
        that.member_money = '';
        demo.msg('error', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    getInfoList() {
      if (!this.member_detail.id) {
        return;
      }
      let params = {
        'systemName': $config.systemName,
        'phone': this.sys_uid,
        'vipId': this.member_detail.id,
        'sysSid': this.sys_sid,
        'currentPage': this.pagenum,
        'id': this.member_detail.id,
        'uid': this.loginInfo.uid,
        'pageSize': 6
      };
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.pc_getVipTopUpDetailById, params)
          .then(res => {
            console.log(res, 'pc_getVipTopUpDetailById res');
            // 最后充值单据
            let undoObj = res.data.data.undo;
            this.outTradeNo = undoObj && undoObj.outTradeNo ? undoObj.outTradeNo : '';
            this.lastTradId = undoObj && undoObj.id ? undoObj.id : '';
            // 会员详情
            let detail = res.data.data.detail;
            clerkService.getEmployeeNumberByUid(detail.list, ['createUser'], rs => {
              this.tableData = demo.t2json(rs);
            });
            this.total = detail.total;
            // 会员余额
            let vip = res.data.data.vip;
            this.member_money = vip.hasMoney;
          })
          .catch(err => {
            demo.msg('warning', err.msg);
          });
      } else {
        this.member_money = '';
        demo.msg('error', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    check_add() {
      if (this.addMemberMoney === '') {
        return;
      }
      if (isNaN(this.addMemberMoney) === true || Number(this.addMemberMoney) < 0) {
        this.addMemberMoney = 0;
      } else if (this.addMemberMoney > 999999.99) {
        this.addMemberMoney = 999999.99;
      } else {
        this.addMemberMoney = Number(this.addMemberMoney).toFixed(2);
      }
    },
    check_gift() {
      if (this.gift_money === '') {
        return;
      }
      if (isNaN(this.gift_money) === true || Number(this.gift_money) < 0) {
        this.gift_money = 0;
      } else if (this.gift_money > 999999.99) {
        this.gift_money = 999999.99;
      } else {
        this.gift_money = Number(this.gift_money).toFixed(2);
      }
    },
    close_window() {
      this.SET_SHOW({
        showInputRecharge: false
      });
    },
    show_detail() {
      var that = this;
      var member_data = {
        'systemName': $config.systemName,
        // 'sysUid':this.sys_uid,
        'phone': this.sys_uid,
        'sysSid': this.sys_sid,
        'vipId': this.member_detail.id,
        'currentPage': this.pagenum,
        'pageSize': 6
      };

      demo.$http.post(that.$rest.pc_showvipDetails, member_data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          clerkService.getEmployeeNumberByUid(rs.data.data.list, ['createUser'], res => {
            that.tableData = demo.t2json(res);
            console.log(that.tableData, 777777);
          });
          that.total = rs.data.data.total;
        });
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      this.show_detail();
    },
    // 充值
    add_vipDetails() {
      this.no_click = true;
      var that = this;
      setTimeout(function() {
        that.no_click = false;
      }, 100);
      if (this.addMemberMoney == '' || this.addMemberMoney == 0) {
        demo.msg('warning', '请输入大于0的充值金额');
        return;
      }
      var det = _.cloneDeep(this.member_detail);
      det.systemName = $config.systemName;
      det.vipId = this.member_detail.id;
      det.type = 1;
      det.has_money = this.member_money;
      det.money = this.addMemberMoney;
      det.giveMoney = this.gift_money == '' ? null : this.gift_money;
      det.createUser = this.phone;
      det.sysUid = this.sys_uid;
      det.sysSid = this.sys_sid;
      this.SET_SHOW({ payRemark: '' });
      this.SET_SHOW({member_detail: det});
      this.SET_SHOW({
        finalPayParam: {
          from: 'member_recharge',
          buy_back: 1,
          pc_return_goods: '',
          showFinalPrice: this.addMemberMoney,
          showMember: false,
          // 默认选择现金选项卡
          acctsId: 1,
          ifautoCash: false,
          ifpay: false,
          member_money: '',
          member_password: '',
          left_goods_list: [],
          totalPrice: this.addMemberMoney,
          rightKeyword: '',
          member_data: this.member_detail,
          preDisc: 1
        }
      });
      demo.screen2({'screen2ReturnGoods': false}, 101);
      this.SET_SHOW({ settlement: true });
    }
  },
  computed: mapState({
    showInputRecharge: state => state.show.showInputRecharge,
    member_detail: state => state.show.member_detail,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    settlement: state => state.show.settlement,
    loginInfo: state => state.show.loginInfo,
    isPay: state => state.show.isPay,
    username: state => state.show.username
  })
};
</script>
