<template>
  <!-- 会员充值明细 -->
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left_container" style="margin-left: -10px;">
        <div
          class="top_left"
          style="margin-left:0"
        >
          <el-select
            v-model="uidListValue"
            placeholder="请选择"
            style="margin-left:0;width:130px;height:44px"
          >
            <el-option
              v-for="item in uidList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              value-format='yyyy-MM-dd'
            >
            </el-option>
          </el-select>
        </div>
        <div class="top_left">
          <el-select
            v-model="zfValue"
            placeholder="请选择"
            style="margin-left:10px;width:130px"
          >
            <el-option
              v-for="item in zfList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              value-format='yyyy-MM-dd'
            >
            </el-option>
          </el-select>
        </div>
        <div
          class="date_picker_container"
          @click="focusDate = true"
          :class="focusDate ? 'isInPutIng' : 'isInPutIng1'"
        >
          <el-date-picker
            v-model="fromDate"
            type="date"
            placeholder="开始日期"
            style="height:44px"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
          <div style="font-size: 16px;color: #567485">至</div>
          <el-date-picker
            v-model="toDate"
            type="date"
            placeholder="结束日期"
            style="height:44px"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
        </div>
        <div @click="currentPage = 1;httpRequest('查询', true)" class="pvr_det">查询</div>
      </div>

      <div class="top_right" style="margin-right: -10px;">
        <div class="btn_export_excel" @click="httpRequest('导出表格', true)">导出表格</div>
      </div>
    </div>
    <div class="table_container">
      <el-table :data="tableData" :empty-text="!loading ? '暂无数据' : ' '" :height="tableHeight" stripe>
        <el-table-column prop="name" label="会员姓名" show-overflow-tooltip min-width="20%" align="left"></el-table-column>
        <el-table-column prop="user_no" label="收银员"  min-width="15%" align="center"></el-table-column>
        <el-table-column label="充值金额" min-width="15%" align="right">
          <template slot-scope="scope">
            {{scope.row.addMoney.toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column label="赠送金额" min-width="15%" align="right">
          <template slot-scope="scope">
            {{scope.row.giveMoney.toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column prop="acctName" label="支付方式" min-width="15%" align="center"></el-table-column>
        <el-table-column label="充值时间" min-width="20%" align="center">
          <template slot-scope="scope">
            {{scope.row.createTime ? scope.row.createTime : '-'}}
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <div>记录：<span>{{total}}</span>，总充值金额：<span>¥{{addmoney
          ? Number(addmoney).toFixed(2) : '0.00'}}</span>，总赠送金额：<span>¥{{givemoney
          ? Number(givemoney).toFixed(2) : '0.00'}}</span></div>
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
        >
          <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="pageSize"
            :currentPage.sync="currentPage"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      keyword: '',
      addmoney: 0,
      givemoney: 0,
      uidList: [
        {
          value: '0',
          label: '全部'
        }
      ],
      uidListValue: '0',
      zfList: [
        {
          value: '0',
          label: '全部'
        }, {
          value: '1',
          label: '现金支付'
        },
        {
          value: '2',
          label: '线下支付'
        },
        {
          value: '3',
          label: '扫码支付'
        }
      ],
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      zfValue: '0', // 寄取类型
      date_value: '',
      tableHeight: 0,
      tableData: [],
      counts: {},
      pageKey: 0,
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  created () {
    this.tableHeight = screen.availHeight - 178;
    // this.pageSize = Math.floor((this.tableHeight - 50) / 50);
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    console.log(this.loginInfo);
    this.getEmployeeList(); // 员工列表暂时注释，不提供选择员工，下版本解开
    this.httpRequest('查询');
  },
  methods: {
    ...mapActions([SET_SHOW]),
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage;
      this.httpRequest('查询');
    },
    handleSizeChange() {
      this.httpRequest('查询');
    },
    // 获取收银员列表
    getEmployeeList() {
      let that = this;
      clerkService.getAllClerks(res => {
        that.uidList = that.uidList.concat(demo.t2json(res));
      });
    },
    httpRequest(str) {
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      var url = '';
      if (str === '导出表格') {
        url = this.$rest.pc_searchVipRechargeListExport;
        demo.actionLog(logList.clickVipRechargeDetailExportExcel);
      } else if (str === '查询') {
        url = this.$rest.pc_searchVipRechargeList;
      } else {
        console.log(str);
      }
      let isAdmin = this.uidList.some(e => {
        return e.value === this.uidListValue && e.label === '管理员';
      }) ? 1 : 0;
      this.loading = true;
      var sub_data = {
        'sysSid': this.sysSid,
        'fromDate': this.fromDate,
        'toDate': this.toDate,
        'phone': this.phone,
        'systemName': $config.systemName,
        'currentPage': this.currentPage,
        'pageSize': this.pageSize,
        'acctId': this.zfValue,
        'uid': this.uidListValue,
        'isAdmin': isAdmin
      };
      var that = this;
      demo.$http.post(url, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          that.parseResult(res, str);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    /**
     * sonar对应
     */
    parseResult (res, str) {
      let that = this;
      if (res.data.code === '0' && str === '查询') {
        if (res.data.data.datalist.length === 0) {
          // demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          that.tableData = [];
          that.total = that.tableData.length;
          that.addmoney = 0;
          that.givemoney = 0;
          return;
        }
        clerkService.getEmployeeNumberByUid(res.data.data.datalist, ['user'], r => {
          that.tableData = demo.t2json(r);
        });
        that.total = res.data.data.totals;
        that.addmoney = res.data.data.dataStatistics.addmoney;
        that.givemoney = res.data.data.dataStatistics.givemoney;
      } else if (res.data.code === '0' && str === '导出表格') {
        if (res.data.data.length > 0) {
          var excelData = [];
          clerkService.getEmployeeNumberByUid(res.data.data, ['user'], rr => {
            excelData = demo.t2json(rr);
            var field_mapping = {
              充值会员: 'name',
              收银员: 'user_no',
              充值金额: 'addMoney',
              赠送金额: 'giveMoney',
              支付方式: 'acctName',
              充值时间: 'createTime'
            };
            that.$makeExcel(excelData, field_mapping, '会员充值明细' + new Date().format('yyyyMMddhhmmss'));
          });
        } else {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        }
      } else {
        demo.msg('warning', res.data.msg);
        that.tableData = [];
      }
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    delayedTime: state => state.show.delayedTime,
    loginInfo: state => state.show.loginInfo
  })
};
</script>

<style lang='less' scoped>
.isInPutIng  {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
/deep/ .el-table th {
  padding: 0px;
}
.pvr_det {
  width: 100px;height: 44px;background: @linearBackgroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: 700;border-radius: 22px;margin-left: 10px;cursor: pointer;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  font-size: 16px;
  padding-right: 0;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  margin-left: 10px;
  display: flex;
  align-items: center;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .el-input--suffix .el-input__inner {
  color: @themeFontColor;background: #FFF;
}
.deposit_record_container{
  background: #F5F8FB;
  .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .top_left_container{
      display: flex;
      align-items: center;
      .top_left{
        display: flex;
        align-items: center;
      }
    }
    .top_right{
      display: flex;
      align-items: center;
      .btn_export_excel{
        width: 130px;
        height: 44px;
        line-height: 44px;
        background: @linearBackgroundColor;
        color: white;
        text-align: center;
        font-size: 18px;
        font-weight: 700;
        border-radius: 22px;
        cursor: pointer;
      }
    }
  }
  .table_container{
    height: calc(100vh - 122px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
      .table_bottom{
        height: 54px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        padding: 0 30px;
        background: white;
        color: @themeFontColor;
        span{
          color: @themeBackGroundColor;
        }
      }
  }
}
</style>
