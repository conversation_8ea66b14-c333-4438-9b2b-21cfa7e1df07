const stringUtils = {
  /**
   * 获取某个月的第一天
   * 上个月，n = -1
   * 当前月（默认值），n = 0
   * 下个月，n = 1
   * ……
   */
  getFirstDayOfMonth: (date, n) => {
    let cloneDate;
    if (isNaN(Date.parse(date))) {
      cloneDate = new Date();
    } else {
      cloneDate = date.clone();
    }
    if (!Number.isInteger(n)) {
      n = 0;
    }
    cloneDate.setDate(1);
    if (n !== 0) {
      cloneDate.setMonth(cloneDate.getMonth() + n);
    }

    return cloneDate;
  },

  /**
   * 获取某个月的最后一天
   * 上个月，n = -1
   * 当前月（默认值），n = 0
   * 下个月，n = 1
   * ……
   */
  getLastDayOfMonth: (date, n) => {
    let cloneDate;
    if (isNaN(Date.parse(date))) {
      cloneDate = new Date();
    } else {
      cloneDate = date.clone();
    }
    if (!Number.isInteger(n)) {
      n = 0;
    }
    cloneDate.setDate(1);
    cloneDate.setMonth(cloneDate.getMonth() + n + 1);
    cloneDate.setDate(0);

    return cloneDate;
  },
  checkBarCode(s) {
    var res = new RegExp(/^\d{12}$/).test(s.substring(0, 12));
    if (!res) {
      return '验证条形码前12位出错：' + s;
    }

    var a = 0;
    var b = 0;
    for (var i = 1; i <= 12; i++) {
      var sc = parseInt(s[i - 1]);
      if (i % 2 == 0) {
        a += sc;
      } else {
        b += sc;
      }
    }

    return (10 - (a * 3 + b) % 10) % 10;
  },

  /**
   * 属性映射
   * @param {*} jsonArray 对象数组
   * @param {*} fieldMappers 映射关系
   */
  fieldMapping(jsonArray, fieldMappers, fkey) {
    var a = [];
    for (var i = 0; i < jsonArray.length; i++) {
      var b = {};
      var json = jsonArray[i];

      for (var key in fieldMappers) {
        if (!this.isBlank(json[key]) && fkey !== key) {
          b[fieldMappers[key]] = (json[key] + '').trim();
        }
      }

      if (Object.keys(b).length > 0) {
        a.push(b);
      }
    }

    return a;
  },
  fieldMapping2(jsonArray, fieldMappers) {
    var a = [];
    for (var i = 0; i < jsonArray.length; i++) {
      var b = {};
      var json = jsonArray[i];

      for (var key in fieldMappers) {
        if (json.hasOwnProperty(key)) {
          b[fieldMappers[key]] = json[key];
        }
      }

      if (Object.keys(b).length > 0) {
        a.push(b);
      }
    }

    return a;
  },
  /**
   * 属性与值切换
   * @param {*} fieldMappers 映射关系
   */
  reverseMapping(fieldMappers) {
    var a = {};

    for (var key in fieldMappers) {
      a[fieldMappers[key]] = key;
    }

    return a;
  },

  // 字符串转字符流
  s2ab(s) {
    if (typeof ArrayBuffer !== 'undefined') {
      let buf = new ArrayBuffer(s.length);
      let view = new Uint8Array(buf);
      for (let i = 0; i < s.length; ++i) {
        view[i] = s.charCodeAt(i) & 0xff;
      }
      return buf;
    } else {
      let buf = [];
      for (let j = 0; j < s.length; ++j) {
        buf.push(s.charCodeAt(j) & 0xff);
      }
      return buf;
    }
  },
  /**
   * 使用前请先校验非空
   * @param {字符串数字} numb
   * @param {字段名} key
   * @param {检查项} type
   */
  checkNumber(numb, key, type) {
    let errMag = '';
    if (isNaN(numb)) {
      errMag += `${key}只能为数字;`;
    } else {
      let data = numb + '';
      if (type.length && data.split('.').length === 2 && data.split('.')[1].length > type.length) {
        errMag += `${key}最多小数点后${type.length}位;`;
      }
      if (type.min && +data < +type.min) {
        errMag += `${key}最小为${type.min};`;
      }
      if (type.max && +data > +type.max) {
        errMag += `${key}最大为${type.max};`;
      }
    }
    return errMag;
  },
  prefixInteger(num, m) {
    return (Array(m).join(0) + num).slice(-m);
  },

  isValidDate(date) {
    return date instanceof Date && !isNaN(date.getTime());
  },

  isBlank(s) {
    return s === null || s === undefined || (typeof s === 'string' && s.trim() === '');
  },
  csvFormat(str) {
    if (typeof str !== 'string' || this.isBlank(str)) {
      return '';
    }

    return `"${str.trim().replaceAll('"', '""')}"`;
  },
  createFile(filename, content, format) {
    const blob = new Blob([content]);
    return new File([blob], filename, { type: format });
  }
};

String.prototype.format = function (...args) {
  if (args.length === 0) {
    return this;
  }
  var param = args[0];
  var s = this;
  if (typeof param == 'object') {
    for (var key in param) {
      s = s.replace(new RegExp('\\{' + key + '\\}', 'g'), param[key]);
    }
    return s;
  } else {
    for (var i = 0; i < args.length; i++) {
      s = s.replace(new RegExp('\\{' + i + '\\}', 'g'), args[i]);
    }
    return s;
  }
};

export class StringBuilder {
  constructor(init) {
    if (init === undefined) {
      this._stringArray = [];
    } else if (Array.isArray(init)) {
      this._stringArray = [...init];
    } else {
      this._stringArray = [init];
    }
  }
  append(obj) {
    this._stringArray.push(obj);
    return this;
  }
  clear() {
    this._stringArray.length = 0;
    return this;
  }
  toString(separator) {
    return this._stringArray.join(separator === undefined ? '' : separator);
  }
  isEmpty() {
    return this._stringArray.length === 0;
  }
}

Date.prototype.format = function (param_format) {
  var format = param_format;
  var o = {
    'M+': this.getMonth() + 1, // month
    'd+': this.getDate(), // day
    'h+': this.getHours(), // hour
    'm+': this.getMinutes(), // minute
    's+': this.getSeconds(), // second
    'q+': Math.floor((this.getMonth() + 3) / 3), // quarter
    S: stringUtils.prefixInteger(this.getMilliseconds(), 3), // millisecond
    'S+': '000' // 微秒
  };
  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (this.getFullYear() + '').substr(4 - RegExp.$1.length)
    );
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
};

Date.prototype.addDays = function (days) {
  let nd = this.valueOf();
  nd = nd + days * 24 * 60 * 60 * 1000;
  nd = new Date(nd);
  let y = nd.getFullYear();
  let m = nd.getMonth() + 1;
  let d = nd.getDate();
  if (m <= 9) {
    m = '0' + m;
  }
  if (d <= 9) {
    d = '0' + d;
  }
  let cdate = y + '-' + m + '-' + d;
  return cdate;
};

Date.prototype.clone = function () {
  return new Date(this.valueOf());
}
export default stringUtils;
