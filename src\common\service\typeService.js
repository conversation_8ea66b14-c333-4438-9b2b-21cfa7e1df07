import md5 from 'js-md5';
import dao from '../dao/dao';
import pyCode from '@/common/pyCode';
import {createTreeList} from '@/utils';
import { reject } from 'lodash';

const typeService = {
  /**
   * 获取所有商品类别
   * @param {*} onSuccess
   * @param {*} onFail
   */
  search: function (onSuccess, onFail) {
    dao.exec(sqlApi.typeSearch, (res) => {
      if (res.length > 0) {
        onSuccess(createTreeList('', res));
      } else {
        onSuccess([]);
      }
    }, onFail);
  },

  /**
   * 判断商品类别是否存在
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  // exist: function (data, onSuccess, onFail) {
  //   var datacp = _.cloneDeep(data);
  //   datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');

  //   if (data.id === undefined) {
  //     dao.exec(sqlApi.typeInsertExist.format(datacp), onSuccess, onFail);
  //   } else {
  //     dao.exec(sqlApi.typeUpdateExist.format(datacp), onSuccess, onFail);
  //   }
  // },

  // use: function (data, onSuccess, onFail) {
  //   dao.exec(sqlApi.typeUse.format(data), onSuccess, onFail);
  // },

  // batchPutItem: function(data, onSuccess, onFail) {
  //   let insertTypeList = data;
  //   if (insertTypeList.length > 0) {
  //     let insertList = insertTypeList.slice(0, 500);
  //     insertTypeList = insertTypeList.length > 500 ? insertTypeList.slice(500) : [];
  //     let typeValues = '';
  //     insertList.map(name => {
  //       typeValues += `(1, '${name}', '','${md5(name)}'),`;
  //     });
  //     // 插入数据sql生成完了
  //     dao.exec(sqlApi.typeBatchInsert.format(typeValues.substr(0, typeValues.length - 1)), () => {
  //       this.batchPutItem(insertTypeList, onSuccess, onFail);
  //     });
  //   } else {
  //     onSuccess();
  //   }
  // },

  // batchPut: function (data, onSuccess, onFail) {
  //   let typeList = data;
  //   // 插入商品类型
  //   dao.exec(sqlApi.typeBatchSelect, res => {
  //     const typesNames = demo.t2json(res);
  //     // 获取需要插入数据
  //     const insertTypeList = typeList.filter(typeName => typesNames.every(types => types.name !== typeName));
  //     // 更新原有删除数据
  //     dao.exec(sqlApi.typeBatchUpdate.format(typeList.join("','")), () => { this.batchPutItem(insertTypeList, onSuccess, onFail); }, onFail);
  //   }, onFail);
  // },

  /**
   * 新增商品类别
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  // put: function (data, onSuccess, onFail) {
  //   if (+data.is_deleted === 1) {
  //     this.use(data, function (res) {
  //       if (demo.t2json(res)[0].cnt > 0) {
  //         demo.$toast('商品使用中，不允许删除');
  //       } else {
  //         dao.exec(sqlApi.typeDel.format(data), onSuccess, onFail);
  //       }
  //     });
  //   } else {
  //     var datacp = _.cloneDeep(data);
  //     datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');
  //     data.fingerprint = md5(data.name);
  //     datacp.fingerprint = data.fingerprint;
  //     datacp.parentFingerprint = data.parentFingerprint || '';
  //     datacp.icon = data.icon || '';
  //     datacp.pinyin = pyCode.getPyCode(data.name);
  //     if (data.id === undefined) { // 新增
  //       this.add(datacp, onSuccess, onFail);
  //     } else { // 修改
  //       this.exist(data, function (res) {
  //         if (demo.t2json(res).length === 0) {
  //           dao.exec(sqlApi.typeUpdate.format(datacp), onSuccess, onFail);
  //         } else {
  //           demo.$toast('数据重复');
  //         }
  //       }, onFail);
  //     }
  //   }
  // },

  /**
   * 修改父分类
   */
  // typeUpdateParent: function (params, onSuccess, onFail) {
  //   if (params.fingerprint === undefined || params.fingerprint === '' || params.id === undefined || params.id === '') {
  //     demo.$toast('请求参数不合法');
  //   }
  //   this.exist(params, function (res) {
  //     if (demo.t2json(res).length === 0) {
  //       var datacp = _.cloneDeep(params);
  //       datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');
  //       datacp.fingerprint = md5(params.name);
  //       datacp.parentFingerprint = params.fingerprint; // 当前父分类的fingerprint
  //       datacp.icon = params.icon || '';
  //       datacp.pinyin = pyCode.getPyCode(params.name);
  //       dao.exec(sqlApi.typeUpdateParent.format(datacp), onSuccess, onFail);
  //     } else {
  //       demo.$toast('数据重复');
  //     }
  //   }, onFail);
  // },

  /**
   * 讨论后的思路分两步
   * 一、检查名字是否重复
   *     如果重复，查看是否已经删除
   *         ①如果删除 ==> 恢复
   *         ②如果未删除 ==> 提示重复
   *     如果不重复走步骤二
   * 二、检查fingerprint是否重复
   *     如果重复 ==> 提示改名字
   *     如果不重复 ==> 新增
   * 整理成JS逻辑，按照顺序一共四种选项
   * 1.名字重复已删除 => 恢复
   * 2.名字重复未删除 => 提示重复
   * 3.fingerprint 重复 => 提示改名字
   * 4.fingerprint 不重复 => 新增
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  // add: function (params, onSuccess, onFail) {
  //   dao.exec(sqlApi.typeInsertExistSelect.format(params), res => {
  //     var type1 = res.find(e => e.name === params.name && +e.is_deleted === 1);
  //     var type2 = res.some(e => e.name === params.name && +e.is_deleted === 0);
  //     var type3 = res.some(e => e.fingerprint === params.fingerprint);
  //     if (type1) {
  //       dao.exec(sqlApi.typeDeleteRecovery.format(type1.id), onSuccess, onFail);
  //     } else if (type2) {
  //       demo.$toast('数据重复');
  //     } else if (type3) {
  //       demo.$toast('请修改类别名字');
  //     } else {
  //       params.parentFingerprint = params.parentFingerprint || '';
  //       params.pinyin = pyCode.getPyCode(params.name);
  //       dao.exec(sqlApi.typeInsert.format(params), onSuccess, onFail);
  //     }
  //   }, onFail);
  // },

  /**
   * 更新分类排序顺序
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  orderbyUpdate: function (params, onSuccess, onFail) {
    var sql = '';
    params.forEach(param => {
      sql += sqlApi.typeOrderByUpdate.format(param);
    });
    dao.transaction(sql, onSuccess, onFail);
  },
  /**
  *
  * @param {parentFingerprint,name} data
  */
  insert(data, onSuccess, onFail) {
    let {parentFingerprint, name, fingerprint, icon} = data;
    const sql = `select is_deleted,fingerprint as curr_fingerprint from types where name = '${name}' 
    and parent_fingerprint = '${parentFingerprint}' order by create_at desc limit 1`;
    dao.exec(sql, res => {
      if (res.length > 0) {
        const { isDeleted, currFingerprint } = res[0];
        if (!isDeleted) {
          if (currFingerprint === fingerprint) {
            onSuccess(fingerprint);
          } else {
            onFail('分类重复');
          }
        } else {
          if (!fingerprint) {
            const pinyin = pyCode.getPyCode(name);
            fingerprint = md5(name + '_' + parentFingerprint + new Date().format('yyMMddhhmmssS'));
            dao.exec(`insert into "types"(name, pinyin,parent_fingerprint, fingerprint,icon, create_at, revise_at, sync_at)
               values('${name}','${pinyin}','${parentFingerprint}','${fingerprint}', '${icon || ""}', datetime('now','localtime'),datetime('now','localtime'),datetime('now','localtime'))`, () => {
              onSuccess(fingerprint);
            })
          } else {
            dao.exec(`update types set name='${name}', is_synced=0, revise_at=datetime('now','localtime') where fingerprint = '${fingerprint}'`, () => {
              onSuccess(fingerprint);
            })
          }
        }
      } else {
        if (!fingerprint) {
          let fingerprint = md5(name + '_' + parentFingerprint);
          const pinyin = pyCode.getPyCode(name);
          const sql = `select * from "types" where fingerprint ='${fingerprint}' `
          dao.exec(sql, res => {
            fingerprint = this.resetFingerprint(res, fingerprint, name, parentFingerprint);
            dao.exec(`insert into "types"(name, pinyin,parent_fingerprint, fingerprint,icon,create_at, revise_at, sync_at)
            values('${name}','${pinyin}','${parentFingerprint}','${fingerprint}','${icon || ""}',datetime('now','localtime'),datetime('now','localtime'),datetime('now','localtime'))`, () => {
              onSuccess(fingerprint)
            });
          })
        } else {
          dao.exec(`update types set name='${name}', is_synced=0, revise_at=datetime('now','localtime') where fingerprint = '${fingerprint}'`, () => {
            onSuccess(fingerprint);
          })
        }
      }
    }, err => {
      console.error(`类别检索${err}`);
    })
  },
  resetFingerprint(res, fingerprint, name, parentFingerprint) {
    if (res.length > 0) {
      fingerprint = md5(name + '_' + parentFingerprint + new Date().format('yyMMddhhmmssS'));
    }
    return fingerprint;
  },
  /**
   *
   * @param {fingerprint} data order by case when sortno is null then 999 else sortno end, id;
   */
  use(fingerprint, onSuccess, onFail) {
    dao.exec(`SELECT
     t.name,
     t.fingerprint 
   FROM
     ( SELECT id, name, fingerprint, sortno, create_at 
     FROM types 
     WHERE fingerprint = '${fingerprint}' 
     OR parent_fingerprint = '${fingerprint}' ) t 
   WHERE
     EXISTS ( SELECT id FROM goods WHERE type_fingerprint = t.fingerprint and is_deleted = 0) 
   ORDER BY
   CASE
     WHEN t.sortno IS NULL THEN
     999 ELSE t.sortno 
     END,
   t.id limit 1`, info => {
      onSuccess(info);
    }, onFail);
  },
  del(data, onSuccess, onFail) {
    const { fingerprint } = data;
    this.use(fingerprint, info => {
      info.length > 0 ? onFail(info) : dao.exec(`update types set is_deleted = 1, is_synced=0 where fingerprint = '${fingerprint}'`, res => {
        onSuccess(res);
      }, err => {
        console.error(`删除失败${err}`);
        onFail('删除失败');
      });
    });
  },
  /**
   * 获取收银台默认显示分类
   */
  getDefaultType() {
    return new Promise(resolve => {
      storeInfoService.get({ id: 1 }, storeInfo => {
        const settings = storeInfo[0].settings ? JSON.parse(storeInfo[0].settings) : {};
        // 如果3.11.0开启的收银台热销，默认分类为热销
        // 如果3.11.0未开启收银台热销，默认分类为全部
        // 如果3.12.0用户主动设置了默认分类，取用户设置的默认分类
        const defaultType = settings.defaultType;
        if (defaultType !== undefined) {
          resolve(settings.defaultType);
        } else if (settings.show_hot_good) {
          resolve('-99');
        } else {
          resolve('');
        };
      });
    });
  },
  /**
   * 设置收银台默认显示分类
   */
  setDefaultType(typeId) {
    return new Promise((resolve, reject) => {
      storeInfoService.get({ id: 1 }, storeInfo => {
        const settings = storeInfo[0].settings ? JSON.parse(storeInfo[0].settings) : {};
        settings.defaultType = typeId;
        storeInfo[0].settings = JSON.stringify(settings);
        storeInfoService.update(storeInfo[0], () => {
          resolve();
        }, (error) => {
          reject(error);
        });
      });
    });
  },
  /**
   * 检查分类是否被删除
   */
  typeDelCheck(fingerprint) {
    return new Promise((resolve, reject) => {
      const sql = sqlApi.typeDelCheck.format({ fingerprint });
      dao.exec(sql, (res) => {
        resolve(res.length)
      }, reject);
    });
  }
};

window.typeService = typeService;
export default typeService;
