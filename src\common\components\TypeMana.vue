<template>
  <cj-mask :visible.sync="visible">
    <div class="type_mana_wrap">
      <div class="type_mana_header">
        <span>分类管理</span>
        <span class="closeIcon" @click="closeTypeManage">×</span>
      </div>
      <div class="header_tips">当前页面支持分类的多种操作，包括新增分类、删除分类、分类排序、设为收银台默认显示分类</div>
      <!-- 使用draggable组件 -->
      <div class="type_list_wrap">
        <div class="type_parent_list list_wrap">
          <div class="pc_type_addType" @click="showAddTypeBox(1)">
            <span>+</span>
            &nbsp;&nbsp;新增分类
          </div>
          <div class="draggable_div">
            <!-- 系统默认分类 -->
            <div v-for="(type, index) in systemType" :key="type.id" class="pc_item system_type_item">
              <div class="info_wrap">
                <div class="default" :class="{ activeDefault: defaultTypeId == type.id }" @click="setDefaultType(type, index)">
                  <div>默认</div>
                  <div v-if="defaultTypeId == type.id" class="select_wrap"></div>
                </div>
                <div class="type_name_value">
                  {{ type.name }}
                </div>
              </div>
            </div>
            <draggable v-model="typeArr"
              ghostClass="pc_chosen"
              forceFallback="true"
              fallbackTolerance="12"
              delay="50"
              animation="500"
              @end="onEnd">
              <transition-group>
                <div
                  class="pc_item"
                  :class="typeSelectedId === type.id ? 'is_active' : ''"
                  @click.prevent="typeItemClick(type, index)"
                  v-for="(type, index) in typeArr"
                  :key="type.id"
                >
                  <div class="info_wrap">
                    <div class="default" :class="{ activeDefault: defaultTypeId == type.id }" @click.stop="setDefaultType(type)">
                      <div>默认</div>
                      <div v-if="defaultTypeId == type.id" class="select_wrap"></div>
                    </div>
                    <div :id="`${type.id}_type`" class="type_name_value">
                      {{ type.name }}
                    </div>
                  </div>
                  <div class="pc_type_iconDiv" v-show="typeSelectedId === type.id && typeSelectedName !== '其他分类'">
                    <i class="el-icon-edit" @click="showEditTypeBox(1, type)"></i>
                    <i class="el-icon-delete" @click="deleteType(1, type)"></i>
                    <i class="el-icon-sort" @click="showMarkedWord"></i>
                  </div>
                </div>
              </transition-group>
            </draggable>
          </div>
        </div>
        <div class="type_children_list list_wrap">
          <div class="pc_type_addType" @click="showAddTypeBox(2)">
            <span>+</span>
            &nbsp;&nbsp;新增子分类
          </div>
          <draggable
            v-model="subTypeArr"
            ghostClass="pc_chosen"
            forceFallback="true"
            fallbackTolerance="12"
            delay="50"
            animation="500"
            @end="onSubEnd"
            class="draggable_div"
          >
            <transition-group>
              <div
                class="pc_item"
                :class="subTypeSelectedId === stype.id ? 'is_active' : ''"
                @click="subTypeItemClick(stype)"
                v-for="stype in subTypeArr"
                :key="stype.id"
              >
                <div class="info_wrap">
                  <div class="default" :class="{ activeDefault: defaultTypeId == stype.id }" @click.stop="setDefaultType(stype)">
                    <div>默认</div>
                    <div v-if="defaultTypeId == stype.id" class="select_wrap"></div>
                  </div>
                  <div :id="`${stype.id}_type`" class="type_name_value">
                    {{ stype.name }}
                  </div>
                </div>
                <div class="pc_type_iconDiv" v-if="subTypeSelectedId === stype.id">
                  <i class="el-icon-edit" @click="showEditTypeBox(2, stype)"></i>
                  <i class="el-icon-delete" @click="deleteType(2, stype)"></i>
                  <i class="el-icon-sort" @click="showMarkedWord"></i>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
    </div>
    <cj-mask :zIndex="801" :visible.sync="showAddType">
      <div class="pc_type_addDiv" style="height: 235px">
        <div class="type_mana_header" id="addType">
          <span>{{ isEdit ? '编辑' : '新增' }}{{ level === 1 ? '' : '子' }}分类</span>
          <span class="closeIcon" @click="closeTypeAdd">×</span>
        </div>
        <div style="margin: 10px 20px">
          <el-input
            id="type_add_input"
            ref="type_add_input"
            v-model.trim="type_value"
            maxlength="20"
            @focus="selectText('type_add_input')"
            @input="type_value = typeNameInput(type_value)"
            placeholder="10个字(20个字符)以内"
          ></el-input>
        </div>
        <div class="pc_type_add_cancle" @click="closeTypeAdd">取消</div>
        <div class="pc_type_add_submit" @click="handleConfirm">确定</div>
      </div>
    </cj-mask>
    <!-- 删除类别或单位的最终确认提示弹出框 -->
    <confirm-dialog
      :visible.sync="show_delete_category"
      :message="'该分类下有商品存在，<br/>商品也将一并删除'"
      cancel-text="取消删除"
      confirm-text="一并删除"
      @cancel="show_delete_category = false"
      @confirm="deleteConfirm"
    ></confirm-dialog>
  </cj-mask>
</template>

<script>
// 加载vuex
import { mapState } from 'vuex';
import draggable from 'vuedraggable';
import { eleToView } from '@/utils/util.js';
import CjMask from '@/common/components/CjMask';
import { typeNameFormat } from '@/common/inputLimitUtils.js';
import ConfirmDialog from '@/common/components/ConfirmDialog';
export default {
  name: 'TypeMana',
  components: {
    CjMask,
    draggable,
    ConfirmDialog
  },
  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 分类列表
    typeArr: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      systemType: [
        { id: '', name: '全部分类' },
        { id: '0', name: '称重分类' }
      ],
      defaultTypeId: '-99',
      level: 1,
      typeSelectedId: '',
      typeSelectedName: '',
      typeSelectedFingerprint: '',
      subTypeSelectedFingerprint: '',
      editItem: {},
      subTypeSelectedId: '',
      subTypeSelectedName: '',
      // 定义要被拖拽对象的数组
      subTypeArr: [],
      showAddType: false,
      showEditType: false,
      type_value: '',
      type_edit: '',
      unit: '',
      addClick: false,
      show_delete_category: false,
      typeFingerprint: '',
      typeLevel: '',
      systemTypeIds: ['-99', '', '0'],
      isEdit: false, // false新增分类，true编辑分类
      deleteItem: {} // 要删除的分类对象
    };
  },
  methods: {
    // 关闭分类编辑
    closeTypeManage() {
      this.$emit('close');
    },
    // 编辑/新增子分类时更新子分类列表
    update(list) {
      if (this.typeSelectedFingerprint) {
        const types = list.find(type => {
          return type.fingerprint === this.typeSelectedFingerprint;
        });
        console.warn('types', types);
        if (types) {
          this.typeItemClick(types);
        }
      }
    },
    // 获取当前默认分类
    async getDefaultType() {
      this.defaultTypeId = await typeService.getDefaultType();
      if (this.showHotGood) {
        this.systemType.unshift({ id: '-99', name: '热销' });
      }
      if (!this.systemTypeIds.includes(this.defaultTypeId)) {
        this.checkIsSubType(this.defaultTypeId);
      }
    },
    /**
     * 选择默认分类
     * @param {object} type 选择的分类信息
     */
    async setDefaultType(type) {
      // 如果点击的是已选择的默认分类拦截
      if (this.defaultTypeId === type.id) {
        return;
      }
      await demo.updateStoreSettings('defaultType', type.id);
      this.defaultTypeId = type.id;
    },
    // 检查选择的分类是否为子分类
    checkIsSubType(typeId) {
      // 扁平分类数组
      const list = this.typeArr.reduce((pre, next) => {
        pre.push(next);
        if (next.list && next.list.length > 0) {
          pre = pre.concat(next.list);
        }
        return pre;
      }, []);
      // 查出默认分类
      const selectType = list.find(item => {
        return item.id === typeId;
      });
      // 如果有父分类则默认分类为子分类
      this.$nextTick(() => {
        if (selectType.parentFingerprint) {
          const types = this.typeArr.find(item => {
            return item.fingerprint === selectType.parentFingerprint;
          });
          this.typeItemClick(types);
          eleToView(`${types.id}_type`);
        }
        eleToView(`${selectType.id}_type`);
      });
    },
    typeNameInput(name) {
      // 分类名长度输入限制
      let newName = name.replace(/[$':;]/g, '');
      return typeNameFormat(newName);
    },
    typeItemClick(item) {
      // 一级分类项点击
      this.typeSelectedId = item.id;
      this.typeSelectedName = item.name;
      this.typeSelectedFingerprint = item.fingerprint;
      this.subTypeArr = item.list;
      this.subTypeSelectedId = '';
      this.subTypeSelectedName = '';
      this.subTypeSelectedFingerprint = '';
      this.level = 1;
    },
    subTypeItemClick(item) {
      // 二级分类项点击
      this.subTypeSelectedId = item.id;
      this.subTypeSelectedName = item.name;
      this.subTypeSelectedFingerprint = item.fingerprint;
      this.level = 2;
    },
    // 拖拽结束事件
    onEnd() {
      const idList = [];
      for (var i = 0; i < this.typeArr.length; i++) {
        idList.push({ id: '', sortno: '' });
        idList[i].id = this.typeArr[i].id;
        idList[i].sortno = i;
      }
      typeService.orderbyUpdate(idList, () => {});
      this.$emit('update');
    },
    // 二级分类拖拽结束事件
    onSubEnd() {
      const idList = [];
      for (var i = 0; i < this.subTypeArr.length; i++) {
        idList.push({ id: '', sortno: '' });
        idList[i].id = this.subTypeArr[i].id;
        idList[i].sortno = i;
      }
      typeService.orderbyUpdate(idList, () => {});
      this.$emit('update');
    },
    showMarkedWord() {
      demo.$toast('拖动调整分类顺序');
    },
    reloadSubTypeArr() {
      // 重载二级分类
      if (this.typeSelectedId !== '') {
        this.typeArr.forEach(item => {
          if (this.typeSelectedId === item.id) {
            this.subTypeArr = item.list;
          }
        });
      } else {
        this.subTypeArr = [];
      }
    },
    deleteConfirm() {
      // 删除分类
      this.show_delete_category = false;
      goodService.deleteByTypes({ fingerprint: [this.typeFingerprint], isDelType: '1' }, async () => {
        demo.msg('success', '删除商品类别成功');
        this.$emit('update');
        if (this.level === 1) {
          this.typeSelectedId = '';
          this.typeSelectedName = '';
          this.typeSelectedFingerprint = '';
          this.subTypeArr = [];
        } else {
          this.subTypeSelectedId = '';
          this.subTypeSelectedName = '';
          this.subTypeSelectedFingerprint = '';
        }
        // 检查当前删除的分类是否为默认分类或者包含默认分类
        const type = this.deleteItem.list.find(pre => {
          return pre.id === this.defaultTypeId;
        });
        if (this.defaultTypeId === this.deleteItem.id || type) {
          await this.setDefaultType('');
          this.defaultTypeId = '';
        }
        // 传递给分类组件当前删除的分类
        this.$emit('deleteType', this.deleteItem);
      });
      this.typeFingerprint = '';
      this.typeLevel = '';
    },
    /**
     * 删除分类
     * @param {number} level 分类类型1父分类2子分类
     * @param {object} item 分类信息
     */
    deleteType(level, item) {
      this.deleteItem = item;
      this.typeFingerprint = item.fingerprint;
      this.typeLevel = level;
      typeService.use(this.typeFingerprint, info => {
        if (info.length > 0) {
          if (this.$employeeAuth('delete_products')) {
            this.show_delete_category = true;
          } else {
            demo.msg('warning', '商品使用中，不允许删除');
          }
        } else {
          this.deleteConfirm();
        }
      });
    },
    closeTypeAdd() {
      // 关闭新增分类
      this.showAddType = false;
    },
    /**
     * 新增分类
     * @param {number} level 1父分类2子分类
     */
    showAddTypeBox(level) {
      // 新增一级/二级分类
      if (level === 2 && this.typeSelectedId === '') {
        demo.msg('warning', '请先选择父分类');
        return;
      }
      if (this.typeSelectedName === '其他分类' && level === 2) {
        demo.msg('warning', '其他分类不能添加子分类!');
        return;
      }
      this.showAddType = true;
      this.isEdit = false;
      this.type_value = '';
      this.level = level;
      this.$nextTick(() => {
        this.$refs.type_add_input.focus();
      });
    },
    /**
     * 编辑分类
     * @param {number} level 1父分类2子分类
     * @param {object} item 分类对象信息
     */
    showEditTypeBox(level, item) {
      // 编辑一级/二级分类
      this.level = level;
      this.showAddType = true;
      this.type_value = item.name;
      this.editItem = item;
      this.isEdit = true;
      this.$nextTick(() => {
        this.$refs.type_add_input.select();
      });
    },
    // 新增/编辑弹窗确定
    handleConfirm() {
      // 编辑/新增前名称检查
      if (this.type_value === '') {
        demo.msg('warning', '请输入分类名称');
        return;
      }
      if (this.type_value === '全部分类' || this.type_value === '称重分类') {
        demo.msg('warning', '分类已存在！');
        return;
      }
      if (this.isEdit) {
        this.editType();
      } else {
        this.addType();
      }
    },
    addType() {
      // 新增分类前检查
      if (this.addClick) {
        return;
      }
      this.addClick = true;
      setTimeout(() => {
        this.addClick = false;
      }, this.clickInterval);
      const params = {
        name: this.type_value
      };
      if (this.level === 1) {
        // 新增分类
        params.icon = '';
        params.parentFingerprint = '';
      } else {
        // 新增子分类
        params.parentFingerprint = this.typeSelectedFingerprint;
      }
      console.log(params, 'addType params');
      typeService.insert(
        params,
        () => {
          demo.msg('success', '新增分类成功');
          this.showAddType = false;
          this.$emit('update');
        },
        err => {
          demo.msg('warning', err);
        }
      );
    },
    editType() {
      // 编辑分类前检查
      const params = {
        name: this.type_value,
        fingerprint: this.editItem.fingerprint,
        parentFingerprint: this.editItem.parentFingerprint
      };
      typeService.insert(
        params,
        () => {
          demo.msg('success', '修改分类成功');
          this.showAddType = false;
          this.$emit('update');
        },
        err => {
          demo.msg('warning', err);
        }
      );
    }
  },
  created() {
    this.reloadSubTypeArr();
    this.getDefaultType();
  },
  computed: mapState({
    showHotGood: state => state.show.showHotGood, // 是否显示热销分类
    clickInterval: state => state.show.clickInterval
  })
};
</script>
<style lang='less' scoped>
/*被拖拽对象的样式*/
.pc_item {
  padding: 6px;
  background-color: #fff;
  cursor: move;
  margin: 5px 10px 0px !important;
  border-radius: 8px;
  height: 39px;
  color: @themeFontColor;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .info_wrap {
    display: flex;
    align-items: center;
    .default {
      width: 40px;
      height: 23px;
      font-size: 12px;
      border-radius: 4px;
      color: #b2c3cd;
      margin-right: 8px;
      border: 1px solid #b2c3cd;
      position: relative;
      overflow: hidden;
      line-height: 22px;
      text-align: center;
      cursor: pointer;
      .select_wrap {
        position: absolute;
        width: 15px;
        height: 15px;
        right: -8px;
        bottom: -8px;
        transform: rotateZ(45deg);
        background-color: @themeBackGroundColor;
      }
      .select_wrap::after {
        content: '✔';
        color: #fff;
        position: absolute;
        right: 9px;
        top: -4px;
        font-size: 10px;
        transform: rotateZ(-34deg) scale(0.6);
      }
    }
    .activeDefault {
      border-color: @themeBackGroundColor;
      color: @themeBackGroundColor;
    }
    .type_name_value {
      width: 145px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
    }
  }
  .pc_type_iconDiv {
    display: flex;
    width: 80px;
    font-size: 20px;
    align-items: center;
    justify-content: space-between;
  }
  .pc_type_iconDiv i {
    cursor: pointer;
  }
}
.system_type_item {
  cursor: default;
}
/*选中样式*/
.pc_chosen {
  background: @themeBackGroundColor;
  color: #fff;
  max-height: 600px;
}
.is_active {
  background: @themeBackGroundColor;
  color: #fff;
  .info_wrap {
    .default {
      color: #fff !important;
      border-color: #fff !important;
      .select_wrap {
        background-color: #fff !important;
      }
      .select_wrap::after {
        color: @themeBackGroundColor !important;
      }
    }
  }
}
.pc_type_addType {
  margin: 10px 10px 8px;
  background-color: @themeButtonBackGroundColor;
  font-size: 16px;
  font-weight: 500;
  color: @themeBackGroundColor;
  text-align: center;
  height: 39px;
  width: 300px;
  border-radius: 8px;
  line-height: 36px;
  cursor: pointer;
  span {
    font-size: 24px;
  }
}
.type_mana_wrap {
  position: relative;
  z-index: 800;
  height: 649px;
  margin: 0 auto;
  background: #fff;
  width: 681px;
  overflow: hidden;
  border-radius: 6px;
  color: @themeFontColor;
}
.type_mana_header {
  display: flex;
  justify-content: space-between;
  height: 62px;
  font-size: 18px;
  font-weight: 600;
  line-height: 60px;
  border-bottom: 1px solid #e3e6eb;
  margin: 0 20px;
  .closeIcon {
    font-size: 30px;
    font-weight: 200;
    cursor: pointer;
  }
}
.header_tips {
  font-size: 14px;
  color: @themeBackGroundColor;
  height: 46px;
  line-height: 46px;
  text-align: left;
  padding-left: 20px;
}
.type_list_wrap {
  display: flex;
  justify-content: between-space;
  .list_wrap {
    border: 1px solid #e3e6eb;
    width: 320px;
    height: 462px;
  }
  .type_parent_list {
    margin-left: 20px;
    border-radius: 4px 0 0 4px;
    .parent_add_btn {
      font-size: 20px;
    }
  }
  .type_children_list {
    border-radius: 0 4px 4px 0;
  }
}

.pc_type_addDiv {
  width: 450px;
  height: 547px;
  background-color: #fff;
  margin: 0 auto;
  .closeIcon {
    font-size: 30px;
    font-weight: 200;
    cursor: pointer;
    color: #567485;
  }
}
.pc_type_icon_container_div {
  margin: 10px 0px 10px 30px;
  width: 410px;
  height: 250px;
}
.pc_type_icon_div {
  width: 70px;
  height: 70px;
  display: inline-block;
  margin-right: 32px;
  margin-bottom: 20px;
  cursor: pointer;
  border: 1px solid #fff;
  border-radius: 4px;
}
.pc_type_icon_div img {
  border: 1px solid #fff;
}
.icon_choose {
  background-color: #cfa26b, linear-gradient(#cfa26b, #cfa26b) !important;
  background-blend-mode: lighten;
  color: #fff;
}
.icon_choose img {
  border: 1px solid #cfa26b !important;
  border-radius: 4px;
}
.pc_type_add_cancle {
  margin-left: 160px;
  width: 120px;
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  color: @themeBackGroundColor;
  border: 1px solid @themeBackGroundColor;
  line-height: 46px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
  margin-top: 40px;
}
.pc_type_add_submit {
  margin-left: 20px;
  width: 120px;
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  background-color: @themeBackGroundColor;
  color: #fff;
  line-height: 46px;
  text-align: center;
  cursor: pointer;
  display: inline-block;
}
.draggable_div {
  max-height: 400px;
  overflow-y: auto;
}
.saveTypeManage {
  width: 400px;
  height: 44px;
  text-align: center;
  line-height: 40px;
  background: @themeBackGroundColor;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  margin: 15px auto 0;
}
#addType {
  color: @themeBackGroundColor;
}
.com_pmcu22 {
  overflow: hidden;
  margin-top: 35px;
  font-size: 16px;
  letter-spacing: 4px;
}
.com_pmcu25 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
}
.com_pmcu26 {
  width: 372px;
  height: 217px;
  background: #fff;
  margin: 0 auto;
  margin-top: 115px;
  position: relative;
  border-radius: 4px;
}
.com_pmcu27 {
  height: 85px;
  line-height: 85px;
  text-align: center;
  font-size: 18px;
  color: #567485;
}
.com_pmcu32 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  margin-left: 92px;
  float: left;
  border-radius: 4px;
  text-align: center;
  font-size: 24px;
  letter-spacing: 0px;
  cursor: pointer;
  background: #567485;
  color: #fff;
}
.com_pmcu33 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  border: 1px solid #bda16a;
  text-align: center;
  margin-left: 14px;
  background: #bda16a;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 24px;
  letter-spacing: 0px;
}
</style>
