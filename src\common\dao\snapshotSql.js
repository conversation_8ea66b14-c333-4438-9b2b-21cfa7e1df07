global.snapshotApi = {
  saveSnapshot:
    `CREATE TABLE if not exists "snapshot" (
      "page_name" text NOT NULL PRIMARY KEY,
      "info" text NOT NULL
    );
    REPLACE INTO snapshot(page_name,info) VALUES ('{pageName}','{info}');`,
  searchSnapshot:
    `CREATE TABLE if not exists "snapshot" (
      "page_name" text NOT NULL PRIMARY KEY,
      "info" text NOT NULL
    );
    select page_name,info from snapshot where page_name='{pageName}';`
};
