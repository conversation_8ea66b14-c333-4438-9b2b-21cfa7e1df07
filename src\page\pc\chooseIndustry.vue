<style lang='less' scoped>
.choose_industry_container{
  background: #f2f5f7;
  height: 100%;
  .industry_img_div {
    padding-top: 20px;
    display: flex;
    justify-content: center;
  }
  .industry_title_img {
    width: 400px;
    height: 193px;
    margin: 0 auto;
  }
  .industry_module_content {
    width: 1020px;
    height: 316px;
    display: flex;
    justify-content: space-around;
    margin: 0 auto;
  }
  .industry_module_div {
    width: 249px;
    height: 314px;
    border-radius: 16px;
    background: #fff;
    img {
      margin: 24px 84px;
      width: 80px;
      height: 80px;
      -webkit-filter: drop-shadow(-3px 8px 10px #E4D1a5);
      filter: drop-shadow(-3px 8px 10px #E4D1a5);
    }
    hr {
      width: 48px;
      color: #E4ECF7;
    }
    .industry_module_btn {
      margin: 10px auto;
      width: 134px;
      height: 39px;
      border: 1px solid #b4995a;
      color: #b4995a;
      border-radius: 6px;
      cursor: pointer;
      text-align: center;
      line-height: 38px;
    }
  }
}
</style>
<template>
  <div>
    <div
      v-show="show_loading"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 1000;
        background-color: hsla(0, 0%, 100%, .5);display: flex;justify-content:center;align-items: center;"
    >
      <div><img src="@/image/pc_cloud_loading.gif" alt="" style="width: 40px;"></div>
      <div style="font-size: 24px;color: #567485;">正在下载业态包,请稍候...</div>
    </div>
    <div class="choose_industry_container">
      <div class="industry_img_div">
        <img class="industry_title_img" src="@/image/logo_title.png">
      </div>
      <div>
        <div class="industry_module_content">
          <div class="industry_module_div" v-for="(module, index) in moduleArr" v-show="module.subName !== currentIndustry" :key="index">
            <img :src="module.ico" />
            <div style="margin: 0px auto;text-align: center;font-size:24px;font-weight: 600;">
              {{module.name}}
            </div>
            <hr />
            <div style="width: 180px;padding: 3px;font-size: 14px;line-height: 24px;text-align:center;margin: 0 auto;">
              {{module.content}}
            </div>
            <div class="industry_module_btn" @click="cutIndustry(module)">
              选择此版本&nbsp;&nbsp;→
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data () {
    return {
      moduleArr: [],
      currentIndustry: '',
      show_loading: false
    };
  },
  created() {
    this.loadModuleList();
    this.currentIndustry = $config.subName;
  },
  methods: {
    ...mapActions([SET_SHOW]),
    cutIndustry(industry) { // 切换业态
      console.log(industry);
      if (!pos.network.isConnected()) {
        demo.msg('warning', '请检查网络是否连接!');
        return;
      }
      this.show_loading = true;
      setTimeout(() => {
        external.switchIndustry(industry.className, industry.classUrl, () => {
          this.show_loading = false;
        });
      }, 200);
    },
    loadModuleList() {
      demo.$http
        .get(this.$rest.industry)
        .then(res => {
          if (res.data.code === 200) {
            this.moduleArr = res.data.data;
            console.log(this.moduleArr, 'this.moduleArr');
          } else {
            demo.msg('warning', res.data.msg);
          }
          console.log(res, '模块列表');
        })
        .catch(error => { console.error(error); });
    }
  },
  computed: mapState({
    ultimate: state => state.show.ultimate
  })
};
</script>
