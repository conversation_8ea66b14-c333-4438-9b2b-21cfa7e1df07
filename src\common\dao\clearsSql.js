﻿global.clearsSql = {
  replaceClearId:
    `insert into settings (key, value, remark)
    select '{0}', '{1}', '{2}'
    where not exists (select 1 from settings where key='{0}');
    update settings set value='{1}' where key='{0}';`,

  delClerks:
    `delete from clerks;
    update sqlite_sequence set seq=0 where name='clerks';`,
  delShiftHistories:
    `delete from shifthistories;
    update sqlite_sequence set seq=0 where name='shifthistories';`,

  delSuppliers:
    `delete from suppliers;
    update sqlite_sequence set seq=0 where name='suppliers';
    insert into suppliers(
      name, contacter, tel, mobile, birthday, wechat, qq, mail, addr, postcode, init_amt, cur_amt, disc,
      remark, is_deleted, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
    ) values (
      '零散供应商', '', '', '', '', '', '', '', '', '', 0.0, 0.0, 1.0, NULL, 0, 1, 'a812adcad0da5076388a48e6866258bd',
      NULL, NULL, '2000-01-01 00:00:00', '2000-01-01 00:00:00', '2000-01-01 00:00:00'
    ), (
      '零售客户', '', '', '', '', '', '', '', '', '', 0.0, 0.0, 1.0, NULL, 0, 1, '3380922606ba526531c21de524fd584d',
      NULL, NULL, '2000-01-01 00:00:00', '2000-01-01 00:00:00', '2000-01-01 00:00:00'
    ), (
      '批发客户', '', '', '', '', '', '', '', '', '', 0.0, 0.0, 1.0, NULL, 0, 1, '7959f6f599737617774d304e323e6927',
      NULL, NULL, '2000-01-01 00:00:00', '2000-01-01 00:00:00', '2000-01-01 00:00:00'
    );`,
  delPurchases:
    `delete from purchases;
    update sqlite_sequence set seq=0 where name='purchases';
    delete from purchase_items;
    update sqlite_sequence set seq=0 where name='purchase_items';`,
  delSales:
    `delete from sales;
    update sqlite_sequence set seq=0 where name='sales';
    delete from sale_items;
    update sqlite_sequence set seq=0 where name='sale_items';
    delete from sale_blend_pays;
    update sqlite_sequence set seq=0 where name='sale_blend_pays';`,
  delInventories:
    `delete from inventories;
    update sqlite_sequence set seq=0 where name='inventories';
    delete from inventory_items;
    update sqlite_sequence set seq=0 where name='inventory_items';`,
  delRecordBills:
    `delete from record_bills;
    update sqlite_sequence set seq=0 where name='record_bills';`,
  delGoodsSuppliers:
    `delete from goods_suppliers;
    update sqlite_sequence set seq=0 where name='goods_suppliers';`,
  delCurStock:
    `update goods set cur_stock=0;`,
  delGoodsAttributes:
    `delete from goods_attributes;
    update sqlite_sequence set seq=0 where name='goods_attributes';`,
  delGoodsExtBarcode:
    `delete from goods_ext_barcode;
    update sqlite_sequence set seq=0 where name='goods_ext_barcode';`,
  delSnapshot:
    `delete from snapshot where page_name = '{pageName}';`,
  delGoods:
    `delete from goods;
    update sqlite_sequence set seq=0 where name='goods';`,
  delImages:
    `delete from images;
    update sqlite_sequence set seq=0 where name='images';`,
  delTypes:
    `delete from types;
    update sqlite_sequence set seq=0 where name='types';`,
  initTypes:
    `insert into types(
      name, pinyin, sortno, icon, is_deleted, is_synced,
      parent_fingerprint, fingerprint, create_at, revise_at, sync_at
    )
    values(
      {name}, {pinyin}, {sortNo}, {icon}, {isDel}, 1,
      {parentSyncG}, {syncG}, {createAt}, {reviseAt}, {syncAt}
    );`,
  delUnits:
    `delete from units;
    update sqlite_sequence set seq=0 where name='units';`,
  initUnits:
    `insert into units(
      name, pinyin, is_deleted, is_synced, fingerprint, create_at, revise_at, sync_at
    )
    values(
      {name}, {pinyin}, {isDel}, 1, {syncG}, {createAt}, {reviseAt}, {syncAt}
    );`,
  delScale: `delete from product_scale;
    update sqlite_sequence set seq=0 where name='product_scale';
    delete from scale_list;
    update sqlite_sequence set seq=0 where name='scale_list';
    delete from sendscale;
    update sqlite_sequence set seq=0 where name='sendscale';
    delete from sendscale_history;
    update sqlite_sequence set seq=0 where name='sendscale_history';
    delete from sendscale_product;
    update sqlite_sequence set seq=0 where name='sendscale_product';`,
  delSpecs:
    `delete from specs;
    update sqlite_sequence set seq=0 where name='specs';`
};
