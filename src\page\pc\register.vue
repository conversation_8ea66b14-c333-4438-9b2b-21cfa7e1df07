<style lang='less' scoped>
.pc_reg {
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
}
.pc_reg1 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @themeFontColor;
  font-family: 'HarmonyOSSansSC', sans-serif;
}
.pc_reg1 img {
  float: left;
}

.pc_reg12 {
  overflow: hidden;
  display: flex;
  align-items: center;
}
/deep/.el-input__inner {
  border: 0px;
  width: 400px;
  height: 44px;
  background: @input_backgroundColor;
  font-size: 16px;
  border-radius: 6px;
  color: @themeFontColor;
  padding-left: 22px;
  font-weight: normal;
}
.pc_reg12 div {
  width: 90px;
  line-height: 60px;
  float: left;
  font-weight: 700;
  color: @themeFontColor;
  font-size: 16px;
}

.pc_reg12 input {
  width: 400px;
  height: 44px;
  background-color: rgba(245, 248, 251, 100);
  border-radius: 6px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  color: @themeFontColor;
}

.pc_reg13 {
  font-size: 14px;
  font-weight: bold;
  margin-left: 70px;
  line-height: 14px;
  margin-top: 30px;
}

.pc_reg13 span {
  cursor: pointer;
}

.pc_reg14 {
  width: 165px;
  height: 135px;
  background-image: linear-gradient(90deg, #D8B774 0%, #DEB071 100%);
  border-radius: 10px;
  text-align: center;
  line-height: 135px;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
}

.pc_reg15 {
  margin-top: 25px;
  cursor: pointer;
}

.pc_reg15 img {
  float: left;
}

.pc_reg15 div {
  float: left;
  margin-left: 10px;
  line-height: 14px;
  font-size: 14px;
  font-weight: bold;
  margin-top: 5px;
}

.pc_reg2 {
  font-size: 14px;
  line-height: 30px;
  color: #333;
}

.pc_reg3 {
  font-size: 14px;
  line-height: 30px;
  color: #333;
  margin-top: 5px;
}

.pc_reg4 {
  width: 100%;
  margin-top: 15px;
  height: 48px;
  line-height: 46px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: linear-gradient(90deg, #69d6fe, #16aaff);
  border-radius: 6px;
  cursor: pointer;
}

.pc_van_botton {
  width: 140px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 6px;
  font-size: 16px;
  color: #ffffff;
  margin-left: 17px;
}
.pc_van_botton_next {
  width: 140px;
  height: 60px;
  border-radius: 6px;
  font-size: 16px;
  color: #ffffff;
  margin-left: 17px;
}
.nextBtnEnable {
  background: #BBBFC4
}
.nextBtnDisable {
  background: @themeFocusBorderColor;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
  -webkit-appearance: none !important;
}
input::-webkit-input-placeholder {
  color: rgba(177, 195, 205, 100);
}
.register_top{
  position: absolute;
  top: 30px;
  right: 0;
  display: flex;
  align-items: center;
}
.top_btn{
  width: 100px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @themeBackGroundColor;
  font-weight: bold;
  font-size: 14px
}
.register_bottom{
  position: absolute;
  bottom: 30px;
  right: 30px;
  color: @themeBackGroundColor;
  font-size: 16px;
}
.qr_container{
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
}
.btn_disabled {
  background: #ccc;
  pointer-events: none;
}
.declaration {
  margin-left: 5px;
  color: @themeBackGroundColor;
  font-weight: bold;
  cursor: pointer;

}
.pc_step_header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  margin-bottom: 50px;
}
.current_step_div {
  color: @themeBackGroundColor;
  font-weight: 600;
}
.pc_circle_stepNumber {
  width: 28px;
  height: 28px;
  border-radius: 14px;
  margin-right: 10px;
  color: #fff;
  text-align: center;
  line-height: 28px;
  display: inline-block;
  background: @themeBackGroundColor;
}
.icon-style {
  margin-top: 20px;
  width: 20px;
  height: 20px;
  cursor: pointer;
}
#container {
  color:  @themeBackGroundColor;
}
#vanRow {
  color: @themeFontColor;
}
.accountUseTips {
  color: @themeBackGroundColor !important;
}
</style>
<template>
  <div class="pc_reg">
    <div class="pc_reg1">
      <!--右侧手机号密码-->
      <div
        style="margin-left: 50px;"
      >
        <!-- <div class="pc_step_header">
          <div class="current_step_div">
            <div class="pc_circle_stepNumber">1</div>
            <span style="">完善账户信息</span>
          </div>
          <div style="width: 100px;height: 1px;background: #E1E1E1;">
          </div>
          <div>
            <div class="pc_circle_stepNumber" style="color: #567485;background: #f2f3f5;">2</div>
            <span>完善店铺信息</span>
          </div>
        </div> -->
        <div
          class="pc_reg12"
        >
          <div @click="debugB = true"><span style="color:red">*</span>店主</div>
          <input
            v-model.trim="username"
            type="text"
            maxlength="12"
            style="width: 400px;"
            @input="username = username.replace(/[\$';:]/g, '');"
            placeholder="为便于沟通建议输入真实姓名"
          />
        </div>
        <div
          class="pc_reg12"
        >
          <div><span style="color:red">*</span>店铺名</div>
          <input
            v-model.trim="store_name"
            @input="store_name = store_name.replace(/[\$';:]/g, '');"
            maxlength="12"
            style="width: 400px;"
            placeholder="请输入不超过12个字的门店名称"
          />
        </div>
        <div
          class="pc_reg12"
        >
          <div><span style="color:red">*</span>所在地区</div>
          <el-cascader
            placeholder="试试搜索：北京"
            :options="areaList"
            style="width: 392px;"
            clearable
            @change="areaChange"
            v-model="selectedOption"
            filterable />
        </div>
        <div
          class="pc_reg12"
        >
          <div><span style="color:red">*</span>详细地址</div>
          <input
            v-model.trim="store_addr"
            @input="store_addr = store_addr.replace(/[\$';:]/g, '');"
            maxlength="50"
            style="width: 400px;"
            placeholder="请输入详细地址"
          />
        </div>
        <hr style="border: 1px solid #F7F8FE;"/>
        <div v-if="authorized" class="pc_reg12">
          <div><span style="color:red">*</span>手机号</div>
          <input
            v-model="encryptPhone"
            placeholder="请输入手机号"
            style="width: 233px;float: left;"
            maxlength="11"
            disabled
          />
          <div style="width: 136px;height: 44px;color: #fff; background: #bda169;border-radius: 6px;
            text-align: center;line-height: 44px;cursor: pointer;margin-left: 30px;"
            @click="authorized = !authorized">
            使用其他手机号
          </div>
        </div>
        <div v-if="!authorized" class="pc_reg12">
          <div><span style="color:red">*</span>手机号</div>
          <input
            v-model="phoneno"
            placeholder="请输入手机号"
            style="float: left;"
            v-input-phone
            maxlength="11"
          />
        </div>
        <div
          v-if="!authorized"
          class="pc_reg12"
          style="margin-top: 2px;"
        >
          <div><span style="color:red">*</span>验证码</div>
          <input
            v-model.trim="verifycode"
            placeholder="请输入验证码"
            style="width: 240px;"
            @input="verifycode = $allNumberLimit(verifycode)"
            maxlength="6"
          />
          <van-button
            slot="button"
            type="default"
            :class="['pc_van_botton', btn_disabled ? 'btn_disabled' : '']"
            @click="getverifycode()"
          >{{code_text}}</van-button>
        </div>
        <div
          class="pc_reg12"
          style="margin-top: 2px;"
        >
          <div><span style="color:red">*</span>密码</div>
          <div>
            <el-input
              v-model.trim="password"
              :type="passwordType"
              maxlength="20"
              style="width: 400px;"
              placeholder="由数字、字母组成（长度为8～20个字符）"
            >
              <img
                slot="suffix"
                class="icon-style"
                :src="pwdElIcon"
                @click="showPassWord = !showPassWord"
              />
            </el-input>
            <div class="accountUseTips" style="width: 400px;font-size: 14px;height: 24px;line-height: 20px;font-weight: normal;">
              <span>手机号和密码可用于账号登录模式时使用</span>
            </div>
          </div>
        </div>
        <div
          class="pc_reg12"
          style="margin-top: 2px;"
        >
          <div>激活码</div>
          <input
            v-model.trim="activation_code"
            maxlength="50"
            style="width: 400px;"
            placeholder="请输入有效激活码（非必填）"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          />
        </div>
        <div style="margin-top: 10px;margin-left: 90px;">
          <div>
            <img
              @click="checked_reg = !checked_reg"
              v-show="!checked_reg"
              style="cursor: pointer;"
              src="../../image/zgzn-pos/pc_goods_checkbox1.png"
              alt=""
            />
            <img
              @click="checked_reg = !checked_reg"
              v-show="checked_reg"
              style="cursor: pointer;"
              src="../../image/zgzn-pos/pc_goods_checkbox2.png"
              alt=""
            />
            <div style="color: #4C567C;">&nbsp;&nbsp;&nbsp;已阅读并同意
              <a
                class="declaration"
                :href="$t('page.register.declaration_url')"
                target="_blank"
              >{{$t('page.register.declaration')}}</a>
                <a
                class="declaration"
                :href="$t('page.register.register_url')"
                target="_blank"
              >{{$t('page.register.register')}}</a>
            </div>
          </div>
        </div>
        <div style="margin-top:15px;margin-left:56px;display: flex;justify-content: space-between;align-items: center;">
          <div
            @click="backToLogin(true)"
            style="background-color: rgba(245, 248, 251, 100);width: 140px;height:52px;font-size: 20px;
              border-radius: 6px;text-align: center;line-height: 52px;margin-left: 35px;cursor: pointer;">
            返回登录页
          </div>
          <div
            @click="doRegisterCheck"
            class="pc_van_botton_next"
            :class="nextDisabled ? 'nextBtnEnable' : 'nextBtnDisable'"
            style="border: none;width: 240px;height: 52px;margin-left: 20px;font-size: 20px;color: #FFF;
              border-radius: 6px;text-align: center;line-height: 52px;cursor: pointer;">
            下一步
          </div>
        </div>
        <div
          v-if="loading_show"
          class="load_cover"
          style="position: fixed;top: 50%;left: 50%;">
          <van-loading size="24px" vertical>加载中...</van-loading>
        </div>
      </div>
    </div>
    <div class="register_top">
      <el-popover
        placement="bottom-start"
        width="224"
        trigger="click"
      >
        <div class="qr_container">
          <div id="container" style="font-size: 16px;margin-top:6px;text-align:center;height: 28px;">
            {{$t('components.header.customer_service')}}
          </div>
        </div>
        <div
          slot="reference"
          class="top_btn"
          style="border:none;margin-right:44px;cursor:pointer"
        >
          <img
            src="@/image/zgzn-pos/icon_customer_service.png"
            style="width:20px;height:20px;margin-right:6px"
            alt=""
          >
          <div>联系客服</div>
        </div>
      </el-popover>
    </div>
    <div class="register_bottom" @click="debugC = true">Ver{{version}}</div>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { areaList } from '@/utils/area.js';
import logList from '@/config/logList';
import { Row, Col, Field, Button, Cell, CellGroup, Loading, CountDown } from 'vant';
import { Popover } from 'element-ui';

export default {
  components: {
    [Field.name]: Field,
    [Row.name]: Row,
    [Col.name]: Col,
    [Button.name]: Button,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Loading.name]: Loading,
    [CountDown.name]: CountDown,
    [Popover.name]: Popover
  },
  data () {
    return {
      loading_show: false,
      phoneno: '',
      encryptPhone: '',
      verifycode: '',
      username: '',
      password: '',
      showPassWord: false,
      checked_reg: false,
      doRegisterClick: false,
      code_text: '获取验证码',
      count: 60,
      btn_disabled: true,
      activation_code: '',
      debugA: false,
      debugB: false,
      debugC: false,
      version: '',
      authorized: false,
      // addstore
      areaList: areaList,
      selectedOption: [],
      store_name: '',
      arr: [],
      store_addr: ''
    };
  },
  created () {
    this.version = this.$rest.version;
    this.checkQkdAuthorize();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    areaChange(arr) {
      this.arr = arr;
    },
    checkQkdAuthorize() { // 小程序授权后注册
      if (Object.keys(this.qkdMqttInfo).length !== 0) { // 授权注册
        this.authorized = true;
        let tmpPhone = this.qkdMqttInfo.wxPhone + '';
        this.encryptPhone = tmpPhone.substring(0, 3) + '****' + tmpPhone.substring(7, 11);
        this.username = this.qkdMqttInfo.name;
      }
    },
    getverifycode () { // 获取手机验证码
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      if (!this.chkPhoneno()) {
        demo.msg('warning', this.$msg.enter_mobile_number);
        return;
      }
      // 当前手机号是否已注册
      this.$http
        .post(this.$rest.pc_checkUserIsExit, {
          subName: $config.subName,
          phone: this.phoneno
        })
        .then(res => {
          if (res.data.code === 200) {
            if (res.data.data) { // 用户存在
              demo.msg('warning', '用户已存在，请勿重复注册!');
            } else {
              var params = {
                phone: this.phoneno,
                systemName: $config.systemName,
                subName: $config.subName,
                flag: 0
              };
              this.$http
                .post(this.$rest.sendVerifyCode, params)
                .then(res => {
                  if (res.data.code !== 200) {
                    demo.msg('warning', res.data.msg);
                  } else {
                    demo.msg('success', '验证码发送成功，请注意查收');
                    this.counDownTimer();
                  }
                })
                .catch(error => {
                  console.error(error);
                });
            }
          } else {
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(error => {
          console.error(error);
        });
    },
    /**
     * 倒计时UI效果
     */
    counDownTimer () {
      if (this.count === 1) {
        this.code_text = '重新发送';
        this.btn_disabled = false;
        this.count = 60;
      } else {
        this.count--;
        this.code_text = '已发送（' + this.count + '）';
        this.btn_disabled = true;
        setTimeout(() => {
          this.counDownTimer();
        }, 1000);
      }
    },
    checkPassword () { // 密码由8位以上数字，字母
      // const re = new RegExp(`(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,20}$`);
      const re = new RegExp(`^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9]{8,20}$`);
      return re.test(this.password);
    },
    chkPhoneno () { // 验证手机号
      return (/^1[3|4|5|6|7|8|9]\d{9}$/.test(this.authorized ? this.qkdMqttInfo.wxPhone : this.phoneno));
    },
    // 新版注册
    doRegisterCheck() { // 注册前校验
      // 防连点
      if (this.doRegisterClick) {
        return;
      }
      this.doRegisterClick = true;
      setTimeout(() => {
        this.doRegisterClick = false;
      }, this.clickInterval);
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      if (!this.checked_reg) {
        demo.msg('warning', this.$msg.read_and_confirm_privacy_statement);
        return;
      }
      if (!this.chkPhoneno()) {
        demo.msg('warning', this.$msg.enter_mobile_number);
        return;
      }
      if (!this.authorized && this.verifycode == '') {
        demo.msg('warning', this.$msg.enter_verification_code);
        return;
      }
      if (!this.authorized && this.verifycode.length < 6) {
        demo.msg('warning', this.$msg.right_length_verification_code);
        return;
      }
      if (this.username === '') {
        demo.msg('warning', this.$msg.enter_user_name);
        return;
      }
      if (!this.checkPassword()) {
        demo.msg('warning', this.$msg.enter_password);
        return;
      }
      this.addStoreCheck();
    },
    addStoreCheck() { // 店铺相关校验
      if (this.store_name.trim() === '') {
        demo.msg('warning', this.$msg.enter_store_name);
        return;
      }
      if (this.store_addr.trim() === '') {
        demo.msg('warning', this.$msg.enter_store_address);
        return;
      }
      if (this.arr.length === 0) {
        demo.msg('warning', '请选择省市区！');
        return;
      }
      if (Object.keys(this.qkdMqttInfo).length !== 0) {
        let cp = _.cloneDeep(this.qkdMqttInfo);
        cp.sysUid = this.phoneno;
        this.SET_SHOW({ qkdMqttInfo: cp });
      }
      this.registerNew();
      // if (this.authorized) { // 小程序授权用户跳过验证码校验
      //   this.registerNew();
      // } else {
      //   this.verifyCodeCheck();
      // }
    },
    // 验证码校验
    // verifyCodeCheck() {
    //   let verData = {
    //     phone: this.phoneno,
    //     code: this.verifycode,
    //     systemName: $config.systemName,
    //     subName: $config.subName,
    //     flag: '0'
    //   };
    //   this.$http.post(this.$rest.checkVerifyCode, verData)
    //     .then(res => {
    //       if (res.data.code === 200) {
    //         // 调用setting模块的新版注册，由后端统一执行各模块初始化表操作
    //         this.registerNew();
    //       } else {
    //         demo.msg('warning', res.data.msg);
    //       }
    //     })
    //     .catch(err => {
    //       console.log(err, '验证码校验异常');
    //     });
    // },
    // 新版注册
    registerNew () {
      let params = {
        shopName: this.store_name,
        code: this.verifycode,
        flag: 0,
        subName: $config.subName,
        systemName: $config.systemName,
        userName: this.username,
        industry: '零售杂货',
        mode: '',
        addr: this.arr.join('@') + '@' + this.store_addr,
        phone: this.authorized ? this.qkdMqttInfo.wxPhone : this.phoneno,
        password: md5(this.password),
        netid: 'off',
        contact: '',
        discountSettings: {'discount': [{'number': '9.8'}, {'number': '9.5'}, {'number': '9'}, {'number': '8.8'}],
          'reduce': [{'number': '0.5'}, {'number': '1'}, {'number': '5'}, {'number': '10'}]},
        appletId: this.qkdMqttInfo.appletId || null,
        wxPhone: this.qkdMqttInfo.wxPhone || null,
        wxOpenId: this.qkdMqttInfo.wxOpenId || null
      }
      if (this.activation_code !== '') {
        params['activeCode'] = this.activation_code
      }
      demo.$http.post(this.$rest.register, params)
        .then(res => {
          if (res.data.code === 200) {
            // settingInit
            this.SET_SHOW({partitionId: res.data.data.id, shopKeepName: this.username, registerPassword: md5(this.password)});
            this.SET_SHOW({ phone: this.authorized ? this.qkdMqttInfo.wxPhone : this.phoneno });
            this.promiseAllSuccess();
            this.SET_SHOW({ sys_sid: res.data.data.sysSid });
          } else {
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(errMsg => {
          demo.msg('error', errMsg);
        });
    },
    promiseAllSuccess() { // 全部初始化完成后处理
      let obj;
      this.SET_SHOW({autoLogin: true});
      if (Object.keys(this.qkdMqttInfo).length !== 0) {
        obj = _.cloneDeep(logList.scanCode);
      } else {
        this.SET_SHOW({showAccountLogin: true});
        obj = _.cloneDeep(logList.register);
      }
      obj.description = obj.description.format($setting.SetupPackge);
      obj.partitionId = this.partitionId;
      demo.actionLog(obj);

      this.loading_show = false;
      this.SET_SHOW({ sys_uid: this.phone });

      demo.$http.post(this.$rest.checkCode, { phone: this.phone })
        .then(res => {
          this.SET_SHOW({showKefu: res.data.data.agentType === 1});
          this.showKefuCheck();
        })
        .catch(() => {
          this.showKefuCheck();
        });
    },
    showKefuCheck() {
      if (this.showKefu && $config.Base.OtherOptions.wecom) { // 非代理用户去添加企业微信
        this.SET_SHOW({ isRegister: false, isAddstore: true });
      } else { // 代理用户直接去登录
        this.SET_SHOW({ isRegister: false, isLogin: true });
      }
    },
    /**
     * 退出或退到登录页
     */
    backToLogin () {
      this.SET_SHOW({ isForgetpwd: false, isLogin: true, isRegister: false });
    },
    debugInit() {
      this.debugA = false;
      this.debugB = false;
      this.debugC = false;
    }
  },
  watch: {
    debugC() {
      if (this.debugA && this.debugB && this.debugC) {
        pos.chrome.debug();
        this.debugInit();
      } else {
        this.debugInit();
      }
    },
    phoneno () {
      this.btn_disabled = !this.chkPhoneno();
    },
    showTipsDialog() {
      if (this.showTipsDialog) {
        external.closeMainForm();
      }
    }
  },
  computed: {
    nextDisabled() {
      var flg;
      if (this.authorized) { // 使用授权手机号
        flg = this.encryptPhone;
      } else { // 使用
        flg = /^1[3-9]\d{9}$/.test(this.phoneno) && this.verifycode.length === 6;
      }
      return !flg || !this.username || !this.checkPassword() || !this.checked_reg || !this.store_name ||
        !this.store_addr || !this.selectedOption.length;
    },
    passwordType() {
      return this.showPassWord ? 'text' : 'password';
    },
    pwdElIcon() {
      return this.showPassWord ? require('@/image/pc_eye_open.png') : require('@/image/pc_eye_close.png');
    },
    ...mapState({
      clickInterval: state => state.show.clickInterval,
      showTipsDialog: state => state.show.showTipsDialog,
      qkdMqttInfo: state => state.show.qkdMqttInfo,
      partitionId: state => state.show.partitionId,
      showKefu: state => state.show.showKefu,
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      uid: state => state.show.uid,
      isAuto: state => state.show.isAuto,
      token: state => state.show.token,
      shopKeepName: state => state.show.shopKeepName,
      phone: state => state.show.phone,
      registerPassword: state => state.show.registerPassword
    })
  }
};
</script>
