<style lang="less" scoped>
/deep/.el-tabs__item {
  color: #B2C3CD;
  font-weight: 700;
  font-size: 16px;
  margin-top: -10px;
}
/deep/.el-checkbox__label {
  color: @themeFontColor;font-size: 16px;
}
/deep/.el-tabs__item.is-active {
  color: #537286;
}
/deep/.el-tabs__active-bar {
  background-color: #537286;
}
.com_pae1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  color: @themeFontColor;
}
/deep/.el-form-item__error {
  font-size: 14px;padding-top: 0;
  /* margin-top: 7px; */
}
/deep/.el-form-item__label {
  color: #B2C3CD;font-size: 18px;font-weight: bold;text-align: left;
}
/deep/.el-form-item__content {
  width: 200px;
}
/deep/.el-checkbox__input {
  line-height: 22px;
}
/deep/.com_pae1 .el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/.com_pae1 .el-input__inner {
  font-size: 16px;
}
.com_pae1 .el-form-item__error {
  font-size: 14px;padding-top: 0;
  /* margin-top: 7px; */
}
.com_pae1 .el-form-item__label {
  color: #B2C3CD;font-size: 18px;font-weight: bold;text-align: left;
}
.com_pae1 .el-form-item__content {
  width: 200px;
}
.com_pae1 .el-checkbox__input.is-checked+.el-checkbox__label {
  color: @themeFontColor;font-size: 18px;
}
.com_pae1 .el-checkbox__label {
  font-size: 18px;
}
.com_pae1 .el-checkbox__input {
  line-height: 22px;
}
.com_pae1 .el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.com_pae1 .el-input__inner {
  font-size: 16px;
}
.com_pae11 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 120;
  top: 0;
  left: 0;
}
.com_pae12 {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 754px;
  height: 96vh;
  margin-top: 8px;
  border-radius: 6px;
  z-index: 150;
  overflow: hidden;
  font-size: 18px;
  #edit_employee_top {
    height: 78vh;
    overflow: scroll;
    width: 100%;
    /deep/.el-form-item {
      float: le;
    }
  }
}
/deep/.el-tabs__nav {
  margin-left: 20px;
}
.authTitle {
  margin-top: 7px;
  margin-left: 50px;
  font-weight: bold;
  color: #B2C3CD;
  display: flex;
  align-items: center;
  .el-icon-question {
    font-size: 20px;
    color: @themeBackGroundColor;
  }
}
.com_pae13 {
  width: 706px;
  margin-left: 24px;
  background: #F9FBFB;
  margin-top: 20px;
  overflow: hidden;
  .authWrap {
    padding: 10px 16px 0;
    .authItem {
      overflow: hidden;
      margin-top: 10px;
    }
  }
}
.com_pae13 .el-form-item__content {
  width: 500px;
}
.com_pae13 .el-checkbox {
  width: 250px;
  margin-top: 17px;
  margin-right: 0px;
}
.authValue {
  float: left;
}
.rightItem {
  width: 170px !important;
}
.com_pae14 {
  width: 120px;height: 44px;line-height: 42px;margin-top: 20px;color: @themeBackGroundColor;
  font-size: 18px;text-align: center;background: #FFF;border-radius: 4px;cursor: pointer;
  border: 1px solid @themeBackGroundColor;float: right;margin-right: 20px;
}
.com_pae15 {
  width: 120px;height: 44px;line-height: 42px;margin-top: 20px;color: #FFF;font-size: 18px;text-align: center;
  background: linear-gradient(90deg, #D2BA8A 0%, #B49A5A 100%);
  border-radius: 4px;cursor: pointer;float: right;margin-right: 50px;
}
.com_pae12 .com_pae16 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: @themeBackGroundColor;
  text-align: center;
  color: #FFFFFF;
  line-height: 20px;
  font-size: 16px;
  float: right;
  margin-top: 3px;
  cursor: pointer;
}
.com_pae17 {
  line-height: 18px;
  font-size: 18px;
  width: 716px;
  border-bottom: 1px solid #E3E6EB;
  overflow: hidden;
  height: 8vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 20px;
  padding-right: 20px;
}
.com_pae18 {
  float: left;font-weight: bold;color: @themeFontColor;
}
.com_pae19 {
  float: right;color: #B2C3CD;font-size: 48px;cursor: pointer;
}
.com_pae2 {
  width: 80px;height: 40px;margin-left: 10px;float: left;background: @themeBackGroundColor;
  color: #FFF;border-radius: 5px;font-size: 16px;text-align: center;line-height: 40px;
}
</style>
<template>
  <div v-if="showAddEmployee === 'new' || showAddEmployee === 'edit'" class="com_pae1">
    <div class="com_pae11"></div>
    <div class="com_pae12">
      <div class="com_pae17">
        <div class="com_pae18" v-show="showAddEmployee === 'new'">新增收银员</div>
        <div class="com_pae18" v-show="showAddEmployee === 'edit'">编辑收银员</div>
        <div class="com_pae19" @click="close()">×</div>
      </div>
      <div id="edit_employee_top" ref="edit_employee_top">
        <div style="overflow: hidden;margin-left: 50px;font-weight: bold;color: #B2C3CD;margin-top: 22px;">
          <div style="float: left;line-height: 16px;font-size: 18px;margin-top: 2px;">是否启用</div>
          <el-switch
            style="margin-left: 20px;float: left;"
            v-model="status"
            active-color="#bda16a"
            inactive-color="#DCDFE6"
          ></el-switch>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="90px" class="demo-ruleForm">
          <div style="margin-top: 36px;overflow: hidden;">
            <el-form-item label="姓名" prop="employeeName" style="float: left;margin-left: 40px;">
              <el-input id="employee_name" v-model="ruleForm.employeeName" v-focus-select="'autoFocus,focusSelect'"
                @input="check('employeeName')" clearable maxlength="15"></el-input>
            </el-form-item>
            <el-form-item label="职务" prop="avatar" style="float: left;margin-left: 38px;padding-left: 12px;">
              <el-input v-model="ruleForm.avatar" v-focus-select="'focusSelect'" @input="check('avatar')"
                clearable maxlength="15" style="margin-left: -12px;"></el-input>
            </el-form-item>
          </div>
          <div style="margin-top: 0px;overflow: hidden;">
            <el-form-item label="工号" prop="employeeNumber" style="float: left;margin-left: 40px;">
              <el-input v-model="ruleForm.employeeNumber" v-focus-select="'focusSelect'" clearable maxlength="15"
                @input="check('employeeNumber')" :disabled="showAddEmployee === 'edit'"></el-input>
            </el-form-item>
            <el-form-item label="密码" prop="password" style="float: left;margin-left: 38px;">
              <el-input id="employee_password" v-model="ruleForm.password" v-focus-select="'focusSelect'" clearable
                v-show="edit_password" maxlength="6" @input="check('password')"></el-input>
              <el-input value="******" disabled style="width: 110px;float: left;" v-show="!edit_password"></el-input>
              <div class="com_pae2" v-show="!edit_password" @click="toEditPassword()">重置</div>
            </el-form-item>
          </div>
          <div style="margin-top: 0px;overflow: hidden;">
            <el-form-item label="联系方式" prop="employeePhone" style="float: left;margin-left: 40px;padding-left: 12px;">
              <el-input v-model="ruleForm.employeePhone" v-focus-select="'focusSelect'" clearable maxlength="11"
                style="margin-left: -12px;" @input="check('employeePhone')"></el-input>
            </el-form-item>
          </div>
          <div class="authTitle">
            <div>权限设置：</div>
            <el-tooltip effect="dark" :offset="-10"
              placement="bottom-start"
              content="如想设置收银员看不到进货价，只需勾选【隐藏商品进价】即可">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="com_pae13">
            <el-tabs v-model="activeName" class="authWrap">
              <el-tab-pane :key="sys.key" v-for="sys in systemList" :label="sys.name" :name="sys.key">
                <div v-for="(aut, index) in authAll[sys.key]" :key="index"
                  class="authItem">
                  <el-checkbox :label="aut.name"
                    v-model="aut.status"
                    @change="changeParentStatus(index)"
                    style="font-weight: bold;"></el-checkbox><br>
                  <el-checkbox
                    v-for="(au, index1) in aut.auth"
                    :key="index1"
                    class="authValue" :class="{ rightItem: index1 % 3 === 2 }"
                    @change="changeChildStatus(index, index1)"
                    :label="au.name === '禁用商品'? '删除商品' : au.name"
                    v-model="au.status">
                  </el-checkbox>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div style="height: 23px;"></div>
          </div>
        </el-form>
      </div>
      <div style="overflow: hidden;height:9vh;">
        <div class="com_pae15" @click="subInfo()">保存</div>
        <div class="com_pae14" @click="close()">取消</div>
        <div class="com_pae14" @click="buttonMsg === '展示更多' ? goDown() : goUp()" style="margin-left: 50px;float: left;">
          <svg v-if="buttonMsg === '展示更多'" width="32" height="32"
            viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 0L32 0L32 32L0 32L0 0Z" fill="white" fill-opacity="0.01"/>
            <path d="M8 8L16 16L24 8" stroke="#BDA169" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 16L16 24L24 16" stroke="#BDA169" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg v-else width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 32L32 32L32 0L0 0L0 32Z" fill="white" fill-opacity="0.01"/>
            <path d="M8 24L16 16L24 24" stroke="#BDA169" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 16L16 8L24 16" stroke="#BDA169" stroke-width="2.66667" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          {{buttonMsg}}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    const validateName = (rule, value, callback) => {
      if (/;|'|\$/.test(value)) {
        return callback(new Error(`员工${rule.field === 'avatar' ? '职务' : '姓名'}不合规`));
      } else {
        callback();
      }
    }
    return {
      systemList: [
        {
          name: '收银端',
          key: 'pc'
        },
        {
          name: '手机端',
          key: 'mobile'
        }
      ],
      status: true,
      buttonMsg: '展示更多',
      ruleForm: {
        employeeName: '',
        employeePhone: '',
        employeeNumber: '',
        password: '',
        auth: ''
      },
      rules: {
        employeeName: [
          { required: true, message: '* 姓名为必填项', trigger: 'blur' },
          { validator: validateName, trigger: 'change' }
        ],
        avatar: [
          { validator: validateName, trigger: 'change' }
        ],
        employeePhone: [
          { min: 11, message: '请输入正确联系方式(手机号)', trigger: 'blur' }
        ],
        employeeNumber: [
          { required: true, message: '* 1-15个字符，数字、字母组成', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '* 1-6个字符，数字、字母组成', trigger: 'blur' }
        ]
      },
      auth: [],
      edit_password: false,
      activeName: 'pc',
      authAll: {},
      editAuth: {}
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    goDown() {
      let mainPage = this.$refs['edit_employee_top'];
      mainPage.scrollTo({
        top: mainPage.scrollHeight
      });
    },
    goUp() {
      let mainPage = this.$refs['edit_employee_top'];
      mainPage.scrollTo({
        top: 0
      });
    },
    scrollHandler() {
      let el = this.$refs['edit_employee_top'];
      const scrollTop = el.pageYOffset || el.scrollTop;
      const scrollHeight = el.scrollHeight;
      const clientHeight = el.clientHeight;
      if (scrollTop + clientHeight >= scrollHeight) {
        this.buttonMsg = '返回顶部';
      } else {
        this.buttonMsg = '展示更多';
      }
    },
    setClass () {
      document.getElementsByClassName('popper__arrow')[0].setAttribute('class', 'popper__arrow pc_pay193');
    },
    check(str) {
      if (str === 'employeeNumber') {
        this.ruleForm.employeeNumber = this.ruleForm.employeeNumber.replace(/[^A-Za-z0-9]/g, '');
      } else if (str === 'password') {
        this.ruleForm.password = this.ruleForm.password.replace(/[^A-Za-z0-9]/g, '');
      } else if (str === 'employeePhone') {
        this.ruleForm.employeePhone = this.ruleForm.employeePhone.replace(/[^0-9]/g, '');
      } else if (str === 'avatar') {
        this.ruleForm.avatar = this.ruleForm.avatar.replace("'", '');
      } else if (str === 'employeeName') {
        this.ruleForm.employeeName = this.ruleForm.employeeName.replace("'", '');
      } else {
        console.log('其他');
      }
    },
    deleteEmployee() {
      var sub_data = {
        'sysSid': this.sys_sid,
        'uid': this.employeeDetail.uid
      };
      var that = this;
      demo.$http.post(this.$rest.deleteEmployeeByUserId, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (res) {
          if (res.data.code === '0') {
            demo.msg('success', '收银员删除成功');
            that.close();
          } else {
            demo.msg('warning', '收银员删除失败');
          }
        })
        .catch(function () {
          demo.msg('warning', '收银员删除失败');
        });
    },
    toEditPassword() {
      this.edit_password = true;
      this.ruleForm.password = '';
      setTimeout(function() {
        $('#employee_password').focus();
      }, 0);
    },
    close() {
      this.buttonMsg = '展示更多';
      this.SET_SHOW({showAddEmployee: 'close'});
    },
    sonarAuthorityList() {
      var list = [];
      for (let t in this.authAll) {
        for (var i = 0; i < this.authAll[t].length; i++) {
          if (this.authAll[t][i].status) {
            list.push({
              functionName: this.authAll[t][i].functionName,
              status: true,
              appName: t
            });
          }
          for (var j = 0; j < this.authAll[t][i].auth.length; j++) {
            if (this.authAll[t][i].auth[j].status) {
              list.push({
                functionName: this.authAll[t][i].auth[j].functionName,
                status: true,
                appName: t
              });
            }
          }
        }
      }
      let privilege = '';
      list.forEach(t => {
        privilege += t.functionName + ',';
      })
      privilege = privilege.substring(0, privilege.length - 1);
      return { list, privilege };
    },
    editAuthorityList() { // 编辑员工权限list
      let obj = {
        pc: [],
        mobile: []
      };
      for (let t in this.authAll) {
        for (var i = 0; i < this.authAll[t].length; i++) {
          if (this.authAll[t][i].status) {
            obj[t].push(this.authAll[t][i].functionName);
          }
          for (var j = 0; j < this.authAll[t][i].auth.length; j++) {
            if (this.authAll[t][i].auth[j].status) {
              obj[t].push(this.authAll[t][i].auth[j].functionName);
            }
          }
        }
      }
      return obj;
    },
    sonarSubData() {
      var data = {
        'employeeName': this.ruleForm.employeeName,
        'employeePhone': this.ruleForm.employeePhone,
        'employeeNumber': this.ruleForm.employeeNumber,
        'password': this.edit_password ? md5(this.ruleForm.password) : this.ruleForm.password,
        'avatar': this.ruleForm.avatar,
        'sysUid': this.sys_uid,
        'sysSid': this.sys_sid,
        'token': ' ',
        'uid': this.showAddEmployee === 'new' ? '' : this.employeeDetail.uid,
        'status': this.status ? 0 : 1,
        'author': this.loginInfo.uid
      };
      if (this.showAddEmployee === 'new') { // 新增员工
        data['authorityList'] = this.sonarAuthorityList().list;
      } else { // 编辑员工
        data['pcAuthorityList'] = this.editAuthorityList().pc;
        data['mobileAuthorityList'] = this.editAuthorityList().mobile;
      }
      return data;
    },
    subInfo() {
      this.$refs['edit_employee_top'].scrollTop = 0;
      var that = this;
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          var sub_data = this.sonarSubData();
          demo.$http.post((this.showAddEmployee === 'new'
            ? this.$rest.addEmployee : this.$rest.updateEmployee), sub_data, {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': this.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
            .then(function (res) {
              if (res.data.code === 200) {
                demo.msg('success', '收银员保存成功');
                that.close();
              } else {
                demo.msg('warning', res.data.msg);
              }
            })
            .catch(function (error) {
              console.error(error);
              demo.msg('warning', '收银员保存失败');
            });
        } else {
          console.log('error submit!');
        }
      });
    },
    changeParentStatus(n) {
      for (var i = 0; i < this.authAll[this.activeName][n].auth.length; i++) {
        this.authAll[this.activeName][n].auth[i].status = this.authAll[this.activeName][n].status;
        // if (this.activeName === 'mobile' && this.authAll[this.activeName][n].functionName === 'orders_auth') {
        //   this.weightedAverageDisabled = !this.authAll[this.activeName][n].status;
        // }
      }
    },
    changeChildStatus(m, n) {
      var mid = true;
      let times = 0;
      let purchaseStatus = false;
      let len = this.authAll[this.activeName][m].auth.length;
      this.setPurChase(m);
      for (var i = 0; i < len; i++) {
        if (!this.authAll[this.activeName][m].auth[i].status) {
          mid = false;
          if (this.authAll[this.activeName][m].auth[i].functionName === 'purchase' ||
          this.authAll[this.activeName][m].auth[i].functionName === 'return_purchase') {
            times++;
          }
        }
        if (this.activeName === 'mobile' && this.authAll[this.activeName][m].auth[i].functionName === 'purchase') {
          purchaseStatus = this.authAll[this.activeName][m].auth[i].status;
          this.authAll[this.activeName][m].auth[i + 2].status = !purchaseStatus ? false
            : this.authAll[this.activeName][m].auth[i + 2].status;
        }
        if (this.activeName === 'mobile' && n === i && this.authAll[this.activeName][m].auth[i].functionName === 'weighted_average' && !purchaseStatus) {
          this.authAll[this.activeName][m].auth[i].status = false;
          demo.msg('warning', '请先赋予进货权限');
        }
      }
      if (times === 2 && this.activeName === 'pc' && n === 2) {
        this.authAll[this.activeName][3].auth[2].status = false;
        demo.msg('warning', '请先赋予进货或者退货权限');
      }
      this.authAll[this.activeName][m].status = mid;
    },
    setPurChase(m) {
      if (this.activeName === 'pc' && this.authAll[this.activeName][m].functionName === 'orders_auth' &&
        !this.authAll[this.activeName][m].auth[0].status && !this.authAll[this.activeName][m].auth[1].status) {
        this.authAll[this.activeName][m].auth[2].status = false;
      }
    },
    getAuthorities() {
      let param = {
        apps: 'pc,mobile',
        authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
      }
      demo.$http.get(this.$rest.getAuthorities, { params: param }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(res => {
          if (res.data.code === 200) {
            console.log(res.data.data, '新权限');
            var arr = res.data.data.mobile;
            arr.forEach(i => {
              i.auth.sort((a, b) => a.id - b.id);
            })
            this.defaultCheck(res.data.data);
          }
        })
        .catch(() => {
          demo.msg('warning', '权限获取失败');
        });
    },
    getUsersAuthorities() {
      let param = {
        apps: 'pc,mobile',
        uids: this.ruleForm.uid,
        all: 1,
        authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
      }
      demo.$http.get(this.$rest.getUsersAuthorities, { params: param }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(res => {
          if (res.data.code === 200) {
            console.log(res.data.data, '员工权限');
            var arr = res.data.data[this.ruleForm.uid].mobile;
            arr.forEach(i => {
              i.auth.sort((a, b) => a.id - b.id);
            })
            this.editAuth = res.data.data[this.ruleForm.uid];
            console.log(this.ruleForm, 'this.ruleForm');
            this.authAll = this.editAuth;
          }
        })
        .catch(() => {
          demo.msg('warning', '权限获取失败');
        });
    },
    defaultCheck(obj) {
      let auth = _.cloneDeep(obj);
      auth.pc.forEach(aut => {
        aut.auth.forEach(au => {
          if (au.functionName === 'show_details' || au.functionName === 'purchase_price') {
            au.status = true;
          }
        });
      });
      auth.mobile.forEach(aut => {
        aut.auth.forEach(au => {
          if (au.functionName === 'purchase_price') {
            au.status = true;
          }
        });
      });
      this.authAll = auth;
    }
  },
  watch: {
    'ruleForm.password'() {
      if (this.ruleForm.password === '') {
        $('#employee_password').focus();
      }
    },
    showAddEmployee() {
      this.activeName = 'pc';
      if (this.showAddEmployee === 'edit') {
        setTimeout(() => {
          this.$refs['edit_employee_top'].addEventListener('scroll', this.scrollHandler);
        }, 0);
        this.edit_password = false;
        this.ruleForm = this.employeeDetail;
        this.status = this.employeeDetail.status === 0;
        this.getUsersAuthorities();
      }
      if (this.showAddEmployee === 'close') {
        this.SET_SHOW({employeeDetail: {}});
        this.auth = [];
        this.authAll = {};
        this.$refs['edit_employee_top'].removeEventListener('scroll', this.scrollHandler);
      }
      if (this.showAddEmployee === 'new') {
        setTimeout(() => {
          this.$refs['edit_employee_top'].addEventListener('scroll', this.scrollHandler);
        }, 0);

        this.edit_password = true;
        this.status = true;
        this.ruleForm = {
          employeeName: '',
          employeePhone: '',
          employeeNumber: '',
          password: '',
          auth: ''
        };
        this.getAuthorities();
      }
    }
  },
  computed: mapState({
    showAddEmployee: state => state.show.showAddEmployee,
    token: state => state.show.token,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    loginInfo: state => state.show.loginInfo,
    employeeDetail: state => state.show.employeeDetail
  })
};
</script>
