import rest from '../../config/rest';
import dao from '../dao/dao';
import stringUtils from '../stringUtils';

const superSyncService = {
  fileName: 'sales.zip',
  tables: ['sales', 'sale_items', 'sale_blend_pays'],

  initial: function(fromSyncAt, toSyncAt, initBigData, current, to) {
    return new Promise((resolve) => {
      const str = JSON.stringify({ startSyncAt: fromSyncAt, endSyncAt: toSyncAt, initBigData, current, to });
      let msg = `[销售单大量数据下载-initial]{0}`.format(str);
      console.log(msg);
      CefSharp.PostMessage(msg);

      let startDate = new Date(fromSyncAt);
      const superKey = `super-{0}`.format(startDate.format('yyyyMM'));
      if (current > to) {
        resolve();
        return;
      }

      if (stringUtils.isBlank(this['token'])) {
        let token = demo.$store.state.show.token;
        if (stringUtils.isBlank(token)) {
          token = $config.token;
        }
        this['token'] = token;
      }
      this[superKey] = {};
      this[superKey]['initBigData'] = initBigData;
      this[superKey]['all'] = [];
      this[superKey]['download'] = [];
      this[superKey]['downloadMonthCount'] = 0;
      this[superKey]['importMonthCount'] = 0;

      let endDate;
      let startSyncAt;
      let endSyncAt;
      for (current; current <= to;) {
        endDate = new Date(toSyncAt);

        if (current === 0) {
          endSyncAt = toSyncAt;
        } else {
          endDate.setDate(1);
          endDate.setMonth(endDate.getMonth() - current + 1);
          endSyncAt = endDate.format('yyyy-MM-01 00:00:00');
          endDate.setMonth(endDate.getMonth() - 1);
        }

        if (startDate.format('yyyyMM') < endDate.format('yyyyMM')) {
          startSyncAt = endDate.format('yyyy-MM-01 00:00:00');
          current++;
        } else {
          startSyncAt = fromSyncAt;
          current = to + 1;
        }

        this[superKey]['all'].push({startSyncAt, endSyncAt});
      }
      msg = `[销售单大量数据下载-initial]{0}`.format(JSON.stringify(this[superKey]));
      console.log(msg);
      CefSharp.PostMessage(msg);
      resolve(superKey);
    });
  },
  /**
   * 文件下载
   * @param {*} superKey
   * @returns
   */
  downloadAllFile: function(superKey) {
    return new Promise(async (resolve, reject) => {
      if (this[superKey]['downloading'] === 1) {
        resolve();
        return;
      }
      const start = new Date();
      let o;
      let msg;
      try {
        this[superKey]['downloading'] = 1;
        const url = rest['down-sales'];
        const len = this[superKey]['all'].length;
        for (let i = 0; i < len; i++) {
          if (+this[superKey]['syncing'] === 0) {
            reject(new Error('[销售单大量数据下载-downloadAllFile]云同步下载失败，大量数据下载停止'));
            return;
          }
          const a = new Date();
          o = this[superKey]['all'][i];
          const directoryName = new Date(o.startSyncAt).format('yyyyMM');
          const str = JSON.stringify(o);
          msg =
            `[销售单大量数据下载-downloadAllFile]{0}-{1} 开始下载 ↓↓↓↓↓↓ {2}`
              .format(superKey, directoryName, str);
          console.log(msg);
          CefSharp.PostMessage(msg);

          const res = await this.downloadFileLoop(url.format(o), this['token'], directoryName);
          if (+res === 1) {
            this[superKey]['downloadMonthCount'] += 1;
            this[superKey]['download'].push(directoryName);
            this.csvImport(superKey).then(() => {
              let downloadMonthCount = this[superKey]['downloadMonthCount'];
              let importMonthCount = this[superKey]['importMonthCount'];
              msg =
              `[销售单大量数据下载-downloadAllFile]{0} 下载月数量={1} 导入月数量={2}`
                .format(superKey, downloadMonthCount, importMonthCount);
              console.log(msg);
              CefSharp.PostMessage(msg);
              if (this[superKey]['downloading'] === 0 && downloadMonthCount === importMonthCount) {
                resolve(res);
              }
            }).catch(err => {
              console.log(err);
              reject(err);
            });
          }

          const b = new Date();
          msg =
            `[销售单大量数据下载-downloadAllFile]{0}-{1} 下载完成 ↑↑↑↑↑↑ 耗时={2}`
              .format(superKey, directoryName, b - a);
          console.log(msg);
          CefSharp.PostMessage(msg);
        };
        this[superKey]['downloading'] = 0;
        const end = new Date();
        msg =
            `[销售单大量数据下载-downloadAllFile] 下载完成 √√√√√√ 总耗时={0}`.format(end - start);
        console.log(msg);
        CefSharp.PostMessage(msg);
      } catch (err) {
        this[superKey]['downloading'] = 0;
        const str = JSON.stringify(o);
        const end = new Date();
        msg =
          `downloadAllFile异常 {0} 耗时={1} 错误={2}`.format(str, end - start, err);
        console.log(msg);
        CefSharp.PostMessage(msg);
        reject(err);
      }
    });
  },
  downloadFileLoop: function(url, token, directoryName) {
    return new Promise((resolve, reject) => {
      csharpHttps.downloadFile(url, token, directoryName, this.fileName, resolve, reject);
    });
  },

  /**
   * 解压
   * @param {*} directoryName
   * @returns
   */
  decompression: function(directoryName) {
    return new Promise((resolve, reject) => {
      sqlitePlugin.decompression(directoryName, this.fileName, resolve, reject);
    });
  },

  /**
   * 导入
   * @param {*} superKey
   * @returns
   */
  csvImport: function(superKey) {
    return new Promise(async (resolve, reject) => {
      try {
        if (this[superKey]['csvImporting'] === 1) {
          resolve();
          return;
        }
        for (;;) {
          const directoryName = this[superKey]['download'].shift();
          if (directoryName === undefined) {
            break;
          }
          await this.csvImportLoop(superKey, directoryName);
        }
        resolve();
      } catch (err) {
        const msg =
          `[销售单大量数据下载-csvImport]{0} 错误={1}`
            .format(superKey, JSON.stringify(err));
        console.log(msg);
        CefSharp.PostMessage(msg);
        reject(err);
      }
    });
  },

  csvImportLoop: function(superKey, directoryName) {
    return new Promise((resolve, reject) => {
      this[superKey]['csvImporting'] = 1;
      let msg =
        `[销售单大量数据下载-csvImportLoop]{0}-{1} 开始解压 ↓↓↓↓↓↓`
          .format(superKey, directoryName);
      console.log(msg);
      CefSharp.PostMessage(msg);
      const start = new Date();
      let a;
      this.decompression(directoryName, this.fileName)
        .then(res => {
          const b = new Date();
          if (+res === 0) {
            msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 无需解压 ↑↑↑↑↑↑ 耗时={2}`
              .format(superKey, directoryName, b - start);
            console.log(msg);
            CefSharp.PostMessage(msg);
            return;
          }
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 解压完成 ↑↑↑↑↑↑ 耗时={2}`
              .format(superKey, directoryName, b - start);
          console.log(msg);
          CefSharp.PostMessage(msg);
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 删除临时表 ↓↓↓↓↓↓`
              .format(superKey, directoryName);
          console.log(msg);
          CefSharp.PostMessage(msg);
          a = new Date();
          return dao
            .asyncTransaction(syncSqlApi.dropTmpSales)
        })
        .then(res => {
          if (res === undefined) {
            return;
          }
          const b = new Date();
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 删除临时表 ↑↑↑↑↑↑ 耗时={2}`
              .format(superKey, directoryName, b - a);
          console.log(msg);
          CefSharp.PostMessage(msg);
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 导入临时表 ↓↓↓↓↓↓`
              .format(superKey, directoryName);
          console.log(msg);
          CefSharp.PostMessage(msg);
          a = new Date();
          return dao
            .asyncImport({ directoryName, fileName: this.fileName, tables: this.tables });
        }).then(res => {
          if (res === undefined) {
            return;
          }
          const b = new Date();
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 导入临时表 ↑↑↑↑↑↑ 耗时={2}`
              .format(superKey, directoryName, b - a);
          console.log(msg);
          CefSharp.PostMessage(msg);
          msg =
            `[销售单大量数据下载-csvImportLoop]{0}-{1} 导入正式表 ↓↓↓↓↓↓`
              .format(superKey, directoryName);
          console.log(msg);
          CefSharp.PostMessage(msg);
          const sql =
            this[superKey]['initBigData'] === true
              ? syncSqlApi.salesImportInit : syncSqlApi.salesImport;
          a = new Date();
          return dao.asyncTransaction(sql);
        })
        .then(res => {
          this[superKey]['importMonthCount'] += 1;
          this[superKey]['csvImporting'] = 0;
          if (res === undefined) {
            return;
          }
          const b = new Date();
          msg =
            `[销售单大量数据下载-导入正式表]{0}-{1} 导入正式表 耗时={2}`
              .format(superKey, directoryName, b - a);
          console.log(msg);
          CefSharp.PostMessage(msg);
          msg =
            `[销售单大量数据下载]{0}-{1} √√√√√√ 总耗时={2}`.format(superKey, directoryName, b - start);
          console.log(msg);
          CefSharp.PostMessage(msg);
          resolve();
        })
        .catch(err => {
          this[superKey]['download'] = 1;
          const end = new Date();
          msg =
            `[销售单大量数据下载-downloadAllFile]{0}-{1} ×××××× 耗时={2} 错误={3}`
              .format(superKey, directoryName, end - start, JSON.stringify(err));
          console.log(msg);
          CefSharp.PostMessage(msg);
          reject(err);
        });
    });
  },

  /**
   * 是否是灰度用户
   */
  isGrayscale: function() {
    return new Promise(resolve => {
      demo.$http
        .get(rest.isSyncGrayscale, {
          params: { type: 'sync' }
        })
        .then(res => {
          if (+res.status !== 200 || +res.data.code !== 200) {
            const msg = `[销售单大量数据下载-isGrayscale]res = {0}`.format(JSON.stringify(res));
            console.log(msg);
            CefSharp.PostMessage(msg);
            resolve(false);
            return;
          }
          resolve(res.data.data);
        })
        .catch(err => {
          const msg = `[销售单大量数据下载-isGrayscale]err = {0}`.format(JSON.stringify(err));
          console.error(msg);
          CefSharp.PostMessage(msg);
          resolve(false);
        });
    });
  }

};

window.superSyncService = superSyncService;
export default superSyncService;
