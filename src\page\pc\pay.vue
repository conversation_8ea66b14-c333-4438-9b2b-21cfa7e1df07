<style scoped="scoped" lang="less">
@import "../../assets/pay.less";
.pc_type_div {
  width: 110px;
  position: fixed;
  right: -2px;
  top: 192px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 285px);
  z-index: 2;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 0 0 4px 4px;
}
#pc_pay170 {
  background: @themeButtonBackGroundColor;
  color: @themeBackGroundColor;
  border: 1px solid @themeBackGroundColor;
}
#pc_pay170_1 {
  background: @themeBackGroundColor;margin-left: 10px;
}
.pc_pay131_top {
  text-align: center;
  padding: 15px 20px 0 20px;
}
.pc_pay78_1 {
  // width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.addMember {
  width: 130px;
  height: 44px;
  border-radius: 2px;
  color: #FFF;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  font-weight: bold;
  float: left;
  cursor:pointer;
}
#sale {
  border-color: @themeBackGroundColor;
  color: @themeBackGroundColor;
}
.editor {
  color: @themeBackGroundColor;
  margin-left: 5px;
}
#saveEdit {
  margin-left: 20px;
  background: @themeBackGroundColor;
}
#toMemberCard {
  float: left;
  color:  @themeBackGroundColor;
  cursor: pointer;
  margin-right: 10px;
}
#toMemberDetail {
  float: left;
  color: @themeBackGroundColor;
  cursor: pointer;
}
.parent_menu_active_div {
  background: @linearBackgroundColor !important;
  width: 110px;
  margin-left: -10px;
  padding-left: 10px;
  height: 56px;
  border-bottom: 1px solid #fff;
}
#pay116 {
  color:  @themeBackGroundColor;
}

#submitNewGoods {
  background: @themeBackGroundColor;
}
#fontColor {
  color: @themeFontColor;
}
.confirm-container {
  display: flex;
  align-items: center;
  margin-top: 40px;
}
.lock-btn-confirm {
  margin-left: 10px;
  width: 110px;
  text-align: center;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  cursor: pointer;
}
</style>
<template>
  <div class="pc_pay">
    <!-- 新版管理 -->
    <v-SelManage></v-SelManage>
    <!-- 新增商品 -->
    <v-AddGoods></v-AddGoods>
    <!--会员充值弹框-->
    <v-MemberRecharge></v-MemberRecharge>
    <!--弹出结算界面的计算器-->
    <v-FinalPay></v-FinalPay>
    <!-- 会员积分兑换商品 -->
    <v-MemberPE></v-MemberPE>
    <!-- 新增会员 -->
    <v-AddMember></v-AddMember>
    <!-- 次卡模态框 -->
    <v-VipTimesCard></v-VipTimesCard>
    <!-- 重复商品选择 -->
    <v-RepeatGoodsChoose
      :repeatData="repeatData"
      :repeatCode="repeatCode"
      @repeatChooseEmit="repeatChoose">
    </v-RepeatGoodsChoose>
    <div v-show="show_database_list == true" class="pc_pay122">
      <div class="pc_pay120" @click="show_database_list = false"></div>
      <div class="pc_pay174">
        <div class="pc_pay174_top">
          <el-input
            placeholder="输入挂单备注或会员手机号"
            v-model="databaseListKeyword"
            clearable
            style="font-size:16px;width: calc(100% - 40px);margin-left: 20px;margin-top: 15px;float: left;border-radius: 20px;"
          >
          </el-input>
        </div>
        <div style="height: 70px;"></div>
        <div class="pc_pay175"
          id="base-main"
          v-infinite-scroll="loadMore"
          :infinite-scroll-disabled="loading"
          :infinite-scroll-immediate="true">
          <div v-for="(dat, index) in database_list" :key="index">
            <div class="pc_pay176">
              <img src="../../image/zgzn-pos/pc_pay_database_list.png" />
              <el-popover
                :disabled="dat.info.remark.length < 12"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="dat.info.remark">
                <div slot="reference"
                  style="font-size: 18px;font-weight: bold;
                    line-height: 58px;width: 233px;
                    overflow: hidden;text-overflow: ellipsis;
                    white-space: nowrap;">
                  {{index + 1}}{{dat.info.remark === '' ? '' : '.' + dat.info.remark }}
                </div>
              </el-popover>
              <div>{{dat.createAt.replace('T', ' ')}}</div>
            </div>
            <div style="min-height: 200px;overflow: hidden;line-height: 20px;">
              <div style="min-height: 78px;">
                <!-- 商品list -->
                <div v-for="(da, index1) in dat.info.left_goods_list" :key="index1">
                  <div style="margin-top: 12px; overflow: hidden;" v-if="index1 < 2">
                    <div style="overflow: hidden;">
                      <el-popover
                        :disabled="da.name.length < 7"
                        popper-class="pc_pay192 popper_self"
                        placement="top"
                        trigger="hover"
                        :content="da.name">
                        <div slot="reference" class="pc_pay177">{{da.name}}</div>
                      </el-popover>
                      <el-popover
                        :disabled="Number(da.vipPrice) === 0 ?
                          Number(da.salePrice).toFixed(2).toString().length < 8 : Number(da.vipPrice).toFixed(2).toString().length < 8"
                        popper-class="pc_pay192 popper_self"
                        placement="top"
                        trigger="hover"
                        :content="Number(da.vipPrice) === 0 ?
                          '¥ ' + Number(da.salePrice).toFixed(2) : '¥ ' + Number(da.vipPrice).toFixed(2)">
                        <div slot="reference">
                          <div class="pc_pay172" style="width: 80px;"
                            v-if="!dat.info.showMember || Number(dat.info.memberPayType) === 1 || (da.isMemberDayGoods && dat.info.showMember)">
                            ¥ {{Number(da.salePrice).toFixed(2)}}
                          </div>
                          <div class="pc_pay172" style="width: 80px;" v-else>¥ {{Number(da.vipPrice) === 0 ?
                            Number(da.salePrice).toFixed(2) : Number(da.vipPrice).toFixed(2)}}</div>
                        </div>
                      </el-popover>
                      <div class="pc_pay172" style="width: 65px;">
                        ×{{Number(da.number)}}
                      </div>
                      <div class="pc_pay172" style="width: 50px;">{{da.disc}}%</div>
                      <el-popover
                        :disabled="Number(da.amt).toFixed(2).toString().length < 8"
                        popper-class="pc_pay192 popper_self"
                        placement="top"
                        trigger="hover"
                        :content="'¥ ' + Number(da.amt).toFixed(2)">
                        <div slot="reference" class="pc_pay172" style="width: 80px;">¥ {{Number(da.amt).toFixed(2)}}</div>
                      </el-popover>
                    </div>
                  </div>
                </div>
                <div style="margin-top: 7px;text-indent: 25px;" v-show="dat.info.left_goods_list.length > 2">......</div>
              </div>
              <div style="margin-top: 16px;overflow: hidden;">
                <div class="pc_pay178">
                  <span v-show="dat.info.showMember">会员：<el-popover
                    :disabled="dat.info.left_member_name.toString().length < 4"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="dat.info.left_member_name.toString()">
                    <span slot="reference" style="color: #CFA26B;">{{dat.info.left_member_name}}</span>
                  </el-popover></span>
                </div>
                <div slot="reference" class="pc_pay179">
                  <el-popover
                    :disabled="dat.info.total_number.toString().length < 7"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="dat.info.total_number.toString()">
                    <span slot="reference" style="max-width:56px;vertical-align: bottom;" class="pc_pay199">{{dat.info.total_number}}</span>
                  </el-popover>件商品，应收金额：<el-popover
                    :disabled="dat.info.showFinalPrice.toString().length < 8"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="'¥ ' + dat.info.showFinalPrice.toString()">
                    <span slot="reference" style="font-size: 20px;max-width: 99px;" class="pc_pay199">
                      ¥ {{Number(dat.info.showFinalPrice).toFixed(2)}}
                    </span>
                  </el-popover>
                </div>
              </div>
              <div style="overflow: hidden;">
                <div class="pc_pay180" @click="toDeleteDatabaseList(dat.id)">删除订单</div>
                <div class="pc_pay180_1" @click="print(dat)">打印小票</div>
                <div class="pc_pay181" @click="databaseToLeftList(dat.id)">取单</div>
              </div>
            </div>
          </div>
        </div>
        <div class="pc_pay182" @click="continue_deleteAllDatabaseList = true;show_database_list = false;left_id = ''">
          <img src="../../image/pc_pay_clear_datalist.png" />
          <div>清空挂单</div>
        </div>
      </div>
    </div>
    <!--锁屏弹出遮罩层，并且模糊背景-->
    <div v-show="pcLockScreen" class="pc_pay25" @click="setInputPassword()">
      <div v-show="false" class="pc_pay26" @click="forgetPassword()">忘记密码？</div>
      <div class="pc_pay27">
        <img src="../../image/zgzn-pos/pc_header_person.png"/>
      </div>
      <div class="pc_pay28">
        <div>{{username}}</div>
        <div class="pc_pay29" v-show="!input_password">任意按键或鼠标点击解锁</div>
        <div class="confirm-container" v-show="input_password">
          <el-input id="password"
            style="width: 250px;" placeholder="请输入登录密码"
            maxlength="32"
            v-model="open_lock" clearable type="password"
            @focus="selectText('password')"
            @keyup.enter.native="openLockScreen()">
          </el-input>
          <div class="lock-btn-confirm" @click="openLockScreen">确定(Enter)</div>
        </div>
      </div>
    </div>
    <!-- 是否退出收银台 -->
    <!-- 结算/退货切换清空列表 -->
    <div v-show="showPayExitMsg || showPayChangeStatus1 || showPayChangeStatus2 || showPayPrint || showGoodsSort || showDetail" class="pc_pay122">
      <div class="pc_pay120" @click="show_batch_delete = false"></div>
      <div class="pc_pay121">
        <div class="pc_pay123">提示</div>
        <div class="pc_pay124">离开将清空所有未结算商品<br />确定离开？</div>
        <div class="pc_pay89">
          <div class="pc_pay9" @click="cancelExit()">取消(Esc)</div>
          <div class="pc_pay91" @click="continueExit()">确定(Enter)</div>
        </div>
      </div>
    </div>
    <!-- 删除一条挂单 -->
    <div v-show="showDeleteDatabaseList" class="pc_pay122">
      <div class="pc_pay120" @click="showDeleteDatabaseList = false"></div>
      <div class="pc_pay121" style="height: 270px;">
        <div class="pc_pay123">提示</div>
        <div class="pc_pay124">是否删除此挂单？</div>
        <div class="pc_pay89">
          <div class="pc_pay9" @click="showDeleteDatabaseList = false;">取消(Esc)</div>
          <div class="pc_pay91" @click="deleteDatabaseList(delOrderId);">确定(Enter)</div>
        </div>
      </div>
    </div>
    <!-- 编辑应收 -->
    <div v-show="showEditPrice" class="pc_pay122">
      <div class="pc_pay120" @click="show_batch_delete = false"></div>
      <div class="pc_pay121">
        <div class="pc_pay123">
          <span style="font-weight: bold;font-size: 24px;">应收：</span>
          <span style="color: #FF6159;font-size: 18px;">¥</span>
          <span class="pc_pay202">{{showFinalPrice}}</span>
        </div>
        <div class="pc_pay203">
          <input type="text" id="inputEditPrice" v-model="inputEditPrice"
            @input="inputEditPrice = $payPriceLimit(inputEditPrice);Number(inputEditPrice) > Number(totalPrice) ? (inputEditPrice = totalPrice) : ''"/>
        </div>
        <div class="pc_pay89" style="margin-top: 40px;">
          <div class="pc_pay9" @click="cancelEditPrice()">取消(Esc)</div>
          <div class="pc_pay91" @click="continueEditPrice()">确定(Enter)</div>
        </div>
      </div>
    </div>

    <!-- 清空挂单列表 -->
    <div v-show="continue_deleteAllDatabaseList" class="pc_pay122">
      <div class="pc_pay120" @click="show_database_list = true;continue_deleteAllDatabaseList = false;databaseListKeyword = '';"></div>
      <div class="pc_pay121" style="height: 270px;">
        <div class="pc_pay123">提示</div>
        <div class="pc_pay124">是否清空全部挂单？</div>
        <div class="pc_pay89">
          <div class="pc_pay9" @click="show_database_list = true;continue_deleteAllDatabaseList = false;">取消(Esc)</div>
          <div class="pc_pay91" @click="deleteAllDatabaseList()">清空(Enter)</div>
        </div>
      </div>
    </div>

    <!-- 编辑折扣 -->
    <div v-show="show_edit_disc" class="pc_pay122">
      <div class="pc_pay120" @click="show_edit_disc = false"></div>
      <div class="pc_pay125">
        <div class="pc_pay126">请输入会员折扣</div>
        <input class="pc_pay127" placeholder="请输入会员折扣" v-model="member_disc"
          @input="member_disc = $memberDiscountLimit(member_disc)" @blur="checkMemberDisc()" />
        <div class="pc_pay89">
          <div class="pc_pay9" @click="show_edit_disc = false">取消</div>
          <div class="pc_pay91" @click="show_edit_disc = false;saveMemberDisc()">确定</div>
        </div>
      </div>
    </div>

    <!-- 编辑折扣和减价弹窗 -->
    <div v-show="edit" class="pc_pay122">
      <div class="pc_pay120" @click="edit = false"></div>
      <div class="pc_pay195">
        <div class="pc_pay196">
          编辑<span v-if="priceoff === 'discount'">折扣</span><span v-else>减价</span>
        </div>
        <div style="overflow: hidden;margin-top: 30px;">
          <div style="float: left;width: 150px;margin-left: 20px;position: relative;">
            <el-input v-for="(nr, index) in number_reduce_list"
              :id="'edit_input' + index"
              style="margin-bottom: 27px;"
              :key="index"
              @focus="editFocus(index);"
              @blur="editFocusBlur(nr.number);"
              @input="nr.number= $priceLimit(nr.number, priceoff === 'discount' ? 100 : 999999.99)"
              v-model="nr.number"
              :maxlength="priceoff === 'discount' ? 3 : 9"
              clearable>
            </el-input>
            <div style="margin-top: -270px;position: absolute;margin-left: 93px;">
              <div style="position: relative;font-size: 16px;line-height: 16px;margin-bottom: 55px;" v-for="(nr, index) in number_reduce_list" :key="index">
                <span v-if="priceoff === 'discount'">折</span>
                <span v-else>元</span>
              </div>
            </div>
          </div>
          <div class="pc_pay130" style="float: left;margin-top: 1px;margin-left: 20px;">
            <div @click="inputCalculator('1')">1</div>
            <div @click="inputCalculator('2')">2</div>
            <div @click="inputCalculator('3')">3</div>
            <div @click="inputCalculator('0')">0</div>
            <div @click="inputCalculator('4')">4</div>
            <div @click="inputCalculator('5')">5</div>
            <div @click="inputCalculator('6')">6</div>
            <div @click="rightKeyword.indexOf('.') == -1 ? inputCalculator('.') : ''">.</div>
            <div @click="inputCalculator('7')">7</div>
            <div @click="inputCalculator('8')">8</div>
            <div @click="inputCalculator('9')">9</div>
            <div @click="inputCalculator('back')"><img src="../../image/pc_pay_calculator.png" style="width: 54px;height: 54px;" /></div>
          </div>
        </div>

        <div class="pc_pay197">
          <div @click="edit = false">取消(Esc)</div>
          <div @click="saveEdit()" id="saveEdit">保存(Enter)</div>
        </div>
      </div>
    </div>

    <!-- 编辑支付码 -->
    <div v-show="show_edit_pwd" class="pc_pay122">
      <div class="pc_pay120" @click="show_edit_pwd = false"></div>
      <div class="pc_pay125">
        <div class="pc_pay126">请输入新支付码</div>
        <input class="pc_pay127" maxlength="6" placeholder="请输入新支付码" v-model="member_pwd" type="password" />
        <div class="pc_pay89">
          <div class="pc_pay9" @click="show_edit_pwd = false">取消</div>
          <div class="pc_pay91" @click="saveMemberPwd()">确定</div>
        </div>
      </div>
    </div>

    <!-- 点击清空，清空列表前询问 -->
    <div v-show="show_clear_left_goods_list" class="pc_pay122">
      <div class="pc_pay120" @click="show_batch_delete = false"></div>
      <div class="pc_pay125">
        <div class="pc_pay123">提示</div>
        <div class="pc_pay124">确定清空商品列表？</div>
        <div class="pc_pay89">
          <div class="pc_pay9" @click="show_clear_left_goods_list = false">取消(Esc)</div>
          <div class="pc_pay91" @click="show_clear_left_goods_list = false;clearLeftGoodsList()">确定(Enter)</div>
        </div>
      </div>
    </div>

    <!-- 商品条码重复手动选择商品 -->
    <!-- <div v-if="showRepeatChoose" class="repeat_choose">
      <div class="repeat_choose--shade">
        <div class="repeat_choose--container">
          <div class="repeat_choose--header">
            <div>
              <span style="font-size: 18px;font-weight: bold;">商品条码重复，请选择商品</span>
            </div>
            <div @click="showRepeatChoose = false">
              <span class="close">×</span>
            </div>
          </div>
          <div class="repeat_choose--body">
            <div class="text">条码重复会造成库存管理混乱，请在【商品管理】中修改或删除重复条码。</div>
            <div class="itemContainer">
              <div v-for="(item, index) in repeatData" :key="item.fingerprint"
                :class="index === repeatIndex ? 'module_active' : 'module'"
                @click="repeatIndex = index;">
                <el-image :src="item.image ? $getSrcUrl(item.image) : require('../../image/pc_no_cloth_img.png')"/>
                <div class="info">
                  <div class="info_name">{{item.name + '（库存：' + item.curStock + '）'}}</div>
                  <div class="info_code" v-html="repeatCodeHighLight(item)">
                  </div>
                </div>
                <div>
                  <i v-if="index === repeatIndex" class="el-icon-success"></i>
                  <div v-else class="circle_empty"></div>
                </div>
              </div>
            </div>
            <div class="repeat_choose--submit" @click="repeatChoose">确定</div>
          </div>
        </div>
      </div>
    </div> -->
    <!--弹出遮罩层-->
    <div v-show="show_input_number" class="pc_pay25">
      <div class="pc_pay120" @click="show_input_number = false;"></div>
      <div class="pc_pay62">
        <div class="pc_pay63" v-show="selfoff == 'discount'">自定义折扣</div>
        <div class="pc_pay63" v-show="selfoff == 'reduce'">自定义减价</div>
        <div class="pc_pay64">
          <span v-show="rightKeyword == '' && selfoff == 'discount'" class="pc_pay128">请输入折扣</span>
          <span v-show="rightKeyword == '' && selfoff == 'reduce'" class="pc_pay128">请输入金额</span>
          <span v-show="rightKeyword != ''" class="pc_pay128" style="color: #5A7788">{{rightKeyword}}</span>
          <div class="pc_pay129">
            <span v-show="selfoff == 'discount'">折</span>
            <span v-show="selfoff == 'reduce'">元</span>
          </div>
        </div>
        <div class="pc_pay130">
          <div @click="inputCalculator('1')">1</div>
          <div @click="inputCalculator('2')">2</div>
          <div @click="inputCalculator('3')">3</div>
          <div @click="inputCalculator('0')">0</div>
          <div @click="inputCalculator('4')">4</div>
          <div @click="inputCalculator('5')">5</div>
          <div @click="inputCalculator('6')">6</div>
          <div @click="rightKeyword.indexOf('.') == -1 ? inputCalculator('.') : ''">.</div>
          <div @click="inputCalculator('7')">7</div>
          <div @click="inputCalculator('8')">8</div>
          <div @click="inputCalculator('9')">9</div>
          <div @click="inputCalculator('back')"><img src="../../image/pc_pay_calculator.png" style="width: 54px;height: 54px;" /></div>
        </div>
        <div @click="subInputNumber()" class="pc_pay183">添&nbsp;加</div>
        <div style="height: 20px;"></div>
      </div>
    </div>

    <!--弹出输入称重商品的重量，方便调用电子秤-->
    <div v-show="showInputWeight" class="pc_pay25">
      <!--灰色背景，点击关闭弹出层-->
      <div class="pc_pay76" @click="closeInputWeight"></div>
      <div class="pc_pay77_1">
        <!-- 返回 -->
        <div class="pc_pay131">
          <div class="weight-head" @click="closeInputWeight">
            <img src="../../image/pc_header_back.png" />
            <div>返回</div>
          </div>
        </div>
        <!--标题-->
        <div class="pc_pay131_top">
          <el-tooltip class="item" effect="dark" :content="weight_title" placement="top">
            <div class="pc_pay78 pc_pay78_1">{{weight_title}}</div>
          </el-tooltip>
        </div>
        <div class="pc_pay204">
          <label>售&nbsp;价：</label>
          <input
            v-model="inputPrice"
            :readonly="ifautoCash || !$employeeAuth('modify_sale_price')"
            style="width: 130px;"
            placeholder="请输入售价"
            v-input-price="{ data: 'inputPrice' }"
            @blur="formatReturn()"
            @click="weightSwitchInputObj('price')"
          />
          <span>元 / {{alert_unit}}</span>
          <span @click="is_temp_modify = !is_temp_modify" v-show="!ifautoCash">
            <img v-show="!is_temp_modify" src="../../image/zgzn-pos/pc_goods_checkbox1.png" />
            <img v-show="is_temp_modify" src="../../image/zgzn-pos/pc_goods_checkbox2.png" />
            &nbsp;
            <span>临时改价</span>
          </span>
        </div>
        <div class="pc_pay204">
          <label>重&nbsp;量：</label>
          <input
            id="iw"
            :readonly="ifautoCash"
            @focus="listen_sm = false"
            @blur="listen_sm = true"
            @input="inputWeight=inputWeight.replace(/^\D*(\d*(?:\.\d{0,3})?).*$/g, '$1')"
            @click="weightSwitchInputObj('weight')"
            v-model="inputWeight"
            maxlength="9"
          />
          <span>千克</span>
          <div :style="weighStatus === '·未连接' ? 'color: #FF6159;': 'color: #BBA16C;'" class="pc_weight7">
            {{weighStatus}}
            <el-popover
              @show="setClass()"
              popper-class="pc_pay192"
              placement="bottom-end"
              width="411"
              trigger="click"
              content="可前往【设置】-【配件设置】-【电子秤设置】调试。">
              <div class="pc_weight6" slot="reference">?</div>
            </el-popover>
          </div>
        </div>
        <div class="pc_pay204">
          <label>金&nbsp;额：</label>
          <span class="weight_amount_span"><span>￥</span>{{ Number(weight_amount).toFixed(2) }}</span>
        </div>
        <!--下方计算器-->
        <div class="pc_pay_calculator pc_pay61" style="margin-top: 0;height: 260px;">
          <table>
            <tr>
              <td class="numberClick" @click="inputWeightKeyboard('7')">7</td>
              <td class="numberClick" @click="inputWeightKeyboard('8')">8</td>
              <td class="numberClick" @click="inputWeightKeyboard('9')">9</td>
              <td class="numberClick" @click="inputWeightKeyboard('back')">
                <img src="../../image/pc_pay_calculator.png" style="width: 54px;height: 54px;" />
              </td>
            </tr>
            <tr>
              <td class="numberClick" @click="inputWeightKeyboard('4')">4</td>
              <td class="numberClick" @click="inputWeightKeyboard('5')">5</td>
              <td class="numberClick" @click="inputWeightKeyboard('6')">6</td>
              <td class="numberClick" @click="inputWeightKeyboard('.')">.</td>
            </tr>
            <tr>
              <td class="numberClick" @click="inputWeightKeyboard('1')">1</td>
              <td class="numberClick" @click="inputWeightKeyboard('2')">2</td>
              <td class="numberClick" @click="inputWeightKeyboard('3')">3</td>
              <td class="numberClick" @click="inputWeightKeyboard('0')">0</td>
            </tr>
          </table>
        </div>
        <div class="btn-confirm" @click="inputLeftList()">确&nbsp;定(Enter)</div>
      </div>
    </div>

    <!--弹出选择电子秤页面-->
    <div v-show="showChooseWeight" class="pc_pay25">
      <!--灰色背景，点击关闭弹出层-->
      <div class="pc_pay76"></div>
      <div class="pc_weight">
        <div class="pc_weight1">
          启用电子秤
          <div @click="closeChoose" style="float: right;display: inline-block;cursor: pointer;">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M1.85811 0.0146484L0.443893
                1.42886L6.94389 7.92886L0.301758 14.571L1.71597 15.9852L8.35811 9.34308L14.586
                15.571L16.0002 14.1568L9.77232 7.92886L15.8581 1.84308L14.4439 0.428864L8.35811
                6.51465L1.85811 0.0146484Z" fill="#8197A6"/>
            </svg>
          </div>
          <div style="height: 16px;border-bottom: 1px solid #E3E6EB;"></div>
        </div>
        <div class="pc_weight2">
          <el-alert
            title="若未连接，可前往【设置】-【配件设置】-【电子秤设置】调试。"
            type="warning"
            :closable="false"
            width='100%'
            show-icon>
          </el-alert>
        </div>
        <div class="pc_weight2">
          <div  @click="weights = 0" class="pc_weight3" style="margin-right: 12px;">
            <div class="pc_weight3_img">
              <img style="width: 120px;height: 120px;" src="../../image/pc_cash_register0.png" />
            </div>
            不使用电子秤
            <div v-if="weights !== 0" style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox1.png" />
            </div>
            <div v-else style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox2.png" />
            </div>
          </div>
          <div @click="weights = 1" class="pc_weight3" style="margin-right: 12px;">
            <div class="pc_weight3_img">
              <img style="width: 120px;height: 120px;" src="../../image/pc_cash_register1.png" />
            </div>
            外接电子秤
            <div v-if="weights !== 1" style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox1.png" />
            </div>
            <div v-else style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox2.png" />
            </div>
          </div>
          <div @click="weights = 2" class="pc_weight3">
            <div class="pc_weight3_img">
              <img style="width: 120px;height: 120px;" src="../../image/pc_cash_register2.png" />
            </div>
            称重一体机
            <div v-if="weights !== 2" style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox1.png" />
            </div>
            <div v-else style="width: 24px;height: 24px;margin: 0 auto;margin-top: 8px;cursor: pointer;">
              <img style="width: 24px;height: 24px;" src="../../image/zgzn-pos/pc_goods_checkbox2.png" />
            </div>
          </div>
        </div>
        <div class="pc_weight4" @click="openWeight(1)" style="margin-top: 26px;">
          确&nbsp;定(Enter)
        </div>
      </div>
    </div>

    <!--备注录入-->
    <div v-show="showInputRemark" class="pc_pay25">
      <!--灰色背景，点击关闭弹出层-->
      <div class="pc_pay76" @click="showInputRemark = false;alert_remark = remark;"></div>
      <div class="pc_pay77">
        <!--标题-->
        <div class="pc_pay78">备注</div>
        <div style="height: 35px;"></div>
        <!--输入框-->
        <el-input
          resize="none"
          id="remark"
          ref="remark"
          type="textarea"
          style="height: 96px;width: 453px;margin-left: 25px;"
          :autosize="{ minRows: 4, maxRows: 4}"
          placeholder="请输入内容"
          maxlength="50"
          @focus="selectText('remark')"
          v-focus-select="'focusSelect'"
          v-model="alert_remark">
        </el-input>
        <!--提示文字-->
        <!--<div class="pc_pay8">请将商品放在电子秤上</div>-->
        <!--取消/添加按钮-->
        <div class="pc_pay81">
          <div class="pc_pay82" @click="showInputRemark = false;remark = '';">
            清空(Esc)
          </div>
          <div class="pc_pay83" @click="showInputRemark = false;remark = alert_remark.trim();">
            添加(Enter)
          </div>
        </div>
      </div>
    </div>

    <!--屏幕左侧内容-->
    <div class="pc_pay1" :style="pcLockScreen ? 'filter: blur(20px);' : ''">
      <!--表格-->
      <div class="pc_pay132">
        <div class="pc_pay_clear_div">
          <div style="float:left;" @click="showLastOrderInfo" v-if="showLastOrder && !pc_return_goods && !ifautoCash">
            <i class="el-icon-printer" style="color: #B2C3CD;font-size:22px;margin-right:5px;"></i>
            显示上一单
            <div class="shortCutTag">Ctrl+L</div>
          </div>
          <div style="float:right;" @click="show_clear_left_goods_list = true">
            <div class="shortCutTag">Ctrl+Q</div>
            <i class="el-icon-delete" style="color: #B2C3CD;font-size:22px;margin-right:5px;"></i>
            清空列表
          </div>
        </div>
        <div class="pc_pay11">
          <div style="width: 40px;text-align: center;margin-left: 10px;">序号</div>
          <div style="width: 100px;margin-left: 24px;">商品</div>
          <div style="width: 68px;text-align: right;">单价</div>
          <div style="width: 76px;text-align: right;">数量</div>
          <div style="width: 83px;text-align: right;">小计</div>
        </div>
        <div style="height: 46px;"></div>
        <div id="lg" class="pc_pay133" :class="showLeftGoodsList.length == 0 ? 'pc_pay1_bg' : ''">
          <div
            v-cloak
            v-for="(ef,index) in showLeftGoodsList"
            v-bind:key="ef.id"
            @click="editLeftList(index,ef);choose_left = index"
            :class="ef.showStock
            || (set_priceNotice && Number((Number(ef.amt) / Number(ef.number)).toFixed(2)) < Number(ef.purPrice)) ? 'pc_pay92' : 'pc_pay93'"
            :style="choose_left == index ? 'background: #EAF9FC' : ''">
            <div style="height: 15px;"></div>
            <div class="pc_pay11_1" >
              <div style="width: 40px;text-align: center;margin-left: 10px;">{{index + 1}}</div>
              <div class="pc_pay190">
                {{ef.name}}<br />
              </div>
              <div v-cloak class="pc_1_1" style="width: 68px;text-align: right;" v-if="!showMember || memberPayType == 1 || (ef.isMemberDayGoods == true && showMember)">
                <div style="width: 100%" v-if="!ef.isScales">{{(Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)}}</div>
                <div style="width: 100%" v-if="ef.isScales">{{Number(ef.salePrice).toFixed(2)}}</div>
                <div
                  v-cloak
                  v-show="Number((Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)) < Number(Number(ef.salePrice).toFixed(2)) && !ef.isScales"
                  class="pc_pay_price pc_1_2">{{Number(ef.salePrice).toFixed(2)}}</div>
              </div>
              <div v-cloak class="pc_2_1" style="width: 68px;text-align: right;" v-else>
                <div style="width:100%" v-if="!ef.isScales">{{(Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)}}</div>
                <div style="width:100%" v-if="ef.isScales">{{Number(ef.salePrice).toFixed(2)}}</div>
                <div v-cloak v-show="(Number(ef.vipPrice) === 0 ?
                  Number((Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)) < Number(Number(ef.salePrice).toFixed(2)) :
                  Number((Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)) < Number(Number(ef.vipPrice).toFixed(2))) && !ef.isScales"
                  class="pc_pay_price pc_2_2">{{Number(ef.vipPrice) === 0 ? Number(ef.salePrice).toFixed(2) : Number(ef.vipPrice).toFixed(2)}}</div>
              </div>
              <el-popover
                :disabled="isWeightProd(ef.unitName) ?
                  setMaxDecimal(ef.number, 3).toFixed(3).length < 7 :
                  setMaxDecimal(ef.number, 3).toFixed(2).length < 7"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="setMaxDecimal(ef.number, 3).toString()">
                <div class="pc_pay199" slot="reference" style="width: 76px;text-align: right;">
                  {{setMaxDecimal(ef.number, 3)}}
                </div>
              </el-popover>
              <el-popover
                :disabled="Number(ef.amt).toFixed(2).toString().length < 8"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(ef.amt).toFixed(2).toString()">
                <div slot="reference" style="width: 78px;text-align: right;margin-left:5px" class="pc_pay199">{{Number(ef.amt).toFixed(2)}}</div>
              </el-popover>
            </div>
            <div v-show="ef.showStock
              || (set_priceNotice && Number((Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)) < Number(ef.purPrice))"
              class="pc_pay134" style="font-size: 13px;color: #FF8484;margin-top: 10px;border: none;">
              <div style="width: 35px;float: left;height: 1px;"></div>
              <div
                style="float: left;width: 177px;"
                v-show="ef.showStock">
                库存不足，剩余{{Number(ef.curStock).toFixed(2)}}
              </div>
              <div style="float: left;" v-cloak
                v-show="set_priceNotice && Number((Number(ef.amtReally || ef.amt) / Number(ef.number)).toFixed(2)) < Number(ef.purPrice)">
                售价小于进价
              </div>
            </div>
            <div class="pc_pay134"></div>
          </div>
        </div><div v-if="lastOrderDetail.length !== 0">
          <el-drawer
            :visible="showLastOrderDetail"
            :with-header="false"
            :wrapperClosable="true"
            :before-close="drawerDetailClose"
            size="494">
            <div style="background:#F4EFE5;height:56px;width:514px;padding: 0 20px;
              display:flex;justify-content:space-between;align-items:center;">
              <div><i class="el-icon-document-add" style="color:#BDA169;font-size:24px;"></i></div>
              <div style="color:#BDA169;font-size:32px;line-height:18px;cursor:pointer;margin-top:-8px;margin-right:10px;"
                @click="showLastOrderDetail = false;">×
              </div>
            </div>
            <div class="lastOrder_detail_title">
              <div style="margin-left:16px;margin-top:12px;padding-top:6px;">订单号: <span>{{lastOrderDetail[0].code}}</span></div>
              <div style="margin-left:16px;margin-top:10px;">
                <div style="width: 280px;display:inline-block;">下单时间: <span>{{lastOrderDetail[0].createtime}}</span></div>
                <div style="width: 165px;display:inline-block;">支付方式: <span>{{lastOrderDetail[0].accts}}</span></div>
              </div>
              <div style="margin-left:16px;margin-top:10px;">
                <div style="width: 280px;display:inline-block;">会员:
                  <span>{{lastOrderDetail[0].vipname === '' ? '-' : lastOrderDetail[0].vipname}}</span>
                </div>
                <div style="width: 165px;display:inline-block;">手机号:
                  <span>{{lastOrderDetail[0].vipmobile === '' ? '-' : lastOrderDetail[0].vipmobile}}</span>
                </div>
              </div>
              <div style="margin-left:16px;margin-top:10px;">
                <div style="width: 280px;display:inline-block;">可用余额:
                  <span>{{lastOrderDetail[0].member_money_value === '' ? '-' : lastOrderDetail[0].member_money_value}}</span>
                </div>
                <div style="width: 165px;display:inline-block;">剩余积分:
                  <span>{{lastOrderDetail[0].member_point_value === '' ? '-' : lastOrderDetail[0].member_point_value}}</span>
                </div>
              </div>
            </div>
            <div class="lastOrder_detail_data">
              <div style="margin-left:26px;margin-top:10px;">
                <div style="width: 280px;display:inline-block;">商品总价: &nbsp;<span style="color:#B4995A;">
                  {{Number(lastOrderDetail[0].billAmt).toFixed(2)}}</span></div>
                <div style="width: 150px;display:inline-block;">优惠合计: &nbsp;<span>
                  {{(Number(lastOrderDetail[0].billAmt) - Number(lastOrderDetail[0].discAmt)).toFixed(2)}}</span></div>
              </div>
              <div style="margin-left:26px;margin-top:10px;">
                <div style="width: 280px;display:inline-block;">实收金额: &nbsp;<span>
                  {{(Number(lastOrderDetail[0].changeAmt) + Number(lastOrderDetail[0].discAmt)).toFixed(2)}}</span></div>
                <div style="width: 150px;display:inline-block;">找零金额: &nbsp;<span>
                  {{Number(lastOrderDetail[0].changeAmt).toFixed(2)}}</span></div>
              </div>
            </div>
            <div class="lastOrder_detail_goods" :style="'height:' + lastProdsHeight + 'px;'">
              <div style="display:flex;">
                <div style="width: 46px;text-align:center;">序号</div>
                <div style="width: 200px;">商品</div>
                <div style="width: 71px;text-align:left;">单价</div>
                <div style="width: 66px;text-align:center;">数量</div>
                <div style="width: 71px;text-align:right;">小计</div>
              </div>
              <div style="display:flex;margin-top:6px;" v-for="(data, $index) in lastOrderDetail[0].saleItems" :key="data.id">
                <div style="width: 46px;text-align:center;">{{$index + 1}}</div>
                <div style="width: 200px;" :title="data.name">
                  {{data.name.length > 20 ? (data.name.substr(0, 17) + '...') : data.name}}
                </div>
                <div style="width: 71px;text-align:left;">{{Number(data.price).toFixed(2)}}</div>
                <div style="width: 66px;text-align:center;color: #AAA;">{{'×' + Number(data.qty)}}</div>
                <div style="width: 71px;text-align:right;">{{Number(data.amt).toFixed(2)}}</div>
              </div>
            </div>
            <div class="last-order-detail-flex">
              <div style="display: flex;">
                <div
                  class="last-order-detail-flex--refund"
                  :class="{fullDisabled: hasRefundType !== -1 || !$employeeAuth('return_sales')}"
                  @click="fullOrderReturn()">
                  整单退货
                </div>
                <div
                  class="last-order-detail-flex--refund"
                  :class="{
                    partDisabled: hasRefundType === 1 ||
                    orderInfo.accountId === 99 ||
                    orderInfo.vipid ||
                    !$employeeAuth('return_sales')
                  }"
                  @click="partialReturn()">
                  部分退货
                </div>
              </div>
              <div
                class="last-order-detail-flex--print"
                @click="printLastOrder()"
                :style="printClick ? 'opacity: 0.5' : ''">
                打印小票(Enter)
              </div>
            </div>
          </el-drawer>
        </div>
      </div>
      <!--左侧底部固定-->
      <div class="pc_pay12">
        <!--第一行-->
        <div class="pc_pay14">
          <div class="pc_pay13" style="margin-left: 15px;" v-show="remark == ''" @click="showRemarkDialog()">
            <img src="../../image/pc_pay_explain.png" />
            <div>备注&nbsp;&nbsp;<span style="font-weight: bold;font-size: 14px;">Ctrl+B</span></div>
          </div>
          <div class="pc_pay13 pc_pay135" v-show="remark != ''"  @click="showRemarkDialog()">
            {{remark.length > 6 ? remark.substring(0,6) + '...' : remark }}
          </div>
          <div class="pc_pay136">
            <!-- 第一版没有会员先注释掉 -->
            <div class="pc_pay16" v-show="showMember" style="position: relative;">
              <div @click="memberRemove()" class="pc_pay137" style="top: -16px;width: 22px;height: 22px;">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20Z" fill="#FF6159"/>
                  <path d="M10 8.58584L7.17202 5.75684L5.75702 7.17184L8.58602
                    9.99984L5.75702 12.8278L7.17202 14.2428L10 11.4138L12.828 14.2428L14.243
                    12.8278L11.414 9.99984L14.243 7.17184L12.828 5.75684L10 8.58584Z" fill="white"/>
                </svg>
              </div>
              <div style="margin-left: 15px;">
                <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4.06082 0.5H15.9392C16.0706 0.499982 16.2003 0.53108 16.3174 0.590751C16.4346 0.650423 16.536 0.736975 16.6133
                    0.843333L19.7992 5.22333C19.8558 5.30127 19.8837 5.39642 19.878 5.4926C19.8724 5.58877 19.8337 5.68003 19.7683 5.75083L10.3058
                    16.0017C10.2309 16.0827 10.1268 16.1307 10.0165 16.135C9.90617 16.1394 9.79864 16.0998 9.71749 16.025C9.71165 16.02 6.55082
                    12.5958 0.231654 5.75083C0.166324 5.68003 0.127548 5.58877 0.121926 5.4926C0.116304 5.39642 0.144184 5.30127 0.200821 5.22333L3.38665
                    0.843333C3.46397 0.736975 3.56536 0.650423 3.68253 0.590751C3.7997 0.53108 3.92933 0.499982 4.06082 0.5ZM4.48582 2.16667L2.15249
                    5.375L9.99999 13.875L17.8475 5.375L15.5142 2.16667H4.48582Z" fill="#CFA26B"/>
                </svg>
              </div>
              <div>{{left_member_name.substring(0,1) + '**'}}</div>
            </div>
            <div class="pc_pay15" v-show="discountIndex != null && discountNumber != 10" style="position: relative;">
              <div @click="clearDiscountReduce()" class="pc_pay137">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20Z" fill="#FF6159"/>
                  <path d="M10 8.58584L7.17202 5.75684L5.75702 7.17184L8.58602 9.99984L5.75702 12.8278L7.17202
                    14.2428L10 11.4138L12.828 14.2428L14.243 12.8278L11.414 9.99984L14.243
                    7.17184L12.828 5.75684L10 8.58584Z" fill="white"/>
                </svg>
              </div>
              {{Number(discountNumber).toFixed(1)}}折
            </div>
            <div class="pc_pay6" v-show="reduceIndex != null && reduce_number != 0" style="position: relative;">
              <div @click="clearDiscountReduce()" class="pc_pay137">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10 20C4.477 20 0 15.523 0 10C0 4.477 4.477 0 10 0C15.523 0 20 4.477 20 10C20 15.523 15.523 20 10 20Z" fill="#FF6159"/>
                  <path d="M10 8.58584L7.17202 5.75684L5.75702 7.17184L8.58602
                    9.99984L5.75702 12.8278L7.17202 14.2428L10 11.4138L12.828 14.2428L14.243 12.8278L11.414
                    9.99984L14.243 7.17184L12.828 5.75684L10 8.58584Z" fill="white"/>
                </svg>
              </div>
              -{{Number(reduce_number).toFixed(2)}}元
            </div>
          </div>
        </div>
        <!--虚线-->
        <div class="pc_pay17"></div>
        <!--虚线以下部分-->
        <div style="overflow: hidden;font-family: HarmonyOSSansSC, sans-serif;"
          :style="pc_return_goods ? 'background: #ecffe9' : 'background: #FFF'">
          <!--第二、三行-->
          <div class="pc_pay18">
            <div class="pc_pay41">
              <div style="line-height: 20px;">件数：
                <span style="font-weight: bold;font-size: 20px;">{{ setMaxDecimal(total_number, 3) }}</span>
              </div>
              <div class="pc_pay19">优惠：
                <span style="font-weight: 700;font-size: 20px;" :style="pc_return_goods ? 'color: #40ab47' : ''">
                  ¥ {{reducePrice == 0 ? '0.00' : reducePrice}}
                </span>
              </div>
            </div>
            <div style="float: left;overflow: hidden;">
              <div class="pc_pay42">合计：
                <span style="font-weight: bold;font-size: 20px;">¥ {{totalPrice}}</span></div>
              <div style="overflow: hidden;"></div>
                <div v-show="!pc_return_goods" class="pc_pay19 pc_pay198" style="padding-top: 3px;">
                  应收：<span style="color: #FF6159;font-weight: bold;">¥</span>
                  <el-popover
                    :disabled="showFinalPrice.toString().length<10"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="'¥' + showFinalPrice.toString()">
                    <span slot="reference" style="margin-left: 0" class="pc_pay43">{{showFinalPrice}}</span>
                  </el-popover>
                </div>
                <div v-show="pc_return_goods" class="pc_pay19 pc_pay198" style="font-weight: bold;padding-top: 3px;">
                  应退：<span style="color: #40ab47;padding-top: 3px;">¥</span>
                  <el-popover
                    :disabled="showFinalPrice.toString().length<10"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="'¥' + showFinalPrice.toString()">
                    <span slot="reference" class="pc_pay44" style="color: #40ab47;margin-left: 0">{{showFinalPrice}}</span>
                  </el-popover>
                </div>
                <div style="float: right;margin-top: -20px;"
                  v-show="Number(totalPrice) > 0 && !pc_return_goods && !ifautoCash && $employeeAuth('modify_sale_price')">
                  <img
                    style="cursor: pointer;width: 20px;margin-bottom: 5px;margin-left: 21px;"
                    @click="editPrice()" src="../../image/zgzn-pos/pc_member_edit.png" />
                  <div class="shortCutTag"
                    style="display: block;margin-top: 5px;height: 24px;line-height: 24px;font-weight: bold">
                    Ctrl+Y
                  </div>
                </div>
            </div>
          </div>
          <!--底部按钮行-->
          <div class="pc_pay2">
            <div class="pc_pay21" style="background: none;text-align: left;cursor: default;" v-show="pc_return_goods">
              <img src="../../image/pc_pay_backgoods.png" style="margin-left: 7px;" />
            </div>
            <div v-if="!pc_return_goods && !ifautoCash"
              class="pc_pay170"
              id="pc_pay170"
              @click="openDataBase()">
              <div class="pc_pay138" v-if="Number(database_list_length)">
                {{Number(database_list_length) > 99 ? '99+' : database_list_length}}
              </div>
              <div class="pc_pay171" style="line-height: 60px;">取单(F2)</div>
            </div>
            <div v-show="!pc_return_goods && !ifautoCash"
              class="pc_pay170"
              id="pc_pay170_1"
              @click="addDatabaseList()">
              <div class="pc_pay22">挂单 (F3)</div>
            </div>
            <div v-show="!pc_return_goods" style="background: #FF6759;"
              :class="ifautoCash?'pc_pay30':'pc_pay23'"
              @click="showSettlement(1);settlementClick()">
              <div class="pc_pay22">结算 (Space)</div>
            </div>
            <div v-show="pc_return_goods" :class="ifautoCash?'pc_pay30':'pc_pay23'"
              @click="showSettlement(2)"
              style="background: #40ab47;min-width: 250px;">
              <div class="pc_pay22">退款 (Space)</div>
            </div>
          </div>
          <div style="height: 17px;"></div>
        </div>

      </div>
    </div>
    <!--屏幕右侧内容-->
    <div class="pc_pay24" :style="pcLockScreen ? 'filter: blur(20px);' : ''">
      <!-- <div v-show="bottomSelect === 3 || bottomSelect === 4" class="pc_pay_selection" :style="screenWidth < 1025 ? 'width: 92px;' : ''">
        <div id="up" @mouseout="clearMouse('up')" @click="mouseOne('up')" @mousedown="mousedown('up')" @mouseup="clearMouse('up')"
          style="border-left: 1px solid #E3E6EB;height: 54px;line-height: 52px;" class="pc_pay_selection1">
          <span class="el-icon-caret-top"></span>
        </div>
        <div id="selectRight" class="pc_pay_selection3" :style="'height:' + rightHeight">
          <div @click="select_right = 0;type = ''" class="pc_pay_selection1" :class="select_right === 0 ? 'pc_pay_selection2' : ''">全部分类</div>
          <div v-for="(ct,index) in category_list" v-bind:key="index"
            @click="select_right = index + 1;type = ct.id"
            class="pc_pay_selection1" :class="select_right == index + 1 ? 'pc_pay_selection2' : ''">
            {{ct.name}}
          </div>
        </div>
        <div id="down" @mouseout="clearMouse('down')" @click="mouseOne('down')" @mousedown="mousedown('down')" @mouseup="clearMouse('down')" class="pc_pay_selection1"
          style="height: 54px;border-left: 1px solid #E3E6EB;border-top: 1px solid #E3E6EB;border-bottom: 0px;line-height: 52px;">
          <span class="el-icon-caret-bottom"></span>
        </div>
      </div> -->
      <div class="pc_type_div" v-show="bottomSelect === 3 || bottomSelect === 4">
        <type-menu ref="typeMenu"
          v-model="type"
          is-pay-page :edit-top="140"
          :show-hot="showHotGood"></type-menu>
      </div>
      <!--选择会员 bottomSelect == 1-->
      <div v-if="bottomSelect == 1" style="width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 10;overflow: auto;">
        <div style="width: 825px;height: 670px;margin: 0 auto;margin-top: 3%;background: #FFF;border-radius: 6px;position: relative;">
          <div id="fontColor" style="height: 58px;line-height: 58px;width: 786px;margin: 0 auto;border-bottom: 1px solid #E3E6EB;font-size: 16px;">
            <div style="float: left;font-weight: bold;">选择会员</div>
            <div style="float: right;font-size: 36px;color: #8197A6;font-weight: 300;cursor: pointer;" @click="bottomSelect = 3">×</div>
          </div>
          <!--搜索按钮-->
          <div style="width: 786px;margin: 0 auto;margin-top: 18px;overflow: hidden;">
            <div class="addMember"
              @click="addMember()" :style="$employeeAuth('create_vips') ? '' : 'opacity: 40%'">新增会员</div>
            <div class="pc_pay188">
              <input maxlength="15"
                type="text"
                id="select_keyword1"
                class="pc_pay39"
                style="background: #FFF;caret-color: #d5aa76;"
                placeholder="请输入姓名/手机号/刷卡"
                v-model="rightKeyword"
                v-focus-select="'autoFocus'"
                @input="toGetVip()"
                @compositionstart="inputing_keyword = true"
                @compositionend="inputing_keyword = false" />
              <img v-show="rightKeyword !== ''" @click="clearMember()" src="../../image/pc_clear_input.png" class="pc_pay200" />
            </div>
          </div>
          <div class="pc_pay189">
            <div style="line-height: 50px;width: 100%;font-size: 16px;color: #567485;">
              <div style="overflow: hidden;width: 100%;font-weight: bold;background: #FAFBFC;">
                <div style="float: left;width: 18%;text-indent: 20px;">姓名</div>
                <div style="float: left;width: 17%;">手机号</div>
                <div style="float: left;width: 20%;">会员折扣</div>
                <div style="float: left;width: 18%;text-indent: 18px;">储值余额</div>
                <div style="float: left;width: 15%;text-indent: 26px;">积分</div>
                <div style="float: left;width: 12%;text-indent: 26px;">操作</div>
              </div>
              <div style="overflow: hidden;width: 100%;"
                v-for="(mt, index) in memTableData" :key="index"
                :class="vipActiveIndex === index ? 'vip_row_active' : (index % 2 ? 'vip_row_grey' : '')">
                <div style="float: left;width: 18%;text-indent: 20px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;"
                 @click="addLeftMember(mt)" :title="mt.name">{{mt.name}}</div>
                <div style="float: left;width: 17%;" @click="addLeftMember(mt)">{{mt.mobile}}</div>
                <div style="float: left;width: 20%;">
                  <div class="pc_pay140">
                    <div>{{mt.pay_type === 1 ? '零售价' : '会员价'}}{{Number(mt.disc).toFixed(1)}}折</div>
                    <img style="cursor: pointer;width: 22px;" alt="" @click="editDisc(index)"
                      v-show="$employeeAuth('edit_vips')" src="../../image/zgzn-pos/pc_member_edit.png" />
                  </div>
                </div>
                <div style="float: left;width: 18%;">
                  <div class="pc_pay140">
                    <div style="width: 80px;text-align: right;">{{Number(mt.has_money).toFixed(2)}}</div>
                    <img alt="" @click="addMemberMoney(index)" src="../../image/zgzn-pos/pc_member_recharge.png"
                      style="margin-left: 5px;cursor: pointer;" />
                  </div>
                </div>
                <div style="float: left;width: 15%;">
                  <el-popover
                    :disabled="(mt.integral+'').length<7"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    :width="mt.integral.length"
                    trigger="hover"
                    :content="mt.integral + ''">
                    <div slot="reference" style="float: left;width: 57px;text-align: right;text-overflow: ellipsis;overflow: hidden;">{{mt.integral}}</div>
                  </el-popover>
                  <div v-show="show_point_gift" @click="setMemberExchange(mt)" class="pc_pay140">
                    <img alt="" src="../../image/zgzn-pos/pc_member_exchange.png" style="margin-left: 5px;cursor: pointer;" />
                  </div>
                </div>
                <div style="float: left;width: 12%;">
                  <div id="toMemberCard" @click="toMemberCard(mt)">次卡</div>
                  <div id="toMemberDetail" @click="toMemberDetail(mt)">查看</div>
                </div>
              </div>
            </div>
            <div class="pc_pay116">
              <div style="font-size: 16px;float: left;margin-left: 22px;">共<span id="pay116">{{total}}</span>个会员</div>
              <el-pagination
                style="float: right;margin-right: 6px;"
                layout="prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
                :current-page="pagenum"
                :page-size="pageSize"
                :page-count="total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>

      <!--优惠折扣 bottomSelect == 2-->
      <div v-show="bottomSelect == 2" style="overflow: hidden;height: 100%;">
        <div class="pc_pay51">
          <!--整单折扣部分-->
          <div class="pc_pay52">
            <div>整单折扣</div>
            <div v-show="!edit" style="margin-left: 20px;cursor: pointer;" @click="editDiscount()">
              <img src="../../image/zgzn-pos/pc_member_score_edit.png" style="margin-top: -3px;" />
              <div class="editor">编辑</div>
            </div>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_pay53" v-for="(di,index) in discount_list" v-bind:key="index" @click="setDiscountNumber(di.number, index)" v-show="di.number !== '-'">
              <span>{{di.number}}折</span>
            </div>
            <div v-show="!edit" class="pc_pay58" @click="setDiscountReduceNumber(1)">
              自定义折扣
            </div>
          </div>
          <!--整单减价部分-->
          <div class="pc_pay52" style="margin-top: 30px;">
            <div>整单减价</div>
            <div v-show="!edit" style="margin-left: 20px;cursor: pointer;" @click="editReduce()">
              <img src="../../image/zgzn-pos/pc_member_score_edit.png" style="margin-top: -3px;" />
             <div class="editor">编辑</div>
            </div>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_pay59" v-for="(re,index) in reducePrice_list" v-bind:key="index"  @click="setReduceNumber(re.number, index)" v-show="re.number !== '-'">
              <span>{{re.number}}元</span>
            </div>
            <div v-show="!edit" class="pc_pay58" id="sale"  @click="setDiscountReduceNumber(2)">
              自定义减价
            </div>
          </div>
          <!--抹零方式部分-->
          <div class="pc_pay52" style="margin-top: 30px;">抹零方式</div>
          <div class="pc_pay141">
            <div class="pc_pay54" :class="clearZero == 0 ? 'pc_pay55' : ''" @click="clearZero = 0">
              不抹零
              <img v-show="clearZero == 0" src="../../image/zgzn-pos/pc_pay_duigou.png">
            </div>
            <div class="pc_pay54" :class="clearZero == 1 ? 'pc_pay55' : ''" @click="clearZero = 1">
              抹分
              <img v-show="clearZero == 1" src="../../image/zgzn-pos/pc_pay_duigou.png">
            </div>
            <div class="pc_pay54" :class="clearZero == 2 ? 'pc_pay55' : ''" @click="clearZero = 2">
              抹角
              <img v-show="clearZero == 2" src="../../image/zgzn-pos/pc_pay_duigou.png">
            </div>
            <div class="pc_pay54" :class="clearZero == 3 ? 'pc_pay55' : ''" @click="clearZero = 3" style="width: 130px;">
              四舍五入到角
              <img v-show="clearZero == 3" src="../../image/zgzn-pos/pc_pay_duigou.png">
            </div>
            <div class="pc_pay54" :class="clearZero == 4 ? 'pc_pay55' : ''" @click="clearZero = 4">
              逢分进角
              <img v-show="clearZero == 4" src="../../image/zgzn-pos/pc_pay_duigou.png">
            </div>
          </div>
        </div>
      </div>
      <!--编辑左侧商品，bottomSelect == 0-->
      <div v-show="bottomSelect == 0" style="overflow: hidden;height: 100%;position: relative;">
        <!--左上角删除按钮-->
        <div class="pc_pay84" @click="delLeftGoods()">
          <img src="../../image/pc_pay_del_left_list.png"/>
        </div>

        <!--上方图片及详情-->
        <div style="overflow: hidden;width: 450px;margin: 0 auto;color: #567485;">
          <div class="pc_pay143" :title="leftGoodsname">
            {{leftGoodsname}}
          </div>
          <div class="pc_pay100" v-if="leftGoodsname !== '直接收款'">
            <img v-show="left_img != '' && left_img != null" :src="$getSrcUrl(left_img)" />
            <!-- <img v-show="left_img != '' && left_img != null" :src="left_img" onerror="javascript:this.src='../../image/pc_no_cloth_img.png';this.onerror=null;" /> -->
            <img v-show="(left_img == '' || left_img == null) && (left_bigImg != '' && left_bigImg != null)" :src="$getSrcUrl(left_bigImg)" />
            <img v-show="(left_img == '' || left_img == null) && (left_bigImg == '' || left_bigImg == null) && left_pinyin == ''" src="../../image/pc_no_cloth_img.png"/>
            <div v-show="(left_img == '' || left_img == null) && (left_bigImg == '' || left_bigImg == null) && left_pinyin !== '' && left_pinyin.length > 2" class="pc_pay185" style="font-size: 64px;width: 160px;height: 160px;margin: 0;line-height: 75px;">
                {{left_pinyin.length > 2 ? left_pinyin.substring(0,2) : ''}}<br><span style="margin-left: 10px;">{{left_pinyin.substring(2,(left_pinyin.length > 4 ? 4 : left_pinyin.length))}}</span>
            </div>
            <div v-show="(left_img == '' || left_img == null) && (left_bigImg == '' || left_bigImg == null) && left_pinyin !== '' && left_pinyin.length <= 2 && left_pinyin.length > 0" class="pc_pay186" style="font-size: 64px;width: 160px;height: 160px;margin: 0;line-height: 160px;">
              {{left_pinyin}}
            </div>
          </div>
          <div class="pc_pay100" v-if="leftGoodsname === '直接收款'">
            <div class="pc_pay185" style="font-size: 64px;width: 160px;height: 160px;margin: 0;line-height: 75px;">
              直接<br><span style="margin-left: 11px;">收款</span>
            </div>
          </div>
          <div class="pc_pay142">
            <div class="pc_pay144" v-if="leftGoodsname !== '直接收款'">
              <div style="width: 102px;float: left;">
                条码：
              </div>
              <div style="float: left;">
                {{leftCode}}
              </div>
            </div>
            <div class="pc_pay145">
              <div class="pc_pay146">
                售价：
              </div>
              <div v-show="!inputLeftSalesprice" class="pc_pay147">
                ¥{{Number(leftSalesprice).toFixed(2)}}
                <img v-show="!ifautoCash && $employeeAuth('modify_sale_price')"  @click="showEditSalesprice()"
                  src="../../image/pc_pay_edit_left_list.png" />
              </div>
              <div v-show="inputLeftSalesprice" class="pc_pay148">
                <input
                  id="pa2"
                  ref="leftSalespricedRef"
                  v-model="leftSalesprice"
                  @focus="leftSalespriceFocus()"
                  placeholder="售价" style="text-indent: 3px;" />
                <div @click="changeLeftSalesprice();inputLeftNumber = true;inputLeftSalesprice = false;"
                  class="pc_pay149">保存</div>
              </div>
            </div>
            <div class="pc_pay150">
              <div class="pc_pay146">
                单品折扣：
              </div>
              <div v-show="!inputLeftDisc" class="pc_pay147">
                {{Number(leftDisc).toFixed(0)}}%
                <img v-show="$employeeAuth('modify_sale_price') && !ifautoCash && (left_ismember_day_goods
                  && !showMember || !left_ismember_day_goods)"
                  @click="showEditLeftdisc()" src="../../image/pc_pay_edit_left_list.png" />
              </div>
              <div v-show="inputLeftDisc" class="pc_pay148">
                <input id="pa3" v-model="leftDisc"
                  @focus="leftDiscFocus()"
                  ref="leftDiscdRef"
                  @input="leftDisc = $intMaxMinLimit({data: leftDisc, max: 100, min: 0})"
                  placeholder="折扣" />
                <div class="pc_pay151" style="margin-left: -22px;color: #B1C3CD;">%</div>
                <div @click="changeLeftDisc();inputLeftNumber = true;inputLeftDisc = false;" class="pc_pay149">
                  保存
                </div>
              </div>
            </div>
            <div style="margin-top: 10px;width: 100%;">
              <div style="width: 102px;float: left;margin-top: 5px;">
                数量：
              </div>
              <div class="pc_pay152">
                <!--计数器-->
                <div class="pc_pay153">
                  <div class="pc_pay154" @click="inputNumber('-')">—</div>
                  <div id="pa1" class="pc_pay155">
                    {{leftNumber || 1}}
                  </div>
                  <div class="pc_pay156" @click="inputNumber('+')">+</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--下方计算器-->
        <div class="pc_pay61" style="margin-top: 20px;max-width: 480px;">
          <table>
            <tr>
              <td @click="editGoodsKeyboard('7')">7</td>
              <td @click="editGoodsKeyboard('8')">8</td>
              <td @click="editGoodsKeyboard('9')">9</td>
              <td @click="editGoodsKeyboard('back')" style="font-size: 26px;">删除</td>
            </tr>
            <tr>
              <td @click="editGoodsKeyboard('4')">4</td>
              <td @click="editGoodsKeyboard('5')">5</td>
              <td @click="editGoodsKeyboard('6')">6</td>
              <td v-show="weighUnitList.indexOf(left_unit) !== -1 || inputLeftNumber === false" @click="editGoodsKeyboard('.')" style="font-size: 20px;">·</td>
              <td v-show="weighUnitList.indexOf(left_unit) === -1 && inputLeftNumber === true" class="grey_point" style="font-size: 20px;color: #BBB;">·</td>
            </tr>
            <tr>
              <td @click="editGoodsKeyboard('1')">1</td>
              <td @click="editGoodsKeyboard('2')">2</td>
              <td @click="editGoodsKeyboard('3')">3</td>
              <td @click="editGoodsKeyboard('0')">0</td>
            </tr>
          </table>
        </div>
      </div>
      <!--手选商品/手输条码页面 bottomSelect == 3 || bottomSelect == 4-->
      <div id="pc_payMain" v-show="bottomSelect == 3 || bottomSelect == 4" style="overflow: hidden;height: 100%;">
        <div class="pc_search_container">
          <!-- 电子秤重量 -->
          <div v-if="hasWeigh && weighShowInPay" class="pc_scales_container">
            <div class="weight_led_number">
              <span class="led">{{Number(weighSet).toFixed(3)}}</span>kg
            </div>
            <div class="scales_connect_status">
              <div :class="weighStatus === '·已连接' ? 'success_point' : 'error_point'"></div>
              <div :class="weighStatus === '·已连接' ? '' : 'error_span'">{{weighStatus === '·已连接'? '已连接' : '未连接'}}</div>
              <el-popover
                @show="setClass()"
                popper-class="pc_pay192 pc_pay192_padding"
                placement="bottom-end"
                width="411"
                trigger="click"
                content="可前往【设置】-【配件设置】-【电子秤设置】调试。">
                <div v-show="weighStatus === '·未连接'" class="pc_weight6 pc_popover_margin" slot="reference">?</div>
              </el-popover>
            </div>
          </div>
          <!--搜索按钮-->
          <div class="pc_pay157">
            <cj-input
              ref="cjInputRef"
              v-model="rightKeyword"
              type="goodsName"
              width="420"
              height="56"
              autofocus
              focus-select
              left-icon="el-icon-search"
              clear-trigger="always"
              placeholder="商品名称/首字母/条码/扫码"
              @input="toGetProdList"></cj-input>
            <img src="../../image/zgzn-pos/pc_pay_keyboard.png" style="margin-left: 8px;cursor: pointer;" @click="bottomSelect === 3 ? bottomSelect = 4 : bottomSelect = 3">
          </div>
        </div>
        <!-- 搜索按钮下方，超出滚动 -->
        <div id="list_id" class="pc_pay158">
          <!--计算器-->
          <div v-show="bottomSelect == 4" class="pc_pay61">
            <table>
              <tr>
                <td @click="inputCalculator('7')">7</td>
                <td @click="inputCalculator('8')">8</td>
                <td @click="inputCalculator('9')">9</td>
                <td @click="inputCalculator('back')" style="font-size: 24px;">删除</td>
              </tr>
              <tr>
                <td @click="inputCalculator('4')">4</td>
                <td @click="inputCalculator('5')">5</td>
                <td @click="inputCalculator('6')">6</td>
                <td @click="inputCalculator('del')" style="font-size: 24px;">清空</td>
              </tr>
              <tr>
                <td @click="inputCalculator('1')">1</td>
                <td @click="inputCalculator('2')">2</td>
                <td @click="inputCalculator('3')">3</td>
                <td @click="inputCalculator('0')">0</td>
              </tr>
            </table>
          </div>

          <!--商品列表-->
          <!--商品父div-->
          <div class="pc_pay45">
            <!--每一个商品div-->
            <div v-for="(rg,index) in right_goods_list" v-bind:key="index"
              @click="addLeftGoodsList(index,'click')" class="pc_pay46">
              <div style="overflow: hidden;">
                <!--商品图片-->
                <img v-lazy="$getSrcUrl(rg.image)" v-if="rg.image != '' && rg.image != null" :src="$getSrcUrl(rg.image)" />
                <img v-show="(rg.image == '' || rg.image == null) && rg.pinyin == ''" src="../../image/pc_no_cloth_img.png"/>
                <div v-show="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length > 2" class="pc_pay185">
                    <span >{{rg.pinyin.length > 2 ? rg.pinyin.substring(0,2) : ''}}</span><br><span style="margin-left: 10px;">{{rg.pinyin.substring(2,(rg.pinyin.length > 4 ? 4 : rg.pinyin.length))}}</span>
                </div>
                <div v-show="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length <= 2 && rg.pinyin.length > 0" class="pc_pay186">
                  {{rg.pinyin}}
                </div>
                <div v-if="set_StockShow" class="stock-style"
                  :style="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length > 0 ? 'border-radius: 0px 0px 20px 20px;' : 'border-radius: 0px;'">
                  {{(rg.curStock + '').length > 5 ? '' : '库存:'}}
                  {{rg.curStock || 0}}
                </div>
                <div class="pc_pay162">
                  <!--商品名字-->
                  <p :title="rg.name">{{rg.name}}</p>
                </div>
              </div>
              <div class="pc_pay47">
                <div class="pc_pay163">¥&nbsp;{{Number(rg.salePrice).toFixed(2)}}</div>
              </div>
            </div>
          </div>
          <!-- empty -->
          <div v-show="right_goods_list.length === 0" class="pc_goods_empty">
            <div>
              <img src="../../image/pc_no_goods.png" />
              <div class="center">暂无商品信息</div>
            </div>
          </div>
        </div>
      </div>
      <!--直接收款页面-->
      <!--计算器-->
      <div v-show="bottomSelect == 5">
        <div class="pc_pay164" @click="focusZJSK()">
          <div class="pc_pay165" :style="rightKeyword === '' ? 'color: #B2C3CD;' : 'color: #567485;'">
            <input id="dirMoney" ref="dirMoney" type="text" @input="rightKeyword = $priceLimit(rightKeyword)"
              v-model="rightKeyword"
              style="width: 200px;border: none;background: #F5F8FB;" placeholder="请输入金额" />
          </div>
          <div style="float: right;margin-right: 30px;">元</div>
        </div>
        <div class="pc_pay61" style="margin-top: 0;">
          <table>
            <tr>
              <td @click="inputZJSK('7')">7</td>
              <td @click="inputZJSK('8')">8</td>
              <td @click="inputZJSK('9')">9</td>
              <td @click="inputZJSK('0')">0</td>

            </tr>
            <tr>
              <td @click="inputZJSK('4')">4</td>
              <td @click="inputZJSK('5')">5</td>
              <td @click="inputZJSK('6')">6</td>
              <td @click="inputZJSK('.')" style="font-size: 20px;">·</td>
              <!-- <td @click="inputZJSK('del')" style="font-size: 26px;">清空</td> -->
            </tr>
            <tr>
              <td @click="inputZJSK('1')">1</td>
              <td @click="inputZJSK('2')">2</td>
              <td @click="inputZJSK('3')">3</td>
              <td @click="inputZJSK('back')">删除</td>
            </tr>
          </table>
          <div v-show="rightKeyword == '' || Number(rightKeyword) == 0">添加(+)</div>
          <div v-show="rightKeyword != '' && Number(rightKeyword) != 0" @click="subToLeftList()">添加(+)</div>
        </div>
      </div>

      <!--没有商品，扫码后新增商品界面-->
      <div v-show="bottomSelect == 6" style="height: 100%;">
        <div class="pc_pay166">
          <img class="pc_pay95" v-if="showNullOrTrimEmpty(newGoods.image)" @error.once="$imgError($event)" :src="newGoods.image" />
          <img class="pc_pay95" v-else src="../../image/pc_no_cloth_img.png"/>
          <div class="pc_pay96">
            <!--第一行-->
            <div class="pc_pay97" style="margin-top: 7px;">
              <div class="pc_pay98">商品条码</div>
              <div class="pc_pay99">
                <el-input v-model="newGoods.code" placeholder="请输入内容" style="width: 100%;" disabled></el-input>
              </div>
            </div>
            <!--第三行-->
            <div class="pc_pay97">
              <div class="pc_pay98">商品类别</div>
              <div style="width: calc(100% - 135px); float: left; height: 40px; line-height: 36px; background: #fff; border: 1px solid #E5E8EC; border-radius: 4px; margin-top: 0; text-indent: 20px; font-weight: normal; color: #567485; margin-left: 10px;"
               @click="quanxian() ? '' : showSelectCategory()"
               :style="quanxian() ? 'background-color: #F5F7FA; border-color: #E4E7ED; color: #C0C4CC; cursor: not-allowed;' : ''"
              >
                  <span
                    style="color:#567485"
                    :style="quanxian() ? 'background-color: #F5F7FA; border-color: #E4E7ED; color: #C0C4CC; cursor: not-allowed;' : ''"
                  >
                    {{ newGoods.typeName }}
                  </span>
                  <span
                    style="margin-right: 8px; color: #999; float: right;"
                    :style="quanxian() ? 'background-color: #F5F7FA; border-color: #E4E7ED; color: #C0C4CC; cursor: not-allowed;' : ''"
                  >
                    〉
                  </span>
              </div>
              <el-popover
                @show="setClass"
                popper-class="pc_pay192"
                placement="bottom"
                width="245"
                trigger="click"
                content="需要【商品权限】-【新增商品】的权限方可修改商品类别">
                <div v-show="quanxian()" class="pc_pay191" slot="reference">?</div>
              </el-popover>
            </div>
            <!--第四行-->
            <div class="pc_pay97">
              <div class="pc_pay98">单位</div>
              <div style="width: calc(100% - 135px); float: left; height: 40px; line-height: 36px; background: #fff; border: 1px solid #E5E8EC; border-radius: 4px; margin-top: 0; text-indent: 20px; font-weight: normal; color: #567485; margin-left: 10px;"
                @click="quanxian() ? '' : showSelectManage(newGoods.unitName) "
                :style="quanxian() ? 'background-color: #F5F7FA; border-color: #E4E7ED; color: #C0C4CC; cursor: not-allowed;' : ''"
              >
                <span v-show="newGoods.unitName != ''">{{
                  newGoods.unitName
                }}</span>
                <span v-show="newGoods.unitName == ''">请选择</span>
                <span
                  style="margin-right: 8px; color: #999; float: right;"
                  :style="quanxian() ? 'background-color: #F5F7FA; border-color: #E4E7ED; color: #C0C4CC; cursor: not-allowed;' : ''"
                >
                〉
                </span>
              </div>
              <el-popover
                @show="setClass"
                popper-class="pc_pay192"
                placement="bottom"
                width="245"
                trigger="click"
                content="需要【商品权限】-【新增商品】的权限方可修改单位">
                <div v-show="quanxian()" class="pc_pay191" slot="reference">?</div>
              </el-popover>
            </div>
            <!--第五行-->
            <div class="pc_pay97 pc_relivate">
              <div class="pc_pay98"><span>*</span>商品名称</div>
              <div class="pc_pay99">
                <el-input
                  v-model="newGoods.name"
                  placeholder="请输入内容"
                  ref="goodsNameRef"
                  @focus="addNameFocus = true;"
                  @blur="addNameFocus = false;"
                  @change="checkNameExist()"
                  style="width: 100%;"
                  :style="nameExist ? 'border-radius: 4px;border: 1px solid #FF6159' : ''"
                  :maxlength="goodsNameLength"
                  clearable></el-input>
              </div>
              <!-- 商品名重复check -->
              <div
                class="prod_name_repeat"
                :style="nameExist ? 'visibility: inherit' : 'visibility: hidden'">
                *商品名称重复，请修改商品名称
              </div>
            </div>
            <!--第三四行-->
            <div class="pc_pay97" style="margin-top: 0px;">
              <div class="pc_pay98"><span>*</span>商品售价</div>
              <div class="pc_pay99">
                <el-input ref="salePriceRef" id="pa4" @focus="selectText('pa4')" v-input-price="{ data: 'newGoods.salePrice' }" v-model="newGoods.salePrice"
                  placeholder="请输入内容" style="width: 100%;" maxlength="7"></el-input>
              </div>
            </div>
            <!--第四行-->
            <div class="pc_pay97">
              <div class="pc_pay98">进货价</div>
              <div class="pc_pay99">
                <el-input id="pa5" :disabled="!$employeeAuth('create_products') || !$employeeAuth('import_products') ||
                !$employeeAuth('delete_products') || !$employeeAuth('products_curstock_inventories')"
                @focus="selectText('pa5')" v-input-pur-price="{ data: 'new_encry' }" v-model="new_encry" placeholder="请输入内容" style="width: 100%;" maxlength="13"></el-input>
              </div>
              <el-popover
                @show="setClass"
                popper-class="pc_pay192"
                placement="bottom"
                width="245"
                trigger="click"
                content="需要【商品权限】-【新增商品】的权限方可修改进货价">
                <div v-show="!$employeeAuth('create_products') || !$employeeAuth('import_products')||
                !$employeeAuth('delete_products')|| !$employeeAuth('products_curstock_inventories')" class="pc_pay191" slot="reference">?</div>
              </el-popover>
            </div>
            <div class="pc_pay167">
              <div class="pc_pay91 pc_pay168" @click="submitNewGoods()" id="submitNewGoods" style="width: 120px;margin-right: 0;">保存</div>
              <div class="pc_pay9" @click="bottomSelect = 3" style="float: right;width: 120px;margin-left: 0;">取消</div>
            </div>
          </div>
          <div class="pc_pay169">
            带<span>*</span>号为必填内容，修改更多商品信息请到：首页-商品管理
          </div>
        </div>
      </div>

      <!--右侧底部，5个按钮部分-->
      <div class="pc_pay3" :style="bottomSelect === 3 && screenWidth < 1025 ? 'width: calc(100% - 92px)' : ''">
        <!--第一个按钮-->
        <div :class="ifautoCash || ultimate === null ? 'pc_pay40' : 'pc_pay31'" @click="chooseVip()" v-show="!ifautoCash && ultimate !== null">
          <!--蓝色小三角-->
          <div style="height: 16px;">
            <div class="pc_pay32" v-show="bottomSelect == 1"></div>
          </div>
          <!--按钮内容-->
          <div class="pc_pay33" :style="screenWidth < 1025 ? 'height: 70px;' : ''" :class="bottomSelect == 1 ? 'pc_pay36' : ''">
            <div class="pc_pay34">选择会员 (F4)</div>
          </div>
        </div>
        <!--第一个按钮-->
        <div :class="ifautoCash || ultimate === null ? 'pc_pay40' : 'pc_pay31'"
          @click="$employeeAuth('use_cashier_discount') ? bottomSelect = 2:''" v-show="!ifautoCash"
          :style="!$employeeAuth('use_cashier_discount') ? 'cursor: not-allowed;opacity: 40%':''">
          <!--蓝色小三角-->
          <div style="height: 16px;">
            <div class="pc_pay32" v-show="bottomSelect == 2 && $employeeAuth('use_cashier_discount')"></div>
          </div>
          <!--按钮内容-->
          <div class="pc_pay33" :style="screenWidth < 1025 ? 'height: 70px;' : ''"
            :class="bottomSelect == 2 && $employeeAuth('use_cashier_discount') ? 'pc_pay36' : ''">
            <div class="pc_pay34">优惠折扣 (F5)</div>
          </div>
        </div>
        <!--第二个按钮-->
        <div :class="ifautoCash || ultimate === null ? 'pc_pay40' : 'pc_pay31'" @click="bottomSelect = 3">
          <!--蓝色小三角-->
          <div style="height: 16px;">
            <div class="pc_pay32" v-show="bottomSelect == 3"></div>
          </div>
          <!--按钮内容-->
          <div class="pc_pay33" :style="screenWidth < 1025 ? 'height: 70px;' : ''" :class="bottomSelect == 3 || bottomSelect == 4 ? 'pc_pay36' : ''">
            <div class="pc_pay34">手选商品 (F6)</div>
          </div>
        </div>
        <!--第四个按钮-->
        <div :class="ifautoCash || ultimate === null ? 'pc_pay40' : 'pc_pay31'"
          @click="$employeeAuth('cashier_direct') ? selectZJSK():''"
          style="margin-right: 0;" :style="!$employeeAuth('cashier_direct') ? 'cursor: not-allowed;opacity: 40%':''">
          <!--蓝色小三角-->
          <div style="height: 16px;">
            <div class="pc_pay32" v-show="bottomSelect == 5 && $employeeAuth('cashier_direct')"></div>
          </div>
          <!--按钮内容-->
          <div class="pc_pay33" :style="screenWidth < 1025 ? 'height: 70px;' : ''" :class="bottomSelect == 5 && $employeeAuth('cashier_direct') ? 'pc_pay36' : ''">
            <div class="pc_pay34">直接收款 (F8)</div>
          </div>
        </div>
      </div>
    </div>
    <cj-mask :visible.sync="showPartialReturn">
      <partly-back v-if="showPartialReturn"
        :good-list="salseGoods"
        :order-info="orderInfo"
        @backDetail="showPartialReturn = false;goDetail(true)"
        @close="showPartialReturn = false"></partly-back>
    </cj-mask>
    <!-- 退货弹窗 -->
    <el-dialog
      v-if="showBackMoneyDialog"
      :visible.sync="showBackMoneyDialog"
      :show-close="false"
      :close-on-click-modal="false"
      width='680px'>
      <BackMoneyDialog
        :params="backMoneyParams"
        @backDetail="showBackMoneyDialog = false;goDetail(true)"
        @closeDialog="showBackMoneyDialog = false"/>
    </el-dialog>
    <confirm-dialog
      :visible.sync="visible"
      customClass="testClass"
      :message="`暂无此商品，<br/>请先完善商品信息`"
      :showCancel="false"
      @confirm="batchConfirm"
      :closeOnClickModal="false"
    ></confirm-dialog>
    <confirm-dialog
      :visible.sync="zeroMoneyTipsShow"
      confirm-text="继续退货"
      :message="`订单中有
        <span style='color: #FF0000'>0元</span>
        商品，<br/>
        是否继续退货？`"
      @confirm="zeroMoneyTipsShow = false, showPartialReturn = true"
      @cancel="zeroMoneyTipsShow = false"
      :closeOnClickModal="false"
    />
  </div>
</template>
<script>
import logList from '@/config/logList';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import pyCode from '@/common/pyCode';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import hotkeys from 'hotkeys-js';
import PartlyBack from '@/common/components/PartlyBack';
import BackMoneyDialog from "@/components/back_money_dialog.vue";
import CjInput from '@/common/components/CjInput';
import CjMask from '@/common/components/CjMask';
import TypeMenu from '@/common/components/TypeMenu';
// import { Keyevent } from '@/utils/keyEvent.js';
import refundParams from '@/mixins/refundParams';
import { getGenerateOrder } from '@/api/order';
export default {
  mixins: [refundParams],
  components: {
    CjMask,
    PartlyBack,
    BackMoneyDialog,
    CjInput,
    TypeMenu,
    ConfirmDialog
  },
  data() {
    return {
      specsOptions: [],
      show_left_goods_list: [],
      databaseListKeyword: '',
      weights: 0,
      showLeftGoodsList: [],
      member_integral: 0,
      rightOverflow: false,
      printFlag: false,
      rightHeight: '0px',
      edit: false,
      priceoff: '',
      selfoff: '',
      edit_focus: 0,
      number_reduce_list: [],
      member_day_goods: '', // 会员日做活动的商品list
      show_point_gift: '', // 会员选项卡，是否显示积分兑换可点击
      left_goods_list: [],
      database_list: [],
      right_goods_list: [],
      discount_list: [
        { 'number': '9.8' },
        { 'number': '9.5' },
        { 'number': '9.0' },
        { 'number': '8.8' }
      ],
      reducePrice_list: [
        { 'number': '1.00' },
        { 'number': '5.00' },
        { 'number': '10.00' },
        { 'number': '20.00' }
      ],
      radio_border: true,
      input_password: false,
      open_lock: '',
      /**
       * 底部按钮选择
       * 0.编辑左侧列表商品
       * 1.选择会员
       * 2.优惠折扣
       * 3.手选商品
       * 4.手输条码
       * 5.直接收款
       * 6.没有商品时新增商品
       */
      bottomSelect: 3,
      mid_goods: {},
      secondGoods: {},
      rightKeyword: '',
      continueAddZJSK: false, // 继续添加直接收款
      discountIndex: null, // 整单折扣选择
      reduceIndex: null, // 整单减价选择
      discountNumber: 0, // 记录打折的折扣数量
      reduce_number: 0, // 记录整单减价的减价数
      clearZero: 0, // 抹零方式
      mid_remark: '', // 挂单时，临时储存
      total_number: 0, // 左下方总数量
      totalPrice: '0.00', // 左下方合计金额
      reducePrice: 0, // 左下方优惠金额
      finalPrice: 0, // 左下方理论应收金额（没有抹零前的数据）
      showFinalPrice: '0.00', // 左下方要展示应收金额
      show_input_number: false,
      // 称重弹出窗口
      showInputWeight: false, // 是否显示称重窗口
      inputWeight: '', // 商品重量（从电子秤自动获取，手动输入）
      inputPrice: '', // 称重窗口的商品售价（可修改）
      inputKeyboardValue: '', // 称重页面数字键盘的输入值（因为检索框中的关键字绑定的关系，输入值时会显示在搜索框中， 所以单独起变量）
      is_temp_modify: false, // 是否临时修改价格
      weight_amount: '0.00', // 称重页面金额
      isWeightPriceInputable: false, // 称重页面当前是否可输入售价（针对窗口数字键盘）
      isWeightWeightInputable: false, // 称重页面当前是否可输入重量（针对窗口数字键盘）
      mid_index: 0, // 多规格商品选择
      set_mid_right: [],
      buy_back: 1,
      inputLeftSalesprice: false, // 点击左侧list时,初始化所有输入框,只有数量为true
      inputLeftNumber: true,
      leftNumberInit: '',
      set_leftList_index: '', // 点击左侧列表弹出来的详情信息
      leftNumber: '',
      leftSalesprice: '',
      leftGoodsname: '',
      leftCode: '',
      left_unit: '',
      left_img: '',
      left_pinyin: '',
      left_ismember_day_goods: '',
      isMemberDayGoods: false,
      type: '',
      category_list: [], // 初始化加载分类列表
      alltype: '',
      codeScan: false,
      codeScanFrom: '',
      dwArrowDisable: null,
      caseFormat: false, // 扫码枪变量
      scanTimeer: null, // 设置定时器变量
      acctsId: 1, // 付款方式（1现金2银行存款3POS收银账户4微信5支付宝）
      limit: 40,
      // zjsk_id: '', // 右下角直接收款的id
      watchStatus: true, // 监听事件，点击左侧列表商品，右侧显示详情
      remark: '', // 说明
      showInputRemark: false,
      // get_price: null,
      listen_sm: true, // 扫码枪在称重时不进行扫码
      choose_left: null, // 左侧列表选中了哪个，则给底色
      setSystem: true,
      kexianPriceType: 'price', // 客显单品价格显示 price：单价 amt：小计
      set_priceNotice: true,
      alert_unit: '', // 弹出的单位是他选中商品的单位
      show_clear_left_goods_list: false, // 清空商品前询问
      watch_type: true, // 是否允许监听type切换
      can_getProdList: true, // 是否允许请求商品列表接口
      /**
       * 点击数字和固定金额
       * 1.如果数字，则记录 record_input_style = 0
       * 2.如果固定金额，则记录 record_input_style = 1
       * 3.跟上次输入方式不一样，清空
       */
      record_input_style: 0,
      alert_remark: '',
      ifpay: false,
      weixin_aoid: '',
      zhifubao_aoid: '',
      sm_loading: false, // 扫码支付等待框
      inputing_keyword: '',
      keyword: '',
      choose_specs_index: 0,
      memTableData: [],
      total: 0, // 会员分页用
      pagenum: 1, // 会员分页用
      pageSize: 8, // 会员分页用
      member_disc: '', // 编辑会员
      show_edit_disc: false,
      show_edit_pwd: false,
      showMember: false,
      left_member_name: '',
      member_pwd: '', // 修改会员密码时的变量
      input_member_password: '',
      member_mobile: '',
      detail: {
        name: '',
        mobile: '',
        password: '',
        disc: '',
        has_money: '',
        birthday: '',
        addr: '',
        remark: '',
        pay_type: 1
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      memberPayType: '', // 选择会员后，该会员用零售价为1，该会员用会员价为2
      list_pagenum: 1, // 商品页数
      stop_scrollFn: false,
      leftDisc: 100,
      inputLeftDisc: false,
      show_database_list: false,
      left_id: '',
      continue_deleteAllDatabaseList: false,
      database_list_length: '',
      member_money: '',
      returnOutTradeNo: '',
      // 销售单退货时，记录单号和list，如果点结算不一致，不能用会员退款
      recordReturnGoodsList: [],
      waiting_click: false,
      goods_fingerprint_list: [],
      goods_fingerprint_old_list: [],
      watch_pause: false, // 暂定监听事件
      addNameFocus: false,
      nameExist: false,
      showEditPrice: false,
      inputEditPrice: '',
      useEditPrice: false,
      getQd: false, // 取单中
      lastTime: '', // keydown毫秒差
      showDeleteDatabaseList: false,
      autoPacking: false,
      delOrderId: '',
      specs_index: [],
      specs_show: [-1, -1, -1],
      left_bigImg: '',
      weight_title: '',
      member_day_discount: '', // 会员日商品折扣
      showLastOrderDetail: false,
      showPartialReturn: false,
      showBackMoneyDialog: false,
      backMoneyParams: {},
      show_weighing_category: false,
      hasBarCodeScales: false,
      new_encry: '***',
      newGoods: {
        id: '',
        majorCode: '',
        code: '',
        name: '',
        pinyin: '',
        typeId: '2',
        typeName: '其他分类',
        typeFingerprint: md5('其他分类'),
        unitName: '',
        unitId: '',
        unitFingerprint: '',
        salePrice: '0.00',
        vipPrice: '0.00',
        purPrice: '0.00',
        curStock: '0',
        addStockNum: '0',
        minStock: '0',
        maxStock: '0',
        remark: '',
        image: '',
        has_image: 0,
        supplier_id: 0,
        initStock: 0,
        initPrice: 0,
        initAmt: 0,
        img_fingerprint: '',
        first_letters: '',
        specs: {unlock: {}}
      },
      packingGoodsMap: {},
      // 条码重复商品选择
      repeatCode: '',
      repeatData: [],
      vipActiveIndex: null,
      lastProdsHeight: 400,
      from: 'pay',
      orderno: '',
      set_StockShow: false,
      printClick: false,
      hotkeysString: '',
      visible: false, // 收银台扫码弹窗
      defaultType: '', // 默认分类
      orderInfo: {}, // 上一单订单信息
      salseGoods: [], // 上一单销售商品列表
      hasRefundType: -1, // 上一单退款状态，0部分退款,1整单退款,-1未退款
      zeroMoneyTipsShow: false, // 是否显示订单中有0元商品提示弹窗
      keyEvent: null, // 键盘事件监听
      combinedItem: {},
      baseLimit: 10,
      basePageNum: 0,
      loading: false,
      keyword_timer: null,
      isFirst: false
    };
  },
  created() {
    this.getHotkeysString();
    // 开启扫码枪监听事件
    external.scanerHookStart();
    // 按键监听事件
    this.addListenevent();
    this.getDatabaseListLength();
    this.SET_SHOW({ cardNo: '' });
    this.getSpecs();
    // 条码秤开关状态
    let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
    this.hasBarCodeScales = dataInfo.hasBarCodeScales ? demo.t2json(dataInfo.hasBarCodeScales) : false;
    this.set_StockShow = dataInfo.set_StockShow ? demo.t2json(dataInfo.set_StockShow) : false;
    this.getDefaultAcctsId(dataInfo);
    // 启用电子秤且收银台显示重量开关开启时，打开电子秤串口
    if (this.hasWeigh && this.weighShowInPay) {
      this.openScale();
    }
  },
  mounted() {
    let mainHeight = document.getElementById('pc_payMain').clientHeight;
    this.rightHeight = (mainHeight - 108) + 'px';
    this.SET_SHOW({isVipDay: false});
    if (pos.network.isConnected()) {
      this.getSettingVipDay();
    }
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    document.getElementById('list_id').addEventListener('scroll', this.scrollFn);
    this.lastProdsHeight = $(window).height() - 377;
    window.addEventListener('resize', this.listenResize);
    // 获取直接收款id
    // if ($zjsk.length === 0) {
    //   this.getProductInsertFirst();
    // } else {
    //   this.zjsk_id = $zjsk[0].id;
    // }
    if (this.pc_return_goods && this.return_goods.length > 0) {
      console.log(this.return_goods, 'this.return_goods');
      console.log(this.returnGoodsMsg, 'this.return_goods');
      this.watch_pause = true;
      this.reduceIndex = -1;
      this.reduce_number = this.returnGoodsMsg.billAmt - this.returnGoodsMsg.discAmt;
      this.returnOutTradeNo = this.returnGoodsMsg.code;
      this.showMember = this.returnGoodsMsg.vipid !== null && this.returnGoodsMsg.vipid !== '0' && this.returnGoodsMsg.vipid !== 0;
      // console.log(this.returnGoodsMsg, 9713);
      this.left_goods_list = _.cloneDeep(this.return_goods);
      for (var i = 0; i < this.return_goods.length; i++) {
        // this.left_goods_list[i].id = this.left_goods_list[i].good_id;
        this.left_goods_list[i].fingerprint = this.left_goods_list[i].goodFingerprint;
        this.left_goods_list[i].name = this.left_goods_list[i].goodName;
        this.left_goods_list[i].salePrice = this.left_goods_list[i].price;
        this.left_goods_list[i].vipPrice = '0';
        this.left_goods_list[i].number = this.left_goods_list[i].qty;
        // this.left_goods_list[i].curStock = this.left_goods_list[i].curStock;
      }
      console.log(this.left_goods_list, 'this.left_goods_list');
      setTimeout(() => {
        this.getPriceList(107);
        this.recordReturnGoodsList = _.cloneDeep(this.left_goods_list);
      }, 0);
    }
    this.SET_SHOW({payListLength: this.left_goods_list.length});
    this.getSetting();
    var _this = this;
    storeInfoService.get({ id: 1 }, function(res) {
      var rs = res.length === 0 ? '' : demo.t2json(res)[0].discountSettings;
      // 2020-02-24
      if (rs !== '') {
        _this.discount_list = demo.t2json(rs).discount;
        _this.reducePrice_list = demo.t2json(rs).reduce;
      }
    });
    console.log(`Time: ${new Date().getTime()}, showFinalPrice: ${this.showFinalPrice}, watch_pause: ${this.watch_pause}`);
    hotkeys.filter = this.hotkeyFilter;
  },
  watch: {
    codeScan() {
      console.log(this.codeScan, 'From: ' + this.codeScanFrom);
    },
    databaseListKeyword() {
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        this.searchOrder();
      }, this.delayedTime);
    },
    showMember() {
      if (!this.showMember) {
        this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
      }
    },
    showLeftGoodsList() {
      if (this.showLeftGoodsList.length === 0) {
        this.getKexian('', 0);
      }
    },
    showMemberExchange() {
      if (!this.showMemberExchange) {
        this.memTableData = [];
        this.getMember();
      }
    },
    memberPayType() {
      if (this.bottomSelect === 0) {
        this.bottomSelect = 3;
      }
      if (this.watch_pause) {
        return;
      }
      this.getPriceList(9);
    },
    cardNo() {
      if (this.bottomSelect !== 1) {
        return;
      }
      this.rightKeyword = this.cardNo;
      this.pagenum = 1;
      this.getMember();
    },
    showAddMember() {
      if (this.showAddMember === 'close' && this.bottomSelect === 1) {
        this.pagenum = 1;
        this.getMember();
      }
    },
    getMember() {
      if (this.showMember === false) {
        this.member_id = '';
        this.left_member_name = '';
        for (var i = 0; i < this.left_goods_list.length; i++) {
          for (var j = 0; j < this.member_day_goods.length; j++) {
            if (this.left_goods_list[i].name === this.member_day_goods[j].name &&
              JSON.stringify(this.left_goods_list[i].specs === JSON.stringify(this.member_day_goods[j].specs))) {
              this.left_goods_list[i].disc = 100;
            }
          }
        }
      }
      this.getPriceList(10);
      if (this.bottomSelect === 0) {
        this.bottomSelect = 3;
      }
    },
    settlement() {
      console.log(this.settlement, 'settlement+');
      if (this.completePay === false && this.settlement === false) {
        if (this.showInputRecharge) {
          this.getMember();
          this.bottomSelect = 1;
        } else {
          this.bottomSelect = 3;
        }
        this.addListenevent();
        // this.can_getProdList = true;
        // this.getProdList(1);
      } else if (this.settlement === false) {
        this.refreshScreen2();
        this.getProdList(2);
        this.addListenevent();
      }
      if (this.settlement === true) {
        this.cancelListenevent();
      }
      this.packingGoodsMap = {};
    },
    discountNumber() {
      if (this.watch_pause) {
        return;
      }
      this.getPriceList(11);
    },
    // 结算完成
    completePay() {
      if (this.completePay === true) {
        this.beginPay();
        if (this.left_id !== '') {
          this.deleteDatabaseList(this.left_id);
        }
      }
    },
    // 编辑商品售价时，键盘和屏幕混合输入同步
    leftSalesprice() {
      if (this.inputLeftSalesprice) {
        this.rightKeyword = this.leftSalesprice.toString();
        if (this.rightKeyword > 999999.99) {
          this.rightKeyword = 999999.99;
          this.leftSalesprice = 999999.99;
        } else if (isNaN(this.rightKeyword)) {
          this.rightKeyword = 0;
          this.leftSalesprice = 0;
        } else if (this.rightKeyword < 0) {
          this.leftSalesprice = 0;
          this.rightKeyword = 0;
        } else {
          console.log('Unexpected value for rightKeyword');
        }
      }
    },
    // 编辑商品折扣时，键盘和屏幕混合输入同步
    leftDisc() {
      if (this.inputLeftDisc) {
        this.rightKeyword = this.leftDisc.toString();
        if (this.rightKeyword > 100) {
          this.rightKeyword = 100;
          this.leftDisc = 100;
        } else if (isNaN(this.rightKeyword)) {
          this.rightKeyword = 0;
          this.leftDisc = 0;
        } else if (this.rightKeyword < 0) {
          this.leftDisc = 0;
          this.rightKeyword = 0;
        } else {
          console.log('Unexpected value for rightKeyword');
        }
      }
    },
    showInputRemark() {
      if (this.showInputRemark) {
        setTimeout(function() {
          $('#remark').focus();
        }, 10);
      }
    },
    pc_return_goods() {
      setTimeout(() => { this.$refs.cjInputRef.focus(); });
      this.discountIndex = null;// 整单折扣选择
      this.reduceIndex = null;// 整单减价选择
      this.showMember = false;
      this.left_id = '';
      this.remark = '';
    },
    showInputWeight() {
      /**
       * 称重窗口显示，初始化称重窗口中的商品信息
       * 默认设定重量为当前数字键盘的输入对象
       */
      this.inputWeight = '';
      this.is_temp_modify = false;
      this.inputKeyboardValue = '';
      this.isWeightPriceInputable = false;
      this.isWeightWeightInputable = true;
      setTimeout(function () {
        $('#iw').focus();
      }, 0);
      document.getElementById('iw').value = '';
    },
    inputPrice() {
      if (this.isWeightPriceInputable) {
        // 当前页面的数字键盘输入价格
        this.inputKeyboardValue = this.inputPrice;
      }
      // 称重页面“售价”发生修改时， 即时更新“金额”
      this.calcWeightAmount();
    },
    inputWeight() {
      if (this.isWeightWeightInputable) {
        // 当前页面的数字键盘输入重量
        this.inputKeyboardValue = this.inputWeight;
      }
      // 称重页面“重量”发生修改时， 即时更新“金额”
      this.calcWeightAmount();
    },
    left_goods_list: {
      deep: true,
      handler: function() {
        console.log('left_goods_list watch +');
        if (this.left_goods_list.length === 0) {
          this.choose_left = null;
        }
        this.SET_SHOW({payListLength: this.left_goods_list.length});
        setTimeout(function() {
          document.getElementById('lg').scrollTop = document.getElementById('lg').scrollHeight;
        }, 0);
        this.left_goods_list.forEach(ef => { this.stockEnoughCheck(ef); });
        if (this.bottomSelect === 5 || this.bottomSelect === 0 || this.getQd) {
          return;
        }
        setTimeout(() => {
          this.getPriceList(12);
        }, 0);
      }
    },
    // 上一单信息
    lastOrderDetail: {
      deep: true,
      handler: function() {
        if (this.lastOrderDetail.length > 0) {
          this.SET_SHOW({ showLastOrder: true });
        } else {
          this.SET_SHOW({ showLastOrder: false });
        }
      }
    },
    discountIndex() {
      if (this.watch_pause) {
        return;
      }
      this.getPriceList(13);
    },
    reduceIndex() {
      if (this.watch_pause) {
        return;
      }
      this.getPriceList(14);
    },
    clearZero() {
      this.getPriceList(15);
    },
    leftCode() {
      this.inputLeftDisc = false;
      this.inputLeftSalesprice = false;
      this.inputLeftNumber = true;
      this.rightKeyword = '';
    },
    leftGoodsname() {
      this.inputLeftDisc = false;
      this.inputLeftSalesprice = false;
      this.inputLeftNumber = true;
      this.rightKeyword = '';
    },
    inputLeftDisc() {
      if (this.watchStatus && this.inputLeftDisc) {
        this.rightKeyword = this.leftDisc.toString();
        this.record_input_style = 1;
      }
      if (!this.inputLeftDisc) {
        this.leftDisc = this.left_goods_list[this.set_leftList_index].disc;
      }
    },
    inputLeftNumber() {
      if (this.watchStatus && this.inputLeftNumber) {
        this.rightKeyword = this.leftNumber.toString();
        this.record_input_style = 1;
      }
    },
    inputLeftSalesprice() {
      if (this.watchStatus && this.inputLeftSalesprice) {
        this.rightKeyword = this.leftSalesprice.toString();
      }
      if (!this.inputLeftSalesprice) {
        let item = this.left_goods_list[this.set_leftList_index];
        this.leftSalesprice = this.showMember && this.memberPayType == 2 ? item.vipPrice : item.salePrice;
      }
    },
    bottomSelect(newVal, oldVal) {
      console.log(this.bottomSelect, 'bottomSelect+');
      this.list_pagenum = 1;
      // 离开售价和折扣进入其他页面清空该属性
      this.inputLeftDisc = false;
      this.inputLeftSalesprice = false;
      this.right_goods_list = [];
      this.rightKeyword = '';
      this.continueAddZJSK = false;
      this.edit = false;
      this.$refs.cjInputRef.blur();
      if (this.bottomSelect === 1) {
        this.SET_SHOW({cardNo: ''});
        this.memTableData = [];
        this.vipActiveIndex = null;
        this.total = 0;
        this.pagenum = 1;
        this.getMember();
        if (pos.network.isConnected()) {
          this.getIntegralRatio();
        }
      }
      if (this.bottomSelect === 3) {
        this.watch_type = false;
        this.type = this.defaultType;
        setTimeout(() => {
          this.watch_type = true;
        }, 50);
        // this.can_getProdList = true;
        if (this.type === '-99') {
          this.getHotList();
        } else {
          this.getProdList();
        }
        if (oldVal !== 1) {
          setTimeout(() => { this.$refs.cjInputRef.focus(); });
        }
        if (this.showMember) {
          this.judgeIsCheckMember();
        }
      }
      if (this.bottomSelect === 4) {
        this.watch_type = false;
        this.type = this.defaultType;
        this.right_goods_list = [];
        var _this = this;
        setTimeout(function() {
          _this.watch_type = true;
        }, 50);
        setTimeout(() => { this.$refs.cjInputRef.focus(); });
      }
      // if (this.bottomSelect === 0) {
      //   this.keyEvent = new Keyevent(this.handleKeyEvent);
      //   this.keyEvent.start();
      // } else {
      //   if (this.keyEvent) {
      //     this.keyEvent.stop();
      //     this.keyEvent = null;
      //   }
      // }
    },
    scanerObj() { // C#扫码枪监听
      console.log('scanerObj++');
      this.continueAddZJSK = false;
      this.codeScan = true;
      this.codeScanFrom = 'scanerObj change';
      let scanerCode = demo.t2json(this.scanerObj).Result.replace(/[^\w-]|_/ig, '');
      if (scanerCode.length >= 8) {
        CefSharp.PostMessage(
          `钩子函数监听到扫码设备超过8位的输入，内容为:${scanerCode} 窗体是否处于活跃状态:${this.zgznActive}`
        );
      }
      // 新增商品弹窗 分类展示时 掌柜智囊窗体未获取焦点时 dialog显示时
      if (this.bottomSelect === 6 || this.showTypeManage || !this.zgznActive || this.dialogReturn() || this.settlement) {
        if (scanerCode.length >= 8) {
          CefSharp.PostMessage(`扫码设备超过8位的输入${scanerCode}被拦截了`);
        }
        return;
      }
      if (scanerCode.length > 16) {
        demo.msg('warning', '商品条码最长16位!');
      } else if (scanerCode.length < 4) {
        demo.msg('warning', '商品条码最短4位!');
      } else {
        console.log(scanerCode, 'scanerCode+++');
        this.watch_pause = true;
        this.setExceptionKeydown();
        this.onScanInput(scanerCode);
      }
    },
    isSyncing() {
      console.log(this.isSyncing);
    },
    inputKeyboardValue() {
      /**
       * 监听称重页面的数字键盘的输入值
       * 没有小数点时， 最多5位
       * 有小数点时，根据小数点来分解字符串， 将分解后的第一项跟第二项组合，保留2位小数
       */
      // 称重取消回车监听
      if (!this.watchStatus) {
        return;
      }
      if (this.ifautoCash) {
        return;
      }
      if (this.inputKeyboardValue.toString().indexOf('.') !== -1) {
        this.inputKeyboardValue = this.inputKeyboardValue.substring(0, 9);
        var mid_keyword = this.inputKeyboardValue.toString().split('.');
        this.inputKeyboardValue = mid_keyword[0] + '.' + mid_keyword[1].substring(0, 3);
      } else {
        if (Number(this.inputKeyboardValue) > 99999.999) {
          this.inputKeyboardValue = '99999.999';
        }
      }
      if (this.isWeightPriceInputable) {
        // 当前页面的数字键盘输入价格
        this.inputPrice = this.inputKeyboardValue;
      } else if (this.isWeightWeightInputable) {
        // 当前页面的数字键盘输入重量
        this.inputWeight = this.inputKeyboardValue;
      } else {
        console.log('Unexpected value for inputKeyboardValue');
      }
    },
    // 所有右侧搜索输入框、计算器的共用一个变量
    rightKeyword() {
      // 称重取消回车监听
      if (!this.watchStatus) {
        return;
      }
      // 如果底部选中了优惠折扣选项卡
      if (this.bottomSelect === 2) {
        this.rightKeywordDiscountSelected();
        if (this.edit) {
          this.number_reduce_list[this.edit_focus].number = this.rightKeyword.toString();
          return;
        }
      }
      this.rightKeywordFlg1();
      this.rightKeywordFlg2();
      this.rightKeywordFlg3();
      this.rightKeywordFlg4();
      // 如果是结算界面，往副屏传实收金额
      if (this.settlement) {
        // external.evaluateScript('demo.$store.commit("SET_SHOW",{screen2ReceiveMoney: ' + this.rightKeyword + '})');
        demo.screen2({'screen2ReceiveMoney': this.rightKeyword}, 19);
      }
    },
    leftNumber() {
      if (isNaN(this.leftNumber)) {
        this.leftNumber = 1;
      }
      if (this.watchStatus) {
        this.rightKeyword = this.leftNumber.toString();
        // 如果是称重，大于0即可，如果不是称重，最小为0
        if (this.weighUnitList.indexOf(this.left_unit) !== -1) {
          this.left_goods_list[this.set_leftList_index].number = this.leftNumber > 0 ? this.leftNumber : 1;
        } else {
          this.left_goods_list[this.set_leftList_index].number = this.leftNumber > 1 ? this.leftNumber : 1;
        }
      }
    },
    isWeightWeightInputable() {
      // 称重页面重量控件被点击后，将重量数据保存为数字键盘的默认输入初始值
      if (this.watchStatus && this.isWeightWeightInputable) {
        this.inputKeyboardValue = this.inputWeight.toString();
      }
    },
    isWeightPriceInputable() {
      // 称重页面售价控件被点击后，将售价数据保存为数字键盘的默认输入初始值
      if (this.watchStatus && this.isWeightPriceInputable) {
        this.inputKeyboardValue = this.inputPrice.toString();
      }
    },
    type() {
      console.log(this.type, 'type now');
      if (!this.watch_type) {
        return;
      }
      if (this.type === '-99') {
        this.getHotList();
        return;
      }
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.stop_scrollFn = true;
      // this.can_getProdList = true;
      // this.rightKeyword = '';
      this.list_pagenum = 1;
      this.right_goods_list = [];
      this.watchStatus = false;
      this.getProdList(4);
      var _this = this;
      setTimeout(function() {
        _this.watchStatus = true;
      }, 10);
    },
    // 开启自动收银，右下角选项卡切换成初始值
    ifautoCash() {
      if (this.ifautoCash) {
        this.showMember = false;
        this.discountIndex = null; // 整单折扣选择
        this.reduceIndex = null; // 整单减价选择
        this.bottomSelect = 3;
      }
      setTimeout(() => { this.$refs.cjInputRef.focus(); });
    },
    showTypeManage() {
      if (!this.showTypeManage) {
        // 刷新分类列表
        this.$refs.typeMenu.getAllCategory();
      }
    },
    addGoodsCategoryId() {
      this.newGoods.typeId = this.addGoodsCategoryId;
      this.newGoods.typeName = this.addGoodsCategory;
      this.newGoods.typeFingerprint = this.addGoodsCategoryfingerprint;
    },
    addGoodsUnit() {
      this.newGoods.unitId = this.addGoodsUnitId;
      this.newGoods.unitName = this.addGoodsUnit;
      this.newGoods.unitFingerprint = this.addGoodsUnitfingerprint;
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    searchOrder() {
      this.database_list = [];
      this.basePageNum = 1;
      this.loading = true;
      this.getDatabaseList(1);
    },
    setScrolltop() {
      const container = document.getElementById('base-main');
      container.scrollTop = 0;
    },
    openDataBase() {
      this.databaseListKeyword = '';
      this.database_list = [];
      this.basePageNum = 1;
      this.loading = true;
      this.getDatabaseList(1);
    },
    loadMore() { // 滚动加载商品
      this.loading = true;
      this.basePageNum += 1;
      console.log('触发load');
      this.getDatabaseList();
    },
    handleKeyEvent({ type, code }) {
      if (this.inputLeftSalesprice || this.inputLeftDisc || this.dialogReturn()) {
        return;
      }
      if (type === 2 && !isNaN(code)) {
        this.editGoodsKeyboard(code);
      } else if (type === 2 && code === 'Backspace') {
        this.editGoodsKeyboard('back');
      } else if (type === 2 && code === '.' && (this.weighUnitList.includes(this.left_unit) || !this.inputLeftNumber)) {
        this.editGoodsKeyboard('.');
      }
    },
    // 鼠标点击结算
    settlementClick() {
      this.$_actionLog(logList.settlement, `鼠标点击触发结算`);
    },
    // 删除光标处理
    setCaretPosition(ctrl, pos) {
      // 设置光标位置函数
      if (ctrl.setSelectionRange) {
        ctrl.focus();
        ctrl.setSelectionRange(pos, pos);
      } else if (ctrl.createTextRange) {
        var range = ctrl.createTextRange();
        range.collapse(true);
        range.moveEnd('character', pos);
        range.moveStart('character', pos);
        range.select();
      } else {
        console.log('');
      }
    },
    // 收银台新增商品弹窗回调
    batchConfirm() {
      this.visible = false;
      // 名称存在售价全选
      if (this.newGoods.name) {
        this.$refs.salePriceRef.select();
      } else {
        this.$refs.goodsNameRef.select();
      }
    },
    // 判断null，undefined, ''的条件
    showNullOrTrimEmpty(value) {
      if (demo.isNullOrTrimEmpty(value)) {
        return false;
      } else {
        return true;
      }
    },
    // 上显示上一单
    showLastOrderInfo() {
      this.showLastOrderDetail = true;
      demo.actionLog(logList.showLastOrder);
      const order = this.lastOrderDetail[0];
      saleService.getOrderByCode(order.code, ({ sales, saleitems, zuhe = {} }) => {
        console.log(sales, saleitems);
        if (sales.length && saleitems.length) {
          this.orderInfo = sales[0];
          this.salseGoods = saleitems;
          // 商品全部退完为整单退
          const boolFull = saleitems.every(good => good.remainingQty === 0);
          if (boolFull) {
            this.hasRefundType = 1;
            return;
          }
          // 商品有退货且没有全部退完为部分退货
          const boolPart = saleitems.some(good => good.refundedQty < 0);
          if (boolPart) {
            this.hasRefundType = 0;
            return;
          }
          this.hasRefundType = -1;
        }
        this.combinedItem = zuhe;
      });
    },
    showSelectCategory() {
      this.SET_SHOW({ showTypeManage: true });
    },
    showSelectManage(name) {
      this.SET_SHOW({ showSelManage: true });
      this.SET_SHOW({ selManageTypeName: '单位' });
      this.SET_SHOW({ selectName: name });
      this.SET_SHOW({ agreeChooseCategory: true });
    },
    submitNewGoods() {
      if (this.addNameFocus || this.nameExist) {
        return;
      }
      this.newGoods.name = this.newGoods.name.trim();
      if (this.newGoods.name === '') {
        demo.msg('warning', this.$msg.enter_product_name);
      } else if (isNaN(this.newGoods.salePrice)) {
        demo.msg('warning', this.$msg.price_must_be_number);
      } else if (isNaN(this.newGoods.purPrice)) {
        demo.msg('warning', this.$msg.cost_must_be_number);
      } else if (this.newGoods.salePrice === '') {
        demo.msg('warning', this.$msg.enter_price);
      } else {
        this.newGoods.first_letters = pyCode.getPyCode(this.newGoods.name);
        this.newGoods.purPrice = this.new_encry === '***' ? this.newGoods.purPrice : Number(this.new_encry).toFixed(6);
        this.newGoods.action = 'insert';
        this.newGoods.initPrice = this.newGoods.salePrice;
        this.newGoods.initAmt = (Number(this.newGoods.purPrice) * Number(this.newGoods.curStock)).toFixed(2);
        this.newGoods.manufactureDate = this.newGoods.manufactureDate || null;
        this.newGoods.expiryDays = this.newGoods.expiryDays || null;
        let data = {
          name: this.newGoods.name,
          majorCode: this.newGoods.majorCode,
          pinyin: this.newGoods.pinyin,
          typeFingerprint: this.newGoods.typeFingerprint,
          unitFingerprint: this.newGoods.unitFingerprint,
          ingredient: '',
          level: '',
          remark: this.newGoods.remark,
          image: this.newGoods.image,
          isVipDisc: 1,
          lockSpecs: {},
          items: [this.newGoods]
        };
        goodService.insert(data,
          () => {
            demo.msg('success', '新增商品成功!');
            this.onScanInput(this.newGoods.code, 'add_goods');
          },
          err => {
            demo.msg('warning', err);
          });
      }
    },
    toMemberCard(mt) {
      this.SET_SHOW({clickFrom: 'pay'});
      this.SET_SHOW({showTimesCardTab: false, showTimesCard: true, vipId: mt.id});
    },
    // showSpecStyle(m, n) {
    //   for (var i = 0; i < this.specs_index.length; i++) {
    //     if (this.specs_index[i][0] === m && this.specs_index[i][1] === n) {
    //       return true;
    //     }
    //   }
    //   return false;
    // },
    chooseSpecs(json, index) {
      this.choose_specs_index = index;
      this.secondGoods = json;
    },
    getSpecs() {
      // this.specs = Object.values(this.specs);
      var specs = Object.values(this.specs);
      console.log(specs, 9999888);
      var mid = [{value: '', label: '全部规格'}];
      for (var i = 0; i < specs.length; i++) {
        specs[i].value = specs[i].id;
        specs[i].label = specs[i].name;
        specs[i].list = Object.values(specs[i].item);
        for (var j = 0; j < specs[i].list.length; j++) {
          specs[i].list[j].value = specs[i].list[j].id;
          specs[i].list[j].label = specs[i].list[j].name;
        }
        specs[i].children = specs[i].list;
        mid.push(specs[i]);
      }
      this.specsOptions = mid;
      console.log(this.specsOptions, 7980);
    },
    chooseRightGoods(n) {
      this.specs_index = [];
      this.mid_index = n;
      console.log(this.right_goods_list[n], 'this.right_goods_list[n];');
      this.mid_goods = _.cloneDeep(this.right_goods_list[n]);
      this.mid_goods.specGroup = Object.values(this.mid_goods.specGroup);
      console.log(this.mid_goods, 'this.mid_goods');
      this.secondGoods = this.mid_goods.list[0];
      this.choose_specs_index = 0;
    },
    closeChoose() {
      this.SET_SHOW({ showChooseWeight: false });
    },
    getKexian(v, t) {
      console.log('客显显示价格:', v);
      console.log('客显显示类型:', t);
      if (this.showKexian) {
        external.customerdisplay({
          displaydata: v,
          displaytype: t,
          port: this.kexianValue,
          baudrate: '2400',
          databits: '8',
          parity: '0',
          stopBits: '1'
        });
      }
    },
    setClass () {
      let setClassEls = document.getElementsByClassName('popper__arrow');
      for (let i = 0; i < setClassEls.length; i++) {
        setClassEls[i].setAttribute('class', 'popper__arrow pc_pay193');
      }
    },
    openWeight(n) {
      console.log(n, 'openWeight n++');
      this.showInputWeight = true;
      this.SET_SHOW({ showChooseWeight: false });
      this.SET_SHOW({ isOnceOpen: false });
      if (this.weights === 0) {
        this.SET_SHOW({ hasWeigh: false });
      } else if (this.weights === 1) {
        this.SET_SHOW({ hasWeigh: true });
        this.SET_SHOW({ weighValue: 'COM1' });
        this.SET_SHOW({ weighTypeValue: 'DaHuaACS' });
      } else {
        this.SET_SHOW({ hasWeigh: true });
        this.SET_SHOW({ weighValue: 'COM2' });
        this.SET_SHOW({ weighTypeValue: 'HongHaiACS' });
      }
      var data = [
        { key: 'hasWeigh', value: this.hasWeigh, remark: '是否启用电子秤' },
        { key: 'weighValue', value: this.weighValue, remark: '电子秤端口' },
        { key: 'weighTypeValue', value: this.weighTypeValue, remark: '电子秤类型' },
        { key: 'isOnceOpen', value: this.isOnceOpen, remark: '是否是第一次打开称重商品' }
      ];
      settingService.put(data, () => {
        // todo
      });
      if (this.hasWeigh) {
        this.openScale();
      }
    },
    getSetting() {
      // 初始化获取抹零方式 获取价格设置
      var that = this;
      if ($setting.cutsmallmoney) {
        that.clearZero = isNaN($setting.cutsmallmoney) ? 0 : Number($setting.cutsmallmoney);
      }
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        if ($setting.stock) {
          this.setSystem = $setting.stock !== '0';
        }
        this.autoPacking = true;
      } else {
        if (demo.t2json(this.storeList[0].settings).setSystem === undefined) {
          if ($setting.stock) {
            this.setSystem = $setting.stock !== '0';
          }
        } else {
          this.setSystem = demo.t2json(this.storeList[0].settings).setSystem === '1';
        }
        this.autoPacking = demo.t2json(this.storeList[0].settings).autoPacking === '1';
        if (demo.t2json(this.storeList[0].settings).kexian_price_type === 'amt') {
          this.kexianPriceType = 'amt';
        }
      }
      if ($setting.set_priceNotice) {
        that.set_priceNotice = $setting.set_priceNotice === '0';
      }
    },
    toMemberDetail(mt) {
      var detail = _.cloneDeep(mt);
      this.SET_SHOW({ member_detail: detail });
      this.SET_SHOW({ showAddMember: 'readonly', clickFrom: 'vip' });
    },
    editPrice() {
      this.inputEditPrice = this.showFinalPrice;
      this.showEditPrice = true;
      setTimeout(() => {
        $('#inputEditPrice').select();
      }, 0);
    },
    continueEditPrice() {
      this.$_actionLog(logList.receivableChange, `修改应收确认,原应收${this.showFinalPrice},修改后应收${this.inputEditPrice}`);
      this.showFinalPrice = Number(this.inputEditPrice).toFixed(2);
      this.reducePrice = (Number(this.totalPrice) - Number(this.showFinalPrice)).toFixed(2);
      this.useEditPrice = true;
      let subData = {
        'screen2ReducePrice': this.reducePrice,
        'screen2ShowFinalPrice': this.showFinalPrice
      };
      demo.screen2(subData, 17);
      this.cancelEditPrice();
    },
    cancelEditPrice() {
      this.showEditPrice = false;
    },
    // rightKeyword 圈复杂度降低 start
    rightKeywordDiscountSelected() { // 底部选中了优惠折扣选项卡
      // 自定义折扣是整单折扣中的
      if (this.priceoff === 'discount' || this.selfoff === 'discount') {
        // 如果存在小数点,则最多三位
        // 如果不存在小数点,最多一位
        if (this.rightKeyword.indexOf('.') !== -1) {
          this.rightKeyword = this.rightKeyword.substring(0, 3);
        } else {
          this.rightKeyword = this.rightKeyword.substring(0, 1);
        }
      } else if (this.priceoff === 'reduce' || this.selfoff === 'reduce') {
        // 如果存在小数点,则最多八位
        // 如果不存在小数点,最多五位
        if (this.rightKeyword.indexOf('.') !== -1) {
          this.rightKeyword = this.rightKeyword.substring(0, 9);
          var mid_keyword = this.rightKeyword.toString().split('.');
          this.rightKeyword = mid_keyword[0] + '.' + mid_keyword[1].substring(0, 2);
        } else {
          this.rightKeyword = this.rightKeyword.substring(0, 6);
        }
      } else {
        console.log(this.rightKeyword);
      }
    },
    rightKeywordFlg1() {
      var flag_rkey1 = (this.bottomSelect === 5);
      if (flag_rkey1) {
        console.warn('rightKeywordFlg1')
        // 如果存在小数点,则最多八位
        // 如果不存在小数点,最多五位
        if (this.rightKeyword.indexOf('.') !== -1) {
          this.rightKeyword = this.rightKeyword.substring(0, 9);
          var mid_keyword1 = this.rightKeyword.toString().split('.');
          this.rightKeyword = mid_keyword1[0] + '.' + mid_keyword1[1].substring(0, 2);
        } else {
          this.rightKeyword = this.rightKeyword.substring(0, 6);
        }
      }
    },
    rightKeywordFlg2() {
      var flag_rkey2 = (this.bottomSelect === 0 && this.inputLeftNumber);
      if (flag_rkey2) { // 修改数量
        console.warn('rightKeywordFlg2')
        // 如果存在小数点,则最多九位 如果不存在小数点,最多五位
        if (this.rightKeyword.indexOf('.') !== -1) {
          // this.rightKeyword = this.rightKeyword.substring(0, 9);
          var mid_keyword3 = this.rightKeyword.toString().split('.');
          if (this.weighUnitList.indexOf(this.left_unit) === -1) {
            this.rightKeyword = mid_keyword3[0];
          } else {
            if (mid_keyword3[1].length > 3) {
              demo.msg('warning', '只精确到小数点后3位');
            }
            this.rightKeyword = mid_keyword3[0] + '.' + mid_keyword3[1].substring(0, 3);
          }
        } else {
          if (this.inputLeftNumber && this.rightKeyword.length > 5) {
            demo.msg('warning', '数量最多5位');
          }
          this.rightKeyword = this.rightKeyword.substring(0, 5);
        }

        this.leftNumber = this.rightKeyword;
        this.left_goods_list[this.set_leftList_index].kexianShowAmt = this.kexianPriceType === 'amt';
        // this.left_goods_list[this.set_leftList_index].isScales = false;
      }
    },
    rightKeywordFlg3() {
      var flag_rkey3 = (this.bottomSelect === 0 && this.inputLeftSalesprice);
      if (flag_rkey3) { // 修改售价
        console.warn('rightKeywordFlg3')
        // 如果存在小数点,则最多九位 如果不存在小数点,最多五位
        if (this.rightKeyword.indexOf('.') !== -1) {
          this.rightKeyword = this.rightKeyword.substring(0, 9);
          var mid_keyword4 = this.rightKeyword.toString().split('.');
          this.rightKeyword = mid_keyword4[0] + '.' + mid_keyword4[1].substring(0, 2);
        } else {
          this.rightKeyword = this.rightKeyword.substring(0, 6);
        }
        this.leftSalesprice = this.rightKeyword;
      }
    },
    rightKeywordFlg4() {
      var flag_rkey4 = (this.bottomSelect === 0 && this.inputLeftDisc);
      if (flag_rkey4) { // 修改单品折扣
        console.warn('rightKeywordFlg4')
        this.rightKeyword = this.$intMaxMinLimit({data: this.rightKeyword, max: 100, min: 0});
        this.leftDisc = this.rightKeyword;
      }
    },
    // rightKeyword 圈复杂度降低 end
    // 添加会员
    addMember () {
      if (!this.$employeeAuth('create_vips')) {
        return;
      }
      this.SET_SHOW({
        member_detail: {
          name: '',
          mobile: '',
          password: '',
          disc: '10',
          has_money: '',
          birthday: '',
          addr: '',
          remark: '',
          pay_type: 1
        }
      });
      this.SET_SHOW({ showAddMember: 'new' });
      demo.actionLog(logList.vipAdd);
    },
    print(dat) {
      if (this.setting_small_printer === undefined || this.setting_small_printer === null ||
        this.setting_small_printer.trim() === '') {
        demo.msg('warning', this.$msg.not_setting_small_printer);
        return;
      }
      if (!this.printFlag) {
        this.printFlag = true;
        if (this.setting_small_printer !== undefined && this.setting_small_printer !== null && this.setting_small_printer.trim() !== '') {
          pos.printer.printRegister(dat);
        } else {
          demo.msg('warning', this.$msg.not_setting_small_printer);
        }
        setTimeout(() => {
          this.printFlag = false;
        }, 1000);
      }
    },
    clearMouse(id) {
      let timer = document.getElementById(id).getAttribute('timer');
      if (timer) {
        clearInterval(timer);
      }
    },
    mouseOne(id) {
      let selectDiv = document.getElementById('selectRight');
      if (id === 'up') {
        selectDiv.scrollTop -= 70;
      } else {
        selectDiv.scrollTop += 70;
      }
    },
    mousedown(id) {
      let selectDiv = document.getElementById('selectRight');
      let timer = setInterval(() => {
        if (id === 'up') {
          selectDiv.scrollTop -= 70;
        } else {
          selectDiv.scrollTop += 70;
        }
      }, 100);
      document.getElementById(id).setAttribute('timer', timer);
    },
    editGoodsKeyboard(n) {
      // 自助收银模式下称重商品不允许修改数量
      if (this.ifautoCash && this.weighUnitList.indexOf(this.left_goods_list[this.set_leftList_index].unitName) !== -1) {
        return;
      }
      this.inputCalculator(n);
      if (this.inputLeftNumber) {
        this.leftNumberInit = '';
      }
      if (this.inputLeftSalesprice || this.inputLeftDisc) {
        return;
      }
      setTimeout(() => {
        this.getPriceList(104);
      }, 0);
    },
    chooseVip() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，会员功能暂时无法使用');
      } else {
        this.bottomSelect = 1;
      }
    },
    formatReturn() {
      if (isNaN(Number(this.inputPrice)) || this.inputPrice === '') {
        this.inputPrice = '';
      } else {
        Number(this.inputPrice) > 999999.99 ? this.inputPrice = 999999.99 : this.inputPrice = Math.abs(Number(this.inputPrice));
      }
    },
    editFocus(index) {
      // this.rightKeyword = val;
      this.record_input_style = 1;
      this.edit_focus = index;
      $('#edit_input' + index).select();
    },
    editFocusBlur(val) {
      this.rightKeyword = val;
    },
    editDiscount() {
      this.priceoff = 'discount';
      this.number_reduce_list = _.cloneDeep(this.discount_list);
      this.rightKeyword = '';
      var that = this;
      setTimeout(function() {
        that.edit = true;
      }, 0);
      setTimeout(function () {
        $('#edit_input0').focus();
      }, 10);
    },
    editReduce() {
      this.priceoff = 'reduce';
      this.number_reduce_list = _.cloneDeep(this.reducePrice_list);
      this.rightKeyword = '';
      var that = this;
      setTimeout(function() {
        that.edit = true;
      }, 0);
      setTimeout(function () {
        $('#edit_input0').focus();
      }, 10);
    },
    sonarSaveEdit1(i) {
      return Number(this.number_reduce_list[i].number) > 9.9 ? '9.9' : Number(this.number_reduce_list[i].number).toFixed(1);
    },
    sonarSaveEdit2(i) {
      return Number(this.number_reduce_list[i].number) > 999999.99 ? '999999.99' : Number(this.number_reduce_list[i].number).toFixed(2);
    },
    saveEdit() {
      let flag = false;
      let msg = '';
      let index = 0;
      for (var i = 0; i < this.number_reduce_list.length; i++) {
        if (this.number_reduce_list[i].number === '' || this.number_reduce_list[i].number === '-' || isNaN(this.number_reduce_list[i].number)) {
          this.number_reduce_list[i].number = '-';
        } else if (this.priceoff === 'discount') {
          this.number_reduce_list[i].number = this.sonarSaveEdit1(i);
          if (Number(this.number_reduce_list[i].number) === 0) {
            this.number_reduce_list[i].number = '';
            index = i;
            flag = true;
            msg = '整单折扣不可设置为0';
          }
        } else {
          this.number_reduce_list[i].number = this.sonarSaveEdit2(i);
          if (Number(this.number_reduce_list[i].number) === 0) {
            this.number_reduce_list[i].number = '';
            index = i;
            flag = true;
            msg = '整单减价不可设置为0';
          }
        }
      }
      if (flag) {
        $('#edit_input' + index).focus();
        demo.msg('warning', msg);
        return;
      }
      this.priceoff === 'discount'
        ? this.discount_list = _.cloneDeep(this.number_reduce_list)
        : this.reducePrice_list = _.cloneDeep(this.number_reduce_list);
      this.edit = false;
      this.updateDiscount();
    },
    updateDiscount() {
      storeInfoService.updateDiscountSettings({
        'id': 1,
        'discountSettings': JSON.stringify({
          'discount': this.discount_list,
          'reduce': this.reducePrice_list
        })});
    },
    setDiscountNumber(n, index) {
      this.watch_pause = true;
      this.discountIndex = index;
      this.discountNumber = n;
      this.reduce_number = 0;
      this.reduceIndex = null;
      setTimeout(() => {
        this.getPriceList(101);
        this.watch_pause = false;
      }, 0);
    },
    setReduceNumber(n, index) {
      this.watch_pause = true;
      this.reduceIndex = index;
      this.reduce_number = n;
      this.discountNumber = 10;
      this.discountIndex = null;
      setTimeout(() => {
        this.getPriceList(102);
        this.watch_pause = false;
      }, 0);
    },
    clearMember() {
      this.rightKeyword = '';
      this.pagenum = 1;
      this.getMember();
      $('#select_keyword1').focus();
    },
    inputNumber(str) {
      // 自助收银模式下称重商品不允许修改数量
      if (this.ifautoCash && this.weighUnitList.indexOf(this.left_goods_list[this.set_leftList_index].unitName) !== -1) {
        return;
      }
      this.inputLeftNumber = true;
      this.inputLeftSalesprice = false;
      this.inputLeftDisc = false;
      if (str === '-' && Number(this.leftNumber) > 1) {
        this.leftNumber = this.setMaxDecimal(Number(this.leftNumber) - 1, 3);
      } else if (str === '+' && Number(this.leftNumber) < 99999) {
        this.leftNumber = this.setMaxDecimal(Number(this.leftNumber) + 1, 3);
      } else {
        console.log(str);
      }
      this.left_goods_list[this.set_leftList_index].isScales = false;
      this.left_goods_list[this.set_leftList_index].kexianShowAmt = this.kexianPriceType === 'amt';
      this.record_input_style = 1;
      setTimeout(() => {
        this.getPriceList(106);
      }, 0);
    },
    getSettingVipDay() {
      demo.$http
        .post(
          this.$rest.pc_vipGetSetting,
          {
            phone: this.sys_uid,
            sysSid: this.sys_sid,
            systemName: $config.systemName
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0' && res.data.data) {
            this.vipday_setting = res.data.data.vipdaySetting ? demo.t2json(res.data.data.vipdaySetting) : this.vipday_setting;
            if (this.vipday_setting.enable === false) {
              return;
            }
            // today_member_day先不赋值为true，继续请求商品list页面，成功则赋值true

            /**
             * 会员日判断：
             * 1.如果会员日设置是每月xx号，跟今天的日期进行对比，相同则为会员日；
             * 2.如果会员日设置是每周周n，跟今天的星期m进行对比，相同则为会员日。
             * 如果今天是会员日，请求会员日商品列表
             */
            if ((this.vipday_setting.vipday_flg === 'month' && this.vipday_setting.days === new Date().getDate()) ||
          (this.vipday_setting.vipday_flg === 'week' && this.vipday_setting.week === new Date().getDay())) {
              this.member_day_discount = this.vipday_setting.discount * 10;
              this.getProductVipDay();
            }
          } else if (res.data.code !== '0') {
            if (res.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
              demo.msg('warning', res.data.msg);
            }
          } else {
            console.log(res);
          }
        });
    },
    getProductVipDay() {
      demo.$http
        .post(
          this.$rest.pc_getProductVipDay,
          {
            phone: this.sys_uid,
            sysSid: this.sys_sid,
            systemName: $config.systemName
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            if (res.data.data) {
              this.goods_fingerprint_list = res.data.data.map((e) => e.fingerprint);
            }
            this.getMasterName();
          }
        });
    },
    // 初始化获取本地商品master数据
    getMasterName() {
      let data = {
        pset: '',
        type: '',
        // condition: this.show_add_member_goods ? this.keyword : '',
        // limit: this.limit,
        fingerprint: this.goods_fingerprint_list,
        // fingerprintFlg: this.show_add_member_goods,
        vipGoods: true,
        // offset: Number((this.pagenum - 1) * this.limit),
        // pset: '',
        // type: '',
        // condition: '',
        limit: 100000,
        // fingerprint: this.goods_fingerprint_list,
        fingerprintFlg: false,
        // vipGoods: true,
        offset: 0,

        // pset: '',
        // isGroup: false,
        // type: '',
        condition: ''
        // limit: this.limit,
        // selectDel: false
        // vipGoods: true,
        // getDel: false,
        // offset: 0
      };
      goodService.search(data, (res) => {
        console.log(res, '会员日活动商品');
        this.member_day_goods = demo.t2json(res);
        this.member_day_goods = this.member_day_goods.map((goods) => {
          this.goods_fingerprint_old_list.find(
            (fingerprint_old) =>
              fingerprint_old.fingerprint === goods.fingerprint
          );
          return goods;
        });
        if (this.member_day_goods.length > 0) {
          /**
           * 满足两种情况到这个位置
           * 1.今天是会员日
           * 2.会员日商品列表不为空
           */
          this.SET_SHOW({isVipDay: true});
        }
      });
    },
    isWeightProd(unit) {
      return this.weighUnitList.indexOf(unit) !== -1;
    },
    getIntegralRatio() {
      demo.$http.post(this.$rest.pc_vipGetSetting,
        {
          phone: this.sys_uid,
          sysSid: this.sys_sid,
          systemName: $config.systemName
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).then(res => {
        if (res.data.code === '0') {
          if (res.data.data &&
            demo.t2json(res.data.data.pointSetting).score_deduction.enable === true &&
            demo.t2json(res.data.data.pointSetting).score_deduction.deduction_flg === 'gift' &&
            this.$employeeAuth('exchange_vip_points')) {
            this.show_point_gift = true;
          } else {
            this.show_point_gift = false;
          }
        } else {
          this.show_point_gift = false;
          if (res.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
            demo.msg('warning', res.data.msg);
          }
        }
      });
    },
    setMemberExchange(mt) {
      if (pos.network.isConnected()) {
        this.SET_SHOW({showMemberExchangeId: mt.id,
          showMemberExchangeIntegral: mt.integral,
          showMemberExchangeName: mt.name,
          member_detail: _.cloneDeep(mt),
          showMemberExchange: true});
        demo.actionLog(logList.exchangePoints);
      } else {
        demo.msg('warning', '本地网络处于离线状态，兑换商品暂时无法使用');
      }
    },
    // 收银台初始化
    beginPay() {
      this.useEditPrice = false;
      this.watch_pause = true;
      this.discountIndex = null;
      this.reduceIndex = null;
      this.discountNumber = 10; // 记录打折的折扣数量
      this.reduce_number = 0; // 记录整单减价的减价数
      this.rightKeyword = '';
      this.bottomSelect = 3;
      this.remark = '';
      this.left_goods_list = [];
      this.showMember = false;
      this.member_id = '';
      this.left_member_name = '';
      this.getPriceList(200);
      setTimeout(() => {
        this.watch_pause = false;
      }, 0);
    },
    getDatabaseListLength() {
      var that = this;
      recordBillsService.getCount(function(res) {
        that.database_list_length = demo.t2json(res)[0].cnt;
        if (that.database_list_length === '0' && that.show_database_list) {
          that.show_database_list = false;
        }
      });
    },
    toDeleteDatabaseList(did) {
      this.delOrderId = did;
      this.showDeleteDatabaseList = true;
    },
    deleteDatabaseList(list_id) {
      recordBillsService.delete({'id': list_id}, () => {
        this.getDatabaseListLength();
        if (this.show_database_list) {
          this.database_list = [];
          this.basePageNum = 1;
          this.loading = true;
          this.getDatabaseList(1);
        }
        this.showDeleteDatabaseList = false;
        if (list_id === this.left_id) {
          this.left_id = '';
        }
      });
    },
    deleteAllDatabaseList() {
      this.continue_deleteAllDatabaseList = false;
      var that = this;
      recordBillsService.delete({}, function() {
        that.getDatabaseListLength();
        that.show_database_list = false;
      });
    },
    databaseToLeftList(id) {
      this.watch_pause = true;
      this.getQd = true;
      recordBillsService.updateRecordBillsGoodIdAndStock(id, res => {
        var rs = demo.t2json(demo.t2json(res)[0].info);
        console.log(rs, 'rs++++');
        this.left_id = id;
        this.SET_SHOW({pc_return_goods: rs.pc_return_goods});
        this.showFinalPrice = Number(rs.showFinalPrice).toFixed(2);
        this.showMember = rs.showMember;
        this.left_member_name = rs.left_member_name;
        this.memberPayType = rs.memberPayType;
        this.acctsId = rs.acctsId;
        this.member_mobile = rs.member_mobile;
        this.member_id = rs.member_id;
        this.member_money = rs.member_money;
        this.totalPrice = Number(rs.totalPrice).toFixed(2);
        this.total_number = rs.total_number;
        this.discountIndex = rs.discountIndex;
        this.discountNumber = rs.discountNumber;
        this.reduceIndex = rs.reduceIndex;
        this.reduce_number = rs.reduce_number;
        this.remark = rs.remark;
        this.useEditPrice = rs.useEditPrice === undefined ? false : rs.useEditPrice;
        this.reducePrice = (Number(this.totalPrice) - Number(this.showFinalPrice)).toFixed(2);
        this.show_database_list = false;
        this.refreshGoodsStock(rs); // 处理rs.left_goods_list的
      });
    },
    refreshGoodsStock(rs) {
      this.left_goods_list = rs.left_goods_list;
      this.showLeftGoodsList = _.cloneDeep(this.left_goods_list);
      setTimeout(() => {
        this.watch_pause = false;
        this.getQd = false;
        this.databaseRefreshScreen2(rs);
      }, 100);
      /**
       *  更新挂单商品的当前库存(不允许无库存销售时)
       *  goodService.refreshGoodsStock
       *  param: ['d478aad78d1d44dbd5bea0fb196ceb50', 'c7a33e93063645ab9e972df8b5364408']
       *  response: "{'d478aad78d1d44dbd5bea0fb196ceb50': 1, 'c7a33e93063645ab9e972df8b5364408', 666}"
       */
      // let fingerprintList = rs.left_goods_list.map(item => { return item.fingerprint; }).join();
      // console.log(fingerprintList, 'fingerprintList+++');
      // goodService.refreshGoodsStock(fingerprintList, () => {
      // {'d478aad78d1d44dbd5bea0fb196ceb50': 1, 'c7a33e93063645ab9e972df8b5364408', 666}
      //   this.left_goods_list = rs.left_goods_list;
      //   this.showLeftGoodsList = _.cloneDeep(this.left_goods_list);
      //   setTimeout(() => {
      //     this.watch_pause = false;
      //     this.getQd = false;
      //     this.databaseRefreshScreen2(rs);
      //   }, 100);
      // });
    },
    databaseRefreshScreen2(rs) {
      var sData = {
        'screen2ShowList': this.left_goods_list,
        'screen2ReducePrice': this.reducePrice,
        'screen2TotalPrice': this.totalPrice,
        'screen2ShowFinalPrice': this.showFinalPrice,
        'screen2ShowMember': this.showMember,
        'screen2MemberMoney': this.member_money,
        'screen2ReturnGoods': this.pc_return_goods,
        'screen2isSinglePayWay': true,
        'screen2MemberPayType': this.memberPayType,
        'screen2MemberName': this.left_member_name,
        'screen2MemberPoint': rs.member_integral,
        'screen2MemberPhone': rs.member_mobile
      };
      demo.screen2(sData, 15);
    },
    listenResize() {
      // 浏览器高度$(window).height()
      this.lastProdsHeight = $(window).height() - 377;
    },
    addDatabaseList() {
      if (this.left_goods_list.length === 0) {
        demo.msg('warning', '请先选择商品！');
        return;
      }
      const keys = ['remark', 'packing', 'url', 'sort', 'weighModelVal',
        'tare', 'majorCode', 'firstLetters', 'curStock', 'initStock', 'minStock', 'maxStock',
        'hasImage', 'isDeleted', 'isSynced', 'createAt', 'reviseAt', 'syncAt', 'localUrl',
        'imgFingerprint', 'manufactureDate', 'expiryDay', 'expireDate', 'kexianShowAmt']
      const list = this.left_goods_list.map(good => {
        keys.forEach(key => {
          delete (good[key]);
        })
        return good;
      })
      const sub_data = {
        pc_return_goods: this.pc_return_goods,
        showFinalPrice: this.showFinalPrice,
        showMember: this.showMember,
        left_member_name: this.showMember ? this.left_member_name : '',
        memberPayType: this.showMember ? this.memberPayType : '',
        acctsId: this.acctsId,
        member_mobile: this.showMember ? this.member_mobile : '',
        member_id: this.showMember ? this.member_id : '',
        left_goods_list: list,
        totalPrice: this.totalPrice,
        total_number: this.total_number,
        discountIndex: this.discountIndex,
        discountNumber: this.discountNumber,
        reduceIndex: this.reduceIndex,
        reduce_number: this.reduce_number,
        remark: this.remark
      };
      this.left_goods_list = [];
      console.log(sub_data, 5543);
      if (this.left_id) {
        recordBillsService.update({'id': this.left_id, 'info': JSON.stringify(sub_data)}, () => {
          this.getDatabaseListLength();
        });
      } else {
        recordBillsService.insert({'info': JSON.stringify(sub_data), 'uid': this.loginInfo.uid}, () => {
          this.getDatabaseListLength();
        });
      }
      this.beginPay();
      this.left_id = '';
      this.bottomSelect = 3;
    },
    /**
     * type: 1为首次打开挂单需要重置滚动条
     */
    getDatabaseList(type) {
      if (this.database_list_length == 0) {
        return;
      }
      let data = {
        limit: this.baseLimit,
        offset: (this.basePageNum - 1) * this.baseLimit
      }
      if (this.databaseListKeyword) {
        data.keyword = this.databaseListKeyword;
      }
      let list = _.cloneDeep(this.database_list);
      recordBillsService.select(data, res => {
        let re = _.cloneDeep(demo.t2json(res));
        for (var i = 0; i < demo.t2json(res).length; i++) {
          re[i].info = demo.t2json(re[i].info);
        }
        this.database_list = list.concat(re);
        console.log(this.database_list, '挂单数据');
        this.loading = false;
        if (this.database_list.length > 0) {
          this.show_database_list = true;
        }
        if (type === 1) {
          setTimeout(() => {
            this.setScrolltop();
          }, 0);
        }
      });
    },
    toGetProdList() {
      this.can_getProdList = false;
      this.watch_type = false;
      this.stop_scrollFn = true;
      var that = this;
      if (Number(this.bottomSelect) === 1) {
        return;
      }
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.watch_type = true;
        that.can_getProdList = true;
        that.list_pagenum = 1;
        that.getProdList(6);
      }, that.delayedTime);
    },
    toGetVip() {
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        this.pagenum = 1;
        this.vipActiveIndex = null;
        this.getMember();
      }, this.delayedTime);
    },
    scrollFn() {
      if (this.stop_scrollFn === true || this.type === '-99') {
        return;
      }
      var test = document.getElementById('list_id');
      var clientH = test.clientHeight;
      var scrollT = test.scrollTop;
      var wholeH = test.scrollHeight;
      if (clientH + scrollT + 50 >= wholeH) {
        this.stop_scrollFn = true;
        var that = this;
        setTimeout(function() {
          that.stop_scrollFn = false;
        }, 1000);
        // this.can_getProdList = true;
        if (this.rightKeyword === '') {
          this.getProdList(7);
        } else {
          this.getProdList(9);
        }
      }
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      this.vipActiveIndex = null;
      this.getMember();
    },
    changeLeftSalesprice() {
      if (this.watchStatus) {
        this.rightKeyword = this.leftSalesprice.toString();
        // memberPayType零售价为1，会员价为2
        if (this.showMember && this.memberPayType == 2) {
          this.left_goods_list[this.set_leftList_index].vipPrice = this.leftSalesprice;
        } else {
          this.left_goods_list[this.set_leftList_index].salePrice = this.leftSalesprice;
        }
        // ==单价
        if (this.kexianPriceType === 'amt') {
          this.left_goods_list[this.set_leftList_index].kexianShowAmt = true;
        } else {
          this.getKexian(Number(this.leftSalesprice).toFixed(2), 1);
        }
      }
      this.left_goods_list[this.set_leftList_index].isScales = false;
      this.getPriceList(105);
    },
    leftDiscFocus() {
      this.inputLeftNumber = false;
      this.inputLeftDisc = true;
      this.inputLeftSalesprice = false;
      this.selectText('pa3');
    },
    changeLeftDisc() {
      if (this.watchStatus) {
        this.rightKeyword = this.leftDisc.toString();
        if (isNaN(this.leftDisc) || Number(this.leftDisc) > 100 || Number(this.leftDisc) < 0) {
          this.leftDisc = 100;
        } else {
          this.leftDisc = Number(this.leftDisc).toFixed(0);
        }
        let relPrice = Number(this.leftSalesprice) * (this.leftDisc / 100);
        // ==单价
        if (this.kexianPriceType === 'amt') {
          this.left_goods_list[this.set_leftList_index].kexianShowAmt = true;
        } else {
          this.getKexian(Number(relPrice).toFixed(2), 1);
        }
        this.left_goods_list[this.set_leftList_index].isScales = false;
        this.left_goods_list[this.set_leftList_index].disc = this.leftDisc;
      }
      this.getPriceList(201);
    },
    changeKeyword() {
      if (!/^[0-9]+$/.test(this.rightKeyword)) {
        this.rightKeyword = '';
      } else {
        this.toGetProdList();
      }
    },
    clearLeftGoodsList() {
      this.showMember = false;
      this.left_goods_list = [];
      this.remark = '';
      this.discountIndex = null;
      this.reduceIndex = null;
      this.bottomSelect = 3;
      this.getKexian('', 0);
      this.$_actionLog(logList.clearGoods, `清空列表确定`)
    },
    editLeftList(n, ef) {
      this.watchStatus = false;
      this.set_leftList_index = n;
      this.leftSalesprice = this.showMember && this.memberPayType == 2 && this.left_goods_list[n].vipPrice != 0
        ? this.left_goods_list[n].vipPrice : this.left_goods_list[n].salePrice;
      this.leftNumber = this.left_goods_list[n].number;
      this.leftNumberInit = this.leftNumber + '';
      this.leftGoodsname = this.left_goods_list[n].name;
      this.leftCode = this.left_goods_list[n].code;
      this.left_img = this.left_goods_list[n].image;
      this.left_bigImg = this.left_goods_list[n].bigImg;
      this.left_pinyin = this.left_goods_list[n].pinyin;
      this.leftDisc = this.left_goods_list[n].disc;
      this.left_unit = this.left_goods_list[n].unitName;
      this.bottomSelect = 0;
      this.inputLeftDisc = false;
      this.inputLeftSalesprice = false;
      this.inputLeftNumber = true;
      var _this = this;
      this.left_ismember_day_goods = this.left_goods_list[n].isMemberDayGoods;
      this.getPriceList();
      setTimeout(function() {
        _this.watchStatus = true;
        _this.rightKeyword = `${_this.leftNumber}`;
        _this.isFirst = true;
      }, 100);
    },
    checkMemberDisc() {
      if (isNaN(this.member_disc)) {
        this.member_disc = 10;
      } else if (this.member_disc > 10 || this.member_disc < 0.1) {
        this.member_disc = 10;
      } else {
        console.log('其他');
      }
      this.member_disc = Number(this.member_disc).toFixed(1);
    },
    getMember(isAdd) {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，会员功能暂时无法使用');
        return;
      }
      var _this = this;
      var vipdata = {
        'systemName': $config.systemName,
        'phone': _this.sys_uid,
        'sysSid': _this.sys_sid,
        'thisMonthBirthday': false,
        'currentPage': _this.pagenum,
        'pageSize': _this.pageSize,
        'is_deleted': 0
      };
      if (_this.rightKeyword != null || _this.rightKeyword != '') {
        vipdata.searchStr = _this.rightKeyword;
      }
      demo.$http.post(_this.$rest.pc_vipList, vipdata, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          rs.data.data.list.forEach(element => {
            element['show'] = false;
          });
          _this.memTableData = rs.data.data.list;
          _this.total = rs.data.data.total;
          if (_this.memTableData.length === 1 && isAdd) {
            _this.addLeftMember(_this.memTableData[0]);
          }
        });
    },
    showEditSalesprice() {
      if (this.ifautoCash) {
        return;
      }
      // 会员日商品禁止临时改售价
      if (this.left_ismember_day_goods && this.showMember) {
        return demo.msg('warning', '会员日商品无法临时修改售价，如需修改请前往商品管理');
      }
      setTimeout(() => {
        document.getElementById('pa2').focus();
      });
      this.inputLeftNumber = false;
      this.inputLeftDisc = false;
      this.inputLeftSalesprice = true;
      this.record_input_style = 1;
    },
    leftSalespriceFocus() {
      this.inputLeftNumber = false;
      this.inputLeftSalesprice = true;
      this.selectText('pa2');
    },
    showEditLeftdisc() {
      if (this.ifautoCash || (this.left_ismember_day_goods && this.showMember)) {
        return;
      }
      setTimeout(() => {
        document.getElementById('pa3').focus();
      });
      this.inputLeftNumber = false;
      this.inputLeftDisc = true;
      this.inputLeftSalesprice = false;
    },
    addListenevent() {
      document.body.addEventListener('keydown', this.keydown, false);
      setTimeout(() => {
        this.hotKeySet();
      });
    },
    cancelListenevent() {
      document.body.removeEventListener('keydown', this.keydown, false);
      this.hotKeyCancel();
    },
    dialogReturn() {
      return this.pcLockScreen || !this.listen_sm || this.showChooseWeight || this.showInputWeight ||
        this.bottomSelect === 1 || this.show_database_list || this.showInputRemark || this.showEditPrice ||
        this.settlement || this.edit || this.show_input_number || this.isPrintSetting ||
        this.continue_deleteAllDatabaseList || this.showTypeManage || this.showSelManage || this.autoCashShow ||
        this.showPayExitMsg || this.showPayChangeStatus1 || this.showPayChangeStatus2 || this.showPayPrint ||
        this.show_clear_left_goods_list || this.showPartialReturn || this.showBackMoneyDialog || this.zeroMoneyTipsShow;
    },
    showRemarkDialog() {
      this.alert_remark = this.remark;
      this.showInputRemark = true;
      setTimeout(() => {
        this.$refs.remark.focus();
      });
    },
    tabShortCut() {
      if (this.ifautoCash || this.dialogReturn()) {
        return;
      }
      if (this.pc_return_goods) {
        if (this.payListLength > 0) {
          this.SET_SHOW({ showPayChangeStatus2: true });
        } else {
          this.SET_SHOW({ pc_return_goods: false });
        }
      } else {
        if (this.payListLength > 0) {
          this.SET_SHOW({ showPayChangeStatus1: true });
        } else {
          this.SET_SHOW({ pc_return_goods: true });
        }
      }
      demo.actionLog(logList.backGoods);
    },
    isPreventDefault(code) {
      return code === 9;
    },
    // 监听按键事件，用于扫码枪扫码
    keydown(e) {
      let _key = window.event.keyCode;
      let code = e.which;
      if (this.dwArrowDisable) { // 回车后紧跟的↓禁用
        return;
      }
      // if (this.isPreventDefault(code)) { // 是否阻止默认事件
      //   e.preventDefault();
      // }
      if (code === 13 && this.dialogShowing()) { // confirm dialog enter
        this.dialogShortCut(true);
        e.preventDefault();
        return;
      }
      if (code === 27 && this.dialogShowing()) { // confirm dialog esc
        this.dialogShortCut(false);
        e.preventDefault();
        return;
      }
      if (!this.checkKey(code) || code === 20 || code === 16) { // 大写键 shift 锁屏及部分弹窗处理
        this.reductionCodeScan(1);
        return;
      }
      if (!this.continueKeyup(_key, e)) { // 直接收款添加 会员方向键选择
        this.reductionCodeScan(2);
        return;
      }
      if (_key === 13) {
        this.setExceptionKeydown();
        setTimeout(() => {
          this.delayKeydown(e, _key);
        }, 10);
        console.log('KEY:' + _key + ', chat:' + code);
        return;
      }
      let curTime = new Date().getTime();
      console.log('sonarKeyup1:' + _key + ', chat:' + code + ',毫秒差:' + (curTime - this.lastTime));
      console.log('sonarKeyup1:', this.rightKeyword);
      this.lastTime = curTime;
      this.sonarKeyup1(_key, e); // F1-F12等快捷键 ctrl+- ctrl+delete
      this.reductionCodeScan(3);
    },
    setExceptionKeydown() {
      if (this.dwArrowDisable) {
        clearTimeout(this.dwArrowDisable);
        this.dwArrowDisable = null;
      }
      this.dwArrowDisable = setTimeout(() => {
        clearTimeout(this.dwArrowDisable);
        this.dwArrowDisable = null;
      }, 100);
    },
    dialogShowing() { // 部分弹窗展示时 Esc与 Enter
      // this.showPayExitMsg || this.showPayChangeStatus1 || this.showPayChangeStatus2 || this.showPayPrint
      // 备注弹窗
      // this.showInputRemark = true;
      return this.beforeLeaveDialogShow() || this.clearDialogShow() || this.remarkDialogShow() ||
        this.editPriceDialogShow() || this.lastOrderDialogShow() || this.editDiscountDialogShow() ||
        this.showDeleteDatabaseList || this.continue_deleteAllDatabaseList;
    },
    beforeLeaveDialogShow() { // 离开收银台或切换收银退货时dialog提示
      return this.showPayExitMsg || this.showPayChangeStatus1 || this.showPayChangeStatus2 || this.showPayPrint ||
        this.showGoodsSort || this.showDetail;
    },
    beforeLeaveDialogSc(flag) {
      if (flag) {
        this.continueExit();
      } else {
        this.cancelExit();
      }
    },
    clearDialogShow() { // 离开时存在未结算商品弹窗
      return this.show_clear_left_goods_list;
    },
    editDiscountDialogShow() { // 修改优惠折扣dialog
      return this.edit;
    },
    clearDialogSc(flag) { // 清空列表dialog内部快捷键
      this.show_clear_left_goods_list = false;
      if (flag) {
        this.clearLeftGoodsList();
      }
    },
    remarkDialogShow() { // 备注dialog
      return this.showInputRemark;
    },
    remarkDialogSc(flag) { // 备注dialog内部快捷键
      this.showInputRemark = false;
      this.remark = flag ? this.alert_remark.trim() : '';
    },
    editPriceDialogShow() {
      return this.showEditPrice;
    },
    editPriceDialogSc(flag) {
      if (flag) {
        this.continueEditPrice();
      } else {
        this.cancelEditPrice();
      }
    },
    lastOrderDialogShow() {
      return this.showLastOrderDetail;
    },
    lastOrderDialogSc(flag) {
      if (flag) {
        this.printLastOrder();
      }
    },
    editDiscountDialogSc(flag) {
      if (flag) {
        this.saveEdit();
      } else {
        this.edit = flag;
      }
    },
    deleteDatabaseListSc(flag) {
      if (flag) {
        this.deleteDatabaseList(this.delOrderId);
      } else {
        this.showDeleteDatabaseList = false;
      }
    },
    continueDeleteAllDatabaseListSc(flag) {
      if (flag) {
        this.deleteAllDatabaseList();
      } else {
        this.show_database_list = true;
        this.continue_deleteAllDatabaseList = false;
      }
    },
    dialogShortCut(flag) {
      if (this.beforeLeaveDialogShow()) {
        this.beforeLeaveDialogSc(flag);
      } else if (this.clearDialogShow()) {
        this.clearDialogSc(flag);
      } else if (this.remarkDialogShow()) {
        this.remarkDialogSc(flag);
      } else if (this.editPriceDialogShow()) {
        this.editPriceDialogSc(flag);
      } else if (this.lastOrderDialogShow()) {
        this.lastOrderDialogSc(flag);
      } else if (this.editDiscountDialogShow()) {
        this.editDiscountDialogSc(flag);
      } else if (this.showDeleteDatabaseList) {
        this.deleteDatabaseListSc(flag);
      } else if (this.continue_deleteAllDatabaseList) {
        this.continueDeleteAllDatabaseListSc(flag);
      }
    },
    sonarKeyup1(_key, e) {
      if (this.dialogReturn()) { // 锁屏状态禁用快捷键
        return;
      }
      var _this = this;
      // F1 快捷键 新增快捷键
      if (_key === 112) {
        this.SET_SHOW({ showShortCutList: true });
      }
      // F2 取单
      if (_key === 113 && !this.ifautoCash && !_this.pc_return_goods) {
        this.openDataBase()
      }
      // F3 快捷键
      if (_key === 114 && !this.ifautoCash && !_this.pc_return_goods) {
        setTimeout(function() {
          _this.addDatabaseList();
        }, 10);
      }
      // F4 快捷键
      if (_key === 115 && !this.ifautoCash && this.ultimate !== null) {
        if (!pos.network.isConnected()) {
          demo.msg('warning', '本地网络处于离线状态，会员功能暂时无法使用');
          return;
        }
        setTimeout(function() {
          _this.bottomSelect = 1;
        }, 10);
      }
      // F5 快捷键
      if (_key === 116 && !this.ifautoCash && this.$employeeAuth('use_cashier_discount')) {
        setTimeout(function() {
          _this.bottomSelect = 2;
        }, 10);
      }
      this.otherFkey(_key, e);
    },
    otherFkey(_key, e) {
      // F6 快捷键
      if (_key === 117) {
        setTimeout(() => {
          this.bottomSelect = 3;
        }, 10);
      }
      // F8 快捷键
      if (_key === 119 && this.$employeeAuth('cashier_direct')) {
        this.selectZJSK();
      }
      // F9 配件设置
      if (_key === 120 && !this.ifautoCash) {
        this.SET_SHOW({ isPrintSetting: true });
        demo.actionLog(logList.accessoriesSetting);
      }
      // F11 快捷键
      if (_key === 122) {
        this.openMoneybox();
      }
      // F12 快捷键
      if (_key === 123) {
        setTimeout(() => {
          this.SET_SHOW({ pcLockScreenConfirm: true });
          this.$_actionLog(logList.losckScreen, '键盘F12锁屏')
        }, 10);
      }
      this.sonarKeyupUpDownLeftRight(_key, e);
    },
    sonarKeyupUpDownLeftRight(key, e) {
      // keycode 37 = Left ←
      // keycode 38 = Up ↑
      // keycode 39 = Right →
      // keycode 40 = Down ↓
      if (this.bottomSelect === 0) {
        if (e.ctrlKey && key === 37) {
          this.inputNumber('-');
        } else if (e.ctrlKey && key === 39) {
          this.inputNumber('+');
        }
      }
      this.keyupDirectionKeySonar(key, e);
      // this.keyupAlt(key, e);
      this.sonarNumber(key, e);
    },
    keyupDirectionKeySonar(key, e) {
      if (this.showLeftGoodsList.length > 0) {
        if (key === 40) {
          if (this.bottomSelect !== 0) {
            this.choose_left = this.showLeftGoodsList.length - 1;
            this.editLeftList(this.choose_left, this.showLeftGoodsList[this.choose_left]);
          } else {
            this.choose_left += (this.showLeftGoodsList.length !== 0 && this.choose_left !== this.showLeftGoodsList.length - 1) ? 1 : 0;
            this.editLeftList(this.choose_left, this.showLeftGoodsList[this.choose_left]);
          }
        }
      }
    },
    quickPay(type) {
      if (!this.left_goods_list.length) {
        return;
      }
      if (this.ifautoCash) {
        return;
      }
      this.SET_SHOW({ quickPayType: type });
      if (!this.useEditPrice) {
        this.getPriceList(6);
      }
      this.showSettlement(this.pc_return_goods ? 2 : 1);
    },
    shortCutEditProdDisc(that) { // 快捷键修改单品折扣
      if (that.bottomSelect === 0 && that.$employeeAuth('modify_sale_price') && !that.ifautoCash &&
        !(that.left_ismember_day_goods && that.showMember)) {
        that.showEditLeftdisc();
      }
    },
    shortCutLastOrder() {
      if (this.pc_return_goods || this.ifautoCash) {
        this.showLastOrderDetail = false;
      } else if (!this.showLastOrderDetail) {
        this.showLastOrderInfo();
      }
    },
    // 组合快捷键设置s
    getHotkeysString() {
      let str = '';
      this.shortCutList.forEach(item => {
        if (item.keyChar) {
          if (item.keyChar.length === 1 && item.necessary) {
            str += item.keyChar[0].toLocaleLowerCase() + ',';
          }
          if (item.keyChar.length === 2) {
            str += item.keyChar.join('+').toLocaleLowerCase() + ',';
          }
          if (item.keyChar.length === 3) {
            let tempStr = item.keyChar[0] + '+' + item.keyChar[1] + ',' + item.keyChar[0] + '+' + item.keyChar[2];
            str += tempStr.toLocaleLowerCase() + ',';
          }
        }
      });
      this.hotkeysString = str.substring(0, str.length - 1);
      console.log(this.hotkeysString, 'this.hotkeysString+');
    },
    hotkeyFilter(e) {
      if (document.activeElement.tagName === 'INPUT' && (document.activeElement.className !== 'input' || e.keyCode === 32)) {
        return false;
      }
      return true;
    },
    hotKeySet() {
      let that = this;
      hotkeys(this.hotkeysString, function(event, handler) {
        if (that.dialogReturn()) {
          return;
        }
        switch (handler.key) {
          case 'alt+x': // 现金支付快速结算
            event.preventDefault();
            that.quickPay(that.pc_return_goods ? 'returnCash' : 'cash');
            break;
          case 'alt+w': // 微信支付快速结算
            event.preventDefault();
            that.quickPay(that.pc_return_goods ? 'returnWechat' : 'wechat');
            break;
          case 'alt+z': // 支付宝支付快捷结算
            event.preventDefault();
            that.quickPay(that.pc_return_goods ? 'returnAlipay' : 'alipay');
            break;
          case 'alt+p': // POS支付快速结算
            event.preventDefault();
            that.quickPay(that.pc_return_goods ? 'returnPos' : 'pos');
            break;
          case 'shift+space': // 组合支付快速结算
            event.preventDefault();
            that.quickPay(that.pc_return_goods ? '' : 'combinePay');
            break;
          case 'ctrl+tab': // 收银/退货
            that.tabShortCut();
            setTimeout(() => { that.$refs.cjInputRef.focus(); });
            event.preventDefault();
            break;
          case 'ctrl+q': // 收银台清空列表
            event.preventDefault();
            that.show_clear_left_goods_list = true;
            break;
          case 'ctrl+l': // 显示上一单
            event.preventDefault();
            that.shortCutLastOrder();
            break;
          case 'ctrl+b': // 备注
            event.preventDefault();
            that.alert_remark = that.remark;
            that.showInputRemark = true;
            setTimeout(() => {
              that.$refs.remark.focus();
            });
            break;
          case 'ctrl+y': // 修改应收
            event.preventDefault();
            if (Number(that.totalPrice) > 0 && !that.pc_return_goods &&
              !that.ifautoCash && that.$employeeAuth('modify_sale_price')) {
              that.editPrice();
            }
            break;
          case 'space': // space结算
            event.preventDefault();
            that.spaceKeydown();
            that.$_actionLog(logList.settlement, `space快捷键触发结算`);
            break;
          default:
            console.log('');
        }
      });
    },
    hotKeyCancel() {
      hotkeys.unbind(this.hotkeysString);
    },
    // 组合快捷键设置e
    sonarNumber(key, e) {
      if (this.showLeftGoodsList.length > 0) {
        if (key === 38) {
          if (this.bottomSelect !== 0) {
            this.choose_left = this.showLeftGoodsList.length - 1;
            this.editLeftList(this.choose_left, this.showLeftGoodsList[this.choose_left]);
          } else {
            this.choose_left -= (this.showLeftGoodsList.length !== 0 && this.choose_left !== 0) ? 1 : 0;
            this.editLeftList(this.choose_left, this.showLeftGoodsList[this.choose_left]);
          }
        }
      }
      this.sonarNumber2(key, e);
    },
    sonarNumber2(key, e) {
      if (this.bottomSelect === 0 && !this.isNextCode) {
        if (e.ctrlKey && key === 46) {
          this.delLeftGoods();
        }
      }
    },
    inputZJSK(n) {
      this.inputCalculator(n);
      this.focusZJSK();
    },
    selectZJSK() {
      this.bottomSelect = 5;
      setTimeout(() => {
        this.focusZJSK();
      }, 0);
    },
    focusZJSK() {
      document.getElementById('dirMoney').focus();
    },
    sonarKeyup2() {
      if (!this.listen_sm) {
        // 如果是称重时扫码，则进行清空
        this.inputWeight = '';
        this.can_getProdList = true;
      } else if (!this.settlement) {
        this.sonarKeyup3();
      }
    },
    sonarKeyup3() {
      if (!this.useEditPrice) {
        this.getPriceList(19);
        this.watch_pause = false;
      }
      if (this.show_input_number || this.show_database_list) {
        return;
      }
      this.showSettlement(this.pc_return_goods ? 2 : 1);
    },
    closeCurrent() {
      if (this.show_edit_disc) {
        this.show_edit_disc = false;
        return true;
      }
      if (this.showInputRecharge) {
        this.SET_SHOW({ showInputRecharge: false });
        return true;
      }
      if (this.showMemberExchange) {
        this.SET_SHOW({ showMemberExchange: false });
        return true;
      }
      if (this.showTimesCard) {
        this.SET_SHOW({ showTimesCard: false });
        return true;
      }
      if (this.showAddMember !== 'close') {
        this.SET_SHOW({ showAddMember: 'close' });
        return true;
      }
      if (this.bottomSelect === 1) {
        this.bottomSelect = 3;
        return true;
      }
      if (this.isPrintSetting) {
        this.SET_SHOW({ isPrintSetting: false });
        return true;
      }
      if (this.pcLockScreenConfirm) {
        this.SET_SHOW({ pcLockScreenConfirm: false });
        return true;
      }
      if (this.show_input_number) {
        this.show_input_number = false;
        return true;
      }
      if (this.showTypeManage) {
        this.SET_SHOW({showTypeManage: false});
        return true;
      }
      if (this.showSelManage) {
        this.SET_SHOW({showSelManage: false});
        return true;
      }
      if (this.showRepeatChoose) {
        this.SET_SHOW({showRepeatChoose: false});
        return true;
      }
      return false;
    },
    payBack () {
      if (this.closeCurrent()) {
        return;
      }
      if (this.payListLength > 0) {
        this.SET_SHOW({ showPayExitMsg: true });
      } else {
        this.SET_SHOW({ isHome: true });
        this.SET_SHOW({ isPay: false });
      }
    },
    checkKey(code) {
      let flag = true;
      if (this.pcLockScreen && !this.input_password) { // 呼出解锁
        this.setInputPassword();
        flag = false;
      }
      if (!this.pcLockScreen && !this.ifautoCash && code === 27) { // 未锁屏未自助收银时Esc退到首页
        this.payBack();
      }
      var pd3 = (this.ifautoCash && this.autoCashShow) || this.show_edit_disc ||
        this.show_edit_pwd || this.showInputRecharge || this.bottomSelect === 6;
      if (pd3) {
        flag = false;
      }
      return flag;
    },
    reductionCodeScan(n) {
      this.codeScan = false;
      this.codeScanFrom = n + ': delayKeydown reduction';
    },
    delayKeydown(e, _key) {
      if (this.showInputWeight && !this.codeScan) {
        this.inputLeftList();
        this.reductionCodeScan(4);
        return;
      } else if (this.showChooseWeight) {
        // e.preventDefault();
        this.openWeight(2);
        this.reductionCodeScan(5);
        return;
      } else if (e.altKey || this.pcLockScreen) {
        this.reductionCodeScan(7);
        return;
      } else if (!this.showInputRemark && !this.pcLockScreen && !this.settlement && !this.codeScan) {
        // 原扫码枪扫完条形码最后会传一个回车作为结尾，现拿到space监听中处理
        this.reductionCodeScan(6);
        return;
      }
      this.sonarKeyup1(_key, e);
      this.reductionCodeScan(8);
    },
    spaceKeydown() {
      if (!(this.pcLockScreen || this.codeScan || this.dialogReturn() || this.bottomSelect === 6 || this.showTypeManage)) {
        this.sonarKeyup2();
      }
      this.reductionCodeScan(9);
    },
    continueKeyup(_key, e) {
      // 加号添加直接收款
      if ((_key === 107 || _key === 187) && this.rightKeyword !== '' &&
        Number(this.rightKeyword) != 0 && this.bottomSelect === 5) {
        this.subToLeftList();
        this.$refs.dirMoney.blur(); // 直接收款添加后输入框失焦以保证结算键可用
        this.continueAddZJSK = true;
        return false;
      }
      if (this.bottomSelect === 5 && this.continueAddZJSK && !e.ctrlKey && !this.dialogShowing() && _key !== 32) { // 连续添加直接收款
        this.focusZJSK();
        this.continueAddZJSK = false;
      }
      // 选择会员弹窗中按键控制
      if (this.bottomSelect === 1) {
        this.keyboardChooseVip(_key);
        return false;
      }
      // F10 销售单
      if (_key === 121 && !this.dialogReturn() && !this.ifautoCash && this.$employeeAuth('show_shift_records')) {
        this.goDetail();
        e.preventDefault();
        return false;
      }
      return true;
    },
    stockEnoughCheck(ef) {
      console.log('stockEnoughCheck执行一次+');
      if (!this.pc_return_goods && !this.setSystem) {
        if (this.autoPacking && ef.packing && ef.packing !== '{}') { // 拆包商品
          this.packingGoodsCheck(ef);
        } else { // 非拆包商品
          this.$set(ef, 'showStock', Number(ef.number) > Number(ef.curStock) && ef.curStock !== undefined && ef.curStock !== null);
        }
      } else {
        this.$set(ef, 'showStock', false);
      }
      return true;
    },
    packingGoodsCheck(ef) {
      let json = demo.t2json(ef.packing);
      console.log(this.left_goods_list);
      if (json.children) { // 该商品为大包商品
        ef.showStock = Number(ef.number) > Number(ef.curStock);
      } else { // 该商品为小包商品
        if (JSON.stringify(json) !== '{}') {
          // 父商品fingerprint
          let pFingerprint = json.parent.fingerprint;
          // 大小包商品兑换规则
          let exchangeCount = json.parent.count;
          // 在购物车列表中查找是否包含大包商品
          let pGoodsList = this.left_goods_list.filter(item => {
            return item.fingerprint === pFingerprint;
          });
          if (pGoodsList.length > 0) { // 包含大包商品
            // 可用于拆分的大包数量
            let qty = pGoodsList[0].curStock - pGoodsList[0].number;
            // 可拆分出的小包数量
            let splitQty = qty * exchangeCount;
            // 可用库存数
            let totalCount = splitQty + ef.curStock;
            this.$set(ef, 'showStock', totalCount < Number(ef.number));
          } else { // 不包含大包商品，需要查出来
            if (!this.packingGoodsMap[pFingerprint]) { // 未查询状态
              this.packingGoodsSearch(pFingerprint).then(list => {
                this.packingGoodsMap[pFingerprint] = _.cloneDeep(list[0]);
                let pCount = list[0].curStock;
                this.$set(ef, 'showStock', (pCount * exchangeCount + ef.curStock) < Number(ef.number));
              });
            } else { // 已查询出该商品信息
              let pCount = this.packingGoodsMap[pFingerprint].curStock;
              this.$set(ef, 'showStock', (pCount * exchangeCount + ef.curStock) < Number(ef.number));
            }
          }
        }
      }
    },
    packingGoodsSearch(pFingerprint) {
      return new Promise((resolve, reject) => {
        let data = {
          pset: '',
          isGroup: false,
          type: this.alltype,
          condition: '',
          limit: 1,
          selectDel: false,
          getDel: false,
          vipGoods: true,
          fingerprintFlg: false,
          fingerprint: [pFingerprint],
          offset: 0
        }
        goodService.search(data, json => {
          resolve(json);
        }, err => {
          reject(err);
        })
      });
    },
    // ==========================
    keyboardChooseVip(_key) {
      if (_key === 38 && this.memTableData.length) { // ↑
        if (this.vipActiveIndex === null) {
          this.vipActiveIndex = 0;
        } else if (this.vipActiveIndex !== 0) {
          this.vipActiveIndex--;
        }
      } else if (_key === 40 && this.memTableData.length) { // ↓
        if (this.vipActiveIndex === null) {
          this.vipActiveIndex = 0;
        } else if (this.vipActiveIndex < this.memTableData.length - 1) {
          this.vipActiveIndex++;
        }
      } else if (_key === 13) { // use enter to choose
        if (this.vipActiveIndex !== null) {
          this.addLeftMember(this.memTableData[this.vipActiveIndex]);
        } else {
          this.pagenum = 1;
          this.getMember(true);
        }
      }
    },
    goDetail(boolean) {
      if (this.payListLength > 0) {
        this.SET_SHOW({ showDetail: true });
      } else {
        this.SET_SHOW({ isPay: false, isDetail: true, pc_detail_tab: 1, fromDetail: 0, showBackCompleteDialog: !!boolean });
      }
    },
    openMoneybox() {
      let _this = this;
      if (_this.setting_hasmoneybox + '' === 'false') {
        demo.msg('warning', '没有钱箱设备，请确认');
        return;
      }
      try {
        if (
          _this.setting_moneybox == null ||
          _this.setting_moneybox.trim() === ''
        ) {
          if (
            _this.setting_small_printer == null ||
            _this.setting_small_printer.trim() === ''
          ) {
            demo.msg('warning', _this.$msg.select_printer);
            return;
          }
          external.openCashBox(
            _this.setting_small_printer,
            function () {
              //
            },
            function () {
              demo.msg('warning', _this.$msg.cash_box_in_error);
            }
          );
        } else {
          external.openCashBox_separate(
            _this.setting_moneybox,
            2400,
            8,
            function () {
              //
            },
            function () {
              demo.msg('warning', _this.$msg.cash_box_in_error);
            }
          );
        }
      } catch (e) {
        demo.msg('warning', '钱箱异常，请检查钱箱');
      }
    },
    onScanInput(str, cz) {
      // 搜索前先判断是否为传秤商品
      if (this.hasBarCodeScales && /^20\d{11}$/.test(str)) { // 符合20开头共13位的条码规范
        zgDHTMFBalance.scanBarCode(str,
          res => {
            let json = demo.t2json(res);
            if (json.CheckSuccess) { // 按传秤商品处理
              let totalPrice = json.TotalPrice;
              let code = json.GoodsCode;
              let data = {
                pset: '',
                isGroup: false,
                type: this.alltype,
                condition: code,
                limit: this.limit,
                selectDel: false,
                getDel: false,
                offset: 0
              }
              goodService.search(data, json => {
                let filterJson = json.filter(item => { return item.code === code; });
                if (filterJson.length !== 0) {
                  // 筛选传秤商品
                  this.right_goods_list = filterJson;
                  this.right_goods_list[0].amt = Number(totalPrice);
                  if (!this.showMember) {
                    // 不选择会员不重新计算
                    this.right_goods_list[0].isScales = true;
                  }
                  let number = Number(totalPrice / this.right_goods_list[0].salePrice).toFixed(3);
                  this.right_goods_list[0].number = number;
                  console.log(this.right_goods_list, '传秤商品right_goods_list');
                  this.bottomSelect = 0;
                  this.addLeftGoodsList(0, 'scales');
                } else {
                  this.watch_pause = false;
                }
                // if (cz === 'edit_goods') {
                //   var n = _this.left_goods_list.length - 1;
                //   _this.editLeftList(n, _this.left_goods_list[n]);
                //   _this.choose_left = n;
                // }
              });
            } else {
              this.watch_pause = false;
              this.onScanInputSonar(str, cz);
            }
          },
          err => {
            this.watch_pause = false;
            demo.msg('warning', err);
          });
      } else {
        this.watch_pause = false;
        this.onScanInputSonar(str, cz);
      }
    },
    onScanInputSonar(str, cz) {
      // 扫码枪扫完商品后搜索商品
      var _this = this;
      var data = {
        pset: '',
        isGroup: false,
        type: this.alltype,
        condition: str,
        limit: this.limit,
        selectDel: false,
        getDel: false,
        offset: 0
      };
      console.log(data, '请求搜索商品接口参数');
      goodService.search(data, json => {
        console.log(_.cloneDeep(json), '获得的接口返回json');
        if (json.length > 1) { // 存在重复条码时
          console.log(json, 'ffffffff');
          this.repeatCode = str;
          this.repeatData = _.cloneDeep(json);
          this.SET_SHOW({ showRepeatChoose: true });
        } else if (json.length === 1) {
          _this.right_goods_list = json;
          if (this.weighUnitList.indexOf(json[0].unitName) === -1) { // 非称重商品展示商品详情
            _this.bottomSelect = 0;
          } else {
            _this.bottomSelect = 3;
          }
          _this.addLeftGoodsList(0, 'ban');
        } else {
          _this.addGoods(str);
        }
        if (cz === 'edit_goods') {
          var n = _this.left_goods_list.length - 1;
          _this.editLeftList(n, _this.left_goods_list[n]);
          _this.choose_left = n;
        }
      });
    },
    repeatChoose(repeatData, repeatIndex) {
      this.SET_SHOW({ showRepeatChoose: false });
      this.right_goods_list = _.cloneDeep(repeatData);
      if (this.weighUnitList.indexOf(this.right_goods_list[repeatIndex].unitName) === -1) { // 非称重商品展示商品详情
        this.bottomSelect = 0;
      } else {
        this.bottomSelect = 3;
      }
      this.addLeftGoodsList(repeatIndex, 'ban');
    },
    /**
     * 获取收银台默认支付方式，先从storeinfo表的settings字段里取，没有的话再取setting表的info字段里取
     * key: defaultAcctsId
     */
    getDefaultAcctsId(dataInfo) {
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        this.setDefaultAcctsIdBy(dataInfo);
      } else {
        this.setDefaultAcctsIdBy(demo.t2json(this.storeList[0].settings).default_accts_id === undefined
          ? dataInfo : demo.t2json(this.storeList[0].settings));
      }
    },
    async setDefaultAcctsIdBy(dataInfo) {
      if (dataInfo.default_accts_id) {
        this.SET_SHOW({ defaultAcctsId: demo.t2json(dataInfo.default_accts_id) });
      }
      // 判断是否显示热销分类
      const show_hot_good = demo.t2json(dataInfo.show_hot_good);
      this.SET_SHOW({ showHotGood: show_hot_good === undefined ? true : show_hot_good });
      // 根据是否打开热销分类开关设置默认分类
      this.type = this.defaultType = await typeService.getDefaultType();
      if (this.type === '-99') {
        this.getHotList();
      } else {
        this.getProdList();
      }
    },
    openScale() {
      if (this.weighValue === '' || this.weighTypeValue === '') {
        return;
      }
      this.closeScale();
      try {
        const data = {
          exec: 'open',
          port: this.weighValue,
          baudrate: '9600',
          databits: '8',
          parity: '0',
          stopBits: '1',
          script: 'demo.weightConvert("{0}");',
          scaleName: this.weighTypeValue
        };
        external.execScale(data);
        CefSharp.PostMessage(`开启电子秤串口`);
      } catch (e) {
        demo.msg('warning', this.$msg.electronic_scale_in_error);
      }
    },
    addGoods(str) {
      if (this.$employeeAuth('create_products')) {
        try {
          external.playVoice('暂无此商品，请先完善商品信息。', this.soundVolume * 4);
        } catch (e) {
          external.playVoice('暂无此商品，请先完善商品信息。');
        }
      } else {
        try {
          external.playVoice('暂无此商品，请联系管理员登录商品信息。', this.soundVolume * 4);
        } catch (e) {
          external.playVoice('暂无此商品，请联系管理员登录商品信息。');
        }
        demo.msg('warning', '请联系管理员登录商品信息');
        return;
      }
      this.newGoods.code = str;
      this.bottomSelect = 6;
      this.visible = true;
      this.SET_SHOW({ addGoodsCategoryId: '2' });
      this.SET_SHOW({ addGoodsCategory: '其他分类' });
      this.SET_SHOW({ addGoodsCategoryfingerprint: md5('其他分类') });
      this.SET_SHOW({ addGoodsUnitId: '' });
      this.SET_SHOW({ addGoodsUnit: '' });
      this.SET_SHOW({ addGoodsUnitfingerprint: '' });
      this.getNewGoods();
    },
    initNewGoods() {
      this.newGoods = {
        id: '',
        majorCode: '',
        code: '',
        name: '',
        pinyin: '',
        typeId: '2',
        typeName: '其他分类',
        typeFingerprint: md5('其他分类'),
        unitName: '',
        unitId: '',
        unitFingerprint: '',
        salePrice: '0.00',
        vipPrice: '0.00',
        purPrice: '0.00',
        curStock: '0',
        addStockNum: '0',
        minStock: '0',
        maxStock: '0',
        remark: '',
        image: '',
        has_image: 0,
        supplier_id: 0,
        initStock: 0,
        initPrice: 0,
        initAmt: 0,
        img_fingerprint: '',
        first_letters: '',
        specs: {unlock: {}}
      };
    },
    getNewGoods() {
      var params = {
        'code': this.newGoods.code,
        'sysUid': this.sys_uid,
        'sysSid': this.sys_sid
      };
      this.initNewGoods();
      this.newGoods.code = params.code;
      var _this = this;
      if (pos.network.isConnected()) {
        demo.$http.post(_this.$rest.getProducts, params, {headers: {'Content-Type': 'application/json', 'Authorization': _this.token}})

          .then(function (res) {
            console.log(res, '新增商品搜索的商品信息');
            var json = res.data.data;
            _this.newGoods.name = json.name;
            _this.newGoods.salePrice = Number(json.salePrc).toFixed(2);
            _this.newGoods.purPrice = Number(json.purPrc).toFixed(2);
            if (_this.getNewGoodsSonar()) {
              _this.new_encry = json.purPrc;
            }
            _this.newGoods.image = json.picsList.length == 0 ? '' : json.picsList[0].filePath;
            if (json.name) {
              _this.checkNameExist();
            }
          })
          .catch(function (error) {
            if (_this.$employeeAuth('create_products') && _this.$employeeAuth('import_products') &&
              _this.$employeeAuth('delete_products') && _this.$employeeAuth('products_curstock_inventories')) {
              _this.new_encry = '0.00';
            }
          });
      } else {
        demo.msg('warning', '本地网络处于离线状态，线上搜商品暂时无法使用');
      }
    },
    getNewGoodsSonar() {
      return this.$employeeAuth('create_products') && this.$employeeAuth('import_products') &&
        this.$employeeAuth('delete_products') && this.$employeeAuth('products_curstock_inventories');
    },
    sonarAdd2(mid, show) {
      if (show) {
        // ==单价
        if (this.kexianPriceType === 'amt') {
          mid.kexianShowAmt = true;
        } else {
          this.getKexian((Number(mid.salePrice) * (Number(mid.disc) / 100)).toFixed(2), 1);
        }
      } else {
        this.sonarKexian(mid);
      }
    },
    sonarAdd(mid, type) {
      let show = !this.showMember || Number(this.memberPayType) === 1 || (mid.ismember_day_goods === true && this.showMember);
      mid.disc = this.getFinalDisc();
      let deepMid = _.cloneDeep(mid);
      mid.mprice = deepMid.salePrice;
      this.sonarAdd2(mid, show);
      for (var k = 0; k < this.left_goods_list.length; k++) {
        if (this.left_goods_list[k].name === mid.name && this.left_goods_list[k].code === mid.code) {
          this.left_goods_list[k].kexianShowAmt = mid.kexianShowAmt;
          this.left_goods_list[k].number++;
          this.pushVoice(type);
          var m = this.left_goods_list;
          this.left_goods_list = [];
          this.left_goods_list = m;
          if (this.bottomSelect === 0) {
            this.editLeftList(k, this.left_goods_list[k]);
          }
          return;
        }
      }
      mid.number = '1';
      mid.disc = this.getFinalDisc();
      console.log(mid, 'mid');
      this.left_goods_list.push(mid);
      this.pushVoice(type);
      if (type === 'ban') {
        this.choose_left = this.left_goods_list.length - 1;
      } else {
        this.choose_left = null;
      }
      if (this.bottomSelect == 0) {
        this.editLeftList(k, this.left_goods_list[this.left_goods_list.length - 1]);
      }
      this.getPriceList(55);
    },
    getFinalDisc() {
      return this.isVipDay && this.showMember && this.isMemberDayGoods ? this.member_day_discount : '100';
    },
    scalesAdd(mid) {
      let show = !this.showMember || Number(this.memberPayType) === 1 || (mid.ismember_day_goods === true && this.showMember);
      mid.disc = this.isVipDay && this.showMember && this.isMemberDayGoods ? this.member_day_discount : '100';
      let deepMid = _.cloneDeep(mid);
      mid.mprice = deepMid.salePrice;
      this.sonarAdd2(mid, show);
      // mid.number = '1';
      mid.disc = this.isVipDay && this.showMember && this.isMemberDayGoods ? this.member_day_discount : '100';
      console.log(mid, 'scalesAdd mid');
      this.left_goods_list.push(mid);
      this.pushVoice();
      this.choose_left = this.left_goods_list.length - 1;
      setTimeout(() => {
        this.getPriceList(55);
      }, 0);
      if (this.bottomSelect == 0) {
        this.editLeftList(this.left_goods_list.length - 1, this.left_goods_list[this.left_goods_list.length - 1]);
      }
    },
    pushVoice(type) {
      if (this.scanCodeSound && type !== 'click') {
        try {
          external.playVoice('1件', this.soundVolume * 4);
        } catch (e) {
          external.playVoice('1件');
        }
      }
    },
    sonarKexian(mid) {
      // ==单价
      if (this.kexianPriceType === 'amt') {
        mid.kexianShowAmt = true;
      } else {
        if (Number(mid.vipPrice) === 0) {
          this.getKexian((Number(mid.salePrice) * (Number(mid.disc) / 100)).toFixed(2), 1);
        } else {
          this.getKexian(Number(mid.vipPrice).toFixed(2).toString(), 1);
        }
      }
    },
    addLeftGoodsList(n, type) {
      if (this.settlement || this.showing) {
        return;
      }
      this.watch_pause = true;
      this.isMemberDayGoods = false;
      var mid = _.cloneDeep(this.right_goods_list[n]);
      mid.bigImg = this.right_goods_list[n].image;
      mid.isMemberDayGoods = false;
      mid.kexianShowAmt = this.kexianPriceType === 'amt';
      this.right_goods_list[n].isMemberDayGoods = false;
      if (this.isVipDay) {
        this.member_day_goods.forEach(item => {
          if (item.name === mid.name && item.fingerprint === mid.fingerprint) {
            this.isMemberDayGoods = true;
            mid.isMemberDayGoods = true;
            this.right_goods_list[n].isMemberDayGoods = true;
          }
        });
      }
      if (type === 'scales') {
        this.scalesAdd(mid);
      } else if (this.weighUnitList.indexOf(mid.unitName) === -1) {
        mid.amt = 0;
        this.sonarAdd(mid, type);
      } else {
        mid.amt = 0;
        this.alert_unit = mid.unitName;
        this.weight_title = this.right_goods_list[n].name;
        this.inputPrice = mid.salePrice; // 称重页面，默认显示商品价格
        this.set_mid_right = this.right_goods_list[n];
        if (this.isOnceOpen && !this.hasWeigh) {
          this.SET_SHOW({ showChooseWeight: true });
          return;
        }
        this.showInputWeight = true;
        this.weighCheck();
        if (type === 'ban') {
          this.list_pagenum = 1;
          this.can_getProdList = true;
          this.getProdList();
        }
      }
    },
    weighCheck() {
      if (this.hasWeigh) {
        if (this.weighShowInPay) {
          setTimeout(() => { this.setWeighInputVal(); });
        } else {
          this.openScale();
        }
      }
    },
    setWeighInputVal() {
      var ele = document.getElementById('iw');
      if (this.weighSet === '' || isNaN(this.weighSet) || this.weighSet <= 0) {
        ele.value = '';
        return;
      }
      ele.value = this.weighSet;
      ele.dispatchEvent(new Event('input'));
    },
    closeInputWeight() { // 关闭称重商品弹窗时如果收银台不显示重量
      this.showInputWeight = false;
      if (!this.weighShowInPay) {
        this.closeScale();
      }
    },
    checkNameExist() {
      this.nameExist = false;
      let data = _.clone(this.newGoods);
      data.fingerprint = '';
      data.code = '';
      goodService.existGoods(data, res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          if (json[0].errMsg === '商品名重复') {
            this.nameExist = true;
            return false;
          }
        }
      });
    },
    // 获取产品列表
    getProdList(n) {
      if (this.type === '-99' && this.rightKeyword) {
        this.type = '';
      }
      // 对应编辑左侧列表时，直接删除商品，右侧会出现列表为空的情况
      if (this.can_getProdList === false || this.type === '-99') {
        return;
      }
      if (this.bottomSelect !== 3 && this.bottomSelect !== 4) {
        return;
      }
      this.can_getProdList = false;
      var type = this.type;
      var data = {
        pset: '',
        isGroup: false,
        type: type,
        condition: this.rightKeyword,
        limit: this.limit,
        selectDel: false,
        getDel: false,
        offset: Number((this.list_pagenum - 1) * this.limit),
        orderBy: [
          {
            column: 'ga.sort',
            order: 'asc'
          },
          {
            column: 'a.create_at',
            order: 'desc'
          }
        ]
      };
      console.log(data, 'goodSearch data++');
      goodService.search(data, res => {
        console.log(`getProdList res:${res.length},list_pagenum:${this.list_pagenum}`);
        if (this.list_pagenum === 1) {
          this.right_goods_list = res;
        } else {
          this.right_goods_list = this.right_goods_list.concat(res);
        }
        this.list_pagenum++;
        this.can_getProdList = true;
        this.stop_scrollFn = false;
        setTimeout(() => {
          this.scrollExistCheck(res);
        });
      });
    },
    // 获取热销商品列表
    getHotList() {
      this.rightKeyword = '';
      goodService.hot((res) => {
        const list = demo.t2json(res);
        this.right_goods_list = list;
      });
    },
    scrollExistCheck(res) { // 如果滚动条不存在，再次尝试加载数据使其显示滚动条
      let el = document.getElementById('list_id');
      if (el && el.scrollHeight <= el.clientHeight && res.length === this.limit) {
        this.getProdList(171);
      }
    },
    setInputPassword() {
      this.input_password = true;
      setTimeout(function() {
        $('#password').focus();
      }, 10);
    },
    openLockScreen() {
      if (this.open_lock === this.password || md5(this.open_lock) === this.password) {
        this.input_password = false;
        this.open_lock = '';
        var _this = this;
        setTimeout(function() {
          _this.SET_SHOW({ pcLockScreen: false });
          _this.input_password = false;
        }, 1);
      } else {
        demo.msg('error', this.$msg.password_error);
      }
    },
    drawerDetailClose() {
      this.showLastOrderDetail = false;
    },
    forgetPassword() {
      this.SET_SHOW({ pcLockScreen: false });
    },
    clearDiscountReduce() {
      var _this = this;
      setTimeout(function() {
        _this.discountIndex = null;
        _this.reduceIndex = null;
      }, 10);
      setTimeout(() => {
        _this.getKexian(Number(_this.showFinalPrice).toFixed(2), 2);
      }, 500);
    },
    memberRemove() {
      this.showMember = false;
      this.member_id = '';
      this.left_member_name = '';
      this.member_mobile = '';
      if (this.isVipDay) {
        for (var i = 0; i < this.left_goods_list.length; i++) {
          for (var j = 0; j < this.member_day_goods.length; j++) {
            if (this.left_goods_list[i].name === this.member_day_goods[j].name &&
                JSON.stringify(this.left_goods_list[i].specs) === JSON.stringify(this.member_day_goods[j].specs)) {
              this.left_goods_list[i].disc = 100;
            }
          }
        }
      }
      this.getPriceList(5);
    },
    getPriceList(n) {
      console.log(`getPriceList origin number:${n}`, this.settlement);
      this.useEditPrice = false;
      this.watch_pause = true;
      if (this.left_goods_list.length > 0) {
        this.total_number = 0;
        this.totalPrice = 0;
        this.reducePrice = 0;
        this.finalPrice = 0;
        // 会员日商品价格，不参与优惠，单独计算
        this.member_price = 0;
        // 1.循环求出总数量和合计金额
        this.getTotalNumAndTotalMoney();
        // 2.满足会员日商品条件时，this.member_price不为0，不满足即为0，不会对下面计算有影响
        this.getMemberDayGoodsPrice();
        // 3.如果选中了整单折扣或整单减价,开始计算优惠金额，会员日商品不参与折扣
        this.completeOrderDiscount();
        // 4.如果既不是整单折扣也不是整单减价
        this.incompleteOrderDiscount();
        // 5.根据不同抹零方式,算出要展示的应收金额的值
        this.calcShouldMoneyByClearZero();
      } else {
        this.total_number = 0;
        this.totalPrice = '0.00';
        this.reducePrice = 0;
        this.finalPrice = 0;
        this.showFinalPrice = '0.00';
      }
      this.showLeftGoodsList = _.cloneDeep(this.left_goods_list);
      this.pushScreen2();
      setTimeout(() => {
        this.watch_pause = false;
      }, 0);
    },
    // getPriceList圈复杂度降低 start
    getTotalNumAndTotalMoney() { // 1.循环求出总数量和合计金额
      for (var i = 0; i < this.left_goods_list.length; i++) {
        if (this.weighUnitList.indexOf(this.left_goods_list[i].unitName) !== -1) {
          this.total_number += 1;
        } else {
          this.total_number += Number(this.left_goods_list[i].number);
        }
        // 是否传称扫码商品
        if (this.left_goods_list[i].isScales) {
          let amt = this.left_goods_list[i].amt;
          amt = this.setMaxDecimal(amt, 5);
          this.left_goods_list[i].amt = Number(amt.toFixed(2));
          this.left_goods_list[i].amtReally = amt;
          this.totalPrice += Number(this.left_goods_list[i].amt);
        } else {
          // 会员且会员价打折
          if (this.showMember && this.memberPayType == 2) {
            if (this.left_goods_list[i].isMemberDayGoods) {
              let amt = Number(this.left_goods_list[i].mprice) * Number(this.left_goods_list[i].number) * Number(this.left_goods_list[i].disc) / 100;
              this.left_goods_list[i].salePrice = Number(this.left_goods_list[i].mprice);
              amt = this.setMaxDecimal(amt, 5);
              this.left_goods_list[i].amt = Number(amt.toFixed(2));
              this.left_goods_list[i].amtReally = amt;
              this.totalPrice += Number(this.left_goods_list[i].amt);
            } else {
              let amt = (this.left_goods_list[i].vipPrice == 0 || this.left_goods_list[i].isMemberDayGoods ? Number(this.left_goods_list[i].salePrice)
                : Number(this.left_goods_list[i].vipPrice)) * Number(this.left_goods_list[i].number) * Number(this.left_goods_list[i].disc) / 100;
              amt = this.setMaxDecimal(amt, 5);
              this.left_goods_list[i].amt = Number(amt.toFixed(2));
              this.left_goods_list[i].amtReally = amt;
              this.totalPrice += Number(this.left_goods_list[i].amt);
            }
          } else if (this.showMember && this.left_goods_list[i].isMemberDayGoods) {
            let amt = Number(this.left_goods_list[i].mprice) * Number(this.left_goods_list[i].number) * Number(this.left_goods_list[i].disc) / 100;
            this.left_goods_list[i].salePrice = Number(this.left_goods_list[i].mprice);
            amt = this.setMaxDecimal(amt, 5);
            this.left_goods_list[i].amt = Number(amt.toFixed(2));
            this.left_goods_list[i].amtReally = amt;
            this.totalPrice += Number(this.left_goods_list[i].amt);
          } else {
            // 其他情况
            let amt = Number(this.left_goods_list[i].salePrice) * Number(this.left_goods_list[i].number) * Number(this.left_goods_list[i].disc) / 100;
            amt = this.setMaxDecimal(amt, 5);
            this.$set(this.left_goods_list[i], 'amt', Number(amt.toFixed(2)));
            this.$set(this.left_goods_list[i], 'amtReally', amt);
            this.totalPrice += Number(this.left_goods_list[i].amt);
          }
        }
        if (this.left_goods_list[i].kexianShowAmt) { // 客显显示小计
          this.left_goods_list[i].kexianShowAmt = false;
          this.getKexian(this.left_goods_list[i].amtReally.toFixed(2), 0);
        }
      }
      console.log(this.left_goods_list, 'this.left_left_goods_list');
      this.total_number = this.total_number.toFixed(0);
      this.totalPrice = Number(this.totalPrice.toFixed(2));
      this.showFinalPrice = this.totalPrice;
    },
    getMemberDayGoodsPrice() { // 2.满足会员日商品条件时，this.member_price不为0，不满足即为0，不会对下面计算有影响
      if (this.isVipDay && this.showMember) {
        for (var i = 0; i < this.left_goods_list.length; i++) {
          let chooseGoodsSpecs = JSON.stringify(this.left_goods_list[i].specs);
          for (var j = 0; j < this.member_day_goods.length; j++) {
            if (this.left_goods_list[i].name === this.member_day_goods[j].name &&
                chooseGoodsSpecs === JSON.stringify(this.member_day_goods[j].specs)) {
              this.member_price += Number(this.left_goods_list[i].amt);
            }
          }
        }
      }
    },
    completeOrderDiscount() { // 3.如果选中了整单折扣或整单减价,开始计算优惠金额，会员日商品不参与折扣
      if (this.discountIndex != null) {
        this.finalPrice = Number(((this.totalPrice - this.member_price) * Number(this.discountNumber) * 0.1 + this.member_price).toFixed(2));
        this.reducePrice = Number((this.totalPrice - this.finalPrice).toFixed(2));
      }
      if (this.reduceIndex != null) {
        this.reducePrice = this.reduce_number;
        // (1) 如果减价已经大于总计 - 会员日商品总价，则剩余价格为会员日商品总价
        // (2) 如果减价不大于总计 - 会员日商品总价，则剩余价格为商品总价 - 减价
        if (Number(this.reducePrice) > Number(this.totalPrice)) {
          this.finalPrice = 0;
        } else {
          this.finalPrice = this.totalPrice - Number(this.reducePrice);
        }
      }
    },
    incompleteOrderDiscount() { // 4.如果既不是整单折扣也不是整单减价
      if (this.discountIndex === null && this.reduceIndex === null) {
        this.reducePrice = 0;
        this.finalPrice = this.totalPrice;
        this.showFinalPrice = this.totalPrice;
      }
    },
    calcShouldMoneyByClearZero() { // 5.根据不同抹零方式,算出要展示的应收金额的值
      if (this.clearZero === 0) {
        this.showFinalPrice = this.finalPrice;
      } else if (this.clearZero === 1) {
        this.showFinalPrice = parseInt(Number(this.finalPrice) * 10) / 10;
      } else if (this.clearZero === 2) {
        this.showFinalPrice = parseInt(Number(this.finalPrice));
      } else if (this.clearZero === 3) {
        this.showFinalPrice = Number((Math.round(Number((Number(this.finalPrice) * 10)).toFixed(1)) / 10).toFixed(2));
      } else if (this.clearZero === 4) {
        this.showFinalPrice = Number((Math.ceil(Number((Number(this.finalPrice) * 10)).toFixed(1)) / 10).toFixed(2));
      } else {
        this.clearZero = 0;
      }
      this.reducePrice = (Number(this.totalPrice) - Number(this.showFinalPrice)).toFixed(2);
      this.showFinalPrice = Number(this.showFinalPrice).toFixed(2);
      this.finalPrice = this.finalPrice.toFixed(2);
      this.totalPrice = this.totalPrice.toFixed(2);
    },
    // getPriceList圈复杂度降低 end
    pushScreen2() {
      var s_data = {
        'screen2ShowList': this.left_goods_list,
        'screen2ReducePrice': this.reducePrice,
        'screen2TotalPrice': this.totalPrice,
        'screen2ShowFinalPrice': this.showFinalPrice,
        'screen2ShowMember': this.showMember,
        'screen2MemberMoney': this.member_money,
        'screen2ReturnGoods': this.pc_return_goods,
        'screen2isSinglePayWay': true,
        'screen2MemberPayType': this.memberPayType,
        'screen2MemberName': this.memberDetail.name,
        'screen2MemberPoint': this.memberDetail.integral,
        'screen2MemberPhone': this.memberDetail.mobile
      };
      demo.screen2(s_data, 24);
    },
    sonarCalculator(n) {
      if (Number(n) > 999999.99) {
        this.rightKeyword = '999999.99';
        this.leftSalesprice = '999999.99';
      } else {
        this.rightKeyword = n;
      }
    },
    inputCalculator(str) {
      const _rightKeyword = _.cloneDeep(this.rightKeyword);
      // 输入框ref
      let keyboardRef;
      // 焦点偏移
      let selectOffset;
      // 起始位置
      let selectionStart;
      let selectionEnd;
      // 售价输入框
      if (this.inputLeftSalesprice) {
        keyboardRef = this.$refs.leftSalespricedRef;
      } else if (this.inputLeftDisc) {
        // 折扣输入框
        keyboardRef = this.$refs.leftDiscdRef;
      }
      if (keyboardRef) {
        // 判断起始位置，是否全选
        selectionStart = keyboardRef.selectionStart;
        selectionEnd = keyboardRef.selectionEnd;
      }
      if (this.rightKeyword.length > 29 && str !== 'back' && str !== 'del') {
        return;
      }
      if (this.record_input_style === 1 && str !== 'back') {
        this.rightKeyword = '';
      }
      this.record_input_style = 0;
      const flag_str1 = (str === 'back');
      const flag_str2 = (str === 'del');
      const flag_str3 = (str === '.');
      const flag_str4 = (this.inputLeftSalesprice || this.bottomSelect === 5 || this.settlement);
      const flag_str5 = (str === '.' && this.rightKeyword === '');
      if (flag_str1) {
        if (this.inputLeftNumber && this.leftNumberInit) {
          this.rightKeyword = this.leftNumberInit;
          this.leftNumberInit = '';
        } else {
          this.rightKeyword = this.rightKeyword.toString();
        }
        if (this.rightKeyword.toString().length > 0) {
          // 售价和折扣输入框修改
          if (keyboardRef) {
            // 有选中部分
            if (selectionStart === selectionEnd) {
              this.rightKeyword = this.rightKeyword.substring(0, selectionStart - 1) + '' + this.rightKeyword.substring(selectionEnd, this.rightKeyword.toString().length);
              selectOffset = -1;
            } else {
              // 无选中部分
              this.rightKeyword = this.rightKeyword.substring(0, selectionStart) + '' + this.rightKeyword.substring(selectionEnd, this.rightKeyword.toString().length);
              selectOffset = 0;
            }
            setTimeout(() => {
              // 删除时光标处理
              this.setCaretPosition(keyboardRef, selectionStart + selectOffset);
            }, 0);
          } else {
            // 计算器原有逻辑
            this.rightKeyword = this.rightKeyword.substring(0, this.rightKeyword.length - 1);
          }
        }
      } else if (flag_str2) {
        this.rightKeyword = '';
      } else if (flag_str5 && !keyboardRef) {
        this.rightKeyword = '0.';
      } else if (flag_str3 && keyboardRef) {
        // 当输入为.并且时单品折扣和售价时
        if (this.rightKeyword === '' || this.rightKeyword === '0.00') {
          this.rightKeyword = '0.';
          selectOffset = 2;
        } else if (selectionStart === selectionEnd) {
          // 无选中部分
          if (this.rightKeyword.indexOf('.') === -1 && this.rightKeyword.length < 10) {
            this.rightKeyword = this.rightKeyword.substring(0, selectionStart) + '.' + this.rightKeyword.substring(selectionEnd, this.rightKeyword.length);
            selectOffset = 1;
          } else {
            selectOffset = 0;
          }
        } else {
          // 有选中部分
          if (this.rightKeyword.indexOf('.') === -1 && this.rightKeyword.length < 10) {
            this.rightKeyword = this.rightKeyword.substring(0, selectionStart) + '.' + this.rightKeyword.substring(selectionEnd, this.rightKeyword.length);
            selectOffset = 1;
          } else if (this.rightKeyword.substring(selectionStart, selectEndIndex).indexOf('.') !== -1) {
            this.rightKeyword = this.rightKeyword.substring(0, selectionStart) + '.' + this.rightKeyword.substring(selectionEnd, this.rightKeyword.length);
            selectOffset = 1;
          } else {
            return;
          }
        }
        if (this.rightKeyword === '.') {
          this.rightKeyword = '0.';
          selectOffset = 2;
        }
        setTimeout(() => {
          // 删除时光标处理
          this.setCaretPosition(keyboardRef, selectionStart + selectOffset);
        }, 0);
      } else {
        let n;
        if (selectionStart === selectionEnd) {
          if (this.inputLeftSalesprice || this.inputLeftDisc) {
            // 无选中部分
            n =
              _rightKeyword.toString().substring(0, selectionStart) +
              str +
              _rightKeyword.toString().substring(selectionStart, _rightKeyword.toString().length);
            setTimeout(() => {
              // 光标处理
              if (keyboardRef && this.rightKeyword.toString().length <= 8) {
                this.setCaretPosition(keyboardRef, selectionStart + 1);
              } else {
                this.setCaretPosition(keyboardRef, selectionStart + 3);
              }
            }, 0);
          } else {
            // 原先逻辑
            if (this.isFirst) {
              n = str;
              this.isFirst = false;
            } else {
              n = this.rightKeyword.toString() + str;
            }
          }
        } else {
          // 有选中部分
          n =
            _rightKeyword.toString().substring(0, selectionStart) + str + _rightKeyword.toString().substring(selectionEnd, _rightKeyword.length);
          setTimeout(() => {
            // 光标处理
            this.setCaretPosition(keyboardRef, selectionStart + 1);
          }, 0);
        }
        if (flag_str4) {
          this.sonarCalculator(n);
        } else {
          this.rightKeyword = n;
        }
      }
      if (this.bottomSelect === 4) {
        this.toGetProdList();
      }
    },
    setDiscountReduceNumber(n) {
      this.rightKeyword = '';
      // n == 1 为准备输入折扣价格
      if (n === 1) {
        this.selfoff = 'discount';
      }
      // n == 2 为准备输入减扣金额
      if (n === 2) {
        this.selfoff = 'reduce';
      }
      this.show_input_number = true;
    },
    subInputNumber() {
      var that = this;
      if (this.waiting_click === true) {
        return;
      }
      this.waiting_click = true;
      setTimeout(function() {
        that.waiting_click = false;
      }, 100);

      this.show_input_number = false;
      // 如果输入框没有值,则取消折扣和减价优惠 如果有值,转为对应的折扣值或者减价值
      if (this.selfoff === 'discount') {
        this.discountNumber = this.rightKeyword;
        this.discountIndex = this.rightKeyword === '' || Number(this.rightKeyword) === 0 ? null : -1;
        this.reduceIndex = null;
      } else {
        this.reduce_number = this.rightKeyword;
        this.reduceIndex = this.rightKeyword === '' || Number(this.rightKeyword) === 0 ? null : -1;
        this.discountIndex = null;
      }
      this.rightKeyword = '';
      this.getPriceList(2);
      this.selfoff = '';
    },
    subToLeftList() {
      let leftPrice = _.cloneDeep(this.rightKeyword);
      // ==单价
      this.getKexian(Number(leftPrice).toFixed(2), this.kexianPriceType === 'amt' ? 0 : 1);
      var _this = this;
      if (this.waiting_click === true) {
        return;
      }
      this.waiting_click = true;
      setTimeout(function() {
        _this.waiting_click = false;
      }, 300);

      this.left_goods_list.push({
        'name': '直接收款',
        'number': 1,
        'first_letters': 'zjsk',
        'salePrice': this.rightKeyword,
        'vipPrice': this.rightKeyword,
        'id': 0,
        'fingerprint': '646b141adf1d4e1b880d373a00dd3025',
        'remark': '',
        'purPrice': '0',
        'disc': '100',
        'pinyin': 'zhijieshoukuan',
        'isMemberDayGoods': false,
        'amt': this.rightKeyword
      });
      this.getPriceList(3);
      this.SET_SHOW({payListLength: _this.left_goods_list.length});
      this.rightKeyword = '';
    },
    sonarShowSettlement() {
      for (var m = 0; m < this.left_goods_list.length; m++) {
        this.left_goods_list[m].finalPrice =
          (this.showMember && this.memberPayType == 2 && this.left_goods_list[m].vipPrice != 0 && this.left_goods_list[m].isMemberDayGoods === false)
            ? this.left_goods_list[m].vipPrice : this.left_goods_list[m].salePrice;
      }
    },
    pdAutoPacking(n) {
      if (this.setSystem || n === 2) {
        this.showSettlementOne(n);
        return;
      }
      var sub_items = _.cloneDeep(this.left_goods_list);
      for (var i = 0; i < sub_items.length; i++) {
        sub_items[i].qty = Number(sub_items[i].number);
      }
      var sub_data = {
        saleItems: sub_items,
        saleWithoutStockCheck: 1,
        saleWithoutStock: this.setSystem ? '1' : '0',
        autoPacking: this.storeList[0].settings === '' ? true : (demo.t2json(this.storeList[0].settings).autoPacking === '1')
      };
      saleService.settlement(sub_data, res => {
        if (res === 1) {
          this.showSettlementOne(n);
        } else if (res === 2) {
          demo.msg('warning', '拆包后商品库存仍不足');
        } else {
          demo.msg('warning', this.$msg.stock_not_enough);
        }
      }, err => {
        console.log(err, 1111);
      });
    },
    showSettlement(n) {
      console.log(this.watch_pause, 'showSettlement:watch_pause');
      if (this.showing) return;
      this.showing = true;
      setTimeout(() => {
        this.showing = false;
      }, 200)
      // 如果结账,为n = 1 如果退货,为n = 2
      if (Number(this.showFinalPrice) > 10000000) {
        demo.msg('warning', '单笔订单不能超过1000万！');
        return;
      }
      if (this.pcLockScreen) {
        return;
      }
      var pd1 = n === 1 && this.showMember && !pos.network.isConnected();
      if (pd1) {
        demo.msg('warning', '网络连接中断，无法使用会员功能，请去掉会员线下支付');
        return;
      }
      if (this.left_goods_list.length === 0) {
        demo.msg('warning', this.$msg.select_product);
        return;
      }
      if (n === 2 && this.watch_pause) { // 金额还在计算中时禁用退货功能
        return;
      }
      this.pdAutoPacking(n);
    },
    showSettlementOne(n) {
      console.log('showSettlementOne: ' + n);
      this.setPayDefaultAcctId();
      this.buy_back = n;
      this.rightKeyword = '';
      console.log('settlement++ 变为true');
      if (n === 1 && this.ultimate !== null && !this.ifautoCash) {
        this.SET_SHOW({ isContentCombinedPay: true });
      }
      this.sonarShowSettlement();
      this.cancelListenevent();
      this.SET_SHOW({
        payRemark: this.remark,
        settlement: true,
        finalPayParam: {
          from: 'pay',
          buy_back: n,
          // 这里直接判全等通不过
          // returnOutTradeNo: this.recordReturnGoodsList.toString === this.left_goods_list ? this.returnOutTradeNo : '',
          returnOutTradeNo: this.recordReturnGoodsList.toString() === this.left_goods_list.toString() ? this.returnOutTradeNo : '',
          pc_return_goods: this.pc_return_goods,
          showFinalPrice: this.showFinalPrice,
          showMember: this.showMember,
          acctsId: this.acctsId,
          ifautoCash: this.ifautoCash,
          ifpay: this.ifpay,
          member_mobile: this.member_mobile,
          member_id: this.member_id,
          member_name: this.left_member_name,
          left_goods_list: JSON.parse(JSON.stringify(this.left_goods_list)),
          totalPrice: this.totalPrice,
          rightKeyword: this.rightKeyword,
          preDisc: this.discountIndex !== 0 ? 1 : Number((Number(this.discountNumber) / 10).toFixed(2))
        }
      });
      this.bottomSelect = -1;
      this.SET_SHOW({settlementReducePrice: this.reducePrice});
      if (this.showMember) {
        this.judgeIsCheckMember();
      }
    },
    setPayDefaultAcctId() {
      if (this.ifautoCash) { // 开启了自助收银
        this.acctsId = 6;
      } else if (this.showMember) {
        // 当客户选择会员之后，默认支付方式都是会员支付，优先于客户设置的默认支付方式
        this.acctsId = 8;
      } else if (!this.defaultAcctsId) {
        // 当客户未设置默认支付方式的时候
        if (this.aid !== '') { // 如果开通扫码付，默认扫码付
          this.acctsId = 6;
        } else { // 未开通扫码付，默认现金支付
          this.acctsId = 1;
        }
      } else { // 客户设置了默认支付方式
        this.acctsId = this.defaultAcctsId;
      }
    },
    judgeInputWeightKeyboard() {
      return this.isWeightPriceInputable && (this.ifautoCash || !this.$employeeAuth('modify_sale_price'));
    },
    // 称重页面数字键盘输入
    inputWeightKeyboard(str) {
      if (this.judgeInputWeightKeyboard()) {
        return;
      }
      if (str === 'back') {
        this.inputKeyboardValue = this.inputKeyboardValue.toString();
        if (this.inputKeyboardValue.length > 0) {
          this.inputKeyboardValue = this.inputKeyboardValue.substring(0, this.inputKeyboardValue.length - 1);
        }
      } else if (str === 'del') {
        this.inputKeyboardValue = '';
      } else if (str === '.' && this.inputKeyboardValue === '') {
        this.inputKeyboardValue += '0.';
      } else {
        var n = this.inputKeyboardValue.toString() + str;
        if (Number(n) >= 99999.999) {
          this.isWeightPriceInputable ? this.inputKeyboardValue = '999999.99' : this.inputKeyboardValue = '99999.999';
        } else {
          this.inputKeyboardValue = n;
        }
      }
    },
    // 称重页面的“价格”和“重量”发生变化时， 重新计算“金额”
    calcWeightAmount() {
      if (isNaN(Number(this.inputPrice))) {
        return;
      }
      var w = this.inputWeight !== '' ? Number(this.inputWeight).toFixed(3) : 0.000;
      var p = this.inputPrice;
      let kgList = ['千克', '公斤', 'kg', 'Kg', 'kG', 'KG'];
      if (kgList.indexOf(this.alert_unit) !== -1) {
        this.weight_amount = this.setMaxDecimal(w * p, 5);
      } else if (this.alert_unit === '克' || this.alert_unit === 'g' || this.alert_unit === 'G') {
        this.weight_amount = w * p * 1000;
      } else if (this.alert_unit === '斤') {
        this.weight_amount = w * p * 2;
      } else if (this.alert_unit === '两') {
        this.weight_amount = w * p * 20;
      } else {
        console.log(this.alert_unit, w, p);
      }
    },
    // 用于判断切换当前数字键盘的输入对象（价格、重量）
    weightSwitchInputObj(str) {
      if (str === 'price') {
        this.isWeightPriceInputable = true;
        this.isWeightWeightInputable = false;
      } else if (str === 'weight') {
        this.isWeightPriceInputable = false;
        this.isWeightWeightInputable = true;
      } else {
        console.log('其他');
      }
    },
    closeScale() {
      try {
        var data = {};
        external.execScale(data);
        CefSharp.PostMessage(`关闭电子秤串口`);
        this.inputWeight = '';
      } catch (e) {
        demo.msg('warning', this.$msg.electronic_scale_in_error);
      }
    },
    sonarInputLeftList() {
      var x = 1;
      let kgList = ['千克', '公斤', 'kg', 'Kg', 'kG', 'KG'];
      if (kgList.indexOf(this.alert_unit) !== -1) {
        x = 1;
      } else if (this.alert_unit === '克' || this.alert_unit === 'g' || this.alert_unit === 'G') {
        x = 1000;
      } else if (this.alert_unit === '斤') {
        x = 2;
      } else if (this.alert_unit === '两') {
        x = 20;
      } else {
        console.log('其他');
      }
      return x;
    },
    /**
     * @description 将称重后的商品添加到左侧的购物列表。
     * 如果“临时改价”被选中，则该售价只在该笔订单中使用。
     * 如果“临时改价”未选中且售价发生了修改，则将该售价更新到数据库中。
     * @return void
     */
    inputLeftList() {
      var that = this;
      if (this.waiting_click === true) {
        return;
      }
      this.waiting_click = true;
      setTimeout(function() {
        that.waiting_click = false;
      }, 300);

      this.inputPrice = Number(this.inputPrice).toFixed(2);
      var w = this.inputWeight !== '' ? Number(this.inputWeight).toFixed(3) : 0.000;
      var p = this.inputPrice;
      var x = this.sonarInputLeftList();
      this.weight_amount = this.setMaxDecimal(w * p * x, 5);
      this.set_mid_right.number = (this.inputWeight === '' ? document.getElementById('iw').value : this.inputWeight) * x;

      if (Number(document.getElementById('iw').value) > 0 || this.inputWeight > 0) {
        var is_changed_price = Number(this.set_mid_right.salePrice) != Number(this.inputPrice); // 计算该商品的价格是否发生了修改
        var modified_good = _.cloneDeep(this.set_mid_right);
        if (is_changed_price) {
          modified_good.salePrice = this.inputPrice;
        }
        modified_good.disc = this.isVipDay && this.showMember && this.isMemberDayGoods ? this.member_day_discount : '100';
        modified_good.amt = 0;
        modified_good.mprice = modified_good.salePrice;
        this.left_goods_list.push(modified_good);
        this.showInputWeight = false;
        if (!this.weighShowInPay) {
          this.closeScale();
        }
        // 非临时修改价格
        if (is_changed_price && !this.is_temp_modify) {
          this.set_mid_right.salePrice = this.inputPrice; // 修改该商品的售价为修改后的售价
          goodService.updateSalePrice(_.cloneDeep(this.set_mid_right), function() {});
        }
        if (this.kexianPriceType === 'amt') {
          this.inputKexianAmt(modified_good);
        } else {
          this.inputKexian(modified_good);
        }
      } else {
        demo.msg('warning', this.$msg.product_weight_cannot_zero);
      }
    },
    inputKexian(modified_good) { // 单品价格显示单价
      let show = !this.showMember || Number(this.memberPayType) === 1 || (modified_good.ismember_day_goods === true && this.showMember);
      if (show) {
        // ==单价
        if (this.kexianPriceType === 'amt') {
          modified_good.kexianShowAmt = true;
        } else {
          this.getKexian((Number(this.inputPrice) * (Number(modified_good.disc) / 100)).toFixed(2), 1);
        }
      } else {
        this.sonarKexian(modified_good);
      }
    },
    inputKexianAmt(modified_good) { // 客显单品价格显示小计
      let show = !this.showMember || Number(this.memberPayType) === 1 || (modified_good.ismember_day_goods === true && this.showMember);
      if (show) {
        let amt = Number(modified_good.salePrice) * Number(modified_good.number) * Number(modified_good.disc) / 100;
        this.getKexian(amt.toFixed(2), 0);
      } else {
        if (!Number(modified_good.vipPrice)) {
          this.getKexian((Number(modified_good.salePrice) * Number(modified_good.number) * Number(modified_good.disc) / 100).toFixed(2), 0);
        } else {
          this.getKexian(Number(modified_good.vipPrice * Number(modified_good.number)).toFixed(2), 0);
        }
      }
    },
    delLeftGoods() {
      this.left_goods_list.splice(this.set_leftList_index, 1);
      this.bottomSelect = 3;
      this.choose_left = null;
      setTimeout(() => { this.getKexian(this.showFinalPrice, 2); }, 500);
    },
    // getProductInsertFirst() {
    //   var _this = this;
    //   var data = {
    //     'name': '直接收款',
    //     'pinyin': 'zjsk',
    //     'code': '',
    //     'supplier_id': 0,
    //     'unit_id': 1,
    //     'type_id': 1,
    //     'type_name': '全部分类',
    //     'purPrice': 0,
    //     'salePrice': 0,
    //     'vipPrice': 0,
    //     'curStock': 0,
    //     'init_stock': 0,
    //     'init_price': 0,
    //     'init_amt': 0,
    //     'minStock': 0,
    //     'maxStock': 0,
    //     'has_image': 0,
    //     'isMemberDayGoods': false,
    //     'first_letters': 'ZJSK'
    //   };
    //   goodService.put(data, function(res) {
    //     var resj = demo.t2json(res);
    //     if (resj.length > 0) {
    //       // 初始化储存直接收款id
    //       _this.zjsk_id = resj[0].id;
    //     } else {
    //       goodService.goodsSearchZjsk(suc => {
    //         _this.zjsk_id = demo.t2json(suc)[0].id;
    //       }, fal => {
    //         console.log(fal);
    //       });
    //     }
    //   }, function(res) { console.info(res); });
    // },
    continueExit() {
      this.remark = '';
      this.bottomSelect = 3;
      if (this.showPayChangeStatus1 || this.showPayChangeStatus2) {
        this.left_goods_list = [];
        this.discountIndex = null;
        this.reduceIndex = null;
        if (this.showPayChangeStatus1) {
          this.SET_SHOW({ pc_return_goods: true });
          this.SET_SHOW({showPayChangeStatus1: false});
        } else {
          this.SET_SHOW({ pc_return_goods: false });
          this.SET_SHOW({showPayChangeStatus2: false});
        }
      } else if (this.showPayPrint) {
        this.SET_SHOW({showPayPrint: false});
        this.SET_SHOW({ settingFrom: 'pay' });
        this.SET_SHOW({ showPaySetting: false });
        this.SET_SHOW({ isPay: false });
        this.SET_SHOW({ isSetting: true });
      } else if (this.showGoodsSort) {
        this.SET_SHOW({ isPay: false, isGoodsSort: true, showGoodsSort: false });
      } else if (this.showDetail) {
        this.SET_SHOW({ isPay: false, isDetail: true, pc_detail_tab: 1, showDetail: false });
      } else {
        this.SET_SHOW({showPayExitMsg: false});
        this.SET_SHOW({isPay: false});
        this.SET_SHOW({isHome: true});
      }
      this.getKexian('', 0);
    },
    cancelExit() {
      this.SET_SHOW({
        showPayChangeStatus1: false,
        showPayChangeStatus2: false,
        showPayExitMsg: false,
        showPayPrint: false,
        showGoodsSort: false,
        showDetail: false
      });
    },
    addLeftMember(d) {
      this.watch_pause = true;
      this.showMember = true;
      this.left_member_name = d.name;
      this.member_mobile = d.mobile;
      this.discountIndex = 0;
      this.discountNumber = d.disc;
      this.reduceIndex = null;
      this.member_id = d.id;
      this.memberPayType = d.pay_type; // 1是零售价 2是会员价
      this.SET_SHOW({member_detail: d});
      this.SET_SHOW({cardNo: ''});
      this.member_integral = d.integral;
      this.member_money = d.has_money;
      for (var i = 0; i < this.left_goods_list.length; i++) {
        this.left_goods_list[i].isScales = false;
        for (var j = 0; j < this.member_day_goods.length; j++) {
          if (this.left_goods_list[i].name === this.member_day_goods[j].name &&
            JSON.stringify(this.left_goods_list[i].specs) === JSON.stringify(this.member_day_goods[j].specs)) {
            this.left_goods_list[i].disc = this.member_day_discount;
          }
        }
      }
      this.can_getProdList = true;
      this.bottomSelect = 3;
      setTimeout(() => {
        this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
      }, 400);
      setTimeout(() => {
        this.watch_pause = false;
        this.getPriceList(4);
      }, 0);
    },
    editDisc(index) {
      if (!this.$employeeAuth('edit_vips')) {
        return;
      }
      this.member_disc = '';
      this.member_index = index;
      this.show_edit_disc = true;
      demo.actionLog(logList.vipChangeDisc);
    },
    editPwd(index) {
      this.member_pwd = '';
      this.member_index = index;
      this.show_edit_pwd = true;
    },
    addMemberMoney(index) {
      this.SET_SHOW({
        member_detail: _.cloneDeep(this.memTableData[index])
      });
      this.SET_SHOW({
        showInputRecharge: true
      });
      demo.actionLog(logList.vipRecharge);
    },
    saveMemberPwd() {
      this.show_edit_pwd = false;
      this.memTableData[this.member_index].password = this.member_pwd;
      this.member_disc = this.memTableData[this.member_index].disc;
      this.saveMemberApi();
    },
    saveMemberDisc() {
      if (this.member_disc === '') {
        return;
      }
      this.memTableData[this.member_index].disc = this.member_disc;
      this.saveMemberApi();
    },
    saveMemberApi() {
      this.memTableData[this.member_index].phone = this.sys_uid;
      this.memTableData[this.member_index].sysSid = this.sys_sid;
      this.memTableData[this.member_index].systemName = $config.systemName;
      this.memTableData[this.member_index].revise_user = this.phone;
      var _this = this;
      var member_data = this.memTableData[this.member_index];
      demo.$http.post(_this.$rest.pc_editvip, member_data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          if (rs.data.code === '0') {
            demo.msg('success', _this.$msg.save_success);
            if (_this.showMember && _this.memTableData[_this.member_index].mobile === _this.member_mobile) {
              _this.discountNumber = _this.member_disc;
            }
          } else {
            demo.msg('warning', _this.$msg.save_failure);
          }
        });
    },
    setLeftDiscNull () {
      this.rightKeyword = '';
    },
    refreshScreen2() {
      this.pushScreen2();
    },
    closeLastOrder() {
      this.SET_SHOW({showLastOrder: false});
    },
    async fullOrderReturn() { // 显示上一单--整单退货
      if (!this.$employeeAuth('return_sales')) {
        demo.msg('error', this.$msg.not_refund_permissions);
        return;
      }
      if (this.hasRefundType !== -1) {
        demo.msg('error', '请勿重复退单');
        return;
      }
      // 扫码付单才进行云端采号
      if ([7, 8].includes(this.orderInfo.accountId)) {
        const params = {
          deviceCode: demo.$store.state.show.devicecode,
          outTradeNo: this.orderInfo.code
        }
        try {
          const { code, data } = await getGenerateOrder(params);
          if (code === 200) {
            this.backMoneyParams = this.getRefundParams(this.salseGoods, this.orderInfo, this.combinedItem, data);
            this.showLastOrderDetail = false;
            this.showBackMoneyDialog = true;
          } else {
            demo.msg('error', '服务器请求异常，请稍后重试');
          }
        } catch (error) {
          demo.msg('error', '服务器请求异常，请稍后重试');
        }
      } else {
        this.backMoneyParams = this.getRefundParams(this.salseGoods, this.orderInfo, this.combinedItem);
        this.showLastOrderDetail = false;
        this.showBackMoneyDialog = true;
      }
      // // 检查该订单是否支持整单退货
      // if (pos.network.isConnected()) {
      //   let url;
      //   demo.$http.post(url, {orderNo: this.lastOrderDetail[0].code})
      //     .then(res => {
      //       console.log(res);
      //       // 符合退款的条件下进行后续操作
      //     })
      // } else {
      //   demo.msg('warning', '销售单退货仅支持联网使用');
      // }
    },
    partialReturn() { // 显示上一单--部分退货
      if (!this.$employeeAuth('return_sales')) {
        demo.msg('error', this.$msg.not_refund_permissions);
        return;
      }
      if (this.hasRefundType === 1) {
        demo.msg('error', '请勿重复退单');
        return;
      }
      if (!demo.isNullOrTrimEmpty(this.lastOrderDetail[0].vipname) || this.lastOrderDetail[0].accountId === 99) {
        // 组合
        if (this.lastOrderDetail[0].accountId === 99) {
          demo.actionLog(logList.clickPurchaseCombinationPayReturns);
        }
        // 有会员
        if (!demo.isNullOrTrimEmpty(this.lastOrderDetail[0].vipname)) {
          demo.actionLog(logList.clickPurchaseVipPartialReturns);
        }
        demo.msg('warning', '暂不支持有组合支付或会员的订单部分退货');
        return;
      }
      this.lastOrderDetail[0].saleItems = this.lastOrderDetail[0].saleItems.map(item => {
        return {...item, 'goodName': item.name};
      });
      // 查询订单中是否有0元商品
      const hasZero = this.salseGoods.find(good => {
        return +good.amt === 0;
      })
      this.showLastOrderDetail = false;
      if (hasZero) {
        this.zeroMoneyTipsShow = true;
        return;
      }
      this.showPartialReturn = true;
      // // 检查该订单是否支持部分退货
      // if (pos.network.isConnected()) {
      //   let url;
      //   demo.$http.post(url, {orderNo: this.lastOrderDetail[0].code})
      //     .then(res => {
      //       console.log(res);
      //       // 符合退款的条件下进行后续操作
      //       if (!demo.isNullOrTrimEmpty(this.lastOrderDetail[0].vipname) || this.lastOrderDetail[0].accountId === 99) {
      //         if (this.lastOrderDetail[0].accountId === 99 && !demo.isNullOrTrimEmpty(this.lastOrderDetail[0].vipname)) {
      //           // 有会员和组合
      //           demo.actionLog(logList.clickPurchaseVipPartialReturns);
      //           demo.actionLog(logList.clickPurchaseCombinationPayReturns);
      //         } else if (this.lastOrderDetail[0].accountId !== 99 && !demo.isNullOrTrimEmpty(this.lastOrderDetail[0].vipname)) {
      //           // 没组合有会员
      //           demo.actionLog(logList.clickPurchaseVipPartialReturns);
      //         } else {
      //           demo.actionLog(logList.clickPurchaseCombinationPayReturns);
      //         }
      //         demo.msg('warning', '暂不支持有组合支付或会员的订单部分退货');
      //         return;
      //       }
      //       this.showLastOrderDetail = false;
      //       this.showPartialReturn = true;
      //     })
      // } else {
      //   demo.msg('warning', '销售单退货仅支持联网使用');
      // }
    },
    printLastOrder() { // 打印上一销售单小票
      if (this.setting_small_printer != undefined && this.setting_small_printer != null &&
        this.setting_small_printer.trim() != '' && this.lastOrderDetail.length !== 0) {
        this.printLastData();
      } else {
        demo.msg('warning', this.$msg.not_setting_small_printer);
      }
    },
    printLastData() {
      if (this.printClick) {
        return;
      }
      try {
        console.log('开始打印');
        this.printClick = true;
        this.orderno = this.lastOrderDetail[0].code;
        let printData = _.cloneDeep(this.lastOrderDetail[0]);
        printData.printername = this.setting_small_printer;
        if (printData.operater) {
          printData.operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
        }
        console.log('printData', printData);
        pos.printer.printPOSTimes(printData, this);
        this.orderno = '';
        setTimeout(() => {
          this.printClick = false;
        }, 1000);
      } catch (e) {
        setTimeout(() => {
          demo.msg('warning', '打印设备异常，请检查小票打印机！');
          this.orderno = '';
          this.printClick = false;
        }, 500);
      }
    },
    judgeIsCheckMember () {
      let that = this;
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，会员功能暂时无法使用');
        return;
      }
      let vipdata = {
        'systemName': $config.systemName,
        'phone': that.sys_uid,
        'sysSid': that.sys_sid,
        'thisMonthBirthday': false,
        'currentPage': that.pagenum,
        'pageSize': that.pageSize,
        'is_deleted': 0,
        'searchStr': that.member_mobile
      };
      demo.$http.post(that.$rest.pc_vipList, vipdata, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          let tmpDataArr = rs.data.data.list;
          console.log(tmpDataArr, 'rs.data.data.list');
          if (tmpDataArr.length !== 0) {
            tmpDataArr.forEach(arr => {
              if (arr.name === that.left_member_name) {
                let sData = {
                  'screen2MemberName': arr.name,
                  'screen2MemberPhone': arr.mobile,
                  'screen2MemberMoney': arr.has_money,
                  'screen2MemberPoint': arr.integral
                };
                demo.screen2(sData, 26);
              }
            });
          }
        });
    },
    quanxian() {
      return !this.$employeeAuth('create_products') || !this.$employeeAuth('import_products') ||
                !this.$employeeAuth('delete_products') || !this.$employeeAuth('products_curstock_inventories')
    }
  },
  computed: {
    ...mapState({
      isHome: state => state.show.isHome,
      addGoodsCategory: state => state.show.addGoodsCategory,
      addGoodsCategoryId: state => state.show.addGoodsCategoryId,
      addGoodsCategoryfingerprint: state => state.show.addGoodsCategoryfingerprint,
      addGoodsUnit: state => state.show.addGoodsUnit,
      addGoodsUnitId: state => state.show.addGoodsUnitId,
      addGoodsUnitfingerprint: state => state.show.addGoodsUnitfingerprint,
      pcLockScreen: state => state.show.pcLockScreen,
      pc_return_goods: state => state.show.pc_return_goods,
      return_goods: state => state.show.return_goods,
      returnGoodsMsg: state => state.show.returnGoodsMsg,
      shortCutList: state => state.show.shortCutList,
      username: state => state.show.username,
      showPayChangeStatus1: state => state.show.showPayChangeStatus1,
      showPayChangeStatus2: state => state.show.showPayChangeStatus2,
      showPayExitMsg: state => state.show.showPayExitMsg,
      showGoodsSort: state => state.show.showGoodsSort,
      showDetail: state => state.show.showDetail,
      password: state => state.show.password,
      setting_moneybox: state => state.show.setting_moneybox,
      setting_small_printer: state => state.show.setting_small_printer,
      setting_hasmoneybox: state => state.show.setting_hasmoneybox,
      showTypeManage: state => state.show.showTypeManage,
      showSelManage: state => state.show.showSelManage,
      token: state => state.show.token,
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      ifautoCash: state => state.show.ifautoCash,
      isSyncing: state => state.show.isSyncing,
      autoCashShow: state => state.show.autoCashShow,
      phone: state => state.show.phone,
      settlement: state => state.show.settlement,
      completePay: state => state.show.completePay,
      showInputRecharge: state => state.show.showInputRecharge,
      goodsNameLength: state => state.show.goodsNameLength,
      delayedTime: state => state.show.delayedTime,
      showRepeatChoose: state => state.show.showRepeatChoose,
      cardNo: state => state.show.cardNo,
      showAddMember: state => state.show.showAddMember,
      showLastOrder: state => state.show.showLastOrder,
      lastOrderDetail: state => state.show.lastOrderDetail,
      clickInterval: state => state.show.clickInterval,
      isVipDay: state => state.show.isVipDay,
      showMemberExchange: state => state.show.showMemberExchange,
      soundVolume: state => state.show.soundVolume,
      aid: state => state.show.aid,
      loginInfo: state => state.show.loginInfo,
      payListLength: state => state.show.payListLength,
      storeList: state => state.show.storeList,
      ultimate: state => state.show.ultimate,
      showPayPrint: state => state.show.showPayPrint,
      isPrintSetting: state => state.show.isPrintSetting,
      codeTimer: state => state.show.codeTimer,
      screenWidth: state => state.show.screenWidth,
      memberDetail: state => state.show.member_detail,
      scanCodeSound: state => state.show.scanCodeSound,
      showHotGood: state => state.show.showHotGood, // 是否显示热销分类
      defaultAcctsId: state => state.show.defaultAcctsId,
      weighValue: state => state.show.weighValue,
      weighTypeValue: state => state.show.weighTypeValue,
      hasWeigh: state => state.show.hasWeigh,
      weighShowInPay: state => state.show.weighShowInPay,
      scanerObj: state => state.show.scanerObj,
      zgznActive: state => state.show.zgznActive,
      pcLockScreenConfirm: state => state.show.pcLockScreenConfirm,
      showChooseWeight: state => state.show.showChooseWeight,
      showTimesCard: state => state.show.showTimesCard,
      isOnceOpen: state => state.show.isOnceOpen,
      showKexian: state => state.show.showKexian,
      weighStatus: state => state.show.weighStatus,
      weighSet: state => state.show.weighSet,
      specs: state => state.info.specs,
      defaultSettlementKey: state => state.show.defaultSettlementKey,
      weighUnitList: state => state.show.weighUnitList,
      kexianValue: state => state.show.kexianValue
    })
  },
  beforeDestroy() {
    this.closeScale();
    // 移除键盘及c#扫码枪监听
    external.scanerHookStop();
    this.cancelListenevent();
    var s_data = {
      'screen2ShowList': [],
      'screen2TotalPrice': 0,
      'screen2ShowFinalPrice': 0,
      'screen2ReducePrice': 0,
      'screen2ReceiveMoney': 0
    };
    demo.screen2(s_data, 27);
    clearInterval(this.scanTimeer);
    this.scanTimeer = null;
    window.removeEventListener('resize', this.listenResize);
    this.left_goods_list = [];
    this.totalPrice = 0;
    this.reducePrice = 0;
    this.rightKeyword = '';
    clearTimeout(this.dwArrowDisable);
  }
};
</script>
