<style lang="less">
.pc_guk {
  width: 100%;
  height: 100%;
  position: relative;
  background: #F5F8FB;
  top: 0;
  left: 0;
  overflow: hidden;
}
.pc_guk1 {
  overflow: hidden;
  position: relative;
}
.pc_guk11 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-top: 13px;
  margin-left: 20px;
  background: #fff;
  input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: @text;
  }
}
.pc_guk11 input {
  width: 230px;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 16px;
  margin-top: 8px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_guk12 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 14px;
  float: left;
  cursor: pointer;
}

.pc_guk13{
  width: 127px;
  height: 17px;
  font-size: 16px;
  font-weight: bold;
  color: #557485;
  line-height: 17px;
}

.pc_guk14{
  flex:1;
  margin-left:50px;
  padding-right: 20px;
  font-size: 16px;
  font-weight: 400;
  color: @text;
}
.pc_guk15{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 20px;
  margin-left: 20px;
}
.pc_guk16 {
  width: 110px;
  height: 44px;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 44px;
  border-radius: 22px;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  float: right;
  margin-right: 28px;
  margin-top: 13px;
  cursor: pointer;
}
.pc_guk17 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  color: @themeFontColor;
  overflow: auto;
}
.pc_guk18 {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 831px;
  height: 250px;
  margin-top: 225px;
  border-radius: 6px;
  z-index: 150;
  overflow: hidden;
}
.pc_guk19 {
  width: 300px;
  height: 46px;
  background: #F5F8FB;
  border: 1px solid #E3E6EB;
  border-radius: 4px;
  line-height: 46px;
  color: @text;
  text-indent: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: @text;
  }
}
.pc_guk2 {
  overflow: hidden;margin-left: 30px;margin-top: 30px;
  font-size: 16px;
}
.pc_guk2 div {
  float: left;
}
.pc_guk21 {
  width: 32px;text-align: center;line-height: 46px;color: @themeFontColor;text-indent: 3px;
}
.pc_guk22 {
  width: 119px;
  height: 44px;
  line-height: 42px;
  background: #FFFFFF;
  border: 1px solid @themeBackGroundColor;
  font-size: 18px;font-weight: 500;
  color: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  margin-top: 40px;margin-left: 546px;
  float: left;
  cursor: pointer;
}
.pc_guk23 {
  width: 119px;
  height: 44px;
  line-height: 42px;
  border: 1px solid @themeBackGroundColor;
  font-size: 18px;font-weight: 500;
  border-radius: 4px;
  text-align: center;
  margin-top: 40px;margin-left: 20px;
  background: @themeBackGroundColor;
  color: #FFF;
  float: left;
  cursor: pointer;
}
.pc_guk24 {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 890px;
  height: 660px;
  margin-top: 15px;
  border-radius: 6px;
  z-index: 200;
  overflow: hidden;
}
.pc_guk25 {
  overflow: hidden;
  margin-top: 10px;
}
.pc_guk26 {
  float: left;
  margin-left: 30px;
  line-height: 32px;
}
.pc_guk27 {
  float: right;
  margin-right: 30px;
}
.pc_guk28 {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 400;
}
.pc_guk29 {
  width: 450px;height: 300px;border-radius: 6px;background: #FFF;
  margin: 0 auto;margin-top: 200px;overflow: hidden;
}
.pc_guk3 {
  line-height: 24px;font-size: 24px;margin-top: 50px;color: @themeFontColor;
  font-weight: bold;text-align: center;
}
.pc_guk31 {
  font-size: 24px;line-height: 24px;margin-top: 40px;text-align: center;color: @themeFontColor;
}
.pc_guk34 {
  overflow: hidden;margin-top: 80px;
}
.pc_guk35 {
  width: 140px;height: 50px;line-height: 50px;text-align: center;border-radius: 4px;
  background: @themeFontColor;color: #FFF;float: left;margin-left: 70px;font-size: 20px;
}
.pc_guk36 {
  width: 140px;height: 50px;line-height: 50px;text-align: center;border-radius: 4px;
  background: @themeBackGroundColor;color: #FFF;float: left;margin-left: 30px;font-size: 20px;
}
.guk11 {
  border-color:  @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div class="pc_guk">

    <div v-show="show_det" class="pc_guk28">
      <div class="pc_guk29">
        <div class="pc_guk3">提示</div>
        <div class="pc_guk31">确定要删除该换算关系吗？</div>
        <div class="pc_guk34">
          <div class="pc_guk35" @click="show_det = false">取消</div>
          <div class="pc_guk36" @click="continueDet()">确定</div>
        </div>
      </div>
    </div>
    <div v-show="selectGoods" class="pc_guk17" style="z-index: 300;">
      <div class="pc_guk24">
        <div style="height: 60px;line-height: 60px;font-size: 18px;font-weight: bold;color: #567485;width: calc(100% - 60px);margin: 0 auto;border-bottom: 1px solid #E3E6EB;">
          <div style="float: left;">选择商品</div>
          <div style="float: right;font-size: 30px;cursor: pointer;height: 60px;" @click="selectGoods = false">×</div>
        </div>
        <el-input
          id="goods_input"
          :placeholder="select_flg === 1 ? '请选择大包装商品' : '请选择小包装商品'"
          style="width: calc(100% - 60px);margin-left: 30px;margin-top: 10px;background: #F5F8FB;"
          v-model="goodsKeyword"
          maxlength="60"
          @focus="selectText('goods_input')"
          clearable
        ></el-input>
        <div style="width: calc(100% - 60px);margin: 0 auto;margin-top: 10px;border: 1px solid #E3E6EB;">
          <el-table
            :data="goods_list"
            @row-click = "clickAlertGoodsRow"
            stripe
            style="width: 100%;height: 476px;font-size: 16px;overflow: auto"
          >
            <el-table-column prop="name" show-overflow-tooltip width="300" label="商品名称"></el-table-column>
            <el-table-column
              prop="code"
              show-overflow-tooltip
              width="170"
              label="条码"
            ></el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              min-width="110"
              align="right"
              show-overflow-tooltip
              label="进价">
              <template slot-scope="scope">{{ scope.row.purPrice }}</template>
            </el-table-column>
            <el-table-column prop="salePrice" show-overflow-tooltip width="110" label="售价" align="right"></el-table-column>
            <el-table-column prop="curStock" show-overflow-tooltip width="118 " label="库存" align="right"></el-table-column>
            <el-table-column prop="" width="20" label=""></el-table-column>
          </el-table>
        </div>
        <div class="pc_guk25">
          <div class="pc_guk26">
            共<span style="color: #d5aa76;margin-left: 8px;margin-right: 8px;">{{goods_total}}</span>款商品
          </div>
          <div class="pc_guk27">
            <el-pagination
              layout="prev, pager, next"
              :total="goods_total"
              @current-change="handleCurrentChange"
              :current-page.sync="goods_pageNum"
              :page-size="goods_limit"
              :page-count="goods_total"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <div v-show="show_addGoodsUnpack || edit_addGoodsUnpack" class="pc_guk17">
      <div class="pc_guk18">
        <div style="height: 60px;display: flex;justify-content: space-between;align-items: center; border-bottom: 1px solid #E3E6EB;margin: 0 30px;">
          <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;"
          v-show="show_addGoodsUnpack"
          @click="selectGoods">
            添加换算关系
          </div>
          <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;" v-show="edit_addGoodsUnpack">
            编辑换算关系
          </div>
          <div
            style="font-size: 30px;color: #8298A6;cursor: pointer;"
            @click="close()"
          >×</div>
        </div>
        <div class="pc_guk2">
          <div class="pc_guk19" @click="selectFunction(1)" :title="select_one.name" :style="edit_addGoodsUnpack ? 'opacity: 0.6;cursor: not-allowed;' : 'cursor: pointer;'">
            <span v-show="select_one.name !== ''" style="color: #567485">{{select_one.name}}</span>
            <span v-show="select_one.name === ''">请选择大包装商品</span>
          </div>
          <div class="pc_guk21">=</div>
          <div class="pc_guk19" style="width: 110px;">
            <input placeholder="请输入数量"
            style="width: 90px;height: 42px;margin-left: -5px;border: none;background: #F5F8FB;color: #567485;"
            v-model="pack_num"
            @input="packNumInputHandler()"/></div>
          <div class="pc_guk21">×</div>
          <div class="pc_guk19" @click="selectFunction(2)" :title="select_two.name" :style="edit_addGoodsUnpack ? 'opacity: 0.6;cursor: not-allowed;' : 'cursor: pointer;'">
            <span v-show="select_two.name !== ''" style="color: #567485">{{select_two.name}}</span>
            <span v-show="select_two.name === ''">请选择小包装商品</span>
          </div>
        </div>
        <div style="overflow: hidden;">
          <div v-show="show_addGoodsUnpack" class="pc_guk22" @click="close()">取消</div>
          <div v-show="edit_addGoodsUnpack" class="pc_guk22" @click="det()">删除</div>
          <div class="pc_guk23" @click="save()">确定</div>
        </div>
      </div>
    </div>
    <div class="pc_guk15">
      <div class="pc_guk13">自动拆包</div>
      <el-switch
        v-model="autoPacking"
        :active-color="$t('image.homeImage.color')"
        inactive-color="#DCDFE6"
      ></el-switch>
      <div class="pc_guk14">
        当小包装不够售卖时，系统按照设定的拆包规则把大包装拆解成小包装售卖，如把一条烟拆成10盒；拆解后将以小商品的价格计算利润。
      </div>
    </div>
    <div style="background: #FFF;border-radius: 6px;width: calc(100% - 20px);margin: 0 auto;border: 1px solid #E3E6EB;" :style="f_height">
      <div class='pc_guk1'>
        <div style='float: left;'>
          <div
            class='pc_guk11'
            :class="inputing_keyword ? 'guk11' : 'isInPutIng1'"
          >
            <input
              @focus='inputing_keyword = true'
              @blur='inputing_keyword = false'
              type='text'
              placeholder='商品名称/条码/首字母/扫码'
              maxlength="60"
              v-model='keyword'
              id='goodsUnpack_keyword'
              style="margin-left:20px"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
            />
            <img
              alt=""
              class='pc_guk12'
              v-show="keyword != ''"
              @click="inputFocus('goodsUnpack_keyword')"
              src='../../image/pc_clear_input.png'
            />
          </div>
        </div>
        <div class="pc_guk16" @click="addGoodsUnpack()">新增关系</div>
      </div>
      <div style="width: calc(100% - 40px);margin: 0 auto;margin-top: 13px;border: 1px solid #E3E6EB;border-radius: 2px;overflow: hidden;">
        <el-table
          ref="multipleTable"
          :data="tableData"
          :stripe="true"
          tooltip-effect="dark"
          @row-click = "clickGoodsRow"
          style="width: 100%;font-size: 16px;color: #567485;"
          :height="table_height"
        >
          <el-table-column
            show-overflow-tooltip
            min-width="38%"
            label="商品名称（大包装）"
            align="left"
          >
            <template slot-scope="scope">{{ scope.row.goodName }}</template>
          </el-table-column>
          <el-table-column label="" align="center" show-overflow-tooltip min-width="6%">
            <template>=</template>
          </el-table-column>
          <el-table-column label="换算数量" align="left" show-overflow-tooltip min-width="12%">
            <template slot-scope="scope">{{ scope.row.packingCount }}</template>
          </el-table-column>
          <el-table-column label="" align="center" min-width="6%">
            <template>x</template>
          </el-table-column>
          <el-table-column label="商品名称（小包装）" align="left" show-overflow-tooltip min-width="38%">
            <template slot-scope="scope">{{ scope.row.childName }}</template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 新增拆包关系时自动拆包开启确认对话框 -->
    <confirm-dialog
      :visible.sync="showConfirmDialog"
      :message="`新增成功<br/>是否开启自动拆包？`"
      confirmText="开启"
      :closeOnClickModal="false"
      @confirm="autoPacking = true;showConfirmDialog = false"
    ></confirm-dialog>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import ConfirmDialog from '@/common/components/ConfirmDialog';

export default {
  components: {
    ConfirmDialog
  },
  data () {
    return {
      showConfirmDialog: false,
      inputing_keyword: true,
      goodsKeyword: '',
      pinyin: false,
      f_height: '',
      goods_list: [],
      table_height: '',
      keyword: '',
      goods_limit: 10,
      goods_pageNum: 1,
      goods_total: 0,
      pack_num: '',
      autoPacking: true,
      show_det: false,
      selectGoods: false,
      tableData: [],
      show_addGoodsUnpack: false,
      edit_addGoodsUnpack: false,
      select_flg: 1,
      select_one: {fingerprint: '', name: ''},
      select_two: {fingerprint: '', name: ''}
    };
  },
  mounted() {
    if (document.body.clientWidth < 1200) {
      this.table_height = document.body.clientHeight - '240';
      this.f_height = 'height: calc(100% - 145px);';
    } else {
      this.table_height = document.body.clientHeight - '220';
      this.f_height = 'height: calc(100% - 125px);';
    }
    this.inputFocus('goodsUnpack_keyword');
    this.autoPacking = this.storeList[0].settings === '' ? true : (demo.t2json(this.storeList[0].settings).autoPacking === '1');
    this.getPackingGoodsList();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    selectFunction(n) {
      if (this.edit_addGoodsUnpack) {
        return;
      }
      this.selectGoods = true;
      this.select_flg = n;
    },
    packNumInputHandler() {
      if (this.pack_num === '') {
        return;
      }
      this.pack_num = this.$intMaxMinLimit({data: this.pack_num, max: 9999, min: 1})
    },
    clickGoodsRow(row) {
      this.edit_addGoodsUnpack = true;
      this.select_one = {
        fingerprint: row.fingerprint,
        name: row.goodName
      };
      this.select_two = {
        fingerprint: row.childFingerprint,
        name: row.childName
      };
      this.pack_num = row.packingCount;
    },
    clickAlertGoodsRow(row) {
      if (this.select_flg === 1) {
        this.select_one = {
          fingerprint: row.fingerprint,
          name: row.name
        };
      } else if (this.select_flg === 2) {
        this.select_two = {
          fingerprint: row.fingerprint,
          name: row.name
        };
      } else {
        // todu
      }
      this.selectGoods = false;
    },
    save() {
      var reg = /^[1-9]\d*$/;
      if (this.select_one.name === '') {
        demo.msg('warning', '请选择大包装商品！');
        return;
      } else if (this.pack_num === '') {
        demo.msg('warning', '请输入数量！');
        return;
      } else if (!reg.test(this.pack_num)) {
        demo.msg('warning', '数量应为大于0的正整数！');
        return;
      } else if (Number(this.pack_num) > 99999) {
        demo.msg('warning', '拆包数量最大值为99999');
        return;
      } else if (this.select_two.name === '') {
        demo.msg('warning', '请选择小包装商品！');
        return;
      } else {
        // to do
      }
      var obj = {
        parent: this.select_one.fingerprint,
        child: this.select_two.fingerprint,
        count: Number(this.pack_num),
        isDel: 0
      };
      saleService.packingSave(obj, () => {
        demo.msg('success', '保存成功');
        this.close();
        this.getPackingGoodsList();
        if (!this.autoPacking) {
          this.showConfirmDialog = true;
        }
      }, function(error) {
        demo.msg('error', error);
      });
    },
    getPackingGoodsList() {
      if (this.pinyin) {
        return;
      }
      saleService.getPackingGoodsList(this.keyword, res => {
        this.tableData = res;
        this.show_det = false;
      });
    },
    handleCurrentChange(val) {
      this.goods_pageNum = val;
      this.getProdList();
    },
    getProdList() {
      var data = {
        pset: '',
        type: '',
        condition: this.goodsKeyword,
        limit: this.goods_limit,
        offset: Number((this.goods_pageNum - 1) * this.goods_limit)
      };
      var that = this;
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        that.goods_list = json;
      });
      goodService.searchCnt(data, function (res) {
        that.goods_total = Number(demo.t2json(res)[0].cnt);
      });
    },
    det() {
      this.show_det = true;
    },
    continueDet() {
      var obj = {
        parent: this.select_one.fingerprint,
        child: this.select_two.fingerprint,
        count: Number(this.pack_num),
        isDel: 1
      };
      var that = this;
      saleService.packingSave(obj, function() {
        demo.msg('success', '删除成功');
        that.close();
        that.getPackingGoodsList();
      }, function(error) {
        demo.msg('error', error);
      });
    },
    inputFocus(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    addGoodsUnpack() {
      this.select_one = {fingerprint: '', name: ''};
      this.select_two = {fingerprint: '', name: ''};
      this.pack_num = '';
      this.show_addGoodsUnpack = true;
    },
    close() {
      this.show_addGoodsUnpack = false;
      this.edit_addGoodsUnpack = false;
    }
  },
  watch: {
    selectGoods() {
      if (!this.selectGoods) {
        return;
      }
      setTimeout(() => {
        $('#goods_input').focus();
      }, 0);
      this.goods_pageNum = 1;
      this.goodsKeyword = '';
      this.getProdList();
    },
    autoPacking() {
      var m_list = _.cloneDeep(this.storeList);
      var mid = JSON.parse(m_list[0].settings);
      mid.autoPacking = this.autoPacking ? '1' : '0';
      m_list[0].settings = JSON.stringify(mid);
      this.SET_SHOW({storeList: m_list});
      storeInfoService.updateSettings({'id': 1, 'settings': JSON.stringify(mid)}, () => {
        // todo
      }, () => {
        // todo
      });
    },
    goodsKeyword() {
      this.watch_type = false;
      this.can_getProdList = false;
      if (this.keyword !== '') {
        this.type = '';
        this.select_right = 3;
      }
      this.pagenum = 1;
      var that = this;
      setTimeout(function() {
        that.watch_type = true;
        that.can_getProdList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.right_list = [];
        that.getProdList(4);
      }, that.delayedTime);
    },
    keyword() {
      this.can_getProdList = false;
      var that = this;
      setTimeout(function() {
        that.can_getProdList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.tableData = [];
        that.getPackingGoodsList();
      }, that.delayedTime);
    }
  },
  computed: mapState({
    delayedTime: state => state.show.delayedTime,
    storeList: state => state.show.storeList
  })
};
</script>
