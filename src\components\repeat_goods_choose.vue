<template>
  <div v-if="showRepeatChoose" class="repeat_choose">
    <div class="repeat_choose--shade">
      <div class="repeat_choose--container">
        <div class="repeat_choose--header">
          <div>
            <span style="font-size: 18px;font-weight: bold;">商品条码重复，请选择商品</span>
          </div>
          <div @click="repeatChooseClose">
            <span class="close">×</span>
          </div>
        </div>
        <div class="repeat_choose--body">
          <div class="text">条码重复会造成库存管理混乱，请在【商品管理】中修改或删除重复条码。</div>
          <div class="itemContainer">
            <div v-for="(item, index) in repeatData" :key="item.fingerprint"
              :class="index === repeatIndex ? 'module_active' : 'module'"
              @click="repeatIndex = index;">
              <div v-if="!item.image && item.pinyin" class="simpleName">
                <div v-if="item.pinyin.length <= 2" style="text-align: center;">
                  {{item.pinyin}}
                </div>
                <div v-else>
                  <div class="double_f">{{item.pinyin.substring(0, 2)}}</div>
                  <div class="double_s">{{item.pinyin.substring(2, 4)}}</div>
                </div>
              </div>
              <el-image v-else :src="item.image ? $getSrcUrl(item.image) : require('../image/pc_no_cloth_img.png')"/>
              <div class="info">
                <div class="info_name">{{item.name + '（库存：' + item.curStock + '）'}}</div>
                <div class="info_code" v-html="repeatCodeHighLight(item)">
                </div>
              </div>
              <div>
                <i v-if="index === repeatIndex" class="el-icon-success"></i>
                <div v-else class="circle_empty"></div>
              </div>
            </div>
          </div>
          <div class="repeat_choose--submit" @click="repeatChoose">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  props: {
    repeatData: {
      type: Array
    },
    repeatCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      repeatIndex: 0
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    repeatChooseClose() {
      this.SET_SHOW({ showRepeatChoose: false });
    },
    repeatCodeHighLight(item) {
      let str = item.isCodesGoods ? item.code + ',' + item.extendCodes.map(i => { return i.extBarcode; }).join() : item.code;
      return str.replace(new RegExp(this.repeatCode, 'g'), `<span style="color: #EC2D30">${this.repeatCode}</span>`);
    },
    repeatChoose() {
      this.$emit('repeatChooseEmit', this.repeatData, this.repeatIndex);
      this.repeatIndex = 0;
    }
  },
  created() {
    console.log(this.repeatData);
    console.log(this.repeatCode);
  },
  computed: mapState({
    showRepeatChoose: state => state.show.showRepeatChoose
  })
}
</script>

<style lang="less" scoped>
.repeat_choose {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 400;
  &--shade {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &--container {
    position: relative;z-index: 800;border-radius: 5px;color: @themeFontColor;
    background: #FFF;width: 630px;overflow: hidden;
  }
  &--header {
    margin: 0 20px;display:flex;justify-content: space-between;
    align-items: center;border-bottom: 1px solid #E3E6EB;height: 56px;
    .close {
      font-size: 36px;color: #567485;cursor:pointer;
    }
  }
  &--body {
    padding: 0 15px 0 20px;font-size: 16px;color: #567485;
    .text {
      margin: 6px 0;
    }
    .itemContainer {
      overflow-y: auto;
      max-height: 286px;
    }
    .simpleName {
      width: 46px;
      height: 46px;
      line-height: 46px;
      font-size: 14px;
      font-weight: bold;
      float: left;
      background: @themeBackGroundColor;
      color: #fff;
      border-radius: 4px;
      overflow: hidden;
    }
    .double_f {
      height: 23px;
      line-height: 23px;
      text-align: center;
    }
    .double_s {
      height: 23px;
      line-height: 14px;
      text-align: center;
    }
    .info_name {
      font-weight: bold;
    }
    .info_code {
      word-break: break-all;
      line-height: 20px;
    }
    /deep/.el-image {
      width: 46px !important;
      height: 46px !important;
      border-radius: 4px;
    }
    .module {
      padding: 16px;
      border: 1px solid #eaecf0;
      border-radius: 8px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      margin-right: 5px;
      .info {
        width: 518px;
        padding-left: 20px;
      }
      .circle_empty {
        width: 20px;
        height: 20px;
        border-radius: 10px;
        margin: 0 2px;
        border: 1px solid #D0D5DD;
      }
    }
    .module_active {
      padding: 16px;
      border: 1px solid #bda16a;
      background-color: #F5F0E3;
      color: #AF7731;
      border-radius: 8px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      margin-right: 5px;
      .info {
        width: 518px;
        padding-left: 20px;
      }
      i {
        color: #bda16a;
        font-size: 24px;
      }
    }
  }
  &--submit {
    background: #b4995a;
    border-radius: 4px;
    color: #fff;
    width: 444px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    margin: 18px auto;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
  }
}
</style>
