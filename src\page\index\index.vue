<style lang="less" scoped>
.container-fluid {
  width: 100%;
  height: 100%;
  padding: 0px;
  div {
    width: 100%;
    height: 100%;
    padding: 0px;
  }
}
</style>
<template>
  <div class='container-fluid'>
    <div>
      <v-header v-if='isHeader'></v-header>
      <v-login v-if='isLogin'></v-login>
      <v-home v-if='isHome'></v-home>
      <v-goods v-if='isGoods'></v-goods>
      <v-employee v-if='isEmployee'></v-employee>
      <v-detail v-if='isDetail'></v-detail>
      <v-stock v-if='isStock'></v-stock>
      <v-pay v-if='isPay'></v-pay>
      <v-setting v-if='isSetting'></v-setting>
      <v-memberSetting v-if='isMemberSetting'></v-memberSetting>
      <v-forgetpwd v-if='isForgetpwd'></v-forgetpwd>
      <v-register v-if='isRegister'></v-register>
      <v-chooseIndustry v-if='isChooseIndustry'></v-chooseIndustry>
      <v-addstore v-if='isAddstore'></v-addstore>
      <v-member v-if='isMember'></v-member>
      <v-report v-if='isReport'></v-report>
      <v-OnceCard v-if='isOnceCard'></v-OnceCard>
      <v-ChangeShifts v-if='isChangeShifts'></v-ChangeShifts>
      <v-ReportForms v-if='isReportForms'></v-ReportForms>
      <v-StockStatistics v-if='isStockStatistics'></v-StockStatistics>
      <v-ChangeShiftsRecord v-if='isChangeShiftsRecord'></v-ChangeShiftsRecord>
      <v-DepositRecord v-if='isDepositRecord'></v-DepositRecord>
      <v-DepositResidue v-if='isDepositResidue'></v-DepositResidue>
      <v-VipValueAnalysis v-if='isVipValueAnalysis'></v-VipValueAnalysis>
      <v-VipIncreaseStatistics v-if='isVipIncreaseStatistics'></v-VipIncreaseStatistics>
      <v-VipConsumeCollection v-if='isVipConsumeCollection'></v-VipConsumeCollection>
      <v-VipRechargeStatistics v-if='isVipRechargeStatistics'></v-VipRechargeStatistics>
      <v-VipRechargeDetail v-if='isVipRechargeDetail'></v-VipRechargeDetail>
      <v-PointsUseDetail v-if='isPointsUseDetail'></v-PointsUseDetail>
      <v-StockRecord v-if='isStockRecord'></v-StockRecord>
      <v-StockChangeDetail v-if='isStockChangeDetail'></v-StockChangeDetail>
      <v-StockCheckDetail v-if='isStockCheckDetail'></v-StockCheckDetail>
      <v-StockWarningDetail v-if='isStockWarningDetail'></v-StockWarningDetail>
      <v-OverdueWarningDetail v-if='isOverdueWarningDetail'></v-OverdueWarningDetail>
      <v-StockStatisticsReport v-if='isStockStatisticsReport'></v-StockStatisticsReport>
      <v-SupplierManage v-if='isSupplierManage'></v-SupplierManage>
      <v-SetGooodsSupplier v-if='showSettingGoodsSupplierDialog'></v-SetGooodsSupplier>
      <v-StockInventory v-if='isStockInventory'></v-StockInventory>
      <v-BuySoftware v-if='isBuySoftware'></v-BuySoftware>
      <v-ShortMessage v-if='isShortMessage'></v-ShortMessage>
      <v-MessageHistory v-if='isMessageHistory'></v-MessageHistory>
      <v-PrintSetting v-if='isPrintSetting'></v-PrintSetting>
      <v-VersionCompare v-if='isVesionCompare'></v-VersionCompare>
      <v-GoodsUnpack v-if='isGoodsUnpack'></v-GoodsUnpack>
      <v-SizeColorSetting v-if='isSizeSetting'></v-SizeColorSetting>
      <v-SizeColorTemplate v-if='isSizeTemplate'></v-SizeColorTemplate>
      <v-Guide v-if='isGuide'></v-Guide>
      <v-AliPay v-if='isAliPay'></v-AliPay>
      <v-GoodsSort v-if='isGoodsSort'></v-GoodsSort>
      <v-ClothSizeColor v-if="showSizeColor === 'new' || showSizeColor === 'edit'"></v-ClothSizeColor>
      <v-TypeManage v-if='showTypeManage'></v-TypeManage>
      <v-PcTag v-if='isTag'></v-PcTag>
      <v-ShortMessageMass v-if='isShortMessageMass'></v-ShortMessageMass>
      <v-ImportVip v-if='isImportVip'></v-ImportVip>
      <v-ScalesManage v-if="showScalesManage"></v-ScalesManage>
      <v-RepositoryNotice v-if="isRepositoryNotice"></v-RepositoryNotice>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
// 加载PC模板
import vHome from '../pc/home.vue';
import vLogin from '../pc/login.vue';
import vQKD from '../../components/QKD.vue';
import vForgetpwd from '../pc/forgetpwd.vue';
import vRegister from '../pc/register.vue';
import vChooseIndustry from '../pc/chooseIndustry.vue';
import vAddstore from '../pc/addstore.vue';
import vGoods from '../pc/goods.vue';
import vEmployee from '../pc/employee.vue';
import vDetail from '../pc/detail.vue';
import vStock from '../pc/stock.vue';
import vPay from '../pc/pay.vue';
import vMember from '../pc/member.vue';
import vReport from '../pc/report.vue';
import BuySoftware from '../../components/buy_software.vue';

import vSetting from '../pc/setting.vue';
import vSettingVip from '../pc/setting/vip/vip';

// 库存盘点(新增盘点单)
import StockInventory from '../pc/pc_add_inventory_list.vue';

// 增加货物
import vAddGoods from '../../components/pc_add_goods.vue';
// 增加/修改供应商
import vAddEditSupplier from '../../components/pc_add_edit_supplier.vue';
import vAddEmployee from '../../components/pc_add_employee.vue';
// 增加会员
import vAddMember from '../../components/pc_add_member.vue';
// 管理类别单位
// import vManageCU from '../../components/pc_manage_category_unit.vue';
// 分类管理
import vTypeManage from '../../components/pc_type_manage.vue';
// 设置会员积分商品
import vSettingMG from '../../components/pc_setting_member_goods.vue';
// 会员兑换礼品
import vMemberPE from '../../components/pc_member_points_exchange.vue';
// 会员充值
import vMemberRecharge from '../../components/pc_member_recharge.vue';
// 会员次卡
import vVipTimesCard from '../../components/pc_vip_times_card.vue';
import vFinalPay from '../../components/pc_final_pay.vue';
import vEditAd from '../../components/pc_edit_ad.vue';
// 寄件
import Mail from '../../components/pc_mail.vue';
// 取件
import PickUp from '../../components/pc_pick_up.vue';
// 次卡报表
import OnceCard from '../../components/pc_once_card.vue';
// 交接班
import ChangeShifts from '../pc/change_shifts.vue';
// 报表
import ReportForms from '../pc/report_forms.vue';
// 寄存明细
import DepositRecord from '../../components/deposit_record.vue';
// 寄存剩余
import DepositResidue from '../../components/deposit_residue.vue';
// 库存统计
import StockStatistics from '../../components/stock_statistics.vue';
// 交接班记录
import ChangeShiftsRecord from '../../components/change_shifts_record.vue';
// 会员价值分析
import VipValueAnalysis from '../../components/pc_vip_value_analysis.vue';
// 会员增长统计
import VipIncreaseStatistics from '../../components/pc_vip_increase_statistics.vue';
// 会员消费汇总
import VipConsumeCollection from '../../components/pc_vip_consume_collection.vue';
// 会员充值统计
import VipRechargeStatistics from '../../components/pc_vip_recharge_statistics.vue';
// 会员充值明细
import VipRechargeDetail from '../../components/pc_vip_recharge_detail.vue';
// 积分使用明细
import PointsUseDetail from '../../components/pc_points_use_detail.vue';
// 商品库存查询
import StockRecord from '../../components/pc_stock_report_detail.vue';
// 商品库存变动明细
import StockChangeDetail from '../../components/pc_stock_change_detail.vue';
// 商品库存盘点明细
import StockCheckDetail from '../../components/pc_stock_check_detail.vue';
// 商品库存库存预警
import StockWarningDetail from '../../components/pc_stock_warning_detail.vue';
// 商品库存保质期预警
import OverdueWarningDetail from '../../components/pc_stock_overdue_warning_detail.vue';
// 商品库存统计
import StockStatisticsReport from '../../components/pc_stock_report_statistics.vue';
// 供应商管理
import SupplierManage from '../../components/pc_supplier_manage.vue';
// 给商品设置供应商
import SetGooodsSupplier from '../../components/set_goods_supplier.vue';
// 会员设置
import vMemberSetting from '../pc/member_setting.vue';
// 商品重复编辑页面
import vExistGoods from '../../components/pc_exist_goods.vue';
// 短信购买页面
import ShortMessage from '../pc/short_message.vue';
// 短信报表页面
import MessageHistory from '../pc/message_history.vue';
// 拆包页面
import GoodsUnpack from '../pc/goods_unpack.vue';
// 规格页面
import vSizeColorSetting from '../pc/sizecolor_setting.vue';
// 规格模板
import vSizeColorTemplate from '../pc/sizecolor_template.vue';
// 短信报表页面
import PrintSetting from '../../components/pc_print_setting.vue';
// 各版本功能比较页面
import versionCompare from '../../components/pc_version_compare.vue';
// 引导页页面
import vGuide from '../../components/pc_guide.vue';
// 扫码付开通引导页
import vSmfGuide from '../pc/smf_guide.vue';
// 支付宝购买短信
import vAliPay from '../../components/pc_alipay.vue';
// 服装版
// 新增/编辑规格
import vClothSizeColor from '../../components/cloth_sizecolor.vue';
// 新版下拉管理页面
import vSelManage from '../../components/pc_select_manage.vue';
// 吊牌打印页面
import vPcTag from '../../components/pc_tag.vue';
// 短信群发页面
import vShortMessageMass from '../pc/short_message_mass.vue';
// 会员导入页面
import vImportVip from '../../components/pc_import_vip.vue';
// 传秤设置
import vScalesManage from '../pc/scales_manage.vue';
// 重复商品选择
import vRepeatGoodsChoose from '../../components/repeat_goods_choose.vue';
// 商品排序页面
import vGoodsSort from '../pc/goods_sort.vue';
// 抖音引流页面
import vRepositoryNotice from '../pc/repositoryNotice.vue';
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

// 注册模板
Vue.component(`v-QKD`, vQKD);
Vue.component(`v-login`, vLogin);
Vue.component(`v-home`, vHome);
Vue.component(`v-forgetpwd`, vForgetpwd);
Vue.component(`v-register`, vRegister);
Vue.component(`v-chooseIndustry`, vChooseIndustry);
Vue.component(`v-addstore`, vAddstore);
Vue.component(`v-goods`, vGoods);
Vue.component(`v-employee`, vEmployee);

Vue.component(`v-detail`, vDetail);
Vue.component(`v-stock`, vStock);
Vue.component(`v-pay`, vPay);
Vue.component(`v-member`, vMember);
Vue.component(`v-report`, vReport);

Vue.component(`v-setting`, vSetting);
Vue.component(`v-memberSetting`, vMemberSetting);
Vue.component(`v-SettingVip`, vSettingVip);

Vue.component(`v-ExistGoods`, vExistGoods);
Vue.component(`v-AddGoods`, vAddGoods);
Vue.component(`v-AddEmployee`, vAddEmployee);
Vue.component(`v-AddMember`, vAddMember);
Vue.component(`v-VipTimesCard`, vVipTimesCard);

// Vue.component(`v-ManageCU`, vManageCU);
Vue.component(`v-SelManage`, vSelManage);
Vue.component(`v-TypeManage`, vTypeManage);
Vue.component(`v-SettingMG`, vSettingMG);
Vue.component(`v-MemberPE`, vMemberPE);

Vue.component(`v-MemberRecharge`, vMemberRecharge);
Vue.component(`v-FinalPay`, vFinalPay);
Vue.component(`v-editAd`, vEditAd);
Vue.component(`Mail`, Mail);
Vue.component(`PickUp`, PickUp);
Vue.component(`v-OnceCard`, OnceCard);
Vue.component(`v-ChangeShifts`, ChangeShifts);
Vue.component(`v-ReportForms`, ReportForms);
Vue.component(`v-StockStatistics`, StockStatistics);
Vue.component(`v-ChangeShiftsRecord`, ChangeShiftsRecord);
Vue.component(`v-DepositRecord`, DepositRecord);
Vue.component(`v-DepositResidue`, DepositResidue);
Vue.component(`v-VipIncreaseStatistics`, VipIncreaseStatistics);
Vue.component(`v-VipValueAnalysis`, VipValueAnalysis);
Vue.component(`v-VipConsumeCollection`, VipConsumeCollection);
Vue.component(`v-VipRechargeStatistics`, VipRechargeStatistics);
Vue.component(`v-VipRechargeDetail`, VipRechargeDetail);
Vue.component(`v-PointsUseDetail`, PointsUseDetail);
Vue.component(`v-StockRecord`, StockRecord);
Vue.component(`v-StockChangeDetail`, StockChangeDetail);
Vue.component(`v-StockCheckDetail`, StockCheckDetail);
Vue.component(`v-StockWarningDetail`, StockWarningDetail);
Vue.component(`v-OverdueWarningDetail`, OverdueWarningDetail);
Vue.component(`v-StockStatisticsReport`, StockStatisticsReport);
Vue.component(`v-SupplierManage`, SupplierManage);
Vue.component(`v-SetGooodsSupplier`, SetGooodsSupplier);
Vue.component(`v-AddEditSupplier`, vAddEditSupplier);
Vue.component(`v-StockInventory`, StockInventory);
Vue.component(`v-GoodsUnpack`, GoodsUnpack);
Vue.component(`v-BuySoftware`, BuySoftware);
Vue.component(`v-ShortMessage`, ShortMessage);
Vue.component(`v-MessageHistory`, MessageHistory);
Vue.component(`v-PrintSetting`, PrintSetting);
Vue.component(`v-VersionCompare`, versionCompare);
Vue.component(`v-Guide`, vGuide);
Vue.component(`v-SmfGuide`, vSmfGuide);
Vue.component(`v-AliPay`, vAliPay);
Vue.component(`v-GoodsSort`, vGoodsSort);
Vue.component(`v-RepositoryNotice`, vRepositoryNotice);
Vue.component(`v-SizeColorSetting`, vSizeColorSetting);
Vue.component(`v-SizeColorTemplate`, vSizeColorTemplate);
// 服装版
Vue.component(`v-ClothSizeColor`, vClothSizeColor);
Vue.component(`v-PcTag`, vPcTag);
Vue.component(`v-ShortMessageMass`, vShortMessageMass);
Vue.component(`v-ImportVip`, vImportVip);
Vue.component(`v-ScalesManage`, vScalesManage);
Vue.component(`v-RepeatGoodsChoose`, vRepeatGoodsChoose);

export default {
  methods: { ...mapActions([SET_SHOW]) },
  computed: mapState({
    isHeader: state => state.show.isHeader,
    isLogin: state => state.show.isLogin,
    isHome: state => state.show.isHome,
    isGoods: state => state.show.isGoods,
    isEmployee: state => state.show.isEmployee,
    isCheck: state => state.show.isCheck,
    isDetail: state => state.show.isDetail,
    isStock: state => state.show.isStock,
    isPay: state => state.show.isPay,
    isSetting: state => state.show.isSetting,
    isForgetpwd: state => state.show.isForgetpwd,
    isRegister: state => state.show.isRegister,
    isChooseIndustry: state => state.show.isChooseIndustry,
    isAddstore: state => state.show.isAddstore,
    isMember: state => state.show.isMember,
    isMemberSetting: state => state.show.isMemberSetting,
    isReport: state => state.show.isReport,
    show_pick_up: state => state.show.show_pick_up,
    isOnceCard: state => state.show.isOnceCard,
    isChangeShifts: state => state.show.isChangeShifts,
    isReportForms: state => state.show.isReportForms,
    isStockStatistics: state => state.show.isStockStatistics,
    isChangeShiftsRecord: state => state.show.isChangeShiftsRecord,
    isVipRechargeDetail: state => state.show.isVipRechargeDetail,
    isPointsUseDetail: state => state.show.isPointsUseDetail,
    isDepositRecord: state => state.show.isDepositRecord,
    isDepositResidue: state => state.show.isDepositResidue,
    isVipRechargeStatistics: state => state.show.isVipRechargeStatistics,
    isVipConsumeCollection: state => state.show.isVipConsumeCollection,
    isVipValueAnalysis: state => state.show.isVipValueAnalysis,
    isVipIncreaseStatistics: state => state.show.isVipIncreaseStatistics,
    isStockRecord: state => state.show.isStockRecord,
    isStockChangeDetail: state => state.show.isStockChangeDetail,
    isStockCheckDetail: state => state.show.isStockCheckDetail,
    isStockWarningDetail: state => state.show.isStockWarningDetail,
    isOverdueWarningDetail: state => state.show.isOverdueWarningDetail,
    isStockStatisticsReport: state => state.show.isStockStatisticsReport,
    isSupplierManage: state => state.show.isSupplierManage,
    showSettingGoodsSupplierDialog: state => state.show.showSettingGoodsSupplierDialog,
    showAddEditSupplier: state => state.show.showAddEditSupplier,
    isGoodsUnpack: state => state.show.isGoodsUnpack,
    isStockInventory: state => state.show.isStockInventory,
    isBuySoftware: state => state.show.isBuySoftware,
    isShortMessage: state => state.show.isShortMessage,
    isMessageHistory: state => state.show.isMessageHistory,
    isPrintSetting: state => state.show.isPrintSetting,
    isVesionCompare: state => state.show.isVesionCompare,
    isGuide: state => state.show.isGuide,
    isSizeSetting: state => state.show.isSizeSetting,
    isSizeTemplate: state => state.show.isSizeTemplate,
    showSizeColor: state => state.show.showSizeColor,
    isAliPay: state => state.show.isAliPay,
    isGoodsSort: state => state.show.isGoodsSort,
    isFirstLogin: state => state.show.isFirstLogin,
    showTypeManage: state => state.show.showTypeManage,
    isTag: state => state.show.isTag,
    isShortMessageMass: state => state.show.isShortMessageMass,
    isImportVip: state => state.show.isImportVip,
    showScalesManage: state => state.show.showScalesManage,
    isRepositoryNotice: state => state.show.isRepositoryNotice
  }),
  created () {
    var result = demo.t2json(external.created());
    if (result.$storeinfo.length !== 0) {
      this.SET_SHOW({ isFirstLogin: false });
    }
    window.$clerks = result.$clerks;
    window.$setting = result.$setting;
    window.$storeinfo = result.$storeinfo;
    window.$userinfo = result.$userinfo;
    window.$config = result.$config;
  }
};
</script>
