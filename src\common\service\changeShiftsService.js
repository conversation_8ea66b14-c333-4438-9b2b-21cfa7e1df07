import dao from '../dao/dao';

const changeShiftsService = {
  /**
   * 商品销售
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  changeProductSales: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.changeProductSales.format(data), onSuccess, onFail);
  },

  /**
   * 应收现金
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  changeCashReceivable: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.changeCashReceivable.format(data), onSuccess, onFail);
  },

  /**
   * 支付统计，商品销售
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  changePayProductSales: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.changePayProductSales.format(data), onSuccess, onFail);
  },

  /**
   * 商品销售报表
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  changeProductSalesReport: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.changeProductSalesReport.format(data), onSuccess, onFail);
  },

  /**
   * 获取交接班结束时间
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getShiftHistories: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.getShiftHistories.format(data), onSuccess, onFail);
  }
};

window.changeShiftsService = changeShiftsService;
export default changeShiftsService;
