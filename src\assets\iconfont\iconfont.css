@font-face {
  font-family: "cj-icon"; /* Project id 3308639 */
  src: url('iconfont.woff2?t=1673488879843') format('woff2'),
       url('iconfont.woff?t=1673488879843') format('woff'),
       url('iconfont.ttf?t=1673488879843') format('truetype');
}

.cj-icon {
  font-family: "cj-icon", sans-serif !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.cj-icon-order-on:before {
  content: "\e6c1";
}

.cj-icon-order-off:before {
  content: "\e6c2";
}

.cj-icon-home-on-ddzg:before {
  content: "\e6bb";
}

.cj-icon-home-off-ddzg:before {
  content: "\e6bc";
}

.cj-icon-data-off-ddzg:before {
  content: "\e6bd";
}

.cj-icon-data-on-ddzg:before {
  content: "\e6be";
}

.cj-icon-mine-off-ddzg:before {
  content: "\e6bf";
}

.cj-icon-mine-on-ddzg:before {
  content: "\e6c0";
}

.cj-icon-temp:before {
  content: "\e6ba";
}

.cj-icon-print:before {
  content: "\e6b9";
}

.cj-icon-pos:before {
  content: "\e614";
}

.cj-icon-money-circle:before {
  content: "\e6b8";
}

.cj-icon-arrow-up-down-line:before {
  content: "\e6b7";
}

.cj-icon-vip:before {
  content: "\e6b5";
}

.cj-icon-zfb:before {
  content: "\e6b6";
}

.cj-icon-cloud:before {
  content: "\e6b3";
}

.cj-icon-logout:before {
  content: "\e6b4";
}

.cj-icon-checked:before {
  content: "\e6b0";
}

.cj-icon-checkbox:before {
  content: "\e6b2";
}

.cj-icon-no-checkbox:before {
  content: "\e6b1";
}

.cj-icon-no-checked:before {
  content: "\e6ae";
}

.cj-icon-radio:before {
  content: "\e6a3";
}

.cj-icon-delete:before {
  content: "\e6af";
}

.cj-icon-add:before {
  content: "\e69a";
}

.cj-icon-arrow-right:before {
  content: "\e69b";
}

.cj-icon-arrow-down:before {
  content: "\e69c";
}

.cj-icon-arrow-left:before {
  content: "\e69d";
}

.cj-icon-arrow-up:before {
  content: "\e69e";
}

.cj-icon-check:before {
  content: "\e6aa";
}

.cj-icon-subtract:before {
  content: "\e6ab";
}

.cj-icon-edit:before {
  content: "\e6ac";
}

.cj-icon-delete-bin:before {
  content: "\e6ad";
}

.cj-icon-camera:before {
  content: "\e698";
}

.cj-icon-close-circle:before {
  content: "\e69f";
}

.cj-icon-file-edit:before {
  content: "\e6a0";
}

.cj-icon-barcode:before {
  content: "\e6a1";
}

.cj-icon-qr-scan:before {
  content: "\e6a2";
}

.cj-icon-question:before {
  content: "\e6a4";
}

.cj-icon-shopping-cart:before {
  content: "\e6a5";
}

.cj-icon-search:before {
  content: "\e6a6";
}

.cj-icon-survey:before {
  content: "\e6a7";
}

.cj-icon-WeChat:before {
  content: "\e6a8";
}

.cj-icon-money:before {
  content: "\e6a9";
}

.cj-icon-eye-off:before {
  content: "\e693";
}

.cj-icon-eye-on:before {
  content: "\e694";
}

.cj-icon-scan:before {
  content: "\e691";
}

.cj-icon-message:before {
  content: "\e692";
}

.cj-icon-home-off:before {
  content: "\e68b";
}

.cj-icon-home-on:before {
  content: "\e68c";
}

.cj-icon-data-off:before {
  content: "\e68d";
}

.cj-icon-data-on:before {
  content: "\e68e";
}

.cj-icon-mine-off:before {
  content: "\e68f";
}

.cj-icon-mine-on:before {
  content: "\e690";
}

