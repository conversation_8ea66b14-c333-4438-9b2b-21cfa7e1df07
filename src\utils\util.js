const showToast = (context, msg, type = 'warning', duration = 3000) => {
  context.$message({
    showClose: true,
    message: msg,
    type: type,
    duration: duration
  });
};

/**
 * 时间戳转日期
 */
const timeStampToDate = (timestamp, format = 'yyyy-MM-dd') => {
  const date = timestamp ? new Date(timestamp.length === 10 ? timestamp * 1000 : timestamp) : new Date();
  const year = date.getFullYear() + '-';
  const month = formatZero(date.getMonth() + 1) + '-';
  const day = formatZero(date.getDate()) + ' ';
  const hours = formatZero(date.getHours()) + ':';
  const minutes = formatZero(date.getMinutes()) + ':';
  const seconds = formatZero(date.getSeconds());
  const base = year + month + day;
  switch (format) {
    case 'yyyy-MM-dd':
      return base;
    case 'yyyy-MM-dd HH:mm':
      return base + hours + minutes;
    case 'yyyy-MM-dd HH:mm:ss':
      return base + hours + minutes + seconds;
  }
}

const formatZero = time => {
  if (time < 10) {
    return '0' + time;
  } else {
    return time;
  }
}

/**
 * 计算两个日期间隔天数
 * @param {*} pre
 * @param {*} next
 * @returns
 */
const calculateDiffDays = (pre, next) => {
  const oneDay = 24 * 60 * 60 * 1000;
  const firstDate = new Date(pre);
  const secondDate = new Date(next);
  const diffDays = Math.round((secondDate - firstDate) / oneDay);
  return diffDays;
}

/**
 * 将指定id的dom元素滚动到可视范围内
 * @param {string} id dom的id
 */
const eleToView = (id, options = {
  behavior: 'smooth', block: 'end', inline: 'nearest'
}) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView(options);
  }
}

/**
 * 签名验证算法
 */
const generateSignature = (params, appSecret) => {
  // 第一步：将参与签名的参数按照键值(key)进行字典排序
  const sortedKeys = Object.keys(params).sort();
  // 第二步：将排序过后的参数，进行key和value字符串拼接
  let signString = '';
  sortedKeys.forEach(key => {
    signString += key + params[key];
  });
  // 第三步：将拼接后的字符串首尾加上appSecret秘钥，合成签名字符串
  signString = appSecret + signString + appSecret;
  // 第四步：对签名字符串进行MD5加密，生成32位的字符串
  const md5Hash = md5(signString);
  // 第五步：将签名生成的32位字符串转换为大写
  const signature = md5Hash.toUpperCase();
  return signature;
};
export {
  showToast,
  timeStampToDate,
  calculateDiffDays,
  eleToView,
  generateSignature
};
