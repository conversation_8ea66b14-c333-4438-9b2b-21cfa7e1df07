<template>
  <div class='stock_statistics_container' v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class='search_input_box' style='margin-left:10px;'
        :style="inputing_keyword ? 'border-color: #d5aa76' : 'border-color: #e3e6eb;'"
        >
        <input
          @focus='inputing_keyword = true'
          @blur='inputing_keyword = false'
          type='text'
          placeholder='请输入商品名称/条码'
          :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
          id='search_input_keyword_stock'
          v-model='keyword'
          @input="keyword = $goodsNameFormat(keyword)"
        />
        <img
          alt=""
          class='search_input_delete'
          v-show="keyword != ''"
          @click="focusInput('search_input_keyword_stock')"
          src='../image/pc_clear_input.png'
        />
      </div>
      <div class="btn_export_excel" @click="exportExcel">导出表格</div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        :empty-text="!loading ? '暂无数据' : ' '"
        stripe
        @sort-change='sortData'
      >
        <el-table-column
          prop="name"
          label="商品名称"
          width="460"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="cur_stock"
          label="现有库存"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div>{{scope.row.cur_stock ? scope.row.cur_stock : '-'}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="count"
          label="寄存数量"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div>{{scope.row.count}}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="left_stock"
          label="可售库存"
          align="center"
          sortable='custom'
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <div>{{scope.row.left_stock}}</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <div>共<span> {{total}} </span>种商品</div>
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="pagenum"
          :page-size="limit"
        >
          <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="limit"
            :currentPage.sync="pagenum"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Input, Table, TableColumn, Pagination } from 'element-ui';
import { showToast } from '@/utils/util.js';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';

export default {
  components: {
    [Input.name]: Input,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      inputing_keyword: false,
      keyword: '',
      checked: false,
      tableHeight: 0,
      tableData: [],
      pageKey: 0,
      pagenum: 1,
      limit: 10,
      total: 0,
      timer: null,
      returnGoods: [], // 接口获取的云上的寄存商品列表
      filterGoodsArr: [], // 输入条件筛选后的数据
      fingerprintArr: [],
      flag: 0, // 0:server端  1:local端
      startTime: 0
    };
  },
  created () {
    this.tableHeight = document.body.offsetHeight - 177;
    // this.limit = Math.floor((this.tableHeight - 50) / 50);
    demo.actionLog(logList.clickJiCunStatistics);
  },
  mounted () {
    this.getRecordData();
    setTimeout(() => {
      $('#search_input_keyword_stock').focus();
    }, 0);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    /**
     * 获取列表数据
     */
    getRecordData () {
      this.flag = 0;
      this.total = 0;
      this.loading = true;
      const param = {
        systemName: $config.systemName,
        phone: this.sysUid,
        sysSid: this.sysSid
      };
      demo.$http
        .post(this.$rest.getAccessProductReports, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          const res = rs.data;
          // console.log('==寄存统计', demo.t2json(JSON.stringify(res)));
          if (res.code === '0') {
            if (res.data && res.data.length > 0) {
              console.log(demo.t2json(JSON.stringify(res)), '寄存统计 res.data');
              this.total = res.data.length;
              let counts = Math.ceil(this.total / 1000);
              this.returnGoods = [];
              for (let i = 0; i < counts; i++) {
                this.getGoodsByFingerprint(res.data, i + 1).then(data => {
                  console.log('返回data', demo.t2json(JSON.stringify(data)));
                  this.returnGoods = this.returnGoods.concat(data);
                  if (i + 1 === counts) {
                    console.log('===总数据', this.returnGoods);
                    this.returnGoods.forEach(function(item) {
                      item.left_stock = Number(Number(item.left_stock).toFixed(3).replace(/[.]?0+$/, ''));
                      item.cur_stock = Number(item.cur_stock).toFixed(3).replace(/[.]?0+$/, '');
                      item.count = Number(item.count).toFixed(3).replace(/[.]?0+$/, '');
                    });
                    this.handleCurrentChange(1);
                  }
                });
              }
            }
            return;
          }
          showToast(this, res.msg);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    /**
     * 分页
     */
    handleCurrentChange (pagenum) {
      this.pagenum = pagenum;
      this.tableData = (this.flag === 0 ? this.returnGoods : this.filterGoodsArr).slice((this.pagenum - 1) * this.limit, this.pagenum * this.limit);
      console.log('要渲染的表格数据', _.clone(this.tableData));
    },
    handleSizeChange() {
      this.tableData = (this.flag === 0 ? this.returnGoods : this.filterGoodsArr).slice((this.pagenum - 1) * this.limit, this.pagenum * this.limit);
    },
    /**
     * 根据接口返回的fingerprint分批去查询商品详情(商品名,条码),每次查1000条
     */
    getGoodsByFingerprint (arr, index) {
      let newArr = arr.slice((index - 1) * 1000, index * 1000);
      return new Promise(resolve => {
        goodService.stockStatisticsReports(newArr, res => {
          resolve(demo.t2json(res));
        });
      });
    },
    /**
     * 列表排序
     */
    sortData ({ order }) {
      if (!order) {
        this.flag === 0 ? this.getRecordData() : this.getLocalData(this.keyword);
        return;
      }
      if (order === 'ascending') {
        if (this.flag === 0) {
          this.returnGoods = _.orderBy(this.returnGoods, ['left_stock'], ['asc']);
        }
        if (this.flag === 1) {
          this.filterGoodsArr = _.orderBy(this.filterGoodsArr, ['left_stock'], ['asc']);
        }
      }
      if (order === 'descending') {
        if (this.flag === 0) {
          this.returnGoods = _.orderBy(this.returnGoods, ['left_stock'], ['desc']);
        }
        if (this.flag === 1) {
          this.filterGoodsArr = _.orderBy(this.filterGoodsArr, ['left_stock'], ['desc']);
        }
      }
      this.handleCurrentChange(1);
    },
    /**
     * 总计
     */
    getSummaries ({ columns }) {
      const sums = [];
      console.log(columns, 'columns');
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计';
          return;
        }
        switch (column.property) {
          case 'num':
            sums[index] = 1;
            break;
          case 'stock':
            sums[index] = 2;
            break;
          case 'sell_stock':
            sums[index] = 3;
            break;
          default:
            break;
        }
      });
      return sums;
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'stock_statistics', action: 'reportFormLog', description});
      }
    },
    /**
     * 筛选出符合输入条件的数据
     */
    getLocalData (keyword) {
      this.filterGoodsArr = [];
      this.flag = 1;
      for (let i = 0; i < this.returnGoods.length; i++) {
        const item = this.returnGoods[i];
        if (item.name.includes(keyword) || item.code.includes(keyword)) {
          this.filterGoodsArr.push(item);
        }
      }
      this.reportFormLog({keyword: keyword}, '寄存统计商品检索');
      console.log('筛选后的数据：', _.clone(this.filterGoodsArr));
      this.total = this.filterGoodsArr.length;
      this.handleCurrentChange(1);
    },
    /**
     * 导出Excel
     */
    exportExcel() {
      let newData = this.flag === 0 ? this.returnGoods : this.filterGoodsArr;
      newData = newData.map(item => {
        return {
          ...item,
          'count': isNaN(item.count) ? item.count : Number(item.count),
          'cur_stock': isNaN(item.cur_stock) ? item.cur_stock : Number(item.cur_stock)
        }
      });
      this.reportFormLog(_.cloneDeep(newData), '寄存统计商品导出表格');
      if (newData && newData.length > 0) {
        let field_mapping = {
          商品名称: 'name',
          现有库存: 'cur_stock',
          寄存数量: 'count',
          可售库存: 'left_stock'
        };
        this.$makeExcel(newData, field_mapping, '寄存统计' + new Date().format('yyyyMMddhhmmss'));
        return;
      }
      demo.msg('warning', '暂无可导出的数据');
    }
  },
  watch: {
    /**
     * 监听输入框内容变化
     */
    keyword () {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        if (this.keyword.length > 0) {
          this.getLocalData(this.keyword);// 匹配云上返回的数据，再分页
        } else {
          this.flag = 0;
          this.total = this.returnGoods.length;
          this.handleCurrentChange(1);
        }
      }, 500);
    }
  },
  computed: mapState({
    delayedTime: state => state.show.delayedTime,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid
  })
};
</script>

<style lang='less' scoped>
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 20px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-table .descending .sort-caret.descending {
  border-top-color: @themeBackGroundColor;
}
/deep/ .el-table .ascending .sort-caret.ascending {
  border-bottom-color: @themeBackGroundColor;
}
.stock_statistics_container{
  background: #F5F8FB;
    .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .cb_container{
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 50px;
    }
    .btn_export_excel{
      cursor: pointer;
      width: 110px;
      height: 44px;
      line-height: 44px;
      margin-right: 10px;
      font-weight: bold;
      border-radius: 22px;
      text-align: center;
      color: #FFFFFF;
      font-size: 18px;
      background: @themeBackGroundColor;
    }
  }
  .table_container{
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    height: calc(100vh - 124px);
    .table_bottom{
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding-left: 10px;
      padding-right: 13px;
      background: white;
      color: @themeFontColor;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
/deep/ .el-input--suffix .el-input__inner{
  height: 44px;
  font-size: 16px;
}
</style>
