<template>
  <cj-mask :zIndex="801" :visible.sync="show">
    <div class="remainDayTips" :class="{ releaseWrap: !isFree }">
      <img v-if="trialDay > 0" class="closeIcon" src="@/image//zgzn-pos/closeIocn.png" alt="" @click.stop="handleClose" />
      <div v-if="trialDay > 0" class="title">
        您的版本将在
        <span>{{ trialDay }}</span>
        天内到期
      </div>
      <div v-else class="title">
        您的版本
        <span>已到期</span>
      </div>
      <template v-if="isFree">
        <div class="tips">可通过以下方式购买，购买后联系售后确认扫</div>
        <div class="tips">码付开通状态，以免影响正常使用。</div>
      </template>
      <div v-else class="tips">可通过以下方式购买，以免影响正常使用。</div>
      <div class="ways">
        <div class="wayItem">
          <div class="wayTitle">方式 ①</div>
          <div>{{ isFree ? '打开“淘宝”搜索购买' : '1.打开“淘宝”搜索购买，获取激活码' }}</div>
        </div>
        <img class="taobaoImg" :class="{ releaseImg: !isFree }" src="@/image//zgzn-pos/taobao.png" alt="" />
        <div v-if="!isFree" class="activationStep">
          <div class="activationTitle">2.输入激活码，进行激活</div>
          <div class="activationInput">
            <input
              v-model="activeCode"
              class="input"
              type="text"
              placeholder="请输入激活码"
              maxlength="50"
              onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
            />
            <div class="activationBtn" @click.stop="handleActivation">激活</div>
          </div>
        </div>
        <div class="wayItem">
          <div class="wayTitle">方式 ②</div>
          <div class="link" @click.stop="handleBuy">直接购买并激活→</div>
        </div>
      </div>
    </div>
  </cj-mask>
</template>
<script>
import { mapState, mapActions } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import CjMask from '@/common/components/CjMask';
export default {
  name: 'RemainDayTips',
  components: {
    CjMask
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      isFree: false,
      loading: false,
      activeCode: ''
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  computed: mapState({
    trialDay: state => state.show.trialDay,
    sys_uid: state => state.show.sys_uid
  }),
  methods: {
    ...mapActions([SET_SHOW]),
    handleClose() {
      this.$emit('update:visible', false);
    },
    // 跳转购买页面
    handleBuy() {
      this.SET_SHOW({ isVesionCompare: true });
    },
    // 激活
    handleActivation() {
      if (!this.activeCode.trim()) {
        demo.msg('warning', '请输入有效激活码');
        return;
      }
      if (this.loading) {
        return;
      }
      this.loading = true;
      this.$http
        .post(
          this.$rest.updateCode,
          {
            phone: this.sys_uid,
            activeCode: this.activeCode,
            systemName: $config.systemName,
            subName: $config.subName
          },
          {
            headers: { 'Content-Type': 'application/json' }
          }
        )
        .then(res => {
          if (res.data === undefined) {
            demo.msg('error', '无法连接远端服务器,处于离线状态');
          } else if (res.data.code === 200) {
            this.show_upgrade = false;
            demo.msg('success', '绑定激活码成功');
            demo.actionLog(logList.upgradeSuccess);
            this.handleRes(res);
          } else {
            demo.msg('warning', res.data.msg);
          }
          this.loading = false;
        })
        .catch(error => {
          this.loading = false;
          demo.msg('error', error);
        });
    },
    handleRes(res) {
      this.SET_SHOW({ period: res.data.data.period });
      this.trial_day = +res.data.data.remainDay + 1;
      this.SET_SHOW({ trialDay: this.trial_day });
      this.SET_SHOW({ sysEndDate: res.data.data.endDate });
      let ultimate = this.trial_day < 0 && !(this.period === -1) ? null : JSON.parse(res.data.data.ultimate);
      this.SET_SHOW({ ultimate: ultimate });
      this.show_trial = this.period !== -1;
      this.SET_SHOW({ showUpgrade: false });
      settingService.put(
        [
          { key: settingService.key.period, value: this.period },
          { key: settingService.key.enddate, value: res.data.data.endDate },
          { key: settingService.key.ultimate, value: ultimate }
        ],
        () => {
          // 没有插入成功回调
        }
      );
      this.SET_SHOW({ showTemporaryTip: false });
      this.handleClose();
      this.autoUpdateUser();
    },
    // 更新用户信息
    autoUpdateUser() {
      const param = {
        phone: this.sys_uid
      };
      if (pos.network.isConnected() && this.sys_uid) {
        demo.$http
          .post(this.$rest.autoUpdateUser, param, {
            headers: {
              'Content-Type': 'application/json'
            },
            timeout: 60000
          })
          .then(res => {
            if (res.data.code === 200) {
              this.SET_SHOW({ isAuto: res.data.data });
            }
          });
      }
    }
  }
};
</script>
<style lang="less" scoped>
.remainDayTips {
  width: 600px;
  height: 500px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 60px;
  background: linear-gradient(to bottom, #fffae7, #ffefc8);
  position: relative;
  .closeIcon {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 20px;
    right: 20px;
    cursor: pointer;
  }

  .title {
    font-size: 32px;
    color: #8b511b;
    margin-bottom: 24px;
    span {
      color: #ff0000;
    }
  }
  .tips {
    font-size: 20px;
    color: #8b511b;
  }
  .ways {
    background-color: #ffffff;
    width: 100%;
    flex-grow: 1;
    margin-top: 35px;
    padding: 40px 0 0 106px;
    border-radius: 20px;
    .wayItem {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 20px;
      color: #000000;
      .wayTitle {
        width: 69px;
        height: 35px;
        border-radius: 16px;
        border-width: 1px;
        font-size: 16px;
        text-align: center;
        line-height: 33px;
        margin-right: 12px;
        color: #8b511b;
        border: 1px solid #8b511b;
        background: linear-gradient(180deg, #fffae7 0%, #ffefc8 100%);
      }
      .link {
        color: @themeBackGroundColor;
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .taobaoImg {
      width: 388px;
      margin: 12px 0 48px;
    }
    .releaseImg {
      width: 286px;
      margin: 20px 0;
    }
    .activationStep {
      .activationTitle {
        font-size: 20px;
        color: #000000;
        font-weight: 600;
      }
      .activationInput {
        display: flex;
        align-items: center;
        margin: 8px 0 35px;
        .input {
          width: 284px;
          height: 52px;
          border-radius: 4px;
          background: #f5f8fb;
          font-size: 16px;
          padding-left: 12px;
          border: 1px solid;
        }
        .input::placeholder {
          color: #b2c3cd;
        }
        .activationBtn {
          width: 92px;
          height: 52px;
          border-radius: 4px;
          text-align: center;
          line-height: 50px;
          font-weight: 600;
          font-size: 18px;
          color: #ffffff;
          margin-left: 12px;
          background-color: @themeBackGroundColor;
        }
      }
    }
  }
}
.releaseWrap {
  height: 550px;
}
</style>
