import dao from '../dao/dao';

const adService = {
  adSearchEdit: function(onSuccess, onFail) {
    dao.exec(sqlApi.adSearchEdit, onSuccess, onFail);
  },

  adSearchShow: function(onSuccess, onFail) {
    dao.exec(sqlApi.adSearchShow, onSuccess, onFail);
  },

  adUpdate: function(data, onSuccess, onFail) {
    dao.exec(sqlApi.adUpdate.format(data), onSuccess, onFail);
  },

  adDelete: function(data, onSuccess, onFail) {
    dao.exec(sqlApi.adDelete.format(data), onSuccess, onFail);
  },

  adInsert: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.adInsert.format(data), onSuccess, onFail);
  },

  adTempSearch: function (onSuccess, onFail) {
    dao.exec(sqlApi.adTempSearch, onSuccess, onFail);
  },

  adSearchMobile: function(onSuccess, onFail) {
    dao.exec(sqlApi.adSearchMobile, onSuccess, onFail);
  }
};

window.adService = adService;
export default adService;
