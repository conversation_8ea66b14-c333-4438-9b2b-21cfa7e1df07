const refundParams = {
  methods: {
    getRefundParams(goodList, order, combinedItem, refundOrderId) {
      const leftGoodsList = goodList.map(good => {
        const {
          code,
          disc,
          goodFingerprint,
          goodName,
          itemDisc,
          mprice,
          price,
          purPrice,
          unitName,
          fingerprint
        } = good;
        return {
          code,
          disc,
          goodFingerprint,
          goodName,
          itemDisc,
          mprice,
          price,
          purPrice,
          amt: -good.amt,
          qty: -good.qty,
          unitName,
          fingerprint
        };
      });
      const params = {
        shouldBackMoney: Number(order.discAmt).toFixed(2),
        realBackMoney: Number(order.discAmt).toFixed(2),
        accountId: order.accountId,
        combinedItem: combinedItem,
        backParams: {
          billAmt: -order.billAmt,
          changeAmt: 0,
          deviceCode: demo.$store.state.show.devicecode,
          disc: order.disc,
          discAmt: -order.discAmt,
          localOperateTime: new Date().format('yyyy-MM-dd hh:mm:ss'),
          outTradeNo: order.code,
          oweAmt: -order.discAmt,
          payAmt: -order.discAmt,
          refundType: 1, // 退款类型0部分1整单
          saleFingerprint: order.fingerprint,
          money: order.discAmt,
          vipMobile: order.vipmobile || '',
          vipname: order.vipname || '',
          vipId: order.vipid || '',
          leftGoodsList
        }
      };
      if (refundOrderId) {
        params.backParams.refundOrderId = refundOrderId;
      }
      return params;
    }
  }
};
export default refundParams;
