<style>
</style>
<template>
  <div v-show="isAliPay">
    {{qrCode}}
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {};
  },
  methods: {
    ...mapActions([SET_SHOW])
  },
  computed: mapState({
    token: state => state.show.token,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    isAliPay: state => state.show.isAliPay
  })
};
</script>
