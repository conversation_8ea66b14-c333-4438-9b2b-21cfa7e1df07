import dao from '../dao/dao';
import { StringBuilder } from '../stringUtils';

const clerkService = {
  sql_get: sqlApi.clerk_shopusers,
  sql_get_employee: sqlApi.clerk_employees,
  sql_put: sqlApi.clerk_upsertShopusers,
  sql_up_name: sqlApi.clerk_up_name,
  sql_select_all: sqlApi.get_all_shop_users,
  sql_employee_put: sqlApi.clerk_employee_upsertShopusers,
  sql_employee_put_values: sqlApi.clerk_employee_upsertShopusers_values,

  /**
   * 查询信息
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  get: function (data, onSuccess, onFail) {
    data.employeenumber
      ? dao.exec(this.sql_get_employee.format(data), onSuccess, onFail)
      : dao.exec(this.sql_get.format(data), onSuccess, onFail);
  },

  /**
   * 更新用户名
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  upName: function (data, onSuccess, onFail) {
    dao.exec(this.sql_up_name.format(data), onSuccess, onFail);
  },

  /**
   * 清空表&添加管理员
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (data, onSuccess, onFail) {
    dao.exec(this.sql_put.format(data), onSuccess, onFail);
  },

  /**
   * 添加广告通知
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  putPoster: function (data, onSuccess, onFail) {
    let sql = '';
    data.map(item => {
      sql += sqlApi.posterValues.format(item);
    });
    dao.exec(sqlApi.insertPosters + sql.substr(0, sql.length - 5), onSuccess, onFail);
  },

  /**
   * 获取广告通知
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getPoster: function (onSuccess, onFail) {
    dao.exec(sqlApi.getPoster, onSuccess, onFail);
  },

  /**
   * 店员更新
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  employeeUpdate: function (data, onSuccess, onFail) {
    let sql = '';
    data.map(employee => {
      sql += this.sql_employee_put_values.format(employee);
    });
    sql ? dao.exec(sqlApi.clerk_employee_del + this.sql_employee_put + sql.substr(0, sql.length - 1), onSuccess, onFail) : onSuccess();
  },

  /**
   * 店员添加
   * @data {*}
   * @param {*} onSuccess
   * @param {*} onFail
   * <AUTHOR>
   */
  employeePut: function (data, onSuccess, onFail) {
    let sql = '';
    data.map(employee => {
      sql += this.sql_employee_put_values.format(employee);
    });
    sql ? dao.exec(this.sql_employee_put + sql.substr(0, sql.length - 1), onSuccess, onFail) : onSuccess();
  },

  /**
   * 获取管理员AND店员
   * @param {*} onSuccess
   * @param {*} onFail
   */
  selectAll: function (onSuccess, onFail) {
    dao.exec(this.sql_select_all, onSuccess, onFail);
  },

  /**
   * 员工下拉列表用
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getAllClerks: function (onSuccess, onFail) {
    dao.exec(sqlApi.getAllClerks, onSuccess, onFail);
  },

  /**
   * 根据 uid 获取工号
   * var datas = [{"uid":15032, "create_by":150323, "revise_by":15032}, {"uid":15348, "create_by":15348, "revise_by":15348}]
   * var colNames = ["uid", "create_by"]
   * clerkService.getEmployeeNumberByUid(datas, colNames, res => {console.log(demo.t2json(res));}, res => {console.log(demo.t2json(res));})
   * @param {*} datas : 对象数组
   * @param {*} colNames : ["uid", "create_by", "revise_by"]
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getEmployeeNumberByUid: function (datas, colNames, onSuccess, onFail) {
    dao.exec(sqlApi.get_all_shop_users, res => {
      var uidNameMap = {};
      var shopUsers = demo.t2json(res);
      shopUsers.forEach(item => {
        uidNameMap[item.uid] = item.employeeNumber === null || item.employeeNumber.trim() === '' ? '管理员' : item.employeeNumber.trim();
      });

      for (var i = 0; i < datas.length; i++) {
        var data = datas[i];

        for (var j = 0; j < colNames.length; j++) {
          var col = colNames[j];

          if (data.hasOwnProperty(col)) {
            data[col + '_no'] = uidNameMap.hasOwnProperty(data[col]) ? uidNameMap[data[col]] : data[col];
            break;
          }
        }
      }

      onSuccess(JSON.stringify(datas));
    }, onFail);
  },

  /**
   * 云端 clerks 覆盖本地
   */
  overwrite: function () {
    return new Promise((resolve, reject) => {
      demo.$http.get(demo.$rest.clerks)
        .then(res => {
          if (+res.data.code === 200) {
            const attrs = res.data.data;
            if (attrs.length === 0) {
              resolve();
              return;
            }
            const sql = new StringBuilder();
            attrs.forEach(item => {
              sql.append(sqlApi.clerksReplace.format(item));
            });
            sql.append(sqlApi.clerksSeqReset);
            dao.transaction(sql.toString(), () => {
              commonService.refreshClerks();
              resolve();
            }, reject);
          } else {
            reject(res);
          }
        })
        .catch(reject);
    });
  }
};

window.clerkService = clerkService;
export default clerkService;
