<template>
  <div
    class="pc_aso"
    style="height:100vh;"
  >
    <div class="vxImg_container">
      <!-- title -->
      <div>
        <div class="pc_title_info">
          请微信扫码，完成注册
          <div class="under-line"></div>
        </div>
        <div class="pc_aso1">
          <div class="pc_img_container">
            <img :src="vxImg" class="vx_img" />
            <div class="pc_aso2">
              打开<span>微信</span>-点击右上角“+”-点击<span>扫一扫</span>
            </div>
          </div>
          <div style="margin-left: 180px;">
            <div class="pc_aso3">
              <img src="../../image/zgzn-pos/pc_customer_service.png" />
              <div style="margin-left: 25px;">
                <div class="div_f">专属客服</div>
                <div class="div_s">咨询接待优先</div>
              </div>
            </div>
            <div class="pc_aso3" style="margin: 40px 0px;">
              <img src="../../image/zgzn-pos/pc_free_course.png" />
              <div style="margin-left: 25px;">
                <div class="div_f">免费教程</div>
                <div class="div_s">免费领取最新系统使用教程</div>
              </div>
            </div>
            <div class="pc_aso3">
              <img src="../../image/zgzn-pos/pc_free_sms.png" />
              <div style="margin-left: 25px;">
                <div class="div_f">赠送短信包</div>
                <div class="div_s">扫码就送100条免费短信</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import CryptoJS from 'crypto-js';

export default {
  components: {},
  data () {
    return {
      vxImg: '',
      client: null,
      state: null,
      zgznClientId: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getWeComQrCode() {
      /**
       * request params
       */
      let param = {
        brandName: $config.systemName,
        brandTypeName: $config.subName,
        merchantId: this.phone,
        send: 'Y'
      }
      /**
       *  response
       */
      this.$http.get(this.$rest.getWeComQrCode, {
        params: param
      })
        .then(res => {
          console.log('getWeComQrCode：', res);
          this.vxImg = res.data.qrCode;
          this.state = res.data.clientId;
          this.mqttConnect();
          console.log(this.vxImg);
        })
        .catch(() => {
          demo.msg('warning', '企业微信加载失败!');
        });
    },
    mqttConnect(flag) {
      let mqttOptions = _.cloneDeep(demo.t2json($config.mqttOptions));
      mqttOptions.keepAlive = 60;
      mqttOptions.groupId = 'GID_WECOM'
      mqttOptions.clientId = mqttOptions.groupId + '@@@' + this.state;
      mqttOptions.port = 443;
      mqttOptions.parentTopic = 'zgzn_wecom';
      mqttOptions.username = 'Signature|' + mqttOptions.accessKey + '|' + mqttOptions.instanceId;
      mqttOptions.password = CryptoJS.HmacSHA1(mqttOptions.clientId, mqttOptions.secretKey).toString(CryptoJS.enc.Base64);
      this.zgznClientId = mqttOptions.clientId;
      // 连接mqtt broker
      this.client = new Paho.MQTT.Client(mqttOptions.brokerUrl, mqttOptions.port, mqttOptions.clientId);
      console.log(mqttOptions, 'mqttOptions+++');
      console.log(this.client, '企业微信client');
      let options = {
        timeout: 6000,
        mqttVersion: 4,
        cleanSession: mqttOptions.cleanSession,
        onSuccess: () => {
          console.log('mq onSuccess');
          CefSharp.PostMessage('企业微信mqtt连接成功');
        },
        onFailure: e => {
          console.log('mq onFailure:', e);
          CefSharp.PostMessage('企业微信mqtt连接失败，errorMsg:' + e);
          if (!flag) {
            CefSharp.PostMessage('企业微信mqtt尝试三秒后重连一次');
            setTimeout(this.mqttConnect(true), 3000);
          }
        }
      }
      this.client.onMessageArrived = (message) => {
        this.receivedMessage(demo.t2json(message));
      };
      if (mqttOptions.username != null) {
        options.userName = mqttOptions.username;
        options.password = mqttOptions.password;
        options.useSSL = true;
      }
      this.client.connect(options);
      // this.receivedTest();
    },
    receivedTest() { // 测试用
      setTimeout(() => {
        this.SET_SHOW({ isAddstore: false, isLogin: true });
      }, 5000);
    },
    receivedMessage(message) {
      var topic = message.destinationName;
      CefSharp.PostMessage('mqtt zgznClientId:' + this.zgznClientId + ', topic:' + topic +
        ', payload:' + message.payloadString);
      this.SET_SHOW({ isAddstore: false, isLogin: true });
    }
  },
  mounted() {
    this.getWeComQrCode();
  },
  watch: {
    showTipsDialog() {
      if (this.showTipsDialog) {
        external.closeMainForm();
      }
    }
  },
  beforeDestroy() {
    if (this.client) {
      this.client.disconnect();
    }
  },
  computed: {
    ...mapState({
      showTipsDialog: state => state.show.showTipsDialog,
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      uid: state => state.show.uid,
      partitionId: state => state.show.partitionId,
      token: state => state.show.token,
      shopKeepName: state => state.show.shopKeepName,
      qkdMqttInfo: state => state.show.qkdMqttInfo,
      phone: state => state.show.phone,
      registerPassword: state => state.show.registerPassword
    })
  }
};
</script>

<style lang="less" scoped>
.pc_aso {
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
}
.vxImg_container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pc_title_info {
  font-size: 40px;
  font-weight: bold;
  margin-bottom: 25px;
  position: relative;
  z-index: 300;
}
.pc_aso1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pc_img_container {
  width: 280px;
  height: 303px;
  border-radius: 8px;
  box-shadow: 0px 4px 20px #ececec;
}
.vx_img {
  width: 204px;height: 204px;
  margin: 30px 38px;
}
.pc_aso2 {
  font-size: 13px;
  text-align: center;
  color: #cacaca;
  span {
    color: #2f2f2f;
  }
}
.pc_aso3 {
  height: 56px;
  display: flex;
  align-items: center;
  img {
    width: 50px;
  }
  .div_f {
    font-size: 24px;
    color: #222;
  }
  .div_s {
    color: #8E8E8E;
    font-size: 16px;
  }
}
.under-line {
  position: absolute;
  height: 10px;
  width: 154px;
  top: 52px;
  left: 41px;
  border-radius: 5px;
  background-color: @themeFocusBorderColor;
  z-index: 100;
}
</style>
