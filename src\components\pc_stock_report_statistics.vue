<style lang="less" scoped>
/deep/ .table_re .el-table .cell {
  padding-right: 30px;
}
.re_deposit_record_container {
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 50px;
    height: 44px;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-range-editor.el-input__inner {
    border-radius: 50px;
    height: 44px;
  }
  /deep/ .el-date-editor .el-range__icon {
    display: none;
    height: 44px;
  }
  /deep/ .el-range-editor .el-range-input {
    margin-left: 12px;
    color: rgba(177, 195, 205, 100);
  }
  /deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-date-editor .el-range-separator {
    color: rgba(177, 195, 205, 100);
    height: 85%;
  }
  /deep/ .el-date-table td.today span {
    color: @themeBackGroundColor !important;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    height: 50px;
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-table th > .cell {
    padding-left: 30px;
  }
  /deep/ .el-table__row > td {
    padding-left: 20px;
  }
  /deep/ .el-table td, .el-table th.is-leaf {
    padding-left: 20px;
  }
  /deep/ .el-input__inner {
    border-radius: 50px;
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  background: #F5F8FB;
  .re_top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 20px;
    .re_top_right{
      display: flex;
      align-items: center;
      .re_top_right_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 16px;
      padding: 0 30px;
      background: white;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}

.el-table .ascending .sort-caret.ascending{
  border-bottom-color: @themeBackGroundColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color: @themeBackGroundColor;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.input_goods_name .el-input--prefix .el-input__inner {
  padding-left: 30px;
  background: white;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
  background: white;
}

.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}

.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}

.el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.end-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}

.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}

.pc_btn1 {
  cursor: pointer;font-weight: 700;
  width: 130px;height: 44px;margin-left: 50px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeBackGroundColor;float: left;border: 0px;outline: none;
}
</style>
<template>
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <!-- 商品库存统计 -->
    <div class="re_deposit_record_container">
      <div>
        <div class="re_top">
          <div class="re_top_right">
            <button
              class="pc_btn1"
              @click="exportExcel">导出表格</button>
          </div>
        </div>
        <div class="re_table_container table_re">
          <el-table
            :data="stockStatistics_data"
            :height="stocks_height"
            stripe
            :empty-text="!loading ? '暂无数据' : ' '"
            :show-summary="showSum"
            :summary-method="getSummaries"
            ref="multipleTable"
          >
            <el-table-column
              prop="name"
              label="分类"
              align="left">
            </el-table-column>
            <el-table-column
              label="库存量"
              align="center">
              <template slot-scope="scope">
                {{ $toDecimalFormat(scope.row.curStockSum, 3, true) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="salePriceSum"
              label="可售总额"
              align="right">
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              prop="purPriceSum"
              label="成本总额"
              align="right">
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="goods_total"
                @current-change="goodsChange"
                :current-page="goods_pagenum"
                :page-size="goods_pageSize"
                :page-count="goods_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="handleSizeChange"
                  :pageSize.sync="goods_pageSize"
                  :currentPage.sync="goods_pagenum"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';

export default {
  data() {
    return {
      loading: false,
      pinyin: false,
      inputing_keyword: false,
      detailValue: '1',
      category_list: [],
      keywordGoods: '',
      goodssort: {},
      goods_total: 0,
      goods_pagenum: 1,
      stocks_height: $(window).height() - 310,
      pageKey: 0,
      goods_pageSize: 10,
      stockStatistics_data: [],
      multipleSelection: [],
      dateGoodStocks: '',
      loadingInstance: null,
      totalData: [],
      showSum: false,
      export_data: []
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading,
    vCjPageSize
  },
  created() {
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.searchGoodStocksMain();
    demo.actionLog(logList.pc_stock_report);
  },
  updated () {
    this.$nextTick(() => {
      this.$refs['multipleTable'].doLayout();
    });
  },
  mounted() {
    this.listenResize();
    this.searchGoodStocksMain();
  },
  watch: {
    pcDetailTab() {
      this.searchGoodStocksMain();
    },
    keywordGoods() {
      var that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.searchGoodStocksMain();
      }, that.delayedTime);
    },
    dateGoodStocks() {
      var that = this;
      if (that.dateGoodStocks) {
        that.searchGoodStocksMain();
      }
    },
    detailValue() {
      var that = this;
      that.searchGoodStocksMain();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keywordGoods = '';
      $('#' + sid).focus();
    },
    cellAlignStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    headerStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    showLoading() {
      this.loadingInstance = Loading.service({
        lock: true
      });
    },
    hideLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close();
      }
    },
    goodsChange(val) {
      this.goods_pagenum = val;
      this.searchGoodStocks();
    },
    handleSizeChange() {
      this.searchGoodStocks();
    },
    /**
     * 商品库存统计
     */
    searchGoodStocksMain() {
      this.goods_pagenum = 1;
      this.searchGoodStocks();
    },
    /**
     * 商品库存统计---列表数据
     */
    searchGoodStocks() {
      this.showSum = false;
      this.loading = true;
      let sub_data = {
        'limit': this.goods_pageSize,
        'offset': Number((this.goods_pagenum - 1) * this.goods_pageSize)
      };
      // this.reportFormLog(_.cloneDeep(sub_data), '库存统计查询');
      goodService.stockStatisticsPriceReports(sub_data, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        this.goods_total = Number(demo.t2json(res).count);
        this.stockStatistics_data = demo.t2json(res).datas.map(item => {
          return {
            ...item,
            'salePriceSum': Number(item.salePriceSum).toFixed(2),
            'purPriceSum': Number(item.purPriceSum).toFixed(2),
            'curStockSum': Number(item.curStockSum)
          };
        });
        console.log(this.stockStatistics_data, 'this.stockStatistics_data++');
        this.totalData = demo.t2json(res).total;
        this.showSum = true;
      }, () => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      })
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_report_statistics', action: 'reportFormLog', description});
      }
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : 0;
    },
    exportExcel() {
      var that = this;
      // this.reportFormLog({}, '库存统计导出表格');
      demo.actionLog(logList.exportExcel);
      goodService.stockStatisticsPriceReportsExport(res => {
        this.export_data = demo.t2json(res).map(item => {
          return {
            ...item,
            'salePriceSum': this.setMaxDecimal(item.salePriceSum, 2),
            'purPriceSum': this.setMaxDecimal(item.purPriceSum, 2),
            'curStockSum': this.$toDecimalFormat(item.curStockSum, 3)
          };
        });
        if (this.export_data.length === 0) {
          demo.msg('warning', '暂无数据');
        } else {
          console.log(this.export_data);
          const field_mapping = {
            分类: 'name',
            库存量: 'curStockSum',
            可售总额: 'salePriceSum',
            ...(!this.$employeeAuth('purchase_price') ? { 成本总额: 'purPriceSum' } : {}) // 如果判断为真，则添加属性
          };
          that.$makeExcel(this.export_data, field_mapping, '类别库存统计' + new Date().format('yyyyMMddhhmmss'));
          this.export_data = [];
        }
      });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 460;
      }, that.delayedTime);
    },
    /**
     * 总计
     */
    getSummaries ({ columns }) {
      let sums = [];
      console.log(this.showSum);
      columns.forEach((column, index) => {
        console.log(this.showSum);
        if (index === 0) {
          sums[index] = '总计';
          return;
        }
        if (index === 1) {
          sums[index] = this.$toDecimalFormat(this.totalData.curStockAmtTotal, 3)
          return;
        }
        if (index === 2) {
          sums[index] = Number(this.totalData.salePriceAmtTotal).toFixed(2);
          return;
        }
        if (index === 3) {
          sums[index] = Number(this.totalData.purPriceAmtTotal).toFixed(2);
        }
      });
      return sums;
    }
  },
  computed: mapState({
    pcDetailTab: state => state.show.pc_detail_tab,
    sysUid: state => state.show.sys_uid,
    delayedTime: state => state.show.delayedTime
  }),
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
    this.hideLoading();
  }
};
</script>
