import _ from 'lodash';
import logList from "../../config/logList";
import rest from '../../config/rest';
import dao from '../dao/dao';
import stringUtils from '../stringUtils';
import commonService from './commonService';
import { calc } from 'frontend-utils';

const saleService = {
  /**
   * 销售排行
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  ranking: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.sale_ranking.format(`'${data.from}'`, `'${data.to}'`, data.ranktype), onSuccess, onFail);
  },

  /**
   * 销售额、预估利润
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  profit: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.sale_profit.format(`'${data.from}'`, `'${data.to}'`), onSuccess, onFail);
  },

  /**
   * 销售额、预估利润
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  profitAmt: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.sale_profit_sumAmt.format(`'${data.from}'`, `'${data.to}'`), onSuccess, onFail);
  },

  getSaleWhereTotal: function (params) {
    const where = {};
    let wheresGoods = '';
    let wheresZhiJie = '';
    let wheresTotal = '';
    let whereRefundCodes = '';
    let whereSaleCode = '';
    let whereRefundQty = '';
    if (!demo.isNullOrTrimEmpty(params.from)) {
      wheresTotal += `and sales.create_at>= '${params.from}' `;
      whereRefundCodes = `AND r.create_at >= '${params.from}' `;
      whereSaleCode += `AND s.create_at >= '${params.from}' `;
      whereRefundQty = `AND refund.create_at >= '${params.from}' `;
    }
    if (!demo.isNullOrTrimEmpty(params.to)) {
      wheresTotal += `and sales.create_at<= '${params.to}'`;
      whereSaleCode += `and s.create_at <= '${params.to}' `
    }
    if (params.hasOwnProperty('inOut')) {
      const inOut = Number(params.inOut);
      if ([1, 2].indexOf(inOut) > -1) {
        wheresTotal += `and sales.in_out= '${params.inOut}' `;
      }
      if (inOut === 3) {
        wheresTotal += `AND EXISTS (
          SELECT 1 
          FROM sales r
           WHERE r.refund_fingerprint = sales.fingerprint 
            AND r.in_out = '2'
           ) `;
      }
    };
    if (params.hasOwnProperty('accountId') && +params.accountId !== 0) {
      wheresTotal += `and sales.account_id= '${params.accountId}' `;
    }
    if (params.employee) {
      wheresTotal += `and sales.uid= '${params.employee}' `;
    }
    if (!demo.isNullOrTrimEmpty(params.keyword)) {
      params.keyword = params.keyword.replace(/'/g, '‘').replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      wheresGoods += `and (goods.name like '%${params.keyword}%' ESCAPE '/' or
                            goods.pinyin like '%${params.keyword}%' ESCAPE '/' or
                            goods_ext_barcode.ext_barcode like '%${params.keyword}%' ESCAPE '/' or
                            goods.code like '%${params.keyword}%' ESCAPE '/' or
                            goods.first_letters like '%${params.keyword}%' ESCAPE '/') `;
      wheresZhiJie += ` and (name like '%${params.keyword}%' or code like '%${params.keyword}%')`;
    }
    if (!demo.isNullOrTrimEmpty(params.vip)) {
      params.vip = params.vip.replace(/'/g, '‘');
      wheresTotal += `and (sales.vipname like '%${params.vip}%' or sales.vipmobile like '%${params.vip}%'
                            or sales.remark like '%${params.vip}%' or sales.code like '%${params.vip}%' )`;
    }
    where.wheresTotal = wheresTotal;
    where.wheresGoods = wheresGoods;
    where.wheresZhiJie = wheresZhiJie;
    where.whereRefundCodes = whereRefundCodes;
    where.whereSaleCode = whereSaleCode;
    where.whereRefundQty = whereRefundQty;
    return where;
  },
  getLastreFundInfoBySaleFingerPrint(fingerPrint, onSuccess, onFail) {
    if (fingerPrint) {
      dao.exec(`SELECT *
      FROM sales
      WHERE refund_fingerprint = '${fingerPrint}'
      ORDER BY create_at DESC
      LIMIT 1;`, onSuccess, onFail)
    } else {
      onFail('参数不可为空')
    }
  },
  /**
   * 销售明细查询
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  search: function (params, onSuccess, onFail) {
    const wheres = this.getSaleWhereTotal(params);
    let wheresDayTotal = wheres.wheresTotal;
    let wheresDayDetails = wheres.wheresTotal;
    let saleGetTotal, saleGetDayTotal, saleGetDetails;
    if (demo.isNullOrTrimEmpty(params.keyword)) {
      saleGetTotal = sqlApi.saleGetTotal;
      saleGetDayTotal = sqlApi.saleGetDayTotalWithGoods;
      saleGetDetails = sqlApi.saleGetDetails;
    } else {
      saleGetTotal = sqlApi.saleGetTotalWithGoods;
      saleGetDayTotal = sqlApi.saleGetDayTotalWithGoods;
      saleGetDetails = sqlApi.saleGetDetailsWithGoods;
    }

    let saleGetTotalSql = new Promise((resolve, reject) => {
      // 是点击查询按钮则查询统计数据否则不查询
      let isSearch = params.isSearch;
      if (demo.isNullOrTrimEmpty(isSearch) || +isSearch === 1) {
        dao.exec(saleGetTotal.format(wheres.wheresTotal, wheres.wheresGoods, wheres.wheresZhiJie), res => resolve(res), err => reject(err));
      } else {
        resolve();
      }
    });

    let saleGetDetailsSql = new Promise((resolve, reject) => {
      const wheresOrderByDay = ' order by sales.create_at desc ';
      dao.exec(saleGetDetails.format(wheresDayDetails, wheresOrderByDay, wheres.wheresGoods, wheres.wheresZhiJie, params.offset, params.pageSize), detailsData => {
        if (detailsData.length > 0) {
          // 区间内每天销售的销售金额总数
          let dateTo = detailsData[0].optDate;
          let dateFrom = detailsData[detailsData.length - 1].optDate;
          // 做分页(区间内每天销售的销售金额总数)根据list查询结果的最大时间和最小时间做查询时间条件
          wheresDayTotal += `and opt_date BETWEEN '${dateFrom}' and '${dateTo}'`;
          dao.exec(saleGetDayTotal.format(wheresDayTotal, wheres.wheresGoods, wheres.wheresZhiJie), getDayTotalData => resolve([detailsData, getDayTotalData]), err => reject(err));
        } else {
          resolve([])
        }
      }, err => reject(err));
    });

    Promise.all([saleGetTotalSql, saleGetDetailsSql]).then((result) => {
      let totalRes = demo.isNullOrTrimEmpty(demo.t2json(result)[0]) ? {} : demo.t2json(result)[0][0];
      let dayTotalObj = demo.t2json(result)[1][1];
      let dayTotalRes = demo.isNullOrTrimEmpty(dayTotalObj) ? [] : dayTotalObj;
      let dayDetailsObj = demo.t2json(result)[1][0];
      let dayDetailsRes = demo.isNullOrTrimEmpty(dayDetailsObj) ? {} : _.groupBy(dayDetailsObj, 'optDate');
      for (let dayTotal of dayTotalRes) {
        dayTotal.details = dayDetailsRes[dayTotal.optDate];
      }
      totalRes.days = dayTotalRes;
      onSuccess(totalRes);
    }).catch((error) => {
      onFail(error)
    })
  },
  getsaleStatusByFingerprint(fingerprint, onSuccess, onFail) {
    if (fingerprint) {
      dao.exec(sqlApi.saleGetDetails.format(`add sales.fingerprint = '${fingerprint}'`, '', '', '', 0, 1), onSuccess, onFail)
    } else {
      onFail();
    }
  },
  /**
   * 获取某条销售明细的详细信息
   * @param {*} fingerprint
   * @param {*} onSuccess
   * @param {*} onFail
   */
  detail: function (id, fingerprint, onSuccess, onFail) {
    var result = {};
    dao.exec(
      sqlApi.saleDetail.format(fingerprint),
      function (res) {
        result.sales = demo.t2json(res);
        dao.exec(
          sqlApi.saleItemDetail.format(fingerprint),
          function (res1) {
            result.saleitems = specsService.specStringToArray(res1);
            if (result.sales.length > 0 && +result.sales[0].accountId === 99) {
              dao.exec(sqlApi.sales_byday_select.format(id), res2 => {
                result.zuhe = demo.t2json(res2);
                onSuccess(result);
              }, onFail);
            } else {
              onSuccess(result);
            }
          },
          onFail
        );
      },
      onFail
    );
  },
  // 根据订单号获取订单详情
  getOrderByCode: function (code, onSuccess, onFail) {
    dao.exec(sqlApi.saleDetailByCode.format(code), (res) => {
      if (res.length) {
        const { id, fingerprint } = res[0];
        this.detail(id, fingerprint, onSuccess, onFail);
      }
    })
  },
  /**
   * 商品拆包列表
   * saleService.getPackingGoodsList('', res => {console.log(res)})
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getPackingGoodsList: function (params, onSuccess, onFail) {
    if (typeof (params) !== 'string') {
      onFail('参数应该是字符串类型');
      return;
    }
    var sql = sqlApi.getPackingGoodsList;
    dao.exec(sql, res => {
      var goods = demo.t2json(res);
      if (goods.length === 0) {
        onSuccess(goods);
        return;
      }

      var parentGoods = [];
      var childGoods = [];
      goods.forEach(good => {
        var packing = demo.t2json(good.packing);
        if (!demo.isNullOrTrimEmpty(packing.parent)) {
          var parent = packing.parent;
          good.parentFingerprint = parent.fingerprint;
          good.count = parent.count;
          childGoods.push(good);
        }
        if (!demo.isNullOrTrimEmpty(packing.children)) {
          var child = packing.children[0];
          good.childFingerprint = child.fingerprint;
          parentGoods.push(good);
        }
      });

      parentGoods.forEach(good => {
        var childGood = _.find(childGoods, { 'fingerprint': good.childFingerprint });
        if (!demo.isNullOrTrimEmpty(childGood) && good.fingerprint === childGood.parentFingerprint) {
          good.childName = childGood.goodName;
          good.childCode = childGood.code;
          good.childExtBarcode = childGood.extBarcode;
          good.childFirstLetters = childGood.firstLetters;
          good.packingCount = childGood.count;
        }
      });

      _.remove(parentGoods, good => {
        return !good.hasOwnProperty('childName');
      });

      if (!demo.isNullOrTrimEmpty(params)) {
        parentGoods = this.getPackingGoodsListWheres(parentGoods, params);
      }

      parentGoods = _.orderBy(parentGoods, 'fingerprint');
      onSuccess(parentGoods);
    }, onFail);
  },

  getPackingGoodsListWheres: function (goods, params) {
    return _.filter(goods, good => {
      return (!demo.isNullOrTrimEmpty(good.name) && good.name.includes(params)) ||
        (!demo.isNullOrTrimEmpty(good.firstLetters) && good.firstLetters.toLowerCase().includes(params.toLowerCase())) ||
        (!demo.isNullOrTrimEmpty(good.code) && good.code.toLowerCase().includes(params.toLowerCase())) ||
        (!demo.isNullOrTrimEmpty(good.childName) && good.childName.includes(params)) ||
        (!demo.isNullOrTrimEmpty(good.childFirstLetters) && good.childFirstLetters.toLowerCase().includes(params.toLowerCase())) ||
        (!demo.isNullOrTrimEmpty(good.extBarcode) && good.extBarcode.toLowerCase().includes(params.toLowerCase())) ||
        (!demo.isNullOrTrimEmpty(good.childExtBarcode) && good.childExtBarcode.toLowerCase().includes(params.toLowerCase())) ||
        (!demo.isNullOrTrimEmpty(good.childCode) && good.childCode.toLowerCase().includes(params.toLowerCase()));
    });
  },

  /**
   * 拆包功能-检测
   * @param {*} obj
   * @param {*} onSuccess
   * @param {*} onFail
   */
  packingSaveCheck: function (obj, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(obj.parent) || demo.isNullOrTrimEmpty(obj.child) || (+obj.isDel !== 1 && +obj.count <= 0)) {
      onFail('请求参数不正确');
      return;
    }

    var parentFingerprint = obj.parent;
    var childFingerprint = obj.child;
    if (parentFingerprint === childFingerprint) {
      onFail('父商品与子商品不能为同一种商品');
      return;
    }

    var fingerprints = [];
    fingerprints.push('\'' + parentFingerprint + '\'');
    fingerprints.push('\'' + childFingerprint + '\'');
    dao.exec(sqlApi.getGoodsByFingerprints.format(fingerprints.join()), async goods => {
      if (goods.length < fingerprints.length) {
        onFail('请求的商品不存在');
        return;
      }
      if (+obj.isDel !== 1) {
        var delGoods = _.filter(goods, { 'is_deleted': 1 });
        if (delGoods.length > 0) {
          onFail('请求的商品已被删除');
          return;
        }
      }
      var isExists = await this.goodsPackingCheck(goods, parentFingerprint, childFingerprint);
      if (isExists === 1) {
        onFail('商品已存在其它换算关系，请重新选择');
        return;
      }

      onSuccess(goods);
    }, onFail);
  },

  goodsPackingCheck: async function (goods, parentFingerprint, childFingerprint) {
    var parent = _.find(goods, { fingerprint: parentFingerprint });
    var child = _.find(goods, { fingerprint: childFingerprint });
    var fingerprints = [];
    if (!demo.isNullOrTrimEmpty(parent.packing)) {
      var parentPacking = JSON.parse(parent.packing);
      var gChildren = parentPacking.children;
      if (!demo.isNullOrTrimEmpty(gChildren) && gChildren[0].fingerprint !== childFingerprint) {
        fingerprints.push('\'' + gChildren[0].fingerprint + '\'');
      }
    }
    if (!demo.isNullOrTrimEmpty(child.packing)) {
      var childPacking = JSON.parse(child.packing);
      var gParent = childPacking.parent;
      if (!demo.isNullOrTrimEmpty(gParent) && gParent.fingerprint !== parentFingerprint) {
        fingerprints.push('\'' + gParent.fingerprint + '\'');
      }
    }

    if (fingerprints.length === 0) {
      return;
    }

    var columns = `name, is_deleted, fingerprint`;
    var wheres = `where fingerprint in (${fingerprints.join()}) and is_deleted = 0`;
    var orderBy = '';
    var limit = '';
    var goods1 = await dao.asyncExec(sqlApi.goodsSelectWheres.format({ columns, wheres, orderBy, limit }));
    if (demo.t2json(goods1).length > 0) {
      return 1;
    }
  },

  /**
   * 拆包功能-商品换算关系
   * var obj = {
   *  parent: '612d738f5ee9090b3b22a4490f68aab3',
   *  child: '29f3f193a809cf0333ea7b7b2b9fa7e6',
   *  count: 10,
   *  isDel: 1
   * }
   * @param {*} obj
   * @param {*} onSuccess
   * @param {*} onFail
   */
  packingSave: function (obj, onSuccess, onFail) {
    this.packingSaveCheck(obj, goods => {
      var count = +obj.count;
      var parentFingerprint = obj.parent;
      var childFingerprint = obj.child;

      var parentGood;
      var childGood;
      goods.forEach(good => {
        if (good.fingerprint === parentFingerprint) {
          parentGood = good;
        } else if (good.fingerprint === childFingerprint) {
          childGood = good;
        } else {
          // do nothing
        }
      });

      var allParams = {};
      allParams.parentGood = parentGood;
      allParams.childGood = childGood;
      allParams.parentFingerprint = parentFingerprint;
      allParams.childFingerprint = childFingerprint;
      this.getPacking(allParams, parentGood, childGood);
      var pgPacking = allParams.pgPacking;
      var cgPacking = allParams.cgPacking;
      var parentChildFingerprint = allParams.parentChildFingerprint;
      var childParentFingerprint = allParams.childParentFingerprint;

      if (+obj.isDel === 1) {
        this.deleteGoodsPackingByFingerprint(allParams, onSuccess, onFail);
        return;
      }

      var params = [];
      var param;

      pgPacking.children = [];
      var child = {};
      child.fingerprint = childFingerprint;
      pgPacking.children.push(child);
      param = {};
      param.fingerprint = parentGood.fingerprint;
      param.packing = JSON.stringify(pgPacking);
      params.push(param);

      cgPacking.parent = {};
      cgPacking.parent.fingerprint = parentFingerprint;
      cgPacking.parent.count = count;
      param = {};
      param.fingerprint = childGood.fingerprint;
      param.packing = JSON.stringify(cgPacking);
      params.push(param);

      if ((demo.isNullOrTrimEmpty(parentChildFingerprint) || parentChildFingerprint === childFingerprint) &&
        (demo.isNullOrTrimEmpty(childParentFingerprint) || childParentFingerprint === parentFingerprint)) {
        this.execUpdateGoodsPackingByFingerprint(params, onSuccess, onFail);
        return;
      }

      this.updateGoodsPackingByFingerprint(params, allParams, onSuccess, onFail);
    }, onFail);
  },

  /**
   * 获取 pgPacking、parentChildFingerprint、cgPacking、childParentFingerprint
   * @param {*} allParams
   * @param {*} parentGood
   * @param {*} childGood
   */
  getPacking: function (allParams, parentGood, childGood) {
    if (!demo.isNullOrTrimEmpty(parentGood.packing)) {
      var pgPacking = demo.t2json(parentGood.packing);
      var pgChildren = pgPacking.children;
      if (!demo.isNullOrTrimEmpty(pgChildren) && !demo.isNullOrTrimEmpty(pgChildren[0])) {
        allParams.parentChildFingerprint = pgChildren[0].fingerprint;
      }
      if (!demo.isNullOrTrimEmpty(pgPacking.parent) && pgPacking.parent.fingerprint === allParams.childFingerprint) {
        delete pgPacking.parent;
      }

      allParams.pgPacking = pgPacking;
    } else {
      allParams.pgPacking = {};
    }

    if (!demo.isNullOrTrimEmpty(childGood.packing)) {
      var cgPacking = demo.t2json(childGood.packing);
      var cgChildren = cgPacking.children;
      if (!demo.isNullOrTrimEmpty(cgPacking.parent)) {
        allParams.childParentFingerprint = cgPacking.parent.fingerprint;
      }
      if (!demo.isNullOrTrimEmpty(cgChildren) && !demo.isNullOrTrimEmpty(cgChildren[0]) && cgChildren[0].fingerprint === allParams.parentFingerprint) {
        delete cgPacking.children;
      }

      allParams.cgPacking = cgPacking;
    } else {
      allParams.cgPacking = {};
    }
  },

  /**
   * 删除拆包对应关系
   * @param {*} allParams
   * @param {*} onSuccess
   * @param {*} onFail
   */
  deleteGoodsPackingByFingerprint: function (allParams, onSuccess, onFail) {
    var params = [];
    var param;
    if (allParams.parentChildFingerprint === allParams.childFingerprint) {
      delete allParams.pgPacking.children;
      param = {};
      param.fingerprint = allParams.parentGood.fingerprint;
      param.packing = JSON.stringify(allParams.pgPacking);
      params.push(param);
    }
    if (allParams.childParentFingerprint === allParams.parentFingerprint) {
      delete allParams.cgPacking.parent;
      param = {};
      param.fingerprint = allParams.childGood.fingerprint;
      param.packing = JSON.stringify(allParams.cgPacking);
      params.push(param);
    }

    this.execUpdateGoodsPackingByFingerprint(params, onSuccess, onFail);
  },

  /**
   * 更新拆包对应关系
   * @param {*} params
   * @param {*} allParams
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateGoodsPackingByFingerprint: function (params, allParams, onSuccess, onFail) {
    var fingerprints = [];
    if (!demo.isNullOrTrimEmpty(allParams.parentChildFingerprint) && allParams.parentChildFingerprint !== allParams.childFingerprint) {
      fingerprints.push('\'' + allParams.parentChildFingerprint + '\'');
    }
    if (!demo.isNullOrTrimEmpty(allParams.childParentFingerprint) && allParams.childParentFingerprint !== allParams.parentFingerprint) {
      fingerprints.push('\'' + allParams.childParentFingerprint + '\'');
    }
    fingerprints = [...new Set(fingerprints)];

    if (fingerprints.length === 0) {
      this.execUpdateGoodsPackingByFingerprint(params, onSuccess, onFail);
      return;
    }

    this.updateGoodsPackingByFingerprintSecond(fingerprints, params, allParams, onSuccess, onFail);
  },

  updateGoodsPackingByFingerprintSecond: function (fingerprints, params, allParams, onSuccess, onFail) {
    var param;
    dao.exec(sqlApi.getGoodsByFingerprints.format(fingerprints.join()), res1 => {
      var goods1 = demo.t2json(res1);
      goods1.forEach(good => {
        if (demo.isNullOrTrimEmpty(good.packing)) {
          return;
        }
        var goodPacking = demo.t2json(good.packing);
        var goodPackingParent = goodPacking.parent;
        var goodPackingChildren = goodPacking.children;
        if (good.fingerprint === allParams.parentChildFingerprint && !demo.isNullOrTrimEmpty(goodPackingParent) &&
          goodPackingParent.fingerprint === allParams.parentFingerprint) {
          delete goodPacking.parent;
        }
        if (good.fingerprint === allParams.childParentFingerprint && !demo.isNullOrTrimEmpty(goodPackingChildren) &&
          !demo.isNullOrTrimEmpty(goodPackingChildren[0]) && goodPackingChildren[0].fingerprint === allParams.childFingerprint) {
          delete goodPacking.children;
        }

        param = {};
        param.fingerprint = good.fingerprint;
        param.packing = JSON.stringify(goodPacking);
        params.push(param);
      });

      this.execUpdateGoodsPackingByFingerprint(params, onSuccess, onFail);
    }, onFail);
  },

  /**
   * 拆包关系保存
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  execUpdateGoodsPackingByFingerprint: function (params, onSuccess, onFail) {
    var sql = '';
    params.forEach(param => {
      sql += sqlApi.updateGoodsPackingByFingerprint.format(demo.sqlConversion(param));
    });
    dao.transaction(sql, onSuccess, onFail);
  },

  /**
   * 收银台结算
   * params.saleWithoutStockCheck=1:检查库存，是否可以正常销售。返回结果：1-可以销售；0-不可以销售
   * params.saleWithoutStockCheck!=1:结算
   * saleWithoutStock:0-不允许无库存销售；1-允许无库存销售
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  settlement: function (params, onSuccess, onFail) {
    CefSharp.PostMessage('saleService.settlement--args: ' + JSON.stringify(params));

    var saleItems = params.saleItems;
    var saleItemsLength = saleItems.length;
    if (saleItemsLength === 0) {
      onFail('销售明细不能为空');
      return;
    }

    params.uid = demo.$store.state.show.loginInfo.uid;
    var sql = '';
    _.forEach(saleItems, (item, index) => {
      sql += sqlApi.getGoodsAfterUpdateStock.format(item);
      if (index < saleItemsLength - 1) {
        sql += ' union all ';
      }
    });
    dao.exec(sql, allGoods => {
      var notEnoughGoods = _.filter(allGoods, good => {
        return good.leftStock < 0;
      });

      if (+params.saleWithoutStockCheck === 1) {
        this.settlementCheck(params, allGoods, notEnoughGoods, onSuccess, onFail);
      } else {
        this.settlementDo(params, allGoods, notEnoughGoods, onSuccess, onFail);
      }
    }, onFail);
  },

  settlementCheck: function (params, allGoods, notEnoughGoods, onSuccess, onFail) {
    if (notEnoughGoods.length === 0) {
      onSuccess(1);
    } else if (+params.autoPacking === 1 || +params.saleWithoutStock === 1) {
      this.dealWithPacking(params, allGoods, notEnoughGoods, onSuccess, onFail);
    } else {
      onSuccess(0);
    }
  },

  settlementDo: function (params, allGoods, notEnoughGoods, onSuccess, onFail) {
    if (notEnoughGoods.length === 0) {
      this.doSettlement(params, onSuccess, onFail);
    } else if (+params.autoPacking === 1) {
      this.dealWithPacking(params, allGoods, notEnoughGoods, onSuccess, onFail);
    } else if (+params.saleWithoutStock === 1) {
      this.doSettlement(params, onSuccess, onFail);
    } else {
      onFail('商品库存不足');
    }
  },

  dealWithPacking: function (params, allGoods, notEnoughGoods, onSuccess, onFail) {
    for (let good of notEnoughGoods) {
      var packing = good.packing;
      let parent;
      if (!demo.isNullOrTrimEmpty(packing)) {
        parent = demo.t2json(packing).parent;
      }
      if (!demo.isNullOrTrimEmpty(parent) && !demo.isNullOrTrimEmpty(parent.fingerprint) && +parent.count > 0) {
        good.parentFingerprint = parent.fingerprint;
        good.parentFingerprintMark = '\'' + parent.fingerprint + '\'';
        good.count = parent.count;
      }
    }

    var notEnoughGoodsWithParent = _.filter(notEnoughGoods, neGood => {
      return +neGood.count > 0;
    });
    if (notEnoughGoodsWithParent.length < notEnoughGoods.length && +params.saleWithoutStock === 0) {
      if (+params.saleWithoutStockCheck === 1) {
        onSuccess(0);
        return;
      }
      onFail('商品库存不足');
      return;
    }
    if (notEnoughGoodsWithParent.length === 0 && +params.saleWithoutStockCheck !== 1) {
      this.doSettlement(params, onSuccess, onFail);
      return;
    }

    this.dealWithPackingSub(params, allGoods, notEnoughGoodsWithParent, onSuccess, onFail);
  },

  dealWithPackingSub: function (params, allGoods, notEnoughGoodsWithParent, onSuccess, onFail) {
    var notEnoughGoodsFingerprintMarks = _.map(notEnoughGoodsWithParent, 'parentFingerprintMark');
    dao.exec(sqlApi.getParentGoods.format(notEnoughGoodsFingerprintMarks.join()), res1 => {
      var parentGoods = demo.t2json(res1) || [];
      // 允许无库存销售
      if (+params.saleWithoutStock === 1) {
        this.dealWithPackingSubWithoutStock(params, allGoods, notEnoughGoodsWithParent, parentGoods, onSuccess, onFail);
        return;
      }
      // 存在库存不足商品
      if (notEnoughGoodsWithParent.length > 0) {
        // 允许自动拆包
        let flg = this.enoughToPacking(params, allGoods, notEnoughGoodsWithParent, onSuccess, parentGoods);
        if (!flg) {
          return;
        }
      } else {
        onSuccess(1);
      }

      this.dealWithPackingSubWithoutStock(params, allGoods, notEnoughGoodsWithParent, parentGoods, onSuccess, onFail);
    }, onFail);
  },
  dealWithPackingSubWithoutStock(params, allGoods, notEnoughGoodsWithParent, parentGoods, onSuccess, onFail) {
    if (!params.saleWithoutStockCheck) {
      var goods = this.getPackingGoods(allGoods, notEnoughGoodsWithParent, parentGoods);
      if (goods.length === 0) {
        this.doSettlement(params, onSuccess, onFail);
      } else {
        this.packing(params, goods, onSuccess, onFail);
      }
    }
  },
  enoughToPacking: function (params, allGoods, notEnoughGoodsWithParent, onSuccess, parentGoods) {
    if (+params.autoPacking === 1) {
      var isFull = true;
      for (let pGoods of parentGoods) {
        var pCurStock = pGoods.curStock;
        var cGoods = _.find(notEnoughGoodsWithParent, function (n) { return n.parentFingerprint === pGoods.fingerprint; });
        // 当前购物车内 被拆包商品
        var tParentGoods = _.find(allGoods, function (n) { return n.fingerprint === pGoods.fingerprint; }) || { qty: 0 };
        var pc = JSON.parse(cGoods.packing).parent.count;
        // (当前被拆包商品库存 - 购物车内被拆包商品数量) * 拆包比例 + 商品数量差额
        if ((pCurStock - tParentGoods.qty) * pc + cGoods.leftStock < 0) {
          isFull = false;
          break;
        }
      }
      if (isFull) {
        // 拆包后库存足够
        if (+params.saleWithoutStockCheck === 1) {
          onSuccess(1);
        }
        return true;
      }

      // 拆包后库存不足
      onSuccess(2);
      return false;
    } else {
      onSuccess(0);
      return false;
    }
  },
  /**
   * 获取拆包的商品及父商品
   * @param {*} notEnoughGoods
   * @param {*} parentGoods
   * @returns
   */
  getPackingGoods: function (allGoods, notEnoughGoodsWithParent, parentGoods) {
    var goods = [];
    for (let good of notEnoughGoodsWithParent) {
      var packingCount = Math.abs(good.leftStock);
      if (packingCount > +good.qty) {
        packingCount = +good.qty;
      }
      var packingParentCounts = Math.ceil(packingCount / good.count);

      var parentGood =
        _.find(allGoods, function (item) { return item.fingerprint === good.parentFingerprint && +item.is_deleted === 0; }) ||
        _.find(parentGoods, function (item) { return item.fingerprint === good.parentFingerprint; });

      if (!demo.isNullOrTrimEmpty(parentGood)) {
        var parentStock = isNaN(parentGood.leftStock) ? parentGood.curStock : parentGood.leftStock;
        packingParentCounts = Math.min(packingParentCounts, Math.floor(parentStock));
        if (packingParentCounts > 0) {
          parentGood.goods_id = parentGood.id;
          parentGood.pick_up_num = -packingParentCounts;
          goods.push(parentGood);
          good.goods_id = good.id;
          good.pick_up_num = good.count * packingParentCounts;
          goods.push(good);
        }
      }
    }
    return goods;
  },

  /**
   * 拆包：做盘点单
   * @param {*} params
   * @param {*} goods
   * @param {*} onSuccess
   * @param {*} onFail
   */
  packing: function (params, goods, onSuccess, onFail) {
    orderService.get({
      type: 'PDD'
    }, res => {
      if (demo.isNullOrTrimEmpty(res) || demo.isNullOrTrimEmpty(res[0].code)) {
        onFail('盘点单号生成失败');
        return;
      }

      var inventoryItems = _.cloneDeep(goods);
      inventoryItems.forEach(item => {
        item.accountQty = +item.curStock;
        item.actualQty = item.accountQty + item.pick_up_num;
        item.price = item.purPrice;
        item.goodFingerprint = item.fingerprint;
      });

      var params1 = {};
      params1.exec = 0;
      params1.uid = demo.$store.state.show.loginInfo.uid;
      params1.code = res[0].code;
      params1.updateStock = 1;
      params1.updatePurPrice = 0;
      params1.dealType = 8;
      params1.remark = '拆包';
      params1.inventoryItems = inventoryItems;
      inventoryService.insert(params1, res3 => {
        params.initSql = res3;
        this.doSettlement(params, onSuccess, onFail);
      }, onFail);
    }, onFail);
  },

  doSettlement: function (params, onSuccess, onFail) {
    if (+params.accountId === 99) {
      this.blendSettlement(params, onSuccess, onFail);
    } else {
      this.singleSettlement(params, onSuccess, onFail);
    }
  },

  /**
   * 非组合支付
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  singleSettlement: function (params, onSuccess, onFail) {
    var listLog = [];
    listLog[0] = params.code;
    let obj = _.cloneDeep(logList.sales);
    obj.description = obj.description.format(listLog);
    demo.actionLog(obj);
    params.uid = demo.$store.state.show.loginInfo.uid;
    params.fingerprint = commonService.guid();
    params.vipname = params.vipname.replace(/'/g, '‘').replace(/;/g, '；');
    params.remark = params.remark.replace(/'/g, '‘').replace(/;/g, '；');
    var sql = demo.isNullOrTrimEmpty(params.initSql) ? '' : params.initSql;
    params.info1 = params.info1 || '';
    params.info2 = params.info2 || '';
    params.refundFingerprint = params.refundFingerprint || '';
    sql += sqlApi.salesInsert + sqlApi.salesInsertValues.format(demo.sqlConversion(params)) + ';';
    var saleItemsInsert = sqlApi.saleItemsInsert + sqlApi.saleItemsInsertValues + ';';
    _.forEach(params.saleItems, item => {
      item.code = params.code;
      item.disc = params.disc;
      item.price = demo.isNullOrTrimEmpty(item.price) ? item.salePrice : item.price;
      item.mprice = demo.isNullOrTrimEmpty(item.mprice) ? item.salePrice : item.mprice;
      item.goodFingerprint = demo.isNullOrTrimEmpty(item.goodFingerprint) ? item.fingerprint : item.goodFingerprint;
      item.fingerprint = commonService.guid();
      item.saleFingerprint = params.fingerprint;
      let itemParams = demo.sqlConversion(item);
      sql += saleItemsInsert.format(itemParams);
      sql += sqlApi.saleUpdateGoods.format(itemParams);
    });
    if (+params.accountId === 99) {
      onSuccess(sql);
    } else {
      let params1 = {};
      params1.id = params.accountId;
      params1.curAmt = params.discAmt;
      sql += sqlApi.accountsUpdate.format(params1);
      dao.transaction(sql, res => {
        onSuccess(res);
        if (+res === 1) {
          this.callSalesUpImmediately(params.code);
        }
      }, onFail);
    }
  },

  /**
   * 组合支付
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  blendSettlement: function (params, onSuccess, onFail) {
    if (+params.accountId !== 99 || !params.hasOwnProperty('blendPays') || params.blendPays.length !== 2) {
      onFail('组合支付参数不正确');
      return;
    }

    var blendPays = params.blendPays;
    var acctIds = _.map(_.filter(blendPays, 'acctId'), 'acctId');
    dao.exec(sqlApi.getAcctsById.format(acctIds.join()), res => {
      var accts = demo.t2json(res);
      if (accts.length < blendPays.length) {
        onFail('支付方式不正确');
        return;
      }
      var acct = {};
      accts.forEach(item => {
        acct[item.id] = item.fingerprint;
      });

      this.singleSettlement(params, initSql => {
        var sql =
          initSql +
          sqlApi.saleBlendPaysInsert +
          sqlApi.saleBlendPaysInsertWith.format(params.fingerprint);
        blendPays.forEach(item => {
          if (!item.hasOwnProperty('remark')) {
            item.remark = null;
          }
          item.acctFingerprint = acct[item.acctId];
          item.saleFingerprint = params.fingerprint;
          item.fingerprint = commonService.guid();

          sql += sqlApi.saleBlendPaysSelect.format(demo.sqlConversion(item)) + ' union all ';
        });
        sql = sql.substring(0, sql.lastIndexOf(' union all ')) + ';';
        sql += sqlApi.updateAcctsByBlendPays.format(params.fingerprint);

        dao.transaction(sql, res => {
          onSuccess(res);
          if (+res === 1) {
            this.callSalesUpImmediately(params.code);
          }
        }, onFail);
      }, onFail);
    }, onFail);
  },

  /**
   * 调用立即上传
   * @param {*} saleCode
   */
  callSalesUpImmediately: function (saleCode) {
    this.salesUpImmediately({ code: saleCode })
      .then(res => {
        if (res === 'success') {
          CefSharp.PostMessage(`订单（${saleCode}）上传成功` + res);
          console.log(`订单（${saleCode}）上传成功`, res);
        } else {
          CefSharp.PostMessage(`订单（${saleCode}）未上传` + res);
          console.log(`订单（${saleCode}）未上传`, res);
        }
      })
      .catch(err => {
        CefSharp.PostMessage(`订单（${saleCode}）上传失败` + err);
        console.error(`订单（${saleCode}）上传失败`, err);
      });
  },

  /**
   * 收银台下单后立即上传
   * @param {*} params
   */
  salesUpImmediately: function (params) {
    return new Promise((resolve, reject) => {
      // 收银台下单立即上传开关
      const salesUpImmediately = +$config.Base.OtherOptions.salesUpImmediately === 1;
      const storeSettings = $storeinfo[0].settings;
      // 收银台不云同步开关
      let stopSync;
      try {
        stopSync = JSON.parse(storeSettings).iCloud === true;
      } catch (err) {
        stopSync = false;
      }

      if (!salesUpImmediately || stopSync || demo.$store.state.show.isSyncing) {
        resolve(`下单立即上传开关=${salesUpImmediately}， 收银台不云同步=${stopSync}， isSyncing=${demo.$store.state.show.isSyncing}`);
        return;
      }

      let syncId;
      let syncAt;
      let synced = 0;
      // 订单是否包含新增且未上传过的商品
      dao.asyncExec(sqlApi.hasUnuploadedProducts.format(params))
        .then(res => {
          // 包含新增且未上传过的商品
          if (res[0].cnt > 0 || demo.$store.state.show.isSyncing || !pos.network.isConnected()) {
            resolve(`包含新增且未上传过的商品=${res[0].cnt}， isSyncing=${demo.$store.state.show.isSyncing}， 网络连接=${pos.network.isConnected()}`);
            return;
          }
          demo.$store.commit('SET_SHOW', { isSyncing: true });
          synced = 1;

          // 清空数据检查 + 插入云同步履历
          return Promise.all([this.clearsCheck(), this.syncCheck()]);
        })
        .then(reses => {
          if (reses === undefined) {
            return;
          }
          const res = reses[1];
          let data = res.data;
          if (!stringUtils.isBlank(res.response) && !stringUtils.isBlank(res.response.data)) {
            data = res.response.data;
          }
          if (data === null || typeof data !== 'object' || +data.code !== 200 || data.data.id <= 0) {
            resolve(`清空数据检查 + 插入云同步履历：${reses}`);
            return;
          }
          syncId = data.data.id;
          syncAt = stringUtils.isBlank(data.data.createAt) ? '2000-01-01 00:00:00.000000' : data.data.createAt;

          // 查询此订单
          return this.getUpSales(params.code);
        })
        .then(res => {
          if (res === undefined) {
            return;
          }
          // 执行上传
          return this.salesDoUp(res, syncId, syncAt);
        })
        .then(res => {
          if (res === undefined) {
            return;
          }
          resolve(res);
        })
        .catch(reject)
        .finally(() => {
          if (synced === 1 && demo.$store.state.show.isSyncing) {
            demo.$store.commit('SET_SHOW', { isSyncing: false });
          }
        });
    });
  },
  /**
   * 清空数据检查
   * @returns
   */
  clearsCheck: function () {
    return new Promise((resolve, reject) => {
      let info = {};
      if (!demo.isNullOrTrimEmpty($setting.info)) {
        info = demo.t2json($setting.info);
      }
      demo.$http.get(rest.listFromId, {
        params: {
          id: info.clearId || 0
        }
      }, {
        timeout: 3000
      })
        .then(res => {
          const res1 = res.data.data;
          const hisId = +res1.hisId;

          if (demo.isNullOrTrimEmpty(info.clearId)) {
            info.clearId = hisId;
            $setting.info = JSON.stringify(info);
            const sql = clearsSql.replaceClearId.format('info', $setting.info, '');
            dao.transaction(sql, resolve, reject);
          } else {
            settingService.otherClears(hisId, res1.successes, resolve, reject);
          }
        })
        .catch(reject);
    });
  },
  /**
   * 插入云同步履历，检测是否有数据需要下载
   * @returns
   */
  syncCheck: function () {
    return new Promise((resolve, reject) => {
      const params = {
        syncHistoryId: $setting.syncHistoryId,
        deviceCode: $config.deviceCode
      };
      const createAt = $storeinfo.length === 0 ? '' : $storeinfo[0].createAt;
      if (!stringUtils.isBlank(createAt)) {
        params.createAt = createAt;
      }
      demo.$http.post(rest.syncCheck, params, {
        maxContentLength: Infinity,
        timeout: 3000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  /**
   * 获取需要上传的订单
   * @param {*} code
   * @returns
   */
  getUpSales: function(code) {
    return new Promise((resolve, reject) => {
      const data = {};
      dao.asyncExec(sqlApi.getSales.format(code)).then(sales => {
        if (sales.length === 0) {
          resolve(data);
          return;
        }

        data.sales = sales;
        return dao.asyncExec(sqlApi.getSaleItems.format(sales[0].syncG));
      }).then(saleItems => {
        data.saleItems = saleItems;
        return dao.asyncExec(sqlApi.getSaleBlendPays.format(data.sales[0].syncG));
      }).then(saleBlendPays => {
        data.saleBlendPays = saleBlendPays;
        resolve(data);
      }).catch(reject);
    });
  },
  /**
   * 执行订单上传
   * @param {*} params
   * @param {*} syncId
   * @param {*} syncAt
   * @returns
   */
  salesDoUp: function(params, syncId, syncAt) {
    return new Promise((resolve, reject) => {
      params.syncAt = syncAt;
      demo.$http.post(rest.upSales, params, {
        maxContentLength: Infinity,
        timeout: 3000
      }).then(res => {
        const data = res.data;
        if (+data.code === 200 && demo.isNullOrTrimEmpty(data.inValidIdList)) {
          demo.$http.post(rest.syncEnd, {syncId, hasUp: 1}, {
            maxContentLength: Infinity,
            timeout: 3000
          })
            .then(() => {
              const saleId = params.sales[0].id;
              const itemIds = _.map(params.saleItems, 'id');
              const sql = sqlApi.updateSales.format({ saleId, itemIds, syncAt });
              dao.transaction(sql, () => {
                resolve('success');
              }, reject);
            });
        } else {
          reject(data.msg);
        }
      }).catch(reject);
    });
  },

  /**
   * 销售明细导出excel wheres.wheresGoods
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  doSalesExcelExport: function (params, onSuccess, onFail) {
    const wheres = this.getSaleWhereTotal(params);
    dao.exec(sqlApi.doSalesExcelExportCount.format(wheres.wheresTotal, wheres.wheresGoods, wheres.wheresZhiJie),
      res => {
        res = demo.t2json(res);
        if (res.length > 0) {
          let salesCount = _.uniqBy(res, 'createAt').length;
          // 总数超过1w给前端错误提示
          if ((salesCount + res.length) > 10000) {
            onFail('导出总数据量超过10000条,无法导出')
          } else {
            dao.exec(sqlApi.doSalesExcelExport.format(wheres.wheresTotal, wheres.wheresGoods, wheres.wheresZhiJie, wheres.whereRefundCodes, wheres.whereSaleCode, wheres.whereRefundQty), onSuccess, onFail);
          }
        } else {
          onSuccess([])
        }
      }, onFail);
  },

  /**
   * 销售单批量删除
   * saleService.deleteBatch({'id':'1,2,3'}, res=>{console.log(res)}, res=>{console.log(res)})
   * 批量启用: saleService.deleteBatch({'id':'1,2,3', 'isDel':0}, onSuccess, onFail)
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  deleteBatch: function (params, onSuccess, onFail) {
    if (params.isDel !== 0) {
      params.isDel = 1;
    }
    dao.transaction(sqlApi.salesDeleteBatch.format(params), onSuccess, onFail);
  },

  /**
   * 获取该销售单已退货商品（汇总）
   * 参数格式：{ fingerprint: 'a04c4be3-9582-4b30-a189-d2cbe91d5bed' }  销售单fingerprint
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getAlreadyBackGoods: function (params, onSuccess, onFail) {
    dao.asyncExec(sqlApi.getRefundFingerprints.format(params)).then(res => {
      const queryRes = demo.t2json(res);
      if (queryRes && queryRes.length > 0) {
        const fingerprintList = queryRes.map(item => item.fingerprint);
        const p = {
          fingerprints: fingerprintList.join('\',\'')
        }
        dao.exec(sqlApi.getRefundSaleItemSum.format(p), saleItemRes => {
          // console.log('查询该销售单已退商品结果:', demo.t2json(saleItemRes));
          onSuccess(demo.t2json(saleItemRes));
        }, onFail);
      } else {
        onSuccess(queryRes);
      }
    }, onFail);
  },

  /**
   * 获取该退货单详情
   * 参数格式：{ fingerprint: 'a04c4be3-9582-4b30-a189-d2cbe91d5bed' }  退货单fingerprint
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getBackOrdersDetail: function (params, onSuccess, onFail) {
    dao.asyncExec(sqlApi.getBackOrdersSales.format(params)).then(res => {
      const queryRes = demo.t2json(res);
      if (queryRes && queryRes.length > 0) {
        const detail = queryRes[0];
        const p = {
          saleFingerprint: detail.fingerprint
        }
        dao.exec(sqlApi.getBackOrdersSaleItem.format(p), saleItemRes => {
          const goods = demo.t2json(saleItemRes);
          detail.goods = goods;
          detail.realBackMoney = goods.reduce((pre, item) => {
            return calc(pre).plus(item.amt).toNumber();
          }, 0);
          detail.sumPurPrice = goods.reduce((pre, item) => {
            return calc(pre).plus(item.purPriceTotal).toNumber();
          }, 0);
          // console.log('查询该退货单详情:', detail);
          onSuccess(detail);
        }, onFail);
      } else {
        onSuccess(queryRes);
      }
    }, onFail);
  },
  /**
   *  通过销售单唯一标识抽取关联退货单
   * @param {*} saleFingerprint
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getSaleByRefundFingerprint(saleFingerprint, onSuccess, onFail) {
    dao.exec(`select create_at,code,fingerprint,id,uid from sales where refund_fingerprint = '${saleFingerprint}' order by create_at desc`, onSuccess, onFail)
  },
  /**
   * 通过退货单 refundFingerprint  查询 原销售单
   * @param {*} refundFingerprint
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getSaleByFingerprint(refundFingerprint, onSuccess, onFail) {
    dao.exec(`select * from sales where fingerprint = '${refundFingerprint}'`, onSuccess, onFail)
  },
  /**
   * 商家成长 某个时间段内累计销售?天
   */
  periodTimeSaleCount() {
    return new Promise((resolve) => {
      const date = new Date();
      let from = stringUtils.getFirstDayOfMonth(date).format('yyyy-MM-dd');
      let to = stringUtils.getLastDayOfMonth(date).format('yyyy-MM-dd');
      dao.exec(sqlApi.periodTimeSaleCount.format(from, to), resolve);
    });
  }
};

window.saleService = saleService;
export default saleService;
