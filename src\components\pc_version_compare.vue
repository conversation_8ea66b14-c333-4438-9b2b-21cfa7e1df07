<style lang="less" scoped>
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
.pc_ver_com1 {
  width: 840px;
  height: 564px;
  margin: 46px auto;
  border-radius: 12px;
  background: #FFF;
  display: flex;
}
.pc_ver_com2 {
  width: 242px;
  height: 510px;
  margin: 0 20px;
  justify-content: space-between;
  border: 1px solid #E3E6EB;
  border-radius: 20px;
}
.pc_ver_compare_div {
  position: relative;
  z-index: 3800;
  width: 880px;
  height: 660px;
  background: #FFF;
  border-radius: 12px;
  margin: 24px auto;
}
.pc_ver_header {
  width: 100%;
  height: 40px;
}
.pc_ver_header_container {
  text-align: center;
  padding-top: 35px;
}
.pc_ver_header_span {
  font-size: 24px;
  line-height: 35px;
  color: @themeFontColor;
  font-weight: 700;
}
.pc_ver_close_icon {
  font-size: 38px;
  color: #8197A6;
  float: right;
  font-weight: normal;
  margin-right: 30px;
  margin-top: -30px;
  cursor: pointer;
}
.pc_ver_null_header {
  background: linear-gradient(90deg, #8FA3C2 0%, #63749A 100%);
  border-radius: 20px 20px 0 0;
  width: 240px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pc_ver_null_span {
  font-size: 22px;
  line-height: 20px;
  color: #FFF;
  font-weight: bold;
}
.pc_ver_list_container {
  border-radius: 0 0 20px 20px;
  width: 240px;
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pc_ver_list_container li{
   list-style-type:none;
}
.pc_ver_container {
  position: relative;
}
.pc_ver_infinity {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 64px;
  height: 20px;
  font-size: 12px;
  text-align: center;
  border-radius: 12px 12px 12px 0px;
  background: linear-gradient(90deg, #3B416B 0%, #24253B 100%);
  color: #fff;
}
.pc_ver_infinity_xx {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 64px;
  height: 20px;
  font-size: 12px;
  text-align: center;
  font-weight: bold;
  border-radius: 12px 12px 12px 0px;
  background: linear-gradient(270deg, #D9A784 0%, #EFDECA 100%);
  color: #3B416B;
}
.li {
    float: left;
}
.pc_ver_false_header {
  background: linear-gradient(270deg, #D9A784 0%, #EFDECA 100%);
  border-radius: 20px 20px 0 0;
  width: 240px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pc_ver_false_span {
  font-size: 22px;
  line-height: 20px;
  color: #282A43;
  font-weight: bold;
}
.pc_ver_true_header {
  background: linear-gradient(90deg, #3B416B 0%, #24253B 100%);
  border-radius: 20px 20px 0 0;
  width: 240px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pc_ver_true_span {
  font-size: 22px;
  line-height: 20px;
  color: #FFCEBF;
  font-weight: bold;
}
ul {
  color: @themeFontColor;
  list-style: inside;
}
.pc_ver_icon1 {
  float: right;
  border-radius: 10px;
  background: linear-gradient(90deg, rgb(59, 65, 107) 0%, rgb(36, 37, 59) 100%);
  color: rgb(255, 206, 191);
  width: 40px;
  text-align: center;
  margin-top: -50px;
  margin-right: 40px;
  font-size: 12px;
}
.pc_ver_icon2 {
  float: right;
  border-radius: 10px;
  background: #FFCEBF;
  color: #25263C;
  width: 40px;
  text-align: center;
  margin-top: -50px;
  margin-right: 40px;
  font-size: 12px;
}
.buy_now {
  width: 180px;
  height: 42px;
  border-radius: 50px;
  font-weight: bold;
  font-size: 18px;
  text-align: center;
  line-height: 40px;
  margin: 0 auto;
  cursor: pointer;
}
.pc_meg25 {
  width: 100%;
  padding-top: 35px;
  padding-bottom: 30px;
  .pc_meg26 {
    margin: 0 auto;
    background: #CFA26B;
    border-radius: 50%;
    height: 60px;
    width: 60px;
    .pc_meg29 {
      margin-left: 10px;
      margin-top: 10px;;
    }
  }
}
.pc_meg27 {
  font-size: 24px;
  line-height: 40px;
  text-align: center;
  letter-spacing: -0.408px;
  color: @themeFontColor;
}
.pc_meg28 {
  display: inline-block;
  width: 140px;
  height: 50px;
  text-align: center;
  background: @themeFontColor;
  border-radius: 4px;
  font-weight: bold;
  font-size: 20px;
  line-height: 48px;
  letter-spacing: -0.408px;
  color: #FFFFFF;
  margin-top: 32px;
  cursor: pointer;
}
.pc_vco {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);
  display:flex;justify-content:center;align-items:center;
}
.pc_buy_cof_div {
  position: relative;
  z-index: 4000;
  width: 768px;
  height: 558px;
  background: #FFF;
  border-radius: 12px;
  margin: 24px auto;
}
.pc_cof_header {
  width: 100%;
  height: 40px;
}
.pc_cof_header_container {
  text-align: left;
  padding-top: 35px;
}
.pc_cof_header_span {
  font-size: 24px;
  line-height: 35px;
  color: #282A43;
  font-weight: 700;
  margin-left: 32px;
}
.pc_cof_shop_container {
  background: linear-gradient(270deg, #D9A784 0%, #EFDECA 100%);
  border-radius: 10px;
  text-align: center;
  color: #282A43;
  display: inline-block;
  padding: 0px 15px;
  font-weight: 600;
  height: 30px;
  line-height: 36px;
  margin-left: 15px;
}
.pc_cof_com1 {
  height: 160px;
  margin: 46px auto 0px;
  background: #FFF;
  display: flex;
  justify-content: space-between;
  padding: 0px 32px;
}
.pc_cof_com2 {
  width: 218px;
  height: 146px;
  display: flex;
  text-align: center;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  background: #F8F8F8;
  border: 2px solid #F8F8F8;
  border-radius: 8px;
  padding: 15px 0px;
  color: #282A43;
  cursor: pointer;
}
.pc_cof_info1 {
  font-weight: 600;
  font-size: 18px;
}
.pc_cof_info2 {
  font-size: 36px;
}
.pc_cof_info3 {
  color: #BFBFBF;
  font-size: 18px;
}
.pc_cof_info_choose {
  color:#895714;
  background: #FFF5EA;
  border: 2px solid #D9A784;
}
.pc_cof_com3 {
  margin: 30px 32px;
  height: 78px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pc_cof_com4 {
  color: #8C8C8C;
  font-size: 16px;
  line-height: 24px;
}
.pc_cof_com5 {
  color: #895714;
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
}
.pc_cof_com6 {
  display: flex;
  width: 162px;
  justify-content: space-between;
}
.pc_cof_com7 {
  background: linear-gradient(270deg, #D9A784 0%, #EFDECA 100%),
  linear-gradient(90deg, #8FA3C2 0%, #63749A 100%), #CFA980;
  width: 704px;
  height: 52px;
  margin: 20px 32px;
  border-radius: 8px;
  color: #282A43;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
}
#ver_icon1 {
  background: @verIcon1_backgroundColor;
  color: @verIcon1_fontColor;
}
#ver_icon2 {
  background: @verIcon2_backgroundColor;
  color:  @verIcon2_fontColor;

}
.liColor {
  color: @text;
}
</style>
<template>
  <!-- 版本功能对比 -->
  <div v-if="isVesionCompare" style="width: 100%;height: 100%;position: fixed;top: 0;left: 0;z-index: 3500">
    <div style="font-family: Microsoft YaHei, sans-serif;" id="aliPage" v-html="aliPage"></div>
    <div v-if="!onlyShowBuyChoose"  class="pc_vco">
      <div class="pc_ver_compare_div">
        <div class="pc_ver_header">
          <div class="pc_ver_header_container">
            <span class="pc_ver_header_span">十年品牌深耕，满足商家不同需求</span>
          </div>
        </div>
        <div class="pc_ver_close_icon" @click="closeVersionCompare">×</div>
        <div class="pc_ver_com1">
          <div class="pc_ver_com2">
            <div class="pc_ver_null_header">
              <span class="pc_ver_null_span">简易版</span>
            </div>
            <div class="pc_ver_list_container">
              <div>
                <ul style="width: 180px;height: 21px;" :key="item.label" v-for="item in verFunctionList">
                  <li class="li" :class="item.level > 0 ? 'liColor' : ''">{{item.label}}</li>
                  <li style="float: right;">
                    <img
                      alt=""
                      v-show="item.level <= 0"
                      src="../image/zgzn-pos/download.png"
                    />
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="pc_ver_com2" :style="ultimate === false && period == -1 ? 'border: 3px solid #EFDECA' : ''">
            <div class="pc_ver_container">
              <div class="pc_ver_false_header">
                <span class="pc_ver_false_span">专业版</span>
              </div>
              <div class="pc_ver_icon1">推荐</div>
              <div v-if="ultimate === false && period == -1" class="pc_ver_infinity">终身使用</div>
            </div>
            <div class="pc_ver_list_container">
              <div>
                <ul style="width: 180px;height: 21px;" :key="item.label" v-for="item in verFunctionList">
                  <li class="li" :class="item.level > 1 ? 'liColor' : ''">{{item.label}}</li>
                    <li style="float: right;">
                      <img
                        alt=""
                        v-show="item.level <= 1"
                        src="../image/zgzn-pos/download.png"
                      />
                  </li>
                </ul>
              </div>
            </div>
            <div v-show="isConnect && (isAuto && (ultimate === null || period != -1))">
              <div class="buy_now" @click="getOptionalList('XS')"
                id="ver_icon1">
                {{$t('components.pc_version_compare.buyNow')}}
              </div>
            </div>
            <div style="color: #8C8C8C;text-align:center;font-size: 14px;margin-top: 20px;"
              v-if="!isAuto && (ultimate === null || (ultimate === false && period != -1))">
              如需升级，请联系原购买渠道
            </div>
          </div>
          <div class="pc_ver_com2" :style="ultimate === true && period == -1 ? 'border: 3px solid #EFDECA' : ''">
            <div class="pc_ver_container">
              <div class="pc_ver_true_header">
                <span class="pc_ver_true_span">旗舰版</span>
              </div>
              <div class="pc_ver_icon2">热门</div>
              <div v-if="ultimate === true && period == -1" class="pc_ver_infinity_xx">终身使用</div>
            </div>
            <div class="pc_ver_list_container">
              <div>
                <ul style="width: 180px;height: 21px;" :key="item.label" v-for="item in verFunctionList">
                  <li class="li" :class="item.level > 2 ? 'liColor' : ''">{{item.label}}</li>
                    <li style="float: right;">
                      <img
                        alt=""
                        v-show="item.level <= 2"
                        src="../image/zgzn-pos/download.png"
                      />
                  </li>
                </ul>
              </div>
            </div>
            <div v-show="isConnect && ((isAuto && ultimate === null)
              || (isAuto && period != -1) || (isAuto && !ultimate))">
              <div class="buy_now" @click="getOptionalList('XX')"
                id="ver_icon2">
                {{$t('components.pc_version_compare.buyNow')}}
              </div>
            </div>
            <div style="color: #8C8C8C;text-align:center;font-size: 14px;margin-top: 20px;"
              v-if="!isAuto && (ultimate === null || period != -1)">
              如需升级，请联系原购买渠道
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="showSuccess"
      width="433px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="pc_meg25">
        <div class="pc_meg26" :style="status === 1 ? 'background: #FF6159' : ''">
          <el-image :src="statusLogo" class="pc_meg29"/>
        </div>
        <div class="pc_meg27" style="margin-top: 16px;padding-left: 10px;">{{statusMsg}}</div>
        <div class="pc_meg27">{{infoMsg}}</div>
        <div class="pc_meg28" style="margin-left: 70px;margin-right: 28px;" @click="closeTip">取消</div>
        <div class="pc_meg28" style="background: #CFA26B" @click="go">{{rightButton}}</div>
      </div>
    </el-dialog>
    <!-- 选择购买的时长 -->
    <div v-if="showBuyConfirm || onlyShowBuyChoose" class="pc_vco">
      <div class="pc_buy_cof_div">
        <div class="pc_cof_header">
          <div class="pc_cof_header_container">
            <span class="pc_cof_header_span">购买{{ultimateName}}</span>
            <div class="pc_cof_shop_container">店铺名：{{shopName}}</div>
          </div>
        </div>
        <div class="pc_ver_close_icon" @click="buyConfirmClose()">×</div>
        <div class="pc_cof_com1">
          <div v-for="(op, index) in optionalList" :key="index">
            <div class="pc_cof_com2"
              @click="chooseChange(index)"
              :class="chooseIndex === index ? 'pc_cof_info_choose' : ''">
              <div class="pc_cof_info1">{{op.dateNum === 0 ? '' : op.dateNum}}{{op.dateNum === 0 ? '终身' : '天'}}</div>
              <div class="pc_cof_info2"><span style="font-size: 24px;">¥</span> {{Number(op.price).toFixed(2)}}</div>
              <div class="pc_cof_info3">{{op.avePrice === null ? ' ' : '低至'+ op.avePrice + '元/天'}}</div>
            </div>
          </div>
        </div>
        <div class="pc_cof_com3">
          <div class="pc_cof_com4">应付金额:</div>
          <div class="pc_cof_com5"><span style="font-size:20px;">¥</span>{{payMoney}}</div>
        </div>
        <div class="pc_cof_com3">
          <div class="pc_cof_com4">支付方式:</div>
          <div class="pc_cof_com6">
            <img style="width: 38px;height:38px;" src="../image/pc_zfb_logo.png" />
            <div class="pc_cof_com4" style="line-height: 38px;">支付宝扫码支付</div>
          </div>
        </div>
        <div class="pc_cof_com7" @click="openALiCode()">立即支付</div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';

export default {
  data() {
    return {
      win: null,
      winLoop: null,
      aliPage: '',
      set_ip: (typeof returnCitySN != 'undefined' && returnCitySN.cip) ? returnCitySN.cip : '127.0.0.1',
      showSuccess: false,
      statusLogo: require('../image/pc_msgMark.png'),
      statusMsg: '购买成功！',
      infoMsg: '',
      rightButton: '确定',
      aLiOutTradeNo: '',
      timersList: [],
      timers: 60,
      showBuyConfirm: false,
      optionalList: [],
      shopName: '',
      chooseIndex: 0,
      payMoney: '0.00',
      servicePriceId: '',
      ultimateName: '',
      status: 0,
      edition: 1,
      isConnect: pos.network.isConnected()
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    closeVersionCompare() {
      this.SET_SHOW({ isVesionCompare: false });
    },
    generateUUID() {
      var d = new Date().getTime();
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now();
      }
      var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
      });
      return uuid;
    },
    openALiCode() {
      demo.actionLog({page: 'pc_version_compare', action: 'waitingForPay', description: `${this.ultimateName}支付宝码出现，等待客户支付`});
      if (this.win !== null) {
        this.win.close();
        this.clearTimer();
        clearInterval(this.winLoop);
      }
      this.edition = this.servicePriceId;
      this.aliPage = '';
      let outTradeNo = this.generateUUID() + '_' + this.sysUid + '_' + '1';
      const param = {
        'subject': this.ultimateName,
        'spbillCreateIp': this.set_ip,
        'payType': '1',
        'mchid': 'd30198ab-574d-4ad7-b09c-2fbc75001a29',
        'body': this.ultimateName,
        'sysUid': this.sysUid,
        'outTradeNo': outTradeNo,
        'servicePriceId': this.servicePriceId,
        'frontUrl': this.$rest.autoUpdateSuccess
      };
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.autoUpdate, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.code === 200) {
              this.aliPage = res.data.data;
              setTimeout(() => {
                this.aliBuy();
              }, 0);
              this.aLiOutTradeNo = outTradeNo;
              this.createTimer(outTradeNo, this.servicePriceId);
            } else {
              demo.msg('warning', res.data.err_code_des);
            }
          });
      } else {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    aliBuy() {
      document.getElementById("aliPage").getElementsByTagName("form")[0].target = 'newWindow';
      this.win = window.open('about:blank', 'newWindow');
      this.winLoop = setInterval(() => {
        console.log('open page listening+');
        if (this.win.closed) {
          console.log('winLoop clearInterval+');
          this.win = null;
          this.clearTimer();
          clearInterval(this.winLoop);
        }
      }, 1500);
      this.$once('hook:beforeDestroy', () => {
        clearInterval(this.winLoop);
      });
      document.getElementById("aliPage").getElementsByTagName("form")[0].submit();
      this.win.focus();
    },
    dataFormat(data, flag) {
      if (data.trade_state === 'TRADE_SUCCESS') {
        console.log(data, 'TRADE_SUCCESS Data');
        demo.actionLog(logList.paySuccess);
        this.clearTimer();
        this.status = 0;
        this.statusLogo = require('../image/pc_msgMark.png');
        this.statusMsg = '购买成功！';
        this.infoMsg = '恭喜你已升级成为' + this.ultimateName;
        this.rightButton = '确定';
        this.showSuccess = true;
        this.showBuyConfirm = false;
        this.SET_SHOW({showTemporaryTip: false});
        if (this.onlyShowBuyChoose) {
          this.SET_SHOW({onlyShowBuyChoose: false, isVesionCompare: true});
        }
        setTimeout(() => {
          this.checkCode();
        }, 5000);
      } else if ((data.trade_state === 'WAIT_BUYER_PAY' || data.trade_state === 'TRADE_FINISHED') && flag === 1) {
        console.log(data, 'WAIT_BUYER_PAY or TRADE_FINISHED Data');
        this.reloadPay();
      } else {
        // todo
      }
    },
    reloadPay() {
      this.status = 1;
      this.statusLogo = require('../image/Vector.png');
      this.statusMsg = '未支付！';
      this.infoMsg = '订单还未支付，请扫码支付！';
      this.rightButton = '重新查询';
      this.showSuccess = true;
      this.createTimer(this.aLiOutTradeNo, this.servicePriceId);
    },
    checkCode() { // 订单完成后查看最高版本的激活码信息
      let params = {
        phone: this.sysUid
      }
      demo.$http.post(this.$rest.checkCode, params)
        .then(res => {
          if (res.data.code === 200) {
            let activeCode = res.data.data;
            let trial_day = +activeCode.remainDay + 1;
            this.SET_SHOW({
              period: activeCode.period,
              trialDay: trial_day,
              sysEndDate: activeCode.endDate,
              ultimate: activeCode.ultimate,
              showKefu: activeCode.agentType === 1
            });
            settingService.put([
              { key: settingService.key.period, value: activeCode.period },
              { key: settingService.key.enddate, value: activeCode.endDate },
              { key: settingService.key.ultimate, value: activeCode.ultimate }
            ], () => {});
          }
        });
    },
    createTimer(aLiOutTradeNo, itemId) { // 轮询订单状态
      const that = this;
      this.timers = 299;
      setTimeout(() => {
        let timer = setInterval(() => { // 创建定时器
          console.log(aLiOutTradeNo + '正在轮询,当前timersList' + this.timersList);
          that.timers--;
          that.shortPayQuery(aLiOutTradeNo, 0, itemId);
        }, 3000);
        this.timersList.push(timer);
        this.$once('hook:beforeDestroy', () => {
          clearInterval(timer);
        });
      }, 3000);
    },
    clearTimer() { // 清除轮询定时器
      this.timersList.forEach(timer => {
        clearInterval(timer);
      });
      this.timersList = [];
    },
    buyConfirmClose() {
      this.showBuyConfirm = false;
      if (this.onlyShowBuyChoose) {
        this.SET_SHOW({ onlyShowBuyChoose: false, isVesionCompare: false });
      }
    },
    getOptionalList (type) { // 加载可选版本信息
      this.setUltimateName(type);
      demo.actionLog({page: 'pc_version_compare', action: 'btnClickPay', description: `版本比较页-${this.ultimateName}立即支付`});
      let params = {
        systemName: $config.systemName,
        subName: $config.subName,
        serviceName: type
      }
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.autoUpdatePrice, params)
          .then(res => {
            if (res.data.code === 200) {
              res.data.data.map(item => {
                item.avePrice = item.dateType === 0 ? null : (Number(item.price / item.dateNum).toFixed(2));
              });
              this.optionalList = res.data.data;
              if (this.optionalList.length === 0) {
                this.SET_SHOW({isVesionCompare: false});
                demo.msg('error', '该版本暂无可选购套餐');
              } else {
                this.showBuyConfirm = true;
                this.chooseChange(0);
              }
            }
          });
      } else {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    setUltimateName(type) {
      this.ultimateName = type === 'XX' ? '旗舰版' : '专业版';
    },
    chooseChange(index) {
      this.chooseIndex = index;
      this.payMoney = Number(this.optionalList[index].price).toFixed(2);
      this.servicePriceId = this.optionalList[index].id;
    },
    renewShow() {
      let verFlg = this.ultimate ? 'XX' : 'XS';
      this.getOptionalList(verFlg);
    },
    // 查询支付结果
    shortPayQuery (outTradeNo, flag) {
      const param = {
        'outTradeNo': outTradeNo
      };
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.shortPayQuery, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.message === 'success') {
              this.dataFormat(res.data, flag);
            } else {
              if (flag === 1) {
                this.reloadPay();
              }
            }
          });
      } else {
        this.clearTimer();
        this.showSuccess = true;
        this.status = 1;
        this.statusLogo = require('../image/Vector.png');
        this.statusMsg = '网络已断开，';
        this.infoMsg = '请联网后重新查询支付结果！';
        this.rightButton = '重新查询';
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    closeTip() {
      this.showSuccess = false;
      this.aLiOutTradeNo = '';
    },
    go() {
      this.showSuccess = false;
      if (this.rightButton === '重新查询') {
        this.shortPayQuery(this.aLiOutTradeNo, 1);
      }
    }
  },
  watch: {
    timers() {
      if (this.timers === 0) {
        this.clearTimer();
      }
    },
    isVesionCompare() {
      if (this.isVesionCompare === false) {
        this.clearTimer();
      }
    },
    showSuccess() {
      if (this.showSuccess === false) {
        this.clearTimer();
      }
    }
  },
  created() {
    this.shopName = $storeinfo[0].name;
    console.log(this.ultimate);
    console.log(this.period);
    if (this.onlyShowBuyChoose) {
      this.renewShow();
    }
  },
  computed: mapState({
    isVesionCompare: state => state.show.isVesionCompare,
    onlyShowBuyChoose: state => state.show.onlyShowBuyChoose,
    verFunctionList: state => state.show.verFunctionList,
    sysUid: state => state.show.sys_uid,
    ultimate: state => state.show.ultimate,
    period: state => state.show.period,
    isAuto: state => state.show.isAuto
  }),
  beforeDestroy() {
    this.clearTimer();
    if (this.win !== null) {
      this.win.close();
    }
  }
};
</script>
