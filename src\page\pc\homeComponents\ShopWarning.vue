<template>
  <div class="shopWarningWrap">
    <div class="header cjFlexBetween">
      <div class="title">提醒事项</div>
      <div v-if="$employeeAuth('use_settings')" class="setting" @click="toSetting()">
        <img src="@/image/zgzn-pos/pc_home_setting.png" />
        <div>设置</div>
      </div>
    </div>
    <div class="warnListWrap">
      <div v-if="stockLow" class="warnItem cjFlexRowCenter"
        @click="toStockWarning(true)">
        <div class="tag">预警</div>
        <div class="warnText">库存不足商品{{ stockLow }}种</div>
      </div>
      <div v-if="stockHight" class="warnItem cjFlexRowCenter"
        @click="toStockWarning(false)">
        <div class="tag">预警</div>
        <div class="warnText">库存过剩商品{{ stockHight }}种</div>
      </div>
      <div v-if="expiredWarningNotice && overdue" class="warnItem cjFlexRowCenter"
        @click="toExpirationWarning()">
        <div class="tag">预警</div>
        <div class="warnText">过期预警商品{{ overdue }}种</div>
      </div>
      <div v-if="emptyShow" class="emptyWrap cjFlexCenter">
        <i class="cj-icon cj-icon-survey" />
        <div>暂无提醒事项</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  name: 'ShopWarning',
  data() {
    return {
      stockHight: 0, // 库存过剩
      stockLow: 0, // 库存不足
      overdue: 0, // 过期预警
      overdueDate: 0, // 过期预警过期天数范围
      expiredWarningNotice: true
    };
  },
  computed: {
    emptyShow() {
      return !this.stockHight && !this.stockLow && (!this.expiredWarningNotice || !this.overdue);
    }
  },
  mounted() {
    this.getStockWarning();
    this.getOverdueDate()
    this.getExpirationCount();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getOverdueDate() {
      const storeList = demo.$store.state.show.storeList
      if (demo.isNullOrTrimEmpty(storeList[0].settings)) {
        this.overdueDate = 10;
        this.expiredWarningNotice = true
      } else {
        const overdue = demo.t2json(storeList[0].settings).overdueDate
        this.overdueDate = overdue === undefined ? 10 : overdue;
        const expiredWarning = demo.t2json(storeList[0].settings).expiredWarningNotice
        this.expiredWarningNotice = expiredWarning === undefined ? 10 : expiredWarning;
      }
    },
    // 获取库存预警
    async getStockWarning() {
      const stock = await goodService.goodsStockWarningCount();
      this.stockHight = stock.hight || 0;
      this.stockLow = stock.low || 0;
    },
    // 跳转库存预警页面
    toStockWarning(boolean) {
      // 先判断是否有查看报表的权限
      if (!this.$employeeAuth('show_shift_records')) {
        demo.msg('warning', this.$msg.not_employee_permissions);
        return;
      }
      this.$event.$emit('stockTabChange', 4);
      localStorage.setItem('stockStatusChange', boolean ? 1 : 2);
      this.SET_SHOW({
        isHome: false,
        isStockWarningDetail: true
      });
    },
    // 获取过期预警
    async getExpirationCount() {
      const res = await goodService.goodsExpirationCount({ overdueDate: this.overdueDate });
      this.overdue = res || 0;
    },
    // 跳转过期预警页面
    toExpirationWarning() {
      // 先判断是否有查看报表的权限
      if (!this.$employeeAuth('show_shift_records')) {
        demo.msg('warning', this.$msg.not_employee_permissions);
        return;
      }
      this.$event.$emit('stockTabChange', 5);
      this.SET_SHOW({
        isHome: false,
        isOverdueWarningDetail: true
      });
    },
    // 跳转设置页面第二个选项卡系统设置
    toSetting() {
      this.SET_SHOW({ selectRow: 2 });
      this.SET_SHOW({
        isHome: false,
        isSetting: true
      });
    }
  }
};
</script>
<style lang="less" scoped>
.shopWarningWrap {
  height: calc(100% - 256px);
  border-radius: 12px;
  padding: 12.5px 12px 0;
  background-color: #ffffff;
  .header {
    font-size: 20px;
    height: 23px;
    line-height: 23px;
    .title {
      color: @homeColor;
      font-weight: bold;
      float: left;
    }
    .setting {
      float: right;
      color: #BDA169;
      font-size: 16px;
      cursor: pointer;
      img {
        float: left;
        margin-top: 3px;
        width: 20px;
      }
      div {
        margin-left: 5px;
        float: left;
        margin-top: 1px;
      }
    }
  }
  .warnListWrap {
    padding-top: 12px;
    height: calc(100% - 23px);
    overflow-y: auto;
    .warnItem {
      font-size: 14px;
      margin-bottom: 12px;
      .tag {
        width: 44px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        border-radius: 2px;
        background-color: #fff7e8;
        color: #ff7d00;
        margin-right: 4px;
      }
      .warnText {
        text-decoration: underline;
        cursor: pointer;
      }
    }
  }
  .emptyWrap {
    height: 100%;
    color: #e1e1e1;
    font-size: 14px;
    flex-direction: column;
    .cj-icon-survey {
      font-size: 36px;
    }
  }
}
</style>
