<style lang='less' scoped>
/deep/ .el-table th {
  padding: 0px;
}
.pvv_ana {
  float: right;width: 140px;height: 44px;background: @linearBackgroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: 700;border-radius: 22px;margin-top: 10px;margin-right: 10px;cursor: pointer;
}
.pvv_ana1 {
  float: right;
  width: 130px;
  height: 44px;
  line-height: 44px;
  background: @linearBackgroundColor;
  color: white;
  text-align: center;
  font-size: 18px;
  border-radius: 22px;
  margin-top: 10px;
  margin-right: 10px;
  cursor: pointer;
  font-weight: 700;
}
.pvv_ana11 {
  position: absolute;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 100;
}
.pvv_ana12 {
  width: 970px;background: #FFF;overflow: hidden;margin: 0 auto;margin-top: 180px;border-radius: 6px;color: @themeFontColor;font-size: 16px;
}
.pvv_ana13 {
  width: 930px;margin-left: 20px;line-height: 60px;border-bottom: 1px solid #E3E6EB;font-size: 18px;font-weight: bold;position: relative;
}
.pvv_ana13 div {
  position: absolute;right: 0;top: -2px;font-size: 32px;cursor: pointer;
}
.pvv_ana14 {
  margin-top: 30px;margin-left: 50px;overflow: hidden;
}
.pvv_ana15 {
  float: left;line-height: 40px;font-weight: 700;
}
.pvv_ana16 {
  float: left;width: 100px;height: 40px;line-height: 38px;text-align: center;font-weight: 700;border: 1px solid #D7D7D7;border-radius: 20px;background: #F5F8FB;margin-left: 10px;margin-right: 10px;cursor: pointer;
}
.pvv_ana17 {
  border-color: @themeBackGroundColor;color: @themeBackGroundColor;background: @themeButtonBackGroundColor;
}
.pvv_ana18 {
  width: 120px;
  height: 40px;
  border: 1px solid #D7D7D7;
  border-radius: 20px;
  margin-left: 10px;
  margin-right: 10px;
  float: left;
}
.pvv_ana18 input {
  font-size: 16px;border: none;width: 76px;margin-top: 6px;margin-left: 12px;float: left;
}
.pvv_ana18 div {
  float: left;line-height: 38px;color: @themeBackGroundColor;
}
.pvv_ana19 {
  float: left;
  margin-top: 45px;margin-left: 660px;width: 120px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  font-weight: 700;
  color: @themeBackGroundColor;
  line-height: 42px;
  text-align: center;
  cursor: pointer;
}
.pvv_ana2 {
  float: left;
  margin-top: 45px;margin-left: 20px;width: 120px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  background: @themeBackGroundColor;
  font-weight: 700;
  color: #FFF;
  line-height: 42px;
  text-align: center;
  cursor: pointer;
}
.pvv_ana21 {
  float: left;margin-left: 10px;line-height: 66px;color: #567485;font-weight: bold;font-size: 16px;
}
.pvv_ana21 span {
  color: #B1C3CD;
}
.date_picker_container{
  width: 300px;
  height: 40px;
  background: #FFFFFF;
  border: 1px solid #D7D7D7;
  border-radius: 20px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  float: left;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
  background: #FFFFFF;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 20px;
  height: 40px;
  color: @themeFontColor;
  font-size: 16px;
  padding-right: 0;
}
/deep/ .el-icon-date:before {
  content: '';
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 38px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: -7px;right: -10px;color: @themeFontColor;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-range__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-range-separator {
  width: 26px;color: @themeFontColor;padding-top: 3px;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-input__inner {
  border-radius: 22px;height: 44px;
}
/deep/ .el-input__inner {
  color: @themeFontColor;
}

/deep/ .el-input__suffix-inner {
  color: #C0C4CC;
  font-size: 14px;margin-right: 12px;
}
/deep/ .el-input__suffix {
  padding-top: 10px;
}
/deep/ .el-date-editor--daterange {
  line-height: 20px;
}
// .el-input__prefix, .el-input__suffix
.deposit_record_container{
  background: #F5F8FB;
  .top{
    height: 66px;
    background: #F5F8FB;
    overflow: hidden;
    // .top_left_container{
    //   .top_left{
    //     float: left;
    //     margin-top: 11px;
    //     position: relative;
    //     .el-input {
    //       float: left;
    //     }
    //     .top_left_title{
    //       float: left;
    //       color: @themeFontColor;
    //       font-size: 16px;
    //       margin-left: 30px;
    //       font-weight: bold;
    //       margin-top: 10px;
    //     }
    //   }
    // }
  }
  .table_container{
    height: calc(100vh - 126px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
      .table_bottom{
        float: right;
        height: 42px;
        font-size: 16px;
        background: white;
        margin-top: 10px;
        span{
          color: @themeBackGroundColor;
        }
      }
  }
}
</style>
<template>
  <!-- 会员价值分析 -->
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="pvv_ana11" v-show="select_condition">
      <div class="pvv_ana12">
        <div class="pvv_ana13">
          编辑查询条件
          <div @click="select_condition = false;">×</div>
        </div>
        <div class="pvv_ana14">
          <div class="pvv_ana15">统计时间：</div>
          <div class="pvv_ana16" :class="choose_date === 1 ? 'pvv_ana17' : ''" @click="choose_date = 1;">最近7天</div>
          <div class="pvv_ana16" :class="choose_date === 2 ? 'pvv_ana17' : ''" @click="choose_date = 2;">最近14天</div>
          <div class="pvv_ana16" :class="choose_date === 3 ? 'pvv_ana17' : ''" @click="choose_date = 3;">最近30天</div>
          <div class="pvv_ana16" :class="choose_date === 4 ? 'pvv_ana17' : ''" @click="choose('date')">自定义</div>
          <!-- :style="focusDate ? 'border-color: #d5aa76' : 'border-color: #E3E6EB'" -->
          <div
            v-show="choose_date === 4"
            class="date_picker_container"
          >
            <el-date-picker
              v-model="date1"
              type="date"
              placeholder="开始日期"
              @focus="focusDate = true"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
              :clearable="false"
            >
            </el-date-picker>
            <div style="font-size: 16px;color: #567485">至</div>
            <el-date-picker
              v-model="date2"
              type="date"
              placeholder="结束日期"
              @focus="focusDate = true"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
              :clearable="false"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="pvv_ana14">
          <div class="pvv_ana15">消费次数：</div>
          <div class="pvv_ana16" :class="choose_times === 1 ? 'pvv_ana17' : ''" @click="choose_times = 1;times1 = 3;times2 = 5;">3~5次</div>
          <div class="pvv_ana16" :class="choose_times === 2 ? 'pvv_ana17' : ''" @click="choose_times = 2;times1 = 6;times2 = 10;">6~10次</div>
          <div class="pvv_ana16" :class="choose_times === 3 ? 'pvv_ana17' : ''" @click="choose_times = 3;times1 = 11;times2 = 20;">11~20次</div>
          <div class="pvv_ana16" :class="choose_times === 4 ? 'pvv_ana17' : ''" @click="choose('times1')">自定义</div>
          <div
            v-show="choose_times === 4"
            style="float: left;"
          >
            <div class="pvv_ana18">
              <input id="times1" type="text" v-model="times1"
                @input="times1 = $intMaxMinLimit({data: times1, max: 999, min: 0})">
              <div>次</div>
            </div>
            <div style="float: left;line-height: 40px;">至</div>
            <div class="pvv_ana18">
              <input type="text" v-model="times2"
                @input="times2 = $intMaxMinLimit({data: times2, max: 999, min: 0})">
              <div>次</div>
            </div>
          </div>
        </div>
        <div class="pvv_ana14">
          <div class="pvv_ana15">订单均价：</div>
          <div class="pvv_ana16" :class="choose_price === 1 ? 'pvv_ana17' : ''" @click="choose_price = 1;price1 = 5;price2 = 15;">5~15元</div>
          <div class="pvv_ana16" :class="choose_price === 2 ? 'pvv_ana17' : ''" @click="choose_price = 2;price1 = 16;price2 = 25;">16~25元</div>
          <div class="pvv_ana16" :class="choose_price === 3 ? 'pvv_ana17' : ''" @click="choose_price = 3;price1 = 26;price2 = 35;">26~35元</div>
          <div class="pvv_ana16" :class="choose_price === 4 ? 'pvv_ana17' : ''" @click="choose('price1')">自定义</div>
          <div
            v-show="choose_price === 4"
            style="float: left;"
          >
            <div class="pvv_ana18">
              <input id="price1" type="text" v-model="price1"
                @input="price1 = $intMaxMinLimit({data: price1, max: 100000, min: 0})">
              <div>元</div>
            </div>
            <div style="float: left;line-height: 40px;">至</div>
            <div class="pvv_ana18">
              <input type="text" v-model="price2" @input="price2 = $intMaxMinLimit({data: price2, max: 100000, min: 0})">
              <div>元</div>
            </div>
          </div>
        </div>
        <div style="overflow: hidden;">
          <div class="pvv_ana19" @click="select_condition = false;">取消</div>
          <div class="pvv_ana2" @click="saveCondition()">保存</div>
        </div>
        <div style="height: 20px;"></div>

        <!-- <div class="top_left">
          <div class="top_left_title">消费次数</div>
          <el-input style="width: 100px;margin-left:14px" v-model="totalTimesFrom" maxlength="3" @blur="check('totalTimesFrom')">
            <span slot="suffix" style="font-size:16px;color:#BDA136">次</span>
          </el-input>
          <div class="top_left_title" style="margin-left: 10px;margin-right: 10px;width: 16px;">至</div>
          <el-input style="width: 100px;" v-model="totalTimesTo" maxlength="3" @blur="check('totalTimesTo')">
            <span slot="suffix" style="font-size:16px;color:#BDA136">次</span>
          </el-input>
        </div>
        <div class="top_left">
          <div class="top_left_title">订单均价</div>
          <el-input style="width: 100px;margin-left:14px" v-model="aveMoneyFrom" maxlength="8" @blur="check('aveMoneyFrom')">
            <span slot="suffix" style="font-size:16px;color:#BDA136">元</span>
          </el-input>
          <div class="top_left_title" style="margin-left: 10px;margin-right: 10px;width: 16px;">至</div>
          <el-input style="width: 100px;" v-model="aveMoneyTo" maxlength="8" @blur="check('aveMoneyTo')">
            <span slot="suffix" style="font-size:16px;color:#BDA136">元</span>
          </el-input>
        </div> -->
      </div>
    </div>
    <div class="top">
      <div class="pvv_ana21">
        查询条件：<span>消费次数{{totalTimesFrom}}~{{totalTimesTo}}次；订单均价{{aveMoneyFrom}}~{{aveMoneyTo}}元；{{fromDate}}至{{toDate}}；</span>
      </div>
      <div class="pvv_ana1" @click="httpRequest('导出表格', true)">导出表格</div>
      <div @click="select_condition = true;" class="pvv_ana">编辑查询条件</div>
    </div>
    <div class="table_container">
      <el-table :data="tableData" :height="tableHeight" stripe :empty-text="!loading ? '暂无数据' : ' '">
        <el-table-column label="会员姓名" align="left" prop="name" min-width="10%" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="手机号" align="center" prop="mobile" min-width="15%"></el-table-column>
        <el-table-column label="卡号" align="center" min-width="13%" :show-overflow-tooltip="true">
          <template slot-scope="scope">{{ scope.row.code ? scope.row.code : '-'}}</template>
        </el-table-column>
        <el-table-column label="积分" align="right" prop="integral" min-width="10%"></el-table-column>
        <el-table-column label="账户余额" align="right" min-width="10%">
          <template slot-scope="scope">
            {{scope.row.hasMoney.toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column label="订单总价" align="right" min-width="10%">
          <template slot-scope="scope">
            {{scope.row.totalMoney ? scope.row.totalMoney.toFixed(2) : '-'}}
          </template>
        </el-table-column>
        <el-table-column label="订单均价" align="right" min-width="10%">
          <template slot-scope="scope">
            {{scope.row.aveMoney ? scope.row.aveMoney.toFixed(2) : '-'}}
          </template>
        </el-table-column>
        <el-table-column label="消费次数" align="center" prop="totalTimes" min-width="10%"></el-table-column>
        <el-table-column label="最后消费日期" align="center" prop="lastDate" min-width="12%"></el-table-column>
      </el-table>
      <div style="width: 100%;background: #FFF;overflow: hidden;">
        <div class="table_bottom">
          <el-pagination
            :key="pageKey"
            layout="prev, pager, next, slot"
            :total="total"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-size="pageSize"
          >
            <!-- slot -->
            <vCjPageSize
              @sizeChange="handleSizeChange"
              :pageSize.sync="pageSize"
              :currentPage.sync="currentPage"
              :pageKey.sync="pageKey">
            </vCjPageSize>
          </el-pagination>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      tableHeight: 0,
      tableData: [],
      fromDate: '',
      toDate: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      pageKey: 0,
      totalTimesFrom: 3,
      totalTimesTo: 5,
      aveMoneyFrom: 5,
      aveMoneyTo: 15,
      focusDate: false,
      choose_date: 1,
      choose_times: 1,
      choose_price: 1,
      date1: new Date().format('yyyy-MM-dd'),
      date2: new Date().format('yyyy-MM-dd'),
      times1: 3,
      times2: 5,
      price1: 5,
      price2: 15,
      select_condition: false
    };
  },
  created () {
    this.tableHeight = document.body.clientHeight - 180;
    // this.pageSize = Math.floor((this.tableHeight - 50) / 50);
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.httpRequest('查询');
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // formatData() {
    //   this.date1 = new Date().format('yyyy-MM-dd');
    //   this.date2 = new Date().format('yyyy-MM-dd');
    //   this.times1 = 3;
    //   this.times2 = 5;
    //   this.price1 = 5;
    //   this.price2 = 15;
    // },
    /**
     * 点击页码
     */
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage;
      this.httpRequest('查询');
    },
    handleSizeChange() {
      this.httpRequest('查询');
    },
    saveCondition() {
      if (this.choose_date === 1 || this.choose_date === 2 || this.choose_date === 3) {
        this.date1 = new Date().format('yyyy-MM-dd');
        this.date2 = this.getBeforeDate(this.choose_date);
      }
      console.log(this.fromDate, 'this.fromDate');
      if (this.times1 === '' || this.times2 === '') {
        demo.msg('warning', '请输入消费次数');
        return;
      }
      if (this.price1 === '' || this.price2 === '') {
        demo.msg('warning', '请输入订单均价');
        return;
      }
      this.fromDate = this.date1;
      this.toDate = this.date2;
      if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
        this.date1 = this.fromDate;
        this.date2 = this.toDate;
      }
      if (this.times1 > this.times2) {
        let mid_times = this.times2;
        this.times2 = this.times1;
        this.times1 = mid_times;
      }
      if (this.price1 > this.price2) {
        let mid_price = this.price2;
        this.price2 = this.price1;
        this.price1 = mid_price;
      }
      this.totalTimesFrom = this.times1;
      this.totalTimesTo = this.times2;
      this.aveMoneyFrom = this.price1;
      this.aveMoneyTo = this.price2;
      this.currentPage = 1;
      this.httpRequest('查询', true);
      this.select_condition = false;
      // this.formatData();
    },
    getBeforeDate(n) {
      let chooseDays;
      if (n === 3) {
        chooseDays = 29;
      } else {
        chooseDays = n * 7 - 1;
      }
      var time = (new Date()).getTime() - chooseDays * 24 * 60 * 60 * 1000;
      var targetTime = new Date(time);
      targetTime = targetTime.getFullYear() + '-' + (targetTime.getMonth() > 8 ? (targetTime.getMonth() + 1) : '0' + (targetTime.getMonth() + 1)) + '-' + (targetTime.getDate() > 9 ? (targetTime.getDate()) : '0' + (targetTime.getDate()));
      console.log(targetTime, '这是一周前日期，格式为2010-01-01')
      return targetTime;
    },
    choose(str) {
      if (str === 'date') {
        this.date1 = new Date().format('yyyy-MM-dd');
        this.date2 = new Date().format('yyyy-MM-dd');
        this.choose_date = 4;
        return;
      }
      if (str === 'times1') {
        this.times1 = '';
        this.times2 = '';
        this.choose_times = 4;
      } else {
        this.price1 = '';
        this.price2 = '';
        this.choose_price = 4;
      }
      setTimeout(function() {
        $('#' + str).focus();
      }, 0);
    },
    // check(str) {
    //   if (str === 'totalTimesFrom') {
    //     this.totalTimesFrom = this.totalTimesFrom.replace(/[^0-9]/g, '');
    //     if (this.totalTimesFrom === '') {
    //       this.totalTimesFrom = 1;
    //     }
    //   }
    //   if (str === 'totalTimesTo') {
    //     this.totalTimesTo = this.totalTimesTo.replace(/[^0-9]/g, '');
    //     if (this.totalTimesTo === '') {
    //       this.totalTimesTo = 1;
    //     }
    //   }
    //   this.judge(str);
    // },
    /**
     * sonar对应
     */
    // judge(str) {
    //   if (str === 'aveMoneyFrom') {
    //     if (isNaN(this.aveMoneyFrom) || this.aveMoneyFrom === '' || Number(this.aveMoneyFrom) < 0) {
    //       this.aveMoneyFrom = '0';
    //     } else {
    //       this.aveMoneyFrom = Number(this.aveMoneyFrom) > 99999.99 ? '99999.99' : Number(this.aveMoneyFrom).toFixed(2);
    //     }
    //   }
    //   this.judge2(str);
    // },
    /**
     * sonar对应
     */
    // judge2(str) {
    //   if (str === 'aveMoneyTo') {
    //     if (isNaN(this.aveMoneyTo) || this.aveMoneyTo === '' || Number(this.aveMoneyTo) < 0) {
    //       this.aveMoneyTo = '0';
    //     } else {
    //       this.aveMoneyTo = Number(this.aveMoneyTo) > 99999.99 ? '99999.99' : Number(this.aveMoneyTo).toFixed(2);
    //     }
    //   }
    // },
    httpRequest(str, logFlg) {
      // if (this.fromDate === null) {
      //   demo.msg('warning', '请选择开始日期');
      //   return;
      // } else if (this.toDate === null) {
      //   demo.msg('warning', '请选择结束日期');
      //   return;
      // } else if (this.fromDate > this.toDate) {
      //   var mid_date = this.toDate;
      //   this.toDate = this.fromDate;
      //   this.fromDate = mid_date;
      // } else {
      //   // nothing to do
      // }
      // if (Number(this.totalTimesTo) < Number(this.totalTimesFrom) && Number(this.totalTimesFrom) > 0) {
      //   demo.msg('warning', '消费次数输入不合法');
      //   return;
      // }
      // if (Number(this.aveMoneyTo) < Number(this.aveMoneyFrom) && Number(this.aveMoneyFrom) > 0) {
      //   demo.msg('warning', '订单均价输入不合法');
      //   return;
      // }
      var url = '';
      if (str === '导出表格') {
        url = this.$rest.pc_valueAnalysisExport;
        demo.actionLog(logList.clickVipValueAnalysisExportExcel);
      } else if (str === '查询') {
        url = this.$rest.pc_valueAnalysis;
      } else {
        console.log(str);
      }
      this.loading = true;
      var sub_data = {
        systemName: $config.systemName,
        phone: this.sysUid,
        sysSid: this.sysSid,
        pageSize: this.pageSize,
        currentPage: this.currentPage,
        dateFrom: this.fromDate,
        dateTo: this.toDate,
        totalTimesFrom: Number(this.totalTimesFrom),
        totalTimesTo: Number(this.totalTimesTo),
        aveMoneyFrom: Number(this.aveMoneyFrom),
        aveMoneyTo: Number(this.aveMoneyTo)
      };
      var that = this;
      demo.$http.post(url, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          that.parseResult(res, str);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    /**
     * 对应sonar
     */
    parseResult (res, str) {
      let that = this;
      let data = res.data.data;
      if (res.data.code === '0' && str === '查询') {
        that.tableData = data.datas;
        that.total = data.count;
      } else if (res.data.code === '0' && str === '导出表格') {
        if (data.length > 0) {
          var field_mapping = {
            会员姓名: 'name',
            手机号: 'mobile',
            卡号: 'code',
            积分: 'integral',
            余额: 'hasMoney',
            订单总价: 'totalMoney',
            订单均价: 'aveMoney',
            消费次数: 'totalTimes',
            最后消费日期: 'lastDate'
          };
          data.forEach(item => {
            Object.keys(item).forEach(key => {
              if (item[key] === null) {
                item[key] = '-';
              }
            })
          });
          that.$makeExcel(data, field_mapping, '会员价值分析' + new Date().format('yyyyMMddhhmmss'));
        } else {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        }
      } else {
        demo.msg('warning', res.data.msg);
      }
    }
  },
  mounted() {
    let that = this;
    console.log('mounted');
    window.onresize = () => {
      if (that.onresize_time) {
        clearTimeout(that.onresize_time);
      }
      that.onresize_time = setTimeout(() => {
        console.log('会员价值分析···');
        that.tableHeight = document.body.clientHeight - 180;
      }, 500);
    };
  },
  destroyed() {
    window.onresize = null;
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    delayedTime: state => state.show.delayedTime,
    sysSid: state => state.show.sys_sid
  })
};
</script>
