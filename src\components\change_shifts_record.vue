<template>
  <div class="change_shifts_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left">
        <vCjDatePicker
          type="date"
          :width="300"
          :start-date.sync="fromDate"
          :end-date.sync="toDate"
          date-format="yyyy-MM-dd"
          @pickerChange="getRecordDataByDate"/>
        <el-select
          v-model="options_value"
          placeholder="收银员"
          style="margin-left:10px;width:160px;font-size:16px;color: #c0c4cc;"
          @change="pagenum = 1;getRecordData()"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div class="check-delete">
          <el-checkbox v-model="isShowDelete" @change="showDeleteChange">显示已删除记录</el-checkbox>
        </div>
      </div>
      <div class="btn_export btn_export__add"
        :style="ultimate === null ? 'opacity: 40%' : ''"
        @click="ultimate === null ? '' : isShowAddNew = true">
        新增交接班记录
      </div>
      <div class="btn_export btn_export__excel" @click="exportExcel">导出表格</div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        stripe
        :empty-text="!loading ? '暂无数据' : ' '"
        ref="table"
        :row-style="rowStyle"
      >
        <el-table-column
          prop="employeeNumber"
          label="收银员"
          align="left"
          min-width="10%"
          show-overflow-tooltip
        >
          <template slot-scope="{row}">
            {{ row.employeeNumber === '' ? '管理员':row.employeeNumber }}
          </template>
        </el-table-column>
        <el-table-column
          prop="beginDate"
          label="开始时间"
          align="center"
          min-width="15%"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="endDate"
          label="结束时间"
          align="center"
          min-width="15%"
          show-overflow-tooltip
        >
        <template slot-scope="scope">
            {{ scope.row.remark.split('&@')[1] === '（补）' ? scope.row.endDate + '（补）' : scope.row.endDate }}
        </template>
        </el-table-column>
        <el-table-column
          prop="salesAmt"
          label="销售总额"
          align="right"
          min-width="10%"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{Number(scope.row.salesAmt).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="vipCharge"
          label="会员充值"
          align="right"
          min-width="10%"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{Number(scope.row.vipCharge).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="cashAmt"
          label="应收现金"
          align="right"
          min-width="10%"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{Number(scope.row.cashAmt).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="payAmt"
          label="支付统计"
          align="right"
          min-width="10%"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{Number(scope.row.payAmt).toFixed(2)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="reviseAt"
          label="更新时间"
          align="center"
          min-width="12%"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          min-width="12%"
          align="center"
        >
          <template slot-scope="scope">
            <!-- <div
              :class="(changeNumber === '' || changeNumber === scope.row.employeeNumber) ? 'tableScope' : 'tableScope1'"
              @click="(changeNumber === '' || changeNumber === scope.row.employeeNumber) ? gotoChange(scope.row) : ''">
              查看详情
            </div> -->
            <div class="operate_container">
              <div
                :key="'button' + index"
                class="button-item"
                v-show="isShowItem(scope.row, index)"
                v-for="(item, index) in buttonItems"
                :class="getColor(scope.row, item, index)"
                @click="gotoChange(scope.row, index)">
                {{getItemName(scope.row, index, item)}}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <div style="color:#567485;width:68%">
          共<span>{{exTotal}}</span>条记录(不包含已删除)
          ，合计应收现金：<span>¥{{towNumber(totalData.cashAmtTotal)}}</span>
          ，销售总额：<span>¥{{towNumber(totalData.salesAmtTotal)}}</span>
          ，会员充值：<span>¥{{towNumber(totalData.vipChargeTotal)}}</span>
          ，支付统计：<span>¥{{towNumber(totalData.payAmtTotal)}}</span>
        </div>
        <div>
          <el-pagination
            v-if="showPage"
            :key="pageKey"
            layout="prev, pager, next, slot"
            :total="total"
            @current-change="handleCurrentChange"
            :current-page.sync="pagenum"
            :page-size="limit"
          >
            <!-- slot -->
            <vCjPageSize
              @sizeChange="handleSizeChange"
              :pageSize.sync="limit"
              :currentPage.sync="pagenum"
              :pageKey.sync="pageKey">
            </vCjPageSize>
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog
      title="新增交接班记录"
      :visible.sync="isShowAddNew"
      append-to-body
      @open='initData'
      @close='isShowAddNew = false'
      width="440px"
      destroy-on-close
      :close-on-click-modal='false'
    >
      <div class="dia-main">
        <div class="dia-main_item">
          <div class="dia-main_item__left">
            收银员
          </div>
          <div class="dia-main_item__right">
            <el-select v-if="+loginInfo.uid === 1" v-model="employee" placeholder="请选择">
              <el-option
                v-for="item in employeeList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <div v-else style="font-size: 16px;">
              {{getName()}}
            </div>
          </div>
        </div>
        <div class="dia-main_item">
          <div class="dia-main_item__left">
            开始时间
          </div>
          <div class="dia-main_item__right">
            <el-date-picker
              v-model="startDate"
              :picker-options="picker"
              type="datetime"
              placeholder="选择时间">
            </el-date-picker>
          </div>
        </div>
        <div class="dia-main_item">
          <div class="dia-main_item__left">
            结束时间
          </div>
          <div class="dia-main_item__right">
            <el-date-picker
              v-model="endDate"
              :picker-options="picker"
              type="datetime"
              placeholder="选择时间">
            </el-date-picker>
          </div>
        </div>
        <div class="dia-main_button" @click="shiftsRecordInsert">
          确定
        </div>
      </div>
    </el-dialog>
    <div v-show="isShowDia" class="dia-del">
      <div class="tips_dialog dia-del_main">
        <div class="title">提示</div>
        <div class="content">{{diaMsg}}</div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            style="background:#567485"
            @click="isShowDia = false"
          >取消</div>
          <div
            class="btn"
            style="background:#BDA169;margin-left:30px"
            @click="comSure"
          >确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Select, DatePicker, Table, TableColumn, Pagination } from 'element-ui';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
import vCjDatePicker from '@/common/components/CjDatePicker';
export default {
  components: {
    [Select.name]: Select,
    [DatePicker.name]: DatePicker,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    vCjPageSize,
    vCjDatePicker
  },
  data () {
    return {
      loading: false,
      picker: {
        disabledDate: (time) => {
          return time.getTime() >= new Date().getTime() + 1000
        }
      },
      options: [{
        value: '-101',
        label: '全部'
      }],
      showSum: false,
      isShowDelete: false,
      isShowAddNew: false,
      isShowDia: false,
      diaMsg: '确定删除这条记录吗？',
      mainRow: {},
      exTotal: 0,
      clickIndex: 0,
      buttonItems: [
        {
          name: '详情',
          oName: '补班',
          class: 'theme'
        },
        {
          name: '校准',
          oName: '校准',
          class: 'theme'
        },
        {
          name: '删除',
          oName: '恢复',
          class: 'red'
        }
      ],
      employee: '',
      employeeList: [],
      startDate: '',
      endDate: '',
      options_value: '-101',
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      tableHeight: 0,
      tableData: [],
      totalData: [],
      showPage: true,
      pageKey: 0,
      pagenum: 1,
      limit: 10,
      total: 0
    };
  },
  created () {
    demo.actionLog(logList.clickChangeShiftRecord);
    this.tableHeight = screen.availHeight - 228;
    // this.limit = Math.floor(this.tableHeight / 50);
    this.SET_SHOW({ changeNumber: this.$store.state.show.loginInfo.employeeNumber });
    this.SET_SHOW({ changeName: this.$store.state.show.loginInfo.name });
    this.SET_SHOW({ isHasEndDate: false });
  },
  updated () {
    this.$nextTick(() => {
      this.$refs['table'].doLayout();
    });
  },
  mounted () {
    let today = new Date();
    this.toDate = today.format('yyyy-MM-dd');
    this.fromDate = new Date(today.setDate(today.getDate() - 7)).format('yyyy-MM-dd');
    if (this.changeShiftsRecordCondition) {
      Object.keys(this.changeShiftsRecordCondition).forEach(key => {
        this[key] = this.changeShiftsRecordCondition[key];
        if (key === 'pagenum') {
          this.showPage = false;
        }
      });
    }
    this.getIsShowDelete();
    this.getShopUsersData();
    this.getRecordData();
    this.getEmployeeList();
    this.SET_SHOW({ isHasEndDate: false });
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getIsShowDelete() {
      const data = $setting.info ? demo.t2json($setting.info) : '';
      if (data && data.isShowDelete) {
        this.isShowDelete = data.isShowDelete;
      }
    },
    // 显示已删除记录
    showDeleteChange() {
      let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      dataInfo.isShowDelete = this.isShowDelete;
      let data = [];
      data.push({ key: 'info', value: JSON.stringify(dataInfo), remark: '公共参数' });
      settingService.put(data, () => {
        // todo
      }, () => {
        // todo
      });
      this.getRecordData('box');
    },
    initData() {
      this.endDate = new Date();
      this.startDate = new Date().format('yyyy-MM-dd 00:00:00');
      this.employee = +this.loginInfo.uid;
    },
    getEmployeeList() {
      clerkService.selectAll(res => {
        this.employeeList = [];
        for (let user of demo.t2json(res)) {
          this.employeeList.push({
            value: user.uid,
            label: user.uid === 1 ? '管理员' : `${user.name}（${user.employeeNumber}）`,
            name: user.uid === 1 ? '管理员' : user.name,
            employeeNumber: user.employeeNumber
          });
        }
      });
    },
    getName() {
      return `${$userinfo.name}(${$userinfo.employeenumber})`;
    },
    checkItem() {
      if (demo.isNullOrTrimEmpty(this.startDate)) {
        demo.msg('warning', '请选择开始时间');
        return false;
      }
      this.startDate = new Date(Date.parse(this.startDate));
      if (demo.isNullOrTrimEmpty(this.endDate)) {
        demo.msg('warning', '请选择结束时间');
        return false;
      }
      if (this.startDate >= this.endDate) {
        demo.msg('warning', '开始时间不能大于结束时间');
        return false;
      }
      if (this.startDate.getTime() > new Date().getTime()) {
        demo.msg('warning', '开始时间不能大于当前时间');
        return false;
      }
      if (this.endDate.getTime() > new Date().getTime()) {
        demo.msg('warning', '结束时间不能大于当前时间');
        return false;
      }
      return true;
    },
    shiftsRecordInsert() { // 新增交接班记录
      if (!this.checkItem()) {
        return;
      }
      this.gotoNewDetail();
    },
    gotoNewDetail() {
      const start = this.startDate.format('yyyy-MM-dd hh:mm:ss');
      const end = this.endDate.format('yyyy-MM-dd hh:mm:ss');
      let name = '';
      let number = '';
      this.employeeList.filter(t => {
        if (t.value === this.employee) {
          name = t.name;
          number = t.employeeNumber;
        }
      })
      this.isShowAddNew = false;
      this.SET_SHOW({changeNumber: number === '管理员' ? '' : number, changeName: name, loginTime: start, endDate: end, isChangeShifts: true});
      this.SET_SHOW({changeUid: this.employee, isRepair: true, changeFrom: 'changeShiftsRecord', isNewChange: true, isChangeShiftsRecord: false});
    },
    isShowItem(row, index) {
      if (index === 0 && !this.$employeeAuth('show_details')) {
        return false;
      }
      if (index === 2 && this.employeeAuth.indexOf('delete_recover_shift_records') === -1) {
        return false;
      }
      if (row.beginDate === $setting.userlastime) {
        return false;
      }
      let info1 = row.info1;
      if (info1) {
        info1 = demo.t2json(info1);
        if (info1.isDel === 1 && (index === 0 || index === 1)) {
          return false;
        }
      }
      return true;
    },
    getColor(row, item, index) {
      if (!this.isSelf(row) && index === 0) {
        return 'disabled';
      }
      if (index === 1 && !row.endDate) {
        return 'hidden';
      }
      let info1 = row.info1;
      if (info1) {
        info1 = demo.t2json(info1);
        if (info1.isDel === 1 && item.oName === '恢复') {
          return 'theme';
        }
      }
      return item.class;
    },
    rowStyle(data) {
      let info1 = data.row.info1;
      if (info1) {
        info1 = demo.t2json(info1);
        if (info1.isDel === 1 && !demo.isNullOrTrimEmpty(data.rowIndex)) {
          return {color: '#B2C3CD'};
        }
      }
    },
    isSelf(row) {
      if (this.changeNumber !== '' && this.changeNumber !== row.employeeNumber) {
        return false;
      }
      return true;
    },
    getItemName(row, index, item) {
      if (index === 0 && !row.endDate) {
        return item.oName;
      }
      if (index === 2) {
        let info1 = row.info1;
        if (info1) {
          info1 = demo.t2json(info1);
          if (info1.isDel === 1) {
            return item.oName;
          }
        }
        return item.name;
      }
      return item.name;
    },
    gotoChange (row, index) {
      this.diaMsg = '确定删除这条记录吗？';
      row.isDel = 0;
      this.mainRow = row;
      this.clickIndex = index;
      if (index === 0) {
        this.setIndexZero(row);
      } else if (index === 1) {
        if (!pos.network.isConnected() || this.$store.state.show.network === false) {
          demo.msg('warning', '当前网络连接异常，请恢复后重试');
          return;
        }
        this.diaMsg = '将更新当前班次的销售数据，确定要校准吗？';
        this.isShowDia = true;
      } else {
        let info1 = row.info1;
        if (info1) {
          info1 = demo.t2json(info1);
          row.isDel = demo.isNullOrTrimEmpty(info1.isDel) ? 0 : info1.isDel;
          if (info1.isDel === 1) {
            this.diaMsg = '确定恢复这条记录吗？'
          }
        }
        this.isShowDia = true;
      }
    },
    setIndexZero(row) {
      if (!row.endDate) {
        if (!this.isSelf(row)) {
          return;
        }
        this.diaMsg = '确定补班吗？';
        this.isShowDia = true;
      } else {
        this.gotoDetail(row);
      }
    },
    gotoDetail (row) { // 查看详情
      if (!this.isSelf(row)) {
        return;
      }
      let that = this;
      let param = {
        uid: row.uid,
        start: row.beginDate
      };
      changeShiftsService.getShiftHistories(param, res => {
        this.isShowDia = false;
        that.SET_SHOW({ isHasEndDate: false, changeId: row.id });
        if (row.endDate !== '' && row.endDate !== null) {
          that.SET_SHOW({ endDate: row.endDate.replace(/-/g, '.') });
          that.SET_SHOW({ isRepair: false });
          that.SET_SHOW({ isHasEndDate: true });
          that.SET_SHOW({ changeShiftRemark: row.remark });
        } else {
          if (demo.t2json(res).length === 0) {
            that.SET_SHOW({ endDate: new Date().format('yyyy.MM.dd hh:mm:ss') });
            that.SET_SHOW({ isRepair: false });
            if (that.changeNumber === '' && that.changeNumber !== row.employeeNumber) {
              that.SET_SHOW({ endDate: that.tableData[0].beginDate });
              that.SET_SHOW({ isRepair: true });
            }
          } else {
            that.SET_SHOW({ endDate: demo.t2json(res)[0].endDate.replace(/-/g, '.') });
            that.SET_SHOW({ isRepair: true });
          }
        }
        that.SET_SHOW({ changeFrom: 'changeShiftsRecord' });
        that.SET_SHOW({ changeNumber: row.employeeNumber });
        that.SET_SHOW({ changeName: row.name });
        that.SET_SHOW({ loginTime: row.beginDate });
        that.SET_SHOW({ changeFingerprint: row.fingerprint });
        that.SET_SHOW({ changeUid: row.uid });
        that.SET_SHOW({ isChangeShifts: true, isNewChange: false });
        that.SET_SHOW({ isChangeShiftsRecord: false });
      }, () => {
        this.isShowDia = false;
      });
    },
    comSure() {
      if (this.clickIndex === 0) {
        this.gotoDetail(this.mainRow);
      } else if (this.clickIndex === 1) {
        this.sync(this.mainRow);
      } else {
        this.gotoDel();
      }
    },
    gotoDel() { // 删除or恢复交接班记录
      const param = {
        isDel: this.mainRow.isDel === 0 ? 1 : 0,
        id: this.mainRow.id
      }
      shiftHistoryService.deleteOrReply(param).then(() => {
        demo.msg('success', this.mainRow.isDel === 0 ? '删除成功' : '恢复成功');
        this.isShowDia = false;
        this.getRecordData();
      }).catch(() => {
        demo.msg('success', this.mainRow.isDel === 0 ? '删除成功' : '恢复成功');
        this.isShowDia = false;
      });
    },
    sync (row) {
      // 如果正在执行云同步，则不继续执行
      if (+this.isSyncing > 0) {
        return;
      }
      if (!pos.network.isConnected() || this.$store.state.show.network === false) {
        this.SET_SHOW({load_message: '正在退出，请稍后……'});
      } else {
        this.SET_SHOW({load_message: '正在云同步，请稍后……'});
      }
      this.SET_SHOW({isSyncingLogin: true});
      syncService.clearDataInfo(() => {
        this.syncCallBack(row);
      }, () => {
        this.syncCallBack(row);
      });
    },
    syncCallBack(row) {
      this.SET_SHOW({ isSyncingLogin: false });
      this.checkChange(row);
      this.isShowDia = false;
    },
    checkChange(row) {
      const params = {
        action: 4,
        uid: row.uid,
        shiftHistoryId: row.id,
        start: row.beginDate,
        end: row.endDate
      }
      shiftHistoryService.save(params).then(() => {
        demo.msg('success', '校准成功');
        this.getRecordData();
      }).catch(err => {
        demo.msg('warning', err);
      });
    },
    getRecordDataByDate () {
      this.pagenum = 1;
      let fromCheck = this.fromDate !== null && this.fromDate !== '';
      let toCheck = this.toDate !== null && this.toDate !== '';
      if (fromCheck && toCheck && this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      }
      if (fromCheck && toCheck) {
        this.getRecordData();
      }
      if (!fromCheck && !toCheck) {
        this.getRecordData();
      }
    },
    /**
     * 点击页码
     */
    handleCurrentChange (pagenum) {
      this.pagenum = pagenum;
      this.getRecordData();
    },
    handleSizeChange() {
      this.getRecordData();
    },
    /**
     * 获取管理员+店员数据
     */
    getShopUsersData () {
      clerkService.selectAll(res => {
        let users = demo.t2json(res);
        for (let user of users) {
          this.options.push({
            value: user.uid,
            label: user.uid === 1 ? '管理员' : user.employeeNumber
          });
        }
      });
    },
    /**
     * 获取列表数据
     */
    getRecordData (type) {
      this.showSum = false;
      this.loading = true;
      let param = {
        uid: this.options_value === '-101' ? '' : this.options_value,
        beginDate: this.fromDate,
        endDate: this.toDate,
        limit: this.limit,
        offset: (this.pagenum - 1) * this.limit,
        showDelete: this.isShowDelete
      };
      this.reportFormLog(_.cloneDeep(param), '交接班记录查询');
      shiftHistoryService.query(param, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        if (type === 'box' && demo.t2json(res).datas && !demo.t2json(res).datas.length && this.pagenum !== 1) {
          this.pagenum--;
          this.getRecordData('box');
          return;
        }
        console.log('交接班记录', demo.t2json(res).datas);
        this.tableData = demo.t2json(res).datas;
        this.totalData = demo.t2json(res).total;
        this.total = Number(demo.t2json(res).deleteCount);
        this.exTotal = Number(demo.t2json(res).excludeDeleteCount);
        this.showSum = true;
        this.showPage = true;
      }, err => {
        demo.msg('warning', err);
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      });
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'change_shifts_record', action: 'reportFormLog', description});
      }
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : 0;
    },
    itemFormat(item) {
      item.employeeNumber = item.employeeNumber === '' ? '管理员' : item.employeeNumber;
      item.endDate = item.remark.split('&@')[1] === '（补）' ? item.endDate + '（补）' : item.endDate;
      item.salesAmt = isNaN(item.salesAmt) ? item.salesAmt : Number(item.salesAmt);
      item.payAmt = isNaN(item.payAmt) ? item.payAmt : Number(item.payAmt);
      item.vipCharge = isNaN(item.vipCharge) ? item.vipCharge : Number(item.vipCharge);
      item.cashAmt = isNaN(item.cashAmt) ? item.cashAmt : Number(item.cashAmt);
    },
    /**
     * 导出Excel数据
     */
    exportExcel() {
      let param = {
        uid: this.options_value === '-101' ? '' : this.options_value,
        beginDate: this.fromDate,
        endDate: this.toDate,
        limit: 65535,
        offset: 0,
        opt: 'import',
        showDelete: this.isShowDelete
      };
      this.reportFormLog(_.cloneDeep(param), '交接班记录表格导出');
      shiftHistoryService.query(param, res => {
        console.log('导出报表记录', demo.t2json(res).datas);
        let arr = demo.t2json(res).datas;
        if (arr && arr.length > 0) {
          for (let item of arr) {
            this.itemFormat(item);
          }
          let field_mapping = {
            收银员: 'employeeNumber',
            开始时间: 'beginDate',
            结束时间: 'endDate',
            销售总额: 'salesAmt',
            会员充值: 'vipCharge',
            应收现金: 'cashAmt',
            支付统计: 'payAmt',
            更新时间: 'reviseAt',
            '': 'status'
          };
          this.$makeExcel(arr, field_mapping, '交接班记录' + new Date().format('yyyyMMddhhmmss'));
          return;
        }
        demo.msg('warning', '暂无可导出的数据');
      });
    }
  },
  watch: {
    isChangeShifts() {
      if (!this.isChangeShifts) {
        this.getRecordData();
      }
    }
  },
  computed: mapState({
    loginInfo: state => state.show.loginInfo,
    ultimate: state => state.show.ultimate,
    isChangeShifts: state => state.show.isChangeShifts,
    employeeAuth: state => state.show.employeeAuth,
    changeShiftsRecordCondition: state => state.show.changeShiftsRecordCondition,
    changeNumber: state => state.show.changeNumber,
    delayedTime: state => state.show.delayedTime,
    changeName: state => state.show.changeName
  }),
  beforeDestroy() {
    if (this.isChangeShifts) { // 跳到查看详情
      console.log('跳到查看详情');
      let condition = {
        fromDate: this.fromDate,
        toDate: this.toDate,
        options_value: this.options_value,
        pagenum: this.pagenum
      }
      this.SET_SHOW({changeShiftsRecordCondition: condition});
    } else {
      console.log('返回报表选择');
      this.SET_SHOW({changeShiftsRecordCondition: null});
    }
  }
};
</script>

<style lang='less' scoped>
.borderColor {
  border-color: @themeBackGroundColor;
}
.borderColor1 {
  border-color: #E3E6EB;
}
.tableScope {
  color:  @themeBackGroundColor;
  cursor: pointer;
}
.tableScope1 {
  color: #B2C3CD;
}

/deep/ .el-tooltip{max-width:100%}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  font-size: 16px;
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-select-dropdown__item.selected {
  color: @themeBackGroundColor !important;
}
/deep/ .el-range-editor.el-input__inner {
  border-radius: 50px;
}
/deep/ .el-date-editor .el-range__icon {
  display: none;
}
/deep/ .el-range-editor .el-range-input {
  margin-left: 12px;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-range-separator {
  color: rgba(177, 195, 205, 100);
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input--suffix .el-input__inner {
  color: @themeFontColor;
}
.change_shifts_record_container{
  background: #F5F8FB;
  .top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding-left: 10px;
    .top_left{
      display: flex;
      align-items: center;
      .top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
    .btn_export{
      cursor: pointer;
      width: 110px;
      height: 44px;
      line-height: 44px;
      margin-right: 10px;
      font-weight: 700;
      border-radius: 22px;
      text-align: center;
      color: #FFFFFF;
      font-size: 18px;
      &__excel {
        color: @themeBackGroundColor;
        border: 1px solid @themeBackGroundColor;
      }
      &__add {
        color: #FFFFFF;
        background: @themeBackGroundColor;
        width: 164px!important;
        position: absolute;
        right: 125px;
      }
    }
    .top_right{
      display: flex;
      align-items: center;
      .top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .table_bottom{
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding-left: 12px;
      padding-right: 30px;
      background: white;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  display: flex;
  align-items: center;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    background: #FFFFFF;
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  padding-right: 0;
}
/deep/ .el-table th > .cell {
  padding-left:10px;
  padding-right:20px;
}
/deep/ .el-table tr {
  color: @themeFontColor;
}
/deep/ .el-table td {
  padding-left:10px;
  padding-right:20px;
}
/deep/ .el-table .cell {
  padding-left:0;
  padding-right:0;
}
.check-delete {
  display: inline-block;
  margin-left: 20px;
  /deep/.el-checkbox__label {
    font-size: 16px;
    color: @themeFontColor;
    vertical-align: middle;
  }
}
.operate_container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .theme {
    color: @themeBackGroundColor;
  }
  .red {
    color: #F64C4C;
  }
  .disabled {
    color: #B2C3CD;
    cursor: no-drop;
  }
  .deleted {
    color: #B2C3CD;
  }
  .hidden {
    visibility: hidden;
  }
}
.button-item {
  cursor: pointer;
}
.dia-main {
  color: @themeFontColor;
  &_item {
    display: inline-block;
    margin-bottom: 22px;
    &__left {
      display: inline-block;
      color: #B2C3CD;
      font-weight: 500;
      font-size: 18px;
      width: 80px;
    }
    &__right {
      display: inline-block;
      /deep/.el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 100%;
      }
      /deep/.el-input__inner {
        color: @themeFontColor;
        border-radius: 4px;
        width: 310px;
        padding: 0 15px;
      }
    }
  }
  &_button {
    background: #BDA169;
    border-radius: 6px;
    width: 100%;
    color: #FFFFFF;
    text-align: center;
    font-weight: 700;
    font-size: 20px;
    height: 52px;
    line-height: 52px;
    cursor: pointer;
  }
}
/deep/.el-dialog__title {
  color: @themeFontColor;
  font-weight: 700;
}
/deep/.el-dialog {
  border-radius: 4px;
}
/deep/.el-dialog__header {
  border-bottom: 1px solid #E3E6EB;
  padding: 20px 0px 10px;
  margin: 0px 20px;
}
/deep/.el-dialog__headerbtn .el-dialog__close {
  color: #8298A6;
  font-size: 22px;
  font-weight: bold;
}
/deep/.el-dialog__body {
  padding-bottom: 20px;
}
/deep/.el-date-editor--date {
  width: 100%;
}
.dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 50px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
  }
.dia-del {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0,0,0,.5);
  z-index: 999999;
  &_main {
    width: 450px;
    margin: 215px auto !important;
    background: #FFF;
    border-radius: 6px;
    overflow: hidden;
    padding: 0 50px;
  }
}
.tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  .title{
    color: @themeFontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @themeFontColor;
    font-size: 24px;
    margin-top: 28px;
    text-align: center;
  }
  .dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 50px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
  }
}
</style>
