const ip = external.getApiUrl('ip');
const userUrl = external.getApiUrl('userUrl');
const productUrl = external.getApiUrl('productUrl');
const posUrl = external.getApiUrl('posUrl');
const agentUrl = external.getApiUrl('agentUrl');
const settingUrl = external.getApiUrl('settingUrl');
const vipUrl = external.getApiUrl('vipUrl') + '/v1';
const payUrl = external.getApiUrl('payUrl');
const pushUrl = external.getApiUrl('pushUrl');
const advertUrl = external.getApiUrl('advertUrl');
const webSocketUrl = external.getApiUrl('webSocketUrl');
const billUrl = external.getApiUrl('billUrl');
const weChatUrl = external.getApiUrl('weixinUrl');
const zgcmUrl = external.getApiUrl('zgcmUrl');
global.sql_count = 500;
const rest = {
  version: external.getVersion(),
  webSocketUrl: webSocketUrl,
  // 手机
  select: '/VueDemo/select',
  insert: '/VueDemo/insert',
  delete: '/VueDemo/delete',

  clearsCheck: userUrl + '/data-base/clears/check',
  clearHistory: userUrl + '/clear-histories',
  clearModuleList: userUrl + '/clear-modules',
  lastClearHistoryId: userUrl + '/clear-histories/last',
  listFromId: userUrl + '/clear-histories/list/from-id',
  clearsSpecificTable: productUrl + '/db/clears-specific-table',
  clearVips: vipUrl + '/db/clears',
  clearPos: posUrl + '/db/clears',
  clearProducts: productUrl + '/db/clears',
  clearUsers: userUrl + '/data-base/clears',
  getInitData: productUrl + '/db/init/data',

  clerks: userUrl + '/shopusers',
  // 查询用户免费到期后是否可以继续使用
  continuedUse: settingUrl + '/free/partition/continuedUse',

  // 退货退款云端接口
  pc_refund: posUrl + '/refund-order/refund',
  // 获取退款订单号接口
  pc_generate_order: posUrl + '/refund-order/generate-order',
  getRefundFingerprint: posUrl + '/refund-order/get-refund-fingerprint',
  // 会员退款积分检查
  check_refund_integral: vipUrl + '/vip/check-refund-integral',
  // 规格
  getSpecs: productUrl + '/specs/getSpecs',
  updateSpecs: productUrl + '/specs/updateSpecs',
  saveSpecs: productUrl + '/specs/saveSpecs',
  getTemplateSpecs: productUrl + '/specs/getTemplateSpecs',
  updateSpecById: productUrl + '/specs/updateSpecById',
  batchImport: productUrl + '/products/batch/imports',
  // getaid: posurl + '/payapply/getaid',
  getaid: billUrl + '/payAid/searchPayAid',
  // 获取支付宝认证状态
  getZfbAuth: billUrl + '/merchant/zfb-auth-status',
  // getaid: posurl + '/bill/payAid/searchPayAid',
  pc_vipList: vipUrl + '/vip/getVips',
  pc_getVipById: vipUrl + '/vip/getVipById',
  pc_addvip: vipUrl + '/vip/addVip',
  pc_editvip: vipUrl + '/vip/editVip',
  pc_refundvip: vipUrl + '/vip/refund',
  pc_checkUserIsExit: userUrl + '/user/check/user',
  pc_settingInit: settingUrl + '/user/init',
  pc_userDbInit: userUrl + '/data-base/create',
  pc_initProdDb: productUrl + '/db/init',
  pc_initPosDb: posUrl + '/db/init',
  pc_initVipDb: vipUrl + '/db/init',
  pc_addShop: userUrl + '/shop/addShop',
  pc_getLastPay: vipUrl + '/vip/getLastPay',
  pc_getVipTopUpDetailById: vipUrl + '/vip/getVipTopUpDetailById',
  pc_deleteOrRecoveryVip: vipUrl + '/vip/deleteOrRecoveryVip',
  pc_addvipDetails: vipUrl + '/vip/addVipDetails',
  getVipTradeStatus: vipUrl + '/vip/vip-details',
  pc_showvipDetails: vipUrl + '/vip/getVipDetails',
  pc_showvipReports: vipUrl + '/vip/getVipReports',
  pc_createVipDetails: vipUrl + '/vip/createVipDetails',
  pc_getVipByCode: vipUrl + '/vip/getVipByCode',
  pc_searchMemberPay: vipUrl + '/vip/searchMemberPay',
  vipBatchImport: vipUrl + '/vip/batch/import',
  pc_productsUpExcel: vipUrl + '/products/upExcel',
  pc_vipStatistics: vipUrl + '/vip/vipStatistics',
  pc_getVipProducts: vipUrl + '/productExchange/getProducts',
  pc_pointProcess: vipUrl + '/vipPoint/pointProcess',
  pc_exchangeProduct: vipUrl + '/productExchange/exchangeProduct',
  pc_createQrCodeUrl: pushUrl + '/notify/wechat/createQrCodeUrl',
  pc_getRestCardsAndTimes: vipUrl + '/timesCard/getRestCardsAndTimes',
  pc_valueAnalysis: vipUrl + '/vipReports/valueAnalysis',
  pc_valueAnalysisExport: vipUrl + '/vipReports/valueAnalysisExport',
  pc_getVipGrowthList: vipUrl + '/vipReports/getVipGrowthList',
  pc_getVipConsumeList: vipUrl + '/vipReports/getVipConsumeList',
  pc_getVipConsumeListExport: vipUrl + '/vipReports/getVipConsumeListExport',

  pc_searchVipRechargeList: vipUrl + '/vipReports/searchVipRechargeList',
  pc_searchVipRechargeListExport: vipUrl + '/vipReports/searchVipRechargeListExport',
  pc_searchVipRechargeTotalsStatistics: vipUrl + '/vipReports/searchVipRechargeTotalsStatistics',
  pc_searchVipRechargeTotalsStatisticsExport: vipUrl + '/vipReports/searchVipRechargeTotalsStatisticsExport',
  pc_getVipPointUseDetail: vipUrl + '/vipReports/getVipPointChangeDetail',
  pc_getVipPointUseDetailExport: vipUrl + '/vipReports/getVipPointUseDetailExport',
  pc_deleteOrRecoveryVipList: vipUrl + '/vip/deleteOrRecoveryVipList/', // 会员批量注销或激活接口

  // 会员设置
  pc_settingVip: vipUrl + '/vipSetting/updVipPointSetting',
  pc_vipGetSetting: vipUrl + '/vipSetting/getVipPointSetting170',
  pc_vipGetGoods: vipUrl + '/productNopoint/getProductNoPoint',
  pc_vipSetGoods: vipUrl + '/productNopoint/addProductNoPoints',
  pc_vipDelGoods: vipUrl + '/productNopoint/delProductNoPoint',
  pc_getProductVipDay: vipUrl + '/productVipday/getProductVipDay',
  pc_addProductVipDay: vipUrl + '/productVipday/addProductVipDay',
  pc_delProductVipDay: vipUrl + '/productVipday/delProductVipDay',
  pc_addProducts: vipUrl + '/productExchange/addProducts',
  pc_getProducts: vipUrl + '/productExchange/getProducts',
  pc_editProduct: vipUrl + '/productExchange/editProduct',
  pc_delProducts: vipUrl + '/productExchange/delProducts',
  pc_getTimesCard: vipUrl + '/timesCard/getTimesCard',
  pc_addTimesCard: vipUrl + '/timesCard/addTimesCard',
  pc_delTimesCard: vipUrl + '/timesCard/delTimesCard',
  pc_getTimesCardPurchase: vipUrl + '/timesCardPurchase/getTimesCardPurchase',
  pc_addTimesCardSales: vipUrl + '/timesCardSales/addTimesCardSales',
  pc_editVipPoints: vipUrl + '/vip/editVipPoints',
  pc_batchUpdateDiscount: vipUrl + '/vip/disc',
  pc_depositSurplus: vipUrl + '/accessProduct/depositSurplus',
  // 群客多
  // createSnCode: pushUrl + '/notify/wechat/createQrCodeUrl',
  checkUserExit: userUrl + '/user/qkd/checkUser',
  qkdSettingSearch: settingUrl + '/setting/search', // 查询绑定状态
  qkdSettingUpdate: settingUrl + '/setting/update', // 更新绑定状态
  msRegisterSearch: weChatUrl + '/setting/zgzn/register/search', // 查询微店注册状态
  msRegister: weChatUrl + '/setting/zgzn/register/qkd', // 注册微店
  msBoundSearch: weChatUrl + '/setting/zgzn/bound/search', // 查询微店绑定状态
  msBoundUpdate: weChatUrl + '/setting/zgzn/bound/update', // 微店绑定/解绑
  msPriceUpdate: weChatUrl + '/setting/zgzn/price/update', // 微店同期商品卖价开关
  // 企业微信
  getWeComQrCode: pushUrl + '/notify/wecom/getWeComQrCode',
  getWeComUrl: pushUrl + '/notify/wecom/getWeComUrl',
  // 云同步
  syncDownCount: 500,
  syncUpCount: 100,
  syncCheck: userUrl + '/sync/syncCheck',
  syncEnd: userUrl + '/sync/syncEnd',
  upProducts: productUrl + '/sync/upProducts',
  downProducts: productUrl + '/sync/downProducts',
  downProductsAttributes: productUrl + '/sync/downProductsAttributes',
  upProductsExtBarcode: productUrl + '/sync/up-products-ext-barcode',
  downProductsExtBarcode: productUrl + '/sync/down-products-ext-barcode',
  upProductCompanies: productUrl + '/sync/up/product-companies',
  downProductCompanies: productUrl + '/sync/down/product-companies',
  downPics: productUrl + '/sync/downPics',
  upTypes: productUrl + '/sync/upTypes',
  downTypes: productUrl + '/sync/downTypes',
  downTypesNew: productUrl + '/sync/down-types', // 新商品分类云同步接口
  upUnits: productUrl + '/sync/upUnits',
  downUnits: productUrl + '/sync/downUnits',
  upSysInfo: userUrl + '/sync/upSysInfo',
  downSysInfo: userUrl + '/sync/downSysInfo',
  downProductsStock: posUrl + '/sync/downProductsStock',
  upInventories: posUrl + '/sync/upInventories',
  downInventories: posUrl + '/sync/downInventories',
  // upSales: posUrl + '/sync/upSales',
  upSales: posUrl + '/sync/up/sales',
  downSales: posUrl + '/sync/downSales',
  'down-sales': posUrl + '/sync/down-sales?startSyncAt={startSyncAt}&endSyncAt={endSyncAt}',
  upPurs: posUrl + '/sync/upPurs',
  downPurs: posUrl + '/sync/downPurs',
  upCompanies: posUrl + '/sync/upCompanies',
  downCompanies: posUrl + '/sync/downCompanies',
  // upLogPay: posurl + '/sync/upLogPay',
  getSyncDataUsers: userUrl + '/sync/getSyncData',
  getSyncDataProducts: productUrl + '/sync/getSyncData',
  getSyncDataPos: posUrl + '/sync/getSyncData',
  downShiftHistories: userUrl + '/sync/downShiftHistories',
  upShiftHistories: userUrl + '/sync/upShiftHistories',
  downRecordBills: posUrl + '/sync/downRecordBills',
  upRecordBills: posUrl + '/sync/upRecordBills',
  downScaleList: productUrl + '/sync/downScaleList',
  upProductScale: productUrl + '/sync/upProductScale',
  downProductScale: productUrl + '/sync/downProductScale',
  upSendscale: productUrl + '/sync/upSendscale',
  downSendscale: productUrl + '/sync/downSendscale',
  upSendscaleProduct: productUrl + '/sync/upSendscaleProduct',
  downSendscaleProduct: productUrl + '/sync/downSendscaleProduct',
  upSendscaleHistory: productUrl + '/sync/upSendscaleHistory',
  qrcode: ip + '/newpayapply/#/',
  upload: ip,
  uploadImage: productUrl + '/file/upload',
  getProducts: productUrl + '/products/getProducts',
  getBarcodes: productUrl + '/products/barcode',
  updPassword: userUrl + '/user/updPassword',
  getDeviceCode: posUrl + '/kvs/getDeviceCode',
  sendVerifyCode: pushUrl + '/sms/sendVerifyCode',
  checkVerifyCode: pushUrl + '/sms/checkVerifyCode',
  resetPassword: userUrl + '/user/resetPassword',
  login: userUrl + '/user/login',
  loginEmployee: posUrl + '/user/loginEmployee',
  checkCode: agentUrl + '/activationcodes/getHighestVersion',
  updateCode: agentUrl + '/activationcodes/activationCodeVerify',
  saveTrialCode: agentUrl + '/activationcodes/saveTrialCode',
  register: settingUrl + '/user/register',
  pc_personMsg: userUrl + '/user/updShopUser',
  getBroadcastNews: posUrl + '/notice/getBroadcastNews',
  qcpay: payUrl + '/pay/activePay',
  qcQuery: payUrl + '/pay/qcQuery',
  pcRefundQuery: payUrl + '/refund/refundQuery',
  qcTradePay: payUrl + '/pay/microPay',
  // 设备信息
  getDevice: posUrl + '/device/getDevice',
  updDevice: posUrl + '/device/updDevice',
  // logs
  addLogs: settingUrl + '/logs/add',
  getAuthority: userUrl + '/employee/getAuthority',
  getUsersAuthorities: userUrl + '/employee/getUsersAuthorities',
  getAuthorities: userUrl + '/employee/getAuthorities',
  getEmployeeList: userUrl + '/employee/getEmployeeList',
  addEmployee: userUrl + '/employee/addEmployee',
  // 新版编辑员工
  updateEmployee: userUrl + '/employee/editEmployee',
  deleteEmployeeByUserId: userUrl + '/employee/deleteEmployeeByUserId',

  // 次卡相关接口
  addAccessProduct: vipUrl + '/accessProduct/addAccessProduct',
  getAccessProducts: vipUrl + '/accessProduct/getAccessProducts',
  getAccessProductReports: vipUrl + '/accessProduct/getAccessProductReports',
  getAccessProductHistory: vipUrl + '/accessHistory/getAccessProductHistory',
  getAccessHistoryReports: vipUrl + '/accessHistory/getAccessHistoryReports',
  delUserTimesCardPurchase: vipUrl + '/timesCardPurchase/delUserTimesCardPurchase',
  getHavingCards: vipUrl + '/timesCardPurchase/getHavingCards',
  revokeUserTimesCardSales: vipUrl + '/timesCardPurchase/revokeUserTimesCardSales',

  // 次卡报表
  timesCardStatistics: vipUrl + '/timesCardReport/timesCardStatistics',
  timesCardSalesDetails: vipUrl + '/timesCardReport/timesCardSalesDetails',
  timesCardUseDetails: vipUrl + '/timesCardReport/timesCardUseDetails',
  // 交接班
  getEmployeeSales: vipUrl + '/employee/getEmployeeSalesDetail',
  addShiftHistories: vipUrl + '/shifthistories/addShiftHistories',
  // 获取交接班数据_会员充值 /v1/employee/getVipChangeMoney
  getVipChangeMoney: vipUrl + '/employee/getVipChangeMoney',
  // configInfo
  getConfigInfo: settingUrl + '/config/getConfigInfo',
  // 意见反馈
  addFeedback: settingUrl + '/feedbacks/addFeedback',
  // 业态选择
  industry: settingUrl + '/config/industry',
  // 退出时，上传actionLogs
  actionLogsGoodsStock: posUrl + '/logs/actionLogsGoodsStock',
  // 店铺localCreatetime对比云端Createtime
  checkTime: userUrl + '/checkTime',
  clearCheck: settingUrl + '/clear-history/check',
  // 条码秤
  initScalesTab: productUrl + '/scale/initScales',
  // 短信
  getPaidItems: pushUrl + '/paidItems/getPaidItems',
  selectSmsCount: pushUrl + '/itemPurchases/selectSmsCount',
  selectSmsType: pushUrl + '/messageType/selectSmsType',
  selectSendRecords: pushUrl + '/sendRecords/selectSendRecords',
  exportSendRecords: pushUrl + '/sendRecords/exportSendRecords',
  shortPay: payUrl + '/pay/pcPay',
  shortPayQuery: payUrl + '/ali/orderQuery',
  imgIp: ip + '/spring-boot-pay',
  insertItemPurchase: pushUrl + '/itemPurchases/insertItemPurchase?pay=true',
  selectItemPurchase: pushUrl + '/itemPurchases/selectItemPurchase',
  exportItemPurchase: pushUrl + '/itemPurchases/exportItemPurchase',
  selectPaySuccessCounts: pushUrl + '/itemPurchases/selectPaySuccessCounts',
  shortMsgSuccess: ip + '/shortMsgSuccess',
  autoUpdateSuccess: window.origin + (process.env.NODE_ENV === 'development' ? '/static/' : '/') + 'autoUpdateSuccess.html',
  // 自主升级
  autoUpdate: agentUrl + '/user/autoUpdateNew',
  autoUpdateUser: agentUrl + '/user/autoUpdateUser',
  autoUpdatePrice: agentUrl + '/PricingManagement/getPrices',
  // 广告
  adFileUpload: advertUrl + '/file/upload',
  advertDelete: advertUrl + '/advertisements/deleteByIds',
  advertUpdate: advertUrl + '/advertisements/update',
  advertGet: advertUrl + '/advertisements/getCustomerAdv',
  insertOne: advertUrl + '/advertisements/insertOne',
  updateDuration: advertUrl + '/advertisements/update/duration',
  advertInfoAdd: advertUrl + '/advertisements-info/save',
  advertInfoAddNew: advertUrl + '/advertisements-info/batch/save',
  // 品牌推广
  promotionCount: advertUrl + '/brand-promotion/count-updated',
  promotionInfo: settingUrl + '/config/promotion',
  promotionList: advertUrl + '/brand-promotion',
  // 短信群发
  sendMessage: pushUrl + '/sms/api/sendMessage',
  smsList: pushUrl + '/send-records-batch/sms-list',
  smsListDetails: pushUrl + '/send-records-batch/sms-list_details',
  // 会员导入
  getVipsDetail: vipUrl + '/vip/getVipsDetail',
  // 短信告知书是否同意
  getOne: pushUrl + '/sms-confirm-status/getOne',
  // 是否需要显示短信告知书
  getValues: pushUrl + '/kvs/getValues',
  // 同意短信告知书
  agreeShort: pushUrl + '/sms-confirm-status/save',

  // 短信签名审核
  applySign: agentUrl + '/market-sms-control/sms-sign',
  // 获取短信签名
  selectSign: agentUrl + '/market-sms-control/sms-sign-list',

  // 掌柜参谋
  // zgcmCreateQrCode: (process.env.NODE_ENV === 'production' ? 'https://zgoap.zhangguizhinang.com' : ip) + '/zgoap/oap/create-qr-code', // 掌柜参谋生成二维码
  // zgcmGetBindList: (process.env.NODE_ENV === 'production' ? 'https://zgoap.zhangguizhinang.com' : ip) + '/zgoap/oap/get-bind-list', // 获取掌柜参谋绑定列表
  // zgcmUnbindUser: (process.env.NODE_ENV === 'production' ? 'https://zgoap.zhangguizhinang.com' : ip) + '/zgoap/oap/bind-unbind', // 掌柜参谋绑定/解绑用户
  // // zgcmCheckBindByMulti: ip + '/zgoap/oap/check-bind-by-multi', // 检测账号是否绑定
  // zgcmGetAppletCode: (process.env.NODE_ENV === 'production' ? 'https://zgcm.zhangguizhinang.com' : ip) + '/zgcm/mp/miniapp/user/code' // 掌柜参谋小程序码
  zgcmCreateQrCode: '/zgoap/oap/create-qr-code', // 掌柜参谋生成二维码
  zgcmGetBindList: '/zgoap/oap/get-bind-list', // 获取掌柜参谋绑定列表
  zgcmUnbindUser: '/zgoap/oap/bind-unbind', // 掌柜参谋绑定/解绑用户
  zgcmGetAppletCode: '/zgcm/mp/miniapp/user/code', // 掌柜参谋小程序码

  // 人脸核身
  detectinfo: ip + '/detectinfo/',
  qrCodePopup: pushUrl + '/sms/qr-code-popup',
  detectinfoCheck: pushUrl + '/face/auth/check',
  getPublicConfig: settingUrl + '/config/public-config'
};
export default rest;
