<style lang='less' scoped>
/deep/.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 20px;
  .header_title{
    color: #567485;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 40px;
    color: #8298A6;
    cursor: pointer;
  }
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
.shortMass {
  background: #F5F8FB;
  padding: 23px!important;
  .shortMass1 {
    width: 48%;
    position: absolute;
    .shortMass1_1 {
      width: 120px;
      height: 44px;
      border: 1px solid @themeFontColor;
      border-radius: 22px;
      font-weight: bold;
      font-size: 18px;
      line-height: 41px;
      color: @themeFontColor;
      text-align: center;
      display: inline-block;
      margin-right: 10px;
      cursor: pointer;
    }
  }
  .shortMass1_3 {
    margin-top: 9px;
    .shortMass1_3_1{
      display: inline-block;
      width: 29px;
      height: 220px;
      position: absolute;
      background: #EEF3F9;
      border-radius: 0 8px 8px 0;
      right: 1px;
      margin-top: 1px;
      cursor: pointer;
      text-align: center;
      color: @themeFontColor;
      font-size: 15px;
    }
    /deep/.el-textarea__inner {
      padding-right: 25px;
      border-radius: 8px;
    }
  }
  .shortMass2 {
    width: 48%;
    position: absolute;
    right: 23px;
    padding-left: 10px;
    .shortMass2_1 {
      width: 200px;
      height: 44px;
      border-radius: 22px;
      font-weight: bold;
      font-size: 16px;
      line-height: 41px;
      color: @themeFontColor;
      background: #FFFFFF;
      border: 1px solid #E5E5E5;
      vertical-align: top;
      display: inline-block;
      .el-dropdown {
        font-size: 16px;
        color: @themeFontColor;
        margin-left: 18px;
      }
      .el-icon--right {
        color: #B2C3CD;
      }
    }
    .shortMass2_2 {
      width: 130px;
      height: 44px;
      border: 1px solid @themeBackGroundColor;
      border-radius: 22px;
      font-weight: bold;
      font-size: 18px;
      line-height: 41px;
      color: @themeBackGroundColor;
      text-align: center;
      display: inline-block;
      right: 0;
      cursor: pointer;
    }
    .shortMass2_3 {
      width: 154px;
      height: 44px;
      background: #FFFFFF;
      border: 1px solid #E3E6EB;
      display: inline-block;
      border-radius: 22px;
      text-align: center;
      .shortMass2_3_1 {
        width: 120px;
        border-radius: 8px;
        margin-top: 9px;
        height: 33px;
        font-size: 16px!important;
        padding: 0;
        display: inline-block;
        margin-left: -12px;
        /deep/.van-field__control {
          color: @themeFontColor!important;
          font-weight: bold;
        }
      }
      .shortMass2_3_2 {
        margin-top: 11px;
        position: absolute;
        display: inline-block;
        color: @themeFontColor!important;
        font-weight: bold;
      }
    }
    .shortMass2_31 {
      width: 156px;
      height: 44px;
      border: 1px solid #567485;
      border-radius: 22px;
      color: #567485;
      font-weight: bold;
      font-size: 18px;
      line-height: 40px;
      text-align: center;
      display: inline-block;
      cursor: pointer;
    }
    .shortMass2_32 {
      display: inline-block;
      color: #567485;
      font-size: 14px;
      font-weight: bold;
      margin-left: 10px;
      position: relative;
      top: -14px;
    }
    .shortMass2_4 {
      color: @themeBackGroundColor;
      font-size: 14px;
      margin-left: 12px;
      display: inline-block;
      position: absolute;
      margin-top: 13px;
    }
    .shortMass2_5 {
      width: 248px;
      height: 44px;
      background: @themeBackGroundColor;
      color: #FFFFFF;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      line-height: 42px;
      border-radius: 22px;
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .shortMass3 {
    min-height: 305px;
    width: calc(100% - 46px);;
    background: #EEF3F9;
    position: absolute;
    bottom: 23px;
    padding: 24px;
  }
}
.downItem:focus, .downItem:not(.is-disabled):hover {
  background: #F5F7FA;
  color: @themeFontColor!important;
}
.downItem1:focus, .downItem1:not(.is-disabled):hover {
  color: #CFA26B!important;
  font-weight: bold;
}
.downItem2:focus, .downItem2:not(.is-disabled):hover {
  color: @themeFontColor!important;
}
.downItem1 {
  color: @themeFontColor;
  font-weight: bold;
  display: inline-block;
  width: 100px;
  overflow: hidden;
  font-size: 16px;
  vertical-align: middle;
  line-height: 35px;
}
/deep/.van-field__control {
  color: @themeFontColor;
}
.themeColor {
  color: @themeBackGroundColor;
}
.tip {
  padding: 20px;
  font-size: 24px;
  color: @themeFontColor;
  .tip_content {
    text-align: center;
  }
  .tip_button {
    width: 112px;
    height: 44px;
    border: 1px solid @themeBackGroundColor;
    color: @themeBackGroundColor;
    border-radius: 4px;
    line-height: 40px;
    text-align: center;
    display: inline-block;
    margin-right: 12px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
  }
  .tip_button_background {
    background: @themeBackGroundColor;
    color: #FFF
  }
}
.applyButton {
  text-align: center;
  color: @themeBackGroundColor;
  width: 200px;
  height: 44px;
  line-height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 6px;
  cursor: pointer;
  display: inline-block;
  margin-right: 12px;
}
.applyButton1 {
  text-align: center;
  color: #FFFFFF;
  width: 200px;
  height: 44px;
  line-height: 40px;
  background: @themeBackGroundColor;
  border-radius: 6px;
  cursor: pointer;
  display: inline-block;
}
#characters {
  color: @themeBackGroundColor;
}
.info-length-warning {
  color: @warningRed;
  text-align: right;
}
</style>
<template>
  <div class="shortMass">
    <v-ImportVip></v-ImportVip>
    <vPcSMSContract :isLogin="false" :fromType="2" @closeSMS="closeSMS" @sureSMS="sureSMS"></vPcSMSContract>
    <div class="shortMass1">
      <div class="shortMass1_1" @click="importVip">
        会员导入
      </div>
      <div class="shortMass1_3">
        <el-input
          type="textarea"
          placeholder="请输入手机号，并用“，”隔开，支持复制粘贴
例：14300001111，14300002222，14300003333，14300004444"
          v-model="phones"
          :rows="rows"
          resize="none"
          @blur="formatPhone()"
          @input="inputPhone()"
          maxlength="10000"
        />
        <div v-if="phones" @click="phones = '';phonesNum = 0" :style="'height: ' + leftHeight + 'px;'" class="shortMass1_3_1">
          <div style="width: 28px;" :style="'margin-top: ' + leftMargin + 'px;'">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 0H0V14H14V0Z" fill="white" fill-opacity="0.01"/>
              <path d="M4.375 3.49967L4.725 1.45801H9.275L9.625 3.49967" stroke="#567485" stroke-width="1.16667" stroke-linejoin="round"/>
              <path d="M1.75 3.5H12.25" stroke="#567485" stroke-width="1.16667" stroke-linecap="round"/>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M10.7916 3.5L10.2083 12.5417H3.79165L3.20831 3.5H10.7916Z" stroke="#567485" stroke-width="1.16667"
                stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M5.54169 10.208H8.45835" stroke="#567485" stroke-width="1.16667" stroke-linecap="round"/>
            </svg>
          清除内容</div>
        </div>
      </div>
      <div class="themeColor">共计 {{phonesNum}} 个号码，最多输入500个手机号，仅限中国大陆手机号</div>
      <div style="color: #FA5E5D;">
        短时间内请勿频繁给相同会员发送营销短信<br>
        停机、空号、暂停服务、关机、不在服务区、携号转网等可能会发送失败<br>
        查询短信发送记录，显示“发送失败”的短信不会扣费（短信通道或有一定延时）
      </div>
    </div>
    <div class="shortMass2">
      <div style="height: 44px;">
        <div v-if="shortSign !== ''" class="shortMass2_3" :style="vanFocus ? 'border-color: #d5aa76' : ''">
          <van-field
            disabled
            id="modelName"
            v-model="shortSign"
            placeholder="输入短信签名"
            class="shortMass2_3_1"
            input-align="center"
            @focus="focusVan"
            @blur="vanFocus = false"
          />
        </div>
        <div v-if="!isApply" @click="openApply()" class="shortMass2_31">
          申请短信签名
        </div>
        <div v-if="auditStatus === 1" class="shortMass2_32" :style="shortSign === '' ? 'top: 12px;margin-left: 0px;':''">
          您申请的签名{{shortApplyingSign}}正在审核中
        </div>
        <div v-if="auditStatus === 2" class="shortMass2_32"
          :style="shortSign === '' ? (errMsg === '' ? 'top: 13px;margin-left: 0px;':'top: -4px;margin-left: 0px;') :
          (errMsg === '' ? '':'top: -4px;')">
          您申请的签名{{shortApplyingSign}}已被拒绝；<br>
          <span v-if="errMsg !== ''">理由：{{errMsg}}</span>
        </div>
        <div v-if="auditStatus === 3" class="shortMass2_32" :style="shortSign === '' ? 'top: -4px;margin-left: 0px;':'top: -4px;'">
          您申请的签名{{shortApplyingSign}}{{status === 0 ? '审核通过；' : '已禁用；'}}<br>
          通过时间：{{passTime}}
        </div>
        <div class="shortMass2_4" v-if="showTag && !isApply">（建议使用简短的店名/公司名）</div>
        <div class="shortMass2_4" v-else style="font-weight: bold;cursor: pointer;" @click="openApply()">重新申请</div>
      </div>
      <div class="shortMass1_3" style="margin-top: 8px;">
        <el-input
          type="textarea"
          placeholder="输入短信正文，每条以60字为一个单位计费，超出另计费"
          v-model="info"
          :rows="rows"
          resize="none"
          maxlength="500"
        />
        <div v-if="info" @click="info = ''" :style="'height: ' + leftHeight + 'px;'" class="shortMass1_3_1" style="height: 157px;">
          <div style="width: 28px;" :style="'margin-top: ' + leftMargin + 'px;'">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 0H0V14H14V0Z" fill="white" fill-opacity="0.01"/>
              <path d="M4.375 3.49967L4.725 1.45801H9.275L9.625 3.49967" stroke="#567485" stroke-width="1.16667" stroke-linejoin="round"/>
              <path d="M1.75 3.5H12.25" stroke="#567485" stroke-width="1.16667" stroke-linecap="round"/>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M10.7916 3.5L10.2083 12.5417H3.79165L3.20831 3.5H10.7916Z" stroke="#567485" stroke-width="1.16667"
                stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M5.54169 10.208H8.45835" stroke="#567485" stroke-width="1.16667" stroke-linecap="round"/>
            </svg>
          清除内容</div>
        </div>
      </div>
      <div v-if="!isApplying && isApply" class="info-length-warning">
        共计 {{shortSign.length + info.length}} 个字（包含“【签名】”） 每条以60字为一个单位计费，超出另计费
      </div>
      <div v-if="isApplying || !isApply" class="info-length-warning">
        共计 {{info.length}} 个字（包含“【签名】”）每条以60字为一个单位计费，超出另计费
      </div>
      <div class="shortMass2_1" :style="dropFoucs ? (smallWidth ? 'width: 156px;border-color: #d5aa76' : 'border-color: #d5aa76') : (smallWidth ? 'width: 156px;' : '')">
        <el-dropdown
          trigger="click"
          style="cursor: pointer;"
          placement="bottom-start"
          @visible-change="setIcon"
          :hide-on-click="showFlag"
        >
          <span class="el-dropdown-link">
            <div style="display: inline-block;overflow: hidden;vertical-align: bottom;height: 43px;text-overflow: ellipsis;white-space: nowrap;"
              :style="findModel() === '请选择模板' ? (smallWidth ? 'width: 97px;color: #969799;':'width: 145px;color: #969799;') : (smallWidth ? 'width: 97px;' : 'width: 145px;')">{{findModel()}}</div>
            <i :class="staClass" class="el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown" style="max-height: 383px;overflow: auto;">
            <div :key="'short' + index" v-for="(item, index) in shortModels" style="width: 192px;">
              <el-dropdown-item :id="'item' + index" class="downItem">
                <div v-if="!item.editFlag && item.value === -1" @click="addModel()" class="downItem2" style="text-align: center;">
                  <svg style="margin-top: -3px;" width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_6002:1021)">
                    <path d="M20 0.5H0V20.5H20V0.5Z" fill="white" fill-opacity="0.01"/>
                    <path d="M10.0253 4.66699L10.01 16.3337" stroke="#999999" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M4.16675 10.5H15.8334" stroke="#999999" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                    </g>
                    <defs>
                    <clipPath id="clip0_6002:1021">
                    <rect width="20" height="20" fill="white" transform="translate(0 0.5)"/>
                    </clipPath>
                    </defs>
                  </svg>
                  {{item.label}}
                </div>
                <div v-if="!item.editFlag && item.value !== -1" :id="'main' + index" @click="chooseModel(item, index)" class="downItem1">
                  {{item.label}}
                </div>
                <div v-if="item.editFlag && item.value !== -1" style="display: inline-block;">
                  <van-field
                    :id="'edit' + index"
                    v-model="item.inputLabel"
                    placeholder="请输入模板名称"
                    maxlength="20"
                    @blur="editLabel(item)"
                    style="height: 26px;padding: 0;line-height: 25px;color: #567485;font-weight: bold;display: inline-block;width: 100px;margin-bottom: -8px;"
                  />
                </div>
                <svg v-if="!item.editFlag && item.value !== -1" style="display: inline-block;" @click="editModels(index, item)" width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_5993:1370)">
                  <path d="M20 0.5H0V20.5H20V0.5Z" fill="white" fill-opacity="0.01"/>
                  <path d="M2.91675 18H17.9167" stroke="#ACACAC" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M4.58325 11.6333V14.6667H7.63209L16.2499 6.04504L13.2062 3L4.58325 11.6333Z" stroke="#ACACAC" stroke-width="1.66667" stroke-linejoin="round"/>
                  </g>
                  <defs>
                  <clipPath id="clip0_5993:1370">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                  </defs>
                </svg>
                <svg v-if="item.value !== -1" @click="deleteModel(index, item)" style="margin-left: 5px;display: inline-block;" width="20" height="21" viewBox="0 0 20 21" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_5993:1374)">
                  <path d="M20 0.5H0V20.5H20V0.5Z" fill="white" fill-opacity="0.01"/>
                  <path d="M6.25 5.49967L6.75 2.58301H13.25L13.75 5.49967" stroke="#ACACAC" stroke-width="1.66667" stroke-linejoin="round"/>
                  <path d="M2.5 5.5H17.5" stroke="#ACACAC" stroke-width="1.66667" stroke-linecap="round"/>
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M15.4166 5.5L14.5833 18.4167H5.41659L4.58325 5.5H15.4166Z"
                    stroke="#ACACAC" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M7.91675 15.083H12.0834" stroke="#ACACAC" stroke-width="1.66667" stroke-linecap="round"/>
                  </g>
                  <defs>
                  <clipPath id="clip0_5993:1374">
                  <rect width="20" height="20" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                  </defs>
                </svg>
              </el-dropdown-item>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="shortMass2_5" :style="smallWidth ? 'width: 130px;' : ''" style="float: right;" @click="send">
        发送
      </div>
      <div class="shortMass2_2" style="margin-left: 10px;" @click="saveModel(1)">
        保存模板
      </div>
    </div>
    <div class="shortMass3">
      <div class="themeColor" style="font-size: 18px;margin-bottom: 12px;">发送须知：</div>
      <div style="width: 49%;display: inline-block;vertical-align: top;color: #567485;">
        1、 群发时间 9:00-18:00；<br>
        2、 应工信部要求短信内容会自动加入“拒收请回复R”，请知晓；<br>
        3、 为保证短信发送质量，编辑内容建议以“尊敬的会员”开头；<br>
        4、 发送内容不得违反《中华人民共和国电信条例》、《中国移动短信类集团信息化产品业务使用协议》等的有关政策、法规、法令，严格禁止违反国家法律法规、
            损害国家利益、社会公共利益及乙方利益的信息发布或传播。<br>
        5、 使用此功能代表已知悉并遵守相关法规，如发生传播相关违规信息，或因签名问题出现的侵权行为，须承担由此带来的一切民事、行政和刑事责任。
      </div>
      <div style="width: 49%;display: inline-block;color: #567485;padding-left: 28px;">
        <div style="font-weight: bold;">依照《中华人民共和国民法典》及有关法律法规规定，使用者不得利用电信网络平台制作、复制、传播含有以下内容的信息。</div>
        ① 反对宪法所确定的基本原则的；<br>
        ② 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；<br>
        ③ 损害国家荣誉和利益的；<br>
        ④ 煽动民族仇恨、民族歧视，破坏民族团结的；<br>
        ⑤ 破坏国家宗教政策，宣扬邪教和封建迷信的；<br>
        ⑥ 散布谣言，扰乱社会秩序，破坏社会稳定的；<br>
        ⑦ 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；<br>
        ⑧ 侮辱或者诽谤他人，侵害他人合法权益的；<br>
        ⑨ 含有法律、行政法规禁止的其他内容的。<br>
      </div>
    </div>
    <el-dialog
      :visible.sync="sendTipFlag"
      width="450px"
      :show-close='false'
      :close-on-click-modal='true'
    >
      <div class="tip">
        <div style="text-align: center;margin: 12px 0 8px 0;">
          <el-image :src="sendTipImg" style="border-radius: 50px;" :style="sendTipButton === '去充值' ? 'background: #FF6159;' : 'background: #67C23A;'"/>
        </div>
        <div style="white-space: pre-wrap" class="tip_content">{{sendTipMsg}}</div>
        <div style="text-align: center;margin-top: 20px;">
          <div v-if="sendTipButton === '去充值'" @click="sendTipFlag = false" class="tip_button">
            取消
          </div>
          <div @click="sendTipButton === '去充值' ? gotoBuyShort() : sendTipFlag = false" class="tip_button tip_button_background">
            {{sendTipButton}}
          </div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="applySign"
      width="500px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">申请短信签名</div>
        <div
          class="icon_close"
          @click="applySign = false"
        >×</div>
      </div>
      <div style="padding: 20px;">
        <div style="font-weight: bold;font-size: 18px;color: #A7B6BF;">
          <div style="display: inline-block;">
            <span style="color: #FA5E5D">*</span>短信签名
          </div>
          <div style="display: inline-block;margin-left: 12px;width: 316px;height: 42px;">
            <el-input
              v-model="applySignInput"
              placeholder="不超过6个字"
              maxlength="6"
              style="display: inline-block;"
            />
          </div>
        </div>
        <div style="font-weight: bold;font-size: 18px;color: #A7B6BF;margin-top: 16px;">
          <div style="display: inline-block;">
            <span style="color: #FA5E5D">*</span>联系电话
          </div>
          <div style="display: inline-block;margin-left: 12px;width: 316px;height: 42px;">
            <el-input
              v-model="applyMobileInput"
              placeholder="请输入手机号码"
              maxlength="11"
              @input="applyMobileInput = applyMobileInput.replace(/[^\d]/g, '')"
              style="display: inline-block;"
            />
          </div>
        </div>
        <div style="font-size: 14px;color: #FA5E5D;margin-top: 3px;">请输入真实有效的手机号码，审核过程中可能会电话核实</div>
        <div id="characters"  style="color: #B4995A;font-size: 16px;margin-top: 16px;">
          申请短信签名需要服务商审核，必要时需要提供证明身份的相关材料，审核需要1～2个工作日；<br>
          如有疑问，请拨打************
        </div>
        <div style="text-align: center;font-size: 18px;margin-top: 30px;">
          <div class="applyButton" @click="applySign = false">取消</div>
          <div class="applyButton1" @click="confirm()">确定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog, Table, TableColumn, Pagination } from 'element-ui';
import { Field } from 'vant';
import vPcSMSContract from '@/components/pc_sms_contract.vue';
import logList from '@/config/logList';

export default {
  components: {
    [Dialog.name]: Dialog,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    [Field.name]: Field,
    vPcSMSContract
  },
  data () {
    return {
      status: 0,
      isMsg: false,
      isEnpty: false,
      smallWidth: false,
      isCopy: false,
      dropFoucs: false,
      vanFocus: false,
      phones: '',
      info: '',
      phonesNum: 0,
      isInput: true,
      shortModel: -1,
      shortModels: [
        {
          value: -1,
          label: '添加模板',
          inputLabel: '添加模板',
          editFlag: false,
          info: '',
          name: ''
        }
      ],
      shortSign: '',
      rows: 20,
      leftHeight: 430,
      leftMargin: 140,
      staClass: 'el-icon-arrow-down',
      showFlag: true,
      sending: false,
      sendTipFlag: false,
      sendTipMsg: '',
      showTag: true,
      sendTipImg: require('../../image/pc_msgMark.png'),
      sendTipButton: '确定',
      shortIndex: 0,
      applySign: false,
      shortMobile: '',
      isApply: false,
      isApplying: false,
      shortApplyingSign: '',
      applySignInput: '',
      applyMobileInput: '',
      auditStatus: 0,
      errMsg: '',
      passTime: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    closeSMS() {
      this.SET_SHOW({ isShortMessageMass: false, isMember: true });
    },
    sureSMS() {
      this.getSign();
    },
    editLabel(item) {
      if (item.inputLabel.trim() === '') {
        demo.msg('warning', '模板名称不能为空');
        item.inputLabel = item.label;
      } else {
        item.label = item.inputLabel;
      }
      this.isEnpty = false;
      item.editFlag = false;
    },
    dataFormat(obj) {
      let array = _.cloneDeep(obj.data);
      array.sort((a, b) => new Date(b.auditAt) - new Date(a.auditAt));
      if (array.length > 0) {
        const setAuditData = (audit) => {
          this.shortApplyingSign = audit.smsSignName;
          this.auditStatus = audit.auditStatus;
          this.status = audit.status;
          this.passTime = audit.auditAt;
          this.shortSign = audit.smsSignName;
        };
        // 当返回结果只有一个的时候
        if (array.length === 1 && array[0].auditStatus === 3) {
          setAuditData(array[0]);
          return;
        }
        // 返回结果为两个的情况
        if (array[0].auditStatus === 1) {
          this.isApplying = true;
          setAuditData(array[0]);
          return false;
        } else if (array[0].auditStatus === 2) {
          this.isApplying = false;
          this.errMsg = array[0].auditMessage !== null ? array[0].auditMessage : '';
          setAuditData(array[0]);
        } else if (array[0].auditStatus === 3) {
          setAuditData(array[0]);
        }
        // 返回有俩并且有一个申请成功的情况
        if (array.length > 1 && array[0].auditStatus !== 3 && array[1].auditStatus === 3 && array[1].status === 0) {
          setAuditData(array[1]);
        }
      }
    },
    getSign() {
      let param = {
        appId: this.sysUid,
        phone: this.shortMobile,
        systemName: $config.systemName,
        subName: $config.subName
      }
      demo.$http.post(this.$rest.selectSign, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          if (obj.data.length === 0) {
            this.isApplying = false;
            this.isApply = false;
            return;
          } else {
            this.isApply = true;
          }
          this.dataFormat(obj);
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    dataInit() {
      if ($setting.info && demo.t2json($setting.info)) {
        this.shortModels = demo.t2json($setting.info).shortModels && demo.t2json($setting.info).shortModels.length > 0 ? demo.t2json($setting.info).shortModels : this.initModels();
        for (var i = 0; i < this.shortModels.length; i++) {
          this.shortModels[i].inputLabel = this.shortModels[i].label;
        }
        if (demo.t2json($setting.info).shortMobile) {
          this.shortMobile = demo.t2json($setting.info).shortMobile;
        }
      }
      if (this.shortPhones !== '') {
        this.phones = this.shortPhones;
      }
      if (this.shortInfo !== '') {
        this.info = this.shortInfo;
      }
      if (this.shortNum !== 0) {
        this.phonesNum = this.shortNum;
      }
    },
    checkSign() {
      if (this.applySignInput === '') {
        demo.msg('warning', '请填写短信签名');
        return true;
      }
      if (this.applyMobileInput === '') {
        demo.msg('warning', '请填写手机号');
        return true;
      }
    },
    addApply() {
      let param = {
        smsSignName: this.applySignInput,
        appId: this.sysUid,
        phone: this.applyMobileInput,
        systemName: $config.systemName,
        subName: $config.subName
      }
      demo.$http.post(this.$rest.applySign, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          this.applySign = false;
          this.isApply = true;
          this.isApplying = true;
          this.getSign();
          demo.msg('success', '申请成功');
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    confirm() {
      if (this.checkSign()) {
        return;
      }
      this.addApply();
    },
    openApply() {
      this.applySignInput = '';
      this.applyMobileInput = '';
      this.applySign = true;
    },
    chooseModel(item, index) {
      this.showFlag = true;
      this.info = '';
      if (item.value !== -1) {
        this.shortIndex = index;
        this.shortModel = item.value;
        this.info = this.shortModels[index].info;
      }
    },
    gotoBuyShort() {
      this.sendTipFlag = false;
      this.SET_SHOW({ isShortMessageMass: false });
      this.SET_SHOW({ msgFrom: 3 });
      this.SET_SHOW({ isShortMessage: true });
    },
    send() {
      demo.actionLog(logList.clickSmsSend);
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，短信功能无法使用');
        return;
      }
      if (this.isApplying || !this.isApply) {
        demo.msg('warning', '未申请短信签名或短信签名正在审核中，无法发送短信');
        return;
      }
      if (!this.checkSend()) {
        return;
      }
      this.sending = true;
      setTimeout(() => {
        this.sending = false;
      }, 2000);
      let phoneList = this.phones.split('，');
      if (phoneList.length > 500) {
        demo.msg('warning', '单次发送最多支持500条');
        return;
      }
      let param = {
        smsSignName: this.shortSign,
        content: this.info,
        appId: this.sysUid,
        phoneList: this.phones.split('，'),
        systemName: $config.systemName,
        subName: $config.subName
      }
      demo.$http.post(this.$rest.sendMessage, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          if (+obj.data.code === 0) {
            this.sendTipMsg = '发送成功';
            this.sendTipImg = require('../../image/pc_msgMark.png');
            this.sendTipFlag = true;
            this.sendTipButton = '确定';
            this.getMessageCount();
          } else {
            demo.msg('warning', '短信发送失败，请稍后再试');
          }
        } else {
          demo.msg('warning', obj.msg + (obj.data ? '<br>短信内容“' + obj.data[0] + '”违规' : ''));
        }
      });
    },
    checkSend() {
      if (this.phonesNum > this.shortTotal) {
        this.sendTipMsg = '短信剩余条数不足，\n删除多余号码或充值短信条数';
        this.sendTipImg = require('../../image/Vector.png');
        this.sendTipButton = '去充值';
        this.sendTipFlag = true;
        return false;
      }
      if (this.phones === '') {
        demo.msg('warning', '请至少输入一个手机号');
        return false;
      }
      if (this.shortSign === '') {
        demo.msg('warning', '缺少短信签名');
        return false;
      }
      if (this.info === '') {
        demo.msg('warning', '短信内容不能为空');
        return false;
      }
      if (this.sending) {
        return false;
      }
      let phoneList = this.phones.split('，');
      for (const element of phoneList) {
        if (!(/^1[3-9]\d{9}$/.test(element))) {
          demo.msg('warning', `请检查【${element}】是否为正确的11位手机号`);
          return false;
        }
      }
      return true;
    },
    generateUUID() {
      var d = new Date().getTime();
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now();
      }
      var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
      });
      return uuid;
    },
    addModel() {
      this.isEnpty = false;
      this.shortModels.forEach(item => {
        if (item.label.trim() === '') {
          this.isEnpty = true;
        }
      });
      if (this.isEnpty) {
        demo.msg('warning', '模板名称不能为空');
        return;
      }
      this.showFlag = false;
      this.shortModels.splice(0, 0, {
        value: this.generateUUID(),
        label: '',
        editFlag: true,
        info: this.info
      });
      setTimeout(() => {
        $('#edit0').focus();
      }, 100);
    },
    focusVan() {
      this.vanFocus = true;
      setTimeout(() => {
        $('#modelName').select();
      }, 0);
    },
    saveModel(flag) {
      // 提交
      if (flag === 1) {
        demo.actionLog(logList.clickSmsSaveTemplate);
        if (this.shortModel === -1) {
          demo.msg('warning', '请选择模板');
          return;
        }
        if (this.shortModel !== -1) {
          this.shortModels[this.shortIndex].info = this.info;
        }
      }
      let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      dataInfo.shortModels = this.shortModels;
      dataInfo.shortMobile = this.shortMobile;
      dataInfo.shortSign = this.shortSign;
      dataInfo.isApplying = this.isApplying;
      var data = [
        { key: 'info', value: JSON.stringify(dataInfo), remark: '短信模板' }
      ];
      settingService.put(data, () => {
        if (flag === 1) {
          demo.msg('success', this.$msg.save_success);
        }
      });
    },
    getMessageCount () {
      const param = {
        'paidServiceCode': '3',
        'phone': this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.selectSmsCount, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(rs => {
          const obj = rs.data;
          if (obj.code === 200 && obj.data !== null) {
            this.SET_SHOW({ shortTotal: obj.data.residueCount })
          }
        });
    },
    importVip() {
      this.SET_SHOW({ isImportVip: true });
      demo.actionLog(logList.clickSmsVipImport);
    },
    deleteModel(index, item) {
      if (item.label.trim() === '') {
        this.isEnpty = false;
      }
      if (this.shortModel === item.value) {
        demo.msg('warning', '不可删除当前选中的模板');
        return;
      }
      if (this.shortModel === index) {
        this.shortModel = -1;
      }
      this.showFlag = false;
      this.shortModels.splice(index, 1);
    },
    editModels(index, item) {
      this.showFlag = false;
      item.editFlag = true;
      setTimeout(() => {
        $('#edit' + index).focus();
        $('#edit' + index).select();
      }, 0);
    },
    checkMsg(flag) {
      let oIndex = 0;
      this.shortModels.forEach((item, index) => {
        if (item.label.trim() === '') {
          oIndex = index
          this.isEnpty = true;
        }
      });
      if (!flag && this.isEnpty) {
        this.isEnpty = false;
        if (!this.isMsg) {
          demo.msg('warning', '模板名称不能为空');
          this.isMsg = true;
        }
        setTimeout(() => {
          this.isMsg = false;
        }, 2000);
        if (this.shortModels.length > 1) {
          this.shortModels.splice(oIndex, 1);
        }
      }
    },
    setIcon(flag) {
      this.dropFoucs = flag;
      this.checkMsg(flag);
      this.shortModels.map((o, index) => {
        if (o.value !== -1) {
          o.editFlag = false;
          document.getElementById('main' + index).removeAttribute('style');
          document.getElementById('item' + index).removeAttribute('style');
          if (o.value === this.shortModel) {
            document.getElementById('main' + index).setAttribute('style', 'color: #CFA26B;');
            document.getElementById('item' + index).setAttribute('style', 'background: #F5F7FA;');
          }
        }
      });
      if (flag) {
        this.staClass = 'el-icon-arrow-up';
      } else {
        this.staClass = 'el-icon-arrow-down';
      }
    },
    findModel() {
      let model = _.find(this.shortModels, o => { return o.value === this.shortModel; });
      if (this.shortModel === -1) {
        return '请选择模板';
      }
      return model.label;
    },
    inputPhone() {
      this.phones = this.phones.replace(/[^\d,，\s]/ig, '');
      this.phones = this.phones.replace(/[,]/ig, '，');
      let phonesList = this.phones.split('，');
      phonesList = _.remove(phonesList, (o) => { return o !== ''; });
      this.phonesNum = phonesList.length;
      if (this.isCopy) {
        this.formatPhone();
      }
    },
    formatPhone() {
      this.phones = this.phones.replace(/[\s]/ig, '，');
      let phonesList = this.phones.split('，');
      phonesList = _.remove(phonesList, (o) => { return o !== ''; });
      this.phonesNum = phonesList.length;
      this.phones = phonesList.join('，');
      this.isCopy = false;
    },
    addListenevent() {
      document.body.addEventListener('keydown', this.keyDown, false);
    },
    cancelListenevent() {
      document.body.removeEventListener('keydown', this.keyDown, false);
    },
    keyDown(e) {
      var code = e.keyCode;
      if (e.ctrlKey && code === 86) {
        this.isCopy = true;
      }
    },
    initModels() {
      return [
        {
          value: -1,
          label: '添加模板',
          inputLabel: '添加模板',
          editFlag: false,
          info: '',
          name: ''
        }
      ]
    }
  },
  created() {
    this.getMessageCount();
    this.addListenevent();
  },
  watch: {
    chooseLists() {
      let pho = _.cloneDeep(this.phones);
      if (this.chooseLists.length > 0 && pho !== '') {
        let phoneLists = pho.split('，');
        let chooseList = this.chooseLists.map(o => o.mobile);
        for (var i = 0; i < chooseList.length; i++) {
          if (phoneLists.indexOf(chooseList[i]) < 0) {
            phoneLists.push(chooseList[i]);
          }
        }
        this.phones = _.cloneDeep(phoneLists.join('，'));
        this.phonesNum = phoneLists.length;
        this.SET_SHOW({ chooseLists: [] });
      }
      if (this.chooseLists.length > 0 && pho === '') {
        this.phones = _.cloneDeep(this.chooseLists.map(o => o.mobile).join('，'));
        this.phonesNum = this.chooseLists.length;
        this.SET_SHOW({ chooseLists: [] });
      }
    }
  },
  mounted() {
    this.dataInit();
    if (document.documentElement.clientWidth < 1315) {
      this.smallWidth = true;
    } else {
      this.smallWidth = false;
    }
    if (document.documentElement.clientWidth < 1140) {
      this.showTag = false;
    }
    if (document.documentElement.clientHeight >= 1000) {
      this.rows = 20;
      this.leftHeight = 430;
      this.leftMargin = 140;
    } else if (document.documentElement.clientHeight < 1000 && document.documentElement.clientHeight >= 900) {
      this.rows = 18;
      this.leftHeight = 388;
      this.leftMargin = 124;
    } else if (document.documentElement.clientHeight < 900 && document.documentElement.clientHeight >= 800) {
      this.rows = 12;
      this.leftHeight = 262;
      this.leftMargin = 66;
    } else {
      this.rows = 7;
      this.leftHeight = 157;
      this.leftMargin = 22;
    }
    this.getSign();
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    chooseLists: state => state.show.chooseLists,
    shortTotal: state => state.show.shortTotal,
    shortPhones: state => state.show.shortPhones,
    shortInfo: state => state.show.shortInfo,
    shortNum: state => state.show.shortNum
  }),
  beforeDestroy() {
    this.SET_SHOW({ shortInfo: this.info });
    this.SET_SHOW({ shortPhones: this.phones });
    this.SET_SHOW({ shortNum: this.phonesNum });
    this.cancelListenevent();
    this.saveModel(2);
  }
};
</script>
