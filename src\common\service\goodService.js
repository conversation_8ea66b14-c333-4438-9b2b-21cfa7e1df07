import pyCode from '@/common/pyCode';
import { calc } from 'frontend-utils';
import md5 from 'js-md5';
import _ from 'lodash';
import logList from '../../config/logList';
import rest from '../../config/rest';
import { INVENTORY_DEAL_TYPE } from '../../utils/inventoryDealType';
import dao from '../dao/dao';
import stringUtils, { StringBuilder } from '../stringUtils';
import commonService from './commonService';
import goodsExtBarcodeService from './goodsExtBarcodeService';
import orderService from './orderService';
import typeService from './typeService';

const goodService = {
  /**
   * 商品列表
   * @param {*} data  {isGroup  specs[""4":24"]  }
   * {
        "pset": "",
        "type": "0,1,2,3,4,5,6,7,8",
        "condition": "{key:val}",
        "limit": 15,
        "selectDel": false,
        "offset": 0
    }
    goodService.search({"isGroup":true,"limit":15,"offset":0,type:"111"},(res)=>{console.log(res)})
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  search(data, onSuccess, onFail) {
    /**
     * goodsSearch
     * goodsGroupSearch
     *  data.sql2 = '';
     * if (data.vipGoods) {
     * if (data.fingerprintFlg) {
     *   data.sql2 = "and a.fingerprint not in ('" + data.fingerprint.join("','") + "')";
     *  } else {
     *   data.sql2 = "and a.fingerprint in ('" + data.fingerprint.join("','") + "')";
     *  }
     * }
     */
    const { limit, offset, isGroup, selectDel, condition, specs, type,
      orderBy, vipGoods, fingerprintFlg, fingerprint } = data;
    const sql = isGroup ? sqlApi.goodsGroupSearch : this.getGoodsSearchSql();

    const wheres =
      (isGroup ? '' : selectDel ? '1 = 1 ' : 'a.is_deleted=0 ') +
      this.joinSpec(specs) +
      this.joinType(type) +
      this.joinOtherEscape(condition) +
      this.joinDate(data) +
      this.joinFingerprint(vipGoods, fingerprintFlg, fingerprint) +
      this.searchDisPlaySettingSale(data) +
      this.searchDisPlaySettingStock(data);
    const orderBys = this.joinOrderBy(orderBy);
    let supplierWhere = this.getSupplierWhere(data);
    dao.exec(
      sql.format({ wheres, limit, offset, orderBys, supplierWhere }),
      req => {
        isGroup
          ? onSuccess(this.getGoodsGroup(specsService.specStringToArray(req)))
          : goodsExtBarcodeService.addExtBarcodeAttribute(req, onSuccess, onFail);
      },
      onFail
    );
  },
  // 搜索商品列表显示设置 销售筛选
  searchDisPlaySettingSale(data) {
    // 隐藏未销售
    let noSale = data.pset.indexOf('0') !== -1;
    // 显示未销售
    let showNoSale = data.pset.indexOf('2') !== -1;
    if (noSale || showNoSale) {
      return ` and a.fingerprint ${showNoSale ? 'not' : ''} in (select distinct good_fingerprint from sale_items where is_deleted = 0) `;
    }
    return '';
  },
  // 搜索商品列表显示设置 库存筛选
  searchDisPlaySettingStock(data) {
    // 隐藏零库存
    let noSale = data.pset.indexOf('1') !== -1;
    // 显示零库存
    let showNoSale = data.pset.indexOf('3') !== -1;
    if (noSale || showNoSale) {
      return ` and a.cur_stock ${showNoSale ? '=' : '<>'} 0 `;
    }
    return '';
  },
  getGoodsSearchSql() {
    let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
    if (dataInfo.hasBarCodeScales) {
      return sqlApi.goodsSearch;
    } else {
      return sqlApi.goodsSearch2;
    }
  },
  searchGoodByCode(data, onSuccess, onFail) {
    const { limit, offset, isGroup, condition, orderBy } = data;
    const sql = isGroup ? sqlApi.goodsGroupSearch : sqlApi.goodsSearch;
    const wheres = condition ? `and  a.code = '${condition}'` : '';
    const orderBys = this.joinOrderBy(orderBy);
    dao.exec(
      sql.format({ wheres, limit, offset, orderBys }),
      req => {
        isGroup ? onSuccess(this.getGoodsGroup(specsService.specStringToArray(req))) : onSuccess(specsService.specStringToArray(req));
      },
      onFail
    );
  },
  searchWithSuppliers(data, onSuccess, onFail) {
    /**
     * goodsSearch
     * goodsGroupSearch
     *  data.sql2 = '';
     * if (data.vipGoods) {
     * if (data.fingerprintFlg) {
     *   data.sql2 = "and a.fingerprint not in ('" + data.fingerprint.join("','") + "')";
     *  } else {
     *   data.sql2 = "and a.fingerprint in ('" + data.fingerprint.join("','") + "')";
     *  }
     * }
     */
    const { limit, offset, isGroup, condition, specs, type, orderBy, vipGoods, fingerprintFlg, fingerprint } = data;
    const sql = isGroup ? sqlApi.goodsGroupSearch : sqlApi.goodSearchWithSuppliers;

    const wheres =
      this.joinSpec(specs) +
      this.joinType(type) +
      this.joinOther(condition) +
      this.joinDate(data) +
      this.joinFingerprint(vipGoods, fingerprintFlg, fingerprint);
    const orderBys = this.joinOrderBy(orderBy);
    dao.exec(
      sql.format({ wheres, limit, offset, orderBys }),
      req => {
        isGroup ? onSuccess(this.getGoodsGroup(specsService.specStringToArray(req))) : onSuccess(specsService.specStringToArray(req));
      },
      onFail
    );
  },
  joinFingerprint(vipGoods, fingerprintFlg, fingerprint) {
    return (vipGoods) ? fingerprintFlg ? `and a.fingerprint not in ('${fingerprint.join("','")}')`
      : `and a.fingerprint in ('${fingerprint.join("','")}')` : '';
  },
  joinSpec(specs) {
    let sql = '';
    if (specs) {
      sql += ' and ( specs like ';
      specs.forEach((spec, index) => {
        if (index === 0) {
          sql += `'%${spec}%'`;
        } else {
          sql += `or specs like  '%${spec}%'`;
        }
      });
      sql += ')';
    }
    return sql;
  },
  joinType(type) {
    if (demo.isNullOrTrimEmpty(type)) {
      return '';
    } else if (type === '0') {
      return `and a.unit_fingerprint in (select fingerprint from units where name in
        ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))`;
    } else {
      return `and a.type_fingerprint in (select fingerprint from types where id in (${type}) UNION select fingerprint
      from types where parent_fingerprint in(select fingerprint from types where id in (${type})))`;
    }
  },
  joinDate(data) {
    var date = '';
    if (data.hasOwnProperty('from')) {
      date += "and a.create_at>='" + data.from + "' ";
    }
    if (data.hasOwnProperty('to')) {
      var dateTime = new Date(data['to']);
      dateTime = dateTime.setDate(dateTime.getDate() + 1);
      const end = new Date(dateTime).format('yyyy-MM-dd');
      date += "and a.create_at<'" + end + "' ";
    }
    return date;
  },
  joinOther(condition) {
    return condition
      ? `and (a.name like '%${condition}%' or a.pinyin like '%${condition}%'
      or goods_ext_barcode.ext_barcode like '%${condition}%'
      or a.code like '%${condition}%'
      or a.first_letters like '%${condition}%')`
      : '';
  },
  joinOtherEscape(condition) {
    if (condition) {
      condition = condition.replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
    }
    return condition
      ? `and (a.name like '%${condition}%' ESCAPE '/'
      or a.pinyin like '%${condition}%' ESCAPE '/'
      or goods_ext_barcode.ext_barcode like '%${condition}%' ESCAPE '/'
      or a.code like '%${condition}%' ESCAPE '/'
      or a.first_letters like '%${condition}%' ESCAPE '/')`
      : '';
  },
  joinOrderBy(orderBy) {
    let tpl = `{column} {order}`;
    return !orderBy
      ? 'a.create_at desc, a.first_letters'
      : orderBy
        .map(item => {
          item.column = item.column === 'a.name' ? 'a.first_letters' : item.column;
          item.order = item.order || 'asc';
          return tpl.format(item);
        })
        .join(',');
  },
  getGoodsGroup(goods) {
    const goodObjects = _.groupBy(goods, 'majorCode');
    return Object.keys(goodObjects).map(key => {
      const goodList = goodObjects[key];
      let maxSalePrice = Number(_.maxBy(goodList, 'salePrice')['salePrice']).toFixed(2);
      let minSalePrice = Number(_.minBy(goodList, 'salePrice')['salePrice']).toFixed(2);
      let price = maxSalePrice === minSalePrice ? minSalePrice : minSalePrice + '~' + maxSalePrice;
      let specList = [];
      goodList.forEach(g => {
        let specs = g['specs']['unlock'];
        specs.forEach(s => {
          specList.push(s);
        });
      });
      return {
        majorCode: key,
        image: goodList[0]['image'],
        name: goodList[0]['name'],
        pinyin: goodList[0]['pinyin'],
        price: price,
        specGroup: _.groupBy(specList, 'parentId'),
        list: goodList
      };
    });
  },
  getSupplierWhere (data) {
    let supplierWhere = '';
    if (data.hasOwnProperty('all') && data.all === false) {
      if (data.hasOwnProperty('notSupplier') && data.notSupplier === true && data.hasOwnProperty('supplierList') && data.supplierList.length > 0) {
        const fingerprints = `'${data.supplierList.join('\',\'')}'`;
        supplierWhere = 'and (supplier_fingerprint is null or supplier_fingerprint in (' + fingerprints + '))';
      } else if (data.hasOwnProperty('notSupplier') && data.notSupplier === true) {
        // 未设置供应商
        supplierWhere = 'and supplier_fingerprint is null';
      } else if (data.hasOwnProperty('supplierList') && data.supplierList.length > 0) {
        // 选择的供应商
        const fingerprints = `'${data.supplierList.join('\',\'')}'`;
        supplierWhere = 'and supplier_fingerprint in (' + fingerprints + ')';
      }
    }
    return supplierWhere;
  },
  searchCnt(data, onSuccess, onFail) {
    const { condition, specs, type, vipGoods, fingerprintFlg, fingerprint } = data;
    const sql = sqlApi.goodsSearchCnt;
    const wheres =
      this.joinSpec(specs || '') +
      this.joinType(type) +
      this.joinOtherEscape(condition) +
      this.joinFingerprint(vipGoods, fingerprintFlg, fingerprint) +
      this.joinDate(data) +
      this.searchDisPlaySettingSale(data) +
      this.searchDisPlaySettingStock(data);
    let supplierWhere = this.getSupplierWhere(data);
    dao.exec(
      sql.format({ wheres, supplierWhere }),
      req => {
        onSuccess(req);
      },
      onFail
    );
  },
  /**
   * 單組商品
   * @param {*} data  {}
   *goodService.search({"majorCode":111},(res)=>{console.log(res)})
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  getOneGoods(data, onSuccess, onFail) {
    const { majorCode } = data;
    dao.exec(
      sqlApi.oneGoods.format({ majorCode }),
      req => {
        onSuccess(specsService.specStringToArray(req));
      },
      onFail
    );
  },
  /**
   * var params = {wheres: 'and 1=1'}
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getGoodsByWheres: function (params, onSuccess, onFail) {
    dao.exec(sqlApi.getGoodsByWheres.format(params), onSuccess, onFail);
  },
  /**
   * var params = {wheres: 'and 1=1'}
   * @param {*} params
   * @returns
   */
  getGoodsByWheresSync: function (params) {
    return new Promise((resolve, reject) => {
      this.getGoodsByWheres(params, resolve, reject);
    });
  },
  goods: {
    name: null,
    majorCode: null,
    isVipDisc: 0,
    image: null,
    lockSpecs: null,
    supplierFingerprint: null,
    typeFingerprint: null,
    unitFingerprint: null,
    remark: null,
    isCodesGoods: 0
  },
  subGoods: {
    code: null,
    purPrice: 0,
    salePrice: 0,
    vipPrice: 0,
    curStock: 0,
    initStock: 0,
    initPrice: 0,
    initAmt: 0,
    minStock: 0,
    maxStock: 0,
    hasImage: 0,
    packing: null,
    specs: null
  },
  subGoods1: {
    initStock: 0,
    initPrice: 0,
    initAmt: 0,
    minStock: 0,
    maxStock: 0,
    hasImage: 0,
    packing: null
  },
  goodsImages: {
    url: null,
    localUrl: null,
    order: null
  },
  /**
   * 生成货号
   * @param {*} onSuccess
   * @param {*} onFail
   */
  generateMajorCode: function (onSuccess, onFail) {
    var params = {};
    params.yy = new Date().format('yy');
    params.deviceCode = demo.$store.state.show.devicecode;
    var preOrder = params.yy + ('000' + params.deviceCode).slice(-3);
    dao.exec(
      sqlApi.generateMajorCode.format(params),
      res => {
        if (res.length > 0) {
          var majorCode = preOrder + ('00000' + res[0].orderNo).slice(-5);
          onSuccess(majorCode);
        } else {
          onFail('自动生成货号数量超限');
        }
      },
      onFail
    );
  },
  /**
   * 获取商品条码为空的数量
   * @returns
   */
  getNullCodeCounts: function(onSuccess, onFail) {
    dao.exec(
      sqlApi.getNullCodeCounts,
      onSuccess,
      onFail
    );
  },
  /**
   * 商品条码为空的全部自动生成
   * @returns
   */
  generateNullCodes: async function(onSuccess, onFail) {
    const goodsCodeNull = await dao.asyncExec(sqlApi.getNullCodeGoods);
    if (goodsCodeNull.length === 0) {
      onSuccess();
      return;
    }
    this.generateCodesThirteen(goodsCodeNull).then(onSuccess).catch(onFail);
  },
  /**
   * 自动生成条码
   * @returns
   */
  generateCodes: async function (params, onSuccess, onFail) {
    const fingerprint = params.fingerprint;
    if (demo.isNullOrTrimEmpty(fingerprint)) {
      onFail('请选择商品');
      return;
    }
    const wheres = `and is_deleted=0
      and fingerprint in(${fingerprint})`;
    const goodsWithNoCode = await dao.asyncExec(sqlApi.getGoodsJoinProductScaleByWheres.format({ wheres }));
    if (goodsWithNoCode.length === 0) {
      onSuccess();
      return;
    }

    // 传称商品
    const goodsSendScale = _.remove(goodsWithNoCode, item => +item.isSendscale === 1);
    Promise.all([this.generateCodesThirteen(goodsWithNoCode), this.generateCodesSeven(goodsSendScale)]).then(onSuccess).catch(onFail);
  },
  // 自动生成13位条码
  generateCodesThirteen(goodsWithNoCode) {
    return new Promise(async (resolve, reject) => {
      const codesThirteen = await this.asyncMakeCode(goodsWithNoCode.length, 0);
      const sql = new StringBuilder('');
      goodsWithNoCode.forEach((item, index) => {
        item.code = codesThirteen[index];
        sql.append(sqlApi.goodsUpdateCode.format(item));
      });
      dao.transaction(sql.toString(), resolve, reject);
    });
  },
  // 自动生成7位条码
  generateCodesSeven(goodsSendScale) {
    return new Promise(async (resolve, reject) => {
      const codesSeven = await this.asyncCreateBarcode(goodsSendScale.length, 0);
      const sql = new StringBuilder('');
      goodsSendScale.forEach((item, index) => {
        item.code = codesSeven[index];
        sql.append(sqlApi.goodsUpdateCode.format(item));
      });
      dao.transaction(sql.toString(), resolve, reject);
    });
  },
  /**
   * 自动生成条码
   * @param {*} count
   * @param {*} isBatchImport
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  asyncMakeCode: function (count, isBatchImport) {
    return new Promise((resolve, reject) => {
      this.makeCode(count, isBatchImport, resolve, reject);
    });
  },
  makeCode: function (count, isBatchImport, onSuccess, onFail) {
    if (count === 0) {
      onSuccess([]);
      return;
    }
    // '2'+年后2位
    const yy = new Date().format('yy');
    const deviceCode = demo.$store.state.show.devicecode;
    const prefix = 2 + yy + ('000' + deviceCode).slice(-3);
    let sql;
    let sql1;
    if (+isBatchImport === 1) {
      sql = sqlApi.goodBatchImportMakeCode;
      sql1 = sqlApi.getBatchImportAllExistCodes;
    } else {
      sql = sqlApi.goodMakeCode;
      sql1 = sqlApi.getAllExistCodes;
    }
    dao.exec(
      sql.format({ prefix }),
      res => {
        const minCode = +res[0].minCode;
        dao.exec(
          sql1.format({ prefix }),
          res1 => {
            const codeList = [];
            const existCodes = _.map(res1, 'code');
            for (let i = 0; codeList.length < count; i++) {
              const code = prefix + ('000000' + (minCode + i)).slice(-7);
              if (!existCodes.includes(code)) {
                codeList.push(code);
              }
            }

            onSuccess(codeList);
          },
          onFail
        );
      },
      onFail
    );
  },
  handleParams: function (params) {
    params.name = params.name.replace(/'/g, '‘').replace(/;/g, '；');
    params.firstLetters = pyCode.getPyCode(params.name);
    params.pinyin = params.pinyin.replace(/'/g, '‘').replace(/;/g, '；');
    params.remark = params.remark.replace(/'/g, '‘').replace(/;/g, '；');
    _.forEach(params.items, item => {
      if (demo.isNullOrTrimEmpty(item.code)) {
        item.code = null;
      }
    });
  },
  insertMajorCode: function (goods, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(goods.majorCode)) {
      this.generateMajorCode(res => {
        goods.majorCode = res;
        onSuccess();
      }, onFail);
    } else {
      var params1 = {};
      params1.wheres = `and is_deleted=0 and major_code = '` + goods.majorCode + `'`;
      dao.exec(
        sqlApi.getGoodsByWheres.format(params1),
        res => {
          if (res.length > 0) {
            onFail('货号已存在，请修改货号');
            return;
          }
          onSuccess();
        },
        onFail
      );
    }
  },
  insertCode: function (goods, goodsParams, onSuccess, onFail) {
    var codes = _.compact(_.map(goods.items, 'code'));
    var params1 = {};
    params1.wheres = `and code <> '' and code in ('` + codes.join("','") + `')`;
    params1.wheres1 = `and ext_barcode in ('` + codes.join("','") + `')`;
    dao.exec(
      sqlApi.getGoodsCodeByWheres.format(params1),
      async res => {
        if (res.length > 0) {
          var repeatCodes = _.map(
            _.filter(res, item => {
              return +item.isDeleted === 0;
            }),
            'code'
          );
          if (repeatCodes.length > 0) {
            onFail('条码已存在：' + repeatCodes.join());
            return;
          }
        }

        _.forEach(goods.items, item => {
          let item1 = _.assign({ ...this.subGoods }, item);
          item1.fingerprint = md5(item1.name);
          item1.specs = null;
          goodsParams.goods.push(item1);
        });

        var fingerprints = _.map(goodsParams.goods, 'fingerprint');
        let params = {};
        params.wheres = `and fingerprint in ('${fingerprints.join("','")}')`;
        let repeats = await dao.asyncExec(sqlApi.getGoodsByWheres.format(params));
        let repeatFingerprints = _.map(repeats, 'fingerprint');
        if (repeatFingerprints.length > 0) {
          var now = new Date().getTime();
          _.filter(goodsParams.goods, item => {
            return repeatFingerprints.includes(item.fingerprint);
          }).forEach(item => {
            item.fingerprint = md5(item.name + '_' + now);
          });
        }

        onSuccess();
      },
      onFail
    );
  },
  /**
   * 新增
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  insert: async function (params, onSuccess, onFail) {
    params.manufactureDate = demo.isNullOrTrimEmpty(params.manufactureDate) ? null : params.manufactureDate;
    params.expiryDays = demo.isNullOrTrimEmpty(params.expiryDays) ? null : params.expiryDays;
    let params1 = _.cloneDeep(params);
    this.handleParams(params1);
    let inputCodes = _.compact(_.map(params1.items, 'code'));
    if (inputCodes.length > new Set(inputCodes).size) {
      onFail('条码不允许重复');
      return;
    }
    let goods = { ...this.goods };
    _.assign(goods, params1);
    let goodsParams = {
      goods: [],
      images: []
    };
    // 商品主信息删除了 商品关联表信息一并删除 传入商品名称和条码
    const deleteGoodsWhere = { code: _.compact(_.map(goods.items, 'code')).join("','") };
    const deleteGoodsFingerprint = await dao.asyncExec(sqlApi.getDeleteGoodsByNameOrCode.format(deleteGoodsWhere));
    console.log(deleteGoodsFingerprint)
    if (!demo.isNullOrTrimEmpty(deleteGoodsFingerprint)) {
      await dao.asyncExec(sqlApi.goodsDelete.format(`'` + _.map(deleteGoodsFingerprint, 'fingerprint').join(`','`) + `'`));
    }
    this.insertMajorCode(
      goods,
      () => {
        this.insertCode(
          goods,
          goodsParams,
          () => {
            this.insertImagesCheck(
              goodsParams,
              () => {
                this.insertDo(
                  goods,
                  goodsParams,
                  () => {
                    goodsExtBarcodeService.insert(goodsParams, () => {
                      this.insertOrUpdategoodsSuppliers(goodsParams.goods[0], onSuccess, onFail);
                    }, onFail);
                  },
                  onFail
                );
              },
              onFail
            );
          },
          onFail
        );
      },
      onFail
    );
  },
  /**
   * 新增或更新商品和供应商关联表
   */
  insertOrUpdategoodsSuppliers: function (goods, onSuccess, onFail) {
    if (!demo.isNullOrTrimEmpty(goods.supplierFingerprint)) {
      dao.exec(sqlApi.insertOrUpdategoodsSuppliers.format(goods), onSuccess, onFail);
    } else {
      let sql = `update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime')
      where fingerprint='${goods.fingerprint}';`;
      dao.exec(sql, onSuccess, onFail);
    }
  },
  insertImagesCheck: function (goodsParams, onSuccess, onFail) {
    _.forEach(goodsParams.goods, item => {
      _.forEach(item.images, item1 => {
        let images = _.assign({ ...this.goodsImages }, item1);
        images.code = item.code;
        images.goodFingerprint = item.fingerprint;
        images.fingerprint = md5(item.code + '_' + images.order);
        goodsParams.images.push(images);
      });
    });
    if (goodsParams.images.length > 0) {
      var now = new Date().getTime();
      dao.exec(
        sqlApi.getImagesByFingerprints.format("'" + _.map(goodsParams.images, 'fingerprint').join("','") + "'"),
        res => {
          _.forEach(res, item => {
            _.find(goodsParams.images, item1 => {
              return item.fingerprint === item1.fingerprint;
            }).fingerprint = md5(item.fingerprint + '_' + now);
          });
          onSuccess();
        },
        onFail
      );
    } else {
      onSuccess();
    }
  },
  insertDo: function (goods, goodsParams, onSuccess, onFail) {
    let goodsInsert = sqlApi.goodsInsert;
    let goodsInsertValues = sqlApi.goodsInsertValues.format(demo.sqlConversion(goods));
    let imagesInsert = sqlApi.imagesInsert;
    let imagesInsertValues = sqlApi.imagesInsertValues;
    let goodsAttributesInsert = sqlApi.goodsAttributesInsert;
    let goodsAttributesInsertValues = sqlApi.goodsAttributesInsertValues;
    let inventoryParams = {};
    inventoryParams.exec = 0;
    inventoryParams.dealType = INVENTORY_DEAL_TYPE.NEW_ADD_INVENTORY;
    inventoryParams.inventoryItems = [];
    let goodsLength = goodsParams.goods.length;
    _.forEach(goodsParams.goods, (item, index) => {
      if (!demo.isNullOrTrimEmpty(item.isBarcodeScalesGoods) && item.isBarcodeScalesGoods === 1) {
        dao.exec(
          sqlApi.productScale.format(item),
          () => {
            if (demo.isNullOrTrimEmpty(item.code)) {
              this.createBarcode(1).then(res1 => {
                item[0].code = res1;
              }).catch(onFail);
            }
          },
          onFail
        );
      }
      if (+item.curStock) {
        let good = {};
        good.accountQty = 0;
        good.actualQty = item.curStock;
        good.price = item.purPrice;
        good.goodFingerprint = item.fingerprint;
        inventoryParams.inventoryItems.push(good);
      }
      item.specs = null;
      goodsInsert += goodsInsertValues.format(demo.sqlConversion(item)) + this.compareIndexAndLength(index, goodsLength);
      goodsAttributesInsert += goodsAttributesInsertValues.format(demo.sqlConversion(item)) + this.compareIndexAndLength(index, goodsLength);
    });
    goodsInsert += goodsAttributesInsert;
    let imagesLength = goodsParams.images.length;
    if (imagesLength > 0) {
      goodsInsert += imagesInsert;
      _.forEach(goodsParams.images, (item, index) => {
        goodsInsert += imagesInsertValues.format(demo.sqlConversion(item)) + this.compareIndexAndLength(index, imagesLength);
      });
    }
    if (inventoryParams.inventoryItems.length === 0) {
      dao.transaction(
        goodsInsert,
        () => {
          onSuccess(goods.majorCode);
        },
        onFail
      );
      return;
    }
    inventoryService.insert(
      inventoryParams,
      res => {
        dao.transaction(
          goodsInsert + res,
          () => {
            onSuccess(goods.majorCode);
          },
          onFail
        );
      },
      onFail
    );
  },
  compareIndexAndLength(index, length) {
    return index === length - 1 ? ';' : ',';
  },
  updateGetInsertImages: function (params, item, item1) {
    let images = _.assign({ ...this.goodsImages }, item1);
    images.code = item.code;
    images.goodFingerprint = item.fingerprint;
    images.fingerprint = md5(item.code + '_' + images.order);
    params.insertImages.push(images);
  },
  updatePre: function (params, updateGoods, insertGoods, onSuccess, onFail) {
    dao.exec(
      sqlApi.getGoodsChkDupMajorCode.format(params.id, params.majorCode),
      res => {
        if (res.length > 0) {
          onFail('货号已存在，请修改货号');
          return;
        }
        var updateGoodsLength = updateGoods.length;
        var insertGoodsLength = insertGoods.length;
        if (updateGoodsLength + insertGoodsLength === 0) {
          onSuccess();
          return;
        }

        var insertAndUpdateGoods = [...insertGoods, ...updateGoods];
        var insertAndUpdateCodes = _.compact(_.map(insertAndUpdateGoods, 'code'));
        var ids = _.compact(_.map(params.items, 'id'));
        var params1 = {};
        params1.wheres = `and code <> ''
                          and code in ('` + insertAndUpdateCodes.join("','") + `')
                          and id not in (` + ids.join() + `)`;
        params1.wheres1 = `and ext_barcode in ('` + insertAndUpdateCodes.join("','") + `')`;
        dao.exec(
          sqlApi.getGoodsCodeByWheres.format(params1),
          async res => {
            if (res.length > 0) {
              var repeatCodes = _.map(
                _.filter(res, item => {
                  return +item.isDeleted === 0;
                }),
                'code'
              );
              if (repeatCodes.length > 0) {
                onFail('条码已存在：' + repeatCodes.join());
                return;
              }
            }

            _.forEach(updateGoods, item => {
              item.majorCode = params.majorCode;
            });
            _.forEach(insertGoods, item => {
              item.majorCode = params.majorCode;
              item.fingerprint = md5(item.name);
            });

            var insertFingerprints = _.map(insertGoods, 'fingerprint');
            params1.wheres = `and fingerprint in ('${insertFingerprints.join("','")}')`;
            dao.exec(sqlApi.getGoodsByWheres.format(params1), res1 => {
              let repeatFingerprints = _.map(res1, 'fingerprint');
              let mNowTime = new Date().getTime();
              if (repeatFingerprints.length > 0) {
                _.filter(insertGoods, item => {
                  return repeatFingerprints.includes(item.fingerprint);
                }).forEach(item => {
                  item.fingerprint = md5(item.name + '_' + mNowTime);
                });
              }

              params.insertImages = [];
              _.forEach(updateGoods, item => {
                _.forEach(item.images, item1 => {
                  this.updatePreInsertAction(params, item, item1);
                });
              });
              _.forEach(insertGoods, item => {
                _.forEach(item.images, item1 => {
                  this.updateGetInsertImages(params, item, item1);
                });
              });
              if (params.insertImages.length === 0) {
                onSuccess();
                return;
              }
              dao.exec(
                sqlApi.getImagesByFingerprints.format("'" + _.map(params.insertImages, 'fingerprint').join("','") + "'"),
                imagesRes => {
                  _.forEach(imagesRes, item => {
                    _.find(params.insertImages, item1 => {
                      return item.fingerprint === item1.fingerprint;
                    }).fingerprint = md5(item.fingerprint + '_' + mNowTime);
                  });
                  onSuccess();
                },
                onFail
              );
            });
          },
          onFail
        );
      },
      onFail
    );
  },
  updatePreInsertAction(params, item, item1) {
    if (item1.action === 'insert') {
      this.updateGetInsertImages(params, item, item1);
    }
  },
  /**
   * 修改
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  update: async function (params, onSuccess, onFail) {
    params.manufactureDate = demo.isNullOrTrimEmpty(params.manufactureDate) ? null : params.manufactureDate;
    params.expiryDays = demo.isNullOrTrimEmpty(params.expiryDays) ? null : params.expiryDays;
    let params1 = _.cloneDeep(params);
    this.handleParams(params1);
    let notDeletedGoods = _.filter(params1.items, item => {
      return item.action !== 'delete';
    });
    let inputCodes = _.compact(_.map(notDeletedGoods, 'code'));
    if (inputCodes.length > new Set(inputCodes).size) {
      onFail('条码不允许重复');
      return;
    }
    let deleteGoods = _.filter(params1.items, item => {
      return item.action === 'delete';
    });
    let updateGoods = _.filter(params1.items, item => {
      return item.action === 'update';
    });
    let insertGoods = _.filter(params1.items, item => {
      return item.action === 'insert';
    });

    // 商品主信息删除了 商品关联表信息一并删除 传入商品名称和条码
    const deleteGoodsWhere = { code: inputCodes.join("','") };
    const deleteGoodsFingerprint = await dao.asyncExec(sqlApi.getDeleteGoodsByNameOrCode.format(deleteGoodsWhere));
    console.log(deleteGoodsFingerprint)
    if (!demo.isNullOrTrimEmpty(deleteGoodsFingerprint)) {
      await dao.asyncExec(sqlApi.goodsDelete.format(`'` + _.map(deleteGoodsFingerprint, 'fingerprint').join(`','`) + `'`));
    }
    this.updatePre(
      params1,
      updateGoods,
      insertGoods,
      () => {
        let sql = '';
        let delIds = _.compact(_.map(deleteGoods, 'id'));
        if (delIds.length > 0) {
          sql += sqlApi.goodsDelete.format(delIds.join());
        }
        let inventoryParams = {};
        inventoryParams.exec = 0;
        inventoryParams.dealType = INVENTORY_DEAL_TYPE.UPDATE_GOODS_INVENTORY;
        inventoryParams.inventoryItems = [];
        let goods = { ...this.goods };
        _.forEach(_.keys(goods), item => {
          goods[item] = params1[item];
        });
        goods.pinyin = params1.pinyin;
        goods.firstLetters = params1.firstLetters;
        sql += this.updateWithUpdateGoods(params1, updateGoods, inventoryParams, goods);

        Object.assign(goods, this.subGoods1);
        sql += this.updateWithInsertGoods(insertGoods, inventoryParams, goods);

        let insertImagesLength = params1.insertImages === undefined ? 0 : params1.insertImages.length;
        if (insertImagesLength > 0) {
          let imagesInsert = sqlApi.imagesInsert;
          let imagesInsertValues = sqlApi.imagesInsertValues;
          sql += imagesInsert;
          _.forEach(params1.insertImages, item => {
            sql += imagesInsertValues.format(demo.sqlConversion(item)) + ',';
          });
          sql = sql.substr(0, sql.length - 1) + ';';
        }
        if (sql === '') {
          onSuccess();
          return;
        }
        this.updateGoods(sql, inventoryParams, params1, () => {
          this.updateBarcodeScalesGoods(params, () => {
            if (updateGoods.length === 0) {
              onSuccess();
              return;
            }
            // 更新供应商关联表
            this.insertOrUpdategoodsSuppliers(updateGoods[0], onSuccess, onFail);
          }, onFail);
        }, onFail);
      }, onFail);
  },
  updateGoods: function (sql, inventoryParams, params1, onSuccess, onFail) {
    if (inventoryParams.inventoryItems.length === 0) {
      dao.transaction(sql, () => this.updateExtGoods(params1, onSuccess, onFail), onFail);
    } else {
      inventoryService.insert(
        inventoryParams,
        res => {
          dao.transaction(sql + res, () => this.updateExtGoods(params1, onSuccess, onFail), onFail);
        },
        onFail
      );
    }
  },
  updateExtGoods: function (params1, onSuccess, onFail) {
    var items = params1.items[0];
    var isCodesGoods = items.isCodesGoods;
    var extendCodes = items.extendCodes;
    var multiToSingleGoods = items.multiToSingleGoods;
    if (isCodesGoods === 1) {
      // 一品多码商品编辑
      goodsExtBarcodeService.updateGoodsExtBarcode(params1, onSuccess, onFail);
    } else if (isCodesGoods === 0) {
      if (multiToSingleGoods && multiToSingleGoods.length > 0) {
        // 关闭一品多码、拆分一品一码
        this.batchImportGoods(params1).then(onSuccess).catch(onFail);
      } else if (extendCodes && extendCodes.length > 0) {
        // 关闭一品多码
        goodsExtBarcodeService.updateGoodsExtBarcode(params1, onSuccess, onFail);
      } else {
        onSuccess();
      }
    } else {
      onSuccess();
    }
  },
  updateBarcodeScalesGoods(params, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(params.isBarcodeScalesGoods)) {
      onSuccess();
      return;
    }
    if (params.isBarcodeScalesGoods === 0) {
      dao.exec(sqlApi.deleteProductScale.format(params), onSuccess, onFail);
    } else {
      dao.exec(sqlApi.updateProductScale.format(params), onSuccess, onFail);
    }
  },
  updateWithUpdateGoods: function (params, updateGoods, inventoryParams, goods) {
    let sql = '';
    if (updateGoods.length > 0) {
      let goodsUpdateSql = sqlApi.goodsUpdate.format(demo.sqlConversion(goods));
      _.forEach(updateGoods, item => {
        if (+item.preCurStock !== +item.curStock) {
          let good = {};
          good.accountQty = item.preCurStock;
          good.actualQty = item.curStock;
          good.price = item.purPrice;
          good.goodFingerprint = item.fingerprint;
          inventoryParams.inventoryItems.push(good);
        }
        item.specs = null;
        sql += goodsUpdateSql.format(demo.sqlConversion(item));
      });
    }
    let updateOrNoChangeGoods = _.filter(params.items, item => {
      return item.action !== 'insert' && item.action !== 'delete';
    });
    let updateImages = [];
    _.forEach(_.map(updateOrNoChangeGoods, 'images'), item => {
      updateImages.push(
        ..._.filter(item, item1 => {
          return item1.action === 'update';
        })
      );
    });
    _.forEach(updateImages, item => {
      sql += sqlApi.imagesUpdate.format(demo.sqlConversion(item));
    });

    return sql;
  },
  updateWithInsertGoods: function (insertGoods, inventoryParams, goods) {
    let sql = '';
    if (insertGoods.length > 0) {
      let goodsInsert = sqlApi.goodsInsert;
      let goodsInsertValues = sqlApi.goodsInsertValues.format(demo.sqlConversion(goods));
      sql += goodsInsert;
      _.forEach(insertGoods, item => {
        if (+item.curStock) {
          let good = {};
          good.accountQty = 0;
          good.actualQty = item.curStock;
          good.price = item.purPrice;
          good.goodFingerprint = item.fingerprint;
          inventoryParams.inventoryItems.push(good);
        }
        item.specs = null;
        sql += goodsInsertValues.format(demo.sqlConversion(item)) + ',';
      });
      sql = sql.substr(0, sql.length - 1) + ';';
    }
    return sql;
  },
  /**
   * 商品库存预警设置功能开发
   * var params = {
   *  minStock: 1,
   *  maxStock: 100,
   *  ids: '1,2,3'
   * }
   * goodservice.updateGoodsMinAndMaxStock(params, res => {console.log(res)}, err => {console.error(err)});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateGoodsMinAndMaxStock: function (params, onSuccess, onFail) {
    dao.exec(sqlApi.updateGoodsMinAndMaxStock.format(params), onSuccess, onFail);
  },
  /**
   * 库存预警报表
   * var params = {
   *  type: 'search', //search、excel
   *  pageSize: 10,
   *  page: 1,
   *  overdueDay :2, //多少天内到期
   *  search: '沙琪玛',
   *  typeId: 97663c441d35c9b9674e57356b33c407, // 全部分类-1 称重分类传0
   *  status: 1, // 0:全部状态；1:库存不足；2:库存过剩；3:库存不足及过剩
   *  orderBy: 'cur_stock' // 库存：cur_stock，库存上线：max_stock，库存下限：min_stock
   *  sort: asc // asc desc
   * };
   * goodservice.goodsStockWarningReports(params, res => {console.log(res)});
   * @param {*} params
   * @param {*} onSuccess
   */
  goodsStockWarningReports: async function (params, onSuccess) {
    // 得到拼接后的条件
    this.goodsStockWarningReportsWhere(params);
    let count = await dao.asyncExec(sqlApi.goodsStockWarningReportsCount.format(params));
    let data = await dao.asyncExec(sqlApi.goodsStockWarningReportsData.format(params));

    const res = {
      // header: header,
      count: demo.t2json(count)[0]['cnt'],
      data: demo.t2json(data)
    };
    onSuccess(res);
  },
  /**
   * 库存预警报表 条件筛选
   */
  goodsStockWarningReportsWhere(params) {
    if (demo.isNullOrTrimEmpty(params.sort)) {
      params.orderBy = 'id';
    }
    params.sort = demo.isNullOrTrimEmpty(params.sort) || params.sort === 'ascending' ? 'asc' : 'desc';
    params.wheres = `where goods.is_deleted=0\n`;
    if (!demo.isNullOrTrimEmpty(params.search)) {
      params.search = params.search.replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      params.wheres += `and (goods.name like '%${params.search}%' ESCAPE '/'
        or goods.code like '%${params.search}%' ESCAPE '/'
        or goods_ext_barcode.ext_barcode like '%${params.search}%' ESCAPE '/'
        or goods.first_letters like '%${params.search}%' ESCAPE '/')`;
    }
    // 称重分类
    if (!demo.isNullOrTrimEmpty(params.typeId) && +params.typeId === 0) {
      params.wheres += `and goods.unit_fingerprint in (select fingerprint from units where name in
        ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))`;
      // 其他自定义分类
    } else if (!demo.isNullOrTrimEmpty(params.typeId) && +params.typeId !== -1) {
      params.wheres += `and (types.fingerprint='${params.typeId}' or types.parent_fingerprint = '${params.typeId}')\n`;
    }
    // 库存不足
    let status = params.status;
    if (status === 1) {
      params.wheres += `and (goods.max_stock >= goods.min_stock
          and goods.max_stock <> 0
          and goods.cur_stock < goods.min_stock
        )`;
      // 库存过剩
    } else if (status === 2) {
      params.wheres += `and (goods.max_stock <> 0
          and goods.cur_stock > goods.max_stock
        )`;
      // 库存不足及过剩
    } else if (status === 3) {
      params.wheres += `and (
        goods.min_stock <> 0
        and goods.cur_stock < goods.min_stock
      ) or (
        goods.max_stock <> 0
        and goods.cur_stock > goods.max_stock
      )`;
    } else {
      params.wheres += `and (
        (goods.min_stock is not null and goods.min_stock <> 0)
        or
        (goods.max_stock is not null and goods.max_stock <> 0)
      )`;
    }
    params.limit = '';
    // 供应商下拉列表
    params.wheres += this.getSupplierWhere(params);
    if (params.type === 'search' && params.pageSize !== -1) {
      let pageSize = +params.pageSize;
      let page = +params.page;
      params.limit = `limit ${pageSize} offset ${pageSize} * (${page} - 1)`;
    }
  },
  /**
   * 商品过期预警报表
   * var params = {
   *  type: 'search', //search、excel
   *  hideNoStockGoods: 1, // 隐藏无库存商品 1：是；其它：否
   *  pageSize: 10,
   *  page: 1,
   *  overdueDay :2, //多少天内到期
   *  search: '沙琪玛',
   *  typeId: 97663c441d35c9b9674e57356b33c407, // 全部分类-1 称重分类传0
   *  orderBy: 'cur_stock' // 库存：cur_stock，库存上线：max_stock，库存下限：min_stock
   *  sort: asc // asc desc
   * };
   * goodservice.goodsStockWarningReports(params, res => {console.log(res)});
   * @param {*} params
   * @param {*} onSuccess
   */
  goodsExpirationWarningReports: async function (params, onSuccess) {
    // 得到拼接后的条件
    this.goodsExpirationWarningReportsWhere(params);
    let count = await dao.asyncExec(sqlApi.goodsStockWarningReportsCount.format(params));
    let data = await dao.asyncExec(sqlApi.goodsExpirationWarningReportsData.format(params));
    const res = {
      // header: header,
      count: demo.t2json(count)[0]['cnt'],
      data: demo.t2json(data)
    };
    onSuccess(res);
  },
  /**
   * 商品过期预警报表 条件筛选
   */
  goodsExpirationWarningReportsWhere(params) {
    if (demo.isNullOrTrimEmpty(params.orderBy)) {
      params.orderBy = 'id';
    }
    params.sort = demo.isNullOrTrimEmpty(params.sort) || params.sort === 'ascending' ? 'asc' : 'desc';
    params.wheres = `where goods.is_deleted=0
                      and ga.expiry_days <> 0
                      and (JULIANDAY(ga.manufacture_date)-JULIANDAY(date())+ga.expiry_days | 0) <= ${params.overdueDate}\n`;
    if (!demo.isNullOrTrimEmpty(params.search)) {
      let search = params.search.toLowerCase().replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      params.wheres += `and (lower(goods.name) like '%${search}%' ESCAPE '/'
        or lower(goods.code) like '%${search}%' ESCAPE '/'
        or lower(goods_ext_barcode.ext_barcode) like '%${search}%' ESCAPE '/'
        or lower(goods.first_letters) like '%${search}%' ESCAPE '/')\n`;
    }
    // 称重分类
    if (!demo.isNullOrTrimEmpty(params.typeId) && +params.typeId === 0) {
      params.wheres += `and goods.unit_fingerprint in (select fingerprint from units where name in
        ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))\n`;
      // 其他自定义分类
    } else if (!demo.isNullOrTrimEmpty(params.typeId) && +params.typeId !== -1) {
      params.wheres += `and (types.fingerprint='${params.typeId}' or types.parent_fingerprint = '${params.typeId}')\n`;
    }
    if (+params.hideNoStockGoods === 1) {
      params.wheres += `and goods.cur_stock > 0\n`;
    }
    params.limit = '';
    // 供应商下拉列表
    params.wheres += this.getSupplierWhere(params);
    if (params.type === 'search' && params.pageSize !== -1) {
      let pageSize = +params.pageSize;
      let page = +params.page;
      params.limit = `limit ${pageSize} offset ${pageSize} * (${page} - 1)`;
    }
  },
  // 批量删除分类下的商品并删除此分类  params:{typeId:'分类id',isDelType:'1：是,0：否'}
  deleteByTypes: function (params, onsuccess, onfail) {
    let typeArrys = JSON.parse(JSON.stringify(params.fingerprint));
    let deleteTypes = () => {
      if (params.isDelType === '1') {
        typeArrys = typeArrys.filter(e => e !== '97663c441d35c9b9674e57356b33c407').join(`','`);
        if (typeArrys === '') {
          onsuccess();
        } else {
          dao.exec(
            sqlApi.typeDel.format({ fingerprint: typeArrys }),
            () => {
              onsuccess();
            },
            onfail
          );
        }
      } else {
        onsuccess();
      }
    };
    // 查询此分类的子分类然后一并删除
    dao.exec(sqlApi.typeIdAndTwoTypeId.format({ fingerprint: params.fingerprint.join(`','`) }), res => {
      dao.exec(
        sqlApi.goodsDelByTypes.format({
          fingerprint: demo
            .t2json(res)
            .map(e => e.fingerprint)
            .join(`','`)
        }),
        () => {
          // 表示称重商品
          if (typeArrys.indexOf('0') !== -1) {
            dao.exec(
              sqlApi.goodsDelByWeighingTypes,
              () => {
                deleteTypes();
              },
              onfail
            );
          } else {
            deleteTypes();
          }
        },
        onfail
      );
    });
  },
  /**
   * 删除
   * var params = {fingerprint: 'aaa','bbb','ccc'}
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  delete: function (params, onSuccess, onFail) {
    dao.transaction(sqlApi.goodsDelete.format(params.fingerprint), onSuccess, onFail);
  },

  /**
   * 商品批量导入
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  batchImport: function (params, onSuccess, onFail) {
    let importGoods = params.goods;
    importGoods.forEach((item, index) => {
      item.id = index;
    });
    let initImportGoods = _.cloneDeep(importGoods);
    let firstRow = importGoods.shift();
    const columns = ['code', 'name', 'pinyin', 'type', 'subType', 'unit', 'salePrc', 'vipPrc', 'purPrc', 'curStock', 'manufactureDate', 'expiryDays'];

    if (importGoods.length === 0) {
      onFail('导入数据为空');
      return;
    }

    let diffColumns = _.difference(columns, _.keys(firstRow));
    if (diffColumns.length > 0) {
      onFail('模板错误，请下载最新模板');
      return;
    }

    this.batchImportInputCheck(importGoods, columns);
    this.batchImportNameAndCodeCheck(importGoods);
    this.batchImportDo(firstRow, initImportGoods, importGoods, params.autoGenerateCodes, onSuccess, onFail);
  },
  batchImportInputCheck: function (importGoods, columns) {
    const numericColumns = ['salePrc', 'vipPrc', 'purPrc', 'curStock', 'manufactureDate', 'expiryDays', 'pinyin'];
    importGoods.forEach(item => {
      columns.forEach(field => {
        if (numericColumns.includes(field) && demo.isNullOrTrimEmpty(item[field])) {
          item[field] = null;
        } else {
          item[field] = String(item[field])
            .replace(/'/g, '‘')
            .replace(/;/g, '；')
            .replace(/\s/g, '')
            .replaceAll(field === 'name' ? '$' : '', '');
        }
      });

      let errMsg = '';
      errMsg += this.batchImportFieldCheck(item.name, '商品名称', 'text', 60, true);
      if (demo.isNullOrTrimEmpty(item.type) && !demo.isNullOrTrimEmpty(item.subType)) {
        errMsg += '二级分类必须依赖一级分类而存在；';
      }
      var specialTypes = ['其他分类'];
      if (specialTypes.includes(item.type.trim()) && !demo.isNullOrTrimEmpty(item.subType)) {
        errMsg += '一级分类是"其他分类"时，二级分类应该为空；';
      }
      errMsg += this.batchImportFieldCheck(item.type, '一级分类', 'bLen', 20, false);
      errMsg += this.batchImportFieldCheck(item.subType, '二级分类', 'bLen', 20, false);
      errMsg += this.batchImportFieldCheck(item.unit, '单位', 'text', 5, false);
      errMsg += this.batchImportFieldCheck(item.code, '条码', 'text', 16, false);
      errMsg += this.batchImportFieldCheck(item.pinyin, '商品简称', 'text', 4, false);
      errMsg += this.batchImportFieldCheck(item.salePrc, '售价', 'money', 0, true);
      errMsg += this.batchImportFieldCheck(item.vipPrc, '会员价', 'money', 0, false);
      errMsg += this.batchImportFieldCheck(item.purPrc, '进货价', 'purMoney', 0, false);
      errMsg += this.batchImportFieldCheck(item.curStock, '库存', 'stock', 0, false);
      errMsg += this.batchImportFieldCheck(item.manufactureDate, '生产日期', 'date', 0, false);
      errMsg += this.batchImportFieldCheck(item.expiryDays, '保质期(天)', 'number', 0, false);

      item.firstLetters = demo.isNullOrTrimEmpty(item.name) ? null : pyCode.getPyCode(String(item.name));
      item.errMsg = errMsg;
    });
  },
  batchImportNameAndCodeCheck: function (importGoods) {
    let nameGroup = _.groupBy(
      _.filter(importGoods, item => {
        return !demo.isNullOrTrimEmpty(item.name);
      }),
      'name'
    );
    let errMsg = '商品名称重复；';
    _.values(nameGroup).forEach(val => {
      if (val.length > 1) {
        _.map(val, item => {
          item.errMsg += errMsg;
        });
      }
    });

    let codeGroup = _.groupBy(
      _.filter(importGoods, item => {
        return !demo.isNullOrTrimEmpty(item.code);
      }),
      'code'
    );
    errMsg = '条码重复；';
    _.values(codeGroup).forEach(val => {
      if (val.length > 1) {
        _.map(val, item => {
          item.errMsg += errMsg;
        });
      }
    });
  },
  batchImportFieldCheck: function (field, fieldName, type, length, notNull) {
    let errMsg = '';

    if (notNull && demo.isNullOrTrimEmpty(field)) {
      errMsg += fieldName + '不能为空；';
      return errMsg;
    }
    if (demo.isNullOrTrimEmpty(field)) {
      return errMsg;
    }

    if (type === 'money' || type === 'stock') {
      errMsg += this.moneyOrStockErrMsg(field, fieldName, type, errMsg);
    } else if (type === 'purMoney') {
      errMsg += this.purMoneyErrMsg(field, fieldName, errMsg, length);
    } else if (type === 'text') {
      errMsg += this.textErrMsg(field, fieldName, errMsg, length);
    } else if (type === 'date') {
      errMsg += this.dateErrMsg(field, fieldName, errMsg);
    } else if (type === 'number') {
      errMsg += this.numberErrMsg(field, fieldName, errMsg);
    } else if (type === 'bLen') {
      errMsg += this.bLenErrMsg(field, fieldName, errMsg, length);
    }

    return errMsg;
  },
  moneyOrStockErrMsg(field, fieldName, type, errMsg) {
    if (isNaN(field)) {
      errMsg += fieldName + '格式错误；';
    } else {
      if (field < 0) {
        errMsg += fieldName + '不能小于0；';
      }
      if (field > 999999.99 && type === 'money') {
        errMsg += fieldName + '不能大于999999.99；';
      }
      if (field > 99999.999 && type === 'stock') {
        errMsg += fieldName + '不能大于99999.999；';
      }
      let arrays = (field + '').split('.');

      let len = type === 'money' ? 2 : 3;
      if (arrays.length > 1 && arrays[1].length > len) {
        errMsg += fieldName + `小数位数不能超过${len}位；`;
      }
    }
    return errMsg;
  },
  purMoneyErrMsg(field, fieldName, errMsg) {
    if (isNaN(field)) {
      errMsg += fieldName + '格式错误；';
    } else {
      if (field < 0) {
        errMsg += fieldName + '不能小于0；';
      }
      if (field > 999999.999999) {
        errMsg += fieldName + '不能大于999999.999999；';
      }
      let arrays = (field + '').split('.');

      if (arrays.length > 1 && arrays[1].length > 6) {
        errMsg += fieldName + `小数位数不能超过${6}位；`;
      }
    }
    return errMsg;
  },
  textErrMsg(field, fieldName, errMsg, length) {
    if (field.length > length) {
      errMsg += fieldName + `长度不能大于${length}；`;
    }
    let pats = ['条码'];
    if (pats.includes(fieldName) && !/^[A-Za-z0-9-]{0,}$/.test(field)) {
      errMsg += fieldName + '必须为字母、数字或中横线；';
    }

    let pats1 = ['商品简称'];
    if (pats1.includes(fieldName) && !/^[\u4E00-\u9FA5A-Za-z0-9!@#%^&*()_\-=+{}"<>?[\],.\\/`~]{0,4}$/.test(field)) {
      errMsg += fieldName + '必须为中文或字母或数字和!@#%^&*()_-=+{}"<>?[],./`~符号';
    }
    return errMsg;
  },
  bLenErrMsg(field, fieldName, errMsg, length) {
    if (this.getBLen(field) > length) {
      errMsg += fieldName + `最多${length / 2}个字(${length}个字符)；`;
    }
    return errMsg;
  },
  dateErrMsg(field, fieldName, errMsg) {
    // 格式YYYY-MM-DD
    let formatDate = /^(\d{4})(-)(\d{2})\2(\d{2})$/;
    if (!formatDate.test(field)) {
      errMsg += fieldName + '必须是YYYY-MM-DD；';
    }
    if (new Date(field).getDate() !== +field.substring(field.length - 2)) {
      errMsg += fieldName + '必须是合法的日期';
    }
    return errMsg;
  },
  numberErrMsg(field, fieldName, errMsg) {
    // 正整数
    let formatDate = /^(?:[1-9]\d{0,3})?$/;
    if (!formatDate.test(field)) {
      errMsg += fieldName + '必须是不超过9999的正整数；';
    }
    return errMsg;
  },
  getBLen(str) {
    if (str === null) return 0;
    if (typeof str !== 'string') {
      str += '';
    }
    // eslint-disable-next-line no-control-regex
    return str.replace(/[^\x00-\xff]/g, "00").length;
  },
  batchImportInsert: async function (importGoods) {
    let sql = sqlApi.importGoodsTruncate;
    importGoods.forEach(item => {
      item.majorCode = null;
      item.brand = null;
      item.season = null;
      item.sex = null;
      item.composition = null;
      item.level = null;
      item.specs1 = null;
      item.specs2 = null;
      item.specs3 = null;
      item.initPrc = null;
      if (demo.isNullOrTrimEmpty(item.code)) {
        item.code = null;
      }
      sql += sqlApi.importGoodsInsert.format(demo.sqlConversion(item));
    });
    sql += sqlApi.importGoodsUpdateErrMsgName;
    sql += sqlApi.importGoodsUpdateErrMsgCode;
    await dao.asyncTransaction(sql);
  },
  batchImportDo: async function (firstRow, initImportGoods, importGoods, autoGenerateCodes, onSuccess, onFail) {
    await this.batchImportInsert(importGoods);

    if (+autoGenerateCodes === 1) {
      var paramsWithoutCode = _.filter(importGoods, item => {
        return demo.isNullOrTrimEmpty(item.code);
      });

      var paramsWithoutCodeLength = paramsWithoutCode.length;
      if (paramsWithoutCodeLength > 0) {
        var codes = await this.asyncMakeCode(paramsWithoutCodeLength, 1);
        paramsWithoutCode.forEach((item, index) => {
          item.code = codes[index];
        });
      }
    }

    dao.exec(
      sqlApi.getImportGoods,
      async importGoods => {
        var errFirstIndex = _.findIndex(importGoods, item => {
          return !demo.isNullOrTrimEmpty(item.errMsg);
        });

        importGoods.unshift(firstRow);
        if (errFirstIndex === -1) {
          let resLength = importGoods.length;
          for (let i = 1; i < resLength; i++) {
            let item = importGoods[i];
            this.defaultType(item);
          }

          var pddCode = await orderService.syncGet({ type: 'PDD' });
          var code = pddCode[0]['code'];
          syncService.syncCheck(
            (res, id) => {
              this.batchImportChecked(code, res, id, importGoods, onSuccess, onFail);
            },
            () => {
              onFail('存在正在同步的处理，请稍后再同步数据。');
            }
          );
        } else {
          var errObject = {};
          importGoods.forEach(item => {
            errObject[item.id] = item.errMsg;
          });
          initImportGoods.forEach((item, index) => {
            if (index > 0) {
              item.errMsg = errObject[item.id];
            }
          });
          onFail(initImportGoods);
        }
      },
      onFail
    );
  },
  defaultTypeAndUnit(item) {
    if (demo.isNullOrTrimEmpty(item.type) && demo.isNullOrTrimEmpty(item.subType)) {
      item.type = '其他分类';
    }
    if (demo.isNullOrTrimEmpty(item.unit)) {
      item.unit = '件';
    }
  },
  defaultType(item) {
    if (demo.isNullOrTrimEmpty(item.type) && demo.isNullOrTrimEmpty(item.subType)) {
      item.type = '其他分类';
    }
  },
  batchImportChecked(code, res, id, importGoods, onSuccess, onFail) {
    if (id <= 0) {
      onFail('存在正在同步的处理，请稍后再同步数据。');
      return;
    }

    var syncAt = res.data.data.createAt;
    demo.$http
      .post(
        demo.$rest.batchImport,
        { syncAt, importGoods, code },
        {
          maxContentLength: Infinity,
          timeout: 300000
        }
      )
      .then(res1 => {
        syncService.syncUpEnd(id, 1);
        var data = res1.data;
        console.log(data);
        if (+data.code !== 200) {
          onFail(data.msg);
          return;
        }

        var syncedAt = res.data.data.syncedAt;
        var createAt = res.data.data.createAt;
        var syncDownDate = createAt;
        if (!demo.isNullOrTrimEmpty(syncedAt)) {
          syncDownDate = syncedAt;
        }
        syncService.syncDownDate = syncDownDate;
        syncService.syncUpDate = null;
        syncService.syncDownLoad(
          () => {
            settingService.put({ key: 'syncHistoryId', value: id });
            demo.$store.dispatch('info/getSpec').then(() => {
              onSuccess('商品批量导入成功。');
            });
          },
          () => {
            onSuccess('商品批量导入成功，但云端数据下载失败。');
          }
        );
      })
      .catch(err => {
        syncService.syncUpEnd(id, 1);
        onFail(err);
      });
  },
  isEmpty: function (obj, columns) {
    var a = true;
    var columnsLength = columns.length;
    for (var i = 0; i < columnsLength; i++) {
      a = a && demo.isNullOrTrimEmpty(obj[columns[i]]);
      if (!a) {
        break;
      }
    }
    return a;
  },
  getDataTypeSql(data, sql) {
    if (demo.isNullOrTrimEmpty(data.type)) {
      data.sql = sql;
    } else if (+data.type === 0) {
      data.sql = sql + sqlApi.goods_unit;
    } else if (data.getDel && data.type.split(',').length > 1) {
      data.sql = sql + sqlApi.goods_nounit_del;
    } else {
      data.sql = sql + sqlApi.goods_nounit.format(data);
    }
    return data;
  },
  // 热销商品
  hot: function (onSuccess, onFail) {
    dao.exec(sqlApi.goods_hot, onSuccess, onFail);
  },
  // 判断商品是否存在
  exist: function (data, onSuccess, onFail) {
    var datacp = _.cloneDeep(data);
    datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');
    if (data.id === undefined) {
      if (data.code === undefined || data.code === '') {
        dao.exec(sqlApi.good_insert_exist_byname.format(datacp), onSuccess, onFail);
      } else {
        dao.exec(sqlApi.good_insert_exist_bycode.format(datacp), onSuccess, onFail);
      }
    } else {
      // 编辑商品-保存：判断商品code、name是否已经存在（name不为空，code可以为空）
      if (data.code === undefined || data.code === '') {
        dao.exec(sqlApi.good_update_exist_byname.format(datacp), onSuccess, onFail);
      } else {
        dao.exec(sqlApi.good_update_exist_bycode.format(datacp), onSuccess, onFail);
      }
    }
  },
  existGoods: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.goodInsertExist.format(data), onSuccess, onFail);
  },
  checkGoodCodeAndGoodExtBarcode: async function (data, onSuccess, onFail) {
    // 商品主信息删除了 商品关联表信息一并删除 传入商品名称和条码
    const deleteGoodsWhere = { code: data.code };
    const deleteGoodsFingerprint = await dao.asyncExec(sqlApi.getDeleteGoodsByNameOrCode.format(deleteGoodsWhere));
    console.log(deleteGoodsFingerprint)
    if (!demo.isNullOrTrimEmpty(deleteGoodsFingerprint)) {
      await dao.asyncExec(sqlApi.goodsDelete.format(`'` + _.map(deleteGoodsFingerprint, 'fingerprint').join(`','`) + `'`));
    }
    dao.exec(
      sqlApi.checkGoodCodeAndGoodExtBarcode.format(data),
      codeArray => {
        const groups = codeArray.reduce((groups, good) => {
          const key = good.isDel;
          if (!groups[key]) {
            groups[key] = [];
          }
          groups[key].push(good);
          return groups;
        }, {});
        console.log('条码检查数据库返回结果:', groups);
        // 一品多码商品重复
        if (groups[0] != undefined) {
          onSuccess(groups[0]);
          return;
        }
        // 一品一码商品重复
        if (groups[1] != undefined) {
          onSuccess(groups[1]);
          return;
        }
        onSuccess([]);
      },
      onFail
    );
  },
  use: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.good_use.format(data), onSuccess, onFail);
  },
  getDeletedGoods: function (data) {
    var ids = data
      .map(item => {
        return item.id;
      })
      .join();
    return data.length > 0
      ? `SELECT id, name, pinyin, code, unit_id, type_id, pur_price, sale_price, -cur_stock as cur_stock, fingerprint, first_letters
      from goods where id in (` +
      ids +
      `)`
      : `SELECT 0 as id, '' as name, '' as  pinyin, '' as code, 0 as unit_id, 0 as type_id, 0 as pur_price, 0 as sale_price,0 as cur_stock,
      '' as fingerprint, '' as first_letters`;
  },
  panDianPut: function (selectSql, maxid, sqlName, sqlList, resData, uid, parameter) {
    let onSuccess = parameter.onSuccess;
    let onFail = parameter.onFail;
    // 做盘点单
    dao.exec(
      sqlApi.good_batch_select.format(selectSql, maxid, sqlName),
      res1 => {
        let goodsList = demo.t2json(res1);
        if (goodsList.length > 0) {
          orderService.get(
            {
              type: 'PDD',
              table: 'inventories'
            },
            resCode1 => {
              let code = demo.t2json(resCode1)[0].code;
              var listLog = [];
              listLog[0] = code;
              let obj = _.cloneDeep(logList.inventories);
              obj.description = obj.description.format(listLog);
              demo.actionLog(obj);
              let inventoryItemsSql1 = '';
              let params1 = {};
              params1.uid = uid;
              params1.account_qty = 0;
              params1.actual_qty = 0;
              params1.diff_qty = 0;
              params1.diff_amt = 0;
              params1.code = code;
              params1.remark = '';
              params1.is_deleted = 0;
              params1.is_synced = 0;
              params1.fingerprint = md5(code);
              params1.dealType = INVENTORY_DEAL_TYPE.BATCH_IMPORT_INVENTORY;
              dao.exec(
                sqlApi.inventory_select_maxid,
                resMaxId1 => {
                  let inventoryMaxId1 = +demo.t2json(resMaxId1)[0].id + 1;
                  goodsList.map(goods => {
                    goods.good_id = goods.id;
                    goods.in_out = 1;
                    goods.account_qty = 0;
                    goods.actual_qty = +goods.cur_stock;
                    goods.diff_qty = +goods.cur_stock;
                    goods.price = goods.pur_price;
                    goods.diff_amt = goods.pur_price * goods.cur_stock;
                    goods.remark = params1.remark;
                    goods.is_deleted = params1.is_deleted;
                    goods.is_synced = params1.is_synced;
                    goods.fingerprint = md5(code + '_' + goods.id);
                    goods.inventory_fingerprint = params1.fingerprint;
                    goods.inventory_id = inventoryMaxId1;
                    params1.actual_qty += goods.actual_qty;
                    params1.diff_qty += goods.diff_qty;
                    params1.diff_amt += goods.diff_amt;
                    inventoryItemsSql1 += sqlApi.inventoryItemsInsertValues.format(goods);
                  });
                  inventoryItemsSql1 = inventoryItemsSql1.substr(0, inventoryItemsSql1.length - 1) + ';';
                  dao.exec(
                    sqlApi.inventory_batchPut_insert.format(params1) + sqlApi.inventoryItemsInsert + inventoryItemsSql1,
                    () => {
                      this.batchPutItem(
                        {
                          resData: resData,
                          sqlList: sqlList,
                          uid: uid
                        },
                        onSuccess,
                        onFail
                      );
                    },
                    onFail
                  );
                },
                onFail
              );
            }
          );
        } else {
          this.batchPutItem(
            {
              resData: resData,
              sqlList: sqlList,
              uid: uid
            },
            onSuccess,
            onFail
          );
        }
      },
      onFail
    );
  },
  batchPutItem: function (data, onSuccess, onFail) {
    let resData = data.resData;
    let uid = data.uid;
    let sqlList = data.sqlList;
    let sql = sqlList[0].sql;
    let selectSql = sqlList[0].selectSql;
    let sqlName = '';
    sqlList = sqlList.length > 1 ? sqlList.slice(1) : [{}];
    if (sql) {
      dao.exec(
        sqlApi.good_batch_max_id,
        maxId => {
          dao.exec(
            sqlApi.getGoodsByWheres.format({ wheres: ' and is_deleted = 1' }),
            deletedData => {
              let deletedDatas = demo.t2json(deletedData);
              sqlName = this.getDeletedGoods(deletedDatas);
              let maxid = +demo.t2json(maxId)[0].id + 1;
              dao.exec(
                sqlApi.good_batch_put.format(sql, selectSql, maxid, sqlName),
                errorData => {
                  resData.errData = resData.errData.concat(
                    demo.t2json(errorData).map(errRow => {
                      errRow.errMag = errRow.err;
                      return errRow;
                    })
                  );
                  // 删除商品的既存库存盘点单作成
                  dao.exec(
                    sqlApi.good_batch_select_deleted.format(selectSql, sqlName),
                    res2 => {
                      let goodsDeletedList = demo.t2json(res2);
                      if (goodsDeletedList.length > 0) {
                        orderService.get(
                          {
                            type: 'PDD',
                            table: 'inventories'
                          },
                          resCode => {
                            let code = demo.t2json(resCode)[0].code;
                            let inventoryItemsSql = '';
                            let params = {};
                            params.uid = uid;
                            params.account_qty = 0;
                            params.actual_qty = 0;
                            params.diff_qty = 0;
                            params.diff_amt = 0;
                            params.code = code;
                            params.remark = '';
                            params.is_deleted = 0;
                            params.is_synced = 0;
                            params.fingerprint = md5(code);
                            params.dealType = INVENTORY_DEAL_TYPE.BATCH_IMPORT_OFFSET;
                            dao.exec(
                              sqlApi.inventory_select_maxid,
                              resMaxId => {
                                let inventoryMaxId = +demo.t2json(resMaxId)[0].id + 1;
                                deletedDatas
                                  .filter(deletGoods => goodsDeletedList.some(deletedGoods => deletedGoods.id === deletGoods.id))
                                  .map(goods => {
                                    goods.good_id = goods.id;
                                    goods.in_out = 1;
                                    goods.account_qty = +goods.cur_stock;
                                    goods.actual_qty = 0;
                                    goods.diff_qty = -+goods.cur_stock;
                                    goods.price = goods.pur_price;
                                    goods.diff_amt = goods.pur_price * -goods.cur_stock;
                                    goods.remark = params.remark;
                                    goods.is_deleted = params.is_deleted;
                                    goods.is_synced = params.is_synced;
                                    goods.fingerprint = md5(code + '_' + goods.id);
                                    goods.inventory_fingerprint = params.fingerprint;
                                    goods.inventory_id = inventoryMaxId;
                                    params.account_qty += goods.account_qty;
                                    params.diff_qty += goods.diff_qty;
                                    params.diff_amt += goods.diff_amt;
                                    inventoryItemsSql += sqlApi.inventoryItemsInsertValues.format(goods);
                                  });
                                inventoryItemsSql = inventoryItemsSql.substr(0, inventoryItemsSql.length - 1) + ';';
                                dao.exec(
                                  sqlApi.inventory_batchPut_insert.format(params) + sqlApi.inventoryItemsInsert + inventoryItemsSql,
                                  () => {
                                    // 做盘点单
                                    let parameter1 = {
                                      onSuccess: onSuccess,
                                      onFail: onFail
                                    };
                                    this.panDianPut(selectSql, maxid, sqlName, sqlList, resData, uid, parameter1);
                                  },
                                  onFail
                                );
                              },
                              onFail
                            );
                          }
                        );
                      } else {
                        // 做盘点单
                        let parameter = {
                          onSuccess: onSuccess,
                          onFail: onFail
                        };
                        this.panDianPut(selectSql, maxid, sqlName, sqlList, resData, uid, parameter);
                      }
                    },
                    onFail
                  );
                },
                onFail
              );
            },
            onFail
          );
        },
        onFail
      );
    } else {
      resData.data.wrong = resData.errData.length;
      resData.data.correct = resData.data.total - resData.data.wrong;
      onSuccess(resData);
    }
  },
  sonar(sqlList, resData, uid, onSuccess, onFail) {
    if (sqlList.length > 0) {
      this.batchPutItem(
        {
          resData: resData,
          sqlList: sqlList,
          uid: uid
        },
        onSuccess,
        onFail
      );
    } else {
      resData.data.wrong = resData.errData.length;
      resData.data.correct = resData.data.total - resData.data.wrong;
      onSuccess(resData);
    }
  },
  batchPut: function (data, onSuccess, onFail) {
    const uid = data.uid;
    const nameRepeatList = data.nameRepeatList;
    const codeRepeatList = data.codeRepeatList;
    let resData = {
      data: {
        total: data.products.length,
        correct: 0,
        wrong: 0
      },
      errData: []
    };
    let sql = '';
    let selectSql = '';
    let typeList = new Map();
    let unitList = new Map();
    dao.exec(
      sqlApi.typeBatchSelect,
      types => {
        dao.exec(
          sqlApi.unitBatchSelect,
          units => {
            demo.t2json(types).map(typeA => {
              typeList.set(typeA.name, typeA.id);
            });
            demo.t2json(units).map(unit => {
              unitList.set(unit.name, unit.id);
            });
            let sqlCount = 0;
            let sqlList = [];
            data.products.map(inSql => {
              let rowData = this.checkData(inSql, typeList, unitList, nameRepeatList, codeRepeatList);
              if (rowData.errFlg) {
                resData.errData.push(rowData);
              } else {
                if (demo.isNullOrTrimEmpty(rowData.first_letters) || rowData.first_letters === '{first_letters}') {
                  rowData.first_letters = pyCode.getPyCode(rowData.name);
                }
                sql += sqlApi.good_batch_put_values.format(rowData);
                selectSql += sqlApi.good_batch_put_select_values.format(rowData);
                sqlCount++;
                if (sqlCount === 500) {
                  sql = sql.substr(0, sql.length - 10);
                  selectSql = selectSql.substr(0, selectSql.length - 10);
                  sqlList.push({ sql: sql, selectSql: selectSql });
                  sqlCount = 0;
                  sql = '';
                  selectSql = '';
                }
              }
            });
            if (sql) {
              sql = sql.substr(0, sql.length - 10);
              selectSql = selectSql.substr(0, selectSql.length - 10);
              sqlList.push({ sql: sql, selectSql: selectSql });
            }
            this.sonar(sqlList, resData, uid, onSuccess, onFail);
          },
          onFail
        );
      },
      onFail
    );
  },
  checkDataName(data, nameRepeatList) {
    if (data.name) {
      data.fingerprint = md5(data.name);
      if (nameRepeatList.has(data.name)) {
        data.errMag += '商品名重复;';
        data.errFlg = true;
      }
      if (data.name.length > 60) {
        data.errMag += '商品名称字符超长度限制，最多为六十个字符;';
        data.errFlg = true;
      }
      if (demo.isNullOrTrimEmpty(data.first_letters) || data.first_letters === '{first_letters}') {
        data.first_letters = pyCode.getPyCode(data.name);
      }
    } else {
      data.errMag += '商品名称不能为空;';
      data.errFlg = true;
    }
    return data;
  },
  checkDataCode(data, codeRepeatList) {
    if (data.code) {
      if (codeRepeatList.has(data.code)) {
        data.errMag += '商品条码重复;';
        data.errFlg = true;
      }
      if (data.code.length > 13) {
        data.errMag += '商品条码字符超长度限制，最多为十三个数字;';
        data.errFlg = true;
      } else {
        data.fingerprint = md5(data.code);
      }
      if (!/^[0-9]*$/.test(data.code)) {
        data.errMag += '商品条码只能为数字;';
        data.errFlg = true;
      }
    } else {
      data.code = '';
    }
    return data;
  },
  checkDataNumber(data, num, strParmer) {
    if (num) {
      let err;
      if (strParmer === '库存数量') {
        err = stringUtils.checkNumber(num, strParmer, {
          length: 3,
          min: '0',
          max: '99999.999'
        });
      } else {
        err = stringUtils.checkNumber(num, strParmer, {
          length: 2,
          min: '0',
          max: '99999.99'
        });
      }
      if (err) {
        data.errMag += err;
        data.errFlg = true;
      }
    } else {
      if (strParmer === '会员价') {
        data.trade_prc = '';
      }
      if (strParmer === '进价') {
        data.pur_prc = '';
      }
      if (strParmer === '库存数量') {
        data.cur_stock = 0;
      }
    }
    return data;
  },
  checkDataType(data, typeList) {
    if (data.type) {
      data.type_id = typeList.get(data.type);
      if (!data.type_id) {
        if (data.type.length > 5) {
          data.errMag += '商品类别字数超过五个;';
        } else {
          data.errMag += '在商品类别表里没有此商品类别;';
        }
        data.errFlg = true;
      }
    } else {
      data.type = '';
      data.type_id = 2;
    }
    return data;
  },
  checkData(rowData, typeList, unitList, nameRepeatList, codeRepeatList) {
    let data = rowData;
    data.errMag = data.errMag || '';
    data = this.checkDataName(data, nameRepeatList);
    if (data.sale_prc) {
      let err = stringUtils.checkNumber(data.sale_prc, '商品售价', {
        length: 2,
        min: '0',
        max: '999999.99'
      });
      if (err) {
        data.errMag += err;
        data.errFlg = true;
      }
    } else {
      data.errMag += '商品售价不能为空;';
      data.errFlg = true;
    }
    data = this.checkDataCode(data, codeRepeatList);
    if (data.pinyin) {
      data.pinyin = data.pinyin.replace(/'/g, '‘').replace(/;/g, '；');
      if (data.pinyin.length > 4) {
        data.errMag += '商品简称超长度限制，最多为四个字符;';
        data.errFlg = true;
      }
    } else {
      data.pinyin = '';
    }
    data = this.checkDataNumber(data, data.trade_prc, '会员价');
    data = this.checkDataNumber(data, data.pur_prc, '进价');
    data = this.checkDataNumber(data, data.cur_stock, '库存数量');
    data = this.checkDataType(data, typeList);
    data = this.checkDataUnit(data, unitList);
    return data;
  },
  checkDataUnit(data, unitList) {
    if (data.unit) {
      data.unit_id = unitList.get(data.unit);
      if (!data.unit_id) {
        if (data.unit.length > 5) {
          data.errMag += '商品单位字数超过五个;';
        } else {
          data.errMag += '在商品单位表里没有此商品单位;';
        }
        data.errFlg = true;
      }
    } else {
      data.unit = '';
      data.unit_id = '';
    }
    return data;
  },

  putNewGood: function (params, paramsCp, onSuccess, onFail) {
    var type = {};
    type.name = params.type_name;
    typeService.exist(
      type,
      function (res) {
        var ret = demo.t2json(res);
        if (ret.length > 0) {
          params.fingerprint = md5(params.name);
          paramsCp.fingerprint = params.fingerprint;
          if (demo.isNullOrTrimEmpty(paramsCp.first_letters) || paramsCp.first_letters === '{first_letters}') {
            paramsCp.first_letters = pyCode.getPyCode(params.name);
          }
          if (params.id === undefined) {
            dao.exec(sqlApi.goodsInsert.format(paramsCp), onSuccess, onFail);
          } else {
            dao.exec(sqlApi.goodsUpdate.format(paramsCp), onSuccess, onFail);
          }
        } else {
          demo.$toast('请重新选择分类');
        }
      },
      onFail
    );
  },
  /**
   * 新规商品插入
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (params, onSuccess, onFail) {
    if (+params.is_deleted === 1) {
      dao.exec(sqlApi.goodsDelete.format(params), onSuccess, onFail);
      return;
    }
    var paramsCp = _.cloneDeep(params);
    paramsCp.name = paramsCp.name.replace(/'/g, '‘').replace(/;/g, '；');
    paramsCp.pinyin = paramsCp.pinyin.replace(/'/g, '‘').replace(/;/g, '；');
    paramsCp.remark = paramsCp.remark.replace(/'/g, '‘').replace(/;/g, '；');
    paramsCp.is_deleted = 0;
    this.exist(
      params,
      res => {
        var json = demo.t2json(res);
        if (json.length === 0) {
          this.putNewGood(params, paramsCp, onSuccess, onFail);
        } else {
          if (params.name === '直接收款') {
            var params1 = {
              pset: '',
              type: '',
              condition: params.name,
              limit: 10,
              offset: 0
            };
            this.search(params1, onSuccess, onFail);
          } else {
            demo.$toast('新增商品失败,商品名重复或商品唯一标识码重复，请修改商品名称或修改已存在的商品！');
          }
        }
      },
      onFail
    );
  },
  getSalesReportWheres: function (data) {
    let mysql = '';
    if (data.fromDate) {
      mysql += `and s.create_at >= '${data.fromDate}' `;
    }
    if (data.toDate) {
      mysql += `and s.create_at <= '${data.toDate}' `;
    }
    if (demo.isNullOrTrimEmpty(data.typeFingerprint)) {
      mysql += '';
    } else if (data.typeFingerprint === '0') {
      mysql += `and g.unit_fingerprint in (select u.fingerprint from units u where u.name in
        ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))`;
    } else {
      mysql += `and (
        t.fingerprint = '${data.typeFingerprint}'
        or
        t.parent_fingerprint = '${data.typeFingerprint}'
      ) `;
    }
    if (data.specs) {
      mysql += ' and ( g.specs like ';
      data.specs.forEach((spec, index) => {
        if (index === 0) {
          mysql += `'%${spec}%'`;
        } else {
          mysql += `or g.specs like '%${spec}%'`;
        }
      });
      mysql += ')';
    }
    return mysql;
  },
  getSalesReportGoodsWheres(data) {
    return !demo.isNullOrTrimEmpty(data.name) ? `and (goods.name like '%${data.name}%' ESCAPE '/'
    or goods.code like '%${data.name}%' ESCAPE '/'
    or goods_ext_barcode.ext_barcode like '%${data.name}%' ESCAPE '/'
    or goods.first_letters like '%${data.name}%' ESCAPE '/') ` : '';
  },
  getSalesReportZhiJieWheres(data) {
    return !demo.isNullOrTrimEmpty(data.name) ? `and (name like '%${data.name}%' escape '/'  or code like '%${data.name}%' escape '/')` : '';
  },
  getSalesReportDateWheres(data) {
    let date = ``;
    if (data.fromDate) {
      date += `and s.create_at >= '${data.fromDate}' `;
    }
    if (data.toDate) {
      date += `and s.create_at <= '${data.toDate}' `;
    }
    return date;
  },
  getSalesReportWeighWheres(data) {
    return data.typeFingerprint === '0' ? `and goods.unit_fingerprint in (select u.fingerprint from units u where u.name in
      ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))` : '';
  },
  getSalesReportNotWeighWheres(data) {
    return !demo.isNullOrTrimEmpty(data.typeFingerprint) && data.typeFingerprint !== '0' ? `and (
      t.fingerprint = '${data.typeFingerprint}'
      or
      t.parent_fingerprint = '${data.typeFingerprint}'
    ) ` : '';
  },
  goodsSaleReport: function (data, onSuccess, onFail) {
    let orderSql = 'order by ';
    if (data.order) {
      orderSql += `${data.prop} `;
      orderSql += data.order === 'ascending' ? 'asc,' : 'desc,';
    }
    orderSql += `g.name,g.code`;
    data.name = data.name.replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
    let where = {
      date: this.getSalesReportDateWheres(data),
      goods: this.getSalesReportGoodsWheres(data),
      zhiJieGoods: this.getSalesReportZhiJieWheres(data),
      weighUnits: this.getSalesReportWeighWheres(data),
      notWeighUnits: this.getSalesReportNotWeighWheres(data),
      supplier: this.getSupplierWhere(data),
      orderBy: orderSql,
      currentPage: data.currentPage,
      pageSize: data.pageSize
    };
    dao.exec(
      sqlApi.goodsSaleReport.format(where), res => {
        onSuccess(specsService.specStringToArray(res));
      },
      onFail
    );
  },
  goodsSaleReportCount: function (data, onSuccess, onFail) {
    let where = {
      date: this.getSalesReportDateWheres(data),
      goods: this.getSalesReportGoodsWheres(data),
      zhiJieGoods: this.getSalesReportZhiJieWheres(data),
      weighUnits: this.getSalesReportWeighWheres(data),
      notWeighUnits: this.getSalesReportNotWeighWheres(data),
      supplier: this.getSupplierWhere(data)
    };
    dao.exec(sqlApi.goodsSaleReportCount.format(where), onSuccess, onFail);
  },
  goodsSaleReportSum: function (data, onSuccess, onFail) {
    let wheres = {
      date: this.getSalesReportDateWheres(data),
      goods: this.getSalesReportGoodsWheres(data),
      zhiJieGoods: this.getSalesReportZhiJieWheres(data),
      weighUnits: this.getSalesReportWeighWheres(data),
      notWeighUnits: this.getSalesReportNotWeighWheres(data),
      supplier: this.getSupplierWhere(data)
    };
    dao.exec(sqlApi.goodsSaleReportSum.format(wheres), onSuccess, onFail);
  },
  /**
   * 兑换商品查询
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  goodsExchangeSearch: function (data, onSuccess, onFail) {
    if (typeof data.fingerprints === 'string' && data.fingerprints.trim() !== '') {
      var wheres = 'and fingerprint in (' + data.fingerprints + ') ';
      if (typeof data.searchStr === 'string' && data.searchStr.trim() !== '') {
        wheres += "and (name like '%" + data.searchStr + "%' or pinyin like '%" + data.searchStr + "%' or code like '%" + data.searchStr + "%');";
      }
      dao.exec(
        sqlApi.goodsExchangeSearch.format(wheres),
        res => {
          var goods = demo.t2json(res);
          var goodMap = {};
          goods.forEach(good => {
            goodMap[good.fingerprint] = good.name;
          });
          var products = [];
          data.products.forEach(product => {
            if (goodMap.hasOwnProperty(product.fingerprint)) {
              product.name = goodMap[product.fingerprint];
              products.push(product);
            }
          });
          onSuccess(products);
        },
        onFail
      );
    }
  },
  /**
   * 库存统计报表：商品名、库存数、可售库存数获取
   * @param {*} datas
   * @param {*} onSuccess
   * @param {*} onFail
   */
  stockStatisticsReports: function (datas, onSuccess, onFail) {
    var fingerprintList = [];
    datas.forEach(data => {
      fingerprintList.push("'" + data.fingerprint + "'");
    });
    var fingerprints = fingerprintList.join(',');
    dao.exec(
      sqlApi.goods_stockStatisticsReports.format(fingerprints),
      res => {
        var result = demo.t2json(res);
        if (Object.keys(result).length === 0) {
          onSuccess(JSON.stringify(datas));
          return;
        }
        var resObj = {};
        var dataObj;
        result.forEach(data => {
          dataObj = {};
          dataObj.name = data.name;
          dataObj.code = data.code;
          dataObj.extBarcode = data.extBarcode;
          dataObj.cur_stock = data.curStock;
          resObj[data.fingerprint] = dataObj;
        });
        datas.forEach(data => {
          data.name = resObj.hasOwnProperty(data.fingerprint) ? resObj[data.fingerprint].name : '';
          data.code = resObj.hasOwnProperty(data.fingerprint) ? resObj[data.fingerprint].code : '';
          data.extBarcode = resObj.hasOwnProperty(data.fingerprint) ? resObj[data.fingerprint].extBarcode : '';
          data.cur_stock = resObj.hasOwnProperty(data.fingerprint) ? resObj[data.fingerprint].cur_stock : '';
          data.left_stock = data.cur_stock - data.count;
        });
        onSuccess(JSON.stringify(datas));
      },
      onFail
    );
  },
  /**
   * 修改商品保质期生产日期
   * {
      "list": [],
      "manufactureDate": "2023-07-18T00:00:00",
      "expiryDays": 21
    }
   */
  updateDays(params, onSuccess, onFail) {
    // 生产日期、保质期验证
    if (!/^[1-9][0-9]{0,3}$/.test(params.expiryDays)) {
      onFail('保质期必须是1-9999的正整数');
      return;
    }
    const manufactureDate = params.manufactureDate;
    if (!manufactureDate) {
      onFail('请选择生产日期');
      return;
    }
    if (!new Date(manufactureDate).getDate() == +manufactureDate.substring(manufactureDate.length - 2)) {
      onFail('生产日期格式不正确');
      return;
    }
    params.reviseBy = demo.$store.state.show.loginInfo.uid;
    dao.exec(sqlApi.updateExpiryDaysManufactureDate.format(params), onSuccess, onFail);
  },
  /**
   * 查询商品
   * var params = {"columns":"id, name, code, cur_stock", "wheres":"where name like '%水%'", "orderBy":"order by id", "limit":"limit 100 offset 0"};
   * goodService.goodsSelectWheres(params, res => {console.log(res)});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  goodsSelectWheres: function (params, onSuccess, onFail) {
    var newParams = params;
    if (newParams === null || typeof newParams !== 'object') {
      newParams = {};
    }
    if (!newParams.hasOwnProperty('columns')) {
      newParams.columns = '*';
    }
    if (!newParams.hasOwnProperty('wheres')) {
      newParams.wheres = '';
    }
    if (!newParams.hasOwnProperty('orderBy')) {
      newParams.orderBy = 'order by id';
    }
    if (!newParams.hasOwnProperty('limit')) {
      newParams.limit = '';
    }
    dao.exec(sqlApi.goodsSelectWheres.format(newParams), onSuccess, onFail);
  },
  /**
   * 根据商品名称，模糊查询
   * goodService.goodsSelectByName({"name":"水", "page":1, "pageSize":10}, res => {console.log(res)});
   *
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  goodsSelectByName: function (params, onSuccess, onFail) {
    params.wheres = `and name !='直接收款' `;
    if (!demo.isNullOrTrimEmpty(params.name)) {
      params.name = params.name.replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      params.wheres = `and (goods.name like '%${params.name}%' ESCAPE '/'
        or goods.pinyin like '%${params.name}%' ESCAPE '/'
        or goods.code like '%${params.name}%' ESCAPE '/'
        or goods_ext_barcode.ext_barcode like '%${params.name}%' ESCAPE '/'
        or goods.first_letters like '%${params.name}%' ESCAPE '/')`;
    }
    var result = {};
    dao.exec(
      sqlApi.goodsCountSelectByName.format(params),
      res => {
        result.count = demo.t2json(res)[0].cnt;
        if (result.count === 0) {
          result.goods = [];
          onSuccess(result);
        } else {
          dao.exec(
            sqlApi.goodsSelectByName.format(params),
            res1 => {
              result.goods = specsService.specStringToArray(res1);
              onSuccess(result);
            },
            onFail
          );
        }
      },
      onFail
    );
  },
  /**
   * 批量设置商品状态
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  setGoodsStatus: function (data, onSuccess, onFail) {
    let params = {
      goodsStatus: data.goodsStatus || '0',
      goodsList: data.goodsList || ''
    };
    dao.exec(sqlApi.setGoodsStatus.format(params), onSuccess, onFail);
  },
  /**
   * 批量设置商品分类
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  setGoodsType: function (data, onSuccess, onFail) {
    let params = {
      goodsType: data.goodsType || md5('其他分类'),
      goodsList: data.goodsList.length > 0 ? data.goodsList.split(',').join(`','`) : ''
    };
    dao.exec(sqlApi.setGoodsType.format(params), onSuccess, onFail);
  },
  /**
   * 批量设置商品单位
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  setGoodsUnit: function (data, onSuccess, onFail) {
    let params = {
      goodsUnit: data.goodsUnit || '0',
      goodsList: data.goodsList || ''
    };
    dao.exec(sqlApi.setGoodsUnit.format(params), onSuccess, onFail);
  },
  /**
   * 批量设置商品规格
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  upGoodsSpecs: function (data, onSuccess, onFail) {
    let sql = data.map(goods => sqlApi.upGoodsSpecs.format(goods));
    if (sql.length === 0) {
      onSuccess();
    } else {
      dao.exec(sql.join(''), onSuccess, onFail);
    }
  },
  /**
   * 批量设置商品品牌
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  setGoodsBrand: function (data, onSuccess, onFail) {
    let params = {
      goodsBrand: data.goodsBrand || '0',
      goodsList: data.goodsList || ''
    };
    dao.exec(sqlApi.setGoodsBrand.format(params), onSuccess, onFail);
  },
  /**
   * 库存统计
   * @param {*} data {"limit": 10, "offset": 0}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  stockStatisticsPriceReports: function (data, onSuccess, onFail) {
    var result = {};
    dao.exec(
      sqlApi.stockStatisticsPriceReports.format(data),
      res1 => {
        result.datas = demo.t2json(res1);
        dao.exec(
          sqlApi.stockStatisticsPriceReportsTotal.format(data),
          res2 => {
            result.total = demo.t2json(res2)[0];
            dao.exec(
              sqlApi.stockStatisticsPriceReportsCount.format(data),
              res3 => {
                result.count = demo.t2json(res3)[0]['totalCount'];
                onSuccess(JSON.stringify(result));
              },
              onFail
            );
          },
          onFail
        );
      },
      onFail
    );
  },
  stockStatisticsPriceReportsExport: function (onSuccess, onFail) {
    dao.exec(sqlApi.stockStatisticsPriceReportsExport, onSuccess, onFail);
  },
  /**
   *
   * 条码秤：展示条码秤内容
   */
  findScaleitem: function (onSuccess, onFail) {
    let result = [];
    var ret = {};
    dao.exec(
      sqlApi.findScaleitem,
      res => {
        ret = _.groupBy(res, 'sendscaleFingerprint');
        _.keys(ret).forEach((item, index) => {
          var ss = {};
          var dd = _.get(ret, item);
          var tt = _.size(dd);
          ss.sendscaleFingerprint = item;
          ss.sendscaleName = dd[0].sendscaleName;
          ss.scaleBrandCode = dd[0].scaleBrandCode;
          ss.scaleTypeCode = dd[0].scaleTypeCode;
          ss.scaleBrandName = dd[0].scaleBrandName;
          ss.scaleTypeName = dd[0].scaleTypeName;
          ss.scaleIp = dd[0].scaleIp;
          ss.port = dd[0].port;
          ss.remark = dd[0].remark;
          var typeCheckList = [];
          var scalesItems = [];
          var types = _.groupBy(dd, 'typeFingerprint');
          _.keys(types).forEach((type, index) => {
            typeCheckList[index] = type;
          });
          for (var i = 0; i < tt; i++) {
            var goods = {};
            goods.name = dd[i].name;
            goods.code = dd[i].code;
            goods.fingerprint = dd[i].fingerprint;
            goods.salePrice = dd[i].salePrice;
            goods.pluCode = dd[i].pluCode;
            goods.hotKey = dd[i].hotKey;
            goods.typeFingerprint = dd[i].typeFingerprint;
            scalesItems[i] = goods;
          }
          ss.typeCheckList = typeCheckList;
          ss.scalesItems = scalesItems;
          result[index] = ss;
        });
        onSuccess(result);
        console.log(result, '展示数据');
      },
      onFail
    );
  },
  /**
   *
   * 条码秤：删除条码秤
   */
  deleteScale: function (param, onSuccess, onFail) {
    dao.exec(sqlApi.deleteScale.format(param), onSuccess, onFail);
  },
  /**
   * 获取能连接条码秤信息
   */
  selectScaleList: function (onSuccess, onFail) {
    demo.$http
      .post(rest.downScaleList, {
        maxContentLength: Infinity,
        timeout: 300000
      })
      .then(res => {
        var data = res.data;
        if (+data.code === 200) {
          var ss = data.data;
          var sql = sqlApi.initScaleList;
          sql += sqlApi.syncTmpScaleListTruncate;
          sql += sqlApi.syncTmpScaleListInsert;
          var ssLenght = _.size(ss);
          ss.forEach((item, index) => {
            sql += sqlApi.syncTmpScaleListInsertValues.format(item) + (index === ssLenght - 1 ? ';' : ',');
          });
          dao.transaction(
            sql,
            () => {
              dao.exec(sqlApi.selectScaleList, onSuccess, onFail);
            },
            () => {
              dao.exec(sqlApi.selectScaleList, onSuccess, onFail);
            }
          );
        } else {
          onFail(res);
        }
      });
  },
  /**
   *
   * 条码秤：获取所有分类
   */
  selectAllGoodsList: function (onSuccess, onFail) {
    var res1 = {};
    let result = [];
    dao.exec(
      sqlApi.selectAllGoodsList,
      res => {
        res1 = _.groupBy(res, 'typeName');
        console.log(res1);
        _.keys(res1).forEach((item, index) => {
          var ss = {};
          var goods = _.get(res1, item);
          ss.name = item;
          ss.typeFingerprint = goods[0].typeFingerprint;
          ss.items = goods;
          result[index] = ss;
        });
        onSuccess(result);
      },
      onFail
    );
  },
  /**
   *
   *条码秤：保存
   */
  scaleSave: async function (params, onSuccess, onFail) {
    const param = _.cloneDeep(params);
    const sql = new StringBuilder();
    const scalesItems = param.scalesItems;
    const uid = demo.$store.state.show.loginInfo.uid;
    const name = String(param.sendscaleName);
    const scalesGoodsItems = [];
    param.uid = uid;
    // 条码秤Fingerprint 为空时，该称为新增的称
    if (demo.isNullOrTrimEmpty(param.sendscaleFingerprint)) {
      param.sendscaleFingerprint = md5(name);
      // 条码秤名称重复时条码秤名加时间生成条码秤的Fingerprint
      let num = await dao.asyncExec(sqlApi.selectsendscaleFingerprint.format(param));
      if (demo.t2json(num)[0]['num'] > 0) {
        const now = String(new Date().getTime());
        param.sendscaleFingerprint = md5(name + now);
      }
      sql.append(sqlApi.insertSendScale.format(param));
    } else {
      sql.append(sqlApi.updateSendscale.format(param));
      sql.append(sqlApi.deleteSendscaleProduct.format(param));
    }
    const scalesGoodsItem = {};
    scalesItems.forEach(item => {
      item.sendscaleFingerprint = param.sendscaleFingerprint;
      item.uid = uid;
      item.hotKey = item.hotKey === '' ? null : item.hotKey;
      const goodFingerprint = String(item.fingerprint);
      const scaleFingerprint = String(item.sendscaleFingerprint);
      const sp = goodFingerprint + scaleFingerprint;
      item.sendscaleProductFingerprint = md5(sp);
      const scalesGoodsItem = {};
      scalesGoodsItem.sendscaleFingerprint = item.sendscaleFingerprint;
      scalesGoodsItem.fingerprint = goodFingerprint;
      scalesGoodsItem.scaleFingerprint = scaleFingerprint;
      scalesGoodsItem.sp = sp;
      scalesGoodsItem.sendscaleProductFingerprint = item.sendscaleProductFingerprint;
      scalesGoodsItems.push(scalesGoodsItem);
      sql.append(sqlApi.insertSendscaleProduct.format(item));
    });
    dao.transaction(sql.toString(), onSuccess, onFail);
    const obj = _.cloneDeep(logList.sendscaleProduct);
    obj.description = obj.description.format(JSON.stringify(scalesGoodsItems));
    demo.actionLog(obj);
  },
  /**
   * 同步自动生成传称条码
   */
  asyncCreateBarcode(count) {
    return new Promise((resolve, reject) => {
      if (+count === 0) {
        resolve([]);
        return;
      }
      this.createBarcode(count, resolve, reject);
    });
  },
  /**
   *自动生成传称条码
   */
  // TODO:XC 生成条码
  createBarcode(count) {
    return new Promise((resolve, reject) => {
      // 设备编号
      const deviceCode = `${demo.$store.state.show.devicecode.toString().padStart(2, '0')}`;
      dao.exec(
        sqlApi.goodsSelectMaxCode.format({ deviceCode, count }),
        newCodes => {
          const codeList = []
          newCodes.forEach(item => {
            codeList.push(`20${deviceCode}${item.maxcode.toString().padStart(3, '0')}`);
          })
          console.log(codeList);
          resolve(codeList);
        },
        reject
      );
    });
  },
  /**
   *
   * 条码秤：传称履历
   */
  insertSendscaleHistory: function (param, onSuccess, onFail) {
    var now = _.toString(new Date().getTime());
    var sql = '';
    dao.exec(
      sqlApi.selectNames.format(param),
      res => {
        res.forEach(item => {
          item.sumName = _.toString(item.sumName);
          item.sendscaleHistoryFingerprint = md5(item.sumName + now);
          sql += sqlApi.insertSendscaleHistory.format(item);
        });
        dao.transaction(sql, onSuccess, onFail);
      },
      onFail
    );
  },
  /**
   * 报表：库存查询
   * @param {*} params {'limit': 10, 'offset': 0, 'condition': '', 'type': 3}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  stockGoodsReportsCommon: function (params) {
    var wheres = '';
    if (!demo.isNullOrTrimEmpty(params.condition)) {
      params.condition = params.condition.replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      wheres += `and (a.name like '%${params.condition}%' ESCAPE '/' or a.pinyin like '%${params.condition}%' ESCAPE '/'
        or a.code like '%${params.condition}%' ESCAPE '/'
        or goods_ext_barcode.ext_barcode like '%${params.condition}%' ESCAPE '/'
        or a.first_letters like '%${params.condition}%' ESCAPE '/') `;
    }
    if (demo.isNullOrTrimEmpty(params.type)) {
      wheres += '';
    } else if (params.type === '0') {
      wheres += `and a.unit_fingerprint in (select u.fingerprint from units u where u.name in
        ('千克','克','斤','公斤','两','g','G','kg','Kg','kG','KG'))`;
    } else if (params.type !== '0') {
      wheres += `and (b.fingerprint = '${params.type}' or b.parent_fingerprint = '${params.type}')`;
    }
    // 供应商筛选
    wheres += this.getSupplierWhere(params);
    params.wheres = wheres;

    let orderBy = '';
    if (params.order === 'ascending') {
      orderBy = 'order by a.cur_stock asc, a.id asc';
    } else {
      orderBy = 'order by a.cur_stock desc, a.id asc';
    }
    params.orderBy = orderBy;
  },
  // 批量修改售价:建议零售价、会员价、进价
  setGoodsPrice: function (data, onSuccess, onFail) {
    let mysql = '';
    data.chooseList.map(choose => {
      let key = demo.toLine(data.priceTypeVal);
      mysql += sqlApi.setGoodsPrice.format(`${key} = ${choose[data.priceTypeVal]}
      where fingerprint = '${choose.fingerprint}'`);
    });
    dao.exec(mysql, onSuccess, onFail);
  },
  stockGoodsReports: function (params, onSuccess, onFail) {
    this.stockGoodsReportsCommon(params);
    var stockGoodsResult = {};
    dao.exec(
      sqlApi.stockGoodsReports.format(params),
      res1 => {
        stockGoodsResult.datas = specsService.specStringToArray(res1);
        dao.exec(
          sqlApi.stockGoodsReportsTotal.format(params),
          res2 => {
            stockGoodsResult.total = res2[0];
            dao.exec(
              sqlApi.stockGoodsReportsCount.format(params),
              res3 => {
                stockGoodsResult.count = res3[0]['totalCount'];
                onSuccess(JSON.stringify(stockGoodsResult));
              },
              onFail
            );
          },
          onFail
        );
      },
      onFail
    );
  },
  stockGoodsReportsExport: function (params, onSuccess, onFail) {
    this.stockGoodsReportsCommon(params);
    dao.exec(sqlApi.stockGoodsReportsExport.format(params), onSuccess, onFail);
  },
  updateSalePrice: function (params, onSuccess, onFail) {
    dao.exec(sqlApi.goodUpdateSalePrice.format(params), onSuccess, onFail);
  },
  /**
   * 商品批量保存、一品多码拆分一品一码
   * @param {*} params
   */
  batchImportGoods: function (params) {
    return new Promise((resolve, reject) => {
      const items = params.items[0];
      // 是否是一品多码商品
      const isCodesGoods = items.isCodesGoods;
      // 一品多码拆分一品一码的商品
      const multiToSingleGoods = items.multiToSingleGoods;
      // 拆分一品一码的扩展条码
      const extendCodes = items.extendCodes;
      if (isCodesGoods != 0) {
        reject(new Error('一品多码拆分一品一码数据不正确'));
        return;
      }
      if (multiToSingleGoods.length === 0 || multiToSingleGoods.length != extendCodes.length) {
        reject(new Error('一品多码拆分一品一码数据不正确'));
        return;
      }

      // 商品名
      const inputNames = _.compact(_.map(multiToSingleGoods, 'name'));
      if (inputNames.length > new Set(inputNames).size) {
        reject(new Error('商品名不允许重复'));
        return;
      }

      // 商品条码
      const codes = _.compact(_.map(multiToSingleGoods, 'code'));
      if (codes.length > new Set(codes).size) {
        reject(new Error('条码不允许重复'));
        return;
      }

      // 商品参数处理
      this.preProcessingParams(multiToSingleGoods);

      return this.checkGoodsCodeAndFingerprint(multiToSingleGoods)
        .then(() => {
          return this.insertGoodsInfo(params);
        })
        .then(resolve, reject);
    });
  },
  checkGoodsCodeAndFingerprint: function (goodsArray) {
    return new Promise((resolve, reject) => {
      // 商品条码检查
      var codes = _.compact(_.map(goodsArray, 'code'));
      var params = {};
      params.wheres = `and code <> '' and code in ('` + codes.join("','") + `')`;
      dao.exec(
        sqlApi.getGoodsByWheres.format(params),
        async res => {
          if (res.length > 0) {
            var repeatCodes = _.map(
              _.filter(res, item => {
                return +item.isDeleted === 0;
              }),
              'code'
            );
            if (repeatCodes.length > 0) {
              reject(new Error(`条码已存在：${repeatCodes.join()}`));
              return;
            }
          }

          // 商品 名称 检查
          const names = _.map(goodsArray, 'name');
          const nameParam = {};
          nameParam.wheres = `and name in ('${names.join("','")}')`;
          const repNames = await dao.asyncExec(sqlApi.getGoodsByWheres.format(nameParam));
          if (repNames.length > 0) {
            const repeatNames = _.map(
              _.filter(res, item => {
                return +item.isDeleted === 0;
              }),
              'name'
            );
            if (repeatNames.length > 0) {
              reject(new Error(`名称已存在：${repeatNames.join()}`));
              return;
            }
          }
          _.forEach(goodsArray, item => {
            item.fingerprint = md5(item.name);
            item.specs = null;
          });
          // 商品 fingerprint 检查
          var fingerprints = _.map(goodsArray, 'fingerprint');
          let fingerprintParam = {};
          fingerprintParam.wheres = `and fingerprint in ('${fingerprints.join("','")}')`;
          let repeats = await dao.asyncExec(sqlApi.getGoodsByWheres.format(fingerprintParam));
          let repeatFingerprints = _.map(repeats, 'fingerprint');
          if (repeatFingerprints.length > 0) {
            var now = new Date().getTime();
            _.filter(goodsArray, item => {
              return repeatFingerprints.includes(item.fingerprint);
            }).forEach(item => {
              item.fingerprint = md5(item.name + '_' + now);
            });
          }

          resolve();
        },
        reject
      );
    });
  },
  insertGoodsInfo: function (params) {
    return new Promise((resolve, reject) => {
      const that = this;
      const goods = params.items[0];
      // 拆分一品一码的扩展条码
      const extendCodes = goods.extendCodes;
      // 拆分一品一码商品
      const multiToSingleGoods = goods.multiToSingleGoods;
      const goodsLength = multiToSingleGoods.length;
      let goodsInsert = sqlApi.goodsInsert;
      let inventoryParams = {};
      let goodsAttributesInsert = sqlApi.goodsAttributesInsert;
      let goodsAttributesInsertValues = sqlApi.goodsAttributesInsertValues;
      inventoryParams.exec = 0;
      inventoryParams.dealType = INVENTORY_DEAL_TYPE.NEW_ADD_INVENTORY;
      inventoryParams.inventoryItems = [];
      _.forEach(multiToSingleGoods, (item, index) => {
        if (!demo.isNullOrTrimEmpty(item.isBarcodeScalesGoods) && item.isBarcodeScalesGoods === 1) {
          dao.exec(
            sqlApi.productScale.format(item),
            () => {
              if (demo.isNullOrTrimEmpty(item.code)) {
                this.createBarcode(1).then(res1 => {
                  item[0].code = res1;
                }).catch(reject);
              }
            },
            reject
          );
        }
        if (+item.curStock) {
          let good = {};
          good.accountQty = 0;
          good.actualQty = item.curStock;
          good.price = item.purPrice;
          good.goodFingerprint = item.fingerprint;
          inventoryParams.inventoryItems.push(good);
        }
        item.specs = null;
        goodsInsert += sqlApi.goodsInsertValues.format(demo.sqlConversion(item)) + that.compareIndexAndLength(index, goodsLength);
        goodsAttributesInsert += goodsAttributesInsertValues.format(demo.sqlConversion(item)) + this.compareIndexAndLength(index, goodsLength);
      });
      goodsInsert += goodsAttributesInsert;
      // 删除扩展条码sql
      extendCodes.forEach(item => {
        goodsInsert += sqlApi.goodsExtBarcodeUpdateIsDeleted.format(item);
      });

      if (inventoryParams.inventoryItems.length === 0) {
        dao.transaction(
          goodsInsert,
          () => {
            resolve();
          },
          reject
        );
        return;
      }
      inventoryService.insert(
        inventoryParams,
        res => {
          dao.transaction(
            goodsInsert + res,
            () => {
              resolve();
            },
            reject
          );
        },
        reject
      );
    });
  },
  preProcessingParams: function (goodsArray) {
    _.forEach(goodsArray, item => {
      item.name = item.name.replace(/'/g, '‘').replace(/;/g, '；');
      item.firstLetters = pyCode.getPyCode(item.name);
      item.pinyin = item.pinyin.replace(/'/g, '‘').replace(/;/g, '；');
      item.remark = item.remark.replace(/'/g, '‘').replace(/;/g, '；');
      item.hasImage = 0;
      item.packing = null;
      item.isVipDisc = 0;
      if (demo.isNullOrTrimEmpty(item.code)) {
        item.code = null;
      }
    });
  },
  /**
   * 商品批量新增-页面数据check
   * http://yapi.trechina.cn/project/1152/interface/api/58186
   * @param {*} params
   */
  batchInsertCheck: function (params) {
    return new Promise((resolve, reject) => {
      this.batchInsertPreCheck(params)
        .then(() => {
          return this.batchInsertDataCheck(params);
        })
        .then(resolve)
        .catch(reject);
    });
  },
  batchInsertPreCheck: function (params) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(params)) {
        reject(new Error('入参错误'));
        return;
      }
      if (params.length === 0) {
        reject(new Error('没有新增商品数据'));
        return;
      }
      if (params.length > 200) {
        reject(new Error('新增商品数据超过200条限制'));
        return;
      }
      const columns = ['code', 'name', 'salePrice', 'vipPrice', 'typeFingerprint', 'unitFingerprint'];
      const firstRowColumns = _.keys(params[0]);
      const bothColumns = columns.filter(i => new Set(firstRowColumns).has(i));
      if (bothColumns.length < columns.length) {
        reject(new Error('新增商品数据错误'));
        return;
      }

      resolve();
    });
  },
  batchInsertDataCheck: function (params) {
    return new Promise((resolve, reject) => {
      params.forEach(i => {
        i.errorMsg = {};

        if (stringUtils.isBlank(i.code)) {
          i.errorMsg.code = '条码不能为空';
        } else if (!/^[0-9a-zA-Z-]{1,16}$/.test(i.code)) {
          i.errorMsg.code = '条码格式错误';
        }
        if (stringUtils.isBlank(i.name)) {
          i.errorMsg.name = '商品名称不能为空';
        } else if (i.name.length > 60) {
          i.errorMsg.name = '商品名称长度不能超过60';
        }
        if (stringUtils.isBlank(i.salePrice)) {
          i.errorMsg.salePrice = '零售价不能为空';
        } else if (isNaN(i.salePrice) || i.salePrice < 0 || i.salePrice > 999999.99) {
          i.errorMsg.salePrice = '零售价应为0~999999.99的数字';
        } else {
          const priceSplit = (+i.salePrice + '').split('.');
          if (priceSplit.length === 2 && priceSplit[1].length > 2) {
            i.errorMsg.salePrice = '零售价最多两位小数';
          }
        }
        if (stringUtils.isBlank(i.vipPrice)) {
          i.errorMsg.vipPrice = '会员价不能为空';
        } else if (isNaN(i.vipPrice) || i.vipPrice < 0 || i.vipPrice > 999999.99) {
          i.errorMsg.vipPrice = '会员价应为0~999999.99的数字';
        } else {
          const priceSplit = (+i.vipPrice + '').split('.');
          if (priceSplit.length === 2 && priceSplit[1].length > 2) {
            i.errorMsg.salePrice = '会员价最多两位小数';
          }
        }
      });

      const codeGroup = _.groupBy(_.filter(params, i => !stringUtils.isBlank(i.code)), 'code');
      for (const key in codeGroup) {
        const items = codeGroup[key];
        if (items.length > 1) {
          items.forEach(i => {
            if (i.errorMsg.code === undefined) {
              i.errorMsg.code = '条码重复';
            } else {
              i.errorMsg.code += '，条码重复';
            }
          });
        }
      }
      const nameGroup = _.groupBy(_.filter(params, i => !stringUtils.isBlank(i.code)), 'name');
      for (const key in nameGroup) {
        const items = nameGroup[key];
        if (items.length > 1) {
          items.forEach(i => {
            if (i.errorMsg.name === undefined) {
              i.errorMsg.name = '商品名称重复';
            } else {
              i.errorMsg.name += '，商品名称重复';
            }
          });
        }
      }

      params.forEach(i => {
        if (Object.keys(i.errorMsg).length === 0) {
          delete i.errorMsg;
        }
      });

      const firstErrorData = _.find(params, i => i.hasOwnProperty('errorMsg'));
      if (firstErrorData !== undefined) {
        reject(new Error('checkFail'));
        return;
      }

      resolve();
    });
  },
  /**
   * 商品批量新增
   * http://yapi.trechina.cn/project/1152/interface/api/58189
   * @param {*} params
   */
  batchInsert: function (params) {
    return new Promise((resolve, reject) => {
      if (params.length === 0) {
        reject(new Error('没有新增商品数据'));
        return;
      }
      this.batchInsertDbCheck(params)
        .then(() => {
          return this.batchInsertDo(params);
        })
        .then(resolve)
        .catch(reject);
    });
  },
  batchInsertDbCheck: function (params) {
    return new Promise((resolve, reject) => {
      params.forEach(i => {
        i.errorMsg = {};
      });
      this.batchInsertDbCodeCheck(params)
        .then(() => {
          return this.batchInsertDbNameCheck(params);
        })
        .then(() => {
          return this.batchInsertDbTypeCheck(params);
        })
        .then(() => {
          return this.batchInsertDbUnitCheck(params);
        })
        .then(() => {
          params.forEach(i => {
            if (Object.keys(i.errorMsg).length === 0) {
              delete i.errorMsg;
            }
          });

          const firstErrorData = _.find(params, i => i.hasOwnProperty('errorMsg'));
          CefSharp.PostMessage(`批量新增商品校验结果:${JSON.stringify(params)}`);
          if (firstErrorData !== undefined) {
            reject(new Error('checkFail'));
            return;
          }
          resolve();
        })
        .catch(reject);
    });
  },
  batchInsertDbCodeCheck: function (params) {
    return new Promise((resolve, reject) => {
      const codes = _.map(params, 'code');
      if (codes.length === 0) {
        resolve();
        return;
      }
      const codeSql = new StringBuilder(`select '${codes[0]}' as code`);
      const codesLength = codes.length;
      for (let i = 1; i < codesLength; i++) {
        codeSql.append(` union all select '${codes[i]}'`);
      }
      const columnSql = `goods.code, tmp1.ext_barcode`;
      dao.asyncExec(sqlApi.goodsIfExists.format(codeSql.toString(), columnSql))
        .then(res => {
          const allCodes = new Set();
          res.forEach(i => {
            if (!stringUtils.isBlank(i.code)) {
              allCodes.add(i.code);
            }
            if (!stringUtils.isBlank(i.extBarcode)) {
              allCodes.add(i.extBarcode);
            }
          });
          params.forEach(i => {
            if (allCodes.has(i.code)) {
              i.errorMsg.code = '条码已存在';
            }
          });
          resolve();
        })
        .catch(reject);
    });
  },
  batchInsertDbNameCheck: function (params) {
    return new Promise((resolve, reject) => {
      const names = _.map(params, 'name');
      if (names.length === 0) {
        resolve();
        return;
      }
      const getGoodsParams = {
        columns: 'name',
        wheres: `where is_deleted = 0 and name in ('${names.join('\',\'')}')`,
        orderBy: '',
        limit: ''
      };
      dao.asyncExec(sqlApi.goodsSelectWheres.format(getGoodsParams))
        .then(res => {
          const allNames = new Set(_.map(res, 'name'));
          params.forEach(i => {
            if (allNames.has(i.name)) {
              i.errorMsg.name = '商品名称已存在';
            }
          });
          resolve();
        })
        .catch(reject);
    });
  },
  batchInsertDbTypeCheck: function (params) {
    return new Promise((resolve, reject) => {
      const typeFingerprints = _.map(_.filter(params, i => !stringUtils.isBlank(i.typeFingerprint)), 'typeFingerprint');
      if (typeFingerprints.length === 0) {
        resolve();
        return;
      }
      const getTypesParams = {
        columns: 'fingerprint',
        wheres: `where is_deleted = 0`,
        orderBys: '',
        limit: ''
      };
      dao.asyncExec(sqlApi.getTypes.format(getTypesParams))
        .then(res => {
          const allTypeFingerprints = _.map(res, 'fingerprint');
          params.forEach(i => {
            if (!allTypeFingerprints.includes(i.typeFingerprint)) {
              i.errorMsg.typeFingerprint = '分类不存在';
            }
          });
          resolve();
        })
        .catch(reject);
    });
  },
  batchInsertDbUnitCheck: function (params) {
    return new Promise((resolve, reject) => {
      const unitFingerprints = _.map(_.filter(params, i => !stringUtils.isBlank(i.unitFingerprint)), 'unitFingerprint');
      if (unitFingerprints.length === 0) {
        resolve();
        return;
      }
      const getUnitsParams = {
        columns: 'fingerprint',
        wheres: `where is_deleted = 0`,
        orderBys: ``,
        limit: ``
      };
      dao.asyncExec(sqlApi.getUnits.format(getUnitsParams))
        .then(res => {
          const allUnitFingerprints = _.map(res, 'fingerprint');
          params.forEach(i => {
            if (i.unitFingerprint && !allUnitFingerprints.includes(i.unitFingerprint)) {
              i.errorMsg.unitFingerprint = '单位不存在';
            }
          });
          resolve();
        })
        .catch(reject);
    });
  },
  batchInsertDo: function (params) {
    return new Promise((resolve, reject) => {
      const uid = demo.$store.state.show.loginInfo.uid;
      const fingerprints = new Set();
      params.forEach(i => {
        i.firstLetters = pyCode.getPyCode(i.name);
        i.fingerprint = md5(i.name);
        i.uid = uid;
        fingerprints.add(i.fingerprint);
      });

      const getGoodsParams = {
        columns: 'fingerprint',
        wheres: `where fingerprint in ('${Array.from(fingerprints).join('\',\'')}')`,
        orderBy: '',
        limit: ''
      };
      dao.asyncExec(sqlApi.goodsSelectWheres.format(getGoodsParams))
        .then(res => {
          const allFingerprints = _.map(res, 'fingerprint');
          if (allFingerprints.length > 0) {
            _.filter(params, i => allFingerprints.includes(i.fingerprint))
              .forEach(i => {
                i.fingerprint = commonService.guid();
              });
          }

          const sql = new StringBuilder();
          params.forEach(i => {
            i.manufactureDate = i.manufactureDate ? `'${i.manufactureDate}'` : null;
            sql.append(sqlApi.goodsBatchInsert.format(i));
          });
          return dao.asyncTransaction(sql.toString());
        })
        .then(resolve)
        .catch(reject);
    });
  },
  /**
   * 商品排序初始化
   * @param {*} onSuccess
   */
  sortInit(onSuccess) {
    dao.exec(sqlApi.initSortData, onSuccess);
  },
  /**
   * 初始化商品排序
   * goodService.initGoodsSort(res => {console.log(res)}, err => {console.error(err)});
   */
  initGoodsSort(onSuccess, onFail) {
    dao.exec(sqlApi.initGoodsSort.format({uid: $userinfo.uid}), () => {
      onSuccess();
    }, onFail);
  },
  /**
   * 拖动单个商品排序保存
   * var param = {"fingerprint":"4c03d5d0af9488b029aa217d948e09f3","sort":2,"beginSort":2,"endSort":5,"sortRule":"sort+1"};
   * goodService.updateGoodSort(param, res => {console.log(res)}, err => {console.error(err)});
   */
  updateGoodSort(data, onSuccess, onFail) {
    data.uid = $userinfo.uid;
    dao.exec(sqlApi.updateGoodSort.format(data), (res) => {
      onSuccess(res);
    }, onFail);
  },

  /**
   * 批量更新商品库存
   * 传参格式：params = {"batchStockNum":100, list:[{fingerprint: "42b76f769ca06ca807ef01619afe74e0", curStock: 9}, {fingerprint: "42b76f769ca06ca807ef01619afe74e0", curStock: 1}]};
   * 更新调用方法：goodService.batchUpdateGoodsStock(params, res => {console.log(res)}, err => {console.error(err)});
   */
  async batchUpdateGoodsStock(params, onSuccess, onFail) {
    // console.log('批量设置库存:', params);
    // 先做盘点单(总单+细单)，防止多端库存数据不同步
    const batchStockNum = params.batchStockNum;
    const list = params.list;
    const inventoryFingerprint = commonService.guid();
    await this.makeInventory(batchStockNum, list, inventoryFingerprint, onFail);
    for (let i = 0; i < list.length; i++) {
      await this.makeInventoryItem(batchStockNum, list[i], inventoryFingerprint, onSuccess, onFail);
    }
    // 再更新库存
    const fingerprintList = list.map(item => item.fingerprint);
    dao.exec(sqlApi.batchUpdateGoodsStock.format({batchStockNum, fingerprints: fingerprintList.join('\',\'')}), () => {
      onSuccess();
    }, onFail);
  },

  /**
   * 库存批量设置后，生成inventories总单盘点明细（报表库存变动明细需要）
   * @param {*} batchStockNum 要修改成的库存数
   * @param {*} list 要做盘点单的商品list
   * @param {*} inventoryFingerprint 盘点单fingerprint
   */
  makeInventory(batchStockNum, list, inventoryFingerprint, onFail) {
    return new Promise(resolve => {
      orderService.get({
        type: 'PDD',
        table: 'inventories'
      }, res => {
        const code = demo.t2json(res)[0].code;
        const sumVal = list.reduce((pre, item) => {
          pre.accountQty += +item.curStock;
          pre.actualQty = calc(batchStockNum).times(list.length).toNumber();
          pre.diffQty += calc(batchStockNum).minus(+item.curStock).toNumber()
          pre.diffAmt += calc(batchStockNum).minus(+item.curStock).times(+item.purPrice).toNumber();
          return pre;
        }, { accountQty: 0, actualQty: 0, diffQty: 0, diffAmt: 0 });
        const inventoryParams = {
          uid: $userinfo.uid,
          code: code,
          account_qty: sumVal.accountQty,
          actual_qty: sumVal.actualQty,
          diff_qty: sumVal.diffQty,
          diff_amt: sumVal.diffAmt,
          deal_type: INVENTORY_DEAL_TYPE.UPDATE_GOODS_INVENTORY,
          remark: '',
          is_deleted: 0,
          is_synced: 0,
          fingerprint: inventoryFingerprint
        };
        dao.exec(sqlApi.inventory_batchPut_insert.format(inventoryParams), () => {
          resolve();
        }, onFail);
      });
    });
  },

  /**
   * 库存批量设置后，生成inventories_items细单盘点明细（报表库存变动明细需要）
   * @param {*} batchStockNum 要修改成的库存数
   * @param {*} item 每个商品项
   * @param {*} inventoryFingerprint 盘点单fingerprint
   */
  async makeInventoryItem(batchStockNum, item, inventoryFingerprint, onSuccess, onFail) {
    const inventoryItemParams = {
      accountQty: +item.curStock,
      actualQty: batchStockNum,
      diffQty: calc(batchStockNum).minus(+item.curStock).toNumber(),
      diffAmt: calc(batchStockNum).minus(+item.curStock).times(+item.purPrice).toNumber(),
      price: item.purPrice,
      goodFingerprint: item.fingerprint,
      fingerprint: commonService.guid(),
      inventoryFingerprint: inventoryFingerprint
    };
    await dao.asyncExec(sqlApi.inventoryItemsInsert + sqlApi.inventoryItemsInsertVal.format(inventoryItemParams), onSuccess, onFail);
  },
  // 库存预警报表总数
  goodsStockWarningReportsCount(params) {
    return new Promise((resolve) => {
      dao.exec(sqlApi.goodsStockWarningReportsCount.format(params), res => { resolve(demo.t2json(res)) })
    });
  },
  // 提醒事项 商品预警提示信息
  goodsStockWarningCount() {
    return new Promise((resolve) => {
      let whereLow = `where goods.is_deleted=0 and goods.name <> '直接收款' and (
        goods.max_stock >= goods.min_stock
        and goods.max_stock <> 0
        and goods.cur_stock < goods.min_stock
      )`;
      let whereHight = `where goods.is_deleted=0 and goods.name <> '直接收款' and (
        goods.max_stock <> 0
        and goods.cur_stock > goods.max_stock
      )`;
      Promise.all([this.goodsStockWarningReportsCount({wheres: whereLow}), this.goodsStockWarningReportsCount({wheres: whereHight})]).then((result) => {
        resolve({low: Number(result[0][0].cnt), hight: Number(result[1][0].cnt)});
      });
    });
  },
  // 过期预警提示信息
  goodsExpirationCount(params) {
    return new Promise((resolve, reject) => {
      let errorMessage = '预警天数不正确';
      if (params.overdueDate === undefined || demo.isNullOrTrimEmpty(params.overdueDate) || params.overdueDate <= 0) {
        reject(new Error(errorMessage));
        return;
      }
      let wheres = `where goods.is_deleted=0
      and ga.expiry_days <> 0
      and (JULIANDAY(ga.manufacture_date)-JULIANDAY(date())+ga.expiry_days | 0) <= ${params.overdueDate}`;
      dao.exec(sqlApi.goodsStockWarningReportsCount.format({wheres: wheres}), result => {
        resolve(Number(result[0].cnt))
      })
    });
  },
  // 根据fingerprint获取商品名称
  getGoodNameByFingerprint(fingerprints) {
    return new Promise(resolve => {
      if (!fingerprints.length) {
        resolve([])
      } else {
        const sql = `('${fingerprints.join("','")}')`
        dao.exec(sqlApi.getGoodNameByFingerprint.format(sql), result => {
          resolve(result)
        })
      }
    })
  }
};
window.goodService = goodService;
export default goodService;
