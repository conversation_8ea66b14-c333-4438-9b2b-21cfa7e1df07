<style lang="less" scoped>
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.scroll-bar {
  direction: rtl;
}
.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/.el-input__inner {
  color: @themeFontColor;
  font-size: 16px;
  height: 40px;
}
.el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-upload--picture-card:hover,
.el-upload:focus {
  border-color: @themeBackGroundColor;
}
.pc_sto2 {
  width: 118px;
  position: fixed;
  right: -6px;
  top: 100px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 46px);
}
.pc_sto22 {
  height: 59px;
  line-height: 59px;
}
.pc_sto23 {
  height: calc(100% - 49px);
  overflow: scroll;
}
.pc_sto23 div {
  border-bottom: 1px solid #fff;
}
.pc_sto24 {
  float: left;
  margin-left: 10px;
}
.pc_pdd25 {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 80px;
  width: 100%;
  overflow: hidden;
  background: #fff;
  z-index: 1;
  color: @themeFontColor;
  border-top: 1px dashed #e3e6eb;
}
.pc_sto26 {
  font-size: 16px;
  position: relative;
  line-height: 16px;
  width: 100%;
  height: 30px;
  border-bottom: 2px dashed #e5e5e5;
  margin-top: 10px;
}
.pc_sto27 {
  float: left;
  font-weight: bold;
  text-indent: 40px;
}
.pc_sto29 {
  width: 100%;
  overflow: hidden;
  font-size: 16px;
  margin-top: 10px;
}
.pc_pdd29 {
  width: 100%;
  overflow: hidden;
  font-size: 16px;
  margin-top: 10px;
}
.pc_sto3 {
  width: 33%;
  overflow: hidden;
  float: left;
}
.pc_pdd30 {
  width: 100%;
  padding-top: 20px;
  overflow: hidden;
  float: left;
}
.pc_sto31 {
  width: 105px;
  text-indent: 20px;
  float: left;
  line-height: 40px;
  font-weight: bold;
}
.pc_sto32 {
  float: left;
  width: calc(100% - 105px);
}
.pc_pdd32 {
  float: left;
  width: calc(100% - 155px);
}
.pc_pddsm32 {
  float: left;
  width: calc(100% - 155px);
}
.pc_sto33 {
  float: left;
  width: calc(100% - 123px);
}
.pc_sto34 {
  overflow: hidden;
  cursor: pointer;
  margin-top: 12px;
  width: 370px;
  direction: ltr;
}
.pc_sto34 img {
  margin-right: 10px;
  width: 55px;
  height: 55px;
  float: left;
  border: 0px;
}
.pc_sto35 {
  float: left;
  width: calc(100% - 158px);
  font-size: 16px;
  overflow: hidden;
  color: @themeFontColor;
  border-bottom: 1px solid #e3e6eb;
}
.pc_sto35 div {
  line-height: 22px;
  overflow: hidden;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pc_sto35_flex {
  display: flex;
  justify-content: left;
  padding-top: 4px;
  font-weight: bold;
  div {
    width: 100px;
  }
}
.pc_sto36 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_sto37 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeFontColor;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
}
.pc_sto38 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 50px;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 5px;
  cursor: pointer;
}
.pc_sto39 {
  width: 93px;
  position: fixed;
  right: -9px;
  top: 153px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 152px);
  z-index: 1;
}
.pc_sto4 {
  height: 96%;
  overflow: scroll;
}
.pc_sto4 div {
  border-bottom: 1px solid #fff;
}
.pc_sto41 {
  height: 47px;
  line-height: 47px;
  border-radius: 5px;
  color: #fff;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
}
.pc_sto42 {
  width: 93px;
  position: fixed;
  right: -9px;
  top: 153px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 130px);
  z-index: 1;
}
.pc_sto43 {
  width: 370px;
  overflow: hidden;
}
.pc_sto44 {
  width: 370px;
  margin: 0 auto;
  margin-top: 60px;
  color: #969696;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
.pc_sto44 img {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  margin-top: 0px;
}
.pc_pdd_search_input {
  float: left;width: 310px;height: 36px;outline: none;
  margin-left: 10px;font-size: 16px;border: none;padding: 0px 10px 0px 0px;
}
.pc_sto45{
  width: 20px;height: 20px;margin-top: 9px;cursor: pointer;
}
.pc_sto46 {
  float: right;
  width: 70px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 20px;
  text-align: center;
  line-height: 38px;
  font-size: 16px;
  color: @themeBackGroundColor;
  margin-right: 20px;
  cursor: pointer;
}
.pc_sto47 {
  float: right; width: 70px; height: 40px; border-radius: 20px; text-align: center; line-height: 40px; font-size: 16px;
  color: rgb(255, 255, 255); margin-right: 20px; cursor: pointer;
  background: @themeBackGroundColor;
  /* background-image: linear-gradient(90deg, rgb(209, 185, 138) 0%, #CFA26B 100%); */
}
/* .pc_ppd46 {
  float: right;width: 100px;height: 40px;background: #ffffff;border: solid 1px #e3e6eb;border-radius: 5px;
  text-align: center;line-height: 38px;font-size: 16px;color: @themeFontColor;margin-right: 12px;cursor: pointer;
}
.pc_ppd47 {
  float: right;width: 120px;height: 40px;background: #ffffff;border: solid 1px #e3e6eb;border-radius: 5px;
  text-align: center;line-height: 38px;font-size: 16px;color: @themeFontColor;margin-right: 12px;cursor: pointer;
} */
.upgrade1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.upgrade11 {
  width: 450px;
  min-height: 280px;
  background: #fff;
  border-radius: 4px;
  margin: 250px auto 0px;
  padding-bottom: 10px;
}

.upgrade12 {
  width: 93%;
  font-size: 24px;
  margin-left: 20px;
  line-height: 100px;
  text-align: center;
  font-weight: bold;
  color: @themeFontColor;
}

.el-tooltip__popper{
  background-color: #F8F4EC !important;
  color: @themeFontColor !important;
  font-size: 16px;
  max-width: 350px;
  border: 1px solid @themeFontColor;
}
.upgrade13{
  display:-webkit-box;
  text-overflow:ellipsis;
  overflow:hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient:vertical;
}
/deep/.bg_highlight_dark {
  background-color:#fff;
  transition: 0.5s;
  animation:bgchange 3s linear 0s 1;
}
  @keyframes bgchange{
    0% {
      background-color: #dad8d8;
    }
    100% {
    background-color: #fff;
    }
}
/deep/ .el-table .cell.el-tooltip {
  white-space: normal;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .cell {
  padding: 0 !important;
}
.pc_inv_add_goods {
  width: 150px;
  height: 54px;
  background: @themeBackGroundColor;
  box-shadow: 0px 2px 6px rgba(180, 153, 90, 0.3);
  border-radius: 30px;
  font-size: 18px;
  line-height: 52px;
  text-align: center;
  color: #FFFFFF;
  font-weight: 700;
  margin-left: 116px;
  margin-top: -38px;
  cursor: pointer;
  position: absolute;
  z-index: 1;
}
.pc_sto_cat {
  float: left;
  width: 110px;
  height: 40px;
  border-radius: 4px 0 0 4px;
  text-align: center;
  color: #FFF;
  line-height: 40px;
  font-weight: normal;
  font-size: 16px;
  /deep/.el-input__inner {
    border-radius: 4px 0 0 4px;
    border-right: 0px;
    background: #CFA26B;
    color: #FFFFFF;
    font-weight: 500;
    text-align: center;
  }
  /deep/ .el-input__icon {
    color: #FFFFFF;
  }
}
.pc_sto_center {
  float: left;
  width: 100%;
  font-size: 16px;
  color: @themeFontColor;
  .pc_sto_center_top {
    font-weight: bold;
    font-size: 16px;
    color: @themeFontColor;
  }
  .pc_sto_center_bottom {
    height: 40px;
    div {
      display: inline-block;
      font-size: 16px;
      color: @themeFontColor;
      font-weight: bold;
      line-height: 40px;
    }
  }
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .header_title{
    color: @themeFontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.pc_stock0 {
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .pc_stock1 {
    width: 240px;
    height: 144px;
    border: 1px solid #E3E6EB;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.pc_stock41 {
  padding: 30px;
  .pc_stock40 {
    color: @themeFontColor;
    font-size: 16px;
    .el-input {
      font-size: 18px;
    }
    .pc_stock39 {
      font-weight: 500;
      line-height: 22px;
      display: inline-block;
      margin-right: 64px;
    }
    .pc_stock42 {
      font-size: 18px;
      background: #FFFFFF;
      border: 1px solid #CFA26B;
      border-radius: 4px;
      text-align: center;
      font-weight: 500;
      line-height: 40px;
      width: 119px;
      height: 44px;
      display: inline-block;
      color: #CFA26B;
      margin-left: 120px;
      cursor: pointer;
    }
    .pc_stock43 {
      border: 1px solid #CFA26B;
      font-size: 18px;
      background: #CFA26B;
      border-radius: 4px;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      color: #FFFFFF;
      width: 119px;
      height: 44px;
      display: inline-block;
      margin-left: 18px;
      cursor: pointer;
    }
    .pc_stock44 {
      display: inline-block;
      .el-input__suffix {
        padding-right: 10px;
        padding-top: 7px;
        color: @themeFontColor;
      }
      .el-input__inner {
        padding-right: 35px;
      }
    }
    .pc_stock45 {
      display: inline-block;
      .el-input__prefix {
        display: none;
      }
    }
  }
}
.pc_inv_36 {
  width: 110px;
  position: fixed;
  right: -2px;
  top: 178px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 258px);
  z-index: 2;
  overflow-y: auto;
  overflow-x: hidden;
}
.parent_menu_active_div {
  background: @linearBackgroundColor !important;
  width: 110px;
  margin-left: -10px;
  padding-left: 10px;
  height: 56px;
  border-bottom: 1px solid #fff;
}
.pc_inv_img {
  width: 55px;
  height: 55px;
  font-size: 16px;
  font-weight: bold;
  line-height: 27px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 8px;
  overflow: hidden;
  letter-spacing: 6px;
  margin-right: 10px;
}
.pc_inv_img2 {
  width: 55px;
  height: 55px;
  font-size: 16px;
  font-weight: bold;
  line-height: 54px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
  margin-right: 10px;
  letter-spacing: 6px;
  text-indent: 8px;
}
#deleteOneGoods {
  color:  @themeBackGroundColor;
}
.stockInventory {
  background: @stockBackGround;
  color: @themeFontColor;
}
.search{
  input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: @text;
  }
}
#editType {
  color: @themeFontColor;
}

</style>
<template>
  <div
    v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)"
    style="height: 100%;position: relative;overflow: hidden;width: 100%;background: #F5F8FB;padding: 12px !important;"
  >
    <v-AddGoods></v-AddGoods>
    <!-- 新版管理 -->
    <v-SelManage></v-SelManage>
    <!-- 重复商品选择 -->
    <v-RepeatGoodsChoose
      :repeatData="repeatData"
      :repeatCode="repeatCode"
      @repeatChooseEmit="repeatChoose">
    </v-RepeatGoodsChoose>
    <!-- 切换商品管理前确认要清空列表 -->
    <div
      v-show="confirmInvToGood"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="cancelToGoods()"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >单据未保存，确定要离开本页面？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="cancelToGoods()">取消</div>
          <div class="pc_sto38" @click="continueToGoods()">确定</div>
        </div>
      </div>
    </div>
    <!-- 是否显示新增商品页 -->
    <div
      v-show="show_if_add_goods"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="show_if_add_goods = false"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >系统暂无此商品，是否新增？</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="show_if_add_goods = false">取消</div>
          <div class="pc_sto38" @click="show_if_add_goods = false;continueAddGoods()">新增</div>
        </div>
      </div>
    </div>

    <!--本页面清空按钮，请空前进行确认-->
    <div
      v-show="sure_clean_list"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="sure_clean_list = false"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >确定清空盘点列表?</div>
        <div class="pc_sto36">
          <div class="pc_sto37" @click="sure_clean_list = false;ready_subInventory = false;">取消</div>
          <div
            class="pc_sto38"
            @click="leftList = [];sure_clean_list = false;pursRemark = '';clearData()"
          >确定</div>
        </div>
      </div>
    </div>

    <!-- 左侧部分 -->
    <div
      style="width: calc(100% - 412px);float: left;position: relative;height: calc(100% - 50px);border-radius: 5px;border: 1px solid #e3e6eb;overflow: hidden;background: #FFF;"
    >
      <div
        class="stockInventory"
        style="height: 90px;border-bottom: 1px solid #e3e6eb;"
      >
        <div class="pc_sto24" style="height: 70px;font-size: 15px;margin-left: 17px;width: 240px;">
          <div style="line-height: 15px;margin-top: 24px;">
            <span>盘点单号：{{InventoryCode}}</span>
          </div>
          <div style="margin-top: 13px;line-height: 15px;">
            <span>盘点日期：{{create_time.split(" ")[0]}}</span>
          </div>
        </div>
        <div style="float: right;margin-top: 26px;">
          <div
            class="pc_sto47"
            @click="subInventory()"
          >保存</div>
          <div
            class="pc_sto46"
            v-show="leftList.length > 0"
            @click="sure_clean_list = true"
          >清空</div>
          <div
            class="pc_sto46"
            style="width: 119px;"
            v-show="leftList.length > 0"
            @click="exportExcel"
          >导出商品列表</div>
        </div>
      </div>
      <div class="pc_sto_center" :style="'height:' + table_height + 'px;'">
        <div style="margin: 20px 20px 0 20px;height: 50px;">
          <div class="pc_sto_center_top" style="display: inline-block;width: 20%;">品名/条码</div>
          <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top" style="display: inline-block;width: 19%;text-align: center;">进价</div>
          <div class="pc_sto_center_top" style="display: inline-block;width: 14%;text-align: center;">盘前库存</div>
          <div class="pc_sto_center_top" style="display: inline-block;width: 19%;text-align: center;">盘后库存</div>
          <div class="pc_sto_center_top" style="display: inline-block;width: 14%;text-align: center;">盈亏数</div>
          <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top" style="display: inline-block;width: 10%;text-align: right;">总额</div>
        </div>
        <div id="mainTab" style="margin: 0 20px 0 20px;overflow: auto;" :style="'height:' + (table_height - 110) + 'px;'">
          <div :class="da.isHighlight ? 'bg_highlight_dark' : ''" :key="da.code" v-for="(da, index) in leftList">
            <div style="margin-bottom: 12px;font-size: 16px;">
              <div style="display: inline-block;width: 86%;font-weight: bold;color: #567485;">{{da.name}}</div>
              <div id="deleteOneGoods" style="width: 11%;text-align: right;display: inline-block;cursor: pointer;" @click="deleteOneGoods(index)">删除</div>
            </div>
            <div style="display: inline-block;width: 20%;color: #B2C3CD;">
              {{da.code ? da.code : '-'}}
            </div>
            <div v-if="!$employeeAuth('purchase_price')" style="display: inline-block;width: 19%;padding: 0 15px 0 15px;">
              <input
                :id="'m' + index"
                @focus="selectText('m' + index)"
                type="text"
                @blur="formatLeftList('m',index)"
                v-model="da.purPrice"
                style="text-align: center;width: 100%;background: #f5f8fb;border: solid 1px #e3e6eb;height: 40px;line-height: 38px;border-radius: 5px;"
                @input="da.purPrice = $pricePurPriceLimit(da.purPrice)"
              />
            </div>
            <div style="display: inline-block;width: 14%;text-align: center;color: #B2C3CD;">{{da.curStock}}</div>
            <div style="display: inline-block;width: 19%;padding: 0 15px 0 15px;">
              <input
                :id="'n' + index"
                @focus="selectText('n' + index)"
                type="text"
                @blur="formatLeftList('n',index)"
                v-model="da.afterStocknum"
                style="text-align: center;width: 100%;height: 40px;line-height: 38px;background: #f5f8fb;border: solid 1px #e3e6eb;border-radius: 5px;padding: 0 16px;"
                @input="da.afterStocknum = afterStockNumInput(da.afterStocknum, da.unitName)"
              />
            </div>
            <div style="display: inline-block;width: 14%;text-align: center;color: #B2C3CD;">{{$toDecimalFormat(Number(da.afterStocknum) - Number(da.curStock), 3)}}</div>
            <div v-if="!$employeeAuth('purchase_price')" :id="'mn' + index" style="display: inline-block;width: 10%;text-align: right;color: #B2C3CD;">{{$toDecimalFormat(da.subAmt, 2, true)}}</div>
            <div style="height: 16px;padding: 0 20px 0 20px;border-bottom: 1px dashed #E5E8EC;margin-bottom: 16px;"></div>
          </div>
        </div>
        <div style="padding: 0px 20px 0 20px;background: #F5F7FA;">
          <div class="pc_sto_center_bottom">
            <div class="pc_sto_center_top" style="display: inline-block;width: 20%;">合计：</div>
            <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top" style="display: inline-block;width: 19%;text-align: center;"></div>
            <div class="pc_sto_center_top" style="display: inline-block;width: 14%;text-align: center;"></div>
            <div class="pc_sto_center_top" style="display: inline-block;width: 19%;text-align: center;">{{formatFloat(Number(total_afterStocknum), 3)}}</div>
            <div class="pc_sto_center_top" style="display: inline-block;width: 14%;text-align: center;">{{formatFloat(Number(total_number), 3)}}</div>
            <div v-if="!$employeeAuth('purchase_price')" class="pc_sto_center_top" style="display: inline-block;width: 10%;text-align: right;">{{Number(total_money).toFixed(2)}}</div>
          </div>
        </div>
      </div>
      <div class="pc_pdd25">
        <div class="pc_pdd30">
          <div class="pc_sto31">说明：</div>
          <el-input
            id="sbt1"
            @focus="selectText('sbt1')"
            v-model="pursRemark"
            placeholder="添加说明..."
            class="pc_pddsm32"
            style="font-size: 16px;margin-left:-20px;border-radius:4px"
            maxlength="50"
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="pursRemark = pursRemark.replace(/[$']/g, '')"
          ></el-input>
        </div>
      </div>
    </div>
    <div class="search" style="width: 400px;float: left;height: calc(100% - 50px);margin-left: 12px;border-radius: 4px 0 0 4px;
      padding:18px 0 20px 18px;background-color: white;border: 1px solid #E3E6EB">
      <div style="overflow: hidden;">
        <div
          style="float: left;width: 354px;height: 40px;background-color: #ffffff;border: solid 1px #e3e6eb;border-radius: 4px;"
        >
          <input
            id="search_keyword"
            type="text"
            placeholder="商品名称/条码/首字母/扫码"
            v-model="keyword"
            class="pc_pdd_search_input"
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="keyword = keyword.replace(/[$']/g, '')"
            @keydown.enter="inputSelectHandler('search_keyword')"
          />
          <img alt="" v-show="keyword != ''" @click="inputFocus('search_keyword')" src="../../image/pc_clear_input.png" class="pc_sto45" />
        </div>
      </div>
      <div id="stock_right_list_id" class="scroll-bar" style="height: calc(100% - 54px);overflow: scroll;margin-top: 10px;">
        <div class="pc_sto34" v-for="(td,index) in right_list" v-bind:key="index" @click="pushLeft(td)">
          <img alt="" v-if="td.url !== '' && td.url !== null" v-lazy="$getSrcUrl(td.url)" onerror="javascript:this.src='../../image/pc_no_cloth_img.png';this.onerror=null;"/>
          <img alt="" v-if="(td.url === '' || td.url === null) && td.image !== '' && td.image !== null" v-lazy="$getSrcUrl(td.image)" onerror="javascript:this.src='../../image/pc_no_cloth_img.png';this.onerror=null;"/>
          <img v-if="(td.url === '' || td.url === null) && (td.image === '' || td.image === null) && td.pinyin === ''" src="../../image/pc_no_cloth_img.png"/>
          <div v-if="(td.url === '' || td.url === null) && (td.image === '' || td.image === null) && td.pinyin !== '' && td.pinyin.length > 2" class="pc_inv_img">
            {{td.pinyin.length > 2 ? td.pinyin.substring(0,2) : ''}}<br><span style="margin-left: 8px;">{{td.pinyin.substring(2,(td.pinyin.length > 4 ? 4 : td.pinyin.length))}}</span>
          </div>
          <div v-if="(td.url === '' || td.url === null) && (td.image === '' || td.image === null) && td.pinyin !== '' && td.pinyin.length <= 2 && td.pinyin.length > 0"
            class="pc_inv_img2">
            {{td.pinyin}}
          </div>
          <div class="pc_sto35">
            <div style="margin-top: 2px;">
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="td.name">
                <div slot="reference">
                  {{td.name}}
                </div>
              </el-popover>
            </div>
            <div class="pc_sto35_flex">
              <el-popover
                v-if="!$employeeAuth('purchase_price')"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="'进价：' + '¥' + formatPurPriceReturn(td.purPrice)">
                <div slot="reference">
                  <span style="font-size: 12px;">{{'进价 '}}</span>
                  {{'¥' + formatPurPriceReturn(td.purPrice)}}
                </div>
              </el-popover>
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :close-delay="10"
                :content="`库存：${$toDecimalFormat(td.curStock, 3, true)}`">
                <div slot="reference" style="margin-left: 5px;">
                  <span style="font-size: 12px;">库存 </span>
                  {{$toDecimalFormat(td.curStock, 3, true)}}
                </div>
              </el-popover>
            </div>
            <div style="height: 16px;"></div>
          </div>
        </div>
        <div
          v-show="right_list.length == 0 && !loading"
          class="pc_sto43"
        >
          <div class="pc_sto44">
            <img alt="" src="../../image/pc_no_goods.png" />
            <div>暂无商品信息</div>
          </div>
        </div>
        <div style="width: 100%;height: 20px;"></div>
      </div>
      <div @click="addGoods()" :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
        class="pc_inv_add_goods"><span class="el-icon-plus">&nbsp;</span>新增商品</div>
    </div>
    <div class="pc_inv_36">
      <type-menu ref="typeMenu"
        v-model="type"
        @edit="editType"></type-menu>
    </div>
    <div
      class='upgrade1'
      v-show='showZeroTips'
    >
      <div
        class='upgrade11'
      >
        <div class='upgrade12'><span>提示</span></div>
        <div style="color: #4C567C;text-align: center;font-size: 24px;">
          <div>盈亏数不能为0，</div>
          <div>请确认盈亏数量再保存</div>
        </div>
        <div class="upgrade12">
          <div style="background: rgb(189, 161, 105);
            width: 130px;
            border-radius: 4px;
            font-size: 20px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            line-height: 50px;
            text-align: center;
            cursor: pointer;
            margin-top: 34px;
            margin-left: 33%;
          " @click="showZeroTips = false">确认</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { INVENTORY_DEAL_TYPE } from '../../utils/inventoryDealType';
import TypeMenu from '@/common/components/TypeMenu';
import goodsSearch from '@/mixins/goodsSearch';
import { calc } from 'frontend-utils';
export default {
  mixins: [goodsSearch],
  components: {
    TypeMenu
  },
  data() {
    return {
      loading: false,
      total_money: 0,
      total_number: 0,
      total_afterStocknum: 0,
      showZeroTips: false,
      right_list: [],
      table_num: 0,
      type: '',
      show_weighing_category: false,
      // 右侧搜索关键字
      keyword: '',
      can_getProdList: true,
      limit: 50,
      pagenum: 1,
      leftList: [],
      // 左下角input变量
      pursRemark: '',
      // 先产生一个盘点单号，然后进行保存
      InventoryCode: '',
      // 要上传的左侧列表
      sub_list: [],
      table_height: 0,

      // 扫码枪变量
      lastTime: '',
      codeString: '',
      // 设置定时器变量
      scanTimeer: null,
      // 是否清空新增进货单弹框
      sure_clean_list: false,
      // 是否允许监听type改变
      watch_type: true,
      // 弹出是否要新增商品
      show_if_add_goods: false,
      create_time: '',
      stop_scrollFn: false,
      ready_subInventory: false,
      pinyin: false,
      typeArr: [],
      repeatCode: '',
      repeatData: []
      // keyword_timer: ''
    };
  },
  created() {
    // 监听屏幕高度变化，给table设置高度，方便屏幕固定
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
    // 开启扫码枪监听
    external.scanerHookStart();
  },
  mounted() {
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    this.reSearchLeft(); // 获取右侧商品列表
    this.getInventoryCode();
    document.getElementById('stock_right_list_id').addEventListener('scroll', this.scrollFn);
  },
  watch: {
    showAddGoods() {
      if (this.showAddGoods === false) {
        this.type = '';
        this.reSearchLeft();
        // 分类列表查询是否有称重商品
        this.$refs.typeMenu.pdWeighingCategory();
      }
    },
    type() {
      if (this.type === '-99') {
        this.getHotList();
      } else {
        this.reSearchLeft();
      }
    },
    leftList() {
      this.SET_SHOW({ pddGoodListLength: this.leftList.length });
    },
    scanerObj() {
      console.log('监听到了');
      console.log(this.zgznActive);
      if (this.showAddGoods || this.showTypeManage || !this.zgznActive) {
        return;
      }
      console.log('顺利执行');
      let scanerCode = demo.t2json(this.scanerObj).Result.replace(/[^\w-]|_/ig, '');
      if (scanerCode.length > 16) {
        demo.msg('warning', '商品条码最长16位!');
      } else if (scanerCode.length < 4) {
        demo.msg('warning', '商品条码最短4位!');
      } else {
        this.codeString = scanerCode;
        this.enterKeyCode();
      }
      this.codeString = '';
    },
    keyword() {
      this.watch_type = false;
      this.can_getProdList = false;
      // if (this.keyword !== '') {
      //   this.type = '';
      //   this.$refs.elMenu.activeIndex = null;
      // }
      var that = this;
      setTimeout(function() {
        that.watch_type = true;
        that.can_getProdList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.stop_scrollFn = true;
        that.pagenum = 1;
        that.getProdList();
        setTimeout(function() {
          that.stop_scrollFn = false;
        }, 50);
      }, that.delayedTime);
    },
    showTypeManage() {
      if (!this.showTypeManage) {
        // 刷新分类列表
        this.$refs.typeMenu.getAllCategory();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    reSearchLeft() {
      if (!this.watch_type) {
        return;
      }
      this.stop_scrollFn = true;
      // this.keyword = '';
      this.pagenum = 1;
      this.right_list = [];
      this.getProdList(1);
      setTimeout(() => {
        this.stop_scrollFn = false;
        if (this.keyword_timer) {
          clearTimeout(this.keyword_timer);
        }
      }, 0);
    },
    clearData() {
      this.total_money = 0;
      this.total_number = 0;
      this.total_afterStocknum = 0;
    },
    sonarCheck(sums, index) {
      let sumsIn = sums;
      if (index === 5 || index === 6) {
        if (sumsIn[index].toString().includes('.')) {
          let arr = sumsIn[index].toString().split('.');
          if (arr && arr[1].length > 3) {
            sumsIn[index] = Number(sumsIn[index]).toFixed(3);
          }
        }
      }
      if (index === 7) {
        sumsIn[index] = Number(sumsIn[index]).toFixed(2);
      }
      return sumsIn;
    },
    getSummaries (param) {
      const { columns, data } = param;
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums = this.sonarCheck(sums, index);
        }
      });
      return sums;
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    inputFocus(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    exportExcel() {
      var field_mapping = {};
      if (!this.$employeeAuth('purchase_price')) {
        field_mapping = {
          品名: 'name',
          进价: 'purPrice',
          盘前库存: 'curStock',
          盘后库存: 'afterStocknum',
          盈亏数: 'pick_up_num',
          总额: 'subAmt'
        };
      } else {
        field_mapping = {
          名称: 'name',
          盘前库存: 'curStock',
          盘后库存: 'afterStocknum',
          盈亏数: 'pick_up_num'
        };
      }
      console.log(this.leftList, 'this.leftList');
      if (this.leftList.length === 0) {
        demo.msg('warning', '库存盘点商品列表不能为空');
      } else {
        this.$makeExcel(this.leftList, field_mapping, '库存盘点导出商品列表' + new Date().format('yyyyMMddhhmmss'));
      }
    },
    deleteOneGoods(n) {
      this.leftList.splice(n, 1);
      this.formatLeftList();
    },
    continueToGoods() {
      this.SET_SHOW({ isGoods: true });
      this.SET_SHOW({ isStockInventory: false });
      this.cancelToGoods();
      this.SET_SHOW({ showAddGoods: false });
    },
    cancelToGoods() {
      this.SET_SHOW({ confirmInvToGood: false });
    },
    addGoods() {
      if (!this.$employeeAuth('create_products')) {
        return;
      }
      this.SET_SHOW({ suggestPrice: '' });
      this.SET_SHOW({ stockCode: '' });
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ addGoodsCategory: '其他分类' });
      this.SET_SHOW({ addGoodsUnit: '' });
    },
    pushLeft(td) {
      let deepTd = _.cloneDeep(td);
      // 如果当前leftlist为空，直接添加
      if (this.leftList.length === 0) {
        // 盘点后库存默认值为盘前库存
        deepTd.afterStocknum = deepTd.curStock;
        // 盈亏数 = 盘后库存 - 盘前库存
        deepTd.pick_up_num = calc(this.$toDecimalFormat(deepTd.afterStocknum, 2)).minus(this.$toDecimalFormat(deepTd.curStock, 2)).toNumber();
        // 盈亏金额 = 盈亏数 * 进价
        deepTd.subAmt = calc(deepTd.pick_up_num).times(this.$toDecimalFormat(deepTd.purPrice, 2)).toNumber();
        td.isHighlight = true;
        this.leftList.push(deepTd);
        this.formatLeftList();
        return;
      }
      this.leftList.forEach(item => {
        item.isHighlight = false;
      });
      // leftlist不为空,遍历判断
      for (var i = 0; i < this.leftList.length; i++) {
        if (this.leftList[i].fingerprint === td.fingerprint) {
          this.leftList[i].afterStocknum = td.curStock;
          this.leftList[i].pick_up_num = Number(Number(td.afterStocknum).toFixed(2)) - Number(Number(td.curStock).toFixed(2));
          this.leftList[i].subAmt = td.pick_up_num * Number(Number(td.purPrice).toFixed(2));
          this.leftList[i].isHighlight = true;
          this.table_num++;
          this.formatLeftList();
          this.autoJudgeScroll(i);
          return;
        }
      }
      // 盘点后库存默认值为盘前库存
      deepTd.afterStocknum = deepTd.curStock;
      // 盈亏数 = 盘后库存 - 盘前库存
      deepTd.pick_up_num = calc(this.$toDecimalFormat(deepTd.afterStocknum, 2)).minus(this.$toDecimalFormat(deepTd.curStock, 2)).toNumber();
      // 盈亏金额 = 盈亏数 * 进价
      deepTd.subAmt = calc(deepTd.pick_up_num).times(this.$toDecimalFormat(deepTd.purPrice, 2)).toNumber();
      td.isHighlight = true;
      this.leftList.push(deepTd);
      this.ready_subInventory = false;
      this.formatLeftList();
      this.autoJudgeScroll(this.leftList.length);
      console.log('AAAAAA+', _.cloneDeep(this.leftList));
    },
    autoJudgeScroll(index) {
      this.$nextTick(() => {
        setTimeout(() => {
          document.getElementById('mainTab').scrollTop = document.getElementById('mainTab').scrollHeight * (index / this.leftList.length);
        }, 0);
      });
    },
    subInventory() {
      var that = this;
      if (this.ready_subInventory === true) {
        return;
      }
      if (this.leftList.length === 0) {
        demo.msg('warning', '新增盘点单不能为空');
        return;
      }
      let inventoryParams = {};
      inventoryParams.exec = 0;
      inventoryParams.dealType = INVENTORY_DEAL_TYPE.NEW_ADD_INVENTORY;
      inventoryParams.inventoryItems = [];
      // 组装商品属性
      var pdd = {
        accountQty: '',
        goodFingerprint: '',
        actualQty: '',
        price: ''
      };
      // goodlist
      this.sub_list = [];
      for (var i = 0; i < this.leftList.length; i++) {
        var ps = _.cloneDeep(this.leftList[i]);
        pdd.accountQty = ps.curStock;
        pdd.goodFingerprint = ps.fingerprint;
        pdd.actualQty = ps.afterStocknum;
        pdd.price = ps.purPrice;
        this.sub_list.push(_.cloneDeep(pdd));
      }
      var data = {
        // 盘点单号
        code: this.InventoryCode,
        updateStock: 1,
        updatePurPrice: 1,
        remark: this.pursRemark,
        inventoryItems: this.sub_list,
        dealType: INVENTORY_DEAL_TYPE.INVENTORY
      };
      try {
        this.ready_subInventory = true;
        console.log(data, 'datadatadata');
        inventoryService.insert(data, function() {
          demo.msg('success', that.$msg.save_success);
          that.ready_subInventory = false;
          that.pursRemark = '';
          that.leftList = [];
          that.right_list = [];
          that.limit = 50;
          that.pagenum = 1;
          that.getProdList();
          that.getInventoryCode();
          that.inputFocus('search_keyword');
          that.total_money = 0;
          that.total_number = 0;
          that.total_afterStocknum = 0;
        }, function() {
          demo.msg('warning', '新增盘点单失败');
          this.ready_subInventory = false;
        });
      } catch (error) {
        demo.msg('warning', '新增盘点单失败');
        this.ready_subInventory = false;
      }
    },
    // 获取单号
    getInventoryCode() {
      var that = this;
      inventoryService.getInventoryCode(res => {
        that.InventoryCode = res;
      });
    },
    formatLeftList(index, row) {
      this.clearData();
      for (var i = 0; i < this.leftList.length; i++) {
        this.leftList[i].purPrice = this.formatPurPriceReturn(this.leftList[i].purPrice);
        this.leftList[i].curStock = isNaN(this.leftList[i].curStock) === true ? 0 : this.formatFloat(Number(this.leftList[i].curStock), 3);
        this.leftList[i].afterStocknum = this.calcAfterStocknum(this.leftList[i].afterStocknum);
        this.leftList[i].pick_up_num = this.formatFloat(Number(this.leftList[i].afterStocknum) - Number(this.leftList[i].curStock), 3);
        this.leftList[i].subAmt = (this.leftList[i].pick_up_num * this.leftList[i].purPrice).toFixed(2);
        this.total_money += Number(this.leftList[i].subAmt);
        this.total_number += Number(this.leftList[i].pick_up_num);
        this.total_afterStocknum = this.total_afterStocknum + Number(this.leftList[i].afterStocknum);
      }
      // this.$set(this.$refs.leftList.tableData, index, row);
    },
    afterStockNumInput(num, unitName) {
      let that = this;
      // 称重商品 数量最大值
      const dictWeighNumMax = this.DICT['FORM'].WEIGHING_PRODUCTS_MAX_NUM;
      // 非称重商品 数量最大值
      const dictNumMax = this.DICT['FORM'].NON_WEIGHED_GOODS_MAX_NUM;
      // 进货 数量范围 1 ~ 99999.999/99999
      const max = that.weighUnitList.indexOf(unitName) !== -1 ? dictWeighNumMax : dictNumMax;
      num = demo.$stockLimit({
        data: num,
        max: max,
        min: -max,
        decimals: that.weighUnitList.indexOf(unitName) !== -1 ? 3 : 0
      });
      return num;
    },
    calcAfterStocknum(num) {
      if (isNaN(num)) {
        return 0;
      }
      if (Number(Number(num).toFixed(2)) >= 100000) {
        return 99999.999;
      }
      if (Number(Number(num).toFixed(2)) <= -100000) {
        return -99999.999;
      }
      if (Number(num) < 0) {
        return '-' + (Math.abs(num) + '').replace(/^()*(\d+)\.(\d\d\d).*$/, '$1$2.$3');
      } else {
        return Number((num + '').replace(/^()*(\d+)\.(\d\d\d).*$/, '$1$2.$3'));
      }
    },
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      that.table_height = $(window).height() - 245;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 245;
      }, that.delayedTime);
    },
    enterKeyCode() {
      // 如果不是称重时扫码，则选择商品进入左侧列表，同时右侧弹出编辑画面
      this.onScanInput(this.codeString);
      for (var i = 0; i < this.leftList.length; i++) {
        this.formatLeftList('m', i);
        this.formatLeftList('n', i);
      }
    },
    onScanInput(str) {
      // 扫码枪扫完商品后搜索商品
      var that = this;
      var data = { pset: '', type: '', condition: this.$barCodeLimit(str), selectDel: false, limit: 100, offset: '0' };
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        console.log(json, '扫码枪扫描出商品');
        if (json.length > 1) { // 存在重复条码商品
          that.repeatCode = that.$barCodeLimit(str);
          that.repeatData = _.cloneDeep(json);
          that.SET_SHOW({ showRepeatChoose: true });
        } else if (json.length === 1 && (json[0].code === str || that.extendCodesContain(json[0], that.$barCodeLimit(str)))) {
          that.pushLeft(json[0]);
        } else {
          // 不直接显示，通过点击弹出框的新增，确定要新增商品
          if (!that.$employeeAuth('create_products')) {
            demo.msg('warning', '暂无此商品，请联系店主增加商品');
            return;
          }
          if (that.showAddGoods === false) {
            that.show_if_add_goods = true;
            that.stockCode = str;
          }
        }
      });
    },
    extendCodesContain(good, code) {
      return good.extendCodes && good.extendCodes.length &&
        good.extendCodes.map(item => { return item.extBarcode; }).indexOf(code) !== -1;
    },
    repeatChoose(repeatData, repeatIndex) {
      this.pushLeft(repeatData[repeatIndex]);
      this.SET_SHOW({ showRepeatChoose: false });
    },
    // 进货价格式化返回
    formatPurPriceReturn(value) {
      if (isNaN(value)) {
        return '0.00';
      }
      let val = Math.abs(value);
      let dotIndex = String(val).indexOf('.');
      let dotAfterLength = dotIndex === -1 ? 0 : String(val).length - dotIndex - 1;
      let indexL = dotAfterLength <= 2 ? 2 : Math.min(dotAfterLength, 6);
      return Math.min(val, 999999.999999).toFixed(indexL);
    },
    continueAddGoods() {
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ stockCode: this.stockCode });
    },
    formatFloat (f, digit) {
      if (isNaN(f)) {
        return '';
      }
      var m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    },
    handleOpen(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleClose(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleSelect(key) {
      this.type = key.split('_')[0];
    },
    editType() {
      this.SET_SHOW({ showTypeManage: true });
    }
  },
  computed: mapState({
    isHome: state => state.show.isHome,
    weighUnitList: state => state.show.weighUnitList,
    delayedTime: state => state.show.delayedTime,
    loginInfo: state => state.show.loginInfo,
    showAddGoods: state => state.show.showAddGoods,
    showTypeManage: state => state.show.showTypeManage,
    toGoodsManageFmInven: state => state.show.toGoodsManageFmInven,
    scanerObj: state => state.show.scanerObj,
    zgznActive: state => state.show.zgznActive,
    pddGoodListLength: state => state.show.pddGoodListLength,
    screenWidth: state => state.show.screenWidth,
    codeTimer: state => state.show.codeTimer,
    confirmInvToGood: state => state.show.confirmInvToGood
  }),
  beforeDestroy() {
    clearInterval(this.scanTimeer);
    this.scanTimeer = null;
    this.SET_SHOW({ showRepeatChoose: false });
    external.scanerHookStop();
  },
  destroyed() {
    this.SET_SHOW({ pddGoodListLength: 0 });
  }
};
</script>
