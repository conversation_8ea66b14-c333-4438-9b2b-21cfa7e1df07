<style lang="less" scoped>
.com_pmcu1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 800;
  display: flex;
  align-items: center;
}
.com_pmcu4 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 750;
  top: 0;
  left: 0;
  background: rgba(0,0,0,.5);
}
.com_pmcu11 {
  width: 680px;
  height: 92vh;
  margin: 4vh auto;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e5e5e5;
  position: relative;
  z-index: 150;
}
.com_pmcu12 {
  width: 100%;
  border-bottom: 1px solid #e5e5e5;
  border-radius: 6px 6px 0 0;
  height: 60px;
  line-height: 47px;
  font-size: 16px;
  background: #F5F8FB;
  display: flex;
  align-content: space-between;
  justify-items: center;
}
.com_pmcu13 {
  color: #8197A6;
  cursor: pointer;
  font-size: 36px;
  margin-left: 520px;
  line-height: 52px;
}
.com_pmcu14 {
  color: @themeFontColor;
  font-size: 18px;
  font-weight: 600;
  line-height: 22px;
  margin-left: 30px;
  padding-top: 18px;
}
.com_pmcu15 {
  color: @themeBackGroundColor;
  margin-left: 146px;
  cursor: pointer;
  float: right;
  font-size: 16px;
  margin-top: 18px;
}
.com_pmcu16 {
  color: @themeBackGroundColor;
  margin-left: 55px;
  cursor: pointer;
  float: right;
  font-size: 16px;
  margin-top: 18px;
  margin-right: 40px;
}
.com_pmcu17 {
  width: 320px;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  margin: 0 auto;
  margin-top: 0px;
}
.com_pmcu2 {
  width: 270px;
  height: 28px;
  line-height: 28px;
  margin-left: 15px;
  font-size: 16px;
  margin-top: 4px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.com_pmcu21 {
  width: 16px;
  height: 16px;
  margin-top: 10px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.com_pmcu22 {
  overflow: hidden;
  margin-top: 70px;
  font-size: 16px;
  letter-spacing: 4px;
}
.com_pmcu23 {
  width: 79px;
  height: 38px;
  color: #fff;
  line-height: 36px;
  margin-left: 100px;
  float: left;
  border-radius: 4px;
  text-indent: 21px;
  cursor: pointer;
  background: @themeFontColor;
}
.com_pmcu24 {
  width: 79px;
  height: 38px;
  line-height: 36px;
  border: 1px solid @themeBackGroundColor;
  text-indent: 21px;
  margin-left: 14px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.com_pmcu25 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.com_pmcu26 {
  width: 372px;
  height: 217px;
  background: #fff;
  margin: 0 auto;
  margin-top: 115px;
  position: relative;
  border-radius: 4px;
}
.com_pmcu27 {
  height: 85px;
  line-height: 85px;
  text-align: center;
  font-size: 18px;
  color: @themeFontColor;
}
.com_pmcu28 {
  position: absolute;
  top: 27px;
  right: 27px;
  font-size: 13px;
  line-height: 10px;
  color: #9099b2;
  cursor: pointer;
}
.com_pmcu29 {
  height: 46px;
  border-bottom: 1px solid #e5e5e5;
  background: #fff;
  width: 634px;
  line-height: 46px;
  color: @themeFontColor;
  font-size: 15px;
  position: relative;
  overflow: hidden;
  padding: 0px;
  margin: 0 20px;
}
.com_pmcu29:hover {
  background: #f4f6f8;
}
.com_pmcu3 {
  width: 16px;
  height: 16px;
  margin-top: -2px;
  margin-right: 22px;
  margin-left: 7px;
  float: left;
}
.com_pmcu31 {
  width: 20px;
  height: 20px;
  margin-top: 12px;
  margin-right: 22px;
  right: 20px;
  float: right;
  background-image: url(../image/pc_edit_mouseleave.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.com_pmcu31:hover {
  background-image: url('../image/pc_edit_mouseover.png');
}
.com_pmcu32 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  margin-left: 92px;
  float: left;
  border-radius: 4px;
  text-align: center;
  font-size: 24px;
  letter-spacing: 0px;
  cursor: pointer;
  background: @themeFontColor;
  color: #fff;
}
.com_pmcu33 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  border: 1px solid @themeBackGroundColor;
  text-align: center;
  margin-left: 14px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 24px;
  letter-spacing: 0px;
}
</style>

<template>
  <div>
    <div v-show="showSelManage" class="com_pmcu4"></div>
    <div v-show="showSelManage" class="com_pmcu1">
      <div class="com_pmcu11">
        <div class="com_pmcu12">
          <div class="com_pmcu14">{{selManageTypeName}}管理</div>
          <div class="com_pmcu13" @click="closeManage()">×</div>
        </div>
        <div style="overflow: scroll;height: calc(100% - 110px);">
          <div v-for="(op,index) in optionsList" :key="index">
            <div
              class="com_pmcu29"
              @click="agreeChooseCategory && !editing ? inputManage(op.id,op.name, op.fingerprint) : ''"
            >
              <div>
                <div style="width: 45px;height: 45px;" :style="selManageTypeName === '分类' ? 'float: left;' : 'float: right;'">
                  <img
                    alt=""
                    v-show="editing && !op.undeletable"
                    @click="showDelConfirm(selManageTypeName === '分类' ? op.id : op.name, op.fingerprint)"
                    class="com_pmcu3" style="margin-top: 15px;margin-left: 15px;"
                    src="../image/pc_delete_category.png"
                  />
                  <!--选中√图标 ↓-->
                  <div v-show="!editing && selectName === op.name"
                    class="com_pmcu3" style="margin-top: 5px;">
                    <i class="el-icon-check" style="font-size: 25px;color: #FF6159;font-weight: bold;"></i>1111
                  </div>
                </div>

                <div style="float: left;width: 420px;" :style="selManageTypeName === '分类' ? '' : 'margin-left: 45px;'">{{op.name}}</div>
                <div style="float: left;margin-left: 90px;color: #00A0E8;"
                  v-show="selManageTypeName === '单位' && weighUnitList.indexOf(op.name) !== -1">称重</div>
                <div v-if="selManageTypeName === '分类'">
                  <img v-show="editing && index == 0" src="../image/pc_category_up_grey.png" style="width: 35px;height: 35px;
                    margin-top: -5px;margin-left: 30px;cursor: pointer;" />
                  <img v-show="editing && index != 0" src="../image/pc_category_up1.png" style="width: 35px;height: 35px;
                    margin-top: -5px;margin-left: 30px;cursor: pointer;" @click="orderChange('up',index)" />
                  <img v-show="editing && index + 1 == optionsList.length" src="../image/pc_category_down_grey.png"
                    style="width: 35px;height: 35px;margin-top: -5px;margin-left: 15px;cursor: pointer;" />
                  <img v-show="editing && index + 1 != optionsList.length" src="../image/pc_category_down1.png"
                    style="width: 35px;height: 35px;margin-top: -5px;margin-left: 15px;cursor: pointer;" @click="orderChange('down',index)" />
                  <!--铅笔图标 ↓-->
                  <div v-show="editing" class="com_pmcu31" @click="editOptions(op)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="height: 50px;border-top: 1px solid #E5E8EC">
          <div v-show="!editing">
            <div class="com_pmcu16" @click="editing = true">编辑</div>
            <div class="com_pmcu15" @click="adding = true;inputFocus('newOption')">新增</div>
          </div>
          <div v-show="editing">
            <div class="com_pmcu16" @click="editing = false">完成</div>
          </div>
        </div>
      </div>
    </div>
    <!--新增弹出框-->
    <div v-show="adding" class="com_pmcu25">
      <div class="com_pmcu26">
        <div class="com_pmcu27">新增{{selManageTypeName}}</div>
        <div @click="adding = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
          <el-input
            class="com_pmcu2"
            id="newOption"
            @focus="inputing = true;selectText('newOption')"
            @blur="inputing = false"
            type="text"
            :placeholder="'输入' + selManageTypeName +'名称(限' + (selManageTypeName === '品牌' ? '20' : '5') + '个字)'"
            :maxlength="selManageTypeName === '品牌' ? 20 : 5"
            v-model.trim="addVal"
            @input="addVal = addVal.replace(/[$']/g, '')"
          >
          </el-input>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="adding = false;addVal = ''">取消</div>
          <div class="com_pmcu24" @click="addOption()">新增</div>
        </div>
      </div>
    </div>
    <!--修改弹出框-->
    <div v-show="showEdit" class="com_pmcu25">
      <div class="com_pmcu26">
        <div class="com_pmcu27">修改{{selManageTypeName}}</div>
        <div @click="showEdit = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
          <el-input
            class="com_pmcu2"
            id="editInput"
            @focus="inputing = true;selectText('editInput');"
            @blur="inputing = false"
            type="text"
            :placeholder="'输入' + selManageTypeName + '名称'"
            maxlength="5"
            v-model.trim="editVal"
            @input="editVal = editVal.replace(/[$']/g, '')"
          >
          </el-input>
        <div class="com_pmcu22">
          <div
            class="com_pmcu23"
            @click="showEdit = false;addVal = '';setIndex = ''"
          >取消</div>
          <div class="com_pmcu24" @click="saveEditOption()">保存</div>
        </div>
      </div>
    </div>
    <!--删除确认弹出框-->
    <div v-show="showDelete" class="com_pmcu25">
      <div class="com_pmcu26" style="width: 450px;height: 250px;">
        <div class="com_pmcu27" style="font-size: 24px;">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;margin-top: 12px;color: #567485"
        >
          是否删除商品{{selManageTypeName}}?
        </div>
        <div class="com_pmcu22" style="margin-top: 39px;">
          <div
            class="com_pmcu32"
            @click="showDelete = false;addVal = '';setIndex = '';fingerprint = ''"
          >取消</div>
          <div
            class="com_pmcu33"
            @click="continueDelete()"
          >确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {
      addVal: '',
      editVal: '',
      unit: '',
      optionsList: [],
      inputing: false,
      adding: false,
      editing: false,
      setIndex: '',
      fingerprint: '',
      showDelete: false,
      showEdit: false,
      inputing_unit: false,
      show_add_unit: false,
      show_editing_unit: false,
      setDetail: '', // 储存要编辑的分类/单位 信息
      addClick: false,
      brandParentId: ''
    };
  },
  watch: {
    showSelManage() {
      if (this.showSelManage) {
        this.getOptions();
      }
      this.inputing = false;
      this.adding = false;
      this.editing = false;
      this.setIndex = '';
      this.fingerprint = '';
      this.showDelete = false;
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getOptions() { // 加载列表数据
      switch (this.selManageTypeName) {
        case '分类':
          this.getAllCategory();
          break;
        case '单位':
          this.getAllUnit();
          break;
        case '品牌':
          this.getAllBrand();
          break;
      }
    },
    // 获取所有分类
    getAllCategory() {
      typeService.search(res => {
        var json = demo.t2json(res);
        console.log(json, 'getAllCategory json +++');
        if (json.length > 0) {
          json.forEach(type => {
            if (type.name === '其他分类') {
              type.undeletable = true;
            }
          });
          this.optionsList = json;
        }
      });
    },
    // 获取所有单位
    getAllUnit() {
      unitService.search(res => {
        var json = demo.t2json(res);
        console.log(json, 'getAllUnit+++');
        if (json.length > 0) {
          json.forEach(type => {
            if (type.name === '件' || this.weighUnitList.indexOf(type.name) !== -1) {
              type.undeletable = true;
            }
          });
          this.optionsList = json;
        } else {
          this.optionsList = [];
        }
      });
    },
    // 获取所有品牌
    getAllBrand() {
      Object.keys(this.specs).forEach(key => {
        if (this.specs[key].name === '品牌') {
          this.brandParentId = this.specs[key].id;
          console.log(this.brandParentId, 'this.brandParentId');
          this.optionsList = this.specs[key].item;
        }
      });
    },
    inputFocus(sid) {
      setTimeout(function() {
        document.getElementById(sid).select();
      }, 10);
    },
    orderChange(str, index) { // 分类排序 str == up 排序上升 str == down 排序下降
      var mid = '';
      if (str === 'up') {
        mid = _.cloneDeep(this.optionsList[index - 1]);
        this.optionsList[index - 1] = this.optionsList[index];
        this.optionsList[index] = mid;
      } else if (str === 'down') {
        mid = _.cloneDeep(this.optionsList[index + 1]);
        this.optionsList[index + 1] = this.optionsList[index];
        this.optionsList[index] = mid;
      } else {
        console.log('其他');
      }
      this.$forceUpdate();
      var idList = [];
      for (var i = 0; i < this.optionsList.length; i++) {
        idList.push({id: '', sortno: ''});
        idList[i].id = this.optionsList[i].id;
        idList[i].sortno = i;
      }
      typeService.orderbyUpdate(idList, () => {});
    },
    addOption() {
      var that = this;
      if (this.addVal === '') {
        demo.msg('warning', '请输入' + this.selManageTypeName + '名称');
        return;
      }
      if (this.addClick === true) {
        return;
      }
      this.addClick = true;
      setTimeout(function() {
        that.addClick = false;
      }, that.clickInterval);

      switch (this.selManageTypeName) {
        case '分类':
          this.addCategroy();
          break;
        case '单位':
          this.addUnit();
          break;
        case '品牌':
          this.addBrand();
          break;
      }
    },
    addCategroy() {
      var that = this;
      if (this.addVal === '全部分类' || this.addVal === '其他分类' || this.addVal === '称重分类') {
        demo.msg('warning', '分类已存在！');
        return;
      }
      var data = {
        parent_id: 1,
        name: this.addVal,
        pinyin: '',
        is_deleted: 0
      };
      typeService.put(data, function(res) {
        var json = demo.t2json(res);
        if (json.length === 0) {
          demo.msg('success', '新增分类成功');
          that.getAllCategory();
        }
      });
      this.addVal = '';
      this.adding = false;
    },
    addUnit() {
      var that = this;
      var data = { name: this.addVal, pinyin: '', is_deleted: 0 };
      unitService.put(data, function() {
        demo.msg('success', '新增单位成功');
        that.getAllUnit();
      });
      this.addVal = '';
      this.adding = false;
    },
    addBrand() {
      // 新增品牌
      if (this.addBrandClick === true) {
        return;
      }
      if (this.addVal === '') {
        demo.msg('warning', '请输入品牌名!');
        return;
      }
      this.addBrandClick = true;
      let subData = {
        name: this.addVal,
        parentId: this.brandParentId,
        isShowOnSpec: 1,
        isEditSelf: 1,
        isAddSub: 0,
        isShowOnProduct: 1,
        isLock: 0,
        isDel: 0
      }
      demo.$http.post(demo.$rest.updateSpecs, [subData]).then(res => {
        if (res.data.code === 200) {
          demo.msg('success', '添加品牌成功！');
          demo.$store.dispatch('info/getSpec').then(() => {
            this.getAllBrand();
            this.adding = false;
            this.addBrandClick = false;
          });
        } else {
          demo.msg('warning', res.data.msg);
          this.adding = false;
          this.addBrandClick = false;
        }
      }).catch(err => {
        demo.msg('warning', err);
        this.addBrandClick = false;
        this.adding = false;
        console.log(err);
      });
    },
    editOptions(det) {
      this.setDetail = det;
      this.editVal = det.name;
      this.showEdit = true;
      this.inputFocus('editInput');
    },
    saveEditOption() {
      if (this.editVal === '') {
        demo.msg('warning', '请输入分类名称');
        return;
      }
      if (this.editVal === '全部分类' || this.editVal === '其他分类' || this.editVal === '称重分类') {
        demo.msg('warning', '分类已存在！');
        return;
      }
      var that = this;
      // 判断分类名称是否重复
      var data = { name: this.editVal, id: this.setDetail.id };
      typeService.exist(
        data,
        function(rs) {
          var res = demo.t2json(rs);
          if (res.length > 0) {
            demo.msg('warning', '分类已存在！');
          } else {
            var subData = {
              name: that.editVal,
              id: that.setDetail.id,
              parent_id: that.setDetail.parentId,
              pinyin: that.setDetail.pinyin,
              is_deleted: 0
            };
            typeService.put(subData, function() {
              demo.msg('success', '保存分类成功');
              that.getAllCategory();
              that.SET_SHOW({ isChangeCategory: true });
            });
          }
          that.showEdit = false;
          that.editVal = '';
          that.setDetail = '';
        },
        function(error) {
          console.error(error);
        }
      );
    },
    showDelConfirm(id, fingerprint) {
      this.showDelete = true;
      this.setIndex = id;
      this.fingerprint = fingerprint;
    },
    continueDelete() {
      // 删除
      switch (this.selManageTypeName) {
        case '分类':
          this.delCategory();
          break;
        case '单位':
          this.delUnit();
          break;
        case '品牌':
          this.delBrand();
          break;
      }
    },
    delCategory() {
      var that = this;
      this.showDelete = false;
      var data = {id: this.setIndex, is_deleted: 1, typeFingerprint: this.fingerprint};
      typeService.put(data, function() {
        demo.msg('success', '删除商品分类成功');
        that.getAllCategory();
      });
      this.setIndex = '';
      this.addVal = '';
      this.editVal = '';
      this.SET_SHOW({ isChangeCategory: true });
    },
    delUnit() {
      var that = this;
      this.showDelete = false;
      var data = {is_deleted: 1, name: this.setIndex, unitFingerprint: this.fingerprint};
      unitService.put(data, function() {
        demo.msg('success', '删除商品单位成功');
        that.getAllUnit();
      });
      this.setIndex = '';
      this.addVal = '';
      this.editVal = '';
    },
    delBrand() {
      // 删除品牌
      let subData = {
        name: this.setIndex,
        parentId: this.brandParentId,
        isShowOnSpec: 1,
        isEditSelf: 1,
        isAddSub: 0,
        isShowOnProduct: 1,
        isLock: 0,
        isDel: 1
      }
      demo.$http.post(demo.$rest.updateSpecs, [subData]).then(res => {
        if (res.data.code === 200) {
          demo.msg('success', '删除品牌成功！');
          demo.$store.dispatch('info/getSpec').then(() => {
            this.getAllBrand();
            this.showDelete = false;
          });
        } else {
          demo.msg('warning', res.data.msg);
          this.showDelete = false;
        }
      }).catch(err => {
        demo.msg('warning', err);
        this.showDelete = false;
        console.log(err);
      });
    },
    inputManage(cid, name, fingerprint) {
      this.setManage(cid, name, fingerprint);
      this.closeManage();
      if (this.batchUpdateGoods !== '') {
        this.batchSet(cid, name, fingerprint);
      }
    },
    setManage(cid, name, fingerprint) {
      switch (this.selManageTypeName) {
        case '分类':
          this.SET_SHOW({ addGoodsCategoryfingerprint: fingerprint });
          this.SET_SHOW({ addGoodsCategoryId: cid });
          this.SET_SHOW({ addGoodsCategory: name });
          break;
        case '单位':
          this.SET_SHOW({ addGoodsUnitfingerprint: fingerprint });
          this.SET_SHOW({ addGoodsUnit: name });
          this.SET_SHOW({ addGoodsUnitId: cid });
          break;
        case '品牌':
          this.SET_SHOW({ addGoodsBrandId: cid });
          this.SET_SHOW({ addGoodsBrand: name });
          break;
      }
    },
    batchSet(cid, name, fingerprint) {
      if (this.batchUpdateGoods === 'category') {
        this.SET_SHOW({ batchUpdateGoodsTypeId: cid });
        this.SET_SHOW({ batchUpdateGoodsType: name });
        this.SET_SHOW({ batchUpdateGoodsTypeFingerprint: fingerprint });
        this.SET_SHOW({ batchUpdateGoods: '' });
      } else if (this.batchUpdateGoods === 'unit') {
        this.SET_SHOW({ batchUpdateGoodsUnitId: cid });
        this.SET_SHOW({ batchUpdateGoodsUnit: name });
        this.SET_SHOW({ batchUpdateGoodsUnitFingerprint: fingerprint });
        this.SET_SHOW({ batchUpdateGoods: '' });
      } else {
        this.SET_SHOW({ batchUpdateGoodsBrandId: cid });
        this.SET_SHOW({ batchUpdateGoodsBrand: name });
        this.SET_SHOW({ batchUpdateGoods: '' });
      }
    },
    closeManage() {
      this.SET_SHOW({ showSelManage: false });
      this.optionsList = [];
    }
  },
  computed: mapState({
    showSelManage: state => state.show.showSelManage,
    // selManageType: state => state.show.selManageType,
    selManageTypeName: state => state.show.selManageTypeName,
    selectName: state => state.show.selectName,
    agreeChooseCategory: state => state.show.agreeChooseCategory,
    clickInterval: state => state.show.clickInterval,
    batchUpdateGoods: state => state.show.batchUpdateGoods,
    batchUpdateGoodsType: state => state.show.batchUpdateGoodsType,
    batchUpdateGoodsTypeId: state => state.show.batchUpdateGoodsTypeId,
    batchUpdateGoodsUnit: state => state.show.batchUpdateGoodsUnit,
    batchUpdateGoodsUnitId: state => state.show.batchUpdateGoodsUnitId,
    weighUnitList: state => state.show.weighUnitList,
    specs: state => state.info.specs
  })
};
</script>
