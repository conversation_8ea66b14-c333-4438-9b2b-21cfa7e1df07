<style lang="less" scoped>
.re_deposit_record_container {
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 50px;
    height: 44px;
    line-height: 44px;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-range-editor.el-input__inner {
    border-radius: 50px;
    height: 44px;
    line-height: 44px;
  }
  /deep/ .el-date-editor .el-range__icon {
    display: none;
    height: 44px;
  }
  /deep/ .el-range-editor .el-range-input {
    margin-left: 12px;
    color: rgba(177, 195, 205, 100);
  }
  /deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-date-editor .el-range-separator {
    color: rgba(177, 195, 205, 100);
    height: 85%;
  }
  /deep/ .el-date-table td.today span {
    color: @themeBackGroundColor !important;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    height: 50px;
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-input__inner {
    border-radius: 50px;
    color: rgba(177, 195, 205, 100);
    font-size: 16px;
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  background: #F5F8FB;
  .re_top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
        .re_top_left_title{
          color: @themeFontColor;
          font-size: 16px;
          font-weight: bold;
        }
        /deep/.el-input__inner {
          color: @themeFontColor;
        }
      }
    }
    .re_top_right{
      display: flex;
      align-items: center;
      .re_top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding: 0 12px;
      background: white;
      color: @themeFontColor;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.supplier-curson {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.supplier-title {
  color:#537286;
  border-bottom: 1px solid #537286;
  cursor: pointer;
}
.pc_report {
  float: left;
  margin-left: 14px;
  margin-top: 5px;
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
}
.pc_report2 input {
  width: 235px;
  height: 25px;
  line-height: 28px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 9px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_rep {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  padding: 12px !important;color: #333;font-size: 15px;background: #F5F8FB;
}
.pc_rep11 {
  margin-top: 5px;overflow: hidden;
}
.pc_rep12 {
  float: left;line-height: 40px;width: 80px;
}
.pc_rep13 {
  float: left;margin-left: 50px;line-height: 40px;width: 50px;color: @themeFontColor;
}
.pc_rep14 {
  cursor: pointer;width: 100px;height: 40px;margin-left: 50px;line-height: 40px;
  border-radius: 5px;text-align: center;color: #FFF;font-size: 18px;
  background: linear-gradient(90deg, #f1d3af, #cfa26b);float: left;
}
.pc_rep15 {
  overflow: hidden;height: 80px;margin-top: 20px;color: @themeFontColor;font-size: 16px;
}
.pc_rep16 {
  width: calc((100% - 4px) / 5);text-align: center;margin-top: 10px;float: left;
}
.pc_rep17 {
  font-size: 16px;line-height: 16px;
}
.pc_rep18 {
  font-size: 22px;line-height: 22px;font-weight: bold;color: #B59C5D;margin-top: 15px;
}
.pc_rep19 {
  border-left: 1px solid #E3E6EB;height: 66px;margin-top: 5px;float: left;
}
.pc_rep2 {
  width: 100%;position: relative;height: calc(100% - 210px);border-radius: 5px;
  border: 1px solid #e3e6eb;overflow: hidden;background: #FFF;margin-top: 20px;
}

.el-table .ascending .sort-caret.ascending{
  border-bottom-color: @themeBackGroundColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color: @themeBackGroundColor;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.input_goods_name .el-input--prefix .el-input__inner {
  padding-left: 30px;
  background: white;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
  background: white;
}

.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}

.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}

.el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.end-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}

.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}

.pc_btn1 {
  cursor: pointer;font-weight: 700;
  width: 110px;height: 44px;margin-left: 50px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeBackGroundColor;float: left;border: 0px;outline: none;
}

.filter_dropdown{
  max-height:250px;
  overflow: auto;
}
.filter_dropdown::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #F5F5F5;
}
.filter_dropdown::-webkit-scrollbar-track {
  //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}
.el-dropdown-menu__item:focus{
  color: #606266;
  background-color: #fff;
}
.el-dropdown-menu__item:hover{
  color: @themeBackGroundColor;
  background-color: #fff;
}
.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #606266;
  background-color: #fff;
}
.isInPutIng  {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <!-- 商品库存查询 -->
    <div class="re_deposit_record_container">
      <div>
        <div class="re_top">
          <div class="re_top_left_container">
            <div style='float: left;'>
              <div
                class='pc_report2'
                :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng1'"
              >
                <input
                  @focus='inputing_keyword = true'
                  @blur='inputing_keyword = false'
                  type='text'
                  placeholder='商品名称/条码/首字母/扫码'
                  v-model.trim='keywordGoods'
                  id='goods_keyword_report'
                  @compositionstart='pinyin = true'
                  @compositionend='pinyin = false'
                  @input="keywordGoods = $goodsNameFormat(keywordGoods)"
                  @keydown.enter="inputSelectHandler('goods_keyword_report')"
                />
                <img
                  alt=""
                  class='pc_report1'
                  v-show="keywordGoods != ''"
                  @click="focusInput('goods_keyword_report')"
                  src='../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div class="re_top_left">
              <vCjSelect @searchChange="searchChange"></vCjSelect>
            </div>
            <div class="re_top_left">
              <el-cascader
                v-model="detailValue"
                :options="category_list"
                :props="{ checkStrictly: true }"
                placeholder="分类选择">
              </el-cascader>
            </div>
          </div>
          <div class="re_top_right">
            <button
              class="pc_btn1"
              @click="exportExcel">导出表格</button>
          </div>
        </div>
        <div class="re_table_container">
          <el-table
            ref="multipleTable"
            :height="goods_height"
            stripe
            :empty-text="!loading ? '暂无数据' : ' '"
            :data="goods_data"
            style="font-size: 16px;margin-top: 5px;color: #567485;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
            id="stockRecordTab">
            <el-table-column
              prop="name"
              min-width="22%"
              :show-overflow-tooltip="true"
              label="商品名称">
            </el-table-column>
            <el-table-column
              min-width="13%"
              label="条码"
              prop="code">
            </el-table-column>
            <el-table-column
              prop="typeName"
              min-width="12%"
              align="left"
              label="商品分类">
            </el-table-column>
            <el-table-column
             label="供应商"
             min-width="7%"
             :show-overflow-tooltip="true"
             align="center">
              <template slot-scope="scope">
                <div class="supplier-curson" @click="openSupplier(scope.row.supplierName, scope.row)">
                   <span :class="scope.row.supplierName ? 'supplier-title' : ''">{{ scope.row.supplierName ? scope.row.supplierName : '-'}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="salePrice"
              min-width="12%"
              align="center"
              label="售价">
            </el-table-column>
            <el-table-column
              prop="vipPrice"
              min-width="11%"
              align="center"
              label="会员价">
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              min-width="11%"
              align="center"
              label="进价">
              <template slot-scope="scope">{{ scope.row.purPrice }}</template>
            </el-table-column>
            <el-table-column
              min-width="12%"
              align="center"
              label="库存"
              :sortable="'custom'">
              <template slot-scope="scope">{{scope.row.curStock}}</template>
            </el-table-column>
            <el-table-column
              prop="unitName"
              min-width="7%"
              align="center"
              label="单位">
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              共<span>{{towIntNumber(goods_head.goodAmtTotal)}}</span>种商品
              ，库存总量：<span>{{$toDecimalFormat(goods_head.curStockAmtTotal, 3, true)}}</span>
              ，<span v-if="!$employeeAuth('purchase_price')" style="color: #567485;">
                总进价：<span>¥{{$toDecimalFormat(goods_head.purPriceAmtTotal, 2, true)}}</span>，
              </span>
              总售价：<span>¥{{$toDecimalFormat(goods_head.salePriceAmtTotal, 2, true)}}</span>，
              总会员价：<span>¥{{$toDecimalFormat(goods_head.vipPriceAmtTotal, 2, true)}}</span>
            </div>
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="goods_total"
                @current-change="goodsChange"
                :current-page="goods_pagenum"
                :page-size="goods_pageSize"
                :page-count="goods_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="handleSizeChange"
                  :pageSize.sync="goods_pageSize"
                  :currentPage.sync="goods_pagenum"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <supplier-information :visible.sync="visible" :detailTitle="detailTitle"></supplier-information>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import logList from '@/config/logList';
import vCjSelect from '@/common/components/CjSelect';
import SupplierInformation from '@/common/components/SupplierInformation';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  data() {
    return {
      loading: false,
      detailTitle: {}, // 供应商详情
      supplierObject: {
        all: false,
        notSupplier: false,
        supplierList: []
      }, // 供应商传参
      pinyin: false,
      inputing_keyword: false,
      detailValue: [''],
      category_list: [],
      keywordGoods: '',
      goodssort: {},
      visible: false, // 编辑供应商弹窗
      pageKey: 0,
      goods_total: 0,
      goods_pagenum: 1,
      goods_height: $(window).height() - 310,
      goods_pageSize: 10,
      goods_data: [],
      multipleSelection: [],
      dateGoodStocks: '',
      goods_head: {
        goodAmtTotal: 0,
        curStockAmtTotal: 0,
        purPriceAmtTotal: 0,
        salePriceAmtTotal: 0,
        vipPriceAmtTotal: 0
      },
      loadingInstance: null,
      field_mapping: {
        '商品名称': 'name',
        '条码': 'code',
        '分类': 'typeName',
        '售价': 'salePrice',
        '进价': 'purPrice',
        '库存': 'curStock',
        '单位': 'unitName'
      },
      export_data: [],
      colorCheckedArr: [], // 颜色下拉菜单选中内容
      sizeCheckedArr: [],
      seasonCheckedArr: []
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading,
    vCjSelect,
    SupplierInformation,
    vCjPageSize
  },
  created() {
    demo.actionLog(logList.clickStockReportDetail);
    this.getAllCategory();
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.searchGoodStocksMain();
  },
  mounted() {
    this.listenResize();
    $('#goods_keyword_report').focus();
  },
  watch: {
    keywordGoods() {
      var that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.searchGoodStocksMain();
      }, that.delayedTime);
    },
    dateGoodStocks() {
      var that = this;
      if (that.dateGoodStocks) {
        that.searchGoodStocksMain();
      }
    },
    detailValue() {
      var that = this;
      that.searchGoodStocksMain();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // openSupplier供应商选择
    openSupplier(e, value) {
      if (e) {
        this.detailTitle = _.cloneDeep(value);
        this.visible = true;
      }
    },
    // 供应商回调
    searchChange(e) {
      this.supplierObject = _.cloneDeep(e);
      this.searchGoodStocksMain();
    },
    focusInput(sid) {
      this.keywordGoods = '';
      $('#' + sid).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    headerStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    showLoading() {
      this.loadingInstance = Loading.service({
        lock: true
      });
    },
    hideLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    sortChange(e) {
      this.goods_pagenum = 1;
      this.goodssort = e;
      this.searchGoodStocks();
    },
    goodsChange(val) {
      this.goods_pagenum = val;
      this.searchGoodStocks();
    },
    handleSizeChange() {
      this.searchGoodStocks();
    },
    /**
     * 商品库存查询
     */
    searchGoodStocksMain() {
      if (this.pinyin) {
        return;
      }
      this.goods_pagenum = 1;
      this.searchGoodStocks();
    },
    /**
     * 商品库存查询
     */
    searchGoodStocks() {
      let that = this;
      this.loading = true;
      let sub_data = {
        'limit': this.goods_pageSize,
        'offset': Number((this.goods_pagenum - 1) * this.goods_pageSize),
        'condition': this.keywordGoods.replace(/'/g, '‘'),
        'type': this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        'order': this.goodssort.order,
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      console.log(sub_data, 'sub_data++');
      // this.reportFormLog(_.cloneDeep(sub_data), '库存查询检索');
      goodService.stockGoodsReports(sub_data, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        this.goods_total = Number(demo.t2json(res).count);
        this.goods_data = demo.t2json(res).datas.map(item => {
          return {
            ...item,
            'salePrice': Number(item.salePrice).toFixed(2),
            'vipPrice': Number(item.vipPrice).toFixed(2),
            'curStock': that.$toDecimalFormat(item.curStock, 3, true)
          };
        });
        this.goods_head = demo.t2json(res).total;
      }, () => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      })
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_report_detail', action: 'reportFormLog', description});
      }
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : 0;
    },
    towIntNumber(val) {
      return val ? Number(val) : 0;
    },
    // 获取所有类别
    getAllCategory() {
      var that = this;
      that.category_list = [{label: '全部分类', value: ''}, {label: '称重分类', value: '0'}];
      typeService.search(function(res) {
        var json = demo.t2json(res);
        if (json.length > 0) {
          json.forEach(item => {
            let obj = {
              label: item.name,
              value: item.fingerprint
            };
            if (item.list.length !== 0) {
              let arr = [];
              item.list.forEach(subItem => {
                let subObj = {
                  label: subItem.name,
                  value: subItem.fingerprint
                };
                arr.push(subObj);
              });
              obj['children'] = arr;
            }
            that.category_list = that.category_list.concat(obj);
          });
        }
      });
    },
    exportExcel() {
      var that = this;
      let sub_data = {
        'condition': this.keywordGoods.replace(/'/g, '‘'),
        'type': this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        'order': this.goodssort.order,
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      this.reportFormLog(_.cloneDeep(sub_data), '库存查询导出表格');
      goodService.stockGoodsReportsExport(sub_data, res => {
        console.log(res, 'ress');
        this.export_data = demo.t2json(res).map(item => {
          return {
            ...item,
            'curStock': that.formatFloat(Number(item.curStock), 3),
            'supplierName': item.supplierName || '-',
            'typename': item.typename || '-'
          };
        });
        if (this.export_data.length === 0) {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        } else {
          var field_mapping = {};
          if (!this.$employeeAuth('purchase_price')) {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              商品分类: 'typename',
              供应商: 'supplierName',
              售价: 'salePrice',
              会员价: 'vipPrice',
              进价: 'purPrice',
              库存: 'curStock',
              单位: 'unitname'
            };
          } else {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              商品分类: 'typename',
              供应商: 'supplierName',
              售价: 'salePrice',
              会员价: 'vipPrice',
              库存: 'curStock',
              单位: 'unitname'
            };
          }
          this.export_data.forEach(item => { item.code = item.code ? item.code : '' });
          that.$makeExcel(this.export_data, field_mapping, '商品库存查询' + new Date().format('yyyyMMddhhmmss'));
          this.export_data = [];
        }
      });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 460;
      }, that.delayedTime);
    },
    formatFloat (f, digit) {
      if (isNaN(f)) {
        return '';
      }
      var m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    },
    filterByColor(id) {
      if (this.colorCheckedArr.indexOf(id) === -1) {
        this.colorCheckedArr.push(id);
      } else {
        this.colorCheckedArr.splice(this.colorCheckedArr.findIndex(i => i === id), 1);
      }
    },
    filterBySize(id) {
      if (this.sizeCheckedArr.indexOf(id) === -1) {
        this.sizeCheckedArr.push(id);
      } else {
        this.sizeCheckedArr.splice(this.sizeCheckedArr.findIndex(i => i === id), 1);
      }
    },
    filterBySeason(id) {
      if (this.seasonCheckedArr.indexOf(id) === -1) {
        this.seasonCheckedArr.push(id);
      } else {
        this.seasonCheckedArr.splice(this.seasonCheckedArr.findIndex(i => i === id), 1);
      }
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    delayedTime: state => state.show.delayedTime
  }),
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
    this.hideLoading();
  }
};
</script>
