# 详细功能说明_S1【商品】

**当前任务状态：** ✅ 已完成分析

## 模块概述

S1【商品】模块是ZGZN POS系统的核心业务模块，包含商品管理、库存设置、一品多码、库存盘点、传秤管理等功能，是整个系统的商品数据中心和库存管理中心。

## 功能详细分析

### 1. 商品管理功能

**文件位置：** `src/page/pc/goods.vue`、`src/common/service/goodService.js`

**主要功能点：**

#### 1.1 商品新增功能
- **基本信息：** 商品名称、条码、分类、单位、规格等
- **价格设置：** 售价、会员价、进价等价格信息
- **库存设置：** 初始库存、库存预警、无库存销售等
- **商品图片：** 支持商品图片上传和管理
- **商品属性：** 生产日期、保质期、供应商等属性

**技术实现：**
```javascript
// 商品新增
insert: async function (params, onSuccess, onFail) {
  params.manufactureDate = demo.isNullOrTrimEmpty(params.manufactureDate) ? null : params.manufactureDate;
  params.expiryDays = demo.isNullOrTrimEmpty(params.expiryDays) ? null : params.expiryDays;
  let params1 = _.cloneDeep(params);
  this.handleParams(params1);
  let inputCodes = _.compact(_.map(params1.items, 'code'));
  if (inputCodes.length > new Set(inputCodes).size) {
    onFail('条码不允许重复');
    return;
  }
  // 执行新增流程
  this.insertMajorCode(goods, () => {
    this.insertCode(goods, goodsParams, () => {
      this.insertImagesCheck(goodsParams, () => {
        this.insertDo(goods, goodsParams, onSuccess, onFail);
      }, onFail);
    }, onFail);
  }, onFail);
}
```

#### 1.2 商品编辑功能
- **信息修改：** 修改商品基本信息
- **价格调整：** 批量或单个调整商品价格
- **库存调整：** 库存数量的调整和修正
- **属性更新：** 更新商品属性和规格信息
- **图片管理：** 商品图片的替换和删除

#### 1.3 商品删除功能
- **单个删除：** 删除单个商品
- **批量删除：** 批量删除多个商品
- **分类删除：** 按分类批量删除商品
- **软删除：** 逻辑删除，可恢复
- **库存处理：** 删除时的库存处理选项

**技术实现：**
```javascript
// 批量删除商品
batchDelGoods() {
  goodService.deleteByTypes({
    fingerprint: this.checkedCities, 
    isDelType: +this.dllCategoryFlg + ''
  }, () => {
    this.showBatchDelGoods = false;
    if (this.dllCategoryFlg) {
      demo.msg('success', '删除分类成功');
      this.checkCurrentTypeIsDel();
    } else {
      demo.msg('success', '删除商品成功');
    }
    this.$refs.typeMenu.update();
    this.handleGoodList();
  });
}
```

### 2. 商品搜索和查询

**主要功能点：**

#### 2.1 多维度搜索
- **名称搜索：** 按商品名称模糊搜索
- **条码搜索：** 按商品条码精确搜索
- **首字母搜索：** 按商品首字母快速搜索
- **扫码搜索：** 支持扫码枪扫码搜索
- **分类筛选：** 按商品分类筛选

**技术实现：**
```javascript
// 商品搜索
getProdList() {
  var data = {
    pset: this.hidSettingValue,
    all: this.supplierObject.all,
    notSupplier: this.supplierObject.notSupplier,
    supplierList: this.supplierObject.supplierList,
    isGroup: false,
    type: this.type,
    condition: this.keyword,
    limit: this.limit,
    selectDel: this.selectDel,
    getDel: this.getDel,
    offset: Number((this.pagenum - 1) * this.limit)
  };
  
  if (this.sortOrder) {
    data.orderBy = [{
      column: 'a.' + this.sortOrder.prop,
      order: this.sortOrder.order === 'descending' ? 'desc' : 'asc'
    }];
  }
  
  goodService.search(data, function(res) {
    var json = demo.t2json(res);
    that.tableData = json;
  });
}
```

#### 2.2 高级筛选
- **供应商筛选：** 按供应商筛选商品
- **时间筛选：** 按创建时间筛选商品
- **状态筛选：** 按商品状态筛选（正常/删除）
- **库存筛选：** 按库存状态筛选商品
- **价格筛选：** 按价格范围筛选商品

#### 2.3 排序功能
- **创建时间排序：** 按创建时间升序/降序
- **首字母排序：** 按首字母排序
- **价格排序：** 按售价/进价排序
- **库存排序：** 按库存数量排序
- **自定义排序：** 支持自定义排序规则

### 3. 商品分类管理

**文件位置：** `src/common/service/typeService.js`、`src/components/pc_select_manage.vue`

**主要功能点：**

#### 3.1 分类层级管理
- **分类创建：** 创建新的商品分类
- **分类编辑：** 修改分类名称和属性
- **分类删除：** 删除不需要的分类
- **层级结构：** 支持多级分类结构
- **分类排序：** 自定义分类显示顺序

**技术实现：**
```javascript
// 新增分类
addCategroy() {
  if (this.addVal === '全部分类' || this.addVal === '其他分类' || this.addVal === '称重分类') {
    demo.msg('warning', '分类已存在！');
    return;
  }
  var data = {
    parent_id: 1,
    name: this.addVal,
    pinyin: '',
    is_deleted: 0
  };
  typeService.put(data, function(res) {
    var json = demo.t2json(res);
    if (json.length === 0) {
      demo.msg('success', '新增分类成功');
      that.getAllCategory();
    }
  });
}
```

#### 3.2 特殊分类
- **热销分类：** 自动统计热销商品
- **称重分类：** 称重商品专用分类
- **其他分类：** 未分类商品的默认分类
- **全部分类：** 显示所有商品的虚拟分类

#### 3.3 分类商品管理
- **批量设置：** 批量设置商品分类
- **分类统计：** 统计各分类商品数量
- **分类商品：** 查看分类下的所有商品
- **分类转移：** 商品在分类间的转移

### 4. 商品单位管理

**主要功能点：**

#### 4.1 单位基础管理
- **单位创建：** 创建新的商品单位
- **单位编辑：** 修改单位名称
- **单位删除：** 删除不需要的单位
- **单位列表：** 显示所有可用单位

#### 4.2 单位应用
- **商品设置：** 为商品设置计量单位
- **批量设置：** 批量设置商品单位
- **单位转换：** 不同单位间的换算
- **单位统计：** 按单位统计商品

### 5. 一品多码功能

**文件位置：** `src/components/pc_add_goods.vue`

**主要功能点：**

#### 5.1 多码管理
- **主条码：** 商品的主要条码
- **扩展条码：** 商品的扩展条码列表
- **条码验证：** 条码唯一性验证
- **条码生成：** 自动生成条码功能

**技术实现：**
```javascript
// 一品多码管理
multicodeClick() {
  this.codesRuleForm.code = this.oneGoods.code;
  this.codesRuleForm.extendCodes = this.oneGoods.extendCodes && this.oneGoods.extendCodes.length
    ? this.returnExtendCodesFmt() : [{key: Date.now(), value: ''}];
  this.showMulticodeManage = true;
  setTimeout(() => {
    this.$refs.codesForm.clearValidate();
  });
}
```

#### 5.2 条码类型支持
- **EAN-13：** 13位国际标准条码
- **EAN-8：** 8位短条码
- **Code128：** Code128条码格式
- **自定义：** 自定义条码格式
- **二维码：** 二维码支持

#### 5.3 条码应用场景
- **销售扫码：** 收银台扫码销售
- **库存管理：** 库存盘点扫码
- **进货验收：** 进货时条码验证
- **商品查询：** 通过条码快速查询商品

### 6. 库存设置和管理

**主要功能点：**

#### 6.1 库存基础设置
- **初始库存：** 设置商品初始库存
- **库存预警：** 设置库存预警阈值
- **无库存销售：** 允许负库存销售
- **库存单位：** 库存计量单位设置

#### 6.2 库存调整
- **手动调整：** 手动调整库存数量
- **批量调整：** 批量调整多个商品库存
- **库存盘点：** 定期库存盘点
- **库存修正：** 库存差异修正

**技术实现：**
```javascript
// 批量更新商品库存
async batchUpdateGoodsStock(params, onSuccess, onFail) {
  const batchStockNum = params.batchStockNum;
  const list = params.list;
  const inventoryFingerprint = commonService.guid();
  await this.makeInventory(batchStockNum, list, inventoryFingerprint, onFail);
  for (let i = 0; i < list.length; i++) {
    await this.makeInventoryItem(batchStockNum, list[i], inventoryFingerprint, onSuccess, onFail);
  }
}
```

#### 6.3 库存统计
- **库存报表：** 库存统计报表
- **库存预警：** 库存不足预警
- **库存变动：** 库存变动记录
- **库存分析：** 库存周转分析

### 7. 传秤管理功能

**主要功能点：**

#### 7.1 传秤商品设置
- **称重标识：** 标识商品为称重商品
- **PLU码设置：** 设置商品PLU码
- **传秤参数：** 配置传秤相关参数
- **秤体同步：** 商品信息同步到电子秤

#### 7.2 条码秤集成
- **条码生成：** 自动生成称重商品条码
- **价格传输：** 价格信息传输到条码秤
- **商品传输：** 商品信息传输到条码秤
- **秤体管理：** 多台条码秤的管理

**技术实现：**
```javascript
// 自动生成条码
generateCodes: async function (params, onSuccess, onFail) {
  const fingerprint = params.fingerprint;
  if (demo.isNullOrTrimEmpty(fingerprint)) {
    onFail('请选择商品');
    return;
  }
  const wheres = `and is_deleted=0 and fingerprint in(${fingerprint})`;
  const goodsWithNoCode = await dao.asyncExec(sqlApi.getGoodsJoinProductScaleByWheres.format({ wheres }));
  
  // 传称商品
  const goodsSendScale = _.remove(goodsWithNoCode, item => +item.isSendscale === 1);
  Promise.all([
    this.generateCodesThirteen(goodsWithNoCode), 
    this.generateCodesSeven(goodsSendScale)
  ]).then(onSuccess).catch(onFail);
}
```

### 8. 商品导入导出

**主要功能点：**

#### 8.1 批量导入
- **Excel导入：** 支持Excel格式批量导入
- **模板下载：** 提供标准导入模板
- **数据验证：** 导入数据的格式验证
- **错误处理：** 导入错误的处理和提示
- **导入预览：** 导入前的数据预览

**技术实现：**
```javascript
// 商品批量导入
batchImport: function (params, onSuccess, onFail) {
  let importGoods = params.goods;
  importGoods.forEach((item, index) => {
    item.id = index;
  });
  let initImportGoods = _.cloneDeep(importGoods);
  let firstRow = importGoods.shift();
  const columns = ['code', 'name', 'pinyin', 'type', 'subType', 'unit', 'salePrc', 'vipPrc', 'purPrc', 'curStock', 'manufactureDate', 'expiryDays'];

  if (importGoods.length === 0) {
    onFail('导入数据为空');
    return;
  }

  let diffColumns = _.difference(columns, _.keys(firstRow));
  if (diffColumns.length > 0) {
    onFail('模板错误，请下载最新模板');
    return;
  }
  // 执行导入逻辑
}
```

#### 8.2 数据导出
- **Excel导出：** 导出商品数据到Excel
- **自定义字段：** 选择导出的字段
- **筛选导出：** 按条件筛选导出
- **格式设置：** 导出格式的自定义设置

#### 8.3 模板管理
- **导入模板：** 标准的导入模板
- **字段说明：** 各字段的详细说明
- **示例数据：** 模板中的示例数据
- **格式要求：** 数据格式的具体要求

### 9. 商品图片管理

**文件位置：** `src/common/service/imageService.js`

**主要功能点：**

#### 9.1 图片上传
- **格式支持：** 支持JPG、PNG格式
- **大小限制：** 图片大小不超过2M
- **比例建议：** 建议1:1正方形比例
- **批量上传：** 支持批量上传图片

#### 9.2 图片管理
- **图片预览：** 商品图片的预览功能
- **图片替换：** 替换现有商品图片
- **图片删除：** 删除不需要的图片
- **图片压缩：** 自动压缩图片大小

**技术实现：**
```javascript
// 图片管理
insertImagesCheck: function (goodsParams, onSuccess, onFail) {
  _.forEach(goodsParams.goods, item => {
    _.forEach(item.images, item1 => {
      let images = _.assign({ ...this.goodsImages }, item1);
      images.code = item.code;
      images.goodFingerprint = item.fingerprint;
      images.fingerprint = md5(item.code + '_' + images.order);
      goodsParams.images.push(images);
    });
  });
  // 处理图片逻辑
}
```

### 10. 商品排序管理

**文件位置：** `src/page/pc/goods_sort.vue`

**主要功能点：**

#### 10.1 排序设置
- **拖拽排序：** 通过拖拽调整商品顺序
- **批量排序：** 批量设置商品排序
- **分类排序：** 按分类设置排序
- **自动排序：** 按规则自动排序

#### 10.2 排序应用
- **收银台显示：** 影响收银台商品显示顺序
- **商品列表：** 影响商品列表显示顺序
- **热销排序：** 热销商品的排序规则
- **搜索排序：** 搜索结果的排序规则

### 11. 商品属性管理

**主要功能点：**

#### 11.1 基础属性
- **商品名称：** 商品的基本名称
- **商品编码：** 商品的内部编码
- **商品条码：** 商品的条码信息
- **商品规格：** 商品的规格描述
- **商品备注：** 商品的备注信息

#### 11.2 价格属性
- **售价：** 商品的销售价格
- **会员价：** 会员专享价格
- **进价：** 商品的进货价格
- **成本价：** 商品的成本价格
- **批发价：** 批发销售价格

#### 11.3 库存属性
- **当前库存：** 商品的当前库存数量
- **预警库存：** 库存预警阈值
- **最大库存：** 最大库存限制
- **最小库存：** 最小库存要求
- **安全库存：** 安全库存数量

#### 11.4 扩展属性
- **生产日期：** 商品的生产日期
- **保质期：** 商品的保质期天数
- **供应商：** 商品的供应商信息
- **产地：** 商品的产地信息
- **品牌：** 商品的品牌信息

## 技术架构

### 1. 数据库设计
- **goods表：** 商品主表，存储商品基本信息
- **goods_attributes表：** 商品属性表，存储扩展属性
- **goods_ext_barcode表：** 商品扩展条码表
- **goods_images表：** 商品图片表
- **types表：** 商品分类表
- **units表：** 商品单位表

### 2. 服务层架构
- **goodService：** 商品业务逻辑服务
- **typeService：** 分类管理服务
- **unitService：** 单位管理服务
- **imageService：** 图片管理服务
- **inventoryService：** 库存管理服务

### 3. 前端组件架构
- **goods.vue：** 商品管理主页面
- **pc_add_goods.vue：** 商品新增编辑组件
- **pc_select_manage.vue：** 分类单位管理组件
- **goods_sort.vue：** 商品排序管理组件

### 4. 数据流管理
- **状态管理：** 通过Vuex管理商品状态
- **数据缓存：** 商品数据的本地缓存
- **实时更新：** 商品数据的实时同步
- **事务处理：** 商品操作的事务管理

## 业务流程

### 1. 商品新增流程
1. **基本信息：** 输入商品基本信息
2. **条码验证：** 验证条码唯一性
3. **分类选择：** 选择商品分类
4. **价格设置：** 设置各种价格
5. **库存设置：** 设置初始库存
6. **图片上传：** 上传商品图片
7. **保存商品：** 保存商品信息

### 2. 商品编辑流程
1. **商品查询：** 查找要编辑的商品
2. **信息修改：** 修改商品信息
3. **变更验证：** 验证修改的合法性
4. **库存处理：** 处理库存变更
5. **保存变更：** 保存修改结果

### 3. 商品删除流程
1. **商品选择：** 选择要删除的商品
2. **依赖检查：** 检查商品依赖关系
3. **库存处理：** 处理商品库存
4. **确认删除：** 确认删除操作
5. **执行删除：** 执行删除操作

## 权限控制

### 1. 功能权限
- **商品查看：** 查看商品信息的权限
- **商品新增：** 新增商品的权限
- **商品编辑：** 编辑商品的权限
- **商品删除：** 删除商品的权限
- **价格管理：** 管理商品价格的权限

### 2. 数据权限
- **价格可见：** 控制价格信息的可见性
- **库存可见：** 控制库存信息的可见性
- **成本可见：** 控制成本信息的可见性
- **利润可见：** 控制利润信息的可见性

### 3. 操作权限
- **批量操作：** 批量操作商品的权限
- **导入导出：** 导入导出商品的权限
- **分类管理：** 管理商品分类的权限
- **库存调整：** 调整商品库存的权限

## 性能优化

### 1. 查询优化
- **索引优化：** 商品表的索引优化
- **分页查询：** 大数据量的分页处理
- **缓存机制：** 商品数据的缓存策略
- **懒加载：** 商品列表的懒加载

### 2. 存储优化
- **图片压缩：** 商品图片的压缩存储
- **数据压缩：** 商品数据的压缩存储
- **清理机制：** 无用数据的清理机制
- **归档策略：** 历史数据的归档策略

### 3. 界面优化
- **虚拟滚动：** 大列表的虚拟滚动
- **图片懒加载：** 商品图片的懒加载
- **搜索防抖：** 搜索输入的防抖处理
- **响应式设计：** 适配不同屏幕尺寸

## 数据安全

### 1. 数据完整性
- **事务处理：** 商品操作的事务保证
- **约束检查：** 数据库约束验证
- **数据验证：** 业务层数据验证
- **回滚机制：** 操作失败的回滚机制

### 2. 数据备份
- **定期备份：** 商品数据的定期备份
- **增量备份：** 商品数据的增量备份
- **备份验证：** 备份数据的完整性验证
- **快速恢复：** 数据的快速恢复机制

### 3. 操作审计
- **操作日志：** 记录商品操作日志
- **用户追踪：** 追踪操作用户信息
- **变更记录：** 记录商品变更历史
- **审计报告：** 生成操作审计报告

## 问题和改进建议

### 1. 当前问题
- **界面复杂度：** 商品管理界面功能过多，操作复杂
- **性能问题：** 大量商品时的查询和显示性能
- **用户体验：** 部分操作流程不够直观

### 2. 改进建议
- **界面简化：** 简化商品管理界面，提升用户体验
- **性能优化：** 优化大数据量的处理性能
- **流程优化：** 优化商品管理流程，减少操作步骤
- **功能增强：** 增加更多实用的商品管理功能

## 3.0版本重构建议

### 1. 架构升级
- **微服务化：** 将商品管理功能微服务化
- **API标准化：** 统一的商品API接口标准
- **数据库优化：** 优化商品数据库设计和性能
- **缓存策略：** 完善的商品数据缓存策略

### 2. 功能增强
- **智能推荐：** 基于数据分析的商品推荐
- **自动化处理：** 增加更多自动化处理功能
- **移动端支持：** 完善的移动端商品管理
- **数据分析：** 更强大的商品数据分析功能

### 3. 技术现代化
- **Vue 3：** 升级到Vue 3.x版本
- **TypeScript：** 引入TypeScript提高代码质量
- **现代化UI：** 使用现代化的UI框架
- **云原生：** 支持云原生部署和扩展
