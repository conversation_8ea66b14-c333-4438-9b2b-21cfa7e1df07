<style lang="less" scoped>
/deep/ .el-dialog__headerbtn {
  top: 10px;
  right: 10px;
}
.pc_hom {
  color: @themeFontColor;
}
.pc_hom1 {
  float: left;
  width: 370px;
  height: 100%;
  position: relative;
  z-index: 1;
  top: -50px;
  overflow-y: scroll;
  background: @left_backgroundColor;
  .logoWrap {
    margin: 0 auto;
    margin-top: 38px;
    width: 208px;
    margin-bottom: 32px;
    img {
      width: 208px
    }
  }
  .menuWrap {
    .menuGroup {
      overflow: hidden;
      width: 300px;
      margin: 0 auto;
      .pc_hom3 {
        width: 140px;
        height: 60px;
        background: @leftBut_backgroundColor;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-left: 24px;
      }
      .pc_hom3 img {
        width: 30px;
        height: 30px;
        margin-right: 20px;
      }
      .pc_hom3 div {
        font-size: 18px;
        color: @leftBut_fontColor;
        font-weight: 700;
      }
    }
    .menuGroup:not(:first-child) {
      margin-top: 20px;
    }
  }
  .pc_hom12 {
    width: 300px;
    margin: 20px auto 0;
    background: @themeBackGroundColor;
    border-radius: 5px;
    height: 180px;
    color: #fff;
    text-align: center;
    cursor: pointer;
    flex-direction: column;
  }
  .pc_hom12:hover {
    background: @themeBackGroundColor;
  }
  .pc_hom12 img {
    width: 40px;
    height: 40px;
    margin-bottom: 7px;
  }
  .pc_hom12 div {
    font-size: 18px;
    font-weight: 700;
  }
  .miniMenu {
    .menuGroup {
      width: 190px;
      .pc_hom3 {
        width: 85px;
        padding: 0;
        justify-content: center;
      }
      .pc_hom66 {
        width: 190px;
        height: 50px;
        align-items: center;
        margin-left: 0 !important;
        img {
          margin-top: 0 !important;
          width: 24px;
          height: 24px;
        }
      }
    }
    .drainage {
      margin-top: 0 auto !important;
      padding: 0 30px !important;
      width: 254px !important;
    }
    .pc_hom12 {
      width: 190px;
      height: 120px;
    }
  }
}
.pc_hom11 {
  width: 300px;
  margin: 0 auto;
  background: #3a4a55;
  border-radius: 5px;
  height: 100px;
  margin-bottom: 20px;
}
.pc_hom11:hover {
  background: #4b5b66;
  cursor: pointer;
}
.pc_hom11 img {
  float: left;
  width: 40px;
  height: 40px;
  margin-top: 29px;
  margin-left: 98px;
}
.pc_hom11 div {
  float: left;
  margin-left: 15px;
  font-size: 18px;
  color: #d1b889;
  line-height: 100px;
  font-weight: bold;
}

.pc_hom19 {
  float: left;
  overflow: hidden;
  position: relative;
  font-size: 18px;
  width: calc(100% - 370px);
  height: calc(100% - 50px);
  background-color: #f0f2f5;
  .shopInfo {
    width: 100%;
    padding: 10px 8px;
    height: calc(100% - 40px);
    display: flex;
    flex-direction: column;
    .infoWrap {
      flex-grow: 1;
      display: flex;
      justify-content: space-between;
      padding-bottom: 8px;
      .salesData {
        width: calc(100% - 255px);
        height: 100%;
      }
      .tipsWrap {
        width: 247px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .carousel {
      flex-shrink: 0;
      width: 100%;
      aspect-ratio: 9.03 / 1
    }
  }
}
.pc_hom4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.pc_hom41 {
  width: 620px;
  height: 395px;
  background: #fff;
  border-radius: 2px;
  margin: 0 auto;
  margin-top: 115px;
}
.pc_hom42 {
  width: 100%;
  height: 71px;
  border-bottom: 1px solid #eee;
  font-size: 24px;
  line-height: 75px;
  text-align: center;
  color: @themeFontColor;
}
.pc_hom43 {
  margin-top: 47px;
  margin-left: 30px;
  font-weight: 700;
  color: @themeFontColor;
  font-size: 16px;
  line-height: 16px;
}
.pc_hom44 {
  margin-top: 14px;
  margin-left: 30px;
  font-size: 16px;
  line-height: 16px;
}
.pc_hom46 {
  overflow: hidden;
  margin-top: 48px;
  font-size: 24px;
}
.pc_hom48 {
  width: 200px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 60px;
  background-image: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
@fontColor: @themeFontColor;
@themeColor: @themeBackGroundColor;
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
.tips_dialog {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  .title {
    color: @fontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content {
    color: @fontColor;
    font-size: 24px;
    margin-top: 28px;
    text-align: center;
  }
  .dialog_btn_container {
    display: flex;
    justify-content: center;
    margin-top: 50px;
    padding-bottom: 30px;
    .btn {
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
    .btn1 {
      width: 130px;
      height: 44px;
      border-style: solid;
      border-color: rgb(189, 161, 105);
      font-size: 18px;
      font-weight: normal;
    }
  }
}
.pc_hom52 {
  position: relative;
  width: 90px;
  height: 60px;
  text-align: center;
  line-height: 60px;
  font-size: 18px;
  color: #d1b889;
  float: left;
  background: #3a4a55;
  font-weight: bold;
  border-radius: 6px;
  cursor: pointer;
}
.pc_hom53 {
  width: 200px;
  height: 160px;
  background: @themeBackGroundColor;
  border-radius: 6px;
  text-align: center;
  line-height: 160px;
  font-size: 20px;
  color: #fff;
  margin: 0 auto;
  font-weight: bold;
  margin-top: 20px;
  cursor: pointer;
}
.pc_active_container {
  width: 321px;
  height: 207px;
  position: absolute;
  bottom: 52px;
  right: 0px;
  background: #fff;
  z-index: 30;
}
.pc_active_title {
  width: 100%;
  height: 52px;
  background: #bda169;
  color: #fff;
  padding: 0px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pc_active_btn_div {
  padding: 0px 32px;
  margin: 26px 0px;
  width: 100%;
  display: flex;
}
.pc_active_btn1 {
  width: 120px;
  height: 42px;
  border-radius: 6px;
  border: 1px solid #b4995a;
  font-size: 16px;
  text-align: center;
  line-height: 38px;
  color: #b4995a;
  cursor: pointer;
}
.pc_active_btn2 {
  width: 112px;
  height: 42px;
  border-radius: 6px;
  border: 1px solid #b4995a;
  background: #b4995a;
  font-size: 16px;
  text-align: center;
  line-height: 38px;
  color: #fff;
  cursor: pointer;
}
.pc_hom65 {
  position: fixed;
  bottom: 20px;
  width: 1432px;
  margin-left: 60px;
}
.pc_hom65 a {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: #b2c3cd;
  cursor: pointer;
}
.drainage {
  margin-top: 0 !important;
  width: 330px !important;
  padding: 0 15px;
}
.pc_hom66 {
  float: left;
  height: 60px;
  background: @leftBut_backgroundColor;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  width: 140px;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  .abs_count {
    position: absolute;
    top: 0;
    right: 0;
    width: 28px;
    height: 28px;
    text-align: center;
    line-height: 14px;
    font-size: 14px;
    color: #FFF;
    border-radius: 50%;
    transform: translate(50%, -50%);
    background: #F64C4C;
    font-weight: normal;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .promotion-btn-name {
    width: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // line-height: 26px;
    text-align: center;
    margin-left: 0 !important;
    p {
      width: 100%;
      margin-bottom: 0 !important;
      height: 20px;
      line-height: 20px;
    }
  }
}

.pc_hom66 img {
  width: 30px;
  height: 30px;
  margin-top: 14px;
  float: left;
}
.pc_hom66 div {
  float: left;
  margin-left: 9px;
  font-size: 16px;
  color: @leftBut_fontColor;
  line-height: 60px;
  font-weight: 700;
}
.pc_hom67 {
  background-size: 100% !important;
  width: 800px;
  height: 520px;
  margin: 0 auto;
  margin-top: calc(50vh - 300px);
  position: relative;
  z-index: 200;
}
.pc_hom68 {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 3px solid #fff;
  color: #fff;
  text-align: center;
  line-height: 18px;
  font-size: 29px;
  margin: 0 auto;
  margin-top: 28px;
  cursor: pointer;
  position: relative;
  z-index: 200;
}
#tips {
  color: @text;
}
#showOfflineDialog {
  background: @themeFontColor;
}
#confirmLogout {
  background: @themeBackGroundColor;
}
.pc_applet_drainage_div {
  height: 519px;
}
.qkd-dialog {
  /deep/.el-dialog {
    margin-top: 5vh !important;
  }
}
.pc_vx_bind {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 25;
  color: @themeFontColor;
  display: flex;
  justify-content: center;
  align-items: center;
  &--container {
    width: 400px;
    height: 412px;
    background: #fcf9f4;
    padding: 40px;
    position: relative;
    border-radius: 20px;
    .close {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
      font-size: 30px;
      font-weight: normal;
    }
    .title {
      color: @themeBackGroundColor;
      text-align: center;
      font-size: 20px;
      font-weight: bold;
    }
    .text {
      margin-top: 15px;
      color: @themeFontColor;
      font-size: 16px;
      text-align: center;
      font-weight: normal;
    }
    img {
      margin: 15px auto;
      width: 200px;
      height: 200px;
    }
    .flex {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .black {
      color: #1f1f1f;
    }
  }
  .pc_no_network_div {
    width: 196px;
    text-align: center;
    height: 100%;
    border-radius: 8px;
    background: #f5f7fa;
    color: #cacaca;
    position: absolute;
    img {
      width: 56px;
      height: 49px;
      margin: 40px 0 20px;
    }
  }
}
.vx_customer_service {
  position: fixed;
  width: 150px;
  height: 180px;
  border: 1px solid #e5c09f;
  border-radius: 12px;
  background: #fff9f4;
  z-index: 22;
  cursor: pointer;
  .title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    margin: 10px;
    color: #8e8e8e;
    position: relative;
    img {
      width: 25px;
      height: 25px;
      margin-right: 5px;
    }
    .close {
      position: absolute;
      top: -15px;
      right: 0;
      font-size: 30px;
      cursor: pointer;
    }
  }
  .img {
    padding: 0 12px;
    display: flex;
    justify-content: center;
  }
}
.themeBgColor {
  background: @themeColor;
}
</style>
<template>
  <v-SmfGuide v-if="showSmfGuide"></v-SmfGuide>
  <div v-else class="pc_hom">
    <v-Guide></v-Guide>
    <!-- <v-QKD v-if="duoduo && isHome && isQKD"></v-QKD> -->
    <!-- 首页绑定微信通知模态框 -->
    <div v-if="!isSyncingLogin && showVxBind" class="pc_vx_bind">
      <div class="pc_vx_bind--container">
        <div class="close" @click="closeVxBind">×</div>
        <div class="title">绑定店铺营业微信通知</div>
        <div class="text">微信扫码，关注“掌柜智囊商家助手”公众号并绑定就可以接收店铺营业信息</div>
        <div class="flex">
          <el-image alt="" :src="officialAccountCodeImgUrl" style="width: 196px; height: 196px">
            <div slot="error">
              <div class="pc_no_network_div">
                <img src="../../image/pc_no_network.png" style="" />
                <div style="width: 196px">无网络，请检查网络连接</div>
              </div>
            </div>
          </el-image>
        </div>
        <div class="flex">
          <el-checkbox v-model="notTipAgain" style="margin-right: 5px"></el-checkbox>
          <div class="black">下次不再提醒</div>
        </div>
      </div>
    </div>
    <!-- 微信客服 -->
    <div
      v-show="showKefu && showCustomerServe && kefuSrc"
      :style="'top:' + customerServeTop + 'px;left:' + customerServeLeft + 'px'"
      class="vx_customer_service"
      @mousedown="startMove($event)"
      @touchstart="startMove($event)"
    >
      <div class="title">
        <img src="../../image/zgzn-pos/pc_customer_serve.png" />
        微信客服
        <div class="close" @click="closeCustomerServe()">×</div>
      </div>
      <div class="img" ref="kefuSrcUrl"></div>
    </div>
    <div v-show="showRecommend" class="pc_hom4" style="z-index: 30">
      <div class="pc_hom4" @click="showRecommend = false"></div>
      <div class="pc_hom67" :style="'background: url(\'' + recommendImg + '\');'">
        <video v-if="showRecommend" width="179" height="386" style="margin-left: 615px; margin-top: 7px" controls>
          <source :src="video_src" type="video/mp4" />
        </video>
      </div>
      <div class="pc_hom68" @click="showRecommend = false">×</div>
    </div>
    <div class="pc_hom4" v-show="show_wd">
      <div class="pc_hom41" style="height: 350px; width: 450px">
        <div class="pc_hom42">开通微店</div>
        <div class="pc_hom43" style="margin-left: 87px; text-align: center; width: 271px">温馨提示：</div>
        <div class="pc_hom44" style="margin-left: 87px; text-align: center; width: 271px">微店服务属于增值服务，请联系售后</div>
        <div class="pc_hom44" style="margin-left: 87px; text-align: center; width: 271px; font-weight: bold">400-033-2520</div>
        <div class="pc_hom46" style="margin-top: 65px">
          <div class="pc_hom48" style="margin-left: 125px" @click="show_wd = false">确定</div>
        </div>
      </div>
    </div>
    <!-- 群客多引流弹窗 -->
    <el-dialog :visible.sync="showQkd" width="800px" class="qkd-dialog" :close-on-click-modal="true">
      <div class="pc_applet_drainage_div">
        <img v-lazy="oneKeyOpenGroupUrl" :src="oneKeyOpenGroupUrl" style="width: 800px" />
        <div style="margin: 20px auto 0px; text-align: center">
          <i @click="showQkd = false" class="el-icon-circle-close" style="font-size: 30px; color: #fff; cursor: pointer"></i>
        </div>
      </div>
      <div></div>
    </el-dialog>
    <!--左侧按钮部分-->
    <div class="pc_hom1" :style="screenWidth >= 1200 ? 'width: 370px;' : 'width: 260px;'">
      <div class="logoWrap">
        <img @click="debugA = true" src="../../image/zgzn-pos/pc_login_logo.png" />
      </div>
      <div class="menuWrap" :class="{miniMenu: screenWidth <= 1199}">
        <div class="menuGroup cjFlexBetween">
          <div
            class="pc_hom3"
            @click="goods()"
            :style="
              $employeeAuth('create_products') ||
              $employeeAuth('import_products') ||
              $employeeAuth('delete_products') ||
              $employeeAuth('products_curstock_inventories')
                ? ''
                : 'opacity: 40%'
            "
          >
            <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_good.png" />
            <div>商品</div>
          </div>
          <div class="pc_hom3" @click="goPage('table')" :style="$employeeAuth('show_shift_records') ? '' : 'opacity: 40%'">
            <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_detail.png" />
            <div>报表</div>
          </div>
        </div>
        <div class="menuGroup cjFlexBetween">
          <div
            class="pc_hom3"
            @click="stock()"
            :style="(!$employeeAuth('purchase') && !$employeeAuth('return_purchase')) || ultimate === null ? 'opacity: 40%' : ''"
          >
            <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_stock.png" />
            <div>进货</div>
          </div>
          <div class="pc_hom3" @click="member()" :style="judgeMember() ? '' : 'opacity: 40%'">
            <img v-if="screenWidth > 1199"
              src="../../image/zgzn-pos/pc_home_member.png"
              :style="judgeMember() ? '' : 'opacity: 40%'"
            />
            <div>会员</div>
          </div>
        </div>
        <div class="menuGroup cjFlexBetween">
          <div
            class="pc_hom3"
            @click="employee()"
            :style="(!$employeeAuth('create_employee') && !$employeeAuth('edit_employee')) || ultimate === null ? 'opacity: 40%' : ''"
          >
            <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_employee.png" />
            <div>员工</div>
          </div>
          <div class="pc_hom3" @click="goPage('change_shifts')" :style="ultimate === null ? 'opacity: 40%' : ''">
            <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_changeshifts.png" />
            <div>交接班</div>
          </div>
        </div>
        <div v-if="hasWd >= 0" class="menuGroup cjFlexBetween">
          <div class="pc_hom3" @click="store()">
            <img v-if="screenWidth > 1199" src="../../image/pc_home_littlestore.png" />
            <div>微店</div>
          </div>
        </div>
        <div class="pc_hom12 cjFlexCenter" @click="pay()">
          <img v-if="screenWidth > 1199" src="../../image/zgzn-pos/pc_home_cash.png" />
          <div>收银台</div>
        </div>
        <div class="menuGroup drainage">
          <div class="pc_hom66" @click="appletDrainage()" v-if="oneKeyOpenGroup">
            <img src="../../image/pc_home_wechat.png" />
            <div>一键微店</div>
          </div>
          <div
            class="pc_hom66"
            v-if="showPromotion && promotionBtnName"
            :style="oneKeyOpenGroup ? 'margin-left: 20px' : ''"
            @click="getRecommendBg"
          >
            <div v-if="promotionCount" class="abs_count">
              {{promotionCount >= 10 ? '10+' : promotionCount}}
            </div>
            <img
              alt=""
              src="../../image/pc_play_icon.png"
              style="margin-left: 5px;"
            />
            <div class="promotion-btn-name">
              <p v-if="promotionTextFirst">{{promotionTextFirst}}</p>
              <p v-if="promotionTextSecond">{{promotionTextSecond}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--右侧进度条及饼状图-->
    <div class="pc_hom19" :style="screenWidth > 1199 ? 'width: calc(100% - 370px);' : 'width: calc(100% - 260px);'">
      <!-- 眼睛 -->
      <div style="width: 100%">
        <van-notice-bar @click="debugB = true" :text="scroll_text" left-icon="volume-o" style="color: #a89074; background: #fcf2e1" />
      </div>
      <div class="shopInfo">
        <div class="infoWrap">
          <div class="salesData">
            <sales-data ref="salesData"
              @debugC="boolean => debugC = boolean"
              @debugD="boolean => debugD = boolean"></sales-data>
          </div>
          <div class="tipsWrap">
            <shop-warning></shop-warning>
            <shop-grow-up></shop-grow-up>
          </div>
        </div>
        <div class="carousel">
          <advert-carousel></advert-carousel>
        </div>
      </div>
      <!-- 激活码临期提醒 -->
      <div v-if="showTemporaryTip" class="pc_active_container">
        <div class="pc_active_title">
          <div style="font-size: 20px; font-weight: 700">版本过期提醒</div>
          <div style="font-size: 36px; cursor: pointer" @click="closeTemporaryTip()">×</div>
        </div>
        <div style="text-align: center; margin-top: 10px">
          <span>
            您当前的{{ ultimate ? '旗舰版' : '专业版' }}还有
            <span style="color: red">{{ trialDay }}</span>
            天到期，
            <br />
            <span>{{ isAuto ? '请点击续费前往购买激活' : '请联系原购买渠道' }}</span>
          </span>
        </div>
        <div class="pc_active_btn_div" :style="isAuto ? 'justify-content: space-between;' : 'justify-content: center;'">
          <div class="pc_active_btn1" @click="todayIgnore()">今日不再提醒</div>
          <div class="pc_active_btn2" v-if="isAuto" @click="renewalSelf()">自助续费</div>
        </div>
      </div>
      <!-- 群客多微店签约提醒 -->
      <div v-if="showSigningDialog" class="pc_active_container">
        <div class="pc_active_title">
          <div style="font-size: 20px; font-weight: 700">消息</div>
          <div style="font-size: 36px; cursor: pointer" @click="closeSigningDialog()">×</div>
        </div>
        <div style="text-align: center; margin-top: 10px">
          <span>
            您的群客多微店审核已完成
            <br />
            <span>请扫码完成签约</span>
          </span>
        </div>
        <div class="pc_active_btn_div" style="justify-content: center">
          <div class="pc_active_btn2" @click="goMicroShopAuth()">去查看</div>
        </div>
      </div>
      <!--         <div class="pc_hom65">
        <div style="text-align: center;">
          <a
          href="http://duobao.trechina.cn/agreement.html"
          target="_blank"
          >《 {{$t('page.home.privacyStatement')}}  》</a>
          <a
            href="http://duobao.trechina.cn/agreement.html"
            target="_blank"
          >《 {{$t('page.home.registeredStatement')}} 》</a>
        </div>
      </div> -->
    </div>
    <!--交接班无网时dialog-->
    <el-dialog :visible.sync="showOfflineDialog" :show-close="false" :close-on-click-modal="false" width="450px" top="214px">
      <div class="tips_dialog">
        <div class="title">提示</div>
        <div class="content">
          当前网络连接异常，
          <br />
          系统将完成交接班并返回登录页
        </div>
        <div class="dialog_btn_container">
          <div class="btn" id="showOfflineDialog" @click="showOfflineDialog = false">取消</div>
          <div class="btn" id="confirmLogout" style="margin-left: 30px" @click="confirmLogout()">登出</div>
        </div>
      </div>
    </el-dialog>

    <!--库存预警dialog-->
    <el-dialog
      :visible.sync="showStockMagDialog"
      :show-close="false"
      :close-on-click-modal="false"
      width="340px"
      height="240px"
      style="top: auto; left: auto; bottom: -50px; border-radius: 6px 6px 0px 0px"
    >
      <div style="background-color: #d5aa76; height: 50px; padding: 11px 20px">
        <span style="font-size: 18px; color: #fcfcfc; font-weight: bold">库存预警</span>
        <span style="float: right; margin-top: -8px; font-size: 28px; color: white">✖</span>
      </div>
      <div style="height: 190px">
        <div>
          <span style="font-size: 16px">您有{{ stockSize }}件商品库存不足，请及时处理</span>
          <div>
            <img
              alt="选择"
              v-show="!showStockCheckbox"
              @click="showStockCheckbox = !showStockCheckbox"
              src="../../image/zgzn-pos/pc_goods_checkbox1.png"
              style="width: 24px; height: 24px"
            />
            <img
              alt="选择"
              v-show="showStockCheckbox"
              @click="showStockCheckbox = !showStockCheckbox"
              src="../../image/zgzn-pos/pc_goods_checkbox2.png"
              style="width: 24px; height: 24px"
            />
            <span style="color: #b1c3cd">不再提醒</span>
          </div>
        </div>
        <div>
          <div class="btn" style="background: #d5aa76" @click="showStockMagDialog = false">查看</div>
          <div class="btn" style="background: white; margin-left: 30px; color: #d5aa76" @click="confirmLogout()">忽略</div>
        </div>
      </div>
    </el-dialog>
    <!--无交接班权限dialog-->
    <el-dialog :visible.sync="showPermissionDialog" :show-close="false" :close-on-click-modal="false" width="450px" top="214px">
      <div class="tips_dialog">
        <div class="title">提示</div>
        <div class="content">
          交接班后将返回登录页，
          <br />
          您确定要交接班并登出吗？
        </div>
        <div class="dialog_btn_container">
          <div class="btn" style="background: #567485" @click="showPermissionDialog = false">取消</div>
          <div class="btn themeBgColor" style="margin-left: 30px" @click="confirmLogout()">登出</div>
        </div>
      </div>
    </el-dialog>
    <vPcSMSContract v-if="isShowSMSContract" :isLogin="true" :fromType="0" @closeSMS="closeSMS" @sureSMS="sureSMS"></vPcSMSContract>
    <remain-day-tips v-if="remainDayTipsShow" :visible.sync="remainDayTipsShow"></remain-day-tips>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog } from 'element-ui';
import QRCode from 'qrcodejs2';
import base_change_shifts from '@/components/base_change_shifts.vue';
import vPcSMSContract from '@/components/pc_sms_contract.vue';
import { getWxQrCode, getServiceImgUrl } from '@/api/wxQrCode';
import { continuedUse } from '@/api/setting';
import logList from '@/config/logList';
import SalesData from './homeComponents/SalesData.vue';
import ShopWarning from './homeComponents/ShopWarning.vue';
import ShopGrowUp from './homeComponents/ShopGrowUp.vue';
import AdvertCarousel from './homeComponents/AdvertCarousel.vue';
import RemainDayTips from './homeComponents/RemainDayTips.vue';
export default {
  mixins: [base_change_shifts],
  components: {
    [Dialog.name]: Dialog,
    vPcSMSContract,
    SalesData,
    ShopWarning,
    ShopGrowUp,
    AdvertCarousel,
    RemainDayTips
  },
  data() {
    return {
      // screenWidth: document.body.clientWidth, // 屏幕尺寸
      showOfflineDialog: false, // 断网弹窗
      showStockMagDialog: false, // 库存提醒弹窗
      stockSize: 0,
      showStockCheckbox: false,
      showPermissionDialog: false, // 无交接班权限弹窗
      now_date: '',
      total_salesmoney: '',
      total_profit: '',
      number_rank: [],
      sales_rank: [],
      show_wd: false,
      ymd: '',
      show_text: false,
      no_sales_money: false,
      click_store: false,
      debugA: false,
      debugB: false,
      debugC: false,
      debugD: false,
      showQkd: false,
      gologining: false,
      showRecommend: false,
      kefuSrc: '',
      recommendImg: '',
      video_src: demo.t2json($config.Base.OtherOptions).qunKeDuo.duoduo.videoUrl,
      oneKeyOpenGroupUrl: '',
      scroll_text: this.$t('components.header.scroll_text'),
      interVal: 5000, // 走马灯切换时间
      autoPlay: true,
      noticeCountTemp: {},
      eyeMarginTop: this.screenWidth > 1199 ? (this.screenWidth - 370) * 0.32 * 0.08 + 30 : (this.screenWidth - 260) * 0.32 * 0.08 + 30,
      adHeight: this.screenWidth > 1200 ? (this.screenWidth - 488) * (195 / 1432) + 'px' : (this.screenWidth - 378) * (195 / 1432) + 'px',
      isShowSMSContract: false,
      duoduo: false,
      oneKeyOpenGroup: false,
      showPromotion: false,
      promotionBtnName: "",
      promotionBtnIndex: 5,
      promotionCount: 0,
      showVxBind: false,
      officialAccountCodeImgUrl: '',
      notTipAgain: false,
      moving: false,
      customerServeTop: window.innerHeight - 200,
      customerServeLeft: window.innerWidth - 170,
      carouselHight: 0, // 底部广告轮播高度
      remainDayTipsShow: false // 是否显示免费版时间提示弹窗
    };
  },
  mounted() {
    // 大云数智图标 → notice提示 → 销售金额排行 → 销售额
    this.debugA = false;
    this.debugB = false;
    this.debugC = false;
    this.debugD = false;
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    this.get_scrolltext();
    this.weidianPrint();
    // 检测断网
    window.addEventListener('offline', () => {
      this.SET_SHOW({ network: false });
    });
    window.addEventListener('online', () => {
      this.SET_SHOW({ network: true });
    });
    this.duoduoShow();
    if (!pos.network.isConnected()) {
      // 离线状态下不等待云同步结束直接判断是否显示微信绑定弹窗
      this.isFirstHomeCheck();
    }
    this.$nextTick(() => {
      this.getHelpQrCode();
      this.continuedUse();
    });
  },
  watch: {
    isOpenSMS(val) {
      if (val) {
        this.isShowSMSContract = true;
      }
    },
    screenWidth() {
      this.adHeight = this.screenWidth >= 1200 ? (this.screenWidth - 488) * (195 / 1432) + 'px' : (this.screenWidth - 378) * (195 / 1432) + 'px';
      this.eyeMarginTop = this.screenWidth > 1199 ? (this.screenWidth - 370) * 0.32 * 0.08 + 30 : (this.screenWidth - 260) * 0.32 * 0.08 + 30;
    },
    debugD() {
      if (this.debugA && this.debugB && this.debugC && this.debugD) {
        pos.chrome.debug();
        this.debugA = false;
        this.debugB = false;
        this.debugC = false;
        this.debugD = false;
      } else {
        this.debugA = false;
        this.debugB = false;
        this.debugC = false;
        this.debugD = false;
      }
    },
    homeF5() {
      if (this.homeF5) {
        this.get_setting();
        this.$refs.salesData.refresh();
        this.SET_SHOW({ homeF5: false });
      }
    },
    isSyncingLogin() {
      if (!this.isSyncingLogin) {
        this.isFirstHomeCheck();
      } else {
        // this.saveAdvertRecord();
      }
    },
    showSmfGuide(newVal) {
      if (!newVal) {
        this.$nextTick(() => {
          this.getHelpQrCode();
        });
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 查询用户试用版到期后是否可以继续使用
    async continuedUse() {
      const { code, data } = await continuedUse();
      if (code === 200) {
        this.remainDayTipsShow = this.trialDay <= 4 && !data.continueWork;
      }
    },
    sureSMS() {
      this.isShowSMSContract = false;
    },
    closeSMS() {
      this.isShowSMSContract = false;
      // 关闭会员设置中所有的短信通知
    },
    duoduoShow() {
      let OtherOptions = demo.t2json($config.Base.OtherOptions);
      if (OtherOptions && OtherOptions.qunKeDuo) {
        let qunKeDuo = OtherOptions.qunKeDuo;
        this.duoduo = qunKeDuo.duoduo && qunKeDuo.duoduo.isOn === 1;
        this.oneKeyOpenGroup = qunKeDuo.oneKeyOpenGroup && qunKeDuo.oneKeyOpenGroup.isOn === 1;
      }
    },
    selectEyeClose() {
      this.SET_SHOW({ eyeClose: !this.eyeClose });
      settingService.put([{ key: 'eye', value: this.eyeClose, remark: '是否隐藏首页销售额利润' }], () => {
        // todo
      });
      this.$_actionLog(logList.homeReportChange, `预估利润${this.eyeClose ? '隐藏' : '显示'}`);
    },
    weidianPrint() {
      if (this.hasWd === 1 && pos.network.isConnected() && demo.$store.state.show.setting_small_printer) {
        pos.pollPrint.ticketFunc(this.sys_uid, this.sys_sid);
      }
    },
    /**
     * 跳转到报表/交接班页面
     */
    goPage(value) {
      if (value === 'table') {
        // 先判断是否有查看报表的权限
        if (!this.$employeeAuth('show_shift_records')) {
          demo.msg('warning', this.$msg.not_employee_permissions);
          return;
        }
        this.SET_SHOW({ isReportForms: true });
        this.SET_SHOW({ isHome: false });
      }
      if (value === 'change_shifts') {
        // 先判断是否连网，未连网的话(无论是否有权限)直接断网提示退出，连网了再判断是否有权限，没权限直接弹窗提示退出，有权限的话跳转到交接班页面
        // 判断连网
        if (!pos.network.isConnected() || this.$store.state.show.network === false) {
          this.showOfflineDialog = true;
          return;
        }

        if (this.ultimate === null) {
          this.SET_SHOW({ isBuySoftware: true });
          return;
        }
        // 判断交接班权限
        if (!this.$employeeAuth('show_details')) {
          this.showPermissionDialog = true;
          return;
        }
        if ($setting.userlastime) {
          this.SET_SHOW({ loginTime: $setting.userlastime });
        }
        console.log(this.$store.state.show.loginInfo, 'this.$store.state.show.loginInfo');
        this.SET_SHOW({ changeUid: this.$store.state.show.loginInfo.uid });
        this.SET_SHOW({ endDate: new Date().format('yyyy.MM.dd hh:mm:ss'), isNewChange: true });
        this.SET_SHOW({ isHome: false, changeId: this.nowChangeId });
        this.SET_SHOW({ changeFrom: 'home' });
        this.SET_SHOW({ isRepair: true });
        this.SET_SHOW({ changeNumber: this.$store.state.show.loginInfo.employeeNumber });
        this.SET_SHOW({ changeName: this.$store.state.show.loginInfo.name });
        this.SET_SHOW({ isChangeShifts: true });
      }
    },
    /**
     * 断网退出/无交接班权限退出
     */
    confirmLogout() {
      if (this.gologining === true) {
        return;
      }
      this.gologining = true;
      setTimeout(() => {
        this.gologining = false;
      }, 3000);
      this.showOfflineDialog = false;
      this.showPermissionDialog = false;
      this.SET_SHOW({ changeId: this.nowChangeId });
      this.getChangeShiftsData('login');
    },
    store() {
      if (this.click_store == true) {
        return;
      }
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，微店功能暂时无法使用');
        return;
      }
      this.click_store = true;
      var that = this;
      setTimeout(function () {
        that.click_store = false;
      }, 5000);
      if (this.hasWd === 1) {
        var data = {
          sys_uid: this.sys_uid.toString(),
          sys_sid: this.sys_sid,
          accesstoken: this.token.split(' ')[1],
          printname: this.setting_small_printer
        };
        external.switchwindow(data, pos.printer.wdPrintPOS);
      } else {
        this.show_wd = true;
      }
    },
    get_scrolltext() {
      let news = external.getAd_news();
      if (news && news.length > 0) {
        news = demo.t2json(news);
        this.scroll_text = news.map(g => g.message).join(' ');
      }
    },
    // 获取设置中的信息，表头手机号为设置中的店铺名
    get_setting() {
      var _this = this;
      var data = { id: 1 };
      storeInfoService.get(data, function (res) {
        var json = demo.t2json(res);
        _this.SET_SHOW({ username: json[0].name, storeList: json });
      });
    },
    goods() {
      if (
        this.$employeeAuth('create_products') ||
        this.$employeeAuth('import_products') ||
        this.$employeeAuth('delete_products') ||
        this.$employeeAuth('products_curstock_inventories')
      ) {
        this.SET_SHOW({ isHome: false });
        this.SET_SHOW({ isGoods: true });
        this.SET_SHOW({ goodsHomeStock: true });
      } else {
        demo.msg('warning', this.$msg.not_employee_permissions);
      }
    },
    employee() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，员工功能暂时无法使用');
        return;
      }
      if (this.ultimate === null) {
        this.SET_SHOW({ isBuySoftware: true });
        return;
      }
      if (!this.$employeeAuth('create_employee') && !this.$employeeAuth('edit_employee')) {
        demo.msg('warning', this.$msg.not_employee_permissions);
        return;
      }
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isEmployee: true });
    },
    detail() {
      this.SET_SHOW({ pc_detail_tab: 4 });
      this.SET_SHOW({ fromReport: 0 });
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isReport: true });
    },
    stock() {
      if (this.ultimate === null) {
        this.SET_SHOW({ isBuySoftware: true });
        return;
      }
      if (!this.$employeeAuth('purchase') && !this.$employeeAuth('return_purchase')) {
        demo.msg('warning', this.$msg.not_employee_permissions);
        return;
      }
      if (!this.$employeeAuth('purchase')) {
        this.SET_SHOW({ buyOrder: false });
      }
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isStock: true });
    },
    report() {
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isReport: true });
    },
    judgeMember() {
      let flag =
        (this.$employeeAuth('vips_auth') ||
          this.$employeeAuth('create_vips') ||
          this.$employeeAuth('edit_vips') ||
          this.$employeeAuth('cancel_vips') ||
          this.$employeeAuth('exchange_vip_points') ||
          this.$employeeAuth('recharge_give_money') ||
          this.$employeeAuth('recharge_give_money_revoke')) &&
        this.ultimate !== null;
      return flag;
    },
    member() {
      if (this.ultimate === null) {
        this.SET_SHOW({ isBuySoftware: true });
        return;
      }
      if (!this.judgeMember()) {
        demo.msg('warning', this.$msg.not_employee_permissions);
        return;
      }
      // 联网
      if (pos.network.isConnected()) {
        this.SET_SHOW({ isHome: false });
        this.SET_SHOW({ isMember: true });
      } else {
        demo.msg('warning', '本地网络处于离线状态，会员功能暂时无法使用');
      }
    },
    pay() {
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isPay: true });
      this.SET_SHOW({ pc_return_goods: false });
    },
    appletDrainage() {
      this.showQkd = true;
      if (pos.network.isConnected()) {
        this.oneKeyOpenGroupUrl = demo.t2json($config.Base.OtherOptions).qunKeDuo.oneKeyOpenGroup.url + '?' + new Date().getTime();
        let description = '群客多小程序引流页点击';
        demo.actionLog({ page: 'home', action: 'appletDrainage', description });
      } else {
        this.oneKeyOpenGroupUrl = require('../../image/QKD/one_key_bg.jpg');
      }
    },
    getPromotionInfo() { // 获取品牌推广消息条数
      if (pos.network.isConnected()) {
        demo.$http
          .get(this.$rest.promotionInfo)
          .then(res => {
            if (res.data.code === 200 && res.data.data) {
              // 离线取上一次查询到的名称
              this.promotionBtnName = res.data.data.buttonName;
              this.showPromotion = res.data.data.isOn == 1;
              this.promotionBtnIndex = res.data.data.index;
              // 缓存品牌推广设置
              this.setPromotionSetting({
                promotionBtnName: this.promotionBtnName,
                showPromotion: this.showPromotion,
                promotionBtnIndex: this.promotionBtnIndex
              });
            }
          })
          .catch(err => {
            CefSharp.PostMessage(`获取品牌推广消息条数失败,Error:${err}`);
          });
      } else {
        this.getPromotionSetting();
      }
      this.getPromotionCount();
    },
    getPromotionSetting() {
      let settingInfo = $setting.info ? demo.t2json($setting.info) : {};
      this.promotionBtnName = settingInfo.promotionBtnName;
      this.showPromotion = settingInfo.showPromotion || false;
      this.promotionBtnIndex = settingInfo.promotionBtnIndex || 6;
    },
    setPromotionSetting(data) {
      let settingInfo = $setting.info ? demo.t2json($setting.info) : {};
      Object.assign(settingInfo, data);
      var data = [{ key: 'info', value: JSON.stringify(settingInfo), remark: '短信模板' }];
      settingService.put(data);
    },
    getPromotionCount() { // 获取品牌推广消息条数
      const dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      const param = {
        updatedAfterStr: dataInfo.updatedAfterStr || "1970-01-01 00:00:00.000"
      }
      demo.$http
        .get(this.$rest.promotionCount, {params: param})
        .then(res => {
          if (res.data.code === 200) {
            this.promotionCount = res.data.data;
          }
        })
        .catch(err => {
          CefSharp.PostMessage(`获取品牌推广消息条数失败,error:${err}`);
        });
    },
    getRecommendBg() {
      // 缓存最新点击时间
      this.setPromotionSetting({updatedAfterStr: new Date().format('yyyy-MM-dd hh:mm:ss.S')});
      // 记录actionLog
      let description = '抖音视频号引流按钮点击';
      demo.actionLog({ page: 'home', action: 'promotionDrainage', description });
      this.SET_SHOW({ isHome: false, isRepositoryNotice: true });
    },
    // 判断是否显示激活码临期提醒
    judgeTemporary() {
      let showTemporary = this.storeList[0].settings ? demo.t2json(this.storeList[0].settings).lastTemporaryIgnoreDate : '';
      this.SET_SHOW({ lastTemporaryIgnoreDate: showTemporary || '' });
      let date = new Date().format('yyyy-MM-dd');
      if (
        (this.lastTemporaryIgnoreDate === '' || this.lastTemporaryIgnoreDate !== date) &&
        $config.activationCode.period !== -1 &&
        this.trialDay < 21
      ) {
        this.SET_SHOW({ showTemporaryTip: true });
      }
    },
    closeTemporaryTip() {
      this.SET_SHOW({ showTemporaryTip: false });
    },
    closeSigningDialog() {
      this.SET_SHOW({ showSigningDialog: false });
    },
    closeCustomerServe() {
      this.SET_SHOW({ showCustomerServe: false });
    },
    // 拖动
    startMove(e) {
      this.moving = true;
      var that = this;
      document.onmousemove = function (e) {
        that.moveDiv(e);
      };
      document.onmouseup = function (e) {
        that.endMove(e);
      };
      document.ontouchmove = function (e) {
        that.moveDiv(e);
      };
      document.ontouchend = function (e) {
        that.endMove(e);
      };
    },
    moveDiv(e) {
      if (!this.moving) {
        return;
      }
      var min_left = window.innerWidth < 1200 ? 100 : 374;
      var moveX = this.customerServeLeft + e.movementX;
      this.customerServeLeft = moveX < min_left ? min_left : moveX > window.innerWidth - 170 ? window.innerWidth - 170 : moveX;
      var moveY = this.customerServeTop + e.movementY;
      this.customerServeTop = moveY < 0 ? 0 : moveY > window.innerHeight - 200 ? window.innerHeight - 200 : moveY;
    },
    endMove() {
      this.moving = false;
    },
    closeVxBind() {
      // 关闭首页绑定店铺营业营业微信通知
      this.showVxBind = false;
      let description = `关闭首页绑定店铺营业营业微信通知,${this.notTipAgain ? '' : '未'}勾选下次不再提醒`;
      demo.actionLog({ page: 'home', action: 'close', description });
      CefSharp.PostMessage(description);
      if (this.notTipAgain) {
        let settingInfo = $setting.info ? demo.t2json($setting.info) : {};
        settingInfo.notTipVxBindAgain = true;
        var data = [{ key: 'info', value: JSON.stringify(settingInfo), remark: '短信模板' }];
        settingService.put(data);
      }
    },
    async getHelpQrCode() {
      if (pos.network.isConnected()) {
        const param = {
          type: 'service'
        };
        const { weComUrl } = await getWxQrCode(param);
        if (weComUrl) {
          let qr_data = {
            text: weComUrl,
            width: 124,
            height: 124,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          };
          this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
          settingService.put({ key: 'serviceImgUrl', value: weComUrl }); // 缓存图片地址
        } else {
          this.getKefuCacheCodeImg();
        }
      } else {
        this.getKefuCacheCodeImg();
      }
    },
    async getKefuCacheCodeImg() {
      const kefuImgUrl = await getServiceImgUrl();
      if (!kefuImgUrl) {
        return;
      }
      let qr_data = {
        text: kefuImgUrl,
        width: 124,
        height: 124,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
    },
    isFirstHomeCheck() {
      // 登录进首页后检查
      if (this.isFirstHome) {
        let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
        if (!dataInfo.notTipVxBindAgain) {
          if (pos.network.isConnected()) {
            // 查询绑定状态，已解绑的不再显示
            let param = {
              sysUid: this.sys_uid,
              sysSid: this.sys_sid,
              systemName: $config.systemName,
              subName: $config.subName
            };
            demo.$http.post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmGetBindList, param).then(res => {
              if (res.data.code === 0) {
                if (res.data.data && res.data.data.length) {
                  // 1：已绑定 2:已解绑 都不再显示店铺微信通知弹窗
                  // 不显示绑定店铺微信通知
                  this.showVxBind = false;
                } else {
                  // 获取商家助手公众号二维码
                  this.createScanningCode();
                }
              }
            });
          } else {
            this.getCacheCodeImg();
          }
        }
        this.SET_SHOW({ isFirstHome: false });
      }
    },
    createScanningCode() {
      let clientId = md5(external.getMac());
      let param = {
        systemName: $config.systemName,
        subName: $config.subName,
        from: 2,
        clientId: clientId
      };
      if ($setting.useruid) {
        param['sysUid'] = $setting.useruid;
      }
      demo.$http
        .post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmCreateQrCode, param)
        .then(res => {
          console.log(res);
          if (res.data.code === 0 && res.data.data) {
            this.officialAccountCodeImgUrl = res.data.data.qrCodeUrl;
            this.showVxBind = true;
            setTimeout(() => {
              settingService.setInfoValueForKey('official_account_code_img_url', this.officialAccountCodeImgUrl); // 缓存图片地址
            }, 1000);
          } else {
            this.getCacheCodeImg();
          }
        })
        .catch(() => {
          this.getCacheCodeImg();
        });
    },
    getCacheCodeImg() {
      this.officialAccountCodeImgUrl = demo.t2json($setting.info).official_account_code_img_url || '';
      this.showVxBind = true;
    },
    todayIgnore() {
      this.SET_SHOW({ showTemporaryTip: false });
      let nowDate = new Date().format('yyyy-MM-dd');
      let store = _.cloneDeep(this.storeList);
      let dataInfo = store[0].settings ? demo.t2json(store[0].settings) : {};
      dataInfo.lastTemporaryIgnoreDate = nowDate;
      let settings = JSON.stringify(dataInfo);
      store[0].settings = settings;
      this.SET_SHOW({ lastTemporaryIgnoreDate: nowDate, storeList: store });
      storeInfoService.updateSettings(
        { id: 1, settings: settings },
        () => {},
        () => {}
      );
    },
    renewalSelf() {
      // 自助续费
      if (this.ultimate === null) {
        this.SET_SHOW({ onlyShowBuyChoose: false, isVesionCompare: true });
      } else {
        this.SET_SHOW({ onlyShowBuyChoose: true, isVesionCompare: true });
      }
      demo.actionLog(logList.renewalSelf);
    },
    goMicroShopAuth() {
      // 去签约
      this.SET_SHOW({ isHome: false, isSetting: true });
      setTimeout(() => {
        this.SET_SHOW({ selectRow: 12 });
      });
    }
  },
  created() {
    this.eyeMarginTop = this.screenWidth > 1199 ? (this.screenWidth - 370) * 0.32 * 0.08 + 30 : (this.screenWidth - 260) * 0.32 * 0.08 + 30;
    this.judgeTemporary();
    this.getPromotionInfo();
  },
  computed: {
    showSmfGuide() {
      // 是否显示开通扫码付引导页
      let smfGuideConfig = demo.t2json($config.Base.OtherOptions).smfGuide;
      this.SET_SHOW({ isHeader: !(this.autoLogin && smfGuideConfig && !this.appSecret && !this.isSyncingLogin) });
      return this.autoLogin && smfGuideConfig && !this.appSecret && !this.isSyncingLogin;
    },
    promotionTextFirst() {
      return this.promotionBtnName.substring(0, this.promotionBtnIndex > 6 ? 6 : this.promotionBtnIndex);
    },
    promotionTextSecond() {
      const startIndex = this.promotionBtnIndex > 6 ? 6 : this.promotionBtnIndex;
      const endIndex = startIndex + 6;
      return this.promotionBtnName.substring(startIndex, endIndex);
    },
    ...mapState({
      isHome: state => state.show.isHome,
      isGoods: state => state.show.isGoods,
      isDetail: state => state.show.isDetail,
      isQKD: state => state.show.isQKD,
      homeF5: state => state.show.homeF5,
      autoLogin: state => state.show.autoLogin,
      token: state => state.show.token,
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      phone: state => state.show.phone,
      showKefu: state => state.show.showKefu,
      isSyncingLogin: state => state.show.isSyncingLogin,
      isFirstHome: state => state.show.isFirstHome,
      setting_small_printer: state => state.show.setting_small_printer,
      storeList: state => state.show.storeList,
      isAuto: state => state.show.isAuto,
      showTemporaryTip: state => state.show.showTemporaryTip,
      showSigningDialog: state => state.show.showSigningDialog,
      lastTemporaryIgnoreDate: state => state.show.lastTemporaryIgnoreDate,
      hasWd: state => state.show.hasWd,
      useCardReader: state => state.show.useCardReader,
      remark_content: state => state.show.remark_content,
      isOpenSMS: state => state.show.isOpenSMS,
      ultimate: state => state.show.ultimate,
      screenWidth: state => state.show.screenWidth,
      eyeClose: state => state.show.eyeClose,
      period: state => state.show.period,
      trialDay: state => state.show.trialDay,
      appSecret: state => state.show.app_secret,
      noticeList: state => state.show.noticeList,
      nowChangeId: state => state.show.nowChangeId,
      showCustomerServe: state => state.show.showCustomerServe,
      estimatedProfit: state => state.show.estimatedProfit
    })
  }
};
</script>
