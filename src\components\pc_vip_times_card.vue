<style lang='less' scoped>
.com_psm1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  color: @themeFontColor;
  overflow: auto;
}
.row_style {
  padding-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #557485;
  line-height: 24px;
}
.com_psm11 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 120;
  top: 0;
  left: 0;
}
.com_psm12 {
  float: left;
  margin-left: 20px;
  line-height: 32px;
}
.com_psm13 {
  float: right;
  margin-right: 20px;
}
.com_psm14_1 {
  position: relative;
  width: 900px;
  background: #fff;
  border-radius: 6px;
  margin: 0 auto;
  margin-top: 30px;
  overflow: hidden;
  z-index: 300;
  font-size: 16px;
}
.com_psm14_2 {
  position: relative;
  width: 900px;
  background: #fff;
  border-radius: 6px;
  margin: 0 auto;
  margin-top: 15px;
  overflow: hidden;
  z-index: 300;
  font-size: 16px;
}
.com_psm15 {
  line-height: 50px;
  width: calc(100% - 40px);
  margin-left: 20px;
  border-bottom: 1px solid #e3e6eb;
  overflow: hidden;
}
.com_psm16 {
  float: left;
  font-weight: 700;
  text-indent: 10px;
  font-size: 18px;
}
.com_psm17 {
  float: right;
  font-size: 30px;
  line-height: 50px;
  color: #8298A6;
  cursor: pointer;
}
.com_psm18 {
  border: 1px solid #e5e8ec;
  margin-left: 20px;
  width: calc(100% - 40px);
  margin-top: 13px;
}
.com_psm18 .el-table th > .cell {
  padding-left: 10px;
}
.com_psm19 {
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 10px;
}

.ckBtn {
  min-width: 120px;
  padding: 0 10px;
  height: 44px;
  margin-left: 20px;
  line-height: 42px;
  margin-top: 18px;
  font-size: 18px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 20px;
}
.ckBtn1 {
  .ckBtn();
  float: left;
  color: @themeFontColor;
  background: #F5F7FA;
  border: 1px solid #E3E6EB;
  margin-top: 13px;
}
.ckBtn2 {
  .ckBtn();
  background: #FF5555;
  color: #FFFFFF;
  border: 1px solid @themeBackGroundColor;
  float: right;
  margin-top: 13px;
}
.ck_report_div {
  float: right;
  margin-top: 12px;
  margin-right: 20px;
  line-height: 40px;
  a {
    color: #CFA26B;
    text-decoration: none;
    font-family: Microsoft YaHei, sans-serif;
    font-size: 18px;
    font-weight: normal;
  }
}
.pc_del31 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_del32 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
  font-weight: normal;
}
.pc_del33 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background-color: #CFA26B;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-weight: normal;
}
.com_psm20 {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  cursor: pointer;
  float: left;
  margin: 25px 20px 0px 20px;
}
.com_ckrep15 {
  line-height: 50px;
  width: calc(100% - 40px);
  margin-left: 20px;
  border-bottom: 1px solid #e3e6eb;
  overflow: hidden;
}
.com_ckrep16 {
  float: left;
  font-weight: bold;
  text-indent: 10px;
  font-size: 18px;
}
.com_ckrep17 {
  float: right;
  font-size: 30px;
  line-height: 50px;
  color: #8298A6;
}
.com_ckrep18 {
  border: 1px solid #e5e8ec;
  margin-left: 20px;
  width: calc(100% - 40px);
  margin-top: 21px;
}
.com_ckrep18 .el-table th > .cell {
  padding-left: 10px;
}
.com_ckrep18 /deep/.el-table__row {
  height: 54px;
}
.com_ckrep18 /deep/.el-table__column-filter-trigger {
  margin-left: 20px;
}
/deep/.el-table-filter {
  margin-left: 120px !important;
}
.com_ckrep19 {
  width: 180px;
  height: 44px;
  border-radius: 4px;
  background: #F5F7FA;
  border: 1px solid #E3E6EB;
  float: right;
  font-size: 18px;
  line-height: 40px;
  text-align: center;
  color: @themeFontColor;
  font-weight: normal;
  font-family: Microsoft YaHei, sans-serif;
  margin-top: 15px;
  margin-right: 25px;
}
.card_dropdown{
  max-height:250px;
  overflow: auto;
}
.card_dropdown::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #F5F5F5;
}
.card_dropdown::-webkit-scrollbar-track {
  //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}
#closeAddCks {
  width: 120px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  float: left;
  border-radius: 4px;
  margin-left: 160px;
  cursor: pointer;
  text-align: center;
  line-height: 42px;
  color: @themeBackGroundColor;
  font-size: 16px;
}
#permitCardPrint {
  width: 120px;
  height: 44px;
  color: #FFF;
  float: left;
  margin-left: 20px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  line-height: 44px;
  font-size: 16px;
}
</style>
<template>
  <!--设置会员积分商品弹出框-->
  <div v-show="showTimesCard" class="com_psm1">
    <div class="com_psm11"></div>
    <div :class="showTimesCardTab ? 'com_psm14_2' : 'com_psm14_1'">
      <div class="com_psm15">
        <div class="com_psm16">
          <span v-show="!showTimesCardTab">持有次卡</span>
          <span v-show="showTimesCardTab">购买次卡</span>
        </div>
        <div
          class="com_psm17"
          @click="closePopup()"
        >×</div>
      </div>
      <el-input
        v-show="showTimesCardTab"
        placeholder="输入次卡名称搜索次卡"
        style="width: calc(100% - 40px);margin-left: 20px;margin-top: 13px;"
        v-model="keyword"
        clearable
      ></el-input>
      <div class="com_psm18">
        <el-table
          :data="ck_list"
          stripe
          v-loading="dataLoading"
          :empty-text="!dataLoading ? '暂无数据' : ' '"
          :header-cell-style="tableHeaderColor"
          :cell-style="tableRowStyle"
          style="width: 100%;height: 432px;font-size: 16px;overflow: auto"
          @selection-change="getSelectionGoods"
          ref="eltableCurrentRow"
          @row-click = "btnRow"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="selectable"
          >
          </el-table-column>
          <el-table-column prop="name" show-overflow-tooltip :key="1" width="230" label="次卡名称"></el-table-column>
          <el-table-column
            prop="startDate"
            show-overflow-tooltip
            v-if="!showTimesCardTab"
            :key="2"
            width="170"
            label="购买日期"
            align="left"
          ></el-table-column>
          <el-table-column
            prop="endDate"
            show-overflow-tooltip
            v-if="!showTimesCardTab"
            :key="3"
            width="110"
            label="截止日期"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            v-if="!showTimesCardTab"
            :key="4"
            width="100"
            label="购买金额"
          >
            <template slot-scope="scope">
              <div>{{scope.row.price.toFixed(2)}}</div>
            </template>
          </el-table-column>
          <el-table-column
            prop="totalTimes"
            show-overflow-tooltip
            v-if="!showTimesCardTab"
            :key="5"
            width="90"
            label="总次数"
          ></el-table-column>
          <el-table-column
            prop="surplusTimes"
            show-overflow-tooltip
            v-if="!showTimesCardTab"
            :key="6"
            width="100"
            label="剩余次数"
          ></el-table-column>
          <el-table-column
            prop="restTimes"
            show-overflow-tooltip
            v-if="showTimesCardTab"
            :key="7"
            width="150"
            label="使用次数"
          ></el-table-column>
          <el-table-column
            prop="period"
            show-overflow-tooltip
            v-if="showTimesCardTab"
            :key="3"
            width="300"
            label="有效期间"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            v-if="showTimesCardTab"
            :key="8"
            label="售价">
            <template slot-scope="scope">
              <div style="color: #FF5656;">¥{{scope.row.price.toFixed(2)}}</div>
            </template>
          </el-table-column>
        </el-table>
        <div class="com_psm19">
          <div class="com_psm12" v-show="!showTimesCardTab">
            已选中
            <span style="color: #FF5656;">{{selectTableData.length}}</span> 种次卡
          </div>
          <div class="com_psm12" v-show="showTimesCardTab">
            共
            <span style="color: #FF5656;">{{total}}</span> 种次卡
          </div>
          <div class="com_psm13">
            <el-pagination
              layout="prev, pager, next"
              :total="total"
              @current-change="handleCurrentChange"
              :current-page.sync="pagenum"
              :page-size="limit"
              :page-count="total"
            ></el-pagination>
          </div>
        </div>
      </div>
      <div style="overflow: hidden;">
        <!-- <div
          v-show="!showTimesCardTab"
          @click="beforeDelCk"
          class="ckBtn1"
        >删除</div> -->
        <div
          v-show="!showTimesCardTab"
          @click="addCkShow()"
          class="ckBtn2"
          style="border-color: #FF5656;"
        >消费</div>
        <!-- <div
          v-show="!showTimesCardTab"
          @click="showckUseReport = true"
          class="ck_report_div">
          <a href="#">查看消费记录></a>
        </div> -->
        <div v-show="showTimesCardTab">
          <div @click="changePermitCardPrint" class="com_psm20">
            <img v-show="!permitCardPrint" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
            <img v-show="permitCardPrint" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
            <div style="display: inline;margin-left: 15px;">支付完成打印小票</div>
          </div>
          <span
          class="ckBtn2"
          style="border-color: #FF5656;"
            @click="ckBuy()"
          >合计：¥{{jine}}</span>
        </div>
      </div>
      <div style="height: 13px;"></div>
    </div>
    <!-- 删除次卡确认框 -->
    <div
      v-show="showDelBox"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="showDelBox = false"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 300px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: normal;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 400;"
        >删除所持次卡后无法恢复，<br/>是否删除？</div>
        <div class="pc_del31">
          <div class="pc_del32" @click="showDelBox = false">取消</div>
          <div class="pc_del33" @click="delCk();showDelBox = false">确定删除</div>
        </div>
      </div>
    </div>
    <!-- 撤销次卡使用记录确认框 -->
    <div
      v-show="showRepealBox"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
        @click="showRepealBox = false"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 300px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: normal;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 400;"
        >撤销次卡消费记录后无法恢复，<br/>是否撤销？</div>
        <div class="pc_del31">
          <div class="pc_del32" @click="showRepealBox = false">取消</div>
          <div class="pc_del33" @click="backoutLastUse();showRepealBox = false">确定撤销</div>
        </div>
      </div>
    </div>
    <!-- 次卡消费记录 -->
    <div
      v-show="showckUseReport"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400">
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.7);"
        @click="showckUseReport = false">
      </div>
      <div
        style="position: relative;z-index: 800;height: 627px;width: 660px;background: #ffffff;overflow: hidden;border-radius: 6px;margin: 0 auto"
        :style="{'margin-top' : selfAdpationTop}">
        <div class="com_ckrep15">
          <div class="com_ckrep16">
            <span>次卡消费记录</span>
          </div>
          <div
            class="com_ckrep17"
            @click="showckUseReport = false"
          >×</div>
        </div>
        <div class="com_ckrep18">
          <el-table
            :data="ckReportList"
            stripe
            :header-cell-style="tableHeaderColor"
            ref="ckReportTable"
            style="width: 100%;height: 426px;font-size: 16px;overflow: hidden "
          >
            <el-table-column
              show-overflow-tooltip
              width="360"
              prop="cardname"
              label="次卡名称">
              <template slot="header" slot-scope="scope">
                <span>次卡名称</span>
                <el-dropdown
                  trigger="click"
                  :key="scope.$index"
                  placement="bottom-start"
                  v-model="timeCardId"
                  @command="filterByCardName">
                  <i class="el-icon-s-operation el-dropdown-link"></i>
                  <el-dropdown-menu
                    class="card_dropdown"
                    slot="dropdown">
                    <el-dropdown-item
                      v-for="item in filterListArr"
                      :key="item.id"
                      :value="item.id"
                      :command="item"
                      :style="item.id === timeCardId ? 'color: #d5aa76' : ''">
                      {{item.name}}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
            <el-table-column
              prop="createtime"
              show-overflow-tooltip
              width="130"
              label="使用时间"
              :formatter="timeStampToDate"
            ></el-table-column>
            <el-table-column
              prop="resttimes"
              show-overflow-tooltip
              align="center"
              width="126"
              label="剩余次数"
            ></el-table-column>
          </el-table>
          <div class="com_psm19">
            <div class="com_psm13">
              <el-pagination
                layout="prev, pager, next"
                :total="useTotal"
                :current-page.sync="useReportPageNum"
                @current-change="getckUseReport('1')"
                :page-size="useReportLimit"
                :page-count="ckReportList.length"
              ></el-pagination>
            </div>
          </div>
        </div>
        <div
          class="com_ckrep19"
          :disabled="checkLast === 0"
          :style="checkLast === 0 ? '' : 'opacity : 0.5'"
          @click="checkLast === 0 ? showRepealBox = true : repeatRepealMsg();">
          撤销最后一次使用
        </div>
      </div>
    </div>
    <!-- 消费次卡弹出框 -->
    <div
      v-show="showMember_add_cks"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="closeAddCks()"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 275px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 440px;overflow: hidden;border-radius: 5px;"
      >
        <div class="com_psm15">
          <div class="com_psm16" style="font-size: 16px;">
            消费提示: <span style="color: #FF5656;">{{selectTableData.length}}</span>张次卡
          </div>
          <div
            class="com_psm17"
            @click="closeAddCks()"
          >×</div>
        </div>
        <div
          style="width: 100%;
            font-size: 16px;
            color: #567485;
            margin-left: 30px;"
        >
          <div class="row_style" style="margin-top: 20px;">
            次卡消费次数
            <el-input-number
              ref="addCkRef"
              v-model="add_ck"
              :min="1"
              :max="surplusTimesUsable"
              @focus="$inputFocusSelectValue($event)"
              @input.native="addCkHandleInput()"
              @blur="setNum()"
              placeholder="请输入次卡消费次数"
              style="margin-left: 27px; width: 262px;"
            ></el-input-number>
          </div>
        </div>
        <div @click="changePermitCardPrint" class="com_psm20" style="margin-left: 23px">
          <img alt="" v-show="!permitCardPrint" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
          <img alt="" v-show="permitCardPrint" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
          <div style="display: inline;margin-left: 15px;">支付完成打印小票</div>
        </div>
        <div style="overflow: hidden;margin-top: 30px;font-size: 24px;float: left;">
          <div
            id="closeAddCks"
            @click="closeAddCks()"
          >取消</div>
          <div
            id="permitCardPrint"
            :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
            @click="save_btn_disabled ? saveAddGoods() : false"
          >确定</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {
      dataLoading: false,
      keyword: '',
      save_btn_disabled: true,
      ck_list_all: [],
      ck_list: [],
      row: 0,
      total: 0,
      limit: 7,
      pagenum: 1,
      showMember_add_cks: false,
      surplusTimesUsable: 1, // 单次次卡消费最大可选次数
      selectTableData: [],
      add_ck: '',
      jine: 0.00,
      from: 'pc_vip_times_card',
      showDelBox: false,
      showckUseReport: false,
      selfAdpationTop: '',
      ckReportList: [],
      useReportLimit: 7,
      useReportPageNum: 1,
      isBackout: false,
      filterListArr: [],
      timeCardId: '',
      useTotal: 0,
      showRepealBox: false,
      checkLast: 0,
      repeatClick: false
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    setNum() {
      if (this.add_ck === '' || Number(this.add_ck) === 0 || this.add_ck === undefined) {
        this.add_ck = 1;
      }
    },
    addCkHandleInput() { // 对应input原生事件缺失的处理
      let val = this.$refs['addCkRef'].userInput;
      this.add_ck = this.$intMaxMinLimit({data: val, max: this.surplusTimesUsable, min: 0});
      this.$refs['addCkRef'].userInput = this.add_ck;
    },
    selectable (row) {
      if (!this.showTimesCardTab) {
        return row.isDeleted === 0;
      } else {
        return true;
      }
    },
    btnRow (row) {
      if (row.isDeleted === 0 || this.showTimesCardTab) {
        row.flag = !row.flag;
        this.$refs.eltableCurrentRow.toggleRowSelection(row, row.flag);
      }
    },
    tableHeaderColor () {
      return 'background: #F9FBFB;';
    },
    tableRowStyle({ row, column }) {
      if (row.isDeleted === 1 && !this.showTimesCardTab) {
        return 'color: #B2C3CD;';
      }
      if (column.property === 'surplusTimes') {
        return 'color: #FF5555;';
      }
    },
    getSelectionGoods(res) {
      this.selectTableData = res;
      if (this.showTimesCardTab) {
        this.jine = 0;
        res.map(e => {
          this.jine += e.price;
        });
        this.jine = this.jine.toFixed(2);
      }
    },
    ckBuy() {
      if (this.selectTableData.length === 0) {
        demo.msg('warning', '请至少选择一张次卡');
      } else {
        var sub_data = {
          phone: this.sysUid,
          sysSid: this.sysSid,
          systemName: $config.systemName,
          vipId: this.vipId,
          type: 2,
          acctId: 1,
          money: this.jine,
          createUser: this.loginInfo.uid,
          originId: 5,
          timesIds: [...this.selectTableData.map(e => {
            return e.id;
          })],
          storeName: this.username
        };
        this.SET_SHOW({
          finalPayParam: {
            from: 'vip_times_card',
            buy_back: 1,
            pc_return_goods: '',
            showFinalPrice: this.jine,
            showMember: true,
            member_id: this.vipId,
            // 默认选择会员支付选项卡
            acctsId: 8,
            ifautoCash: false,
            ifpay: false,
            member_money: '',
            member_password: '',
            left_goods_list: this.selectTableData,
            totalPrice: this.jine,
            rightKeyword: '',
            vip_times_card: sub_data,
            preDisc: 1,
            operater: this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员',
            purchase: [],
            createtime: '',
            member_name: '会员名',
            member_value: '',
            member_money_name: '余额',
            member_money_value: '',
            member_mobile_name: '会员手机号',
            member_mobile_value: '',
            member_point_name: '积分',
            member_point_value: 0,
            storename: this.username
          }
        });
        demo.screen2({'screen2ReturnGoods': false}, 101);
        this.SET_SHOW({settlement: true});
      }
    },
    addCkShow() {
      if (this.selectTableData.length === 0) {
        demo.msg('warning', '请至少选择一张次卡');
      } else {
        // for (let i = 0; i < this.selectTableData.length; i++) {
        //   let result = this.selectTableData.some(item => {
        //     if (item.isDeleted === 1) {
        //       return true;
        //     }
        //   });
        //   if (result) {
        //     demo.msg('warning', '所选次卡包含不可用次卡，请重新选择');
        //     return;
        //   }
        // }
        this.add_ck = null;
        // 次卡单次最大可消费次数
        console.log(this.selectTableData);
        let minCk = _.minBy(this.selectTableData, ck => {
          return ck.surplusTimes === '-' ? 99999 : Number(ck.surplusTimes);
        });
        this.surplusTimesUsable =
          minCk.surplusTimes === '-' || isNaN(minCk.surplusTimes) ? 99999 : Number(minCk.surplusTimes);
        this.showMember_add_cks = true;
      }
    },
    beforeDelCk() {
      if (this.selectTableData.length === 0) {
        demo.msg('warning', '请至少选择一张次卡');
      } else {
        this.showDelBox = true;
      }
    },
    /**
     * 设置兑换礼品添加窗口保存
     */
    saveAddGoods() {
      console.log(this.add_ck, 'this.add_ck111');
      let changeTimes = _.clone(this.add_ck);
      let that = this;
      if (this.selectTableData.length === 0) {
        demo.msg('warning', '请至少选择一张次卡');
        this.closeAddCks();
        return;
      }
      this.save_btn_disabled = false;
      const printUseCards = this.selectTableData;
      let cards = this.selectTableData.map(e => {
        return {timesId: e.timesId, purchaseId: e.purchaseId};
      });
      demo.$http
        .post(
          this.$rest.pc_addTimesCardSales,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            subName: $config.subName,
            vipId: this.vipId,
            times: this.add_ck,
            cards: cards,
            storeName: this.username
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          console.log(res, 'res');
          if (res.data.code === '0') {
            if (res.data.data.length === 0) {
              that.closeAddCks();
              demo.msg('success', '消费成功');
              that.ckSearch(printUseCards, changeTimes);
            } else {
              demo.msg('warning', '次卡剩余次数不足');
              that.add_ck = 1;
            }
          } else {
            demo.msg('warning', '获取云端数据失败，请重试');
          }
          setTimeout(() => {
            that.save_btn_disabled = true;
          }, 1500);
        }).catch(error => {
          console.log(error);
          setTimeout(() => {
            that.save_btn_disabled = true;
          }, 1500);
        });
    },
    print(data, memberNewData, purchases, changeTimes) {
      console.log(changeTimes, 'changeTimes');
      let operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
      let conCards = data.map(e => {
        if (e.surplusTimes !== '-') {
          e.surplusTimes = Number(e.surplusTimes) - Number(changeTimes);
        }
        return e;
      });
      let printData = {
        conCards: conCards,
        memberNewData: memberNewData,
        operater: operater,
        createtime: new Date().format('yyyy-MM-dd hh:mm:ss'),
        createAt: new Date().format('yyyy-MM-dd hh:mm:ss'),
        storename: this.username,
        purchases: purchases,
        add_ck: changeTimes
      };
      printData.printNum = demo.$store.state.show.smallPrinterCopies;
      pos.printer.printPOSTimes(printData, this);
    },
    getMemberIntegral(flag, purchases, changeTimes) {
      var that = this;
      let memberNewData = {
        'member_name': '',
        'member_value': '',
        'member_money_name': '',
        'member_money_value': '',
        'member_mobile_name': '',
        'member_mobile_value': '',
        'member_point_name': '',
        'member_point_value': 0
      };
      var vipdata = {
        'systemName': $config.systemName,
        'phone': that.sysUid,
        'id': that.vipId
      };
      if (pos.network.isConnected()) {
        demo.$http.post(that.$rest.pc_getVipById, vipdata, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (rs) {
            memberNewData.member_name = '会员名';
            memberNewData.member_value = rs.data.data.name;
            memberNewData.member_money_name = '余额';
            memberNewData.member_money_value = Number(rs.data.data.hasMoney).toFixed(2);
            memberNewData.member_mobile_name = '会员手机号';
            memberNewData.member_mobile_value = rs.data.data.mobile.toString().substring(0, 3) + '****' + rs.data.data.mobile.toString().substring(7, 11);
            memberNewData.member_point_name = '积分';
            memberNewData.member_point_value = Number(rs.data.data.integral);
            that.print(flag, memberNewData, purchases, changeTimes);
          });
      } else {
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    closeAddCks() {
      this.add_ck = null;
      this.surplusTimesUsable = 1;
      this.showMember_add_cks = false;
    },
    /**
     * 最终关闭回调
     */
    closePopup() {
      this.keyword = '';
      this.ck_list_all = [];
      this.ck_list = [];
      this.total = 0;
      this.limit = 7;
      this.pagenum = 1;
      this.selectTableData = [];
      this.jine = 0.00;
      this.SET_SHOW({ showTimesCard: false });
    },
    // 前台分页
    handleCurrentChange(val) {
      this.ck_list = this.ck_list_all.slice(this.limit * (val - 1), this.limit * val);
    },
    /**
    * 初始化获取本地商品master数据
    */
    ckSearch(flag, changeTimes) {
      this.dataLoading = true;
      demo.$http
        .post(
          this.showTimesCardTab ? this.$rest.pc_getTimesCard : this.$rest.pc_getTimesCardPurchase,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            vipId: this.vipId,
            name: this.keyword,
            isShowOverdue: this.showTimesCardTab === false
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
          if (res.data.code === '0') {
            let ck_all = res.data.data;
            if (this.showTimesCardTab) {
              this.ck_list_all = ck_all;
            } else {
              this.ck_list_all = ck_all.map(e => {
                e.surplusTimes = e.restTimes;
                return e;
              });
            }
            this.ck_list = this.ck_list_all.slice(0, this.limit);
            this.total = this.ck_list_all.length;
            this.pagenum = 1;
            this.sonarPrint(flag, ck_all, changeTimes);
          } else {
            demo.msg('warning', '获取云端数据失败，请重试');
            setTimeout(() => {
              this.dataLoading = false;
            }, this.delayedTime);
          }
        }).catch(error => {
          console.log(error);
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
        });
    },
    sonarPrint(flag, ck_all, changeTimes) {
      let that = this;
      if (flag !== 0 && this.permitCardPrint) {
        // 打印小票
        if (this.settingSmallPrinter !== undefined && this.settingSmallPrinter !== null && this.settingSmallPrinter.trim() !== '') {
          this.pritnOeder(ck_all, flag, changeTimes);
        } else {
          setTimeout(function() {
            demo.msg('warning', that.$msg.not_setting_small_printer);
          }, 3000);
        }
      }
    },
    pritnOeder(ck_all, flag, changeTimes) {
      // 打印小票
      let purchases = [];
      ck_all.map(e => {
        e.surplusTimes = e.restTimes;
        if (e.isDeleted === 0) {
          if (e.endDate === '永久') {
            purchases.push(e.name + '，' + (e.surplusTimes !== '-' ? '剩余' + e.surplusTimes + '次' : '不限次数') + '，有效时间' + e.endDate);
          } else {
            purchases.push(e.name + '，' + (e.surplusTimes !== '-' ? '剩余' + e.surplusTimes + '次' : '不限次数') + '，有效时间' + e.startDate + '至' + e.endDate);
          }
        }
      });
      this.getMemberIntegral(flag, purchases, changeTimes);
    },
    /**
     * 支付完成后是否打印小票check回调
     */
    changePermitCardPrint() {
      var data = [{ key: settingService.key.print_card_small_ticket_afterpay, value: !this.permitCardPrint }];
      settingService.put(data, res => {
        console.log(res);
        this.SET_SHOW({permit_card_print: !this.permitCardPrint});
      });
    },
    // 次卡消费记录弹窗自适应页面高度
    selfAdpationBox() {
      this.selfAdpationTop = (document.body.clientHeight - 653) / 2 + 'px';
    },
    timeStampToDate(row) {
      let date = new Date(row.createtime);
      let year = date.getFullYear() + '-';
      let month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      let day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
      return year + month + day;
    },
    delCk() {
      var that = this;
      let cards = this.selectTableData.map(e => {
        return {timesId: e.timesId, purchaseId: e.purchaseId};
      });
      let params = {
        systemName: $config.systemName,
        sysSid: this.sysSid,
        phone: this.sysUid,
        vipId: this.vipId,
        cards: cards,
        storeName: this.username
      };
      demo.$http
        .post(this.$rest.delUserTimesCardPurchase, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(res => {
          if (res.data.code === '0') {
            demo.msg('success', '删除次卡成功!');
            that.ckSearch(0);
          }
        })
        .catch(err => {
          demo.msg('error', err.msg);
        });
    },
    clearScreen2() { // 清空副屏信息
      let subDate = {
        'screen2ShowList': [],
        'screen2ShowFinalPrice': 0,
        'screen2ReducePrice': 0,
        'screen2ShowMember': false,
        'screen2MemberName': '',
        'screen2MemberPhone': '',
        'screen2MemberMoney': '',
        'screen2MemberPoint': ''
      };
      demo.screen2(subDate, 2);
    },
    getckUseReport(type) { // 获取次卡消费记录数据
      var that = this;
      var vipdetial = _.cloneDeep(this.memberDetail);
      let params = {
        systemName: $config.systemName,
        sysSid: this.sysSid,
        phone: this.sysUid,
        searchStr: vipdetial.mobile,
        from: '',
        to: '',
        pageNumber: this.useReportPageNum,
        pageSize: this.useReportLimit,
        timeCardId: this.timeCardId,
        useType: 0
      };
      demo.$http
        .post(this.$rest.timesCardUseDetails, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(res => {
          that.ckReportList = res.data.data.list;
          if (type === '0') { // 需要重新加载filterList
            that.reloadFilterListArr();
          }
          that.useTotal = res.data.data.total;
        })
        .catch(err => {
          demo.msg('error', err.data.msg);
        });
    },
    reloadFilterListArr() {
      var that = this;
      let params = {
        systemName: $config.systemName,
        sysSid: this.sysSid,
        phone: this.sysUid,
        vipId: this.vipId
      };
      demo.$http
        .post(this.$rest.getHavingCards, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(res => {
          if (res.data.code === '0') {
            that.filterListArr = res.data.data.list;
            that.checkLast = res.data.data.checkLast;
          }
        })
        .catch(err => {
          demo.msg('error', err.data.msg);
        });
    },
    backoutLastUse() { // 撤销最后一次使用
      var that = this;
      let params = {
        systemName: $config.systemName,
        sysSid: this.sysSid,
        phone: this.sysUid,
        vipId: this.vipId
      };
      demo.$http
        .post(this.$rest.revokeUserTimesCardSales, params, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(res => {
          if (res.data.code === '0') {
            that.isBackout = true; // 成功撤销
            demo.msg('success', '撤销最后一次使用次卡成功');
            this.useReportPageNum = 1;
            this.timeCardId = '';
            that.getckUseReport('0');
          } else { // 没有使用记录、撤销失败等
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(err => {
          demo.msg('error', err.msg);
        });
    },
    filterByCardName (item) {
      this.timeCardId = item.id;
      this.useReportPageNum = 1;
      this.getckUseReport('1');
    },
    repeatRepealMsg () {
      if (!this.repeatClick) {
        demo.msg('warning', '您已撤销最后一次使用，请勿重复撤销');
        this.repeatClick = setTimeout(() => {
          this.repeatClick = false;
        }, 3000);
      }
    },
    selfHeightListen() {
      if (this.onresTimer) {
        clearTimeout(this.onresTimer);
      }
      this.onresTimer = setTimeout(() => {
        this.selfAdpationBox();
      }, 300);
    }
  },
  mounted () {
    this.selfAdpationBox();
    // window.addEventListener('resize', this.selfAdpationBox(), false);
    window.addEventListener('resize', this.selfHeightListen);
  },
  watch: {
    showTimesCard() {
      if (this.showTimesCard) {
        this.ckSearch(0);
      } else {
        this.clearScreen2();
      }
    },
    keyword() {
      this.ckSearch(0);
    },
    showckUseReport() {
      if (this.showckUseReport) {
        this.timeCardId = '';
        this.useReportPageNum = 1;
        this.getckUseReport('0');
      } else {
        if (this.isBackout) { // 有撤销数据成功的操作
          // 查看
          this.ckSearch();
        }
      }
    }
  },
  computed: mapState({
    showTimesCard: (state) => state.show.showTimesCard,
    showTimesCardTab: (state) => state.show.showTimesCardTab,
    vipId: (state) => state.show.vipId,
    token: (state) => state.show.token,
    sysUid: (state) => state.show.sys_uid,
    username: state => state.show.username,
    sysSid: (state) => state.show.sys_sid,
    loginInfo: (state) => state.show.loginInfo,
    delayedTime: state => state.show.delayedTime,
    permitCardPrint: (state) => state.show.permit_card_print,
    settingSmallPrinter: state => state.show.setting_small_printer,
    memberDetail: state => state.show.member_detail
  }),
  destroyed() {
    window.removeEventListener('resize', this.selfHeightListen);
  }
};
</script>
