<template>
  <div>
    <v-AddEditSupplier></v-AddEditSupplier>
    <div class="container">
    <div v-show="showSettingGoodsSupplierDialog" class="supplier-dialog">
      <div class="supplier-dialog-container">
          <div class="com_pad43">
            <div class="dialog-title">选择供应商</div>
            <div class="close" @click="dismissDialog">×</div>
          </div>
          <div :class="['supplier-info-container', supplierDetail.id ? 'supplier-info-container-active' : '']">
              <div v-if="supplierDetail.id" class="supplier-info">
                <el-tooltip effect="dark" :content="supplierDetail.name" placement="top-start">
                  <div class="flex-2 top-show-name maxline-1">{{ supplierDetail.name }}</div>
                </el-tooltip>
                <el-tooltip effect="dark" :disabled="!supplierDetail.contacter" :content="supplierDetail.contacter" placement="top-start">
                  <div class="flex-1 maxline-1">{{ supplierDetail.contacter || '-'}}</div>
                </el-tooltip>
                <div class="flex-2">{{ supplierDetail.mobile || '-'}}</div>
                <div class="flex-1">启用</div>
                <div class="btn-cancel-supplier" @click="cancelSelect">取消选择</div>
              </div>
              <div v-else class="select-tips">请选择供应商</div>
          </div>
          <div class="supplier-table-container">
            <div class="table-top">
              <div class="table-top-left">
                <cj-input
                    ref="cjInputRef"
                    v-model="keyword"
                    height="40"
                    maxlength="60"
                    fontSize="15"
                    autofocus
                    focus-select
                    left-icon="el-icon-search"
                    clear-trigger="always"
                    placeholder="供应商名称/联系人/电话"
                    @input="getKeyWord"/>
              </div>
              <div class="table-top-right">
                <el-select
                  class="select-status"
                  v-model="statusVal"
                  placeholder="请选择"
                  @change="getStatus"
                  style="width:100px;margin-left:20px">
                    <el-option
                      v-for="item in statusList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                </el-select>
                <div v-if="$employeeAuth('supplier_management')" class="btn-create-supplier" @click="createSupplier">新建供应商</div>
              </div>
            </div>
            <div class="table-content">
              <el-table
                ref="multipleTable"
                :height="tableHeight"
                stripe
                :data="tableData"
                style="font-size: 16px;margin-top: -10px;color: #567485;width: 100%;"
                :style="$t('image.homeImage.tableFont')"
                tooltip-effect="dark">
                <el-table-column
                  prop="name"
                  min-width="40%"
                  show-overflow-tooltip
                  label="供应商名称">
                </el-table-column>
                <el-table-column
                  min-width="20%"
                  show-overflow-tooltip
                  label="联系人">
                  <template slot-scope="{row}">
                    {{ row.contacter || '-'}}
                  </template>
                </el-table-column>
                <el-table-column
                  min-width="25%"
                  show-overflow-tooltip
                  label="联系电话">
                  <template slot-scope="{row}">
                    {{ row.mobile || '-'}}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="isDeleted"
                  min-width="15%"
                  align="center"
                  label="状态">
                  <template slot-scope="{row}">
                    <span :class="[row.isDeleted === 1 ? 'written_off' : '']">{{ row.isDeleted === 1 ? '禁用' : '启用' }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fingerprint"
                  min-width="20%"
                  align="center"
                  label="操作">
                  <template slot-scope="scope">
                    <a v-if="$employeeAuth('supplier_management')" href="#" @click="chooseOneSupplier(scope.row)">
                      <span id="butColor">编辑</span>
                    </a>
                    <a href="#" @click="selectOneSupplier(scope.row)"  class="ml-10">
                      <span id="butColor" :class="[scope.row.id === supplierDetail.id || scope.row.isDeleted === 1 ? 'written_off' : '']">{{ scope.row.id === supplierDetail.id ? '已选' : '选择' }}</span>
                    </a>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="table-bottom">
              <div class="sum">共<span class="number">{{total}}</span>个供应商</div>
              <div v-if="total > 0">
                <el-pagination
                  layout="prev, pager, next"
                  :total="total"
                  @current-change="suppliersChange"
                  :current-page="pageNum"
                  :page-size="pageSize"
                />
              </div>
            </div>
          </div>
          <div class="com_pad34 btn-supplier-confirm" id="save" @click="confirm">确定</div>
          <div class="com_pad34" id="cancel" @click="dismissDialog">取消</div>
      </div>
      </div>
    </div>
    <confirm-dialog
      :visible.sync="visible"
      :message="`确定设置批量选中商品的供应<br/>商信息吗？`"
      @confirm="batchConfirm"
      @cancel="batchCancel"
      :closeOnClickModal="false"
    ></confirm-dialog>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import CjInput from '@/common/components/CjInput';
export default {
  components: {
    CjInput,
    ConfirmDialog
  },
  data() {
    return {
      visible: false, // 确定弹窗
      showSupplierDialog: false,
      supplierDetail: { // 已选择的供应商详情
        id: null,
        fingerprint: '',
        name: '',
        contacter: '',
        mobile: ''
      },
      keyword: '',
      statusVal: '0',
      statusList: [
        {
          id: '2',
          name: '全部'
        },
        {
          id: '0',
          name: '启用'
        },
        {
          id: '1',
          name: '禁用'
        }
      ],
      tableHeight: 345,
      tableData: [],
      pageNum: 1,
      pageSize: 20,
      total: 0
    }
  },
  created() {
    if (this.selectedSuppliersDetail.id) {
      this.supplierDetail = this.selectedSuppliersDetail;
    }
    this.getSupplieres();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 让弹窗消失
     */
    dismissDialog() {
      this.SET_SHOW({ showSettingGoodsSupplierDialog: false, showSupplierDialgEdit: false });
    },

    /**
     * 监听输入框值变化实时检索
     */
    getKeyWord(val) {
      this.keyword = val;
      this.pageNum = 1;
      this.getSupplieres();
    },

    /**
     * 状态下拉选择回调
     */
    getStatus(val) {
      this.statusVal = val;
      this.pageNum = 1;
      this.getSupplieres();
    },

    /**
     * 获取供应商列表数据
     */
    async getSupplieres() {
      const params = {
        'systemName': $config.systemName,
        'name': this.keyword.replace(/'/g, "'").replace(/;/g, '；'),
        'status': this.statusVal,
        'limit': this.pageSize,
        'offset': (this.pageNum - 1) * this.pageSize
      };
      this.tableData = await this.querySuppliers(params);
      this.total = await this.querySuppliersCount(params);
    },

    querySuppliers(params) {
      return new Promise(resolve => {
        supplierService.searchSuppliers(params, res => {
          resolve(demo.t2json(res));
        });
      });
    },

    querySuppliersCount(params) {
      return new Promise(resolve => {
        supplierService.getSupplierCount(params, res => {
          resolve(Number(demo.t2json(res)[0].cnt));
        });
      });
    },

    /**
     * 新增供应商
     */
    createSupplier() {
      this.SET_SHOW({ suppliersDetail: '', showAddEditSupplier: true });
    },

    /**
     * 编辑供应商
     */
    chooseOneSupplier(info) {
      this.SET_SHOW({ suppliersDetail: _.cloneDeep(info), showAddEditSupplier: true });
    },

    /**
     * 选择一个供应商
     * 已选中的/禁用的，禁止点击
     */
    selectOneSupplier(info) {
      if (info.id === this.supplierDetail.id || info.isDeleted === 1) {
        return;
      }
      this.supplierDetail = info;
    },

    /**
     * 取消选择供应商(清空)
     */
    cancelSelect() {
      this.supplierDetail = {
        id: null,
        fingerprint: '',
        name: '',
        contacter: '',
        mobile: ''
      };
    },

    /**
     * 点击分页
     */
    suppliersChange(val) {
      this.pageNum = val;
      this.getSupplieres();
    },
    // 批量设置供应商
    batchConfirm() {
      this.SET_SHOW({ showSupplierDialgEdit: false });
    },
    // 批量设置供应商取消
    batchCancel() {
      this.SET_SHOW({ selectedSuppliersDetail: '' });
    },
    /**
     * 点击确定
     */
    confirm() {
      this.SET_SHOW({ selectedSuppliersDetail: _.cloneDeep(this.supplierDetail) });
      if (this.showSupplierDialgEdit) {
        if (this.selectedSuppliersDetail.fingerprint) {
          this.visible = true;
        } else {
          this.dismissDialog();
        }
      } else {
        this.dismissDialog();
      }
    },

    /**
     * 先选择，后编辑的供应商信息刷新
     */
    updateSelectedSupplierInfo() {
      if (this.supplierDetail.fingerprint) {
        supplierService.getSupplierInfoByFingerprint({fingerprint: this.supplierDetail.fingerprint}, res => {
          if (res.isDeleted === 0) {
            this.supplierDetail = res;
          }
          if (res.isDeleted === 1) {
            const initObj = {
              id: null,
              fingerprint: '',
              name: '',
              contacter: '',
              mobile: ''
            };
            this.supplierDetail = initObj;
            this.SET_SHOW({ selectedSuppliersDetail: initObj });
          }
        });
      }
    }
  },
  watch: {
    async showAddEditSupplier(val) {
      if (!val) {
        await this.getSupplieres();
        this.updateSelectedSupplierInfo();
      }
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    showSettingGoodsSupplierDialog: state => state.show.showSettingGoodsSupplierDialog,
    showAddEditSupplier: state => state.show.showAddEditSupplier,
    selectedSuppliersDetail: state => state.show.selectedSuppliersDetail,
    showSupplierDialgEdit: state => state.show.showSupplierDialgEdit,
    supplierGoodsList: state => state.show.supplierGoodsList
  })
}
</script>

<style lang="less" scoped>
a {
  text-decoration: none;
}
.ml-10 {
  margin-left: 10px;
}
.written_off {
  color: #B2C3CD !important;
}
#butColor {
  color:@themeBackGroundColor;
}
/deep/ .el-table::before {
  background: none;
}
/deep/ .el-table th > .cell {
  height: 50px;
  line-height: 50px;
  background: #F5F7FA;
}
.select-status /deep/ .el-input__inner {
  height: 40px;
  font-size: 16px;
}

.select-status /deep/ .el-input__suffix {
  top: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: flex-start;
}
.container {
  color: #537286;
}
.com_pad43 {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
}
.dialog-title {
  font-size: 18px;
  font-weight: bold;
  line-height: 60px;
}
.close {
  font-size: 30px;
  color: #8298A6;
  cursor: pointer;
}
.com_pad34 {
  width: 120px;
  height: 44px;
  margin-top: 16px;
  margin-left: 10px;
  float: right;
  color: #fff;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  line-height: 42px;
  border-radius: 4px;
  cursor: pointer;
}
.supplier-dialog {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 200;
  background: rgba(0,0,0,.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.supplier-dialog-container {
  width: 720px;
  border-radius: 10px;
  z-index: 150;
  overflow: hidden;
  background: white;
  padding-bottom: 15px;
}

.supplier-info-container {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #E3E6EB;
  box-sizing: border-box;
  margin: 10px 20px;
  font-size: 16px;
}
.supplier-info-container-active {
  background: #F5F0E3;
  border: 1px solid #BDA169;
}
.supplier-info {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 20px;
}
.flex-2 {
  flex: 2;
}
.top-show-name {
  margin-right: 4px;
}
.maxline-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}
.flex-1 {
  flex: 1;
}
.btn-cancel-supplier {
  width: 80px;
  text-align: center;
  padding: 5px 0;
  color: white;
  background: #BDA169;
  border-radius: 4px;
  cursor: pointer;
}
.select-tips {
  opacity: .6;
}
.supplier-table-container {
  height: 440px;
  border-radius: 4px;
  border: 1px solid #E3E6EB;
  margin: 0 20px;
  display: flex;
  flex-direction: column;
}
.table-top {
  padding: 10px;
  display: flex;
  align-items: center;
}
.table-top-left {
  flex: 1;
}
.table-top-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.table-content{
  flex: 1;
}
.table-bottom {
  height: 44px;
  border-top: 1px solid #E3E6EB;
  display: flex;
  align-items: center;
}
.btn-supplier-confirm {
  margin-right: 20px !important;
}
.sum {
  flex: 1;
  font-size: 16px;
  margin-left: 15px;
}
.number {
  color: #BDA169;
  display: inline-block;
  margin: 0 3px;
}
.btn-create-supplier {
  height: 40px;
  line-height: 40px;
  background: #BDA169;
  color: white;
  padding: 0 20px;
  border-radius: 4px;
  margin-left: 20px;
  cursor: pointer;
  font-size: 16px;
}
</style>
