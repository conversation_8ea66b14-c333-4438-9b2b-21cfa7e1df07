import dao from '../dao/dao';
import md5 from 'js-md5';

const storeInfoService = {
  /**
   * 查询信息
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  get: function (data, onSuccess, onFail) {
    dao.exec(
      sqlApi.getStoreInfo.format(data),
      onSuccess,
      onFail
    );
  },

  /**
   * 更新
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  update: function (params, onSuccess, onFail) {
    params.fingerprint = md5(params.name.replace(/'/g, '‘'));
    if (params.is_deleted === undefined) {
      params.is_deleted = '0';
    }
    var params1 = _.cloneDeep(params);
    dao.exec(sqlApi.updateStoreInfo.format(demo.sqlConversion(params1)), onSuccess, onFail);
  },

  /**
   * 修改优惠折扣
   * storeInfoService.updateDiscountSettings({'id':1,
   *  'discountSettings':JSON.stringify({
   *    'discount':[{'number':'9.8'},{'number':'9.5'},{'number':'9'},{'number':'8.8'}],
   *    'reduce':[{'number':'0.5'},{'number':'1'},{'number':'5'},{'number':'10'}]
   * })});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateDiscountSettings: function(params, onSuccess, onFail) {
    dao.exec(sqlApi.updateDiscountSettings.format(demo.sqlConversion(params)), onSuccess, onFail);
  },

  /**
   * 修改settings
   * storeInfoService.updateSettings({'id':1, 'settings':JSON.stringify({autoPacking:1})});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateSettings: function(params, onSuccess, onFail) {
    dao.exec(sqlApi.updateStoreInfoSettings.format(demo.sqlConversion(params)), onSuccess, onFail);
  }
};
window.storeInfoService = storeInfoService;
export default storeInfoService;
