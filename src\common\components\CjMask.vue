<template>
  <div v-if="visible" class="maskWrap"
    :style="{ zIndex: index }" @click.stop="handleClick">
    <slot v-if="visible"></slot>
  </div>
</template>
<script>
let cjMaskIndex = 400;
export default {
  name: 'CjMask',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    zIndex: {
      type: Number,
      default: 400
    }
  },
  data() {
    return {
      index: 400
    };
  },
  mounted() {
    cjMaskIndex = this.zIndex === 400 ? cjMaskIndex + 1 : this.zIndex;
    this.index = cjMaskIndex;
  },
  methods: {
    // 点击事件
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>
<style lang="less" scoped>
.maskWrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
