import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

const router = new Router({
  routes: [
    {
      path: '*',
      name: '/',
      component: resolve => require(['../page/index/index.vue'], resolve)
    },
    {
      path: '/',
      name: 'home',
      component: resolve => require(['../page/index/index.vue'], resolve)
    },
    {
      path: '/scanPayIntro',
      name: 'scanPayIntro',
      component: resolve => require(['../page/index/scanPayIntro.vue'], resolve)
    }
  ]
});

router.beforeEach((to, from, next) => {
  next();
});

export default router;
