<style lang="less" scoped>
.shortcutFont {
  float: right;
  display: inline-block;
  margin-right: 10px;
  line-height: 36px;
  color: @themeBackGroundColor;
  font-size: 16px;
}
.shortcut {
  height: 32px;
  line-height: 32px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 8px;
  margin-left: 10px;
  color: @themeBackGroundColor;
  display: inline-block;
  padding: 0px 12px;
  .group-label {
    background-color: #fff;
    height: 100%;
  }
  .shortcutInfo {
    font-weight: bold;
    font-size: 14px;
  }
}

.el-select-dropdown {
  .el-popper[x-placement^=top] .popper__arrow {
    bottom: 0px;
    border-top-color: #FFF!important;
  }
  .el-popper[x-placement^=top] .popper__arrow::after {
    border-top-color: #FFF!important;
  }
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #3dbfff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 217px;
  height: 217px;
  line-height: 217px;
  text-align: center;
}
.avatar {
  width: 217px;
  height: 217px;
  display: block;
}
.el-textarea__inner:focus {
  border-color: @themeBackGroundColor;
}
.avatar-uploader .el-upload {
  border-color: @themeBackGroundColor;
}
.avatar-uploader .el-upload:hover {
  border-color: @themeBackGroundColor;
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .header_title{
    color: @fontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.idea_input{
  margin-top: 15px;
  margin-left: 30px;
  margin-right: 30px;
  background: #F5F8FB;
  /deep/.el-textarea__inner {
    border: 1px solid #F7F8F9;
  }
}
.desc{
  margin-top: 14px;
  margin-left: 60px;
  margin-right: 60px;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: @themeFontColor;
}
.btn_feedback{
  width: 390px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: @themeBackGroundColor;
  border-radius: 4px;
  color: white;
  font-weight: bold;
  margin: auto;
  margin-top: 30px;
  font-size: 16px;
  cursor: pointer;
}
/deep/ .el-textarea__inner {
  height: 210px;
  color: @themeFontColor;
  resize: none;
}
.logo {
  float: left;
}
.setting {
  float: right;
}
.el-loading-spinner .path {
  stroke: @themeBackGroundColor !important;
}
.pc_hed {
  width: 145px;
  background: linear-gradient(90deg, #f2a147, #fc6051);
  color: #fff;
  height: 32px;
  font-size: 16px;
  float: left;
  margin-top: -2px;
  border-radius: 5px;
  cursor: pointer;
}
.pc_hed img {
  width: 18px;
  height: 18px;
  margin-right: 5px;
  float: left;
  margin-top: 8px;
  margin-left: 27px;
}
.pc_hed div {
  line-height: 32px;
  float: left;
}
.pc_hed1 {
  width: 145px;
  background: linear-gradient(90deg, #69d6fe, #16aaff);
  color: #fff;
  height: 32px;
  font-size: 16px;
  float: left;
  margin-top: -2px;
  margin-left: 15px;
  border-radius: 5px;
  cursor: pointer;
}
.pc_hed1 img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  float: left;
  margin-top: 7px;
  margin-left: 27px;
}
.pc_hed1 div {
  line-height: 32px;
  float: left;
}
.pc_hed11 {
  text-align: center;
  height: 38px;
  background: @themeFontColor;
  margin-top: 5px;
  border-radius: 17px;
  cursor: pointer;
  .membersSet {
    margin-left: 10px;
    color: @themeBackGroundColor;
    font-weight: bold;
    font-size: 17px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    width: 70px;
  }
}
.pc_hed11 img {
  width: 20px;
  height: 20px;
  float: left;
  margin-top: 10px;
}
.pc_hed11 div {
  float: left;
  margin-left: 5px;
  font-size: 16px;
  color: #fff;
  line-height: 38px;
}
.pc_hed12 {
  right: 15px;
  position: relative;
}
.pc_hed12 div {
  float: left;
  margin-left: 20px;
}
.pc_hed_short {
  min-width: 204px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 22px;
  font-weight: bold;
  font-size: 14px;
  line-height: 41px;
  color: @themeBackGroundColor;
  text-align: center;
  display: inline-block;
  .pc_hed_short1 {
    width: 76px;
    height: 36px;
    background: @themeBackGroundColor;
    border-radius: 22px;
    cursor: pointer;
    display: inline-block;
    margin-top: 3px;
    color: #FFFFFF;
    line-height: 35px;
    margin-right: 4px;
    margin-left: 6px;
  }
}
.pc_hed_short2 {
  width: 112px;
  height: 44px;
  color: @themeBackGroundColor;
  font-size: 17px;
  line-height: 41px;
  text-align: center;
  border: 1px solid @themeBackGroundColor;
  border-radius: 22px;
  font-weight: bold;
  cursor: pointer;
}
.pc_hed13 {
  font-size: 16px;
  color: @themeBackGroundColor;
  top: 7px;
  position: relative;
  font-weight: 700;
  width: calc(100% - 120px);
  margin: 0 auto;
}
.pc_hed13 div {
  float: right;
}
.pc_hed13 img {
  float: left;
  margin-right: 8px;
  width: 24px;
  height: 24px;
  margin-top: 4px;
  border-radius: 50%;
  overflow: hidden;
}
.pc_hed14 {
  font-size: 16px;
  cursor: pointer;
  margin-left: 16px;
  color: @themeFontColor;
}
.pc_hed14 img {
  float: left;
  margin-top: 10px;
}
.pc_hed14 div {
  float: left;
  line-height: 50px;
  margin-left: 2px;
}
.pc_hed15 {
  color: @themeFontColor;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 1px;
  .pay_header_container {
    width: calc(100% - 841px);
    margin-left: 255px;
  }
  .pay_header {
    width: 220px;
    height: 36px;
    background-color: #F4EFE5;
    margin: 7px auto 0;
    border-radius: 18px;
    overflow: hidden;
  }
}
.pc_hed16 {
  margin-left: 30px;
  cursor: pointer;
}
.pc_hed17 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_hed18 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  background: @themeFontColor;
  color: #fff;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.pc_hed19 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  background: @linearBackgroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.stock-inven-remind__div {
  position: relative;z-index: 800;height: 300px;
  margin: 0 auto;margin-top: 240px;background: white;width: 450px;overflow: hidden;border-radius: 5px;
  &--title {
    width: 100%;text-align: center;font-size: 25px;
    color: @themeFontColor;margin-top: 40px;font-weight:bold
  }
  &--text {
    width: 100%;text-align: center;font-size: 25px;
    color: @themeFontColor;margin-top: 25px;font-weight: normal;
  }
}
.pc_hed2 {
  background:linear-gradient(90deg,rgba(209,185,138,1) 0%,rgba(180,153,90,1) 100%) !important;
  color: #FFF;border-color: rgba(180,153,90,1) !important;
}
.pc_hed21 {
  width: 140px;height: 38px;border: 1px solid #E3E6EB;background: #F5F8FB;
  float: left;text-align: center;line-height: 36px;cursor: pointer;
}
.pc_hed22 {
  width: 140px;height: 38px;border-top: 1px solid #E3E6EB;
  border-bottom: 1px solid #E3E6EB;background: #F5F8FB;float: left;
  text-align: center;line-height: 36px;cursor: pointer;
  border-top-left-radius: 6px;border-bottom-left-radius: 6px;
}
.pc_hed23 {
  width: 140px;height: 38px;border: 1px solid #E3E6EB;border-right: none;
  background: #F5F8FB;float: left;text-align: center;line-height: 36px;cursor: pointer;
}
.pc_hed24 {
  width: 140px;height: 38px;border-top: 1px solid #E3E6EB;border-bottom: 1px solid #E3E6EB;
  border-left: 1px solid #E3E6EB;background: #F5F8FB;float: left;text-align: center;
  line-height: 36px;cursor: pointer;
}
.pc_hed25 {
  width: 140px;height: 38px;border: 1px solid #E3E6EB;background: #F5F8FB;float: left;text-align: center;
  line-height: 36px;border-top-right-radius: 6px;border-bottom-right-radius: 6px;cursor: pointer;
}
.pc_hed26 {
  opacity: 40%;
}
.pc_hed27 {
  background: @themeButtonBackGroundColor;width: 90px;height: 36px;color: @themeBackGroundColor;font-weight: 700;font-size: 16px;line-height: 36px;
  margin-top: 7px;border-radius: 18px;text-align: center;cursor: pointer;margin-left: 15px;
}
.pc_show_more {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin-left: 0 !important;
  z-index: 500;
  &--bg {
    position: absolute;width: 100%;height: 100%;top: 0;left: 0;margin-left: 0 !important;
  }
  &--container {
    width: 134px;background: #F4EFE5;border-radius: 8px;float: right !important;
    margin-top: 50px;margin-right: 15px;position: relative;z-index: 1;
  }
  &--line {
    width: 112px;height: 1px;margin-left: 12px;background: #E3D6BD;
  }
  .headerDown {
    margin-left: 0;
    width: 134px;
    float: none;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #BDA16A;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
  }
}
.pc_hed28 {
  width: 104px;height: 36px;overflow: hidden;line-height: 36px;cursor: pointer;background: @themeButtonBackGroundColor;font-weight: 700;
  font-size: 16px;text-align: center;color:@themeBackGroundColor;display: inline-block;
  margin-top: 7px;float: right;border-radius: 18px;margin-right: 20px;
}
.pc_hed29 {
  width: 92px;
  height: 36px;
  border-radius: 18px;
  overflow: hidden;
  margin-top: 7px;
  line-height: 36px;
  cursor: pointer;
  font-weight: 700;
  background: @themeButtonBackGroundColor;
  color: @themeBackGroundColor;
  float: right;
  font-size: 16px;
  text-align: center;
  margin-right: 20px;
}
@fontColor:@themeFontColor;
@themeColor:@themeBackGroundColor;
.pc_hed3 {
  font-size: 20px;font-weight: bold;color: @themeFontColor;text-indent: 24px;margin-top: 20px;
}
.pc_hed31 {
  width: 512px;height: 295px;background: #F7F8F9;margin-top: 32px;
  margin-left: 32px;color: #8E8E8E;overflow: hidden;
}
.pc_hed32 {
  width: 464px;height: 36px;margin-top: 12px;margin-left: 24px;text-align: center;cursor: pointer;
  color: white;background: #BDA16A;border-radius: 4px;font-size: 14px;line-height: 36px;
}
.icon_close_kf{
  font-size: 30px;
  color: #8298A6;
  cursor: pointer;
  position: absolute;
  top: -3px;
  right: 13px;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
.tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  .title{
    color: @fontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @fontColor;
    font-size: 24px;
    margin-top: 20px;
    text-align: center;
  }
  .dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 35px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
  }
}
.tab_container{
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  border-radius: 4px;
  border: 1px solid rgba(227, 230, 235, 100);
  .tab{
    width: 150px;
    height: 38px;
    text-align: center;
    line-height: 38px;
    color: #B39959;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
  }
}
.vip_tab_container{
  width: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  border-radius: 4px;
  border: 1px solid rgba(227, 230, 235, 100);
  .tab{
    width: 120px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    color: @themeFontColor;
    background: #F5F8FB;
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    border-left: 1px solid #E3E6EB;
  }
}
.tab_one{
  border-left: 0px solid #E3E6EB !important;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.tab_last{
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.tab_active{
  background: @themeBackGroundColor !important;
  color: white !important;
}
.tab_active1{
  background: @themeBackGroundColor !important;
  color: white !important;
  height: 36px !important;line-height: 36px !important;
}
.pc_header_help1{
  min-width: 104px!important;
}
.pc_header_help2{
  color: @themeFontColor;
  font-size: 16px;
  line-height: 40px;
  font-weight: 400;
  text-align: center;
  div {
    cursor: pointer;
  }
}
.pc_header_help3{
  font-size: 24px;
  font-weight: bold;
  color: @themeFontColor;
  line-height: 40px;
  text-align: center;
  padding-top: 30px;
}
.pc_header_help4{
  font-size: 16px;
  font-weight: 400;
  color: #B1C3CD;
  line-height: 40px;
  text-align: center;
  margin-bottom: 18px;
}
.pc_header_help5{
  margin: 0 auto;
  width: 390px;
  height: 60px;
  border-radius: 6px;
  padding: 18px;
  background: #F5F8FB;
  span{
    font-size: 18px;
    font-weight: bold;
    color: @themeFontColor;
  }
}
.pc_header_help6{
  height: 61px;
  text-align: right;
  font-size: 30px;
  padding-right: 15px;
  color: #8298A6;
  cursor: pointer;
}
.pc_header_help7{
  font-size: 16px;
  text-align: center;
  margin-top: 8px;
  line-height: 19px;
}
.pc_header_help8{
  font-weight: 400;
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
  line-height: 14px;
}
.pc_header_help9{
  font-weight: 400;
  font-size: 20px;
  color: #999;
  text-align: center;
  margin-top: 34px;
}
.pc_header_help10{
  font-weight: 400;
  font-size: 16px;
  color: #999999;
  text-align: center;
  margin-top: 18px;
  padding-bottom: 54px;
}
.pc_header_user1{
  font-size: 16px;
  font-weight: 400;
  color: @themeBackGroundColor;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 18px;
}
.pc_no_network_div {
  width: 196px;
  height: 100%;
  border-radius: 8px;
  background: #f5f7fa;
  color: #cacaca;
  position: absolute;
  img {
    width: 56px;
    height: 49px;
    margin: 40px 0 20px;
  }
}
.pc_header_user2{
  font-size: 16px;
  font-weight: 400;
  color: #B1C3CD;
  text-align: center;
  margin-top: 14px;
}
.pc_header_user3{
  font-size: 16px;
  font-weight: 400;
  color: #B1C3CD;
  text-align: center;
  margin-bottom: 37px;
}
.pc_header_user4{
  font-size: 24px;
  font-weight: bold;
  color: @themeFontColor;
  text-align: center;
  margin-bottom: 20px;
}
.mobile_container_div {
  width: 430px;
  height: 280px;
  margin: 0 auto;
  display: flex;
  justify-content: space-evenly;
  img {
    width: 140px;
    height: 260px;
  }
}
.iso_img_div {
  width: 92px;
  height: 92px;
  top: 78px;
  left: 24px;
  position: absolute;
}
.image_contain_div {
  width: 140px;
  height: 260px;
  position: relative;
}
.iosDownloadBg {
  background-image: url('../image/ios_download.png');
}
.androidDownloadBg {
  background-image: url('../image/android_download.png');
}
.tab_container_stock{
  width: 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  border-radius: 4px;
  border: 1px solid rgba(227, 230, 235, 100);
  .tab{
    width: 120px;
    height: 34px;
    text-align: center;
    line-height: 34px;
    color: @themeFontColor;
    background: #F5F8FB;
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    border-left: 1px solid #E3E6EB;
  }
}
.pc_hed_font {
  margin: 0 auto;
  color: @themeBackGroundColor;
  font-weight: bold;
  font-size: 17px;
  height: 36px;
  line-height: 34px;
}
.content_top{
  height: 49px;
  background: #FFF;
  display: flex;
  justify-content: center;
  align-items: center;
}
.update_button{
  text-align: center;
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 27px;
  width: 70px;
  height: 30px;
  border: 1px solid @themeBackGroundColor;
  color: @themeBackGroundColor;
  box-sizing: border-box;
  border-radius: 15px;
}
.upgrade1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @areaColor;
}
.upgrade11 {
  width: 450px;
  min-height: 280px;
  background: #fff;
  border-radius: 6px;
  padding-bottom: 10px;
  position: absolute;
  transform: translate(-50%, -50%);
  top: 50%;
  left:50%;
}
.upgrade12 {
  width: 93%;
  height: 75px;
  font-size: 24px;
  margin-left: 20px;
  line-height: 100px;
  text-align: center;
  font-weight: bold;
  color: @themeFontColor;
}
.upgrade13 {
  float: right;
  font-size: xx-large;
  color: @text;
  margin-top: -22px;
  cursor: pointer;
}
.upgrade14 {
  width: 93%;
  overflow: hidden;
  margin: 21px auto;
  text-align: center;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.upgrade14 div {
  width: 70px;
  text-align: center;
  line-height: 61px;
  float: left;
  font-size: 16px;
  font-weight: bold;
}
.upgrade14 input {
  width: 320px;
  height: 61px;
  background: #f5f8fb;
  border-radius: 10px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  font-weight: bold;
}
.pc_dtclear1 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_dtclear2 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 158px;
  background: linear-gradient(90deg, #f1d3af, #cfa26b);
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  font-weight: normal;
}
.pc_header_hom {
  min-width: 100px;
  height: 30px;
  background: linear-gradient(156.99deg, rgba(208, 244, 255, 0.5) 10.75%, rgba(169, 210, 248, 0.5) 47.67%, rgba(100, 188, 237, 0.5) 87.8%);
  border-radius: 15px;
  text-align: right;
  color: #957159;
  margin-right: 10px;
  padding-right: 16px;
  .insufficient {
    color: #FF0000;
  }
  .arrowRight {
    display: inline-block;
    float: none;
    margin-right: 0;
    margin-top: -3px;
  }
}
.pc_header_hom0 {
  width: 30px;
  height: 30px;
  background: @versionIcon;
  border-radius: 50px;
}
.pc_meg_header {
  background: #F6EEE1!important;
}
.isTrue {
  background: @themeBackGroundColor;
  color: #FFF;
  width: 80px;
}
.isFalse {
  color: @themeBackGroundColor;
  width: 140px;
}
.isAutoCash {
  background-image: linear-gradient(90deg, #D8B774 0%, #DEB071 100%);
  color: #FFF;
  width: 100%;
}
.notIsAutoCash {
  background: @themeBackGroundColor;
  color: #FFF;
  width: 80px;
}
.returnGoods {
  color: @themeBackGroundColor;
  width: 140px;
}
.stockTaking {
  width:90px;
  height:36px;
  font-size:16px;
  font-weight: bold;
  background-color:@themeButtonBackGroundColor;
  outline: none;
  color:@themeBackGroundColor;
  display: inline-block;
  float: right;
  border-radius: 10px;
  border: 0px;
  margin-right: 20px;
  margin-top: 7px;
}
.goodsUnpacking {
  width:108px;
  height:36px;
  font-size:16px;
  font-weight: bold;
  background-color:@themeButtonBackGroundColor;
  outline: none;
  color: @themeBackGroundColor;
  display: inline-block;
  float: right;
  border-radius: 10px;
  border: 0px;
  margin-right: 20px;
  margin-top: 7px;
}
.usernameHeader {
  line-height: 30px;
  color: @themeBackGroundColor;
}
.settingUp {
  line-height: 30px;
  color: @themeBackGroundColor;
}
.synchronous {
  line-height: 30px;
  color: @themeBackGroundColor;
}
.cloudLoading {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,.7);
}
.help {
  line-height: 30px;
  color:  @themeBackGroundColor;
  span {
    font-weight: bold;
    color: @themeBackGroundColor;
  }
}
#pcHed11 {
  background: @themeButtonBackGroundColor;
  width: 90px;
  height: 36px;
  margin-right: -5px;
}
#elIconPlus {
  font-weight: bold;
  color: @themeBackGroundColor;
  margin-left: 10px;
}
.jihuo {
  background: @themeBackGroundColor;
  width: 130px;
  border-radius: 4px;
  font-size: 20px;
  font-weight: bold;
  color: rgb(255, 255, 255);
  line-height: 50px;
  text-align: center;
  float: right;
  cursor: pointer;
  margin: 10px;
  margin-right: 53px;
}
#tips {
  color: @themeFontColor;
}
#btnStyle {
  background: @themeFontColor;
}
.btn_regular {
  line-height: 30px;display: inline-block;position: absolute;top: 1px;text-align: center;
  width: 86px;color: @themeFontColor;margin-left: 5px;font-weight: bold;height: 30px;
  border-radius: 8px;background: @input_backgroundColor;font-size: 14px;
}
.return_pay_btn {
  line-height: 30px;
  display: inline-block;
  position: absolute;
  text-align: center;
  margin: 8px 0 0 15px;
  font-weight: bold;
  border-radius: 8px;
  font-size: 14px;
  background: @themeButtonBackGroundColor;
  width: 90px;
  height: 30px;
  color: @themeBackGroundColor;
  cursor: pointer;
}
.help-dialog {
  display: flex;
  align-items: center;
  /deep/.el-dialog {
    margin-top: 6vh !important;
  }
}
.short-cut-container-div {
  height: 56px;width: 360px;padding-top: 12px;
}
.strip-group {
  background: #fff;
  height: 40px;
  color: @themeFocusBorderColor;
  padding: 0px 12px;
  font-size: 18px;
  line-height: 40px;
}
.strip-light {
  background: #F8F5EF;
}
.strip-dark {
  background: #F4EFE5;
}
.dialog_mask {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);
}
.tokenDialog {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;
  &--container {
    position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;
    background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;
  }
  &--text {
    width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 30px;font-weight: normal;
  }
}
.left-tab-radius {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-right:1px solid rgba(227, 230, 235, 100);
}
.right-tab-radius {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-left:1px solid rgba(227, 230, 235, 100);
}
@media only screen and (max-width: 1280px) {
  .pc_hed27 {
    margin-left: 10px !important;
  }
}
</style>
<style>
@import "../ad/ad.css";
</style>
<template>
  <div
    style="height: 50px;width: 100%;position: relative;background: #FFF;"
    :class="isShortMessage ? 'pc_meg_header' : ''"
    :style="pcLockScreen ? (isHome || isShortMessage ? 'filter: blur(20px);border-top: solid 1px #e3e6eb;' : 'filter: blur(20px);border-bottom: solid 1px #e3e6eb;')
    :(isHome || isShortMessage ? 'border-top: solid 1px #e3e6eb;' : 'border-bottom: solid 1px #e3e6eb;')"
  >
    <v-editAd></v-editAd>
    <v-PrintSetting></v-PrintSetting>
    <!-- 结算/退货切换清空列表 -->
    <div
      v-show="exit"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="exit = false"
      ></div>
      <div style="position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;">
        <div style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight:bold;">提示2</div>
        <div style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;">确定退出系统？</div>
        <div class="pc_hed17">
          <div
            class="pc_hed18"
            @click="exit = false"
          >取消</div>
          <div
            class="pc_hed19"
            @click="continueExit()"
          >确定</div>
        </div>
      </div>
    </div>
    <!-- 跳转库存盘点页提示框 -->
    <div
      v-show="showStockInvenRemind"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="showStockInvenRemind = false"
      ></div>
      <div style="position: relative;z-index: 800;height: 300px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;">
        <div id="tips" style="width: 100%;text-align: center;font-size: 25px;margin-top: 40px;font-weight:bold">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;
          margin-top: 25px;font-weight: normal;color: #567485;">
          请避免在不同设备上同时盘点，<br/>否则将导致库存异常
        </div>
        <div class="pc_hed17" style="margin-top:45px;">
          <div
            class="pc_hed18"
            style="font-weight:bold;"
            @click="showStockInvenRemind = false"
          >取消</div>
          <div
            class="pc_hed19"
            @click="continueToStockInven()"
          >盘点</div>
        </div>
      </div>
    </div>
    <!-- 锁屏确认框 -->
    <div
      v-show="pcLockScreenConfirm"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="closeLockScreenConfirm()"
      ></div>
      <div class="stock-inven-remind__div">
        <div class="stock-inven-remind__div--title">提示</div>
        <div class="stock-inven-remind__div--text">
          系统即将进入锁屏，<br/>确定继续吗？
        </div>
        <div class="pc_hed17" style="margin-top:45px;">
          <div
            class="pc_hed18"
            style="font-weight:bold"
            @click="closeLockScreenConfirm()"
          >取消</div>
          <div
            class="pc_hed19"
            style="font-weight:bold"
            @click="setClockScreen()"
          >确定</div>
        </div>
      </div>
    </div>
    <!-- 退出系统dialog -->
    <div
      v-show="showTipsDialog" style="width: 100%;height: 100%;position: fixed;top: 0;left: 0;
      background: rgba(0,0,0,.5);z-index: 999999;"
    >
      <div class="tips_dialog" style="width: 450px;height: 264px;margin: 215px auto !important;
      background: #FFF;border-radius: 6px;overflow: hidden;">
        <div class="title">提示</div>
        <div class="content">确定退出系统？<br/>
          <span v-if="ultimate !== null">系统将自动交接班。</span>
        </div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            id="btnStyle"
            @click="cancelClose"
          >取消</div>
          <div
            class="btn"
            style="background:#BDA169;margin-left:30px"
            @click="confirmLogout"
          >确定</div>
        </div>
      </div>
    </div>
    <!-- 帮助弹窗 -->
    <el-dialog
      :visible.sync="showHelp"
      :width="showKefu ? '1112px' : '560px'"
      class="help-dialog"
      :show-close="false"
      :close-on-click-modal='true'
    >
      <div
        class="icon_close_kf"
        @click="showHelp = false"
      >×</div>
      <!-- 四个模块 -->
      <div style="overflow: hidden;">
        <!-- 左侧两个 -->
        <div v-if="showKefu" style="float: left;">
          <!-- 客服微信二维码 -->
          <div class="pc_hed31" style="margin-top: 32px;height: 326px;">
            <div class="pc_hed3">联系微信客服</div>
            <div class="kefuSrc" ref="kefuSrcUrl" style="width: 160px;margin: 0 auto;margin-top: 12px;"></div>
            <div style="line-height: 39px;color: #567485;margin-top: 12px;overflow: hidden;">
              <div style="float: left;width: 84px;background: #FFF;margin-left: 70px;font-size: 16px;
                text-align: center;font-weight: bold;border-radius: 6px;">售后热线</div>
              <div style="float: left;font-size: 32px;font-weight: bold;margin-left: 12px;">400-033-2520转2</div>
            </div>
            <div style="text-align: center;line-height: 19px;font-size: 16px;margin-top: 8px;">
              客服工作时间：08:00-17:00
            </div>
          </div>
        </div>
        <!-- 右侧一个 -->
        <div style="float: left;">
          <div class="pc_hed31" style="height: 326px;margin-left: 24px;">
            <!-- 意见反馈 -->
            <div class="pc_hed3">提出改善建议</div>
            <div>
              <div class="idea_input">
                <el-input
                  id="headSug"
                  type="textarea"
                  maxlength="800"
                  style="font-size: 16px; height: 210px;"
                  placeholder="对我们的系统、服务，您还有什么建议吗？您还期待什么新功能？请告诉我们...（800字以内）"
                  v-model="feedBackContent"
                ></el-input>
              </div>
              <div
                class="pc_hed32"
                @click="feedBack"
              >提 交</div>
            </div>
          </div>
        </div>
      </div>
      <div style="height: 20px;"></div>
    </el-dialog>
    <!-- 左侧部分，返回按钮等 -->
    <div style="position: absolute;top: 0;left: 0;">
      <div v-if="isShortMessage">
        <div
          class="pc_hed14"
          @click="backPrevious"
          style="margin-top: 10px;"
        >
          <span class="el-icon-back" style="display: inline-block;color: #7F6C40;font-size: 25px;"></span>
          <span style="color: #7F6C40;display: inline-block;line-height: 24px;vertical-align: top;">返回</span>
        </div>
      </div>
      <div v-if="isMessageHistory">
        <div
          class="pc_hed14"
          @click="backPrevious"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isGoodsSort">
        <div
          class="pc_hed14"
          @click="sortBack"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isMemberSetting || isShortMessageMass">
        <div
          class="pc_hed14"
          @click="backMember"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isSetting && (isGoods || isDetail || isPay)">
        <div
          class="pc_hed14"
          @click="backBefore"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回</div>
        </div>
      </div>
      <div v-if="isGoodsUnpack">
        <div
          class="pc_hed14"
          @click="recycleBackToGoods"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isCheck || isEmployee || (isReport && fromReport===0) || (isDetail && fromDetail===0)">
        <div
          class="pc_hed14"
          @click="back"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
        <div
          v-if="isDetail && pcDetailTab === 1 && fromDetail === 0"
          class="return_pay_btn"
          @click="returnPay">
          返回收银台
        </div>
      </div>
      <div v-if="isSetting">
        <div
          class="pc_hed14"
          @click="back"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isMember">
        <div
          class="pc_hed14"
          @click="back"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isGoods && !isSetting">
        <div
          class="pc_hed14"
          @click="back"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isStock">
        <div
          class="pc_hed14"
          @click="toHomeFromStock"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="showScalesManage">
        <div
          class="pc_hed14"
          @click="backGoodsFromScales()"
        >
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <div v-if="isPay && !isSetting && !ifautoCash" style="margin-top: 3px;">
        <div
          @click="payBack()"
          style="cursor: pointer;overflow: hidden;display: inline-block;"
        >
          <img
            alt=""
            class="pc_hed14"
            src="../image/pc_header_back.png"
            style="margin-left: 25px;margin-top: 8px;float: left;"
          />
          <div
            style="float: left;font-size: 17px;font-weight: bold;color: #698293;margin-left: 5px;margin-top: 10px;">
            返回 [Esc]
          </div>
        </div>
        <el-popover
          ref="shortCutPopover"
          @show="setPointerUp(2)"
          @hide="setPointerDown(2)"
          popper-class="short-cut-popover"
        >
          <div>
            <div v-for="(item, index) in shortCutList" :key="index"
              class="short-cut-container-div"
              :class="item.type === 'group' ? 'strip-group' : index % 2 ? 'strip-light' : 'strip-dark'">
              <div class="group-label" v-if="item.type === 'group'">
                {{item.label}}
              </div>
              <div v-else>
                <div class="shortcut">
                  <span class="shortcutInfo">{{item.keyChar[0]}}</span>
                </div>
                <span v-if="item.keyChar.length > 1 " class="el-icon-plus" id="elIconPlus"></span>
                <div v-if="item.keyChar.length > 1" class="shortcut">
                  <span class="shortcutInfo">{{item.keyChar[1]}}</span>
                </div>
                <span v-if="item.keyChar.length > 2 " id="elIconPlus">/</span>
                <div v-if="item.keyChar.length > 2" class="shortcut">
                  <span class="shortcutInfo">{{item.keyChar[2]}}</span>
                </div>
                <div class="shortcutFont">{{item.label}}</div>
              </div>
            </div>
          </div>
          <div
            style="display: inline-block;position: absolute;top: 11px;width: 140px;margin-left: 20px;cursor: pointer;"
            slot="reference"
          >
            <div class="btn_regular" style="">
              <i class="el-icon-question" style="font-size: 16px;"></i> 帮助 F1
            </div>
          </div>
        </el-popover>
      </div>
      <!-- 交接班 -->
      <div
        v-if="isChangeShifts"
        @click="backChangeShifts"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 报表 -->
      <div
        v-if="isReportForms"
        @click="back"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 业态选择 -->
      <div
        v-if="isChooseIndustry"
        @click="backLogin"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回</div>
        </div>
      </div>
      <!-- 寄存明细 -->
      <div
        v-if="isDepositRecord || isDepositResidue"
        @click="backToReportForms"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 交接班记录 -->
      <div
        v-if="isChangeShiftsRecord"
        @click="backToReportForms"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 供应商管理 -->
      <div
        v-if="isSupplierManage"
        @click="backToBuyOrder"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 库存盘点 -->
      <div
        v-if="isStockInventory"
        @click="backToGoods"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
      <!-- 返回报表页 -->
      <div
        v-if="isStockStatistics || (isReport  && fromReport===1) || isOnceCard || (isDetail && !isSetting && fromDetail===1) || isVipRechargeDetail
        || isPointsUseDetail || isVipRechargeStatistics || isVipConsumeCollection || isVipValueAnalysis || isStockRecord || isStockStatisticsReport
        || isStockChangeDetail || isStockCheckDetail || isStockWarningDetail || isOverdueWarningDetail || isVipIncreaseStatistics"
        @click="backReportForms"
      >
        <div class="pc_hed14">
          <img alt="" src="../image/pc_header_back.png" />
          <div>返回 (ESC)</div>
        </div>
      </div>
    </div>
    <!-- 中间标题部分 -->
    <div style="text-align:center;width: 100%;line-height: 50px;overflow: hidden;">
      <div
        v-if="isGoods && !isSetting"
        class="pc_hed15"
      >
        <div>
          商品管理
        </div>
      </div>
      <div
        v-if="isMember"
        class="pc_hed15"
      >会员管理</div>
      <div
        v-if="isMemberSetting"
        class="pc_hed15"
      >会员设置</div>
      <div
        v-if="isEmployee"
        class="pc_hed15"
      >员工管理</div>
      <div
        v-if="isSizeSetting"
        class="pc_hed15"
      >规格管理</div>
      <div
        v-if="showScalesManage"
        class="pc_hed15"
      >传秤管理</div>
      <div
        v-if="isSizeTemplate"
        class="pc_hed15"
      >规格模板</div>
      <div
        v-if="isStockInventory"
        class="pc_hed15"
      >库存盘点</div>
      <div
        v-if="isChangeShifts"
        class="pc_hed15"
      >交接班</div>
      <div
        v-if="isReportForms"
        class="pc_hed15"
      >报表</div>
      <div
        v-if="isGoodsSort"
        class="pc_hed15"
      >商品排序</div>
      <div
        v-if="isChooseIndustry"
        class="pc_hed15"
      >选择所属行业</div>
      <div
        v-if="isGoodsUnpack && ultimate !== null"
        class="pc_hed15"
      >商品拆包</div>
      <div
        v-if="isStockStatistics || isDepositRecord || isDepositResidue"
        class="pc_hed15"
      >
        <div style="display:flex;justify-content:center;align-items:center;height:50px">
          <div class="tab_container">
            <div
              :class="['tab left-tab-radius', tab_index == 1 ? 'tab_active' : '']"
              @click="clickTab(1)"
            >寄存统计</div>
            <div
              :class="['tab', tab_index == 2 ? 'tab_active' : '']"
              @click="clickTab(2)"
            >寄存明细</div>
            <div
              :class="['tab right-tab-radius', tab_index == 3 ? 'tab_active' : '']"
              @click="clickTab(3)"
            >寄存剩余</div>
          </div>
        </div>
      </div>
      <!-- 会员报表 -->
      <div
        v-if="(isReport && pcDetailTab == 3) || isVipRechargeDetail || isPointsUseDetail || isVipRechargeStatistics
          || isVipConsumeCollection || isVipValueAnalysis || isVipIncreaseStatistics"
        class="pc_hed15"
      >
        <div style="display:flex;justify-content:center;align-items:center;height:50px">
          <div class="vip_tab_container">
            <div
              v-for="(item,index) in vipArr"
              :key="index"
              :class="['tab', vip_index == index ? 'tab_active' : '',index == 0 ? 'tab_one' : '',index == vipArr.length - 1 ? 'tab_last' : '']"
              @click="clickVipTab(index)"
            >{{item}}</div>
          </div>
        </div>
      </div>
      <div
        v-if="isChangeShiftsRecord"
        class="pc_hed15"
      >交接班记录</div>
      <div
        v-if="isDetail && pcDetailTab === 1 && !isSetting"
        class="pc_hed15"
      >销售明细</div>
      <div
        v-if="isShortMessageMass"
        class="pc_hed15"
      >短信群发</div>
      <div
        v-if="isDetail && pcDetailTab === 2 && !isSetting"
        class="pc_hed15"
      >进货明细</div>
      <div
        v-if="isReport && pcDetailTab === 4"
        class="pc_hed15"
        style="font-size: 18px;"
      >商品销售统计</div>
      <div
        v-if="isOnceCard && pcDetailTab === 5"
        class="pc_hed15"
      >
       <div class='content_top'>
        <div class="tab_container_stock" style="width: 360px">
          <div
            :class="['tab', tabIndexOnceCard == 1 ? 'tab_active' : '', 'tab_one']"
            @click="clickTabOnceCard(1)"
          >次卡统计</div>
          <div
            :class="['tab', tabIndexOnceCard == 2 ? 'tab_active' : '']"
            @click="clickTabOnceCard(2)"
          >次卡销售明细</div>
          <div
            :class="['tab', tabIndexOnceCard == 3 ? 'tab_active' : '', 'tab_last']"
            @click="clickTabOnceCard(3)"
          >次卡使用明细</div>
        </div>
      </div>
      </div>
      <div
        v-if="isMessageHistory"
        class="pc_hed15"
      >
        <div class='content_top'>
          <div class="tab_container_stock" style="width: 360px">
            <div
              :class="['tab', tabIndexShort === 1 ? 'tab_active' : '', 'tab_one']"
              @click="clickTabIndexShort(1)"
            >短信发送记录</div>
            <div
              :class="['tab', tabIndexShort === 2 ? 'tab_active' : '', 'tab_last']"
              @click="clickTabIndexShort(2)"
            >短信购买记录</div>
            <div
              :class="['tab', tabIndexShort === 3 ? 'tab_active' : '', 'tab_last']"
              @click="clickTabIndexShort(3)"
            >短信群发记录</div>
          </div>
        </div>
      </div>
      <!-- 库存报表 -->
      <div
        v-if='isStockRecord || isStockStatisticsReport ||  isStockChangeDetail || isStockCheckDetail || isStockWarningDetail || isOverdueWarningDetail'
        class="pc_hed15"
      >
        <div style="display:flex;justify-content:center;align-items:center;height:50px;">
          <div class="tab_container_stock">
            <div
              v-for="(item,index) in stockArr"
              :key="index"
              :class="['tab', stock_index == index ? 'tab_active1' : '',index == 0 ? 'tab_one' : '',index == stockArr.length - 1 ? 'tab_last' : '']"
              @click="clickStockTab(index)"
            >{{item}}</div>
          </div>
        </div>
      </div>
      <div
        v-if="isStock"
        class="pc_hed15"
      >
        <span v-show="buyOrder">进货</span>
        <span v-show="!buyOrder">退货</span>
      </div>

      <div
        v-if="isSupplierManage"
        class="pc_hed15"
      >供应商管理</div>
      <div
        v-if="isPay && !isSetting"
        class="pc_hed15"
      >
        <div class="pay_header_container">
          <div class="pay_header">
            <div
              style="float: left;line-height: 36px;height: 36px;font-size: 18px;cursor: pointer;border-radius: 18px;"
              :class="!pc_return_goods ? (ifautoCash ? 'isAutoCash' :'notIsAutoCash') : 'returnGoods'"
              @click="pc_return_goods ? setPcReturnGoods():''"
            >收银<span style="font-size: 15px;">{{pc_return_goods ? '(Ctrl+Tab)' : ''}}</span></div>
            <div
              v-show="!ifautoCash"
              style="float: left;line-height: 36px;height: 36px;font-size: 18px;cursor: pointer;border-radius: 18px;"
              :class="pc_return_goods ? 'isTrue' : 'isFalse'"
              @click="!pc_return_goods ? toReturnGoods():''"
            >退货<span style="font-size: 15px;">{{pc_return_goods ? '' : '(Ctrl+Tab)'}}</span></div>
          </div>
        </div>
      </div>
      <div
        v-if="isSetting"
        class="pc_hed15"
      >设置</div>
    </div>
    <!-- 右侧部分 -->
    <div style="position: absolute;right: 0;top: 0;" :style="isHome && (screenWidth < 1199 ? 'width: calc(100% - 196px);' :screenWidth > 1199 ? 'width: calc(100% - 370px);' : 'width: calc(100% - 260px);')">
      <div
        v-if="isStock"
      >
          <div
            class="pc_hed28"
            @click="!$employeeAuth('supplier_management') ? '':supplierManage()"
            :style="!$employeeAuth('supplier_management') ? 'opacity: 40%':''"
          >
            供应商管理
          </div>
        <div v-show="$employeeAuth('purchase') && buyOrder" @click="setBuyOrder(1)"
             :style="$employeeAuth('return_purchase') && buyOrder ? '':'opacity: 40%'" class="pc_hed29">退货</div>
        <div v-show="$employeeAuth('return_purchase') && !buyOrder" @click="setBuyOrder(2)"
             :style="$employeeAuth('purchase') && !buyOrder ? '':'opacity: 40%'" class="pc_hed28">进货</div>
      </div>
      <div v-if="isGoods && !isSetting && ultimate !== null" style="display: inline"
        :style="!$employeeAuth('products_curstock_inventories') ? 'opacity: 40%':''">
        <button class="stockTaking"
          @click="!$employeeAuth('products_curstock_inventories') ? '':toStockInventory()"
        >
          库存盘点
        </button>
      </div>
      <div v-if="isGoods && !isSetting" style="display: inline">
        <button class="goodsUnpacking"
          @click="goodsUnpack()"
        >
          商品拆包
        </button>
      </div>
      <div
        v-if="isHome"
        class="pc_hed13"
      >
        <el-popover
          @show="setPointerUp(1)"
          @hide="setPointerDown(1)"
          trigger="click"
        >
          <div class="pc_header_help2">
            <div @click="showWeClick()">关于我们</div>
          </div>
          <div class="pc_header_help2">
            <div @click="showPhoneDownload">手机下载</div>
          </div>
          <div
            class="pc_hed16"
            style="overflow: hidden;margin-right: 12px;margin-left: 26px;"
            slot="reference"
          >
            <img
              alt=""
              src="../image/zgzn-pos/pc_header_person.png"
              style="width: 30px;height: 30px;margin-top: 0px"
            />
            <div class="usernameHeader"
              v-show="usernameHeader !== '' && loginInfo.employeeNumber === ''"
            >{{usernameHeader}}
              <span
                :class="pointer1"
                style="font-weight: bold"
              ></span>
            </div>
            <div
              style="line-height: 30px;"
              v-show="loginInfo.employeeNumber !== ''"
            >{{usernameHeader + '-' + loginInfo.employeeNumber}}
              <span
                :class="pointer1"
                style="font-weight: bold"
              ></span>
            </div>
          </div>
        </el-popover>
<!--         <el-popover
          @show="setPointerUp(1)"
          @hide="setPointerDown(1)"
          trigger="click"
        >
          <div class="pc_header_help2">
            <div @click="showWe = true">关于我们</div>
          </div>
          <div
            class="pc_hed16"
            style="overflow: hidden;"
            :style="screenWidth < 1240 ? 'margin-left: 26px;' : 'margin-left: 40px;'"
            slot="reference"
          >
            <i class="el-icon-s-shop" style="font-size:24px;line-height:30px;"></i>
            <div
              style="line-height: 30px;color:#4C567C;"
              v-show="usernameHeader !== '' && loginInfo.employeeNumber === ''"
            >{{usernameHeader}}
              <span
                :class="pointer1"
                style="font-weight: bold"
              ></span>
            </div>
            <div
              style="line-height: 30px;"
              v-show="loginInfo.employeeNumber !== ''"
            >{{usernameHeader + '-' + loginInfo.employeeNumber}}
              <span
                :class="pointer1"
                style="font-weight: bold"
              ></span>
            </div>
          </div>
        </el-popover> -->
        <div
          class="pc_hed16"
          @click="setting()"
          v-show="$employeeAuth('use_settings')"
          style="cursor: pointer;"
          :style="screenWidth < 1240 ? 'margin-left: 5px;' : ''"
        >
<!--           <i class="el-icon-setting" style="font-size:20px;color:#4C567C;line-height:30px;"></i>
          <div style="line-height: 30px;color:#4C567C;margin-left:3px;">设置</div> -->
          <img alt="" src="../image/zgzn-pos/pc_home_setting.png" />
          <div class="settingUp">设置</div>
        </div>
        <div
          class="pc_hed16"
          @click="sync()"
          style="cursor: pointer;position: relative;"
          :style="screenWidth < 1240 ? 'margin-left: 5px;' : ''"
        >
          <img
            alt=""
            src="../image/zgzn-pos/pc_header_cloudrefresh.png"
            style
          />
          <div class="synchronous">云同步</div>
         <!--  <div style="line-height: 30px;color:#4C567C;">云同步</div> -->
          <div
            class="cloudLoading"
            v-show="show_cloud_loading"
          >
            <img
              alt=""
              src="../image/zgzn-pos/pc_cloud_loading.gif"
              style="margin-left: 60px;margin-top: 3px;"
            />
          </div>
        </div>
        <div
          class="pc_hed16"
          :style="screenWidth < 1240 ? 'margin-left: 5px;' : ''"
          @click="help()"
        >
          <i class="el-icon-headset" style="margin-right: 8px;margin-top: 8px;font-weight: bold;"></i>
          <div class="help">售后客服</div>
        </div>
        <div
          class="pc_hed16"
          style="cursor: pointer;position: relative;margin-left: 0px;"
        >
          <div class="pc_header_hom" :style="{background: edition.background}" style="line-height: 30px;cursor:pointer;float: left;" @click="showVersionCompare">
            <div class="pc_header_hom0" style="float: left;">
              <el-image :src="edition.url" style="height: 22px;width: 22px;margin-top: 3px;right: 4px;"/>
            </div>
            <span style="margin-left: 6px;letter-spacing: 1.3px;" :style="'color:' + edition.color">{{edition.name}}</span>
            <template v-if="ultimate !== null && period !== -1">
              <span :class="{ insufficient: trialDay <= 4 }">[剩{{ trialDay }}天]</span>
              <img class="arrowRight" src="../image/arrowRight.png" alt="右箭头">
            </template>
          </div>
          <div class="update_button" v-show="!ultimate" @click="openJiHuo(); jihuocode = ''">升级</div>
        </div>
      </div>
      <div
        v-if="isPay"
        class="pc_hed12"
      >
        <div
          class="pc_hed27"
          v-if="!(aid.trim()=='' || app_secret.trim()=='')"
          v-show="!ifautoCash && !pc_return_goods"
          @click="doautoCash()"
        >
          自助收银
        </div>
        <div
          v-show="ifautoCash"
          class="pc_hed27"
          style="width: 120px;"
          @click="doautoCash()"
        >
          解除自助收银
        </div>
        <div
          v-show="!ifautoCash && $employeeAuth('show_shift_records')"
          class="pc_hed27"
          style="width: 96px;"
          @click="goDetail()"
        >
          销售单F10
        </div>
        <div
          v-show="!ifautoCash"
          class="pc_hed27"
          style="width: 96px;"
          @click="openMoneybox()"
        >
          开钱箱F11
        </div>
        <div
          v-show="!ifautoCash"
          class="pc_hed27"
          @click="setClockScreenConfirm()"
        >
          锁屏F12
        </div>
        <div
          v-show="!ifautoCash"
          class="pc_hed27"
          @click="showMore = true"
        >
          ≡ 更多
        </div>
        <div v-show="showMore" class="pc_show_more">
          <div class="pc_show_more--bg"  @click="showMore = false"></div>
          <div class="pc_show_more--container">
            <div v-show="!ifautoCash" class="headerDown" @click="openPrintSetting()">配件设置 F9</div>
            <div class="pc_show_more--line"></div>
            <div v-show="!ifautoCash" class="headerDown" @click="openGoodsSort()">商品排序</div>
          </div>
        </div>
      </div>
      <div
        v-if="isMember"
        class="pc_hed12"
      >
        <div
          class="pc_hed11"
          style="background: linear-gradient(90deg, #FFB68C 0%, #FF6D5C 100%);width: 90px;height: 36px;margin-right: -5px"
        >
          <div
            style="margin-left: 10px;color: #FFFFFF;font-weight: bold;font-size: 17px;height: 36px;
            line-height: 36px;text-align: center;width: 70px;"
            @click="openShortMessageMass()"
          >短信群发</div>
        </div>
        <div
          class="pc_hed11"
          id="pcHed11"
          :style="(!$employeeAuth('create_vips') && !$employeeAuth('edit_vips') && !$employeeAuth('cancel_vips')
            && !$employeeAuth('exchange_vip_points') && !$employeeAuth('recharge_give_money') && !$employeeAuth('recharge_give_money_revoke')) ? 'opacity: 40%':''"
        >
          <div
          class="membersSet"
            @click="(!$employeeAuth('create_vips') && !$employeeAuth('edit_vips') && !$employeeAuth('cancel_vips')
            && !$employeeAuth('exchange_vip_points') && !$employeeAuth('recharge_give_money') && !$employeeAuth('recharge_give_money_revoke')) ? '':openMemberSetting()"
          >会员设置</div>
        </div>
      </div>
      <div
        v-if="isShortMessageMass"
        class="pc_hed12"
      >
        <div class="pc_hed_short">
          <svg style="margin-right: 8px;margin-left: 16px;" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 0H0V16H16V0Z" fill="white" fill-opacity="0.01"/>
            <path d="M14.6666 7.99967C14.6666 11.6816 11.6819 14.6663 7.99998 14.6663C6.00881 14.6663 1.33331 14.6663 1.33331 14.6663C1.33331
              14.6663 1.33331 9.69041 1.33331 7.99967C1.33331 4.31777 4.31808 1.33301 7.99998 1.33301C11.6819 1.33301 14.6666
              4.31777 14.6666 7.99967Z" stroke="#CFA26B" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M4.66663 6H10.6666" stroke="#CFA26B" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M4.66663 8.66699H10.6666" stroke="#CFA26B" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M4.66663 11.333H7.99996" stroke="#CFA26B" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>剩余 {{shortTotal}} 条
          <div class="pc_hed_short1" style="float: right;" @click="gotoBuyShort">充值</div>
        </div>
        <div @click="gotoShortHistory" class="pc_hed_short2">
          群发记录
        </div>
      </div>
    </div>
    <div
      v-show="autoCashShow"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="autoCashClose"
      ></div>
      <div style="position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;">
        <div style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 35px;">提示</div>
        <div style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;">
          <span style="color:red">*</span>
          <el-input
            type="password"
            ref="payPassword"
            placeholder="请输入登录密码"
            v-model="login_password"
            style="width: 305px;"
            maxlength="32"
          ></el-input>
        </div>
        <div class="pc_hed17">
          <div
            class="pc_hed18"
            style="margin-left: 80px;"
            @click="autoCashClose"
          >取消</div>
          <div
            class="pc_hed19"
            @click="autoCashExit"
          >确定</div>
        </div>
      </div>
    </div>
    <!-- Token过期 -->
    <div
      v-show="tokenExpired"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
    >
      <div style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);" ></div>
      <div style="position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;">
        <div style="width: 100%;text-align: center;font-size: 25px;
        color: #567485;margin-top: 40px;font-weight: normal;">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >{{tokenExpiredMsg}}</div>
        <div class="pc_hed17">
          <div
            class="pc_hed18"
            @click="tokenExpiredAction(0)"
          >再等等</div>
          <div
            class="pc_hed19"
            @click="tokenExpiredAction(1)"
          >重新登录</div>
        </div>
      </div>
    </div>
    <!-- 云同步loading -->
    <div
      v-show="show_loading || isSyncingLogin"
      style="position:fixed; background:rgba(0,0,0,.7); width:100%; height:100%; top: 0px; z-index:99999;"
    >
      <div style="width:300px; margin: 0 auto; margin-top:400px; text-align:center;">
        <img
          alt=""
          src="../image/zgzn-pos/pc_cloud_loading.gif"
          style="width:80px;"
        />
        <div
          id="load_message"
          style="text-align:center; color:#FFF; margin-top:10px; font-size:20px; line-height:22px;"
        >{{load_message}}</div>
      </div>
    </div>
    <div
      v-show="delReLogin"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
      ></div>
      <div
        style="position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: normal;"
        >提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
        >云端数据已清除，请重新登录</div>
        <div class="pc_dtclear1">
          <div class="pc_dtclear2" @click="clearAllData()">确定</div>
        </div>
      </div>
    </div>
    <div
      v-show="tokenReLogin"
      class="tokenDialog"
    >
      <div
        class="dialog_mask"
      ></div>
      <div
        class="tokenDialog--container"
      >
        <div
          class="tokenDialog--text"
        >提示</div>
        <div
          class="tokenDialog--text"
        >用户登录凭证已失效，请重新登录</div>
        <div class="pc_dtclear1">
          <div class="pc_dtclear2" @click="reloadForm()">确定</div>
        </div>
      </div>
    </div>
    <!-- 意见反馈弹窗 -->
    <el-dialog
      :visible.sync="showFeedBackIdea"
      width="450px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">意见反馈</div>
        <div
          class="icon_close"
          @click="showFeedBackIdea = false"
        >×</div>
      </div>
      <div class="idea_input">
        <el-input
          type="textarea"
          maxlength="800"
          placeholder="请输入您宝贵的意见或建议（800字以内）"
          v-model="feedBackContent"
        ></el-input>
      </div>
      <div class="desc">欢迎您为我们提出宝贵的意见和建议，您留下的任何 信息都将用来改善我们的软件。</div>
      <div
        class="btn_feedback"
        @click="feedBack"
      >提 交</div>
      <div style="height:30px"></div>
    </el-dialog>
    <!--联系客服弹窗-->
    <el-dialog
      :visible.sync="showCustomerService"
      width="450px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="pc_header_help3">
        <span>
          联系客服
        </span>
      </div>
      <div class="pc_header_help4">
        <span>
          （客服工作时间：08:00~20:00）
        </span>
      </div>
      <div class="pc_header_help5">
        <span>客服电话：</span>
        <span style="float: right">{{$t('components.header.telephone')}}</span>
      </div>
      <div
        class="btn_feedback"
        @click="closeCustomerService"
      >我知道了</div>
      <div style="height:30px"></div>
    </el-dialog>
    <!--操作视频弹窗-->
    <el-dialog
      :visible.sync="showVideo"
      width="450px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="pc_header_help6">
        <div @click="showVideo = false">×</div>
      </div>
      <div style="text-align: center;">
        <img src="../image/pc_header_video.png" />
      </div>
      <div class="pc_header_help7">
        <span>
          {{$t('components.header.header_user')}}
        </span>
      </div>
      <div class="pc_header_help8">
        <span>
          做生意，就用{{$t('components.header.header_user')}}！
        </span>
      </div>
      <div class="pc_header_help9">
        <span>
          使用最新版抖音扫码，关注@{{$t('components.header.header_user')}}
        </span>
      </div>
      <div class="pc_header_help10">
        <span>
          了解{{$t('components.header.header_user')}}的操作方法
        </span>
      </div>
    </el-dialog>
    <!--关于我们弹窗-->
    <el-dialog
      :visible.sync="showWe"
      width="450px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div style="text-align: center;padding-top: 22px">
        <img src="../image/zgzn-pos/pc_login_logo.png" />
      </div>
      <div class="pc_header_user1">
        <span>
          {{$t('components.header.header_user')}}门店系统（版本 {{version}}）
        </span>
      </div>
      <div style="text-align: center">
        <el-image
          alt=""
          :src="officialAccountCodeImgUrl"
          style="width: 196px;height: 196px;"
        >
          <div slot="error">
            <div class="pc_no_network_div">
              <img src="../image/pc_no_network.png" style="" />
              <div style="width: 196px;">无网络，请检查网络连接</div>
            </div>
          </div>
        </el-image>
      </div>
      <div class="pc_header_user2">
        <span>
          扫描二维码关注{{$t('components.header.accountName')}}
        </span>
      </div>
      <div
        class="btn_feedback"
        @click="showWe=false"
      >我知道了</div>
      <div style="height:30px"></div>
    </el-dialog>
    <!--手机下载弹窗-->
    <el-dialog
      :visible.sync="showPhone"
      width="600px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div
        class="pc_header_help6"
        style="height: 35px"
      >
        <div @click="showPhone = false">×</div>
      </div>
      <div class="pc_header_user4">
        <span>
          手机下载
        </span>
      </div>
      <div class="mobile_container_div">
        <div v-if="iosUrl" class="image_contain_div iosDownloadBg">
          <div class="iso_img_div iosQrCode" ref="iosQrCode"></div>
        </div>
        <div v-if="androidUrl" class="image_contain_div androidDownloadBg">
          <div class="iso_img_div androidQrCode" ref="androidQrCode"></div>
        </div>
      </div>
      <div style="height:41px"></div>
    </el-dialog>
    <!-- 升级弹窗 -->
    <div
      class='upgrade1'
      v-show='showUpgrade'
    >
      <div class='upgrade11'>
        <div class='upgrade12'><span>升级更高版本</span><span
            class='upgrade13'
            @click="exitUpgrade"
          >×</span>
        </div>
        <div class="upgrade14">
          <input
            type="text"
            placeholder="请输入有效激活码"
            v-model="jihuocode"
            maxlength="50"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          />
        </div>
        <!-- <div style="text-align:center;margin-bottom: 10px;font-family: Microsoft YaHei, sans-serif;">
          <a src="#" @click="showVersionCompare" style="color: #CFA26B;cursor: pointer;font-size: 18px;">查看版本功能>></a>
        </div> -->
        <div class="upgrade12">
          <div
            style="background-color: #4C567C;
            width: 130px;
            border-radius: 4px;
            font-size: 20px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            line-height: 50px;
            text-align: center;
            float: left;
            cursor: pointer;
            margin: 10px;
            margin-left: 46px;
          "
            @click="exitUpgrade()"
          >取消</div>
          <div
            class="jihuo"
            @click="jihuo"
          >升级</div>
        </div>
      </div>
    </div>
    <remain-day-tips v-if="remainDayTipsShow" :visible.sync="remainDayTipsShow"></remain-day-tips>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog, Input } from 'element-ui';
import base_change_shifts from '@/components/base_change_shifts.vue';
import { showToast } from '@/utils/util.js';
import QRCode from 'qrcodejs2';
import logList from '@/config/logList';
import { getServiceImgUrl } from '@/api/wxQrCode';
import RemainDayTips from '../page/pc/homeComponents/RemainDayTips.vue';
export default {
  name: 'Header',
  mixins: [base_change_shifts],
  components: {
    [Dialog.name]: Dialog,
    [Input.name]: Input,
    RemainDayTips
  },
  data () {
    return {
      //      控制按钮为右上方新增进货单
      //      新增进货单为true
      //      新增退货单为false
      //      buyOrder: true
      // 退出系统确认
      exit: false,
      gologining: false,
      showStockInvenRemind: false,
      // 云同步loading
      show_cloud_loading: false,
      login_password: '',
      show_loading: false,
      load_message: '正在云同步，请稍后……',
      // showTipsDialog: false,
      tab_index: 1,
      vipArr: ['会员交易明细', '会员价值分析', '会员充值统计', '会员充值明细', '积分变动明细'], // 会员报表顶部title数组
      stockArr: ['库存查询', '库存统计', '变动明细', '盘点明细', '库存预警', '过期预警'], // 库存顶部报表名数组
      vip_index: 0,
      stock_index: 0,
      androidUrl: '',
      iosUrl: '',
      androidQrCode: null,
      iosQrCode: null,
      showFeedBackIdea: false, // 反馈意见弹窗
      feedBackContent: '', // 返回内容
      showVideo: false, // 操作视频窗口
      pointer1: 'el-icon-caret-bottom', // 帮助指示标识
      pointer2: 'el-icon-caret-bottom', // 指示标识
      showMore: false,
      showHelp: false,
      kefuSrc: '',
      showWe: false, // 关于我们窗口
      showPhone: false, // 手机下载窗口
      officialAccountCodeImgUrl: '',
      userInfo: this.$store.state.show.loginInfo,
      ad_detail: {},
      show_delete: false,
      upload_data: [],
      show_ad_txt: true,
      jihuocode: '', // 激活code
      jihuoerchong: false, // 激活二重限制
      localCreatetime: '',
      trial_day: '0', // 剩余体验天数
      edition: {
        name: '简易版',
        background: 'linear-gradient(180deg, #F4EFE5 0%, #F6E8DF 110.56%);',
        url: require('../image/zgzn-pos/pc_header_mfb.png'),
        color: '#567485'
      },
      remainDayTipsShow: false
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    gotoBuyShort() {
      this.SET_SHOW({ isShortMessageMass: false });
      this.SET_SHOW({ msgFrom: 3 });
      this.SET_SHOW({ isShortMessage: true });
      demo.actionLog(logList.smsClickRecharge);
    },
    gotoShortHistory() {
      this.SET_SHOW({ msgFrom: 3 });
      this.SET_SHOW({ tabIndexShort: 3 });
      this.SET_SHOW({isShortMessageMass: false});
      this.SET_SHOW({ isMessageHistory: true });
      demo.actionLog(logList.clickSendSmsBatch);
    },
    openShortMessageMass() {
      this.SET_SHOW({ shortInfo: '' });
      this.SET_SHOW({ shortPhones: '' });
      this.SET_SHOW({ shortNum: 0 });
      this.SET_SHOW({isMember: false});
      this.SET_SHOW({isShortMessageMass: true});
    },
    backToSizeSetting() {
      this.SET_SHOW({isSizeTemplate: false});
      this.SET_SHOW({isSizeSetting: true});
    },
    toColorSetting() {
      this.SET_SHOW({ isSizeSetting: false });
      this.SET_SHOW({ isGoods: false });
    },
    toSizeSetting() {
      this.SET_SHOW({ isSizeSetting: true });
      this.SET_SHOW({ isGoods: false });
    },
    openJiHuo() {
      this.SET_SHOW({ showUpgrade: true });
    },
    goodsUnpack() {
      demo.actionLog(logList.clickGoodsUnpacking);
      this.SET_SHOW({ isGoodsUnpack: true });
      this.SET_SHOW({ isGoods: false });
    },
    showCustomer () {
      this.SET_SHOW({ showCustomerService: true });
    },
    closeCustomerService () {
      this.SET_SHOW({ showCustomerService: false });
    },
    exitUpgrade() {
      this.SET_SHOW({ showUpgrade: false });
    },
    clickTabOnceCard(index) {
      this.SET_SHOW({ tabIndexOnceCard: index });
    },
    clickTabIndexShort(index) {
      this.SET_SHOW({ tabIndexShort: index });
    },
    openMemberSetting () {
      this.SET_SHOW({ selectRow: 1 });
      this.SET_SHOW({ memberIndex: 'first' });
      this.SET_SHOW({ isMemberSetting: true });
      this.SET_SHOW({ isMember: false });
    },
    /**
     * 会员报表顶部tab按钮点击
     */
    clickVipTab (index) {
      this.vip_index = index;
      this.SET_SHOW({ isReport: false });
      this.SET_SHOW({ isVipValueAnalysis: false });
      this.SET_SHOW({ isVipIncreaseStatistics: false });
      this.SET_SHOW({ isVipConsumeCollection: false });
      this.SET_SHOW({ isVipRechargeStatistics: false });
      this.SET_SHOW({ isVipRechargeDetail: false });
      this.SET_SHOW({ isPointsUseDetail: false });
      switch (index) {
        case 0:
          this.SET_SHOW({ isReport: true });
          this.SET_SHOW({ pc_detail_tab: 3 });
          this.SET_SHOW({ fromReport: 1 });
          break;
        case 1:
          demo.actionLog(logList.clickVipValueAnalysis);
          this.SET_SHOW({ isVipValueAnalysis: true });
          break;
        case 2:
          demo.actionLog(logList.clickVipRechargeStatistics);
          this.SET_SHOW({ isVipRechargeStatistics: true });
          break;
        case 3:
          demo.actionLog(logList.clickVipRechargeDetail);
          this.SET_SHOW({ isVipRechargeDetail: true });
          break;
        case 4:
          demo.actionLog(logList.clickPointChangeDetail);
          this.SET_SHOW({ isPointsUseDetail: true });
          break;
        default:
          break;
      }
    },
    /**
     * 库存报表顶部tab按钮点击
     */
    clickStockTab (index) {
      this.stock_index = index;
      this.SET_SHOW({ isStockRecord: false });
      this.SET_SHOW({ isStockStatisticsReport: false });
      this.SET_SHOW({ isStockChangeDetail: false });
      this.SET_SHOW({ isStockCheckDetail: false });
      this.SET_SHOW({ isStockWarningDetail: false });
      this.SET_SHOW({ isOverdueWarningDetail: false });
      switch (index) {
        case 0:
          this.SET_SHOW({ isStockRecord: true });
          break;
        case 1:
          this.SET_SHOW({ isStockStatisticsReport: true });
          break;
        case 2:
          this.SET_SHOW({ isStockChangeDetail: true });
          break;
        case 3:
          this.SET_SHOW({ isStockCheckDetail: true });
          break;
        case 4:
          this.SET_SHOW({ isStockWarningDetail: true });
          break;
        case 5:
          this.SET_SHOW({ isOverdueWarningDetail: true });
          break;
        default:
          break;
      }
    },
    /**
     * 提交反馈意见
     */
    feedBack () {
      if (!this.feedBackContent.trim()) {
        showToast(this, '反馈意见不能为空');
        return;
      }
      let param = {
        'createBy': this.userInfo.phone,
        'suggestion': this.feedBackContent
      };
      demo.$http.post(this.$rest.addFeedback, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          demo.msg('success', '感谢您的反馈！');
          this.showFeedBackIdea = false;
          this.feedBackContent = '';
          return;
        }
        showToast(this, obj.msg);
      });
    },
    /**
     * tab点击事件, index说明
     * 1: 寄存统计
     * 2：寄存明细
     * 3：寄存剩余
     */
    clickTab (index) {
      this.tab_index = index;
      this.SET_SHOW({ isStockStatistics: this.tab_index === 1 });
      this.SET_SHOW({ isDepositRecord: this.tab_index === 2 });
      this.SET_SHOW({ isDepositResidue: this.tab_index === 3 });
    },
    /**
     * 点击取消退出系统
     */
    cancelClose() {
      this.SET_SHOW({ showTipsDialog: false });
      external.closeCancel();
      // this.showTipsDialog = false;
    },
    getKexian(v, t) {
      external.customerdisplay({
        displaydata: v,
        displaytype: t,
        port: this.kexianValue,
        baudrate: '2400',
        databits: '8',
        parity: '0',
        stopBits: '1'
      });
    },
    /**
     * 点击确定退出系统
     */
    confirmLogout () {
      if (this.gologining === true) {
        return;
      }
      if (this.userInfo.uid === undefined) {
        external.closeMainForm();
        return;
      }
      this.gologining = true;
      setTimeout(() => {
        this.gologining = false;
      }, 3000);
      this.SET_SHOW({ showTipsDialog: false });
      setTimeout(() => {
        if (this.showKexian) {
          this.getKexian('', 0);
        }
        if (this.hasWeigh) {
          var data = {};
          external.execScale(data);
        }
        this.SET_SHOW({ changeId: this.nowChangeId });
        this.getChangeShiftsData('exit');
      }, 100);
    },
    backBefore () {
      this.SET_SHOW({ isSetting: false });
    },
    backPrevious () {
      if (this.msgFrom === 0) {
        this.SET_SHOW({ isHome: true });
      } else if (this.msgFrom === 1) {
        this.SET_SHOW({ selectRow: 11 });
        this.SET_SHOW({ isMemberSetting: true });
      } else {
        this.SET_SHOW({ isShortMessageMass: true });
      }
      this.SET_SHOW({ isShortMessage: false });
      this.SET_SHOW({ isMessageHistory: false });
    },
    sortBack() { // 商品排序页退出
      this.SET_SHOW({ isGoodsSort: false, isPay: true });
    },
    backMember () {
      this.SET_SHOW({
        isMemberSetting: false,
        showMemberGoods: false,
        isShortMessageMass: false,
        isMember: true
      });
    },
    logout () {
      this.SET_SHOW({ ifLogin: false });
      this.SET_SHOW({ sys_uid: '' });
      this.SET_SHOW({ sys_sid: '' });
      this.SET_SHOW({ token: '' });
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isHeader: false });
      this.SET_SHOW({ isLogin: true });
      this.SET_SHOW({ isMember: false });
      this.SET_SHOW({ isMemberSetting: false });
    },
    back () {
      if (this.isSetting && this.hasWeigh) {
        if (this.weighValue === '') {
          demo.msg('warning', '电子秤端口号不能为空');
          return;
        }
        if (this.weighTypeValue === '') {
          demo.msg('warning', '电子秤类型不能为空');
          return;
        }
      }
      if (this.isKexian()) {
        return;
      }
      if (this.isSetting) {
        this.$event.$emit('closeSetting');
      } else {
        this.SET_SHOW({
          isHome: true,
          isGoods: false,
          isEmployee: false,
          showAddEmployee: 'close',
          isCheck: false,
          isDetail: false,
          isStock: false,
          isPay: false,
          isSetting: false,
          isMember: false,
          isReport: false,
          isOnceCard: false,
          isChangeShifts: false,
          isReportForms: false,
          isMemberSetting: false,
          showSelManage: false,
          showUnitManage: false,
          showBrandManage: false,
          showAddMember: 'close',
          isSizeSetting: false,
          showScalesManage: false,
          isSizeTemplate: false
        });
      }
      this.showStockInvenRemind = false;
      this.judgeExitIsAddGoods();
    },
    returnPay() {
      this.SET_SHOW({ isDetail: false, isPay: true, pc_detail_tab: 1, fromDetail: 0 });
    },
    backLogin() {
      this.SET_SHOW({ isLogin: true, isHeader: false, isChooseIndustry: false });
    },
    showPhoneDownload() {
      demo.$http.post(this.$rest.getConfigInfo)
        .then(res => {
          this.showPhone = true;
          let configInfo = demo.t2json(res.data.data.configInfo).Base.OtherOptions;
          this.loadAndroidQrCode(configInfo);
          this.loadIosQrCode(configInfo);
        })
        .catch(() => {
          this.showPhone = true;
          let configInfo = $config.Base.OtherOptions;
          this.loadAndroidQrCode(configInfo);
          this.loadIosQrCode(configInfo);
        });
    },
    loadAndroidQrCode(configInfo) {
      if (configInfo.mobileDownload && configInfo.mobileDownload.android &&
        configInfo.mobileDownload.android.isOn && configInfo.mobileDownload.android.imageUrl) {
        this.androidUrl = configInfo.mobileDownload.android.imageUrl;
        this.$nextTick(() => {
          var androidQrCode = {
            text: configInfo.mobileDownload.android.imageUrl,
            width: 92,
            height: 92,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          }
          if (this.androidQrCode) {
            this.$refs.androidQrCode.innerHTML = '';
          }
          this.androidQrCode = new QRCode(this.$refs.androidQrCode, androidQrCode);
        });
      }
    },
    loadIosQrCode(configInfo) {
      if (configInfo.mobileDownload && configInfo.mobileDownload.ios &&
        configInfo.mobileDownload.ios.isOn && configInfo.mobileDownload.ios.imageUrl) {
        this.iosUrl = configInfo.mobileDownload.ios.imageUrl;
        this.$nextTick(() => {
          var iosQrCode = {
            text: configInfo.mobileDownload.ios.imageUrl,
            width: 92,
            height: 92,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          }
          if (this.iosQrCode) {
            this.$refs.iosQrCode.innerHTML = '';
          }
          this.iosQrCode = new QRCode(this.$refs.iosQrCode, iosQrCode);
        });
      }
    },
    isKexian() {
      if (this.isSetting && this.showKexian && (this.kexianValue === '' || this.kexianValue === '请选择')) {
        demo.msg('warning', '客显地址不能为空');
        return true;
      }
      if (this.isSetting && this.usernameHeader === '') {
        demo.msg('warning', '店铺名称不能为空');
        return true;
      }
      return false;
    },
    backChangeShifts () {
      if (this.showDetailDialog || this.showTableDialog) {
        this.SET_SHOW({ showDetailDialog: false });
        this.SET_SHOW({ showTableDialog: false });
        return;
      }
      if (this.changeFrom === 'changeShiftsRecord') {
        this.SET_SHOW({ isChangeShiftsRecord: true });
        this.SET_SHOW({ changeNumber: this.$store.state.show.loginInfo.employeeNumber });
      } else {
        this.SET_SHOW({ isHome: true });
      }
      this.SET_SHOW({ isChangeShifts: false });
    },
    backReportForms () {
      this.SET_SHOW({ isReportForms: true });
      this.SET_SHOW({ isDetail: false });
      this.SET_SHOW({ isReport: false });
      this.SET_SHOW({ tabIndexOnceCard: 1 });
      this.SET_SHOW({ isOnceCard: false });
      this.SET_SHOW({ isStockStatistics: false });
      this.SET_SHOW({ isVipValueAnalysis: false });
      this.SET_SHOW({ isVipIncreaseStatistics: false });
      this.SET_SHOW({ isVipConsumeCollection: false });
      this.SET_SHOW({ isVipRechargeStatistics: false });
      this.SET_SHOW({ isVipRechargeDetail: false });
      this.SET_SHOW({ isPointsUseDetail: false });
      this.SET_SHOW({ isStockRecord: false });
      this.SET_SHOW({ isStockStatisticsReport: false });
      this.SET_SHOW({ isStockChangeDetail: false });
      this.SET_SHOW({ isStockCheckDetail: false });
      this.SET_SHOW({ isStockWarningDetail: false });
      this.SET_SHOW({ isOverdueWarningDetail: false });
      this.SET_SHOW({ settlement: false });
      demo.screen2({
        'screen2ShowList': []
      }, 996);
      this.vip_index = 0;
      this.stock_index = 0;
    },
    /**
     * 返回到报表菜单页
     */
    backToReportForms () {
      this.tab_index = 1;
      this.SET_SHOW({ isReportForms: true });
      this.SET_SHOW({ isStockStatistics: false });
      this.SET_SHOW({ isChangeShiftsRecord: false });
      this.SET_SHOW({ isDepositRecord: false });
      this.SET_SHOW({ isDepositResidue: false });
    },
    /**
     * 返回到进/退货页面
     */
    backToBuyOrder () {
      this.SET_SHOW({ buyOrder: this.$employeeAuth('purchase') });
      // this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isStock: true });
      this.SET_SHOW({ isSupplierManage: false });
      this.SET_SHOW({ suppliersDetail: [] });
      this.SET_SHOW({ showAddEditSupplier: false });
    },
    /**
     * 返回到商品管理页面
     */
    backToGoods () {
      if (this.pddGoodListLength > 0) {
        this.SET_SHOW({ confirmInvToGood: true });
      } else {
        this.SET_SHOW({ isGoods: true });
        this.SET_SHOW({ isStockInventory: false });
        this.SET_SHOW({ showAddGoods: false });
      }
      this.SET_SHOW({ isSizeSetting: false, showScalesManage: false });
    },
    backGoodsFromScales() {
      this.SET_SHOW({ isGoods: true, showScalesManage: false });
    },
    /**
     * 商品回收站返回到商品管理页面
     */
    recycleBackToGoods () {
      this.SET_SHOW({ isGoods: true });
      this.SET_SHOW({ isGoodsUnpack: false });
    },
    toHomeFromStock () {
      this.SET_SHOW({ isHome: true });
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isEmployee: false });
      this.SET_SHOW({ isCheck: false });
      this.SET_SHOW({ isDetail: false });
      this.SET_SHOW({ isStock: false });
      this.SET_SHOW({ isPay: false });
      this.SET_SHOW({ isSetting: false });
      this.SET_SHOW({ isMember: false });
      this.SET_SHOW({ isMemberSetting: false });
      this.judgeExitIsAddGoods();
      /*       if (this.stockListLength > 0) {
        this.SET_SHOW({ stockDelList3: true });
      } else {
        this.SET_SHOW({ isHome: true });
        this.SET_SHOW({ isGoods: false });
        this.SET_SHOW({ isEmployee: false });
        this.SET_SHOW({ isCheck: false });
        this.SET_SHOW({ isDetail: false });
        this.SET_SHOW({ isStock: false });
        this.SET_SHOW({ isPay: false });
        this.SET_SHOW({ isSetting: false });
        this.SET_SHOW({ isMember: false });
        this.SET_SHOW({ isMemberSetting: false });
        this.judgeExitIsAddGoods();
      } */
    },
    judgeExitIsAddGoods() {
      if (this.showAddGoods) {
        this.SET_SHOW({ goodsDetail: [] });
        this.SET_SHOW({ stockCode: '' });
        this.SET_SHOW({ addGoodsUnitId: '' });
        this.SET_SHOW({ addGoodsUnit: '' });
        this.SET_SHOW({ addGoodsCategoryId: '1' });
        this.SET_SHOW({ addGoodsCategory: '其他分类' });
        this.SET_SHOW({ showAddGoods: false });
      }
    },
    payBack () {
      if (this.payListLength > 0) {
        this.SET_SHOW({ showPayExitMsg: true });
      } else {
        this.SET_SHOW({ isHome: true });
        this.SET_SHOW({ isPay: false });
      }
    },
    check () {
      this.SET_SHOW({ isCheck: true });
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isDetail: false });
    },
    detail () {
      this.SET_SHOW({ isDetail: true });
      this.SET_SHOW({ isCheck: false });
      this.SET_SHOW({ isGoods: false });
    },
    setClockScreenConfirm () {
      this.SET_SHOW({ pcLockScreenConfirm: true });
      this.$_actionLog(logList.losckScreen, `点击锁屏按钮`)
    },
    closeLockScreenConfirm() {
      this.SET_SHOW({ pcLockScreenConfirm: false });
    },
    setClockScreen () {
      this.SET_SHOW({ pcLockScreen: true, pcLockScreenConfirm: false });
      this.showMore = false;
      this.$_actionLog(logList.losckScreen, `锁屏弹窗确认`)
    },
    weight () {
      demo.msg('warning', '未连接电子秤');
    },
    toReturnGoods () {
      if (!this.ifautoCash) {
        if (this.payListLength > 0) {
          this.SET_SHOW({ showPayChangeStatus1: true });
        } else {
          this.SET_SHOW({ pc_return_goods: true });
        }
      }
      demo.actionLog(logList.backGoods);
    },
    setPcReturnGoods () {
      if (this.payListLength > 0) {
        this.SET_SHOW({ showPayChangeStatus2: true });
      } else {
        this.SET_SHOW({ pc_return_goods: false });
      }
    },
    checkUltimate () {
      if (this.ultimate === null) {
        this.SET_SHOW({isBuySoftware: true});
      }
    },
    openMoneybox () {
      let _this = this;
      if (_this.setting_hasmoneybox + '' === 'false') {
        demo.msg('warning', '没有钱箱设备，请确认');
        return;
      }
      try {
        if (
          _this.setting_moneybox == null ||
          _this.setting_moneybox.trim() === ''
        ) {
          if (
            _this.setting_small_printer == null ||
            _this.setting_small_printer.trim() === ''
          ) {
            demo.msg('warning', _this.$msg.select_printer);
            return;
          }
          external.openCashBox(
            _this.setting_small_printer,
            function () {
              //
              _this.$_actionLog(logList.openMoneybox, `F11打开钱箱成功`)
            },
            function () {
              demo.msg('warning', _this.$msg.cash_box_in_error);
              _this.$_actionLog(logList.openMoneybox, `F11打开钱箱失败,${_this.$msg.cash_box_in_error}`)
            }
          );
        } else {
          external.openCashBox_separate(
            _this.setting_moneybox,
            2400,
            8,
            function () {
              //
              _this.$_actionLog(logList.openMoneybox, `F11打开钱箱成功`)
            },
            function () {
              demo.msg('warning', _this.$msg.cash_box_in_error);
              _this.$_actionLog(logList.openMoneybox, `F11打开钱箱失败,${_this.$msg.cash_box_in_error}`)
            }
          );
        }
      } catch (e) {
        demo.msg('warning', '钱箱异常，请检查钱箱');
      }
    },
    clearAllData() {
      // 调用C#删除后台
      var that = this;
      var subData = {
        islocalonly: '1'
      }
      that.$log.info('clearData', subData);
      external.clearData(subData, () => {
        external.reloadForm();
      }, res => {
        that.SET_SHOW({ delReLogin: false });
        let msg = '更新失败,请重启' + this.$t('components.header.header_user') + '客户端!';
        demo.msg('error', msg);
        that.$log.info('clearData', {'error': res});
      });
    },
    reloadForm() {
      external.reloadForm();
    },
    sync () {
      demo.actionLog(logList.clickCloudSync);
      var that = this;
      // 如果正在执行云同步，则不继续执行
      if (that.isSyncing) {
        return;
      }
      that.show_cloud_loading = true;
      syncService.clearDataInfo(
        function (msg) {
          that.show_cloud_loading = false;
          if (demo.isNullOrTrimEmpty(msg) || typeof (msg) === "object") {
            msg = that.$msg.sync_success;
          }
          demo.msg('success', msg);
          that.$store.dispatch('info/getSpec');
          that.SET_SHOW({ homeF5: true });
        },
        function (msg) {
          that.show_cloud_loading = false;
          if (demo.isNullOrTrimEmpty(msg) || typeof (msg) === "object") {
            msg = that.$msg.sync_failure;
          }
          demo.msg('warning', msg);
        }
      );
    },
    help() {
      demo.actionLog(logList.clickCustomerService);
      this.showHelp = true;
      if (this.showKefu) {
        this.getHelpQrCode();
      }
    },
    clearQRCode() {
      if (this.$refs.kefuSrcUrl) {
        this.$refs.kefuSrcUrl.innerHTML = '';
      }
    },
    getHelpQrCode() {
      if (pos.network.isConnected()) {
        const param = {
          type: 'service'
        }
        this.$http.get(this.$rest.getWeComUrl, {
          params: param
        })
          .then(res => {
            this.clearQRCode();
            let qr_data = {
              text: res.data.data.weComUrl,
              width: 160,
              height: 160,
              colorDark: '#000000',
              colorLight: '#ffffff',
              correctLevel: QRCode.CorrectLevel.H
            };
            try {
              this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
              settingService.put({ key: 'serviceImgUrl', value: res.data.data.weComUrl });
            } catch (error) {
              this.getKefuCacheCodeImg();
            }
          })
          .catch(() => {
            this.getKefuCacheCodeImg();
          });
      } else {
        this.getKefuCacheCodeImg();
      }
    },
    async getKefuCacheCodeImg() {
      const serviceImgUrl = await getServiceImgUrl();
      if (!serviceImgUrl) {
        return;
      }
      this.clearQRCode();
      let qr_data = {
        text: serviceImgUrl,
        width: 160,
        height: 160,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
    },
    setting () {
      this.SET_SHOW({ selectRow: 1 });
      this.SET_SHOW({ isSetting: true });
      this.SET_SHOW({ isHome: false });
    },
    setBuyOrder (n) {
      if ((!this.$employeeAuth('purchase') && n === 2) || (!this.$employeeAuth('return_purchase') && n === 1)) {
        return;
      }
      if (
        (this.buyOrder === true && n === 2) ||
        (this.buyOrder === false && n === 1)
      ) {
        return;
      }
      if (n === 1) {
        this.SET_SHOW({ buyOrder: false });
      } else {
        this.SET_SHOW({ buyOrder: true });
      }
      /*       if (this.stockListLength > 0) {
        this.SET_SHOW({ stockDelList: true });
      } else {
        if (n === 1) {
          this.SET_SHOW({ buyOrder: false });
        } else {
          this.SET_SHOW({ buyOrder: true });
        }
      } */
    },
    supplierManage () {
      // this.SET_SHOW({ buyOrder: false });
      this.SET_SHOW({ isStock: false });
      this.SET_SHOW({ isSupplierManage: true });
      /*       if (this.stockListLength > 0) {
        this.SET_SHOW({ stockDelList2: true });
      } else {
        this.SET_SHOW({ buyOrder: false });
        this.SET_SHOW({ isStock: false });
        this.SET_SHOW({ isSupplierManage: true });
      } */
    },
    toStockInventory () {
      demo.actionLog(logList.clickStockInventory);
      this.showStockInvenRemind = true;
    },
    continueToStockInven () {
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isStockInventory: true });
      this.showStockInvenRemind = false;
    },
    doautoCash () {
      let _this = this;
      if (_this.ifautoCash) {
        _this.SET_SHOW({ autoCashShow: true });
        setTimeout(function () {
          _this.$refs.payPassword.focus();
        }, 500);
      } else {
        if (!pos.network.isConnected()) {
          demo.msg('warning', '未联网，无法自助收银');
          return;
        }
        _this.SET_SHOW({
          ifautoCash: !_this.ifautoCash
        });
      }
      this.$_actionLog(logList.autoCash, `${this.ifautoCash ? '开启' : '关闭'}自助收银`);
    },
    autoCashClose () {
      this.SET_SHOW({ autoCashShow: false });
      this.login_password = '';
    },
    autoCashExit () {
      let _this = this;
      if (_this.login_password.trim() === '') {
        demo.msg('warning', _this.$msg.enter_password);
        return;
      }
      if (md5(_this.login_password) === _this.passwordHeader) {
        _this.login_password = '';
        _this.SET_SHOW({ ifautoCash: false, autoCashShow: false });
      } else {
        demo.msg('error', _this.$msg.password_error);
      }
    },
    setPointerUp (index) {
      let that = this;
      if (index === 1) {
        that.pointer1 = 'el-icon-caret-top';
      } else {
        that.pointer2 = 'el-icon-caret-top';
      }
    },
    setPointerDown (index) {
      let that = this;
      if (index === 1) {
        that.pointer1 = 'el-icon-caret-bottom';
      } else {
        that.pointer2 = 'el-icon-caret-bottom';
      }
    },
    showWeClick() {
      if (pos.network.isConnected()) {
        let clientId = md5(external.getMac());
        let param = {
          systemName: $config.systemName,
          subName: $config.subName,
          from: 2,
          clientId: clientId
        };
        if ($setting.useruid) {
          param['sysUid'] = $setting.useruid;
        }
        demo.$http
          .post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmCreateQrCode, param)
          .then(res => {
            console.log(res);
            if (res.data.code === 0 && res.data.data) {
              this.officialAccountCodeImgUrl = res.data.data.qrCodeUrl;
              settingService.setInfoValueForKey('official_account_code_img_url', this.officialAccountCodeImgUrl); // 缓存图片地址
              this.showWe = true;
            } else {
              this.getCacheCodeImg();
            }
          })
          .catch(() => { this.getCacheCodeImg(); });
      } else {
        this.getCacheCodeImg();
      }
    },
    getCacheCodeImg() {
      this.officialAccountCodeImgUrl = demo.t2json($setting.info).official_account_code_img_url || '';
      this.showWe = true;
    },
    openPrintSetting() {
      this.showMore = false;
      this.SET_SHOW({ isPrintSetting: true });
      demo.actionLog(logList.accessoriesSetting);
    },
    goDetail() {
      if (this.payListLength > 0) {
        this.SET_SHOW({ showDetail: true });
      } else {
        this.SET_SHOW({ isPay: false, isDetail: true, pc_detail_tab: 1, fromDetail: 0 });
      }
    },
    openGoodsSort() {
      this.showMore = false;
      if (this.payListLength > 0) {
        this.SET_SHOW({ showGoodsSort: true });
      } else {
        this.SET_SHOW({ isPay: false, isGoodsSort: true });
      }
    },
    selectText(n) {
      document.getElementById('ad' + n).select();
    },
    autoUpdateUser() {
      const param = {
        'phone': this.sys_uid
      };
      if (pos.network.isConnected() && this.sys_uid) {
        demo.$http.post(this.$rest.autoUpdateUser, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.code === 200) {
              this.SET_SHOW({ isAuto: res.data.data });
            }
          });
      }
    },
    sonarJihuo(res) {
      this.SET_SHOW({ period: res.data.data.period });
      this.trial_day = +res.data.data.remainDay + 1;
      this.SET_SHOW({ trialDay: this.trial_day });
      this.SET_SHOW({ sysEndDate: res.data.data.endDate });
      let ultimate = (this.trial_day < 0 && !(this.period === -1)) ? null : JSON.parse(res.data.data.ultimate);
      this.SET_SHOW({ ultimate: ultimate });
      this.show_trial = this.period !== -1;
      this.setEdition();
      this.SET_SHOW({ showUpgrade: false });
      settingService.put([
        { key: settingService.key.period, value: this.period },
        { key: settingService.key.enddate, value: res.data.data.endDate },
        { key: settingService.key.ultimate, value: ultimate }
      ], () => {
        // 没有插入成功回调
      });
      this.SET_SHOW({showTemporaryTip: false});
      this.autoUpdateUser();
    },
    // 激活
    jihuo () {
      if (this.jihuoerchong) {
        return;
      } else {
        setTimeout(() => {
          this.jihuoerchong = false;
        }, 1500);
      }
      if (!this.jihuocode.trim()) {
        demo.msg('warning', '请输入有效激活码');
        return;
      }
      this.jihuoerchong = true;
      this.$http.post(this.$rest.updateCode, {
        'phone': this.sys_uid,
        'activeCode': this.jihuocode,
        'systemName': $config.systemName,
        'subName': $config.subName
      }, {
        headers: { 'Content-Type': 'application/json' }
      }).then(res => {
        console.log(res, '版本升级res');
        if (res.data === undefined) {
          demo.msg('error', "无法连接远端服务器,处于离线状态");
        } else if (res.data.code === 200) {
          this.show_upgrade = false;
          demo.msg('success', '绑定激活码成功');
          demo.actionLog(logList.upgradeSuccess);
          this.sonarJihuo(res);
        } else {
          demo.msg('warning', res.data.msg);
        }
        this.jihuoerchong = false;
      }).catch(error => {
        this.jihuoerchong = false;
        demo.msg('error', error);
      });
    },
    // 显示版本功能对比页
    showVersionCompare () {
      if (this.ultimate !== null && this.trialDay <= 4) {
        this.remainDayTipsShow = true;
      } else {
        this.SET_SHOW({ isVesionCompare: true });
      }
    },
    setEdition() {
      if (this.ultimate === true) {
        this.edition = {
          name: '旗舰版',
          background: this.$t('image.homeImage.editionBackground'),
          url: require('../image/star3.png'),
          color: this.$t('image.homeImage.editionColor')
        };
      } else if (this.ultimate === false) {
        this.edition = {
          name: '专业版',
          background: 'linear-gradient(180deg, #F4EFE5 0%, #F6E8DF 110.56%)',
          url: require('../image/star.png'),
          color: '#957159'
        };
      } else {
        this.edition = {
          name: '简易版',
          background: 'linear-gradient(180deg, #D5E0E8 0%, #C1C9CF 110.56%)',
          url: require('../image/star2.png'),
          color: '#567485'
        };
      }
    },
    addShortCutKey(e) {
      this.showMore = false;
      var code = e.which;
      if (code === 27) {
        console.log('addShortCutKey');
        if (this.isMessageHistory) {
          this.backPrevious();
        } else if (this.isPay && !this.isSetting && !this.ifautoCash) {
          console.log(1)
        } else if (this.isSetting && (this.isGoods || this.isDetail || this.isPay)) {
          this.backBefore();
        } else if (this.isGoodsUnpack) {
          this.recycleBackToGoods();
        } else if (this.isShortMessageMass || this.isMemberSetting) {
          this.backMember();
        } else {
          this.sonarAddShortCutKey();
        }
      }
    },
    sonarAddShortCutKey() {
      if (this.isStockInventory || this.isSizeSetting || this.showScalesManage) {
        this.backToGoods();
      } else if (this.judgeIsBack()) {
        this.back();
      } else if (this.isStock) {
        this.toHomeFromStock();
      } else if (this.isChangeShifts) {
        this.backChangeShifts();
      } else if (this.isDepositRecord || this.isChangeShiftsRecord || this.isDepositResidue) {
        this.backToReportForms();
      } else if (this.isSupplierManage) {
        this.backToBuyOrder();
      } else if (this.judgeIsBackReportForms() && !this.isPartlyBack) {
        this.backReportForms();
      } else if (this.isMember) {
        this.SET_SHOW({ memberShortCutExit: true });
      } else {
        console.log('do nothing');
      }
    },
    judgeIsBack() {
      let flg = this.isCheck || this.isSetting || this.isEmployee ||
        (this.isGoods && !this.showAddGoods && !this.isSetting && !this.isPrintSetting) || (this.isReport && this.fromReport === 0) ||
        (this.isDetail && this.fromDetail === 0) || this.isReportForms;
      return flg;
    },
    judgeIsBackReportForms() {
      let flg = this.isStockStatistics || (this.isReport && this.fromReport === 1) ||
        this.isOnceCard || (this.isDetail && !this.isSetting && this.fromDetail === 1) || this.isVipRechargeDetail ||
        this.isPointsUseDetail || this.isVipRechargeStatistics || this.isVipConsumeCollection ||
        this.isVipValueAnalysis || this.isStockRecord || this.isStockStatisticsReport ||
        this.isStockChangeDetail || this.isStockCheckDetail || this.isStockWarningDetail || this.isOverdueWarningDetail ||
        this.isVipIncreaseStatistics;
      return flg;
    },
    tokenExpiredAction(flag) {
      if (flag === 0) {
        this.SET_SHOW({ tokenExpired: false, isSyncing: false });
      } else {
        external.reloadMainForm();
      }
    }
  },
  computed: mapState({
    usernameHeader: state => state.show.username,
    isHome: state => state.show.isHome,
    isShortMessage: state => state.show.isShortMessage,
    isGoods: state => state.show.isGoods,
    isEmployee: state => state.show.isEmployee,
    isCheck: state => state.show.isCheck,
    isDetail: state => state.show.isDetail,
    isPartlyBack: state => state.show.isPartlyBack,
    isStock: state => state.show.isStock,
    isChangeShifts: state => state.show.isChangeShifts,
    isReportForms: state => state.show.isReportForms,
    isStockStatistics: state => state.show.isStockStatistics,
    isChangeShiftsRecord: state => state.show.isChangeShiftsRecord,
    isDepositRecord: state => state.show.isDepositRecord,
    isDepositResidue: state => state.show.isDepositResidue,
    isVipValueAnalysis: state => state.show.isVipValueAnalysis,
    isVipIncreaseStatistics: state => state.show.isVipIncreaseStatistics,
    isVipConsumeCollection: state => state.show.isVipConsumeCollection,
    isVipRechargeStatistics: state => state.show.isVipRechargeStatistics,
    isVipRechargeDetail: state => state.show.isVipRechargeDetail,
    isStockRecord: state => state.show.isStockRecord,
    isStockStatisticsReport: state => state.show.isStockStatisticsReport,
    isStockChangeDetail: state => state.show.isStockChangeDetail,
    isStockCheckDetail: state => state.show.isStockCheckDetail,
    isStockWarningDetail: state => state.show.isStockWarningDetail,
    isOverdueWarningDetail: state => state.show.isOverdueWarningDetail,
    isSupplierManage: state => state.show.isSupplierManage,
    isStockInventory: state => state.show.isStockInventory,
    pddGoodListLength: state => state.show.pddGoodListLength,
    isPointsUseDetail: state => state.show.isPointsUseDetail,
    isPay: state => state.show.isPay,
    isLogo: state => state.show.isLogo,
    isHelp: state => state.show.isHelp,
    isUser: state => state.show.isUser,
    isSetting: state => state.show.isSetting,
    isMember: state => state.show.isMember,
    isReport: state => state.show.isReport,
    isOnceCard: state => state.show.isOnceCard,
    pcDetailTab: state => state.show.pc_detail_tab,
    pcLockScreen: state => state.show.pcLockScreen,
    pcLockScreenConfirm: state => state.show.pcLockScreenConfirm,
    pc_return_goods: state => state.show.pc_return_goods,
    buyOrder: state => state.show.buyOrder,
    payListLength: state => state.show.payListLength,
    setting_small_printer: state => state.show.setting_small_printer,
    setting_moneybox: state => state.show.setting_moneybox,
    setting_hasmoneybox: state => state.show.setting_hasmoneybox,
    stockListLength: state => state.show.stockListLength,
    confirmInvToGood: state => state.show.confirmInvToGood,
    isSyncing: state => state.show.isSyncing,
    ifautoCash: state => state.show.ifautoCash,
    isGoodsSort: state => state.show.isGoodsSort,
    autoCashShow: state => state.show.autoCashShow,
    passwordHeader: state => state.show.password,
    aid: state => state.show.aid,
    app_secret: state => state.show.app_secret,
    token: state => state.show.token,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    isSyncingLogin: state => state.show.isSyncingLogin,
    fromReport: state => state.show.fromReport,
    fromDetail: state => state.show.fromDetail,
    ultimate: state => state.show.ultimate,
    loginInfo: state => state.show.loginInfo,
    showPrintSetting: state => state.show.showPrintSetting,
    showPaySetting: state => state.show.showPaySetting,
    shortCutList: state => state.show.shortCutList,
    isMemberSetting: state => state.show.isMemberSetting,
    tabIndexOnceCard: state => state.show.tabIndexOnceCard,
    screenWidth: state => state.show.screenWidth,
    changeFrom: state => state.show.changeFrom,
    showTipsDialog: state => state.show.showTipsDialog,
    period: state => state.show.period,
    delReLogin: state => state.show.delReLogin,
    tokenReLogin: state => state.show.tokenReLogin,
    msgFrom: state => state.show.msgFrom,
    isMessageHistory: state => state.show.isMessageHistory,
    showCustomerService: state => state.show.showCustomerService,
    tabIndexShort: state => state.show.tabIndexShort,
    showUpgrade: state => state.show.showUpgrade,
    isVesionCompare: state => state.show.isVesionCompare,
    showAddGoods: state => state.show.showAddGoods,
    isSizeSetting: state => state.show.isSizeSetting,
    isPrintSetting: state => state.show.isPrintSetting,
    isSizeTemplate: state => state.show.isSizeTemplate,
    kexianValue: state => state.show.kexianValue,
    hasWeigh: state => state.show.hasWeigh,
    showKexian: state => state.show.showKexian,
    tokenExpired: state => state.show.tokenExpired,
    setRecycle: state => state.show.setRecycle,
    isGoodsUnpack: state => state.show.isGoodsUnpack,
    isChooseIndustry: state => state.show.isChooseIndustry,
    tokenExpiredMsg: state => state.show.tokenExpiredMsg,
    isShortMessageMass: state => state.show.isShortMessageMass,
    memberShortCutExit: state => state.show.memberShortCutExit,
    showKefu: state => state.show.showKefu,
    trialDay: state => state.show.trialDay,
    storeList: state => state.show.storeList,
    shortTotal: state => state.show.shortTotal,
    showShortCutList: state => state.show.showShortCutList,
    weighValue: state => state.show.weighValue,
    weighTypeValue: state => state.show.weighTypeValue,
    nowChangeId: state => state.show.nowChangeId,
    showDetailDialog: state => state.show.showDetailDialog,
    showScalesManage: state => state.show.showScalesManage,
    showTableDialog: state => state.show.showTableDialog
  }),
  created () {
    this.version = this.$rest.version;
  },
  mounted: function () {
    this.setEdition();
    this.SET_SHOW({ isLogo: true });
    document.body.addEventListener('keyup', this.addShortCutKey, false);
    this.$event.$on('stockTabChange', index => {
      console.warn('stockTabChange', index)
      this.stock_index = index;
    })
  },
  watch: {
    isStockRecord() {
      if (this.isStockRecord === true) {
        this.stock_index = 0;
      }
    },
    showTipsDialog() {
      if (this.showTipsDialog && this.pcLockScreen) {
        demo.msg('warning', '为了账户安全，请先解锁后再退出系统');
        this.SET_SHOW({showTipsDialog: false});
        external.closeCancel();
      }
    },
    showShortCutList() {
      if (this.showShortCutList) {
        this.SET_SHOW({showShortCutList: false});
        this.$refs.shortCutPopover.doShow();
      }
    },
    ultimate() {
      this.setEdition();
    },
    period() {
      this.setEdition();
    },
    trialDay() {
      this.setEdition();
    }
  },
  beforeDestroy() {
    this.$event.$off('stockTabChange');
    document.body.removeEventListener('keyup', this.addShortCutKey, false);
  }
};
</script>
