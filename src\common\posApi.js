import rest from '../config/rest';
var chrome = {
  notify: function() {
    // do nothing
  },
  debug: function() {
    return external.showDevTools();
  }
};
var pollPrint;
var db = {
  query: function(sql, onSuccess, onFail) {
    return sqlitePlugin.executeQuery(sql, onSuccess, onFail);
  },
  executeSqlBatch: function(sql, onSuccess, onFail) {
    sqlitePlugin.executeSqlBatch_sqls(sql, onSuccess, onFail);
  }
};
var eventBackButton = function() {
  // confirm('再点击一次退出!');
  window.plugins.toast.showShortBottom('再点击一次退出!');
  document.removeEventListener('backbutton', eventBackButton, false); // 注销返回键
  document.addEventListener('backbutton', exitApp, false); // 绑定退出事件
  // 3秒后重新注册
  var intervalID = window.setInterval(function() {
    window.clearInterval(intervalID);
    document.removeEventListener('backbutton', exitApp, false); // 注销返回键
    document.addEventListener('backbutton', eventBackButton, false); // 返回键
  }, 3000);

  function exitApp() {
    navigator.app.exitApp();
  }
};
var network = {
  isConnected: function() {
    return external.isConnected();
  },
  checkConnection: function() {
    var networkState = navigator.connection.type;
    var states = {};
    states[Connection.UNKNOWN] = 'Unknown connection';
    states[Connection.ETHERNET] = 'Ethernet connection';
    states[Connection.WIFI] = 'WiFi connection';
    states[Connection.CELL_2G] = 'Cell 2G connection';
    states[Connection.CELL_3G] = 'Cell 3G connection';
    states[Connection.CELL_4G] = 'Cell 4G connection';
    states[Connection.CELL] = 'Cell generic connection';
    states[Connection.NONE] = 'No network connection';
    if (states[networkState] === 'No network connection' || states[networkState] === 'Unknown connection') {
      return false;
    }
    return true;
  },
  getMac: function() {
    return external.getMac();
  }
};
var print_cols = {
  58: 32,
  80: 48,
  '': 32,
  undefined: 32
};
var Cols = print_cols['58'];
String.prototype.gblen = function() {
  var len = 0;
  for (var i = 0; i < this.length; i++) {
    if (this.charCodeAt(i) > 127 || this.charCodeAt(i) === 94) {
      len += 2;
    } else {
      len++;
    }
  }
  return len;
};
String.prototype.hidden = function(frontLen, endLen) {
  var len = this.length - frontLen - endLen;
  var xing = '';
  for (var i = 0; i < len; i++) {
    xing += '*';
  }
  return this.substring(0, frontLen) + xing + this.substring(this.length - endLen);
};
String.prototype.autoAddEllipsis = function(pLen) {
  var _ret = cutString(this, pLen);
  return _ret.cutstring;
};
function cutString(pStr, pLen) {
  // 原字符串长度
  var _strLen = pStr.length;
  var _cutString;
  // 默认情况下，返回的字符串是原字符串的一部分
  var _cutFlag = '1';
  var _lenCount = 0;
  var _ret = false;
  if (_strLen <= pLen / 2) {
    _cutString = pStr;
    _ret = true;
  }
  if (!_ret) {
    for (var i = 0; i < _strLen; i++) {
      if (isFull(pStr.charAt(i))) {
        _lenCount += 2;
      } else {
        _lenCount += 1;
      }
      if (_lenCount > pLen) {
        _cutString = pStr.substring(0, i);
        _ret = true;
        break;
      } else if (_lenCount === pLen) {
        _cutString = pStr.substring(0, i + 1);
        _ret = true;
        break;
      } else {
        // todo
      }
    }
  }
  if (!_ret) {
    _cutString = pStr;
    _ret = true;
  }
  if (_cutString.length === _strLen) {
    _cutFlag = '0';
  }
  return { cutstring: _cutString, cutflag: _cutFlag };
}
function isFull(pChar) {
  if (pChar.charCodeAt() > 128) {
    return true;
  }
  return false;
}
var getGoodColumnLong32 = function(head) {
  var intLong = Cols - head[0].gblen();
  if (head.length > 1) {
    intLong = parseInt(intLong / (head.length - 1));
  }
  return intLong;
};
let dataFormat = function(len, result, item) {
  let inResult = result;
  for (let i = 0; i < item.length; i++) {
    if (i === 0) {
      inResult = inResult + item[i] + ''.padStart(len[i] - item[i].gblen());
    } else {
      inResult = inResult + ' ' + ''.padStart(len[i] - item[i].gblen()) + item[i];
    }
  }
  inResult = inResult + '@#&';
  return inResult;
};
let arrDataFormat = function(len, arrLength, arr) {
  let result = '';
  let max = 0;
  max = Math.max(...arrLength);
  arr.forEach(item => {
    for (let i = item.length; i < max; i++) {
      item.push('');
    }
  });
  let relArr = _.zip(...arr);
  relArr.forEach(item => {
    result = dataFormat(len, result, item);
  });
  return result;
};
let dataFormatArr = function(str, len) {
  let strArr = [];
  let newStr = str.autoAddEllipsis(len);
  let relStr = '';
  strArr.push(newStr);
  if (newStr.length < str.length) {
    for (let i = newStr.length; i < str.length; i += relStr.length) {
      let inStr = str.substring(i);
      relStr = inStr.autoAddEllipsis(len);
      strArr.push(relStr);
      newStr = newStr + relStr;
    }
  }
  return strArr;
};
var getGoodNotNormal = function(head, detail) {
  let result = '';
  let inDetail = detail;
  if (inDetail == null) {
    inDetail = head;
    result = head[0];
  } else {
    result = inDetail[0];
  }
  var intLong = getGoodColumnLong32(head);
  for (var i = 1; i < inDetail.length; i++) {
    if (i === inDetail.length - 1) {
      intLong = Cols - [result][0].gblen();
    }
    var itemLong = intLong - (i === inDetail.length - 1 ? inDetail[i].gblen() : inDetail[i - 1].gblen());
    result = result + ''.padStart(itemLong <= 0 ? 1 : itemLong) + inDetail[i];
  }
  return result;
};
let getGood = function(title, items) {
  let head = title.split('&');
  let detail = items ? items.split('&') : null;
  let result = '';
  let arr = [];
  let len = [];
  let arrLength = [];
  if (detail !== null) {
    if (head.length === 3) {
      len = Cols === 32 ? [10, 7, 13] : [14, 11, 21];
    } else if (head.length === 4) {
      len = Cols === 32 ? [7, 7, 6, 9] : [10, 9, 11, 15];
    } else {
      // todo
    }
    for (let i = 0; i < head.length; i++) {
      let inArr = dataFormatArr(detail[i], len[i]);
      arr.push(inArr);
      arrLength.push(inArr.length);
    }
    result = arrDataFormat(len, arrLength, arr);
    return result;
  } else {
    result = getGoodNotNormal(head, detail);
    return result;
  }
};
var PrinterName = 'Printer_F713';
const printer = {
  /**
   * 蓝牙打印机列表
   * @param {*} onSuccess
   * @param {*} onFail
   */
  listBluePrinters: function(onSuccess, onFail) {
    let list = [];
    TreChinaPrinter.list(function(res) {
      for (let i = 0; i < res.length; i++) {
        if (i % 3 === 0) {
          list.push(res[i]);
        }
      }

      onSuccess(list);
    }, onFail);
  },
  close: function() {
    setTimeout(function() {
      TreChinaPrinter.disconnect(
        function(data) {
          console.log(data);
        },
        function(err) {
          console.error(err);
        },
        PrinterName
      );
    }, 1500);
  },
  getPrintPosBuyBack: function(obj, groupData, total, pay, printdata) {
    var group = groupData;
    if (obj.buy_back === 2) {
      if (Number((+total + +pay).toFixed(2))) {
        group['其他减项'] = (-(+total + +pay)).toFixed(2);
      }
      group['实退金额'] = (-+pay).toFixed(2);
    } else {
      if (Number((total - pay).toFixed(2))) {
        group['其他优惠'] = (total - pay).toFixed(2) === '-0.00' ? '0.00' : (total - pay).toFixed(2);
      }
      if (printdata.final_integral_money && Number(printdata.final_integral_money) && !isNaN(printdata.final_integral_money) && printdata.member_mobile_name) {
        group['积分抵扣'] = '（含 积分抵扣：' + printdata.final_integral_money + '）';
      }
      group['应付'] = pay;
      if (printdata.accts === '组合支付') {
        group['支付方式'] = printdata.payWay1 + ":" + printdata.combinedFinalPay[0] + '↵' +
        printdata.payWay2 + ":" + printdata.combinedFinalPay[1];
      } else {
        group['支付方式'] = printdata.accts + ":" + printdata.pay_amt;
      }
      group['找零'] = printdata.change_amt;
    }
    return group;
  },
  // 打印挂单
  printRegister(printdata) {
    console.log(printdata, '打印数据');
    Cols = print_cols[demo.$store.state.show.print_cols];
    var params = {
      cols: Cols,
      printername: demo.$store.state.show.setting_small_printer,
      storename: demo.$store.state.show.username,
      lineheight: demo.$store.state.show.line_height,
      goods: [],
      template: 'FZShoppingReceiptTemplate',
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000,
      挂单时间: printdata.createAt,
      收银员: demo.$store.state.show.loginInfo.employeeNumber || '管理员',
      printNum: 1,
      打印时间: new Date().format('yyyy-MM-dd hh:mm:ss'),
      备注内容: '备注：' + printdata.info.remark
    };
    // this.setGfront(params, printdata);
    var total = 0;
    printdata.info.left_goods_list.forEach(item => {
      let name, disc, price, number, mprice;
      name = item.name;
      disc = Number(item.disc / 100);
      price = Number(item.salePrice).toFixed(2);
      number = Number(item.number);
      mprice = item.mprice ? Number(item.mprice) : price;
      total = this.setRegisterData(params.goods, name, mprice, number, total, price);
    });
    var pay = Number(printdata.info.showFinalPrice).toFixed(2);
    params['合计'] = Number(total).toFixed(2);
    if (Number((total - pay).toFixed(2))) {
      params['其他优惠'] = Number(Number(total) - Number(pay)).toFixed(2);
    }
    params['应付'] = pay;
    this.dataFormatMemberSonar(printdata, params);
    console.log('挂单打印传参', params);
    zgPrinter.print(params);
  },
  setRegisterData(goods, name, mprice, number, total, price) {
    goods.push({ 商品名称: name, 原价: mprice, 折扣价: price, 数量: number, 小计: (number * Number(price)).toFixed(2) });
    total = total + Number(Number(mprice) * number);
    return total;
  },
  setGfront(params, printdata) {
    if (demo.$store.state.show.font_value || printdata.gfont) {
      params.gfont = !printdata.gfont ? demo.$store.state.show.font_value : printdata.gfont;
    }
  },
  dataFormatMemberSonar(printdata, params) {
    if (printdata.info.left_member_name !== undefined && printdata.info.left_member_name !== '' && demo.$store.state.show.memberInfoArr[0].flag) {
      params['会员名'] = printdata.info.left_member_name;
    }
    if (printdata.info.member_mobile !== undefined && printdata.info.member_mobile !== '' && demo.$store.state.show.memberInfoArr[1].flag) {
      params['会员手机号'] =
        printdata.info.member_mobile.toString().substring(0, 3) + '****' + printdata.info.member_mobile.toString().substring(7, 11);
    }
    if (printdata.info.member_money !== undefined && printdata.info.member_money !== '' && demo.$store.state.show.memberInfoArr[2].flag) {
      params['余额'] = printdata.info.member_money;
    }
    if (printdata.info.member_integral !== undefined && printdata.info.member_integral !== '' && demo.$store.state.show.memberInfoArr[3].flag) {
      params['积分'] = printdata.info.member_integral;
    }
  },
  dataFormatMember(printdata, group_3) {
    if (
      (printdata.info.left_member_name !== undefined && printdata.info.left_member_name !== '' && demo.$store.state.show.memberInfoArr[1].flag) ||
      (printdata.info.member_mobile !== undefined && printdata.info.member_mobile !== '' && demo.$store.state.show.memberInfoArr[1].flag) ||
      (printdata.info.member_money !== undefined && printdata.info.member_money !== '' && demo.$store.state.show.memberInfoArr[2].flag) ||
      (printdata.info.member_integral !== undefined && printdata.info.member_integral !== '' && demo.$store.state.show.memberInfoArr[3].flag)
    ) {
      group_3['l_1'] = ' ';
    }
    return this.dataFormatMemberSonar(printdata, group_3);
  },
  // PC type : 1 购物小票,2 销售小票,3 会员充值小票
  printPOSTimes: function(printdata, obj) {
    this.printPOS(printdata, obj, false);
  },
  getPrintPosParams: function(printdata, obj, testFlag, remarkFlag, Cols) {
    var params = {
      cols: Cols,
      printername: printdata.printername === undefined ? demo.$store.state.show.setting_small_printer : printdata.printername,
      storename: printdata.storename,
      lineheight: printdata.line_height === undefined ? demo.$store.state.show.line_height : printdata.line_height,
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000,
      交易时间: printdata.createAt,
      收银员: printdata.operater,
      打印时间: new Date().format('yyyy-MM-dd hh:mm:ss'),
      printNum: printdata.printNum,
      template: 'SCShoppingReceiptTemplate'
    };
    // this.setGfront(params, printdata);
    // 判断是否有默认图
    if (demo.$store.state.show.logAndCode[0].flag && (testFlag || demo.$store.state.show.ultimate) && printdata.logoUrl) {
      params.logo = printdata.logoUrl;
    }
    if (demo.$store.state.show.logAndCode[1].flag && (testFlag || demo.$store.state.show.ultimate) && printdata.codeUrl) {
      params.qrcode = printdata.codeUrl;
    }
    this.setParamRemark(remarkFlag, printdata, params);
    // 退款名字更改
    if (obj.from !== 'vip_times_card' && obj.from !== 'pc_vip_times_card') {
      if (obj.buy_back === 2) {
        params['退货单号'] = obj.orderno.replace('.', '');
      } else {
        params['销售单号'] = obj.orderno.replace('.', '');
      }
    }
    return params;
  },
  setParamRemark(remarkFlag, printdata, params) {
    var remark = demo.$store.state.show.remark_content || '';
    if (remarkFlag) {
      remark = printdata.remark;
    }
    params['备注内容'] = remark;
  },
  printPOS: function(printdata, obj, remarkFlag, testFlag) {
    console.log(obj, 'printObj');
    // oem统一对应
    const logoUrl = demo.$store.state.show.logAndCode[0].url;
    const codeUrl = demo.$store.state.show.logAndCode[1].url;
    if (logoUrl.indexOf('logo1') === -1) {
      printdata['logoUrl'] = logoUrl;
    }
    if (codeUrl.indexOf('qrcode1') === -1) {
      printdata['codeUrl'] = codeUrl;
    }
    Cols = print_cols[demo.$store.state.show.print_cols ? demo.$store.state.show.print_cols : '58'];
    console.log(printdata, '打印数据');
    let params = this.getPrintPosParams(printdata, obj, testFlag, remarkFlag, Cols);
    switch (obj.from) {
      case 'pay':
      case 'detail_member':
      case 'sales_detail':
        this.salesDetailPrintPos(printdata, obj, params);
        break;
      case 'member_recharge':
        this.memberRechargePrintPos(printdata, obj, params);
        break;
      case 'vip_times_card':
        this.vipTimesCardPrintPos(printdata, Cols, params);
        break;
      case 'pc_vip_times_card':
        this.pcVipTimesCardPrintPos(printdata, params);
        break;
      case 'point_exchange_print':
        this.pointExchangePrintPos(printdata, params);
        break;
      default:
        break;
    }
    if (params.lineheight === undefined) {
      params.lineheight = '60';
    }
    console.log('打印传参', params);
    zgPrinter.print(
      params,
      a => {
        console.log('打印成功', a);
      },
      b => {
        console.log('打印小票错误信息', b);
      }
    );
  },
  salesDetailPrintPos(printdata, obj, params) {
    var i = 1;
    var total = 0;
    var sumNumber = 0;
    params.goods = [];
    printdata.goods.forEach(item => {
      if (demo.$store.state.show.weighUnitList.indexOf(item.unitName) !== -1) {
        sumNumber += 1;
      } else {
        sumNumber += +(item.number || item.qty);
      }
      let name, disc, price, number, disc_price;
      if (obj.from === 'pay') {
        name = item.name;
        disc = Number(item.disc / 100);
        price = item.name !== '直接收款' && item.mprice !== '' ? Number(item.mprice).toFixed(2) : Number(item.salePrice).toFixed(2);
        number = Number(item.number);
      } else {
        name = item.goodName;
        disc = item.itemDisc;
        price = item.name !== '直接收款' && item.mprice !== '' ? Number(item.mprice).toFixed(2) : Number(item.price).toFixed(2);
        number = Number(item.qty);
      }
      disc_price = Number((item.amtReally || item.amt) / number).toFixed(2);
      if (item.amtReally !== undefined && item.amtReally !== null) {
        disc_price = Number(item.amtReally / number).toFixed(2);
      } else {
        disc_price = Number(item.price * item.itemDisc).toFixed(2);
      }
      if (demo.$store.state.show.weighUnitList.indexOf(item.unitName) !== -1) {
        if (Number(disc_price) > Number(price)) {
          disc_price = Number(price * disc).toFixed(2)
        }
      }
      params.goods.push({
        商品名称: name,
        原价: Number(price).toFixed(2),
        折扣价: disc_price,
        数量: number,
        小计: ((Number(number) * item.amt) / number).toFixed(2)
      });
      total = total + Number(((Number(number) * item.amt) / number).toFixed(2));
      i++;
    });
    let pay = (Number(printdata.pay_amt) - Number(printdata.change_amt)).toFixed(2);
    params['件数'] = '件数：' + sumNumber;
    params['合计'] = total.toFixed(2);
    params = this.getPrintPosBuyBack(obj, params, total, pay, printdata);
    if (printdata.member_name !== undefined && printdata.member_name !== '' && demo.$store.state.show.memberInfoArr[0].flag) {
      params[printdata.member_name] = printdata.member_value;
    }
    if (printdata.member_mobile_name !== undefined && printdata.member_mobile_name !== '' && demo.$store.state.show.memberInfoArr[1].flag) {
      params[printdata.member_mobile_name] = printdata.member_mobile_value;
    }
    if (printdata.member_money_name !== undefined && printdata.member_money_name !== '' && demo.$store.state.show.memberInfoArr[2].flag) {
      params[printdata.member_money_name] = printdata.member_money_value;
    }
    if (printdata.member_point_name !== undefined && printdata.member_point_name !== '' && demo.$store.state.show.memberInfoArr[3].flag) {
      params[printdata.member_point_name] = printdata.member_point_value;
    }
  },
  getSalesDetailPrice(item, obj) {
    let price;
    if (obj.from === 'pay') {
      price = item.name !== '直接收款' && item.mprice ? Number(item.mprice).toFixed(2) : Number(item.salePrice).toFixed(2);
    } else {
      price = item.name !== '直接收款' && item.mprice ? Number(item.mprice).toFixed(2) : Number(item.price).toFixed(2);
    }
    return price;
  },
  memberRechargePrintPos(printdata, obj, params) {
    params.template = 'HBMemberChargeTemplate';
    let giveMoney = obj.memberDetail.giveMoney;
    let money = obj.memberDetail.money;
    params.groups = [
      { name: '充值金额：' + Number(money).toFixed(2) },
      { name: '赠送金额：' + Number(giveMoney).toFixed(2) },
      { name: '实付金额：' + printdata.pay_amt },
      { name: '找零：' + printdata.change_amt }
    ];
    if (printdata.member_name !== undefined && printdata.member_name !== '' && demo.$store.state.show.memberInfoArr[0].flag) {
      params[printdata.member_name] = printdata.member_value;
    }
    if (printdata.member_mobile_name !== undefined && printdata.member_mobile_name !== '' && demo.$store.state.show.memberInfoArr[1].flag) {
      params[printdata.member_mobile_name] = printdata.member_mobile_value;
    }
    if (printdata.member_money_name !== undefined && printdata.member_money_name !== '' && demo.$store.state.show.memberInfoArr[2].flag) {
      params[printdata.member_money_name] = printdata.member_money_value;
    }
    if (printdata.member_point_name !== undefined && printdata.member_point_name !== '' && demo.$store.state.show.memberInfoArr[3].flag) {
      params[printdata.member_point_name] = printdata.member_point_value;
    }
  },
  vipTimesCardPrintPos(printdata, Cols, params) {
    params.template = 'SCBuyTimeCardTemplate';
    params.goods = [];
    printdata.left_goods_list.forEach(item => {
      params.goods.push({ 名称: item.name, 次数: item.restTimes, 价格: item.price });
    });
    params['合计'] = printdata.totalPrice;
    params[printdata.accts] = printdata.vip_times_card.info.receivedMoney;
    params['找零'] = printdata.vip_times_card.info.changeMoney;
    if (printdata.purchase.length > 0) {
      params.goods2 = [];
      printdata.purchase.forEach(item => {
        params.goods2.push({
          name: item
        });
      });
    }
    if (printdata.member_name !== undefined && printdata.member_name !== '' && demo.$store.state.show.memberInfoArr[0].flag) {
      params[printdata.member_name] = printdata.member_value;
    }
    if (printdata.member_mobile_name !== undefined && printdata.member_mobile_name !== '' && demo.$store.state.show.memberInfoArr[1].flag) {
      params[printdata.member_mobile_name] = printdata.member_mobile_value;
    }
    if (printdata.member_money_name !== undefined && printdata.member_money_name !== '' && demo.$store.state.show.memberInfoArr[2].flag) {
      params[printdata.member_money_name] = printdata.member_money_value;
    }
    if (printdata.member_point_name !== undefined && printdata.member_point_name !== '' && demo.$store.state.show.memberInfoArr[3].flag) {
      params[printdata.member_point_name] = printdata.member_point_value;
    }
  },
  pcVipTimesCardPrintPos(printdata, params) {
    params.template = 'SCTimeCardUsedTemplate';
    params.goods = [];
    printdata.conCards.forEach(item => {
      params.goods.push({
        name: item.name + ' ' + printdata.add_ck + '次，' + (item.surplusTimes !== '-' ? '剩余' + item.surplusTimes + '次' : '不限次数')
      });
    });
    if (printdata.purchases.length > 0) {
      params.goods2 = [];
      printdata.purchases.forEach(item => {
        params.goods2.push({
          name: item
        });
      });
    };
    if (
      printdata.memberNewData.member_name !== undefined &&
      printdata.memberNewData.member_name !== '' &&
      demo.$store.state.show.memberInfoArr[0].flag
    ) {
      params[printdata.memberNewData.member_name] = printdata.memberNewData.member_value;
    }
    if (
      printdata.memberNewData.member_mobile_name !== undefined &&
      printdata.memberNewData.member_mobile_name !== '' &&
      demo.$store.state.show.memberInfoArr[1].flag
    ) {
      params[printdata.memberNewData.member_mobile_name] = printdata.memberNewData.member_mobile_value;
    }
    if (
      printdata.memberNewData.member_money_name !== undefined &&
      printdata.memberNewData.member_money_name !== '' &&
      demo.$store.state.show.memberInfoArr[2].flag
    ) {
      params[printdata.memberNewData.member_money_name] = printdata.memberNewData.member_money_value;
    }
    if (
      printdata.memberNewData.member_point_name !== undefined &&
      printdata.memberNewData.member_point_name !== '' &&
      demo.$store.state.show.memberInfoArr[3].flag
    ) {
      params[printdata.memberNewData.member_point_name] = printdata.memberNewData.member_point_value;
    }
  },
  pointExchangePrintPos(printdata, params) {
    params.template = 'exchangeTemplate';
    params.goods = [
      {
        name: printdata.productName + ' ' + printdata.exchange + '个，' + printdata.point + '积分'
      }
    ];
    if (
      printdata.memberNewData.member_name !== undefined &&
      printdata.memberNewData.member_name !== '' &&
      demo.$store.state.show.memberInfoArr[0].flag
    ) {
      params[printdata.memberNewData.member_name] = printdata.memberNewData.member_value;
    }
    if (
      printdata.memberNewData.member_mobile_name !== undefined &&
      printdata.memberNewData.member_mobile_name !== '' &&
      demo.$store.state.show.memberInfoArr[1].flag
    ) {
      params[printdata.memberNewData.member_mobile_name] = printdata.memberNewData.member_mobile_value;
    }
    if (
      printdata.memberNewData.member_money_name !== undefined &&
      printdata.memberNewData.member_money_name !== '' &&
      demo.$store.state.show.memberInfoArr[2].flag
    ) {
      params[printdata.memberNewData.member_money_name] = printdata.memberNewData.member_money_value;
    }
    if (
      printdata.memberNewData.member_point_name !== undefined &&
      printdata.memberNewData.member_point_name !== '' &&
      demo.$store.state.show.memberInfoArr[3].flag
    ) {
      params[printdata.memberNewData.member_point_name] = printdata.memberNewData.member_point_value;
    }
  },
  getSpace: function(spaceNum) {
    let space = '';
    for (let i = 0; i < spaceNum; i++) {
      space += ' ';
    }
    return space;
  },
  // type : 1 交接班详情 2 商品销售报表
  printChangeShiftsTimes: function(detail, printData, type) {
    this.printChangeShifts(detail, printData, type);
  },
  printChangeShifts: function(detail, printData, type) {
    Cols = print_cols[demo.$store.state.show.print_cols];
    const that = this;
    let params = {
      cols: Cols,
      printername: demo.$store.state.show.setting_small_printer,
      storename: printData.title,
      printNum: demo.$store.state.show.smallPrinterCopies,
      lineheight: demo.$store.state.show.line_height,
      '打印时间': new Date().format('yyyy-MM-dd hh:mm:ss'),
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000
    };
    params['开始时间'] = printData.start.replace(/（补）/, "");
    params['结束时间'] = printData.end.replace(/（补）/, "");
    params['收银员'] = printData.user;
    // this.setGfront(params, printData);
    switch (type) {
      case 'change':
        params['template'] = 'changeShiftsTemplate';
        params['总销售'] = [];
        params['应收现金'] = [];
        params['销售总额'] = [];
        params['会员充值'] = [];
        params['支付统计'] = [];
        detail.forEach(item => {
          params[item.title].push({ 'data': `${item.title} ${item.total}` });
          that.getDesc(item.desc).forEach(function (val) {
            let innerGroupIt = {};
            if (!demo.isNullOrTrimEmpty(val.desc)) {
              innerGroupIt['data'] = '  ' + val.title + val.total;
              params[item.title].push(innerGroupIt);
              that.getDesc(val.desc).forEach(function (val2) {
                let innerGroupVal = {};
                innerGroupVal['data'] = `    ${that.printTemplateTitle(val2)}  ${Number(val2.money).toFixed(2)} 元（${val2.count} 笔）`;
                params[item.title].push(innerGroupVal);
              });
            } else {
              innerGroupIt['data'] = `    ${that.printTemplateTitle(val)}  ${Number(val.money).toFixed(2)} 元（${val.count} 笔）`;
              params[item.title].push(innerGroupIt);
            }
            params['总销售'] = params['销售总额'];
          });
        });
        for (let key in params) {
          if (demo.isNullOrTrimEmpty(params[key])) {
            delete params[key]
          }
        }
        break;
      case 'sales':
        params.template = 'goodsSalesReportTemplate';
        this.printChangeShiftsSale(detail, params);
        break;
      default:
        break;
    }
    this.setParamsLineHeight(params);
    console.log(params);
    zgPrinter.print(
      params,
      a => {
        console.log('打印成功', a);
      },
      b => {
        console.log('打印小票错误信息', b);
      }
    );
  },
  // 为0的数不要
  getDesc(arr) {
    if (arr && arr.length > 0) {
      return arr.filter(d => d.count);
    } else {
      return [];
    }
  },
  // 打印模板:得到子标题
  printTemplateTitle(item) {
    return demo.isNullOrTrimEmpty(item.payType)
      ? demo.isNullOrTrimEmpty(item.mainType) ? item.title : item.mainType : item.payType;
  },
  printChangeShiftsSale(detail, params) {
    let total = 0;
    let totalCount = 0;
    if (detail.length > 0) {
      params.groups = [];
    }
    detail.forEach(item => {
      let name, money, category, count;
      name = item.name;
      count = item.count;
      money = Number(item.money).toFixed(2);
      category = item.category ? item.category : '-';
      params.groups.push({name: name, num: '', total: ''});
      params.groups.push({name: category, num: count, total: money});
      total = total + Number(money);
      if (this.isWeightUnit(item)) {
        totalCount += 1;
      } else {
        totalCount += +(count || item.qty || 0);
      }
    });
    params['总数'] = totalCount;
    params['总计'] = Number(total).toFixed(2);
  },
  isWeightUnit(item) {
    return item.unit_name === '斤' || item.unit_name === '两' || item.unit_name === '千克' || item.unit_name === '克';
  },
  setParamsLineHeight(params) {
    if (params.lineheight === undefined) {
      params.lineheight = '60';
    }
  },
  wdPrintPOS: function(printdata) {
    var params = {
      cols: print_cols[demo.$store.state.show.print_cols],
      printername: demo.$store.state.show.setting_small_printer,
      storename: printdata.storename,
      lineheight: demo.$store.state.show.line_height,
      groups: [],
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000
    };
    // this.setGfront(params, printdata);
    params.groups.push({ 订单号: printdata.orderno.replace('.', ''), style: { size: 3 } });
    let opient = {};
    opient['交易时间'] = printdata.createAt;
    params.groups.push(opient);

    var goods_head = '原价&数量&小计';
    var goods = { l_0: getGood(goods_head) };
    var i = 1;
    printdata.goods.forEach(item => {
      let name, price, number, detail;
      name = item.goodName;
      price = item.price;
      number = item.qty;
      detail = Number(price).toFixed(2) + '&' + Number(number).toFixed(2) + '&' + (number * price).toFixed(2);
      let nameArr = dataFormatArr(name, Cols);
      nameArr.forEach(nameItem => {
        goods['l_' + i] = nameItem;
        i++;
      });
      let arr = getGood(goods_head, detail).split('@#&');
      for (let z = 0; z < arr.length; z++) {
        goods['l_' + i] = arr[z];
        i++;
      }
    });
    params.groups.push(goods);
    var group_3 = {};
    group_3['合计'] = Number(printdata.order_price).toFixed(2);
    group_3['l_1'] = ' ';
    group_3['应付'] = Number(printdata.goods_price).toFixed(2);
    params.groups.push(group_3);
    var group_4 = {};
    group_4['收货人'] = printdata.consignee.hidden(1, 0);
    group_4['电话'] = printdata.mobile.hidden(3, 4);
    group_4['地址'] = printdata.address;
    if (printdata.remark !== '') {
      let re = /[\r\n]+/g;
      let remarks = remark.split(re);
      remarks.forEach((item, index) => {
        group_4['l_' + index] = item;
      });
      group_4['打印时间'] = new Date().format('yyyy-MM-dd hh:mm:ss');
    } else {
      group_4['打印时间'] = new Date().format('yyyy-MM-dd hh:mm:ss');
    }
    params.groups.push(group_4);
    // external.printPOS(params);
    // 回调更改状态
    // if (printdata.flg !== undefined) {
    //   demo.$http
    //     .post(
    //       rest.webSocketUrl + 'api/auth/printCallBack',
    //       {
    //         sysUid: printdata.sys_uid,
    //         sysSid: printdata.sys_sid,
    //         flg: printdata.flg,
    //         id: printdata.id
    //       },
    //       {
    //         headers: {
    //           'Content-Type': 'application/json'
    //         }
    //       }
    //     )
    //     .then(() => {
    //       // 轮训时如果还有未打印的继续打印
    //       if (printdata.hasOther === 1) {
    //         pollPrint.printOrder(printdata.sys_uid, printdata.sys_sid);
    //       }
    //     });
    // }
  },
  /**
   * 打印寄件单/寄件单（补）/取件单（补）小票
   */
  printMailTicket(data, ticketTitle) {
    if (!demo.$store.state.show.setting_small_printer) {
      setTimeout(() => {
        demo.msg('warning', '打印设备异常，请检查小票打印机！');
      }, 1500);
      return;
    }
    let params = {
      cols: print_cols[demo.$store.state.show.print_cols || '58'],
      printername: demo.$store.state.show.setting_small_printer,
      printNum: demo.$store.state.show.smallPrinterCopies,
      lineheight: demo.$store.state.show.line_height,
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000,
      title: ticketTitle,
      "收银员": data.operator,
      "打印时间": data.printTime,
      "template": 'mailing',
      "goods": data.goods,
      "会员名": data.vipName
    }
    params['寄件时间'] = `${ticketTitle === '取件单（补）' ? '取' : '寄'}件时间：${data.time}`;
    console.log('打印寄件/寄件单（补）/取件单（补）- 小票参数：', params);
    zgPrinter.print(
      params,
      a => {
        console.log('打印成功', a);
      },
      b => {
        console.log('打印小票错误信息', b);
      }
    );
  },
  /**
   * 打印取件单小票
   */
  printPickUpTicket(data) {
    if (!demo.$store.state.show.setting_small_printer) {
      setTimeout(() => {
        demo.msg('warning', '打印设备异常，请检查小票打印机！');
      }, 1500);
      return;
    }
    let params = {
      cols: print_cols[demo.$store.state.show.print_cols || '58'],
      printername: demo.$store.state.show.setting_small_printer,
      printNum: demo.$store.state.show.smallPrinterCopies,
      lineheight: demo.$store.state.show.line_height,
      printMode: demo.$store.state.show.printMode === 1 ? 1001 : 1000,
      title: "取件单",
      "取件时间": data.time,
      "收银员": data.operator,
      "打印时间": data.time,
      "template": 'pickup',
      "goods": data.goods,
      "会员名": data.vipName
    }
    console.log('打印取件小票参数：', params);
    zgPrinter.print(
      params,
      a => {
        console.log('打印成功', a);
      },
      b => {
        console.log('打印小票错误信息', b);
      }
    );
  }
};
pollPrint = {
  ticket: '',
  printOrder(sysUid, sysSid) {
    demo.$http
      .post(
        rest.webSocketUrl + 'api/auth/orderPrint',
        { sysUid: sysUid, sysSid: sysSid },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      .then(resqqq => {
        if (resqqq.data && resqqq.data.data && resqqq.data.data.printflg === 1) {
          resqqq.data.data.orderInfo.flg = 0;
          resqqq.data.data.orderInfo.hasOther = resqqq.data.data.hasOther;
          printer.wdPrintPOS(resqqq.data.data.orderInfo);
        }
      });
  },
  ticketFunc(sysUid, sysSid) {
    if (this.ticket) {
      return;
    }
    try {
      demo.$http
        .post(
          rest.webSocketUrl + 'admin/auth/getOrderPrint',
          { sysUid: sysUid, sysSid: sysSid },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then(res => {
          if (res.data && res.data.data && res.data.data.printflg === 1) {
            this.printOrder(sysUid, sysSid);
            this.ticket = setInterval(() => {
              this.printOrder(sysUid, sysSid);
            }, 600000);
          } else {
            this.ticket = '微店未启动自动打印';
          }
        });
    } catch (error) {
      this.ticket = '小程序有又双叒叕挂了';
      console.log(error);
    }
  }
};
let pos = {
  db: db,
  chrome: chrome,
  network: network,
  printer: printer,
  pollPrint: pollPrint
};

window.pos = pos;
export default pos;
