module.exports = {
  page: {
    register: {
      declaration: '《隐私声明》',
      declaration_url: 'https://www.zhangguizhinang.com/dfprivacy.html',
      register: '《用户服务协议》',
      register_url: 'https://www.zhangguizhinang.com/dfproduct.html'
    },
    setting: {
      business_scope: [
        { label: '零售杂货' },
        { label: '烘焙茶饮' },
        { label: '文体玩具' },
        { label: '母婴行业' },
        { label: '服饰鞋包' },
        { label: '美业理疗' },
        { label: '餐饮快餐' },
        { label: '其它' }
      ],
      del_data_div_txt: '为防止产生异常数据，请确认已关闭其它设备上的掌柜智囊程序'
    },
    home: {
      privacyStatement: '用户隐私声明',
      registeredStatement: '用户注册声明'
    }
  },
  components: {
    pc_version_compare: {
      startEmploy: '开始使用',
      buyNow: '立即购买'
    },
    header: {
      header_user: '掌柜智囊',
      accountName: '掌柜智囊商家助手',
      telephone: '************',
      customer_service: '客服热线：************',
      scroll_text: '掌柜智囊 新一代高颜值收银系统，云端同步，免费试用。'
    }
  },
  image: {
    homeImage: {
      color: '#B4995A',
      // 文字颜色 和themeFontColor一致
      fontColor: '#1D324F',
      // 首页软件版本图片颜色
      editionColor: '#FF8A00',
      // 更换版本修改color: #B4995A
      showAccts: 'background: #F9F1E0;color: #B4995A;border-color: #D49F57;',
      showAccts1: 'background: #FFF;color: #567485;',
      // 更换版本修改color: #567485
      menuItem: 'background-color: #E2ECF1 !important;color: #567485;',
      // 库存盘点界面商品子菜单字体颜色
      butFont: 'color: #567485;background-color: #E2ECF1 !important;',
      // 表格字颜色
      tableFont: 'color: #567485;',
      editionBackground: 'linear-gradient(180deg, #FFF1DF 0%, #FBDDB7 110.56%)'
    }
  }
}
