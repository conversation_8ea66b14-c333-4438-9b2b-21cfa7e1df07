<template>
  <div>
    <div class="dialog-container">
        <div class="diff-header">
          <div class="header-title">计算保质期天数</div>
          <div class="close-container" @click="dismissDialog">
            <img src="@/image/close_gray.png" alt="">
          </div>
        </div>
        <div class="diff-content">
          <div class="date-item-container">
            <div class="star">*</div>
            <div class="title">生产日期</div>
            <el-date-picker
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              v-model="manufactureDate"
              type="date"
              placeholder="选择日期"
              @change="calc">
            </el-date-picker>
          </div>
          <div class="date-item-container">
            <div class="star">*</div>
            <div class="title">有效期至</div>
            <el-date-picker
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              v-model="zhiDate"
              type="date"
              placeholder="选择日期"
              @change="calc">
            </el-date-picker>
          </div>
        </div>
        <div class="days">保质期（天）：<span :class="[+days < 0 ? 'error' : '']">{{ days }}</span></div>
        <div class="diff-footer">
          <div class="btn btn-cancel" @click="dismissDialog">取消</div>
          <div class="btn btn-confirm ml-10" @click="confirm">确定</div>
        </div>
      </div>
  </div>
</template>

<script>
import { calculateDiffDays } from '@/utils/util';
export default {
  props: {
    manufactureDate: {
      type: String, // 生产日期
      default: ''
    }
  },
  data() {
    return {
      zhiDate: '', // 有效期至
      days: '-'
    }
  },
  methods: {

    /**
     * 控制弹窗消失
     */
    dismissDialog() {
      this.$emit('dismissDialog');
    },

    /**
     * 计算日期差
     */
    calc() {
      if (!this.manufactureDate || !this.zhiDate) {
        this.days = '-';
        return;
      }
      this.days = calculateDiffDays(this.manufactureDate, this.zhiDate);
    },

    /**
     * 点击确定
     */
    confirm() {
      if (!this.manufactureDate) {
        return demo.msg('warning', '请选择生产日期');
      }
      if (!this.zhiDate) {
        return demo.msg('warning', '请选择有效日期');
      }
      if (+this.days < 0) {
        return demo.msg('warning', '请选择正确的日期区间');
      }
      this.$emit('callback', {
        manufactureDate: this.manufactureDate,
        diff: this.days
      });
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .el-input__inner {
  height: 38px;
}
/deep/ .el-input__icon {
  line-height: 38px;
}
/deep/ .el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 280px;
}
.dialog-container {
  padding: 0 20px 20px 20px;
  .diff-header {
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E3E6EB;
    .header-title {
      font-size: 18px;
      font-weight: bold;
      color: #567485;
    }
    .close-container {
      width: 40px;
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      cursor: pointer;
    }
    .close-container img {
      width: 15px;
      height: 15px;
    }
  }
  .date-item-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    .star {
      color: red;
    }
    .title {
      font-size: 18px;
      color: #b2c3cd;
      font-weight: bold;
      margin-right: 20px;
      width: 80px;
    }
  }
  .days {
    text-align: center;
    padding: 16px 0;
    font-size: 18px;
    color: #567485;
  }
  .error {
    color: red;
  }
  .diff-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    .btn {
      flex: 1;
      padding: 10px 0;
      border-radius: 6px;
      font-size: 16px;
      font-weight: bold;
      border: 1px solid #bda169;
    }
    .ml-10 {
      margin-left: 18px;
    }
    .btn-cancel {
      color: #bda169;
    }
    .btn-confirm {
      background: #bda169;
      color: white;
    }
  }
}

</style>
