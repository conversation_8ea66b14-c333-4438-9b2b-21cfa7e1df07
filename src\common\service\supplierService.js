import dao from '../dao/dao';
import message from '../../config/message';

const supplierService = {
  /**
   * 供应商检索
   */
  searchSuppliers: function (data, onSuccess, onFail) {
    if (data != null) {
      data.wheres = 'where 1=1 ';
      data.limit = data.limit || 10;
      data.offset = data.offset || 0;
      if (data.name != null && data.name.toString().trim() !== '') {
        data.wheres +=
          "and (name like '%" +
          data.name +
          "%' or contacter like '%" +
          data.name +
          "%' or mobile like '%" +
          data.name +
          "%') ";
      }
      if (+data.status === 0 || +data.status === 1) {
        data.wheres += "and is_deleted = '" + data.status + "' ";
      }
    }
    dao.exec(sqlApi.searchSuppliers.format(data), onSuccess, onFail);
  },

  /**
   * 根据条件，获取供应商总数
   */
  getSupplierCount: function (data, onSuccess, onFail) {
    if (data != null) {
      data.wheres = 'where 1=1 ';
      data.limit = data.limit || 10;
      data.offset = data.offset || 0;
      if (data.name != null && data.name.toString().trim() !== '') {
        data.wheres +=
          `and (name like '%` +
          data.name +
          `%' or contacter like '%` +
          data.name +
          `%' or mobile like '%` +
          data.name +
          `%') `;
      }
      if (+data.status === 0 || +data.status === 1) {
        data.wheres += `and is_deleted = '` + data.status + `' `;
      }
    }
    dao.exec(sqlApi.getSupplierCount.format(data), onSuccess, onFail);
  },

  /**
   * 更新本地本地供应商
   */
  updateSupplier: function (data, onSuccess, onFail) {
    let params = {
      fingerprint: data.fingerprint,
      name: data.name
    };
    let responseResult = {
      code: 0,
      msg: message.save_success
    };
    dao.exec(
      sqlApi.getSupplierByNameNotId.format(demo.sqlConversion(params)),
      res => {
        var result = demo.t2json(res);
        console.log(result);
        if (result.length > 0) {
          responseResult.code = -1;
          responseResult.msg = message.supplierNameAlreadyExists;
          onSuccess(JSON.stringify(responseResult));
          return;
        }
        dao.exec(
          sqlApi.updateSupplier.format(demo.sqlConversion(data)), () => {
            onSuccess(JSON.stringify(responseResult));
          }, onFail
        );
      },
      onFail
    );
  },

  /**
   * 更新本地供应商状态
   */
  updateSupplierStatus: function (data, onSuccess, onFail) {
    dao.exec(
      sqlApi.updateSupplierStatus.format(demo.sqlConversion(data)), onSuccess, onFail
    );
  },

  /**
   * 插入本地供应商
   */
  insertSupplier: function (data, onSuccess, onFail) {
    let params = {
      name: data.name
    };
    let responseResult = {
      code: 0,
      msg: message.save_success
    };
    dao.exec(
      sqlApi.getSupplierByName.format(demo.sqlConversion(params)),
      res => {
        var result = demo.t2json(res);
        if (result.length > 0) {
          responseResult.code = -1;
          responseResult.msg = message.supplierNameAlreadyExists;
          onSuccess(JSON.stringify(responseResult));
          return;
        }
        const { name, mobile, fingerprint } = data;
        dao.exec(sqlApi.getSupplierByFingerprint.format({ fingerprint }), info => {
          var supplier = demo.t2json(info);
          if (supplier.length > 0 && supplier[0]['name'] !== name) {
            var now = new Date().getTime();
            data['fingerprint'] = md5(name + '_' + mobile + '_' + now);
          }
          dao.exec(
            sqlApi.insertSupplier.format(demo.sqlConversion(data)), () => {
              onSuccess(JSON.stringify(responseResult));
            }, onFail
          );
        }, onFail)
      },
      onFail
    );
  },

  /**
   * 获取供应商下拉列表
   */
  getSupplierDropDownList: function (onSuccess, onFail) {
    dao.exec(sqlApi.getSupplierDropDownList, onSuccess, onFail);
  },

  /**
   * 进货明细获取供应商下拉列表
   */
  getDetailSupplierDropDownList: function (onSuccess, onFail) {
    dao.exec(sqlApi.getDetailSupplierDropDownList, onSuccess, onFail);
  },

  /**
   * 根据供应商名字，获取供应商
   */
  getSupplierByName: function (data, onSuccess, onFail) {
    let params = {
      name: data.name
    };
    dao.exec(
      sqlApi.getSupplierByName.format(demo.sqlConversion(params)),
      onSuccess,
      onFail
    );
  },
  /**
   * 根据供应商fingerprint获取供应商
   */
  getSupplierInfoByFingerprint: function (params, onSuccess, onFail) {
    dao.exec(sqlApi.getSupplierByFingerprint.format(params), result => {
      onSuccess(result[0])
    }, onFail)
  },

  hasGoodsSupplier(params, onSuccess, onFail) {
    dao.exec(sqlApi.getGoodsSupplierByFingerprint.format(params), result => {
      onSuccess(result.length > 0)
    },
    onFail
    )
  }
};
window.supplierService = supplierService;
export default supplierService;
