/**
 * 描述:日志帮助工具
 * @ClassName log
 * <AUTHOR>
 * @Date 2020-07-06 21:12
 * @Version 1.0
 */

const Log = (Vue) => {
  Vue.prototype.$log = new Vue({
    methods: {
      error(...params) {
        console.log('❌···', ...params, '···❌');
      },
      post(params) {
        demo.$http.post(
          demo.$rest.addLogs,
          params,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            }
          }
        );
      },
      info(action, info) {
        var that = this;
        if (!pos.network.isConnected()) {
          return;
        }
        var mypost = async function (info) {
          var device_type = '';
          var mac = '';
          device_type = 'pc';
          mac = pos.network.getMac();
          if (typeof returnCitySN != 'undefined') {
            Object.assign(info, returnCitySN || {});
          }
          var params = {
            sys_uid: demo.$store.state.show.sys_uid,
            sys_sid: demo.$store.state.show.sys_sid,
            device_type: device_type,
            mac: mac,
            action: action,
            info: JSON.stringify(info)
          };
          that.post(params);
        };
        var actionArr = ['login', 'logout', 'clearData', 'clearDataClick', 'register'];
        // var actionArr = ['login', 'logout', 'pay', 'vip_times_card', 'reload', 'clearData', 'clearDataClick', 'stock'];
        if (actionArr.indexOf(action) > -1) {
          if (info) {
            mypost(info);
          } else {
            mypost($setting);
          }
        }
      }
    }
  });
};
export default Log;
