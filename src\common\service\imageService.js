import dao from '../dao/dao';

const imageService = {
  /**
   * 查询信息 data:{'product_id':'1'}
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  get: function (data, onSuccess, onFail) {
    dao.exec(
      sqlApi.image_get.format(data),
      onSuccess,
      onFail
    );
  },

  /**
   * 添加或更新
   * @data {good_id, url, localUrl}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (data, onSuccess, onFail) {
    if (!data.hasOwnProperty('fingerprint') || data.fingerprint == null || data.fingerprint === undefined || data.fingerprint.trim().length === 0) {
      data.fingerprint = md5(data.good_fingerprint);
    }
    if (data.is_deleted === undefined) {
      data.is_deleted = '0';
    }
    if (data.is_synced === undefined) {
      data.is_synced = '0';
    }
    dao.exec(sqlApi.image_put.format(data), onSuccess, onFail);
  },

  /**
   * 新增商品时，判断是否存被删除的同商品，有的话获取对应图片的fingerprint
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getDelImage: function(data, onSuccess, onFail) {
    dao.exec(sqlApi.image_del_get_by_good.format(data), onSuccess, onFail);
  }

};

window.imageService = imageService;
export default imageService;
