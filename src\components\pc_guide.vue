<style lang="less" scoped>
.pc_guide {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  color: @themeFontColor;
  display: flex;
  align-items: center;
  .pc_guide1 {
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 840px;
    border-radius: 10px;
    overflow: hidden;
    .pc_guide_title {
      text-align: center;
      line-height: 29px;
      font-size: 20px;
      font-weight: bold;
      color: @themeBackGroundColor;
      margin-top: 18px;
    }
    .pc_guide_content {
      text-align: center;
      font-size: 16px;
      color: @themeFontColor;
      line-height: 24px;
      margin-top: 12px;
      padding: 0 90px;
      min-height: 40px
    }
    .pc_guide2 {
      border-radius: 24px;
      border: 1px solid @themeBackGroundColor;
      background: #FFFFFF;
      color: @themeBackGroundColor;
      width: 140px;
      height: 48px;
      font-size: 18px;
      line-height: 44px;
      cursor: pointer;
      display: inline-block;
      font-weight: bold;
    }
    .pc_guide3 {
      border-radius: 24px;
      border: 1px solid @themeBackGroundColor;
      cursor: pointer;
      background: @themeBackGroundColor;
      font-size: 18px;
      line-height: 44px;
      height: 48px;
      width: 140px;
      font-weight: bold;
      margin-left: 20px;
      display: inline-block;
      color: #FFFFFF;
    }
    .pc_guide4 {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
      cursor: pointer;
      margin-left: 5px;
      margin-right: 5px;
    }
  }
}
</style>
<template>
  <!--引导页弹出-->
  <div v-show="showNovice">
    <div class="pc_guide">
      <div class="pc_guide1">
        <div v-show="item.show" :key="index" v-for="(item, index) in noviceList">
          <div style="float: right;margin-right: 20px;margin-top: 20px;cursor: pointer;" @click="addShouStatus">
            <img alt="关闭" style="width: 30px;height: 30px;" src="../image/close.png" />
          </div>
          <div style="text-align: center;padding-top: 50px;background: #FCF9F4;">
            <el-image :src="item.srcNovice"
              style="height: 421px;width: 653px;margin-bottom: -11px;"/>
          </div>
          <div class="pc_guide_title">
            {{item.mainTipNovice}}
          </div>
          <div class="pc_guide_content">
            {{item.tipNovice}}
          </div>
          <div style="text-align: center;padding-top: 20px;padding-bottom: 30px;">
            <div v-show="index !== 0"
              class="pc_guide2"
              @click="last(index)"
            >上一个</div>
            <div v-show="index !== noviceList.length - 1"
              class="pc_guide3"
              @click="next(index)"
            >下一个</div>
            <div v-show="index === noviceList.length - 1"
              class="pc_guide3"
              @click="addShouStatus()"
            >完 成</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data () {
    return {
      noviceList: [
        {
          srcNovice: require('../image/pc_home_novice1.png'), // 新手引导提示图片
          mainTipNovice: '首页功能区改造', // 新手引导主表题
          tipNovice: `【首页】新增查询昨日、本周、上周、上月的数据；新增“过期预警”等提醒事项并支持快捷跳转到对应的报表，新增“商家成长”展示店铺经营状态。`,
          show: true
        },
        {
          srcNovice: require('../image/pc_home_novice2.png'), // 新手引导提示图片
          mainTipNovice: '新增【修改积分】功能', // 新手引导主表题
          tipNovice: `在【会员设置】中启用“积分抵扣”后，【收银台】结算时支持修改会员可使用的积分数量，该功能关联员工权限，需要提前请先赋予收银员“修改积分抵现”权限。`,
          show: false
        },
        {
          srcNovice: require('../image/pc_home_novice3.png'), // 新手引导提示图片
          mainTipNovice: '新增【自动计算保质期天数】功能', // 新手引导主表题
          tipNovice: `【新增（编辑）商品】中新增“计算保质期天数”功能，点击“计算”按钮，输入“生产日期”、“有效日期”后，即可自动计算成保质期天数。`,
          show: false
        }
        // {
        //   srcNovice: require('../image/pc_home_novice3.png'), // 新手引导提示图片
        //   mainTipNovice: '新增【收银台-分类管理】功能', // 新手引导主表题
        //   tipNovice: `【收银台】页面中，新增“分类管理”功能，支持分类的多种操作，包括新增、删除、排序、设置为收银台默认显示分类。`,
        //   show: false
        // }
        // {
        //   srcNovice: require('../image/pc_home_novice3.png'), // 新手引导提示图片
        //   mainTipNovice: '新增【收银台-热销分类】功能', // 新手引导主表题
        //   tipNovice: `【收银台】“热销”分类用来展示店铺销量排名前30的商品（最近1个月），可在【设置】-【系统设置】通过开关来控制是否在收银台显示“热销”分类。`,
        //   show: false
        // }
        // {
        //   srcNovice: require('../image/pc_home_novice4.png'), // 新手引导提示图片
        //   mainTipNovice: '新增【进货修改零售价】功能', // 新手引导主表题
        //   tipNovice: '进货时如果零售价或会员价有变更，可点击零售价进行修改。',
        //   show: false
        // }
      ]
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    addShouStatus () {
      this.SET_SHOW({ showNovice: false, isFirstLogin: false });
      $config.IndustryUpdated = false;
      var data = [
        { key: 'show_novice', value: '0', remark: '0不显示引导页1显示引导页' }
      ];
      settingService.put(data);
    },
    // 下一步
    next(index) {
      this.noviceList[index].show = false;
      this.noviceList[index + 1].show = true;
    },
    // 上一步
    last(index) {
      this.noviceList[index].show = false;
      this.noviceList[index - 1].show = true;
    }
  },
  computed: mapState({
    showNovice: state => state.show.showNovice
  })
};
</script>
