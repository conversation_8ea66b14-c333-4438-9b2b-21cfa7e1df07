import XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style';
import stringUtils from './stringUtils';
import FileSaver from 'file-saver';
export default {
  install(Vue) {
    Vue.prototype.$makeExcel = function(info, field_mapping, excelName) {
      console.log(info, '导出数据');
      const defaultCellStyle = {
        font: { name: '宋体' }
      };
      const wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
        defaultCellStyle: defaultCellStyle,
        showGridLines: false
      };
      const wb = { SheetNames: [excelName], Sheets: {}, Props: {} };
      wb.Sheets[excelName] = XLSX.utils.json_to_sheet(
        stringUtils.fieldMapping2(info, stringUtils.reverseMapping(field_mapping))
      );

      // 创建二进制对象写入转换好的字节流
      let tmpDown = new Blob([stringUtils.s2ab(XLSXStyle.write(wb, wopts))], {
        type: 'application/octet-stream'
      });
      FileSaver.saveAs(tmpDown, excelName + '.xlsx');
    };
  }
};
