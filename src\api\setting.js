import rest from '@/config/rest';
// 查询用户试用版过期后是否可以继续使用
export function continuedUse() {
  return new Promise((resolve, reject) => {
    demo.$http
      .get(rest.continuedUse, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
