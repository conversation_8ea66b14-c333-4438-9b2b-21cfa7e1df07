<style lang="less" scoped>
.pc_log {
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
}
.pc_log1 {
  width: 1100px;
  margin: 0 auto;
  margin-top: 12%;
  color: @themeFontColor;
}
.pc_log1 img {
  float: left;
}
.pc_log11 {
  float: left;
  margin-top: 40px;
  margin-left: 100px;
}
.pc_log12 {
  overflow: hidden;
}
.pc_log12 div {
  width: 70px;
  text-align: center;
  line-height: 61px;
  float: left;
  font-size: 16px;
  font-weight: 700;
}
.pc_log12 input {
  width: 320px;
  height: 61px;
  background: #f5f8fb;
  border-radius: 10px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  font-weight: bold;
}
.login_top{
  position: absolute;
  top: 30px;
  right: 0;
  display: flex;
  align-items: center;
}
.top_btn{
  width: 100px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @themeBackGroundColor;
  font-weight: bold;
  font-size: 14px
}

.upgrade1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.upgrade11 {
  width: 450px;
  min-height: 280px;
  background: #fff;
  border-radius: 6px;
  margin: 250px auto 0px;
  padding-bottom: 10px;
}

.upgrade12 {
  width: 93%;
  height: 75px;
  font-size: 24px;
  margin-left: 20px;
  line-height: 100px;
  text-align: center;
  font-weight: bold;
  color: @themeFontColor;
}

.upgrade13 {
  float: right;
  font-size: xx-large;
  color: #B2C3CD;
  margin-top: -22px;
  cursor: pointer;
}

.upgrade14 {
  width: 93%;
  overflow: hidden;
  margin: 21px auto;
  text-align: center;
}
.upgrade14 div {
  width: 70px;
  text-align: center;
  line-height: 61px;
  float: left;
  font-size: 16px;
  font-weight: bold;
}
.upgrade14 input {
  width: 320px;
  height: 61px;
  background: #f5f8fb;
  border-radius: 10px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  font-weight: bold;
}
/deep/ .el-tabs__active-bar {
  background-color: rgba(189, 161, 105, 100);
}
/deep/ .el-tabs__item {
  font-size: 18px;
  color: @themeFontColor;
}
/deep/ .el-tabs__item.is-active {
  font-weight:bold;
  font-size: 20px;
  color: @themeFontColor;
}
/deep/ .el-tabs__item.is-top {
  margin-bottom: 10px;
}
/deep/ .el-tabs__header.is-top {
  margin-left: 70px;
  margin-bottom: 35px;
}
/deep/ .el-input.el-input--suffix input {
  background: rgb(245, 248, 251);
  border: none;
  text-indent: 7px;
  font-size: 16px;
}
/deep/ input::-webkit-input-placeholder{
  color: rgba(177, 195, 205, 100);
}
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  .title{
    color: @themeFontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @themeFontColor;
    font-size: 24px;
    margin-top: 28px;
    text-align: center;
  }
  .dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 50px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
    .btn1 {
      width: 130px;
      height: 44px;
      border-style:solid;
      border-color: rgb(189, 161, 105);
      font-size: 18px;
      font-weight: normal;
    }
  }
}
.pc_industry_div {
  position: absolute;
  top: 30px;
  right: 180px;
  background: #F2F5F7;
  line-height: 40px;
  text-align: center;
  border-radius: 6px;
  width: 70px;
  height: 40px;
  font-weight: bold;
  cursor: pointer;
  img {
    width: 20px;
    height: 18px;
  }
}
.logining_img {
  width: 30px;
  position: absolute;
  top: 16px;
}
.pc_scan_loading_div {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  position: absolute;
  top: 0px;
  left: 0px;
}
.pc_load_error_div {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #fafafa;
  color: #cacaca;
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pc_waiting_empower_div {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  position: absolute;
  top: 37px;
  left: 136px;
}
.pc_no_network_div {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #fafafa;
  color: #cacaca;
  position: absolute;
  top: 37px;
  left: 136px;
}
#gologin {
  background: @themeBackGroundColor;
  width: 320px;
  border-radius: 4px;
  font-size: 20px;
  font-weight: bold;
  color: #FFFFFF;
  line-height: 60px;
  text-align: center;
  float: left;
  cursor: pointer;
  margin-left: 70px;
  margin-top: 20px;
  margin-bottom: 20px;
}
#goRegister {
  color: @themeBackGroundColor;
  font-weight: bold;
  cursor: pointer;
  font-size: 16px;
  bottom: 60px;
  right: 42%;
  margin-left: 144px;
}
#btnBackground {
  background: @themeBackGroundColor;
}
#btnColor {
  color: @themeBackGroundColor;
}
#upgradeVersion {
  border: 1px solid @themeBackGroundColor;
}
#closeBut {
  background: @themeFontColor;
}
.yes {
  border-color: @themeBackGroundColor !important;
}
.selectY {
  float: left;
  width: 24px;
  height: 24px;
  background: #fff;
  border: 2px solid #D2D5D9;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  cursor: pointer;
}
.selectN {
  width: 14px;
  height: 14px;
  background: @themeBackGroundColor;
  margin-top: 3px;
  margin-left: 3px;
  border-radius: 50%;
  overflow: hidden;
}
.selectB {
  float: left;
  margin-left: 20px;
  font-size: 16px;
  color: @themeFontColor;
  margin-top: 10px;
  margin-right: 10px;
  cursor: pointer;
}
</style>
<template>
  <div class="pc_log">
    <el-dialog
      :visible.sync="loginMsg"
      :show-close='false'
      :close-on-click-modal='false'
      width="450px"
      top='214px'
    >
      <div class="tips_dialog">
        <div class="title">提示</div>
        <div class="content">
          当前处于断网状态，<br>请取消后，检查网络重新登录，<br>若仅使用离线功能，请点击登录按钮
        </div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            id="closeBut"
            @click="loginMsg = false"
          >取消</div>
          <div
            class="btn"
            id="btnBackground"
            style="margin-left:30px"
            :style="gologining ? 'opacity: 0.5' : ''"
            @click="gologin(loginType)"
          >登录</div>
        </div>
      </div>
    </el-dialog>
    <div class="pc_log1">
      <!--左侧logo图-->
      <img
        alt=""
        src="../../image/zgzn-pos/pc_login_logo.png"
        style="margin-top: 185px;margin-left: 60px;"
        @click="debugA++"
      />
      <!--右侧手机号密码-->
      <div
        class="pc_log11"
      >
        <el-tabs style="width: 400px;" v-model="activeType" stretch>
          <el-tab-pane label="扫码登录" name="scanningCode">
            <div style="margin-left: 135px;">
              <div style="margin-bottom: 15px;font-weight: 600;">
                <span>请使用微信扫一扫登录</span>
                <span style="cursor: pointer;" :style="refreshClick ? 'opacity: 0.6' : ''"  @click="refreshScanCode">
                  &nbsp;&nbsp;刷新 <i class="el-icon-refresh-right"
                    style="font-size: 15px;font-weight: 600;"></i>
                </span>
              </div>
              <el-image style="width: 184px;height: 184px;" :src="scanningCodeImg">
                <div slot="placeholder" class="pc_scan_loading_div">
                  <div style="margin: 68px auto;text-align: center;font-size: 20px;">
                    加载中...
                  </div>
                </div>
                <div slot="error" class="pc_load_error_div">
                  <i class="el-icon-picture-outline" style="font-size: 42px;"></i>
                </div>
              </el-image>
              <div v-show="status_code === 1" class="pc_waiting_empower_div">
                <div>
                  <div style="text-align: center;margin-top: 50px;"><i class="el-icon-circle-check" style="font-size: 42px;"></i></div>
                  <div style="text-align: center;margin-top: 10px;">请在手机上确认是否授权</div>
                </div>
              </div>
              <!-- 离线扫码提示 -->
              <div v-show="status_code === 4" class="pc_no_network_div">
                <div style="margin-top: 40px;">
                  <div style="width: 100%;height: 50px;">
                    <img src="../../image/pc_no_network.png" style="width: 56px;height: 49px;margin-left: 60px;" />
                  </div>
                  <div style="width: 100%;text-align: center;margin-top: 20px;">无网络，请检查网络连接</div>
                </div>
              </div>
              <!-- 等待mq连接成功 -->
              <div v-show="status_code === 5" class="pc_waiting_empower_div">
                <div style="margin-top: 40px;">
                  <div style="width: 100%;text-align: center;margin-top: 73px;font-size: 20px;">加载中...</div>
                </div>
              </div>
              <!-- mq连接失败 -->
              <div v-show="status_code === 6" class="pc_waiting_empower_div">
                <div style="margin-top: 40px;">
                  <div style="text-align: center;margin: 64px 10px 0px;font-size: 17px;">扫码登录暂不可用，请使用账号登录</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="账号登录" name="accountLogin">
            <div v-if="!lock_username">
              <div class="pc_log12">
                <div>手机号</div>
                <input
                  id="username"
                  type="text"
                  placeholder="请输入手机号"
                  v-model="username"
                  v-input-phone
                  maxlength="11"
                  :disabled="!ifCanRegister || sys_uid !== ''"
                />
                <icon
                  name="md-cancel"
                  @click.native="username=''"
                  v-show="username && ifCanRegister"
                  style="margin-left: -46px;margin-top: 19px;position: fixed;width: 25px;height: 25px;color: #C2D5DF;"
                ></icon>
              </div>
              <div
                class="pc_log12"
                style="margin-top: 13px;"
              >
                <div @click="debugB = true">密码</div>
                <input
                  id="password"
                  type="password"
                  placeholder="请输入密码"
                  v-model="password"
                  maxlength="32"
                />
              </div>
              <div
                class="pc_log12"
                style="width: 390px;margin-top: 20px;"
              >
                <div
                  style="font-size: 16px;
                    font-weight: bold;
                    line-height: 25px;
                    margin-top: 0px;
                    float: right;
                    width: 320px;"
                  @click="remember_pwd = !remember_pwd"
                >
                  <img
                    style="cursor: pointer;
                    width: 20px;
                    height: 20px;
                    float: left;
                    margin-top: 3px;"
                    alt="选择"
                    v-show='!remember_pwd'
                    src='../../image/zgzn-pos/pc_goods_checkbox1.png'
                  />
                  <img
                    style="cursor: pointer;
                      width: 20px;
                      height: 20px;
                      float: left;
                      margin-top: 3px;"
                    alt="选择"
                    v-show='remember_pwd'
                    src='../../image/zgzn-pos/pc_goods_checkbox2.png'
                  />
                  <span style="cursor: pointer;
                      float: left;
                      margin-left: 10px;">记住密码</span>
                  <span
                    style="float: right;cursor: pointer;"
                    @click="forgetpwd"
                  >忘记密码?</span>
                </div>
              </div>
              <div class="pc_log12">
                <div
                id="gologin"
                :style="gologining ? 'opacity: 0.5' : ''"
                @click="gologin(0)"
                >登录</div>
              </div>
              <!-- 注册链接  -->
              <div
                v-show="activeName!=='cashier' && ifCanRegister"
                @click="goRegister(true)"
                id="goRegister"
                >还没有账号？去注册<span style="font-family: Microsoft YaHei, sans-serif;">>></span></div>
            </div>
            <div v-else style="margin-top: -10px;">
              <div style="display: flex;justify-content: space-between;width: 296px;margin-left: 85px;">
                <div @click="activeName = 'administrators'">
                  <div
                    class="selectY"
                    :class="activeName === 'administrators' ? 'yes' : ''"
                  >
                    <div
                      class="selectN"
                      :style="activeName === 'administrators' ? '' : 'background: #FFF'"
                    ></div>
                  </div>
                  <div
                    class="selectB"
                  >管理员</div>
                </div>
                <div @click="activeName = 'cashier'">
                  <div
                    class="selectY"
                    :class="activeName === 'cashier' ? 'yes' : ''"
                  >
                    <div
                      class="selectN"
                      :style="activeName === 'cashier' ? '' : 'background: #FFF'"
                    ></div>
                  </div>
                  <div
                    class="selectB"
                  >收银员</div>
                </div>
              </div>
              <div v-show="activeName === 'administrators'" style="margin-top: 25px;">
                <div class="pc_log12" style="display: inline-block;">
                  <div>手机号</div>
                  <input
                    id="username"
                    type="text"
                    placeholder="请输入手机号"
                    v-model="username"
                    disabled
                    v-input-phone
                    maxlength="11"
                  />
                </div>
                <div
                  class="pc_log12"
                  style="margin-top: 13px;"
                >
                  <div @click="debugB = true">密码</div>
                  <input
                    id="administrators_password"
                    type="password"
                    placeholder="请输入密码"
                    v-model="password"
                    maxlength="32"
                  />
                </div>
                <div class="pc_log12">
                  <div
                    id="gologin"
                    :style="gologining ? 'opacity: 0.5' : ''"
                    @click="gologin(1)"
                  >登录</div>
                </div>
                <div class="pc_log12">
                  <div
                    style="font-size: 16px;
                    font-weight: bold;
                    line-height: 25px;
                    margin-top: 0px;
                    float: right;
                    width: 320px;"
                    @click="remember_pwd = !remember_pwd"
                  >
                    <img
                      style="cursor: pointer;
                      width: 20px;
                      height: 20px;
                      float: left;
                      margin-top: 3px;"
                      alt="选择"
                      v-show='!remember_pwd'
                      src='../../image/zgzn-pos/pc_goods_checkbox1.png'
                    />
                    <img
                      style="cursor: pointer;
                      width: 20px;
                      height: 20px;
                      float: left;
                      margin-top: 3px;"
                      alt="选择"
                      v-show='remember_pwd'
                      src='../../image/zgzn-pos/pc_goods_checkbox2.png'
                    />
                    <span style="cursor: pointer;
                      float: left;
                      margin-left: 10px;">记住密码</span>
                    <span
                      style="float: right;cursor: pointer;"
                      @click="forgetpwd"
                    >忘记密码?</span>
                  </div>
                </div>
              </div>
              <div v-show="activeName === 'cashier'" style="margin-top: 25px;">
                <div class="pc_log12" style="display: inline-block;">
                  <div>手机号</div>
                  <input
                    id="username"
                    type="text"
                    placeholder="请输入手机号"
                    v-model="username"
                    disabled
                    v-input-phone
                    maxlength="11"
                  />
                </div>
                <div
                  class="pc_log12"
                  style="margin-top: 13px;"
                >
                  <div>工号</div>
                  <el-select
                    ref="employeenumber"
                    v-model="employeenumber"
                    filterable
                    allow-create
                    default-first-option
                    v-input-length="15"
                    @blur='selectBlur'
                    style="width: 320px;
                          height: 61px;
                          background: #f5f8fb;
                          border-radius: 10px;"
                    placeholder="请输入工号"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div
                  class="pc_log12"
                  style="margin-top: 13px;"
                >
                  <div>密码</div>
                  <input
                    id="employeepassword"
                    type="password"
                    placeholder="请输入密码"
                    v-model="employeepassword"
                    maxlength="6"
                  />
                </div>
                <div class="pc_log12">
                  <div
                    id="gologin"
                    :style="gologining ? 'opacity: 0.5' : ''"
                    @click="gologin(2)"
                  >登录</div>
                </div>
                <div class="pc_log12">
                  <div
                    style="font-size: 16px;font-weight: bold;line-height: 25px;margin-top: 0px;float: left;width: 390px;"
                    @click="remember_employee = !remember_employee"
                  >
                    <img
                      style="cursor: pointer;margin-top: 4px;width: 20px;height: 20px;margin-left: 70px;"
                      alt="选择"
                      v-show='!remember_employee'
                      src='../../image/zgzn-pos/pc_goods_checkbox1.png'
                    />
                    <img
                      style="cursor: pointer;margin-top: 4px;width: 20px;height: 20px;margin-left: 70px;"
                      alt="选择"
                      v-show='remember_employee'
                      src='../../image/zgzn-pos/pc_goods_checkbox2.png'
                    />
                    <span style="padding-left: 10px;cursor: pointer;padding-right: 225px;">记住工号</span>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div class="login_top">
        <el-popover
          placement="bottom-start"
          width="224"
          trigger="click"
        >
          <div class="qr_container">
            <div id="btnColor" style="font-size: 16px;margin-top:6px;text-align:center;height: 28px;">
              {{$t('components.header.customer_service')}}
            </div>
          </div>
          <div
            slot="reference"
            class="top_btn"
            style="border:none;margin-right:44px;cursor:pointer"
          >
            <img
              src="@/image/zgzn-pos/icon_customer_service.png"
              style="width:20px;height:20px;margin-right:6px"
              alt=""
            >
            <div>联系客服</div>
          </div>
        </el-popover>
      </div>
      <el-popover
        placement="bottom-start"
        width="110"
        trigger="hover">
        <div style="color: rgba(189, 161, 105, 100);font-size: 14px;width:110px;height:40px;text-align:center;line-height: 40px;">点击切换业态</div>
        <div class="pc_industry_div" slot="reference" @click="chooseIndustry">
          <span>{{ cur_industry }}</span>
        </div>
      </el-popover>
      <div
        style="position: absolute;
        bottom: 30px;
        left: 30px;
        font-size: 16px;"
        v-show="showUpdateBtn && show_trial && !ifCanRegister"
        id="btnColor"
      >
        <span v-show="show_trial">{{0 >= trial_day ? '试用时间已到期' : `试用时间剩余${trial_day}天`}} </span> <span
          id="upgradeVersion" style="border-radius: 4px;padding: 8px;font-weight: bolder;
            margin-left: 11px;cursor: pointer;" @click="upgradeVersion()"
        > 升级更高版本</span>
      </div>
      <div @click="debugC = true" id="btnColor" style="position: absolute;bottom: 30px;right: 30px;
       font-size: 16px;">Ver{{version}}
      </div>
    </div>
    <div
      class='upgrade1'
      v-show='show_upgrade'
    >
      <div class='upgrade11'>
        <div class='upgrade12'><span>{{show_tips? '升级更高版本' : '提示'}}</span><span
            class='upgrade13'
            @click="show_upgrade = false"
          >×</span>
        </div>
        <div
          v-show="show_tips && ultimate !== null && ultimate === true"
          style="color: #4C567C;width: 93%;margin-left:66px;font-size: 24px;"
        >
          <div>对不起，您的账号{{0 > trial_day ? '已' : '将'}}过试用期</div>
          <div>如需继续使用，请升级更高版本</div>
        </div>
        <div class="upgrade14">
          <input
            type="text"
            placeholder="请输入有效激活码"
            v-model="jihuocode"
            maxlength="50"
            onKeypress="javascript:if(event.keyCode == 32)event.returnValue = false;"
          />
        </div>
        <div class="upgrade12">
          <div
            style="background-color: #567485;
            width: 130px;
            border-radius: 4px;
            font-size: 20px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            line-height: 50px;
            text-align: center;
            float: left;
            cursor: pointer;
            margin: 10px;
            margin-left: 46px;
          "
            @click="show_upgrade = false;"
          >取消</div>
          <div
            id="btnBackground"
            style="
            width: 130px;
            border-radius: 4px;
            font-size: 20px;
            font-weight: bold;
            color: rgb(255, 255, 255);
            line-height: 50px;
            text-align: center;
            float: right;
            cursor: pointer;
            margin: 10px;
            margin-right: 53px;"
            :style="jihuoerchong ? 'opacity: 0.7;' : ''"
            @click="jihuo"
          >升级</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import print from '@/components/pc_print_setting.vue';
import CryptoJS from 'crypto-js';
import logList from '@/config/logList';
import { newMd5 } from '../../utils/newMd5';

export default {
  mixins: [print],
  data () {
    return {
      client: null,
      mqttMessage: null,
      status_code: 0, // 消息状态
      loginType: 1,
      loginMsg: false,
      activeType: 'scanningCode', // scanningCode accountLogin
      activeName: 'administrators',
      zgznClientId: '',
      refreshClick: false,
      scanningCodeImg: '', // 登录用二维码
      fullscreenLoading: null,
      username: '', // 用户名
      password: '', // 密码
      password_local: '', // 本地存储的密码
      jihuocode: '', // 激活code
      jihuoerchong: false, // 激活二重限制
      remember_pwd: false, // 记住密码
      employeenumber: '', // 员工号
      employeepassword: '', // 员工密码
      remember_employee: false, // 记住工号
      lock_username: false, // 锁定登录号
      username_list: [], // 用户名下拉
      gologining: false, // 防止二重登录
      show_trial: false, // 是否显示体验版
      trial_day: '0', // 剩余体验天数
      show_upgrade: false, // 是否提示升级
      show_tips: true, // 是否提示升级
      employeenumberList: [],
      options: [],
      ifCanRegister: true,
      cur_industry: '',
      debugA: 0,
      debugB: false,
      debugC: false,
      showUpdateBtn: true // 默认不显示升级按钮
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 点击升级版本按钮
     */
    upgradeVersion() {
      this.show_tips = true;
      this.show_upgrade = true;
      demo.actionLog(logList.upgradeVersion);
    },
    generateUUID() {
      var d = new Date().getTime();
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now();
      }
      var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
      });
      return uuid;
    },
    autoUpdateUser() {
      const param = {
        'phone': this.username
      };
      if (pos.network.isConnected() && this.username) {
        demo.$http.post(this.$rest.autoUpdateUser, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.code === 200) {
              this.SET_SHOW({ isAuto: res.data.data });
            }
          });
      }
    },
    selectTab() {
      if (this.ultimate === null && this.activeName !== 'administrators') {
        this.SET_SHOW({isBuySoftware: true});
        var that = this;
        setTimeout(function() {
          that.activeName = 'administrators';
        }, 0);
      }
    },
    // 获取是否记住密码、手机号、密码
    getUserPwd () {
      var remember = $setting.userremember;
      var username = $setting.useruid;
      var password = $setting.userpwd;
      var password_local = $setting.userpwd;
      var employeeremember = $setting.employeeremember;
      var employeenumber = $setting.employeenumber;
      var enddate;
      if ($config.activationCode) {
        enddate = Number($config.activationCode.endDate);
        this.SET_SHOW({ period: Number($config.activationCode.period) });
      } else {
        enddate = new Date('2099-12-31 00: 00: 00').getTime();
        this.SET_SHOW({ period: -1 });
      }
      this.SET_SHOW({ sysEndDate: enddate });
      cefSharp.postMessage('ultimate版本的值：' + JSON.stringify($config.activationCode) + '------');
      this.SET_SHOW({
        ultimate: $config.activationCode === null || $config.activationCode.ultimate === '' ? null : JSON.parse($config.activationCode.ultimate)
      });
      this.employeenumberList = $setting.employeenumber_list
        ? $setting.employeenumber_list.split(',') : ''.split(',');
      this.options = this.employeenumberList.map(e => {
        return {
          value: e,
          label: e
        };
      });
      if (remember === '1') {
        this.password = password;
        this.password_local = password_local;
        this.remember_pwd = true;
      }
      if (username) {
        this.lock_username = true;
        this.username = username;
        this.username_list = [{ username: username }];
      }
      if (employeeremember === 'true' || employeeremember === 'True') {
        this.employeenumber = employeenumber;
        this.remember_employee = true;
      } else if (employeeremember === 'false' || employeeremember === 'False') {
        this.employeenumber = '';
        this.remember_employee = false;
      } else {
        // 上次没有保存的情况下，不做任何操作
      }
      // 是否显示左下角提示升级专业版
      this.sonarGetPwd(enddate);
    },
    sonarGetPwd(enddate) {
      if (this.username) {
        this.SET_SHOW({ sys_uid: this.username });
        if (pos.network.isConnected()) {
          if (this.period !== -1 || this.ultimate === null) {
            this.show_trial = true;
          }
        } else {
          this.getPwdOffline(enddate);
        }
      }
    },
    getPwdOffline(enddate) {
      if (this.period !== -1 || this.ultimate === null) {
        this.trial_day = Math.ceil((+enddate - new Date().valueOf()) / 1000 / 60 / 60 / 24) === 0
          ? 0 : Math.ceil((+enddate - new Date().valueOf()) / 1000 / 60 / 60 / 24);
        this.SET_SHOW({ trialDay: this.trial_day });
        this.show_trial = true;
      }
    },
    // 激活
    jihuo () {
      if (!this.jihuocode.trim()) {
        demo.msg('warning', '请输入有效激活码');
        return;
      }
      if (this.jihuoerchong) {
        return;
      }
      this.jihuoerchong = true;
      this.$http.post(this.$rest.updateCode, {
        'phone': this.username,
        'activeCode': this.jihuocode,
        'systemName': $config.systemName,
        'subName': $config.subName
      }, {
        headers: { 'Content-Type': 'application/json' }
      }).then(res => {
        if (res.data === undefined) {
          demo.msg('error', "无法连接远端服务器,处于离线状态");
        } else if (res.data.code === 200) {
          this.show_upgrade = false;
          // 更新vuex版本相关
          demo.msg('success', '绑定激活码成功');
          this.jihuocode = '';
          this.jihuoSonar(res);
        } else {
          demo.msg('warning', res.data.msg);
        }
        this.recoverJihuoClick();
      }).catch(error => {
        this.recoverJihuoClick();
        demo.msg('error', error);
      });
    },
    recoverJihuoClick() { // 激活防连点恢复可用
      setTimeout(() => {
        this.jihuoerchong = false;
      }, 1000);
    },
    jihuoSonar(res) {
      this.SET_SHOW({ period: res.data.data.period });
      this.trial_day = +res.data.data.remainDay + 1;
      this.SET_SHOW({ trialDay: this.trial_day });
      this.SET_SHOW({ sysEndDate: res.data.data.endDate });
      let ultimate = (this.trial_day < 0 && !(this.period === -1)) ? null : JSON.parse(res.data.data.ultimate);
      this.SET_SHOW({ ultimate: ultimate });
      this.show_trial = this.period !== -1;
      settingService.put([
        { key: settingService.key.period, value: this.period },
        { key: settingService.key.enddate, value: res.data.data.endDate },
        { key: settingService.key.ultimate, value: ultimate }
      ], () => {});
      this.SET_SHOW({showTemporaryTip: false});
      demo.actionLog(logList.upgradeSuccess);
    },
    selectBlur (value) {
      this.employeenumber = value.path[0].value;
    },
    mqttSuccess(data) {
      var print_data = JSON.parse(data);
      if (print_data.sendtype === undefined) {
        print_data.flg = 1;
        pos.printer.wd_printPOS(print_data);
      } else {
        if (print_data.sendtype === 'downloaddata') {
          if (confirm('是否下载云端数据到本地？')) {
            external.downloaddata(print_data.url);
          }
        }
        if (print_data.sendtype === 'uploaddata') {
          external.uploaddata();
        }
        if (print_data.sendtype === 'popmsg') {
          external.popmsg(print_data.msg);
        }
      }
    },
    // 验证码检查
    checkCode() {
      var ultimate = null;
      let endDate;
      // 激活码相关
      if (!$config.activationCode) { // 无激活码&激活码已过期
        endDate = new Date('2099-12-31 00:00:00').getTime();
        this.SET_SHOW({ period: -1 });
        this.show_trial = !this.isFirstLogin;
        this.trial_day = -1;
      } else {
        endDate = new Date($config.activationCode.endDate).getTime();
        this.SET_SHOW({ period: $config.activationCode.period });
        this.trial_day = +$config.activationCode.remainDay + 1;
        ultimate = $config.activationCode.ultimate;
        this.show_trial = $config.activationCode.period !== -1;
      }
      this.SET_SHOW({
        // 激活码相关
        ultimate: JSON.parse(ultimate),
        sysEndDate: endDate,
        trialDay: this.trial_day
      });
    },
    // 登录提交
    gologin (type) {
      this.loginType = type;
      if (!pos.network.isConnected() && this.isFirstLogin) {
        demo.msg('warning', this.$msg.login_network_title);
        return;
      }
      if (!pos.network.isConnected() && !this.loginMsg) {
        this.loginMsg = true;
        return;
      }
      if (this.gologining === true) {
        return;
      }
      this.gologining = true;
      if (!this.username) {
        $('#username').focus();
        demo.msg('warning', this.$msg.enter_mobile_number);
        this.loginMsg = false;
        this.gologining = false;
        return;
      }
      if (type !== 2) {
        if (!this.password) {
          type === 0 ? $('#password').focus() : $('#administrators_password').focus();
          demo.msg('warning', this.$msg.enter_password);
          this.loginMsg = false;
          this.gologining = false;
          return;
        }
      } else {
        if (!this.employeenumber) {
          this.$refs.employeenumber.focus();
          demo.msg('warning', this.$msg.enter_employee_number);
          this.loginMsg = false;
          this.gologining = false;
          return;
        }
        if (!this.employeepassword) {
          demo.msg('warning', this.$msg.enter_user_password);
          $('#employeepassword').focus();
          this.loginMsg = false;
          this.gologining = false;
          return;
        }
      }
      const password = type !== 2
        ? (this.password.length < 32 ? md5(this.password)
          : (this.password === this.password_local ? this.password : md5(this.password))
        )
        : md5(this.employeepassword);
      this.SET_SHOW({ password: password });
      this.SET_SHOW({ phone: this.username });
      var loginOffline = () => {
        // 离线登陆，判断手机号密码是否正确。1：正确；0：错误
        var data = {
          username: this.username,
          employeeNumber: this.employeenumber,
          userpwd: password,
          type: type
        };
        settingService.offlineLogin(data, res => {
          var res0 = demo.t2json(res);
          console.log(res0, 'res0');
          if (+res0[0].iftrue === 0) {
            if (type === 2) {
              demo.msg('warning', '工号密码验证失败，请确认输入内容，或联网后重新验证');
            } else {
              demo.msg('warning', this.$msg.login_failure);
            }
            this.gologining = false;
          } else {
            var cnt = 0;
            // 将手机号、密码、是否记住密码状态、登陆时间 插入到settings表
            var settingData0 = [
              { key: settingService.key.username, value: this.username },
              {
                key: settingService.key.userlastime,
                value: new Date().format('yyyy-MM-dd hh:mm:ss')
              }
            ];
            if (type !== 2) {
              settingData0.push(
                {
                  key: settingService.key.userpwd,
                  value: password
                },
                {
                  key: settingService.key.userremember,
                  value: this.remember_pwd ? '1' : '0'
                },
                {
                  key: settingService.key.employeenumber,
                  value: ''
                }
              );
            } else {
              if (this.remember_employee) {
                this.employeenumberList = [...new Set(this.employeenumberList).add(this.employeenumber)];
              } else {
                this.employeenumberList = this.employeenumberList.filter(e => e !== this.employeenumber);
              }
              settingData0.push(
                {
                  key: settingService.key.employeeremember,
                  value: this.remember_employee
                },
                {
                  key: settingService.key.employeenumber_list,
                  value: this.employeenumberList.join()
                },
                {
                  key: settingService.key.employeenumber,
                  value: this.employeenumber
                }
              );
            }
            if ($setting.devicecode) {
              this.SET_SHOW({ devicecode: $setting.devicecode });
            }
            settingService.put(settingData0, () => {
              console.log(cnt, 'settingService.put cnt++');
              cnt++;
              if (cnt === 5) {
                this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
                console.log('离线登录成功');
                demo.actionLog(logList.offlineLogin);
              }
            }, () => {
              this.gologining = false;
            });
            // 获取sys_uid、sys_sid、token
            var settingData1 = [
              { key: settingService.key.useruid },
              { key: settingService.key.usersid },
              { key: settingService.key.usertoken }
            ];
            settingService.get(
              settingData1,
              res1 => {
                var sys_uid;
                var sys_sid;
                var token;
                demo.t2json(res1).forEach(item => {
                  if (item.key === 'useruid') {
                    sys_uid = item.value;
                  } else if (item.key === 'usersid') {
                    sys_sid = item.value;
                  } else if (item.key === 'usertoken') {
                    token = item.value;
                  } else {
                    // todo
                  }
                });
                cnt++;
                console.log(cnt, 'settingService.get cnt++');
                if (cnt === 5) {
                  this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
                  console.log('离线登录成功');
                  demo.actionLog(logList.offlineLogin);
                }
                this.SET_SHOW({
                  sys_uid: sys_uid,
                  sys_sid: sys_sid,
                  // token: token
                  token: '' // 后台改掉了token里会存uid，如果登录时没网登陆后有网，会发生错乱
                });
              },
              err => {
                this.gologining = false;
                demo.msg('warning', err.msg);
              }
            );
            // 获取店铺信息
            storeInfoService.get({ id: 1 }, stores => {
              var res1 = demo.t2json(stores);
              if (res1.length > 0) {
                settingService.setInfoValueForKey('loginType', this.activeName);
                cnt++;
                console.log(cnt, 'storeInfoService.get cnt++');
                this.SET_SHOW({ storeList: res1, username: res1[0].name });
                if (cnt === 5) {
                  this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
                  console.log('离线登录成功');
                  demo.actionLog(logList.offlineLogin);
                }
              }
            }, () => {
              this.gologining = false;
            });
            if ($setting.posprint) {
              this.SET_SHOW({ setting_small_printer: $setting.posprint });
            }
            if ($setting.labelprint) {
              this.SET_SHOW({ setting_label_printer: $setting.labelprint });
            }
            if ($setting.tipprint) {
              this.SET_SHOW({ setting_tip_printer: $setting.tipprint });
            }
            if ($setting.tagPrint) {
              this.SET_SHOW({ setting_tag_printer: $setting.tagPrint });
            }
            if ($setting.moneybox) {
              this.SET_SHOW({ setting_moneybox: $setting.moneybox });
            }
            if ($setting.has_moneybox) {
              this.SET_SHOW({ setting_hasmoneybox: $setting.has_moneybox });
            }
            cnt++;
            console.log(cnt, 'normal cnt++');
            if (cnt === 5) {
              this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
              console.log('离线登录成功');
              demo.actionLog(logList.offlineLogin);
            }
            // clerks中获取shopusers
            if ($clerks) {
              console.log(this.employeenumber, 'bbb');
              $clerks.forEach(clerk => {
                // type为1表示员工登录 2表示管理员登录
                let employeenumber = type === 1 ? '' : this.employeenumber;
                if (clerk.employee_number === employeenumber) {
                  this.setData(clerk, type);
                  cnt++;
                }
              });
              console.log(cnt, '$clerks cnt++');
              if (cnt === 5) {
                this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
                console.log('离线登录成功');
                demo.actionLog(logList.offlineLogin);
              }
            }
          }
        });
      };
      if (this.loginMsg) {
        this.loginMsg = false;
        demo.$store.dispatch('info/getSpec').then(() => {
          loginOffline();
        }).catch(err => {
          demo.msg('warning', this.$msg.login_network_title);
          console.error(err);
        });
        this.gologining = false;
        return;
      }
      if (pos.network.isConnected()) { // 如果是联网状态，请求在线login，可以得到sys_uid、sys_sid，能进行后续图片上传
        var params = {
          username: this.username,
          employeeNumber: type === 2 ? this.employeenumber : '',
          employeeremember: type === 2 ? this.remember_employee : false,
          password: password,
          userremember: this.remember_pwd ? '1' : '0',
          type: type,
          version_db: '3.14.0',
          sysSid: 1,
          authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
        };
        console.log(JSON.stringify(params), 'params1');
        external.login(params, result => {
          demo.actionLog(logList.login);
          CefSharp.PostMessage('账号密码登录，params:' + JSON.stringify(params));
          this.loginSuccess(result, type);
        }, err => {
          demo.msg('warning', err);
          this.gologining = false;
          console.log(err, 'errorrrrr');
        });
      }
    },
    loginSuccess(result, type) {
      this.autoUpdateUser();
      console.log(demo.t2json(result), 'external.login result++');
      var res = demo.t2json(result);
      console.log(res.data.employeeNumber, '登录时employeeNumber', type);
      this.SET_SHOW({
        fingerprint: res.data.fingerprint,
        sys_uid: res.data.sysUid,
        sys_sid: res.data.sysSid,
        token: 'Bearer ' + res.data.token,
        hasWd: res.data.hasWd,
        devicecode: res.data.deviceCode,
        loginInfo: res.data,
        storeList: res.data.shopList,
        username: res.data.shopList[0].name,
        employeeAuth: res.data.privilege.split(',')
      });
      demo.$store.dispatch('info/getSpec').then(() => {
        this.loginSpecsSuccess();
      });
    },
    // 登陆成功
    loginSpecsSuccess () {
      if (this.activeType === 'scanningCode') {
        settingService.setInfoValueForKey('loginType', 'scanningCode');
      } else {
        settingService.setInfoValueForKey('loginType', this.activeName);
      }
      if (this.isFirstLogin) {
        this.paySetting();
      }
      this.getChangeNowId();
      this.SET_SHOW({ isLogin: false, isHome: true, isFirstHome: true });
      console.log('登录完变量变更');
      console.log(this.isFirstLogin);
      if (this.isFirstLogin) {
        var result = demo.t2json(external.created());
        window.$config = result.$config;
        this.checkCode();
      }
      // 云同步
      this.SET_SHOW({ isSyncingLogin: true });
      syncService.clearDataInfo(() => {
        this.synced(1);
        upgradeService.execute();
      }, () => {
        this.synced(0);
        upgradeService.execute();
      });
    },
    synced (result) {
      var that = this;
      console.log('synced执行' + result);
      that.SET_SHOW({isSyncingLogin: false});
      external.loginSynced(() => {
        console.log('external.loginSynced成功callback');
        if (that.isFirstLogin) {
          var result = demo.t2json(external.created());
          window.$config = result.$config;
          setTimeout(() => {
            that.checkCode();
          }, 0);
        }
        that.SET_SHOW({
          isOpenSMS: true,
          setting_small_printer: $setting.posprint,
          setting_label_printer: $setting.labelprint,
          setting_tip_printer: $setting.tipprint,
          setting_moneybox: $setting.moneybox,
          setting_hasmoneybox: $setting.has_moneybox,
          font_value: $setting.font_value,
          line_height: $setting.line_height,
          print_cols: $setting.print_cols,
          print_cols_label: $setting.print_cols_label
        });
        console.log('*********');
      }, mqttData => {
        console.log('loginSynced失败callback');
        that.mqttSuccess(mqttData);
      });
      // 默认延迟30分钟后云同步
      that.loopsiloop();
    },
    isCanSync () {
      let iCloud = demo.$store.state.show.storeList[0].settings !== '' &&
        JSON.parse(demo.$store.state.show.storeList[0].settings).hasOwnProperty('iCloud')
        ? JSON.parse(demo.$store.state.show.storeList[0].settings).iCloud : false;
      var stopCloudList = demo.$store.state.show.storeList[0].settings !== '' &&
        JSON.parse(demo.$store.state.show.storeList[0].settings).hasOwnProperty('stopCloudList')
        ? JSON.parse(demo.$store.state.show.storeList[0].settings).stopCloudList : false;
      var canSync = true;
      var selectStr = [];
      var hTime = new Date().getHours();
      if (stopCloudList) {
        for (var i = 0; i < stopCloudList.length; i++) {
          selectStr.push(stopCloudList[i].select)
          if (stopCloudList[i].select && stopCloudList[i].startTime <= hTime && stopCloudList[i].endTime > hTime) {
            canSync = false;
            break;
          }
        }
      }
      return (!iCloud || (iCloud && !demo.$store.state.show.isPay)) &&
        (!stopCloudList || canSync || (selectStr.indexOf(true) === -1 && selectStr.length !== 0));
    },
    loopsiloop (interval) {
      var that = this;
      interval = interval || that.syncInterval;
      setTimeout(() => {
        if (!that.isSyncing && that.isCanSync()) {
          that.sync(interval);
          cefSharp.postMessage('进行云同步：' + new Date());
        } else {
          that.loopsiloop();
        }
      }, interval);
    },
    // 云同步 连续失败10次 停止自动云同步 每失败一次多延迟1分钟
    sync (interval) {
      var that = this;
      syncService.clearDataInfo(() => {
        that.SET_SHOW({isSyncingLogin: false});
        that.syncFailed = 0;
        that.loopsiloop();
      }, () => {
        that.SET_SHOW({isSyncingLogin: false});
        if (pos.network.isConnected()) {
          that.syncFailed++;
        }
        if (that.syncFailed < 10) {
          interval = interval + 60000;
          that.loopsiloop(interval);
        }
      });
    },
    mqttConnect (flag) {
      let mqttOptions = _.cloneDeep(demo.t2json($config.mqttOptions));
      mqttOptions.keepAlive = 60;
      mqttOptions.groupId = 'GID_ZGCM'
      mqttOptions.clientId = mqttOptions.groupId + '@@@' + md5(external.getMac());
      mqttOptions.port = 443;
      mqttOptions.username = 'Signature|' + mqttOptions.accessKey + '|' + mqttOptions.instanceId;
      mqttOptions.password = CryptoJS.HmacSHA1(mqttOptions.clientId, mqttOptions.secretKey).toString(CryptoJS.enc.Base64);
      this.zgznClientId = mqttOptions.clientId;
      // 连接mqtt broker
      this.client = new Paho.MQTT.Client(mqttOptions.brokerUrl, mqttOptions.port, mqttOptions.clientId);
      let options = {
        timeout: 6000,
        mqttVersion: 4,
        cleanSession: mqttOptions.cleanSession,
        onSuccess: () => {
          console.log('mq onSuccess');
          CefSharp.PostMessage('登录mqtt连接成功');
          this.status_code = 0;
        },
        onFailure: e => {
          console.log('mq onFailure:', e);
          CefSharp.PostMessage('登录mqtt连接失败，errorMsg:' + e);
          if (!flag) {
            CefSharp.PostMessage('登录mqtt尝试三秒后重连一次');
            setTimeout(this.mqttConnect(true), 3000);
          } else {
            this.status_code = 6;
          }
        }
      }
      this.client.onMessageArrived = (message) => {
        this.receivedMessage(demo.t2json(message));
      };
      if (mqttOptions.username != null) {
        options.userName = mqttOptions.username;
        options.password = mqttOptions.password;
        options.useSSL = true;
      }
      this.client.connect(options);
    },
    receivedMessage(message) {
      let topic = message.destinationName;
      let payload = demo.t2json(message.payloadString);
      CefSharp.PostMessage('mqtt zgznClientId:' + this.zgznClientId + ', topic:' + topic +
        ', payload:' + message.payloadString);
      this.SET_SHOW({ zgcmMqttInfo: payload });
      let data = {
        sysUid: payload.sysUid,
        appletId: payload.unionId,
        wxOpenId: payload.openId,
        wxPhone: payload.sysUid,
        authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
      };
      if ($userinfo.sysUid && $userinfo.sysUid !== (payload.sysUid + '')) {
        CefSharp.PostMessage('公众号授权手机号与已登录账号不同');
        demo.msg('warning', '系统暂不支持切换账号，请使用注册时的微信号扫码登录');
        return;
      }
      this.checkUserExit(data);
    },
    updateQkdInfo(payload) {
      this.SET_SHOW({ zgcmMqttInfo: payload });
      this.status_code = payload.status;
    },
    checkUserExit (data) {
      this.$http.post(this.$rest.checkUserExit, data).then(res => {
        console.log(res);
        if (res.data.status === 200) {
          if (!res.data.data) { // 用户不存在，跳注册
            CefSharp.PostMessage('微信公众号授权跳转注册页');
            this.goRegister();
          } else { // 用户存在，跳登录
            this.fullscreenLoading = this.$loading({fullscreen: true, background: 'rgba(0, 0, 0, 0.5)'});
            var params = {
              data: res.data,
              type: 1,
              userremember: this.remember_pwd ? '1' : '0',
              password: res.data.data.password,
              version_db: '3.14.0',
              authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
            };
            console.log(JSON.stringify(params));
            external.login(params, result => {
              CefSharp.PostMessage('商家助手公众号授权登录，params:' + JSON.stringify(params));
              demo.actionLog(logList.login_zgcm);
              this.reloadWindowObj();
              this.SET_SHOW({ password: res.data.data.password });
              if (!this.username) {
                this.username = data.sysUid;
              }
              this.loginSuccess(result, 1);
              // 断开长链接
              if (this.client && this.client.isConnected()) {
                CefSharp.PostMessage('登录完成后断开mqtt连接');
                this.client.disconnect();
              }
            }, err => {
              demo.msg('warning', err);
              this.gologining = false;
            });
          }
        } else {
          demo.msg('warning', res.data.msg);
        }
      }).catch(err => {
        console.error(err);
      });
    },
    reloadWindowObj() {
      var result = demo.t2json(external.created());
      if (result.$storeinfo.length !== 0) {
        this.SET_SHOW({ isFirstLogin: false });
      }
      window.$clerks = result.$clerks;
      window.$setting = result.$setting;
      window.$storeinfo = result.$storeinfo;
      window.$userinfo = result.$userinfo;
      window.$config = result.$config;
    },
    setData (loginInfo, type) {
      loginInfo['employeeNumber'] = loginInfo.employee_number;
      this.SET_SHOW({ loginInfo: loginInfo });
      this.SET_SHOW({
        employeeAuth: loginInfo.privilege.split(',')
      });
      let uuid = this.generateUUID();
      let fingerprint = md5(uuid);
      shiftHistoryService.insertShiftHistories({
        uid: loginInfo.uid,
        employee_number: loginInfo.employeeNumber ? loginInfo.employeeNumber : null,
        name: loginInfo.name,
        create_by: loginInfo.uid,
        fingerprint: fingerprint
      }, () => {
        this.SET_SHOW({ fingerprint: fingerprint });
        this.getChangeNowId();
      });
    },
    getChangeNowId() { // 获取当前班次的交接班id
      console.log(`fingerprint:${this.fingerprint}`);
      shiftHistoryService.getShiftOneHistories({fingerprint: this.fingerprint}, res => {
        if (res.length > 0) {
          this.SET_SHOW({ nowChangeId: res[0].id });
        }
      }, () => {
        // todo
      })
    },
    forgetpwd () {
      this.SET_SHOW({ isLogin: false, isForgetpwd: true });
    },
    goRegister (flag) {
      if (flag) {
        this.SET_SHOW({ qkdMqttInfo: {} });
      }
      this.SET_SHOW({ isLogin: false, isRegister: true });
    },
    chooseIndustry() {
      if (this.gologining) {
        return;
      }
      this.SET_SHOW({ isLogin: false, isHeader: true, isChooseIndustry: true });
    },
    getAid() {
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.getaid, { 'sysUid': this.sys_uid, 'aid': 'pay' })
          .then(rs => {
            let res = rs.data;
            if (res.code === '0' && res.data !== null) {
              let data = res.data;
              if (data) {
                this.SET_SHOW({aid: data.aid, app_secret: data.appSecret, sysId: data.sysId});
                return;
              }
            }
            this.SET_SHOW({aid: '', app_secret: '', sysId: ''});
          })
          .catch(() => {
            this.SET_SHOW({aid: '', app_secret: '', sysId: ''});
            demo.msg('warning', this.$msg.get_aid_fail);
          });
      }
    },
    /**
     * 监听回车键
     */
    listenerKeyDown (e) {
      if (e.keyCode === 13) {
        if (this.lock_username) {
          this.activeName === 'administrators' ? this.gologin(1) : this.gologin(2)
        } else {
          this.gologin(0);
        }
      }
    },
    postCheckCode() { // 订单完成后查看最高版本的激活码信息
      if (this.sys_uid === '' || !pos.network.isConnected()) {
        return;
      }
      let params = {
        phone: this.sys_uid
      }
      demo.$http.post(this.$rest.checkCode, params)
        .then(res => {
          const info = res.data.data
          this.trial_day = info.remainDay + 1;
          this.SET_SHOW({
            // 激活码相关
            ultimate: info.ultimate,
            sysEndDate: info.endDate,
            trialDay: info.remainDay + 1,
            showKefu: info.agentType === 1
          });
        });
    },
    createScanningCode () { // 生成登录二维码
      if (!pos.network.isConnected()) {
        demo.msg('warning', this.$msg.login_network_title);
        this.status_code = 4;
        return;
      }
      let clientId = md5(external.getMac());
      let param = {
        systemName: $config.systemName,
        subName: $config.subName,
        from: 1,
        clientId: clientId
      };
      if (!this.isFirstLogin && $setting.useruid) {
        param['sysUid'] = $setting.useruid;
      }
      demo.$http
        .post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmCreateQrCode, param)
        .then(res => {
          console.log(res);
          CefSharp.PostMessage(`登录生成二维码res:${JSON.stringify(res)}`);
          if (res.data.code === 0 && res.data.data) {
            this.scanningCodeImg = res.data.data.qrCodeUrl;
            if (!this.isFirstLogin) {
              settingService.setInfoValueForKey('official_account_code_img_url', this.scanningCodeImg); // 缓存图片地址
            }
            this.status_code = 5;
            this.mqttConnect();
          } else {
            this.scanningCodeImg = '';
          }
        }, error => {
          this.scanningCodeImg = '';
          CefSharp.PostMessage(`登录生成二维码error:${JSON.stringify(error)}`);
          demo.msg('warning', '登录二维码获取失败，请稍后再试');
        });
    },
    autoLoginCheck() {
      if (this.autoLogin) {
        this.gologining = true;
        this.fullscreenLoading = this.$loading({fullscreen: true});
        if (this.showAccountLogin) {
          this.activeType = 'accountLogin';
          this.activeName = 'administrators';
        }
        if (Object.keys(this.qkdMqttInfo).length !== 0) { // 扫码授权登录
          let data = _.cloneDeep(this.qkdMqttInfo);
          if (data.sysUid) {
            data.wxPhone = data.sysUid;
          }
          data.authorityVersion = this.DICT['PERMISSIONS']['AUTHORITYVERSION'];
          this.checkUserExit(data);
        } else {
          let params = {
            username: this.phone,
            employeeNumber: '',
            employeeremember: false,
            password: this.registerPassword,
            userremember: '0',
            type: 0,
            version_db: '3.14.0',
            authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
          };
          console.log('自动登录用params+', params);
          external.login(params, result => {
            demo.actionLog(logList.login);
            CefSharp.PostMessage('自动登录，params:' + JSON.stringify(params));
            this.loginSuccess(result, 0);
          }, err => {
            demo.msg('warning', err);
            this.gologining = false;
          });
        }
      }
    },
    loginTypeShow() {
      const oldLoginType = settingService.getInfoValueForKey('loginType');
      if (oldLoginType === 'administrators') {
        this.activeType = 'accountLogin';
        this.activeName = 'administrators'
      } else if (oldLoginType === 'cashier') {
        this.activeType = 'accountLogin';
        this.activeName = 'cashier';
      } else {
        // todo 默认扫码登录
      }
    },
    refreshScanCode() { // 微信扫一扫登录
      if (this.refreshClick) {
        return;
      }
      this.refreshClick = true;
      setTimeout(() => {
        this.refreshClick = false;
      }, 2000);
      this.status_code = 0;
      this.createScanningCode();
    }
  },
  computed: mapState({
    isLogin: state => state.show.isLogin,
    isFirstLogin: state => state.show.isFirstLogin,
    isHome: state => state.show.isHome,
    ifLogin: state => state.show.ifLogin,
    isSyncing: state => state.show.isSyncing,
    token: state => state.show.token,
    sys_uid: state => state.show.sys_uid,
    ultimate: state => state.show.ultimate,
    showTipsDialog: state => state.show.showTipsDialog,
    syncInterval: state => state.show.syncInterval,
    registerPassword: state => state.show.registerPassword,
    qkdMqttInfo: state => state.show.qkdMqttInfo,
    autoLogin: state => state.show.autoLogin,
    showAccountLogin: state => state.show.showAccountLogin,
    fingerprint: state => state.show.fingerprint,
    phone: state => state.show.phone,
    qkdSign: state => state.show.qkdSign,
    trialDay: state => state.show.trialDay,
    period: state => state.show.period
  }),
  created () {
    this.syncFailed = 0;
    this.version = this.$rest.version;
    this.username = this.sys_uid;
    this.lock_username = !!this.sys_uid;
    this.ifCanRegister = !(window.$storeinfo && window.$storeinfo.length > 0);
    let comsList = external.getPortNames();
    this.SET_SHOW({ weighList: comsList });
    this.SET_SHOW({ kexianList: comsList });
    let weighType = demo.t2json(external.getScaleList());
    let weighs = [];
    if (weighType !== null) {
      for (let k in weighType) {
        weighs.push({label: weighType[k], value: k});
      }
    }
    this.SET_SHOW({ weighTypeList: weighs });
    if (!this.isFirstLogin) {
      console.log('非首次登陆!');
      this.getUserPwd();
      if (pos.network.isConnected()) {
        this.postCheckCode();
      } else {
        this.checkCode();
      }
    }
    this.loginTypeShow();
    // 调用之前需要看一下是否默认展示扫码入口
    this.createScanningCode();
    // 注册完成后立即登录
    this.autoLoginCheck();
  },
  mounted () {
    document.addEventListener('keydown', this.listenerKeyDown);
    this.cur_industry = $config.Industry.name;
  },
  watch: {
    showTipsDialog() {
      if (this.showTipsDialog) {
        external.closeMainForm();
      }
    },
    activeType() {
      if (this.activeType === 'scanningCode' && this.scanningCodeImg === '') {
        this.createScanningCode();
      }
    },
    debugC() {
      if (this.debugA === 5 && this.debugB && this.debugC) {
        pos.chrome.debug();
        this.debugA = 0;
        this.debugB = false;
        this.debugC = false;
      } else {
        this.debugA = 0;
        this.debugB = false;
        this.debugC = false;
      }
    }
  },
  beforeDestroy () {
    document.removeEventListener('keydown', this.listenerKeyDown);
    if (this.isHome) {
      this.getAid();
    }
    if (this.client && this.client.isConnected()) {
      CefSharp.PostMessage('login页面生命周期结束时断开mqtt连接');
      this.client.disconnect();
    }
    if (this.fullscreenLoading) {
      this.fullscreenLoading.close();
    }
    this.postCheckCode();
  }
};
</script>
