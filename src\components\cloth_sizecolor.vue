<style lang='less' scoped>
.clt_siz {
  width: 100%;height: 100%;background: rgba(0, 0, 0, .8);
  top: 0;left: 0;position: fixed;z-index: 999;overflow: hidden;
}
.clt_siz1 {
  float: left;line-height: 44px;color: #B2C3CD;margin-left: 30px;
  width: 115px;font-size: 18px;font-weight: bold;
}
.clt_siz11 {
  width: 530px;height: 532px;border-radius: 2px;background: #FFF;
  margin: 0 auto;margin-top: 105px;font-size: 16px;
}
.clt_siz12 {
  float: left;line-height: 20px;margin-top: 24px;font-size: 20px;
  color: @themeBackGroundColor;margin-left: 30px;font-weight: bold;
}
.clt_siz13 {
  color: #8197A6;font-size: 30px;width: 56px;text-align: center;
  margin-top: 9px;float: right;cursor: pointer;
}
.clt_siz14 {
  margin-top: 40px;overflow: hidden;
}
.clt_siz15 {
  float: left;line-height: 44px;width: 70px;background: @themeBackGroundColor;
  color: #FFF;text-align: center;margin-left: 15px;border-radius: 4px;
  cursor: pointer;
}
.clt_siz16 {
  width: 350px;margin-left: 140px;height: 220px;margin-top: 22px;overflow-y: scroll;
}
.clt_siz17 {
  float: left;padding-left: 10px;padding-right: 10px;height: 32px;line-height: 32px;background: #F2F2F2;
  border-radius: 16px;margin-bottom: 15px;margin-right: 30px;margin-top: 11px;position: relative;
}
.clt_siz17 div {
  width: 20px;height: 20px;position: absolute;right: -10px;top: -10px;background: #FF6159;
  border-radius: 50%;color: #FFF;text-align: center;line-height: 16px;font-size: 22px;
}
.clt_siz18 {
  overflow: hidden;margin-top: 20px;
}
.clt_siz19 {
  float: left;width: 120px;height: 44px;line-height: 42px;border: 1px solid #FF6159;
  color: #FF6159;text-align: center;margin-left: 35px;border-radius: 4px;
}
.clt_siz2 {
  float: right;width: 120px;height: 44px;line-height: 42px;border: 1px solid @themeBackGroundColor;
  background: @themeBackGroundColor;color: #FFF;text-align: center;margin-right: 35px;border-radius: 4px;
  cursor: pointer;
}
.clt_siz21 {
  float: right;width: 120px;height: 44px;line-height: 42px;border: 1px solid @themeBackGroundColor;
  color: @themeBackGroundColor;text-align: center;margin-right: 20px;border-radius: 4px;cursor: pointer;
}
</style>

<template>
  <div class="clt_siz">
    <div class="clt_siz11">
      <div style="height: 44px;">
        <div class="clt_siz12">
          <span v-if="showSizeColor === 'new'">新增规格</span>
          <span v-if="showSizeColor === 'edit'">添加规格值</span>
        </div>
        <div class="clt_siz13" @click="close()">×</div>
      </div>
      <div class="clt_siz14">
        <div class="clt_siz1">规格名称</div>
        <el-input
          placeholder=""
          v-model.trim="name"
          id="name"
          maxlength="5"
          :disabled="showSizeColor === 'edit'"
          style="width: 349px;float: left;"
          @input="name = name.replace(/[\$\'\:\;]/g,'')"
          @focus="selectText('name')"
          clearable>
        </el-input>
      </div>
      <div class="clt_siz14" style="margin-top: 20px;">
        <div class="clt_siz1">规格信息</div>
        <el-input
          placeholder=""
          v-model.trim="info"
          @input="info = info.replace(/[\$\'\:\;]/g,'')"
          maxlength="20"
          id="info"
          style="width: 264px;float: left;"
          @focus="selectText('info')"
          clearable>
        </el-input>
        <div class="clt_siz15" @click="showSizeColor === 'new' ? addNewList() : addEditList()">添加</div>
      </div>
      <div class="clt_siz16">
        <div class="clt_siz17" v-for="(li, index) in list" :key="index" v-show="showSizeColor === 'new' || li.isDel === 0">
          {{li.name}}
          <div v-if="li.isEditSelf === 1" @click="showSizeColor === 'new' ? newDel(index) : editDel(index)">×</div>
        </div>
      </div>
      <div class="clt_siz18">
        <!-- <div class="clt_siz19" v-show="showSizeColor === 'edit'">删除规格</div> -->
        <div class="clt_siz2" @click="showSizeColor === 'new' ? newSave() : editSave()">确定</div>
        <div class="clt_siz21" @click="close()">取消</div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data () {
    return {
      name: '',
      info: '',
      list: []
    };
  },
  created () {
    console.log(this.sizeColorDetail, 33222111);
    var sizeColorDetail = _.cloneDeep(this.sizeColorDetail);
    this.info = '';
    if (this.showSizeColor === 'new') {
      this.name = '';
      this.list = [];
    } else if (this.showSizeColor === 'edit') {
      this.name = sizeColorDetail.name;
      // var arr = [];
      // for (let j in sizeColorDetail.item) {
      //   arr.push({j, ...sizeColorDetail.item[j]});
      // }
      this.list = Object.values(sizeColorDetail.item);
    } else {
      console.log(this.showSizeColor);
    }
  },
  // watch: {
  //   showSizeColor() {
  //     console.log(sizeColorDetail, 221111111);
  //     this.info = '';
  //     if (this.showSizeColor === 'new') {
  //       this.name = '';
  //       this.list = [];
  //     } else if (this.showSizeColor === 'edit') {
  //       this.name = this.sizeColorDetail.name;
  //       this.list = this.sizeColorDetail.item;
  //     } else {
  //       console.log(this.showSizeColor);
  //     }
  //   }
  // },
  methods: {
    ...mapActions([SET_SHOW]),
    getSpecs() {
      this.$store.dispatch('info/getSpec');
    },
    editSave() {
      var sub_data = [{
        name: this.name,
        parentId: this.sizeColorDetail.parentId,
        isShowOnSpec: this.sizeColorDetail.isShowOnSpec,
        isEditSelf: this.sizeColorDetail.isEditSelf,
        isAddSub: this.sizeColorDetail.isAddSub,
        isShowOnProduct: this.sizeColorDetail.isShowOnProduct,
        isLock: this.sizeColorDetail.isLock,
        isDel: this.sizeColorDetail.isDel
      }];
      var that = this;
      console.log(this.list, 555555);
      demo.$http.post(demo.$rest.updateSpecs, sub_data.concat(this.list)).then(res => {
        console.log(res, 309303);
        if (res.data.code === 200) {
          demo.msg('success', '编辑规格保存成功！');
          that.getSpecs();
          that.close();
        } else {
          demo.msg('warning', res.data.msg);
        }
      }).catch((error) => {
        console.log(error, '编辑规格保存error');
      });
    },
    newSave() {
      if (this.name === '') {
        demo.msg('warning', '请输入规格名称！');
        this.focus('name');
        return;
      }
      var mid = {
        name: this.name,
        isShowOnSpec: 1,
        isAddSub: 1,
        isShowOnProduct: 1,
        specsList: this.list
      };
      var sub_data = [];
      sub_data.push(mid);
      console.log(sub_data, 11111);
      demo.$http.post(demo.$rest.saveSpecs, sub_data).then(res => {
        console.log(res, 309303);
        console.log(res.status, 309303);
        if (res.status === 200) {
          demo.msg('success', '新增规格成功！');
          this.getSpecs();
          this.close();
        } else {
          demo.msg('warning', '新增规格失败，请稍后再试！');
        }
        // res.status === 200 ? resolve(res.data.data) : resolve([]);
      }).catch((error) => {
        console.log(error, 555);
      });
    },
    close() {
      this.SET_SHOW({showSizeColor: 'close'});
    },
    addEditList() {
      if (this.info === '') {
        demo.msg('warning', '规格信息不能为空');
        this.focus('info');
        return;
      }
      for (var i = 0; i < this.list.length; i++) {
        if (this.list[i].name === this.info) {
          this.list[i].isDel = 0;
          this.info = '';
          this.focus('info');
          return;
        }
      }
      this.list.push({
        isShowOnSpec: 1,
        isAddSub: 0,
        isShowOnProduct: 1,
        isEditSelf: 1,
        name: this.info,
        parentId: this.sizeColorDetail.id,
        isLock: 0,
        isDel: 0
      });
      this.info = '';
      this.focus('info');
    },
    focus(str) {
      setTimeout(() => {
        document.getElementById(str).focus();
      }, 0);
    },
    addNewList() {
      if (this.info === '') {
        demo.msg('warning', '规格信息不能为空');
        this.focus('info');
        return;
      }
      for (var i = 0; i < this.list.length; i++) {
        if (this.info === this.list[i].name) {
          this.info = '';
          demo.msg('warning', '规格信息已存在，请勿重复添加！');
          this.focus('info');
          return;
        }
      }
      this.list.push({
        isShowOnSpec: 1,
        isAddSub: 0,
        isEditSelf: 1,
        isShowOnProduct: 1,
        name: this.info
      });
      this.info = '';
      this.focus('info');
    },
    addDel(n) {
      this.list.splice(n, 1);
      console.log(this.list, n, 99933300);
      // this.list[n].isLock = 0;
    },
    editDel(n) {
      // console.log(this.list, n, 99933300);
      // this.list.splice(n, 1);
      var delList = [];
      delList.push(this.list[n].id);
      this.searchIfDel(delList, n);
    },
    searchIfDel(delList, n) {
      var data = {
        pset: '',
        isGroup: false,
        type: 0,
        condition: '',
        limit: 1,
        selectDel: false,
        getDel: false,
        offset: 0,
        specs: delList
      };
      console.log(data, 'goodSearch data++');
      var that = this;
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        console.log('json', json)
        if (json.length === 0) {
          that.list[n].isDel = 1;
        } else {
          demo.msg('warning', '存在规格正被商品使用，请删除商品后重新操作。');
        }
      });
    },
    newDel(n) {
      this.list.splice(n, 1);
    }
  },
  computed: mapState({
    sizeColorDetail: state => state.show.sizeColorDetail,
    showSizeColor: state => state.show.showSizeColor
  })
};
</script>
