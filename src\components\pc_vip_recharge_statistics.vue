<template>
  <!-- 会员充值统计页面 -->
  <div class="change_shifts_record_container"  v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left">
        <div class='search_input_box'
        :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng1'"
        >
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder='请输入姓名/手机号/卡号/刷卡'
            :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
            id='search_input_keyword_charge'
            v-model='keyword'
            @input="keyword = $vipNameFormat(keyword)"
          />
          <img
            alt=""
            class='search_input_delete'
            v-show="keyword != ''"
            @click="focusInput('search_input_keyword_charge')"
            src='../image/pc_clear_input.png'
          />
        </div>
        <div
          class="date_picker_container"
          @click="focusDate = true"
          :class="focusDate ? 'isInPutIng' : 'isInPutIng1'"
        >
          <el-date-picker
            v-model="fromDate"
            type="date"
            placeholder="开始日期"
            style="height:44px"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
          <div style="font-size: 16px;color: #567485">至</div>
          <el-date-picker
            v-model="toDate"
            type="date"
            placeholder="结束日期"
            style="height:44px"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
        </div>
        <div class="btn_search" @click="currentPage = 1;httpRequest('查询', true)">查询</div>
      </div>
      <div class="top_right">
        <div class="btn_export_excel" @click="httpRequest('导出表格', true)">导出表格</div>
      </div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        :empty-text="!loading ? '暂无数据' : ' '"
        stripe
        show-summary
        :summary-method="getSummaries"
        ref="table"
      >
        <el-table-column prop="name" label="会员姓名" align="left"></el-table-column>
        <el-table-column label="会员号" align="center">
         <template slot-scope="scope">
            {{scope.row.code ? scope.row.code : '-'}}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
        <el-table-column label="充值金额" align="right">
          <template slot-scope="scope">
            {{scope.row.addMoney ? Number(scope.row.addMoney).toFixed(2) : '-'}}
          </template>
        </el-table-column>
        <el-table-column label="赠送金额" align="right">
          <template slot-scope="scope">
            {{scope.row.giveMoney ? Number(scope.row.giveMoney).toFixed(2) : '0.00'}}
          </template>
        </el-table-column>
        <el-table-column label="消费金额" align="right">
          <template slot-scope="scope">
            {{scope.row.wasteMoney ? Number(scope.row.wasteMoney).toFixed(2) : '-'}}
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
        >
          <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="pageSize"
            :currentPage.sync="currentPage"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      inputing_keyword: false,
      keyword: '',
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      tableHeight: 0,
      tableData: [{}],
      totalData: [],
      pageKey: 0,
      currentPage: 1,
      pageSize: 10,
      total: 0
    };
  },
  created () {
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.tableHeight = screen.availHeight - 228;
    // this.pageSize = Math.floor((this.tableHeight - 50) / 50);
    this.httpRequest('查询');
    this.SET_SHOW({ cardNo: '' });
  },
  watch: {
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      this.keyword = this.cardNo;
      this.SET_SHOW({ cardNo: '' });
    }
  },
  mounted() {
    setTimeout(() => {
      $('#search_input_keyword_charge').focus();
    }, 0);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    /**
     * 点击页码
     */
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage;
      this.httpRequest('查询');
    },
    handleSizeChange() {
      this.httpRequest('查询');
    },
    /**
     * 总计
     */
    getSummaries ({ columns }) {
      let sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = this.totalData.cardname;
          return;
        }
        if (index === 1) {
          sums[index] = '-';
          return;
        }
        if (index === 2) {
          sums[index] = this.totalData.member;
          return;
        }
        if (index === 3) {
          sums[index] = this.totalData.addmoney;
          return;
        }
        if (index === 4) {
          sums[index] = this.totalData.givemoney;
          return;
        }
        if (index === 5) {
          sums[index] = this.totalData.wastemoney;
        }
      });
      return sums;
    },
    httpRequest(str, logFlg) {
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      var url = '';
      if (str === '导出表格') {
        url = this.$rest.pc_searchVipRechargeTotalsStatisticsExport;
        demo.actionLog(logList.clickVipRechargeStatisticsExportExcel);
      } else if (str === '查询') {
        url = this.$rest.pc_searchVipRechargeTotalsStatistics;
      } else {
        console.log(str);
      }
      this.loading = true;
      var sub_data = {
        'sysSid': this.sysSid,
        'fromDate': this.fromDate,
        'toDate': this.toDate,
        'phone': this.phone,
        // 'uid': this.sysUid,
        'systemName': $config.systemName,
        'vipSearch': this.keyword,
        'currentPage': this.currentPage,
        'pageSize': this.pageSize
      };
      var that = this;
      demo.$http.post(url, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          that.parseResult(res, str);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    /**
     * sonar对应
     */
    parseResult (res, str) {
      let that = this;
      if (res.data.code === '0' && str === '查询') {
        if (res.data.data.length === 0) {
          // demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          that.tableData = [];
          that.total = that.tableData.length;
          return;
        }
        that.tableData = res.data.data.rechargeDatalist;
        that.totalData = res.data.data.totalStatistics;
        that.total = res.data.data.total;
      } else if (res.data.code === '0' && str === '导出表格') {
        if (res.data.data.length > 0) {
          var field_mapping = {
            会员姓名: 'name',
            会员号: 'code',
            手机号: 'phone',
            充值金额: 'addMoney',
            赠送金额: 'giveMoney',
            消费金额: 'wasteMoney'
          };
          that.$makeExcel(res.data.data, field_mapping, '会员充值统计' + new Date().format('yyyyMMddhhmmss'));
        } else {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        }
      } else {
        demo.msg('warning', res.data.msg);
      }
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    delayedTime: state => state.show.delayedTime,
    cardNo: (state) => state.show.cardNo
  })
};
</script>

<style lang='less' scoped>
.isInPutIng  {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
/deep/ .el-table th {
  padding: 0px;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;background: #FFF;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  color: @themeFontColor;
  font-size: 16px;
  padding-right: 0;
}

/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-input--suffix .el-input__suffix {
  top: 2.5px;
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: #D5AA76;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  margin-left: 20px;
  display: flex;
  align-items: center;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
.change_shifts_record_container{
  background: #F5F8FB;
  .top{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 10px 20px;
    .top_left{
      display: flex;
      align-items: center;
      .search_input_box{
        input::-webkit-input-placeholder {
        /* WebKit browsers */
        color: @text;
        }
      }
      .btn_search{
        width: 100px;
        height: 44px;
        line-height: 44px;
        color: #FFFFFF;
        font-size: 18px;
        font-weight: 700;
        text-align: center;
        background: @linearBackgroundColor;
        border-radius: 50px;
        margin-left: 20px;
        cursor: pointer;
      }
      .search_div {
        /deep/ .el-input__inner {
          padding-right: 38px;
        }
      }
    }
    .top_right{
      display: flex;
      align-items: center;
      .btn_export_excel{
        width: 130px;
        height: 44px;
        line-height: 44px;
        background: @linearBackgroundColor;
        color: white;
        text-align: center;
        font-size: 18px;
        border-radius: 50px;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .table_bottom{
      height: 54px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 16px;
      padding: 0 30px;
      background: white;
    }
  }
}
</style>
