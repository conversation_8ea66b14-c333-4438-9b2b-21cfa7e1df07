/* 滚动条*/

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #888;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #666;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-size: 14px;
  font-family: DinMedium, SourceHanSansCN, sans-serif !important;
  overflow: hidden;
  touch-action: manipulation;
  -webkit-overflow-scrolling: touch;
}

.large {
  font-size: 36px;
}

.medium {
  font-size: 24px;
}

.small {
  font-size: 18px;
}

.pos-grid {
  display: flex;

  &-cell {
    flex: 1;
  }

  &-cell.u-full {
    flex: 0 0 100%;
  }

  &-cell.u-full2 {
    flex: 0 0 50%;
  }

  &-cell.u-full3 {
    flex: 0 0 33.3333%;
  }

  &-cell.u-full4 {
    flex: 0 0 25%;
  }

  &-cell.u-full5 {
    flex: 0 0 20%;
  }
}

.pos-grid-column {
  display: flex;
  flex-direction: column;
}

.pos-media {
  display: flex;
  align-items: flex-start;

  &-figure {
    margin: 1em;
    position: relative;
  }

  &-body {
    flex: 1;
  }
}

.van-ellipsis {
  font-size: inherit !important;
}

.van-cell {
  font-size: inherit !important;
}

.van-search__content {
  border-radius: 27px !important;
}

.van-tree-select {
  height: 100vh !important;
}

.van-tabbar--fixed {
  border-top: 1px solid transparent;
}

.van-stepper__input {
  width: 45px !important;
}

.dt {
  display: table;
}

.dt-r {
  display: table-column;
}

.dt-r-c {
  display: table-cell;
}

@media screen and (min-width: 320px) and (max-width: 640px) {
  ::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  ::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #888;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #666;
  }
}

/*Thick font*/

.blinker {
  animation-name: blinker;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(1, 0, 0, 1);
  animation-duration: 1s;
  display: inline-block;
  width: 2px;
  height: 20px;
  border-radius: 3px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  border: 0 solid #c90;
  border-right-width: 1px;
}

@-webkit-keyframes blinker {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes blinker {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.stylizedHelper {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  color: #fff;
  cursor: row-resize;
  border: 1px solid #fff;
  z-index: 99999;
}

.pc_popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  color: #567485;
  display: flex;
  align-items: center;
  .pc_popup_lg {
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 840px;
    border-radius: 10px;
    overflow: hidden;
    .pc_popup_title {
      text-align: center;
      line-height: 29px;
      font-size: 20px;
      font-weight: bold;
      color: #b4995a;
      margin-top: 18px;
    }
    .pc_popup_content {
      text-align: center;
      font-size: 16px;
      color: #567485;
      line-height: 24px;
      margin-top: 12px;
      padding: 0 135px;
      min-height: 40px;
    }
  }
}

.cjFlexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}
.cjFlexBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cjFlexRowCenter {
  display: flex;
  align-items: center;
}
.cjFlexWarp {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
