const logList = {
  clickImportProducts: { page: 'goods', action: 'clickImportProducts', description: '点击导入商品' },
  downloadImportProductsTemplate: { pages: 'goods', action: 'downloadImportProductsTemplate', description: '点击下载商品资料导入模板' },
  uploadImportProductsFile: { pages: 'goods', action: 'uploadImportProductsFile', description: '点击上传文件' },
  downloadImportProductsFailedFile: { page: 'goods', action: 'downloadImportProductsFailedFile', description: '点击下载失败文件' },
  clickPrintBarcodeOnProdMng: { page: 'goods', action: 'clickPrintBarcodeOnprodMng', description: '点击打印条码' },
  clickPrintPriceOnProdMng: { page: 'goods', action: 'clickPrintPriceOnProdMng', description: '点击打印标价签' },
  checkedItemHideUnsold: { page: 'goods', action: 'checkedItemHideUnsold', description: '点击隐藏未销售' },
  checkedItemHideZeroStock: { page: 'goods', action: 'checkedItemHideZeroStock', description: '点击隐藏零库存' },
  checkedItemShowUnsold: { page: 'goods', action: 'checkedItemShowUnsold', description: '点击显示未销售' },
  checkedItemShowZeroStock: { page: 'goods', action: 'checkedItemShowZeroStock', description: '点击显示零库存' }
};
// 注册
logList.register = { page: 'register', action: 'register', maxUploadRows: 1, description: '注册({0})' };
logList.scanCode = { page: 'scanCode', action: 'register', maxUploadRows: 1, description: '扫码注册({0})' };

// 登录
logList.login = { page: 'login', action: 'login', description: '账号密码登录' };
logList.offlineLogin = { page: 'login', action: 'offlineLogin', description: '账号密码离线登录' };
logList.login_zgcm = { page: 'login_zgcm', action: 'login', description: '商家助手公众号授权登录' };
logList.base_change_login = { page: 'base_change_shifts', action: 'logout', description: '重新登录' };
logList.change_shifts = { page: 'change_shifts', action: 'logout', description: '重新登录' };
logList.upgradeVersion = { page: 'login', action: 'clickUpgrade', description: '登录页点击左下角升级更高版本' };
logList.upgradeSuccess = { page: 'login', action: 'upgradeSuccess', description: '激活升级成功' };

// 首页
logList.clickCustomerService = { page: 'home', action: 'clickCustomerService', description: '点击首页顶部售后客服' };
logList.clickCloudSync = { page: 'home', action: 'clickCloudSync', description: '点击首页顶部云同步' };

// 设置
logList.clickRenewImmediately = { page: 'setting', action: 'clickRenewImmediately', description: '点击设置-店铺信息-立即续费' };
logList.paySuccess = { page: 'pc_version_compare', action: 'paySuccess', description: '版本支付成功' };
logList.clickClearData = { page: 'setting', action: 'clickClearData', description: '点击设置-清空数据' };
logList.clickSystemSetting = { page: 'setting', action: 'clickSystemSetting', description: '点击设置-系统设置' };
logList.clickPriceSetting = { page: 'setting', action: 'clickPriceSetting', description: '点击设置-价格设置' };
logList.clickAccessorySetting = { page: 'setting', action: 'clickAccessorySetting', description: '点击设置-配件设置' };
logList.clickCloudSyncSetting = { page: 'setting', action: 'clickCloudSyncSetting', description: '点击设置-云同步设置' };
logList.clickWechatAssistant = { page: 'setting', action: 'clickWechatAssistant', description: '点击设置-微信助手' };
logList.clickGoodsPriceDisplay = { page: 'setting', action: 'clickGoodsPriceDisplay', description: '点击设置-配件设置-客显-单品价格显示-{0}' };

// 商品
logList.clickGoodsUnpacking = { page: 'Header', action: 'clickGoodsUnpacking', description: '点击商品-顶部-商品拆包' };
logList.clickStockInventory = { page: 'Header', action: 'clickStockInventory', description: '点击商品-顶部-库存盘点' };
logList.clickScaleManage = { page: 'goods', action: 'clickScaleManage', description: '点击商品-传秤管理' };
logList.clickScaleManageExportExcel = { page: 'goods', action: 'clickScaleManageExportExcel', description: '点击商品-传秤管理-导出表格' };
logList.clickImportGoods = { page: 'goods', action: 'clickImportGoods', description: '点击商品-导入商品' };
logList.clickBatchDelete = { page: 'goods', action: 'clickBatchDelete', description: '点击商品-批量删除-删除已选商品' };
logList.clickBatchDelCategory = { page: 'goods', action: 'clickBatchDelCategory', description: '点击商品-批量删除-按分类删除商品' };
logList.clickBatchChangePrice = { page: 'goods', action: 'clickBatchChangePrice', description: '点击商品-批量改价' };
logList.clickEditCategory = { page: 'goods', action: 'clickEditCategory', description: '点击商品-编辑分类' };
// 报表
logList.clickGoodsSaleStatistics = { page: 'report_forms', action: 'clickGoodsSaleStatistics', description: '点击报表-商品销售统计' };
logList.clickGoodsSaleStatisticsExportExcel = {
  page: 'report_forms',
  action: 'clickGoodsSaleStatisticsExportExcel',
  description: '点击报表-商品销售统计-导出报表'
};

logList.clickVipDealDetail = { page: 'report', action: 'clickVipDealDetail', description: '点击报表-会员报表-会员交易明细' };
logList.clickVipDealDetailExportExcel = {
  page: 'report',
  action: 'clickVipDealDetailExportExcel',
  description: '点击报表-会员报表-会员交易明细-导出Excel'
};
logList.clickVipValueAnalysis = { page: 'pc_vip_value_analysis', action: 'clickVipValueAnalysis', description: '点击报表-会员报表-会员价值分析' };
logList.clickVipValueAnalysisExportExcel = {
  page: 'pc_vip_value_analysis',
  action: 'clickVipValueAnalysisExportExcel',
  description: '点击报表-会员报表-会员价值分析-导出报表'
};
logList.clickVipRechargeStatistics = {
  page: 'pc_vip_recharge_statistics',
  action: 'clickVipRechargeStatistics',
  description: '点击报表-会员报表-会员充值统计'
};
logList.clickVipRechargeStatisticsExportExcel = {
  page: 'pc_vip_recharge_statistics',
  action: 'clickVipRechargeStatisticsExportExcel',
  description: '点击报表-会员报表-会员充值统计-导出报表'
};
logList.clickVipRechargeDetail = { page: 'pc_vip_recharge_detail', action: 'clickVipRechargeDetail', description: '点击报表-会员报表-会员充值明细' };
logList.clickVipRechargeDetailExportExcel = {
  page: 'pc_vip_recharge_detail',
  action: 'clickVipRechargeDetailExportExcel',
  description: '点击报表-会员报表-会员充值明细-导出报表'
};
logList.clickPointChangeDetail = { page: 'pc_point_user_detail', action: 'clickPointChangeDetail', description: '点击报表-会员报表-积分变动明细' };
logList.clickVipRechargeDetailExportExcel = {
  page: 'pc_point_user_detail',
  action: 'clickVipRechargeDetailExportExcel',
  description: '点击报表-会员报表-积分变动明细-导出报表'
};

logList.clickSaleDetail = { page: 'detail', action: 'clickSaleDetail', description: '点击报表-销售明细' };
logList.clickSaleDetailPrint = { page: 'detail', action: 'clickSaleDetailPrint', description: '点击报表-销售明细-打印' };

logList.clickPurchaseDetail = { page: 'detail', action: 'clickPurchaseDetail', description: '点击报表-进货明细' };
logList.clickPurchaseDetailDelete = { page: 'detail', action: 'clickPurchaseDetailDelete', description: '点击报表-进货明细-删除' };
logList.clickPurchaseCombinationPayReturns = { page: 'detail_combination_pay_returns', action: 'clickPurchaseCombinationPayReturns', description: '点击报表_销售明细报表_点击部分退货按钮-组合支付部分退货' };
logList.clickPurchaseVipPartialReturns = { page: 'detail_sales_vip_partial_returns', action: 'clickPurchaseVipPartialReturns', description: '点击报表_销售明细报表_点击部分退货按钮-会员支付部分退货' };

logList.clickTimesCardStatistics = { page: 'pc_once_card', action: 'clickTimesCardStatistics', description: '点击报表-次卡报表-次卡统计' };
logList.clickTimesCardSaleDetail = { page: 'pc_once_card', action: 'clickTimesCardSaleDetail', description: '点击报表-次卡报表-次卡销售明细' };
logList.clickTimesCardSaleDetailExportExcel = {
  page: 'pc_once_card',
  action: 'clickTimesCardSaleDetailExportExcel',
  description: '点击报表-次卡报表-次卡销售明细-导出报表'
};
logList.clickTimesCardUseDetail = { page: 'pc_once_card', action: 'clickTimesCardUseDetail', description: '点击报表-次卡报表-次卡使用明细' };
logList.clickTimesCardUseDetailExportExcel = {
  page: 'pc_once_card',
  action: 'clickTimesCardUseDetailExportExcel',
  description: '点击报表-次卡报表-次卡使用明细-导出报表'
};

logList.clickJiCunStatistics = { page: 'detail', action: 'clickJiCunStatistics', description: '寄存统计' };
logList.clickJiCunDetail = { page: 'detail', action: 'clickJiCunDetail', description: '寄存明细' };
logList.clickJiCunDetailExportExcel = { page: 'detail', action: 'clickJiCunDetailExportExcel', description: '寄存明细-导出报表' };
logList.clickJiCunResidue = { page: 'deposit_residue', action: 'enterDepositResidue', description: '报表_寄存数据_寄存剩余' };
logList.clickJiCunResidueExportExcel = { page: 'deposit_residue_export_excel', action: 'depositResidueExportExcel', description: '报表_寄存数据_寄存剩余_导出表格' };

logList.clickChangeShiftRecord = { page: 'change_shifts_record', action: 'clickChangeShiftRecord', description: '点击报表-交接班记录' };
// logList.clickChangeShiftRecordExportExcel = {page: 'change_shifts_record', action: 'clickChangeShiftRecordExportExcel', description: '点击报表-交接班记录-导出报表'};

logList.clickStockReportDetail = { page: 'pc_stock_report_detail', action: 'clickStockReportDetail', description: '点击报表-库存报表-库存查询' };
logList.clickStockReportDetailExportExcel = {
  page: 'pc_stock_report_detail',
  action: 'clickStockReportDetailExportExcel',
  description: '点击报表-库存报表-库存查询-导出报表'
};

logList.clickStockChangeDetail = { page: 'pc_stock_change_detail', action: 'clickStockChangeDetail', description: '点击报表-库存报表-变动明细' };
logList.clickStockChangeDetailExportExcel = {
  page: 'pc_stock_change_detail',
  action: 'clickStockChangeDetailExportExcel',
  description: '点击报表-库存报表-变动明细-导出报表'
};
logList.clickStockChangeCheckDetail = {
  page: 'pc_stock_change_detail',
  action: 'clickStockChangeCheckDetail',
  description: '点击报表-库存报表-变动明细-查看详情'
};
logList.clickStockInventoryDetail = { page: 'pc_stock_check_detail', action: 'clickStockInventoryDetail', description: '点击报表-库存报表-盘点明细' };
logList.clickStockInventoryDetailExportExcel = {
  page: 'pc_stock_check_detail',
  action: 'clickStockInventoryDetailExportExcel',
  description: '点击报表-库存报表-盘点明细-导出报表'
};

logList.clickHomePurchaseBackGoods = { page: 'stock', action: 'clickHomePurchaseBackGoods', description: '点击首页进货-退货' };
logList.clickHomePurchaseBatchImportGoods = { page: 'stock', action: 'clickHomePurchaseBatchImportGoods', description: '点击首页进货-批量进货' };
logList.clickHomePurchaseAddGoods = { page: 'stock', action: 'clickHomePurchaseAddGoods', description: '点击首页进货-新增商品' };
logList.clickHomePurchaseEditCategory = { page: 'stock', action: 'clickHomePurchaseEditCategory', description: '点击首页进货-编辑分类' };

logList.editRefundMoney = { page: 'detail', action: 'editRefundMoney', description: '部分退货退款-点击修改退货金额' }
// 退出
logList.logout = { page: 'logout', action: 'logout', description: '退出' };
logList.change_shifts_logout = { page: 'change_shifts', action: 'logout', description: '退出' };
logList.base_change_shifts = { page: 'base_change_shifts', action: 'logout', description: '交接班退出' };
// 单号
logList.sales = { page: 'sales', action: 'sales', description: '销售单号：{0}' };
logList.purs = { page: 'purs', action: 'purs', description: '进货单号：{0}' };
logList.inventories = { page: 'inventories', action: 'inventories', description: '盘点单号：{0}' };
logList.sendscaleProduct = { page: 'sendscaleProduct', action: 'sendscaleProduct', description: '传称信息：{0}' };
// 统计/明细
logList.search = { page: 'report', action: 'reportFormLog', description: '商品销售统计' };
logList.detail = { page: 'detail', action: 'reportFormLog', description: '进货明细查询' };
logList.pc_stock_report = { page: 'pc_stock_report_statistics', action: 'mounted', description: '库存统计查询' };
logList.exportExcel = { page: 'pc_stock_report_statistics', action: 'exportExcel', description: '库存统计导出表格' };
logList.change_shifts_record = { page: 'change_shifts_record', action: 'reportFormLog', description: '交接班记录' };
logList.pc_vip_increase = { page: 'pc_vip_increase_statistics', action: 'getEcharts', description: '会员增长统计' };
// 短信服务用户告知书
logList.agreeContract = { page: 'pc_sms_contract', action: 'agree', description: '用户点击同意短信服务用户告知书' };
logList.disagreeContract = { page: 'pc_sms_contract', action: 'agree', description: '用户点击不同意短信服务用户告知书' };
logList.smsClickRecharge = { page: 'Header', action: 'clickRecharge', description: '短信群发-充值' };
logList.clickSendSmsBatch = { page: 'Header', action: 'clickSendSmsBatch', description: '短信群发-群发记录' };
logList.clickSmsSendRecord = { page: 'message_history', action: 'clickSmsSendRecord', description: '短信群发-短信发送记录-导出报表' };
logList.clickSmsBuyRecord = { page: 'message_history', action: 'clickSmsBuyRecord', description: '短信群发-短信购买记录-导出报表' };
logList.clickSmsVipImport = { page: 'message_history', action: 'clickSmsVipImport', description: '短信群发-会员导入' };
logList.clickSmsVipImportImport = { page: 'pc_import_vip', action: 'clickSmsVipImportImport', description: '短信群发-会员导入-导入' };
logList.clickSmsSaveTemplate = { page: 'short_message_mass', action: 'clickSmsSaveTemplate', description: '短信群发-保存模板' };
logList.clickSmsSend = { page: 'short_message_mass', action: 'clickSmsSend', description: '短信群发-发送' };

// 会员设置
logList.clickVipDaySetting = { page: 'member_setting', action: 'clickVipDaySetting', description: '会员设置-会员日设置' };
logList.clickAddTimesCard = { page: 'member_setting', action: 'clickAddTimesCard', description: '会员设置-设置次卡-新增次卡' };
logList.clickSmsMessageSetting = { page: 'member_setting', action: 'clickSmsMessageSetting', description: '会员设置-短信消息设置' };
logList.clickWechatMessageSetting = { page: 'member_setting', action: 'clickWechatMessageSetting', description: '会员设置-微信消息设置' };
logList.clickChangeDiscountBatch = { page: 'member', action: 'clickChangeDiscountBatch', description: '会员-批量修改折扣' };
logList.clickVipImportBatch = { page: 'member', action: 'clickVipImportBatch', description: '会员-批量导入' };
logList.clickAddVipSaveAndRecharge = { page: 'pc_add_member', action: 'clickAddVipSaveAndRecharge', description: '会员-新增会员-保存并充值' };
logList.clickVipDetailExchange = { page: 'pc_add_member', action: 'clickVipDetailExchange', description: '会员-会员详情-点击兑换' };
logList.clickVipDetailChangePointDialogShow = {
  page: 'pc_add_member',
  action: 'clickVipDetailChangePointDialogShow',
  description: '会员-会员详情-修改积分弹窗出现'
};
logList.clickVipDetailSending = { page: 'pc_add_member', action: 'clickVipDetailSending', description: '会员-会员详情-寄件' };
logList.clickVipDetailPickUp = { page: 'pc_add_member', action: 'clickVipDetailPickUp', description: '会员-会员详情-取件' };

// 交接班
logList.changeShiftsView = { page: 'change_shifts', action: 'changeShiftsView', description: '交接班-页面展示' };
logList.changeShiftsDetailPrint = { page: 'change_shifts', action: 'changeShiftsDetailPrint', description: '交接班-查看详情-打印' };
logList.changeShiftsReportPrint = { page: 'change_shifts', action: 'changeShiftsReportPrint', description: '交接班-商品销售报表-打印' };
logList.changeShiftsLogout = { page: 'change_shifts', action: 'changeShiftsLogout', description: '交接班-交接班并登出' };

// 收银台
logList.clearGoods = { page: 'pay', action: 'clearGoods', description: '收银台-清空列表' };
logList.cashPay = { page: 'pc_final_pay', action: 'cashPay', description: '收银台-结算' };
logList.posPay = { page: 'pc_final_pay', action: 'posPay', description: '收银台-结算' };
logList.scanPay = { page: 'pc_final_pay', action: 'scanPay', description: '收银台-结算' };
logList.vipPay = { page: 'pc_final_pay', action: 'vipPay', description: '收银台-结算' };
logList.receivableChange = { page: 'pay', action: 'receivableChange', description: '收银台-结算-修改应收' };
logList.combinationPay = { page: 'pay', action: 'combinationPay', description: '收银台-结算-组合支付' };
logList.backGoods = { page: 'pay', action: 'backGoods', description: '收银台-退货' };
logList.autoCash = { page: 'pay', action: 'autoCash', description: '收银台-自助收银' };
logList.openMoneybox = { page: 'pay', action: 'openMoneybox', description: '收银台-开钱箱' };
logList.losckScreen = { page: 'pay', action: 'losckScreen', description: '收银台-锁屏' };
logList.accessoriesSetting = { page: 'pay', action: 'accessoriesSetting', description: '收银台-配件设置' };
logList.exchangePoints = { page: 'pay', action: 'exchangePoints', description: '收银台-选择会员-兑换积分' };
logList.deductionPoints = { page: 'pay', action: 'deductionPoints', description: '收银台-选择会员-积分抵扣' };
logList.vipChangeDisc = { page: 'pay', action: 'vipChangeDisc', description: '收银台-选择会员-修改折扣' };
logList.vipRecharge = { page: 'pay', action: 'vipRecharge', description: '收银台-选择会员-充值' };
logList.vipAdd = { page: 'pay', action: 'vipAdd', description: '收银台-选择会员-新增会员' };
logList.showLastOrder = { page: 'pay', action: 'showLastOrder', description: '收银台-结算-查看上一单' };
logList.settlement = { page: 'pay', action: 'settlement', description: '收银台-结算' };
// 首页
logList.homeReportChange = { page: 'home', action: 'homeReportChange', description: '首页-预估利润' };
logList.renewalSelf = { page: 'home', action: 'renewalSelf', description: '收银台-结算-自助续费' };

// 掌柜参谋相关日志
logList.closeBindAccountNotice = { page: 'home', action: 'close', description: '关闭首页绑定店铺营业营业微信通知' };
// 退货退款
logList.refund = { page: 'detail', action: 'refund', description: '退货退款' }
// 扫码付开通引导
logList.scanPaySelfPlay = { page: 'scanPayIntro', action: 'play', description: '扫码付自主开通教程播放' };
logList.scanPayServicePlay = { page: 'scanPayIntro', action: 'play', description: '扫码付案例分享播放' };
export default logList;
