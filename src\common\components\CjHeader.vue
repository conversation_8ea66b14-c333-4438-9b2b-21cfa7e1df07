<template>
  <div class="headerWrap" :style="{backgroundColor: backgroundColor}">
    <div class="leftWrap">
      <slot v-if="$slots.navLeft" name="navLeft"></slot>
      <div v-else class="backMenu" @click.stop="leftClick">
        <img class="backIcon" src="../../image/pc_header_back.png" />
        <div>{{ backText }}</div>
      </div>
    </div>
    <div class="centerWrap">
      <slot v-if="$slots.navTitle" name="navTitle"></slot>
      <div v-else class="title">{{ title }}</div>
    </div>
    <div class="rightWrap">
      <slot v-if="$slots.navRight"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CjHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    backText: {
      type: String,
      default: '返回 (ESC)'
    },
    enableEsc: {
      type: Boolean,
      default: false
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    }
  },
  data() {
    return {
      timer: null
    };
  },
  methods: {
    // 左侧点击
    leftClick() {
      this.$emit('leftClick');
    },
    handleKeydown(event) {
      if (event.key === 'Escape' || event.keyCode === 27) {
        this.handleEscPress();
      }
    },
    handleEscPress() {
      this.$emit('leftClick');
    }
  },
  mounted() {
    if (this.enableEsc) {
      window.addEventListener('keydown', this.handleKeydown);
    }
  },
  beforeDestroy() {
    if (this.enableEsc) {
      window.removeEventListener('keydown', this.handleKeydown);
    }
  }
};
</script>
<style lang="less" scoped>
.headerWrap {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  .leftWrap,
  .rightWrap {
    position: absolute;
    top: 0;
    height: 50px;
    .backMenu {
      height: 100%;
      font-size: 16px;
      cursor: pointer;
      color: @themeFontColor;
      display: flex;
      align-items: center;
      padding-left: 16px;
      .backIcon {
        width: 30px;
        height: 30px;
        margin-right: 2px;
      }
    }
  }
  .centerWrap {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 1px;
    color: @themeFontColor;
    height: 50px;
    line-height: 50px;
    margin: 0 auto;
  }
  .leftWrap {
    left: 0;
  }
  .rightWrap {
    right: 0;
  }
}
</style>
