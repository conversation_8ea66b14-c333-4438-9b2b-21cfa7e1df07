<style lang="less">
.pc_tag {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  color: @themeFontColor;
  display: flex;
  align-items: center;
  .el-input-number {
    line-height: 42px;
  }
  .pc_tag1 {
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 730px;
    border-radius: 10px;
    overflow: hidden;
    .pc_tag2 {
      padding: 30px;
      .pc_tag3 {
        color: @themeFontColor;
        font-size: 16px;
        .el-input {
          font-size: 18px;
        }
        .pc_tag4 {
          font-weight: 500;
          line-height: 22px;
          display: inline-block;
          margin-right: 64px;
        }
        .pc_tag5 {
          border: 1px solid #CFA26B;
          font-size: 18px;
          background: #CFA26B;
          border-radius: 4px;
          text-align: center;
          line-height: 40px;
          font-weight: 500;
          color: #FFFFFF;
          width: 119px;
          height: 44px;
          display: inline-block;
          margin-left: 18px;
          cursor: pointer;
        }
      }
    }
    .dialog_header{
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #E3E6EB;
      margin: 0 30px;
      .header_title{
        color: @themeFontColor;
        font-size: 18px;
        font-weight: bold;
        line-height: 60px;
      }
      .icon_close{
        font-size: 30px;
        color: #8298A6;
        cursor: pointer;
      }
    }
  }
}
</style>
<template>
  <div v-show="isTag">
    <div class="pc_tag">
      <div class="pc_tag1">
        <div class="dialog_header">
          <div class="header_title">打印吊牌</div>
          <div
            class="icon_close"
            @click="closeTag"
          >×</div>
        </div>
        <div class="pc_tag2" style="padding-top: 12px;">
          <div class="pc_tag3" style="margin-bottom: 20px;">
            <div class="pc_tag4" style="margin-right: 16px;">打印份数</div>
            <el-input-number
              v-model="tagPrinterCopies"
              @blur="setNum"
              placeholder="请输入打印份数"
              :min="1"
              :max="999"
              v-input-int-max-min="{max: 999,min: 0}"
              style="width: 176px;font-size: 18px;"
            ></el-input-number>
          </div>
          <div style="min-width: 640px;border-radius: 4px;border: 1px solid #E6E5EB;padding: 0 10px;color: #567485;font-weight: 500;">
            <div style="width: 318px;border-right: 1px solid #E6E5EB;display: inline-block;padding-bottom: 10px;height: 395px;">
              <div style="font-size: 22px;line-height: 44px;padding-top: 10px;padding-left: 8px;">吊牌模板
                <div style="display: inline-block;cursor: pointer;font-size: 16px;color: #CFA26B;float: right;margin-right: 10px;" @click="gotoSetting">
                  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                    <path d="M24.5 0H0.5V24H24.5V0Z" fill="white" fill-opacity="0.01"/>
                    <path d="M17.5002 20.5L22.5 12L17.5002 3.5H7.5001L2.5 12L7.5001 20.5H17.5002Z" stroke="#CFA26B" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M12.5 14.5C13.8807 14.5 15 13.3807 15 12C15 10.6193 13.8807 9.5 12.5
                      9.5C11.1193 9.5 10 10.6193 10 12C10 13.3807 11.1193 14.5 12.5 14.5Z" stroke="#CFA26B" stroke-width="2" stroke-linejoin="round"/>
                    </g>
                    <defs>
                    <clipPath id="clip0">
                    <rect width="24" height="24" fill="white" transform="translate(0.5)"/>
                    </clipPath>
                    </defs>
                  </svg>
                  <span style="position: relative; top: 2px;font-weight: bold;">设置</span>
                </div>
              </div>
              <div :key="item.key + index" v-for="(item, index) in tagModels">
                <div style="font-size: 16px;line-height: 50px;cursor: pointer;border-radius: 8px;padding-left: 8px;margin-right: 10px;"
                  :style="nowIndex === index ? 'background: #F5F7FA' : ''" @click="choseModel(index)">{{item.name}}</div>
              </div>
            </div>
            <div style="width: 318px;display: inline-block;position: absolute;">
              <div style="font-size: 22px;line-height: 44px;padding-top: 10px;padding-left: 12px;">实时预览
                <span v-if="tagIsTransverse && this.tagPrintCols === '60'" style="font-size: 16px;">（竖向40*60mm）</span>
                <span v-if="tagIsTransverse && this.tagPrintCols !== '60'" style="font-size: 16px;">（竖向40*30mm）</span>
                <span v-if="!tagIsTransverse && this.tagPrintCols === '60'" style="font-size: 16px;">（横向40*60mm）</span>
                <span v-if="!tagIsTransverse && this.tagPrintCols !== '60'" style="font-size: 16px;">（横向40*30mm）</span>
                </div>
              <div v-if="tagIsTransverse && this.tagPrintCols === '60'" style="width: 210px; height: 328px;border: 1px solid #929292;
                border-radius: 4px;margin: 0 auto;padding: 10px;font-weight: bold;">
                <div :key="item.key + index" v-for="(item, index) in tagModels[nowIndex].subModel">
                  <div v-if="item.key === 'custom'">
                    {{item.label + '：' + item.value}}
                  </div>
                  <div v-if="item.key === 'majorCode'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].majorCode : '' }}
                  </div>
                  <div v-if="item.key === 'price'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].salePrice.toString().indexOf('.') !== -1 ? chooseList[0].salePrice : chooseList[0].salePrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'initPrice'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].initPrice.toString().indexOf('.') !== -1 ? chooseList[0].initPrice : chooseList[0].initPrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="barcode" alt=""/>
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].code : ''}}
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="majorCode" alt=""/>
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].majorCode : ''}}
                  </div>
                  <div v-if="item.key === 'name'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].name : '' }}
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.lock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.lock">
                      <div v-if="it.parentName === item.titleKey">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.unlock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.unlock">
                      <div v-if="it.parentName === item.titleKey">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="tagIsTransverse && this.tagPrintCols !== '60'" style="width: 210px; height: 158px;border: 1px solid #929292;
                border-radius: 4px;margin: 0 auto;padding: 10px;font-weight: bold;">
                <div :key="item.key + index" v-for="(item, index) in tagModels[nowIndex].subModel">
                  <div v-if="item.key === 'custom'">
                    {{item.label + '：' + item.value}}
                  </div>
                  <div v-if="item.key === 'majorCode'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].majorCode : '' }}
                  </div>
                  <div v-if="item.key === 'price'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].salePrice.toString().indexOf('.') !== -1 ? chooseList[0].salePrice : chooseList[0].salePrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'initPrice'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].initPrice.toString().indexOf('.') !== -1 ? chooseList[0].initPrice : chooseList[0].initPrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="barcode" alt=""/>
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].code : ''}}
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="majorCode" alt=""/>
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].majorCode : ''}}
                  </div>
                  <div v-if="item.key === 'name'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].name : '' }}
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.lock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.lock">
                      <div v-if="it.parentName === item.title ">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.unlock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.unlock">
                      <div v-if="it.parentName === item.title">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="!tagIsTransverse && this.tagPrintCols === '60'" style="width: 328px; height: 210px;border: 1px solid #929292;
                border-radius: 4px;margin: 0 auto;padding: 10px;font-weight: bold;margin-left: 5px;">
                <div :key="item.key + index" v-for="(item, index) in tagModels[nowIndex].subModel">
                  <div v-if="item.key === 'custom'">
                    {{item.label + '：' + item.value}}
                  </div>
                  <div v-if="item.key === 'majorCode'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].majorCode : '' }}
                  </div>
                  <div v-if="item.key === 'price'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].salePrice.toString().indexOf('.') !== -1 ? chooseList[0].salePrice : chooseList[0].salePrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'initPrice'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].initPrice.toString().indexOf('.') !== -1 ? chooseList[0].initPrice : chooseList[0].initPrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="barcode" alt=""/>
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].code : ''}}
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="majorCode" alt=""/>
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].majorCode : ''}}
                  </div>
                  <div v-if="item.key === 'name'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].name : '' }}
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.lock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.lock">
                      <div v-if="it.parentName === item.title ">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.unlock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.unlock">
                      <div v-if="it.parentName === item.title">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="!tagIsTransverse && this.tagPrintCols !== '60'" style="width: 158px; height: 210px;border: 1px solid #929292;
                border-radius: 4px;margin: 0 auto;padding: 10px;font-weight: bold;">
                <div :key="item.key + index" v-for="(item, index) in tagModels[nowIndex].subModel">
                  <div v-if="item.key === 'custom'">
                    {{item.label + '：' + item.value}}
                  </div>
                  <div v-if="item.key === 'majorCode'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].majorCode : '' }}
                  </div>
                  <div v-if="item.key === 'price'">
                    {{chooseList.length > 0 ? item.title + '：' + ((chooseList[0].salePrice.toString().indexOf('.') !== -1 ? chooseList[0].salePrice : chooseList[0].salePrice.toFixed(2))) : '' }}
                  </div>
                  <div v-if="item.key === 'initPrice'">
                    {{chooseList.length > 0 ? item.title + '：' + (chooseList[0].initPrice.toString().indexOf('.') !== -1 ? chooseList[0].initPrice : chooseList[0].initPrice.toFixed(2)) : '' }}
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="barcode" alt=""/>
                  </div>
                  <div v-if="item.key === 'code'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].code : ''}}
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;margin-top: 5px;height: 48px;">
                    <img style="width: 146px;height: 52px;" id="majorCode" alt=""/>
                  </div>
                  <div v-if="item.key === 'printMajorCode'" style="text-align: center;">
                    {{chooseList.length > 0 ? chooseList[0].majorCode : ''}}
                  </div>
                  <div v-if="item.key === 'name'">
                    {{chooseList.length > 0 ? item.title + '：' + chooseList[0].name : '' }}
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.lock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.lock">
                      <div v-if="it.parentName === item.title ">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                  <div v-if="chooseList.length > 0 && chooseList[0].specs.unlock.length > 0">
                    <div :key="it.id" v-for="it in chooseList[0].specs.unlock">
                      <div v-if="it.parentName === item.title">{{it.parentName + '：' + it.name}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="pc_tag3" style="margin-top: 16px;">
            <div class="pc_tag5" style="width: 402px;margin-left: 113px;" @click="printTag">打印</div>
          </div>
      </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import draggable from 'vuedraggable';
export default {
  components: {
    draggable
  },
  data() {
    return {
      tagPrinterCopies: 1,
      nowIndex: 0,
      printtag_loading: false
    };
  },
  mounted() {
    this.setCodeImg();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    setCodeImg() {
      if (this.chooseList.length > 0) {
        setTimeout(() => {
          JsBarcode('#barcode', this.chooseList[0].code, {
            format: 'CODE128',
            lineColor: '#000',
            displayValue: false
          });
        }, 0);
        setTimeout(() => {
          JsBarcode('#majorCode', this.chooseList[0].majorCode, {
            format: 'CODE128',
            lineColor: '#000',
            displayValue: false
          });
        }, 0);
      }
    },
    closeTag() {
      this.SET_SHOW({ isTag: false });
    },
    setNum() {
      if (this.tagPrinterCopies === '' || Number(this.tagPrinterCopies) === 0 || this.tagPrinterCopies === undefined) {
        this.tagPrinterCopies = 1;
      }
    },
    gotoSetting() {
      this.SET_SHOW({ settingIndex: 'tab11' });
      this.SET_SHOW({ selectRow: 3 });
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isTag: false });
      this.SET_SHOW({ isSetting: true });
      this.SET_SHOW({ isStock: false });
    },
    choseModel(index) {
      this.nowIndex = index;
      this.setCodeImg();
    },
    printTag() {
      this.printtag_loading = true;
      setTimeout(() => {
        this.printtag_loading = false;
      }, 2000);
      let deepPrint = _.cloneDeep(this.chooseList);
      console.log(this.chooseList, '选中数据');
      let printLists = _.flatMap(deepPrint, this.duplicateTag);
      let arr = [];
      for (let i = 0; i < printLists.length; i++) {
        let data = [];
        this.tagModels[this.nowIndex].subModel.forEach(item => {
          if (item.flag) {
            this.sonarInitPrice(item, data, printLists, i);
            if (printLists[i].specs.lock.length > 0) {
              printLists[i].specs.lock.forEach(it => {
                this.setIt(it, item, data);
              });
            }
            if (printLists[i].specs.unlock.length > 0) {
              printLists[i].specs.unlock.forEach(it => {
                this.setIt(it, item, data);
              });
            }
            this.sonarPrint(item, printLists[i], data);
          }
        });
        arr.push(data);
      }
      this.SET_SHOW({ isTag: false });
      arr.forEach(item => {
        let printData = {
          printname: this.setting_tag_printer,
          Width: this.getTagPrintWidth(),
          Height: this.getTagPrintHeight(),
          Landscape: !this.tagIsTransverse,
          Offset: 0.02,
          DefaultSize: 10,
          others: item
        };
        console.log(printData, 'printData');
        external.printLabel_new(printData);
      });
    },
    getTagPrintWidth() {
      return this.tagPrintCols === '60' ? (this.tagIsTransverse ? 40 : 60) : (this.tagIsTransverse ? 40 : 30);
    },
    getTagPrintHeight() {
      return this.tagPrintCols === '60' ? (this.tagIsTransverse ? 60 : 40) : (this.tagIsTransverse ? 30 : 40);
    },
    sonarInitPrice(item, data, printLists, i) {
      if (item.key === 'initPrice') {
        data.push({Title: item.title + '：', Text: printLists[i].initPrice.toString().indexOf('.') !== -1 ? printLists[i].initPrice : printLists[i].initPrice.toFixed(2), Size: item.size});
      }
    },
    sonarPrint(item, printData, data) {
      if (item.key === 'custom') {
        data.push({Title: item.label + '：', Text: item.value, Size: item.size});
      }
      if (item.key === 'majorCode') {
        data.push({Title: item.title + '：', Text: printData.majorCode, Size: item.size});
      }
      if (item.key === 'price') {
        data.push({Title: item.title + '：', Text: printData.salePrice.toString().indexOf('.') !== -1 ? printData.salePrice : printData.salePrice.toFixed(2), Size: item.size});
      }
      if (item.key === 'code') {
        data.push({barcode: printData.code, barcode_Width: 170, barcode_Height: 40});
      }
      if (item.key === 'printMajorCode') {
        data.push({barcode: printData.majorCode, barcode_Width: 170, barcode_Height: 40});
      }
      if (item.key === 'name') {
        data.push({Title: item.title + '：', Text: printData.name, Size: item.size});
      }
    },
    duplicateTag(n) {
      let arr = [];
      for (let i = 0; i < this.tagPrinterCopies; i++) {
        arr.push(n);
      }
      return arr;
    },
    setIt(it, item, data) {
      if (it.parentName === item.titleKey) {
        data.push({Title: item.title + '：', Text: it.name, Size: item.size});
      }
    }
  },
  // 从vuex中获取变量
  computed: mapState({
    isTransverse: state => state.show.isTransverse,
    codePrintValue: state => state.show.codePrintValue,
    tagModels: state => state.show.tagModels,
    tagPrintCols: state => state.show.tagPrintCols,
    tagIsTransverse: state => state.show.tagIsTransverse,
    isTag: state => state.show.isTag,
    setting_tag_printer: state => state.show.setting_tag_printer,
    chooseList: state => state.show.chooseList
  })
};
</script>
