export function compressImage(file, quality = 0.8) {
  // 返回一个Promise对象
  return new Promise((resolve, reject) => {
    // 创建FileReader对象
    const reader = new FileReader();
    // 读取文件并转换为DataURL
    reader.readAsDataURL(file);

    reader.onload = (event) => {
      // 创建Image对象
      const img = new Image();
      // 将DataURL赋值给图片的src
      img.src = event.target.result;

      img.onload = () => {
        // 创建canvas元素
        const canvas = document.createElement('canvas');
        // 获取2D绘图上下文
        const ctx = canvas.getContext('2d');
        // 设置图片的最大宽度和最大高度
        const maxWidth = 1024;
        const maxHeight = 1024;
        // 获取图片的原始宽度和高度
        let width = img.width;
        let height = img.height;
        // 根据最大宽高调整图片尺寸
        if (width > height) {
          if (width > maxWidth) {
            // 等比缩放高度并设置最大宽度
            height = Math.round((height *= maxWidth / width));
            width = maxWidth;
          }
        } else {
          // 等比例缩放宽度并设置高度为最大高度
          if (height > maxHeight) {
            width = Math.round((width *= maxHeight / height));
            height = maxHeight;
          }
        }
        // 设置canvas宽高
        canvas.width = width;
        canvas.height = height;
        // 在canvas上绘制图片
        ctx.drawImage(img, 0, 0, width, height);
        // 将PNG无损压缩格式图片的输出格式设置为JPEG来保证图片质量参数生效
        const outputType = file.type === 'image/png' ? 'image/jpeg' : file.type;
        // 将canvas转换成blob对象输出
        canvas.toBlob(
          (blob) => {
            resolve(blob);
          },
          outputType,
          quality
        );
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
}
