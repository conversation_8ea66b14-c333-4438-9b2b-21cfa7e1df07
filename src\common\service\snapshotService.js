import dao from '../dao/dao';

const snapshotService = {
  /**
   * 查询暂存数据
   * snapshotService.searchSnapshot({pageName:'test'}, res => {console.log(res)}, err => {console.error(err)});
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  searchSnapshot: function(data, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(data) || demo.isNullOrTrimEmpty(data.pageName)) {
      onFail('页面属性不能为空');
      return;
    }
    dao.exec(snapshotApi.searchSnapshot.format(data), onSuccess, onFail);
  },

  /**
   * 保存暂存数据
   * snapshotService.saveSnapshot({pageName:'test',info:'123'}, res => {console.log(res)}, err => {console.error(err)});
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  saveSnapshot: function(data, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(data) || demo.isNullOrTrimEmpty(data.pageName) || demo.isNullOrTrimEmpty(data.info)) {
      onFail('保存数据不能为空');
      return;
    }
    dao.exec(snapshotApi.saveSnapshot.format(data), onSuccess, onFail);
  }
};

window.snapshotService = snapshotService;
export default snapshotService;
