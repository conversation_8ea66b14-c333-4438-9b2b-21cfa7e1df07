<style lang='less' scoped>
.re_deposit_record_container {
  background: #F5F8FB;
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table td, .el-table th {
    padding: 10px 0;
  }
  /deep/ .el-range-editor.el-input__inner {
    border-radius: 50px;
    height: 44px;
  }
  /deep/ .el-date-editor .el-range__icon {
    display: none;
    height: 44px;
  }
  /deep/ .el-range-editor .el-range-input {
    margin-left: 12px;
    color: @themeFontColor;
  }
  /deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-date-editor .el-range-separator {
    color: @themeFontColor;
    height: 85%;
  }
  /deep/ .el-date-table td.today span {
    color: @themeBackGroundColor !important;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table__header-wrapper {
    height: 50px;
    margin-top: -5px;
    > .el-table__header > .has-gutter > tr > th {
      height: 50px;
      > div {
        height: 100%;
      }
    }
  }
  /deep/ .tab1 .el-table__header-wrapper > .el-table__header > .has-gutter > tr > th {
    padding: 10px 0px 1px 0px;
    > div > .caret-wrapper{
      margin-top: -6px;
      > .sort-caret .ascending {
        top: 7px;
      }
      > .sort-caret .descending {
        bottom: 5px;
      }
    }
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-input__inner {
    border-radius: 22px;
    font-size: 16px;
    height: 44px;
    padding-left: 20px;;
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  .re_top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
        height: 44px;
        .el-input__inner {
          color: @themeFontColor;
        }
      }
    }
    .re_top_right{
      display: flex;
      align-items: center;
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding: 0 10px;
      color: @themeFontColor;
      background: white;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.supplier-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 24px !important;
}
.supplier-search {
  float: left;
  margin-left: 10px;
}
.pc_report {
  float: left;
  margin-left: 14px;
  margin-top: 5px;
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 22px;
  background: #FFFFFF;
}
.pc_report2 > input {
  width: 235px;
  height: 18px;
  line-height: 18px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 12px;
  border: none;
  color: #B1C3CD;
  background: #FFFFFF;
  float: left;
}

.pc_rep14 {
  cursor: pointer;
  width: 100px;
  height: 44px;
  line-height: 44px;
  border-radius: 22px;
  text-align: center;
  font-weight: 700;
  color: #FFFFFF;
  font-size: 18px;
  background: @themeBackGroundColor;
  float: left;
  margin-left: 10px;
}
.pc_rep15 {
  float: right;margin-right: 10px;
  width: 110px;
  height: 44px;
  background: @themeBackGroundColor !important;
  border-radius: 22px;
  font-weight: 700;
  font-size: 18px;
  color: #FFF;line-height: 44px;
  text-align: center;
}

.el-table .ascending .sort-caret.ascending{
  border-bottom-color: @themeBackGroundColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color: @themeBackGroundColor;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}

.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}

.el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.end-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}

.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}
/deep/ .el-input__icon.el-icon-date {
  display: none;
}
/deep/ .date_picker_container{
  width: 400px;
  height: 46px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 22px;
  margin-left: 10px;
  display: flex;
  align-items: center;
  float: left;
  > .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date .el-input__inner {
    border: none;
    background: #FFFFFF;
    color: @themeFontColor;
    font-size: 16px;
    padding-left: 0px;
    padding-right: 0px;
    text-align: center;
  }
  .el-input__suffix {
    top: 2px;
  }
  /deep/ .el-input__prefix {
    display: none;
  }
  /deep/.el-input__inner {
    border: none;
    padding: 12px;
  }

}
.tab1 {
  /deep/.el-table th>.cell.highlight {
    color: @themeBackGroundColor;
  }
  /deep/.el-table__header-wrapper > .el-table__header > .has-gutter > tr > th > div {
    line-height: 33px;
  }
  /deep/.el-table th .cell {
    padding-left: 10px;
  }
  /deep/.totalStockPriceCell {
    font-size: 13px;
    padding-right: 0px !important;
  }
  /deep/.totalSalesPriceCell {
    padding-right: 0px !important;
    text-align: center;
  }
}
.filter_dropdown{
  max-height:250px;
  overflow: auto;
}
.filter_dropdown::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #F5F5F5;
}
.filter_dropdown::-webkit-scrollbar-track {
  //-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
  border-radius: 10px;
  background-color: #F5F5F5;
}
.el-dropdown-menu__item:focus{
  color: #606266;
  background-color: #fff;
}
.el-dropdown-menu__item:hover{
  color: @themeBackGroundColor;
  background-color: #fff;
}
.el-dropdown-menu__item:not(.is-disabled):hover {
  color: #606266;
  background-color: #fff;
}
.focusDate {
  border-color: @themeBackGroundColor;
}
.focusDate1 {
  border-color: #E3E6EB;
}
</style>
<style lang="less" rel="stylesheet/less">
.el-cascader-panel .el-radio{
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}
.el-cascader-panel .el-radio__input{
  visibility: hidden;
}
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
</style>
<template>
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <!--商品销售统计-->
    <div class="re_deposit_record_container">
      <div v-show="goods_member == 'goods'">
        <div class="re_top">
          <div class="re_top_left_container">
            <div style='float: left;'>
              <div
                class='pc_report2'
                :class="inputing_keyword ? 'focusDate' : 'focusDate1'"
              >
                <input
                  @focus='inputing_keyword = true;selectText("goods_keyword_report")'
                  @blur='inputing_keyword = false'
                  :style="keyword_goods ? 'color: #567485;' : 'color: #B1C3CD;'"
                  type='text'
                  placeholder='商品名称/条码/首字母/扫码'
                  v-model='keyword_goods'
                  id='goods_keyword_report'
                  @compositionstart='pinyin = true'
                  @compositionend='pinyin = false'
                  @input="keyword_goods = $goodsNameFormat(keyword_goods)"
                  @keydown.enter="inputSelectHandler('goods_keyword_report')"
                />
                <img
                  alt=""
                  class='pc_report1'
                  v-show="keyword_goods != ''"
                  @click="focusInput('goods_keyword_report')"
                  src='../../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div class="supplier-search">
              <vCjSelect @searchChange="searchChange"></vCjSelect>
            </div>
            <div @click="focusDate = true" class="date_picker_container"
              :class="focusDate ? 'focusDate' : 'focusDate1'"
            >
              <el-date-picker
                v-model="date_goods[0]"
                type="datetime"
                placeholder="开始日期"
                value-format='yyyy-MM-dd HH:mm:ss'
                @blur="focusDate = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485">至</div>
              <el-date-picker
                v-model="date_goods[1]"
                type="datetime"
                placeholder="结束日期"
                value-format='yyyy-MM-dd HH:mm:ss'
                :default-time="'23:59:59'"
                @blur="focusDate = false"
              >
              </el-date-picker>
            </div>
            <div class="re_top_left" style="float: left;">
              <el-cascader
                v-model="detail_value"
                :options="category_list"
                :props="{ checkStrictly: true }"
                placeholder="分类选择">
              </el-cascader>
            </div>
          </div>
          <div class="pc_rep15" @click="getGoodsExcel()" style="margin-right: -10px;cursor: pointer;">导出表格</div>
        </div>
        <div class="re_table_container tab1">
          <el-table
            ref="multipleTable"
            :height="goods_height"
            stripe
            :data="goods_data"
            style="font-size: 16px;color: #567485;width: 100%;"
            :cell-style="priceStockStyle"
            :header-cell-style="headerStyle"
            :empty-text="!loading ? '暂无数据' : ' '"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            @sort-change="sort_change">
            <el-table-column
              min-width="19%"
              label="商品名称">
              <template slot-scope="scope">
                <cj-copy :name="scope.row.name"></cj-copy>
              </template>
            </el-table-column>
            <el-table-column
              min-width="10%"
              align="center"
              label="条码">
                <template slot-scope="scope">
                  <cj-copy :name="scope.row.code" :center="'center'"></cj-copy>
                </template>
            </el-table-column>
            <el-table-column
              prop="category"
              min-width="17%"
              align="center"
              label="商品分类">
            </el-table-column>
            <el-table-column
              min-width="13%"
              show-overflow-tooltip
              align="center"
              label="供应商">
              <template slot-scope="scope">
                {{ scope.row.supplierName ? scope.row.supplierName : '-'}}
              </template>
            </el-table-column>
            <el-table-column
              min-width="10%"
              prop="stock"
              sortable="custom"
              align="right"
              label="现有库存">
              <template slot-scope="scope">
                <div style="padding-right: 24px;">{{scope.row.name === '直接收款' ? '-' : $toDecimalFormat(scope.row.stock, 3)}}</div>
              </template>
            </el-table-column>
            <el-table-column
              min-width="11%"
              prop="salesnumber"
              sortable="custom"
              label="销售数量">
              <template slot-scope="scope">
                <div style="padding-right: 24px;">{{$toDecimalFormat(scope.row.salesnumber, 3)}}</div>
              </template>
            </el-table-column>
            <el-table-column
              prop="totalsalesprice"
              min-width="10%"
              label="商品总售价"
              label-class-name="totalSalesPriceCell">
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              prop="totalstockprice"
              min-width="10%"
              label="已售商品总进价"
              label-class-name="totalStockPriceCell">
              <template slot-scope="scope">{{ scope.row.totalstockprice }}</template>
            </el-table-column>
            <el-table-column
              prop="money"
              min-width="10%"
              label="实收金额">
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              min-width="10%"
              sortable="custom"
              prop="profits"
              label="毛利">
              <template slot-scope="scope">
                <span>{{Number(Number(scope.row.money) - Number(scope.row.totalstockprice)).toFixed(2)}}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              min-width="10%"
              sortable="custom"
              prop="profitsRate"
              label="毛利率">
              <template slot-scope="scope">{{scope.row.profitsRate != null ? Number(scope.row.profitsRate * 100).toFixed(2)+ '%' : '-'}}</template>
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              总销售量：<span>{{$toDecimalFormat(goods_head.salesnumber, 3)}}</span>
              ，商品总售价：<span>{{'￥' + towNumber(goods_head.totalsalesprice)}}</span>
              ，<span style="color: #567485;" v-if="!$employeeAuth('purchase_price')">已售商品总进价：
              <span>{{'￥' + towNumber(goods_head.totalstockprice)}}</span>，</span>
              实收金额：<span>{{'￥' + towNumber(goods_head.money)}}</span>
              <span style="color: #567485;" v-if="!$employeeAuth('purchase_price')">
              ，净收入：<span>{{'￥' + towNumber(goods_head.receipt)}}</span></span>
            </div>
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="goods_total"
                @current-change="goods_change"
                :current-page="goods_pagenum"
                :page-size="goods_pageSize"
                :page-count="goods_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="goodsSizeChange"
                  :pageSize.sync="goods_pageSize"
                  :currentPage.sync="goods_pagenum"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--会员交易明细-->
    <div class="re_deposit_record_container">
      <div style="overflow: hidden;padding: 10px;" v-show="goods_member == 'member'">
        <div style='float: left;'>
          <div
            class='pc_report2'
            :class="inputing_keyword ? 'focusDate' : 'focusDate1'"
          >
            <input
              @focus='inputing_keyword = true;selectText("vips_keyword_report")'
              @blur='inputing_keyword = false'
              :style="vipSearch ? 'color: #567485;' : 'color: #B1C3CD;'"
              type='text'
              placeholder='请输入姓名/手机号/卡号/刷卡'
              @input="vipSearch = $vipNameFormat(vipSearch)"
              v-model='vipSearch'
              id='vips_keyword_report'
            />
            <img
              alt=""
              class='pc_report1'
              v-show="vipSearch != ''"
              @click="focusInput('vips_keyword_report')"
              src='../../image/pc_clear_input.png'
            />
          </div>
        </div>
        <div @click="focusDate = true" class="date_picker_container"
        :class="focusDate ? 'focusDate' : 'focusDate1'">
          <el-date-picker
            v-model="date_member[0]"
            type="date"
            placeholder="开始日期"
            value-format='yyyy-MM-dd'
            @blur="focusDate = false"
          >
          </el-date-picker>
          <div style="font-size: 16px;color: #567485">至</div>
          <el-date-picker
            v-model="date_member[1]"
            type="date"
            placeholder="结束日期"
            value-format='yyyy-MM-dd'
            @blur="focusDate = false"
          >
          </el-date-picker>
        </div>
        <div class="pc_rep14" @click="member_currentPage = 1;searchMember('')">查询</div>
        <div class="pc_rep15" style="cursor: pointer;" @click="searchMember('excel')">导出表格</div>
      </div>
      <div v-show="goods_member == 'member'" class="re_table_container">
        <div style="height: calc(100vh - 100px)">
          <el-table
            ref="multipleTable"
            :height="member_height"
            stripe
            :empty-text="!loading ? '暂无数据' : ' '"
            :data="member_data"
            :cell-style="cellStyleMember"
            :header-cell-style="headerStyleMember"
            style="font-size: 16px;margin-top: 5px;color: #567485;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange">
            <el-table-column
              prop="name"
              min-width="13%"
              align="left"
              show-overflow-tooltip
              label="会员姓名">
            </el-table-column>
            <el-table-column
              prop="mobile"
              min-width="14%"
              align="center"
              label="手机号">
            </el-table-column>
            <el-table-column
              prop="code"
              min-width="13%"
              align="center"
              show-overflow-tooltip
              label="卡号">
              <template slot-scope="scope">{{ scope.row.code ? scope.row.code : '-'}}</template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              min-width="22%"
              align="center"
              label="交易时间">
            </el-table-column>
            <el-table-column
              prop=""
              align="right"
              min-width="13%"
              label="交易金额">
              <template slot-scope="scope">{{`${!/^[\+-]/.test(scope.row.tranAmount)? '+' : ''}${scope.row.tranAmount}`}}</template>
            </el-table-column>
            <el-table-column
              prop="originName"
              align="center"
              min-width="12%"
              label="交易类型">
            </el-table-column>
            <el-table-column
              prop="hasMoney"
              align="right"
              min-width="13%"
              label="账户余额">
            </el-table-column>
          </el-table>
        </div>
        <div class="re_table_bottom">
            <div>
              会员总数：<span>{{vip_head.vipCounts}}</span>
              ，总消费金额：<span>¥{{Number(vip_head.consumeMoney).toFixed(2)}}</span>
              <span style="color: #567485;">（会员卡消费金额：</span><span>¥{{Number(vip_head.cardMoney).toFixed(2)}}</span>
              ），总储值金额：<span>¥{{Number(vip_head.storeMoney).toFixed(2)}}</span>
              ，总赠送金额：<span>¥{{Number(vip_head.giveMoney).toFixed(2)}}</span>
              ，会员卡总余额：<span>¥{{Number(vip_head.hasMoney).toFixed(2)}}</span>
            </div>
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="member_total"
                @current-change="member_change"
                :current-page="member_currentPage"
                :page-size="member_pageSize"
                :page-count="member_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="memberSizeChange"
                  :pageSize.sync="member_pageSize"
                  :currentPage.sync="member_currentPage"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import { showToast } from '@/utils/util.js';
import logList from '@/config/logList';
import vCjSelect from '@/common/components/CjSelect';
import vCjPageSize from '@/common/components/CjPageSize';
import CjCopy from '@/common/components/CjCopy';
export default {
  data() {
    return {
      loading: false,
      pinyin: false,
      supplierObject: {
        all: false,
        notSupplier: false,
        supplierList: []
      }, // 供应商传参
      inputing_keyword: false,
      detail_value: [''],
      category_list: [],
      keyword_member: '',
      keyword_goods: '',
      goods_member: 'goods',
      member_total: 0,
      goodssort: {},
      member_currentPage: 1,
      member_height: $(window).height() - 175,
      member_pageSize: 10,
      pageKey: 0,
      goods_total: 0,
      goods_pagenum: 1,
      goods_height: $(window).height() - 279,
      goods_pageSize: 10,
      goods_data: [],
      member_data: [],
      multipleSelection: [],
      date_from_goods: '',
      date_goods: '',
      date_from_vip: '',
      date_member: '',
      vipSearch: '',
      goods_head: {
        salesnumber: 0,
        totalsalesprice: 0,
        totalstockprice: 0,
        money: 0,
        receipt: 0
      },
      vip_head: {
        consumeMoney: 0,
        giveMoney: 0,
        hasMoney: 0,
        cardMoney: 0,
        storeMoney: 0,
        vipCounts: 0
      },
      focusDate: false,
      loadingInstance: null,
      colorCheckedArr: [], // 颜色下拉菜单选中内容
      sizeCheckedArr: [],
      seasonCheckedArr: []
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading,
    vCjSelect,
    vCjPageSize,
    CjCopy
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 供应商回调
    searchChange(e) {
      console.log(e, 'eeeeeeeeeeeeeeeeeeeeeee123');
      this.supplierObject = _.cloneDeep(e);
      this.search_goods();
      this.search_goods_head();
    },
    focusInput(sid) {
      this.keyword_goods = '';
      this.vipSearch = '';
      $('#' + sid).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    priceStockStyle(column) {
      let mStyle = '';
      if (column.columnIndex === 6) {
        mStyle += 'padding-right:15px;';
      }
      return mStyle + (column.columnIndex < 4 ? 'padding-left: 15px;' : 'text-align: right');
    },
    cellStyleMember(column) {
      if (column.columnIndex === 6) {
        return 'padding-right:15px';
      }
    },
    headerStyle(column) {
      if (column.columnIndex < 4) {
        return 'padding-left: 15px;';
      } else {
        return column.columnIndex === 6 ? 'text-align: right;padding-right:20px;' : 'text-align: right;';
      }
    },
    headerStyleMember(column) {
      if (column.columnIndex === 6) {
        return 'padding-right:20px';
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /**
     * 排序
     */
    sort_change(e) {
      this.goods_pagenum = 1;
      this.goodssort = e;
      this.search_goods_head();
      this.search_goods();
    },
    goods_change(val) {
      this.goods_pagenum = val;
      this.search_goods_head();
      this.search_goods();
    },
    goodsSizeChange() {
      this.search_goods_head();
      this.search_goods();
    },
    member_change(val) {
      this.member_currentPage = val;
      this.searchMember();
    },
    memberSizeChange() {
      this.searchMember();
    },
    exportAddExcel(exportList) {
      var field_mapping = {
        商品名称: 'name',
        条码: 'code',
        商品分类: 'category',
        供应商: 'supplierName',
        现有库存: 'stock',
        销售数量: 'salesnumber',
        商品总售价: 'totalsalesprice',
        已售商品总进价: 'totalstockprice',
        实收金额: 'money',
        毛利: 'profits',
        '毛利率(%)': 'profitsRate'
      };
      if (this.$employeeAuth('purchase_price')) { // 报表导出毛利毛利率受权限控制
        delete field_mapping['已售商品总进价'];
        delete field_mapping['毛利'];
        delete field_mapping['毛利率(%)'];
      }
      if (exportList.length === 0) {
        demo.msg('warning', '商品销售统计列表不能为空');
      } else {
        exportList.forEach(item => {
          item.code = item.code ? item.code : '';
          item.category = item.category ? item.category : '-';
        });
        this.$makeExcel(exportList, field_mapping, '商品销售统计' + new Date().format('yyyyMMddhhmmss'));
      }
    },
    getGoodsExcel() {
      demo.actionLog(logList.clickGoodsSaleStatisticsExportExcel);
      let sub_data = {
        'systemName': $config.systemName,
        'name': this.keyword_goods.replace(/'/g, "'").replace(/;/g, '；'),
        'fromDate': this.date_goods.length === 0 ? null : this.date_goods[0],
        'toDate': this.date_goods.length === 0 ? null : this.date_goods[1],
        'currentPage': 1,
        'pageSize': 65535,
        'prop': this.goodssort ? this.goodssort.prop : null,
        'order': this.goodssort ? this.goodssort.order : null,
        'profits': this.goodssort ? this.goodssort.profits : null,
        'profitsRate': this.goodssort ? this.goodssort.profitsRate : null,
        'typeFingerprint': this.detail_value.length === 2 ? this.detail_value[1] : this.detail_value[0],
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      this.reportFormLog(_.cloneDeep(sub_data), '商品销售统计列表数据导出');
      // demo.actionLog(logList.search);
      goodService.goodsSaleReport(sub_data, res => {
        var exportList = demo.t2json(res).map(item => {
          return {
            ...item,
            'profitsRate': item.profitsRate == null ? '-' : Number(item.profitsRate * 100),
            'supplierName': item.supplierName || '-'
          };
        });
        this.exportAddExcel(exportList);
      });
    },
    /**
     * 商品销售统计查询
     */
    search_goods_main() {
      if (this.pinyin) {
        return;
      }
      if (!this.date_goods) {
        return Message.warning('请选择开始日期和结束日期');
      }
      this.goods_pagenum = 1;
      this.search_goods_head();
      this.search_goods();
    },
    /**
     * 商品销售统计查询---头部数据
     */
    search_goods_head() {
      if (new Date(this.date_goods[0]).getTime() > new Date(this.date_goods[1]).getTime() && this.date_goods[1]) {
        [this.date_goods[0], this.date_goods[1]] = [this.date_goods[1], this.date_goods[0]]; // 俩元素互换位置
      }
      let sub_data = {
        'systemName': $config.systemName,
        'name': this.keyword_goods,
        'fromDate': this.date_goods.length == 0 ? null : this.date_goods[0],
        'toDate': this.date_goods.length == 0 ? null : this.date_goods[1],
        'typeFingerprint': this.detail_value.length === 2 ? this.detail_value[1] : this.detail_value[0],
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      goodService.goodsSaleReportSum(sub_data, res => {
        this.goods_head = demo.t2json(res)[0];
        console.log(this.goods_head, 'this.goods_head');
      });
    },
    /**
     * 商品销售统计查询---列表数据
     */
    search_goods() {
      this.loading = true;
      let sub_data = {
        'systemName': $config.systemName,
        'name': this.keyword_goods.replace(/'/g, "'").replace(/;/g, '；'),
        'fromDate': this.date_goods.length == 0 ? null : this.date_goods[0],
        'toDate': this.date_goods.length == 0 ? null : this.date_goods[1],
        'currentPage': this.goods_pagenum,
        'pageSize': this.goods_pageSize,
        'prop': this.goodssort ? this.goodssort.prop : null,
        'order': this.goodssort ? this.goodssort.order : null,
        'typeFingerprint': this.detail_value.length === 2 ? this.detail_value[1] : this.detail_value[0],
        'color': JSON.stringify(this.colorCheckedArr),
        'size': JSON.stringify(this.sizeCheckedArr),
        'season': JSON.stringify(this.seasonCheckedArr),
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      // this.reportFormLog(_.cloneDeep(sub_data), '商品销售统计列表数据查询');

      goodService.goodsSaleReportCount(sub_data, res => {
        this.goods_total = Number(demo.t2json(res)[0].count);
      });
      goodService.goodsSaleReport(sub_data, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        this.goods_data = demo.t2json(res).map(item => {
          return {
            ...item,
            'totalsalesprice': Number(item.totalsalesprice).toFixed(2),
            'totalstockprice': Number(item.totalstockprice).toFixed(2),
            'money': Number(item.money).toFixed(2),
            'salesnumber': Number(item.salesnumber),
            'profits': Number(item.profits).toFixed(2),
            'profitsRate': item.profitsRate
          };
        });
        console.log(this.goods_data, 'this.goods_data');
        if (this.goods_data.length === 0 && this.goods_pagenum !== 1) {
          this.goods_pagenum = 1;
          this.search_goods();
        }
      }, () => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      });
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : 0;
    },
    /**
     * 会员交易明细查询---头部数据
     */
    getVipHeadData() {
      let param = {
        'systemName': $config.systemName,
        'phone': this.sys_uid,
        'sysSid': this.sysSid
      };
      demo.$http.post(this.$rest.pc_vipStatistics, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        console.log('==会员交易明细查询头部数据', obj);
        if (obj.code === '0') {
          this.vip_head = obj.data;
          return;
        }
        if (obj.msg.indexOf('该用户没有被授权访问资源') === -1) {
          showToast(this, obj.msg);
        }
      });
    },
    /**
     * 会员交易明细查询---列表数据
     */
    searchMember(str) {
      if (!this.date_member) {
        return Message.warning('请选择开始日期和结束日期');
      }
      if (new Date(this.date_member[0]).getTime() > new Date(this.date_member[1]).getTime() && this.date_member[1]) {
        [this.date_member[0], this.date_member[1]] = [this.date_member[1], this.date_member[0]]; // 俩元素互换位置
      }
      var that = this;
      that.loading = true;
      var sub_data = {
        'systemName': $config.systemName,
        'phone': this.sys_uid,
        'sysSid': this.sysSid,
        'vipSearch': this.vipSearch,
        'fromDate': this.date_member.length == 0 ? null : this.date_member[0],
        'toDate': this.date_member.length == 0 ? null : this.date_member[1],
        'currentPage': this.member_currentPage,
        'pageSize': this.member_pageSize
      };
      if (str === 'excel') {
        demo.actionLog(logList.clickVipDealDetailExportExcel);
        sub_data.currentPage = 1;
        sub_data.pageSize = 65535;
      }
      this.reportFormLog(_.cloneDeep(sub_data), str === 'excel' ? '会员交易明细报表导出' : '会员交易明细查询');
      demo.$http.post(that.$rest.pc_showvipReports, sub_data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          let flag = that.sonarSearchMember(str, rs);
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
          if (!flag && rs.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
            Message.warning(rs.data.msg);
          }
        }).catch(err => {
          console.log('请求异常：', err);
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
        });
    },
    sonarSearchMember(str, rs) {
      let that = this;
      if (str === 'excel' && rs.data.code && rs.data.code === '0') {
        var exportList = rs.data.data.list.map(item => {
          return {
            ...item,
            'code': item.code || '-',
            'tranAmount': Number(item.tranAmount),
            'hasMoney': Number(item.hasMoney)
          };
        });
        that.exportExcel(exportList);
        return true;
      }
      if (str !== 'excel' && rs.data.code && rs.data.code === '0') {
        that.member_data = rs.data.data.list.map(item => {
          return {
            ...item,
            'tranAmount': (item.originName.includes('门店充值') || item.originName.includes('线上退款') ? '+' : '').toString() + Number(item.tranAmount).toFixed(2),
            'hasMoney': Number(item.hasMoney).toFixed(2)
          };
        });
        that.member_total = rs.data.data.total;
        if (rs.data.data.list.length === 0 && that.member_currentPage !== 1) {
          that.member_currentPage = 1;
          that.searchMember();
        }
        return true;
      }
      return false;
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
      }
    },
    exportExcel(exportList) {
      var field_mapping = {
        会员: 'name',
        手机号: 'mobile',
        卡号: 'code',
        交易时间: 'createTime',
        交易金额: 'tranAmount',
        交易类型: 'originName',
        账户余额: 'hasMoney'
      };
      if (exportList.length === 0) {
        demo.msg('warning', '暂无符合条件数据，请重新选择条件');
      } else {
        this.$makeExcel(exportList, field_mapping, '会员交易明细' + new Date().format('yyyyMMddhhmmss'));
      }
    },
    // 获取所有类别
    getAllCategory() {
      var that = this;
      that.category_list = [{label: '全部分类', value: ''}, {label: '称重分类', value: '0'}];
      typeService.search(function(res) {
        var json = demo.t2json(res);
        console.log(json, '分类+');
        if (json.length > 0) {
          json.forEach(item => {
            let obj = {
              label: item.name,
              value: item.fingerprint
            }
            if (item.list.length !== 0) {
              let arr = [];
              item.list.forEach(subItem => {
                let subObj = {
                  label: subItem.name,
                  value: subItem.fingerprint
                }
                arr.push(subObj);
              });
              obj['children'] = arr;
            }
            that.category_list = that.category_list.concat(obj);
          });
        }
      });
    },
    listenResize() {
      this.goods_height = $(window).height() - 279;
      this.member_height = $(window).height() - 175;
      // this.goods_pageSize = parseInt(this.goods_height / 45);
      // this.member_pageSize = parseInt(this.member_height / 53);
    },
    // filterHandler(value, row, column) {
    //   const property = column['property'];
    //   return row[property] === value;
    // },
    udSizeChkArr(id) {
      if (this.sizeCheckedArr.indexOf(id) === -1) {
        this.sizeCheckedArr.push(id);
      } else {
        this.sizeCheckedArr.splice(this.sizeCheckedArr.findIndex(i => i === id), 1);
      }
    },
    udSeasonChkArr(id) {
      if (this.seasonCheckedArr.indexOf(id) === -1) {
        this.seasonCheckedArr.push(id);
      } else {
        this.seasonCheckedArr.splice(this.seasonCheckedArr.findIndex(i => i === id), 1);
      }
    },
    filterHandler() {
      let that = this;
      if (this.filterTimer) {
        clearTimeout(this.filterTimer);
      }
      this.filterTimer = setTimeout(() => {
        that.search_goods_main();
      }, that.delayedTime);
    }
  },
  computed: mapState({
    // isLogin
    pc_detail_tab: state => state.show.pc_detail_tab,
    sys_uid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    delayedTime: state => state.show.delayedTime,
    cardNo: (state) => state.show.cardNo
  }),
  created() {
    this.SET_SHOW({ cardNo: '' });
    let today = new Date();
    let date_to = today.format('yyyy-MM-dd 23:59:59');
    let date_from_goods = today.format('yyyy-MM-dd 00:00:00');
    if (this.pc_detail_tab === 3) {
      this.date_member = [date_from_goods, date_to];
      this.goods_member = 'member';
      demo.actionLog(logList.clickVipDealDetail);
      this.searchMember();
      this.getVipHeadData();
    } else {
      this.getAllCategory();
      this.date_goods = [date_from_goods, date_to];
      this.goods_member = 'goods';
      demo.actionLog(logList.clickGoodsSaleStatistics);
      this.search_goods_main();
    }
  },
  mounted() {
    setTimeout(() => {
      $('#goods_keyword_report').focus();
      $('#vips_keyword_report').focus();
    }, 0);
    this.listenResize();
    let that = this;
    window.onresize = () => {
      if (that.clearOnresize) {
        clearTimeout(that.clearOnresize);
      }
      that.clearOnresize = setTimeout(() => {
        that.listenResize();
        if (that.pc_detail_tab === 3) {
          this.searchMember();
          this.getVipHeadData();
        } else {
          that.search_goods_main();
        }
      }, that.delayedTime);
    };
  },
  destroyed() {
    window.onresize = null;
  },
  watch: {
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      this.vipSearch = this.cardNo;
      this.SET_SHOW({ cardNo: '' });
    },
    pc_detail_tab() {
      if (this.pc_detail_tab === 3) {
        this.goods_member = 'member';
        this.searchMember();
        this.getVipHeadData();
      } else {
        this.goods_member = 'goods';
        this.search_goods_main();
      }
    },
    keyword_goods() {
      var that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.search_goods_main();
      }, that.delayedTime);
    },
    date_goods() {
      var that = this;
      if (that.date_goods) {
        that.search_goods_main();
      }
    },
    detail_value: {
      deep: true,
      handler: function() {
        this.search_goods_main();
      }
    },
    colorCheckedArr() { // 颜色筛选重新检索
      this.filterHandler();
    },
    sizeCheckedArr() {
      this.filterHandler();
    },
    seasonCheckedArr() {
      this.filterHandler();
    }
  }
};
</script>
