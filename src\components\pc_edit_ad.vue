<template>
  <div v-if="show_edit_ad" class="mask">
    <div class="dialog-container">
      <div class="header-container">
        <div class="title">{{ isAdd ? '上传' : '修改' }}广告</div>
        <img src="@/image/zgzn-pos/icon_close.png" class="icon-close" @click="closeDialog">
      </div>
      <div class="item-container">
        <div class="item-title">选择模板</div>
        <div class="ad-type-container">
          <div :class="['ad-item', adSelectedIndex === 0 ? 'ad-item-active' : '']" @click="changeAdTemp(0)">
            <div class="bingo" v-if="adSelectedIndex === 0">
              <img src="@/image/zgzn-pos/bingo.png" class="icon-bingo">
            </div>
            <img src="@/image/zgzn-pos/ad_shan.png" class="ad-shan">
            <div class="ad-bottom-text">这里是广告语</div>
          </div>
          <div :class="['ad-item', 'm-l-r-20', adSelectedIndex === 1 ? 'ad-item-active' : '']" @click="changeAdTemp(1)">
            <div class="ad-shan-container">
              <img src="@/image/zgzn-pos/ad_shan.png" class="ad-shan op-8">
            </div>
            <div class="ad-center-text">这里是广告语</div>
            <div class="bingo" v-if="adSelectedIndex === 1">
              <img src="@/image/zgzn-pos/bingo.png" class="icon-bingo">
            </div>
          </div>
          <div :class="['ad-item', adSelectedIndex === 2 ? 'ad-item-active' : '']" @click="changeAdTemp(2)">
            <div class="bingo" v-if="adSelectedIndex === 2">
              <img src="@/image/zgzn-pos/bingo.png" class="icon-bingo">
            </div>
            <img src="@/image/zgzn-pos/ad_shan.png" class="ad-shan">
          </div>
        </div>
      </div>
      <div class="item-container" v-if="adSelectedIndex !== 2">
        <div class="item-title">广告语</div>
        <el-input
          type="textarea"
          maxlength="50"
          placeholder="请输入广告语，最多50字"
          v-model="adText">
        </el-input>
      </div>
      <div class="item-container">
        <div class="item-title">上传图片</div>
        <div class="upload-container">
          <div class="left-img-container">
            <el-upload action="" accept=".jpg, .jpeg, .png" :show-file-list="false" :on-change="onChange" :auto-upload="false">
              <img v-if="imgUrl" :src="imgUrl" class="img-show">
              <div v-else class="add-img-container">
                <i class="el-icon-plus"></i>
                <div class="upload-text">上传广告</div>
              </div>
            </el-upload>
            <div class="delete-container" @click="deleteImg" v-if="imgUrl">
              <img src="@/image/pc_pay_del_left_list.png" class="icon-delete">
              <div class="text-delete">删除</div>
            </div>
          </div>
          <div class="tips">
            <div v-if="imgUrl">点击图片进行替换</div>
            <div>图片建议尺寸1920*768<br>仅支持格式为JPG或PNG，图片大小不超过3M</div>
          </div>
        </div>
      </div>
      <div class="btn-container">
        <div class="btn del" v-if="!isAdd" @click="visible = true">删除</div>
        <div class="right-area">
          <div class="btn cancel" @click="closeDialog">取消</div>
          <div class="btn save m-l-10" @click="saveImg">保存</div>
        </div>
      </div>
    </div>
    <confirm-dialog
      :visible.sync="visible"
      :message="`删除后不可恢复，是否删除广告？`"
      confirmText="删除"
      @confirm="visible = false;delImg()"
      @cancel="visible = false"
      :closeOnClickModal="false" />
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import { saveAdd, updateAd, delAd, getAds, uploadAdFile } from '@/api/ad';
export default {
  components: {
    ConfirmDialog
  },
  data() {
    return {
      visible: false, // 删除二次确认框
      isAdd: true,
      adId: null, // 广告id
      adSelectedIndex: 0, // 广告类型
      adText: '', // 广告语
      imgFile: null, // 图片文件file
      imgUrl: '' // 图片预览展示地址
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),

    /**
     * 初始化
     */
    init() {
      this.adSelectedIndex = 0;
      this.adText = '';
      this.imgUrl = '';
      this.imgFile = null;
    },

    /**
     * 广告模板切换
     */
    changeAdTemp(index) {
      this.adSelectedIndex = index;
    },

    /**
     * 检测上传图片大小
     */
    async onChange(file, fileList) {
      if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
        return demo.msg('warning', '上传图片只能是 JPG/PNG 格式!');
      }
      const isLt3M = file.size / 1024 / 1024 > 3;
      if (isLt3M) {
        return demo.msg('warning', '上传图片大小不能超过 3MB!');
      }
      this.imgFile = file.raw;
      if (this.isAdd) {
        this.imgUrl = URL.createObjectURL(this.imgFile);
      } else {
        if (file.status !== 'ready') {
          return;
        };
        const res = await uploadAdFile(this.imgFile);
        this.imgUrl = res;
      }
    },

    /**
     * 临时删除图片
     */
    deleteImg() {
      this.imgUrl = '';
    },

    /**
     * 关掉弹窗
     */
    closeDialog() {
      this.SET_SHOW({ show_edit_ad: false });
    },

    /**
     * 保存图片
     */
    async saveImg() {
      if (this.adSelectedIndex !== 2 && !this.adText) {
        return demo.msg('warning', '请输入广告语');
      }
      if (!this.imgUrl) {
        return demo.msg('warning', '请上传图片');
      }
      if (this.isAdd) {
        const formData = new FormData();
        formData.append('templateId', this.adSelectedIndex + 1);
        formData.append('comments', this.adSelectedIndex !== 2 ? this.adText.replace(/'/g, "'").replace(/;/g, '；').trim() : '');
        formData.append('location', 3);
        formData.append('startDate', new Date().format('yyyy-MM-dd hh:mm:ss'));
        formData.append('endDate', '2099-12-12 12:12:12');
        formData.append('duration', 100);
        formData.append('file', this.imgFile);
        formData.append('index', 1); // 播放顺序
        formData.append('type', 3);
        await saveAdd(formData);
      } else {
        const params = {
          templateId: this.adSelectedIndex + 1,
          index: 1,
          id: this.adId,
          img: this.imgUrl,
          comments: this.adSelectedIndex !== 2 ? this.adText.replace(/'/g, "'").replace(/;/g, '；').trim() : ''
        };
        await updateAd(params);
      }
      this.closeDialog();
      demo.msg('success', `广告${this.isAdd ? '上传' : '修改'}成功`);
      this.getData();
    },

    /**
     * 删除广告
     */
    async delImg() {
      await delAd([this.adId]);
      this.closeDialog();
      demo.msg('success', '广告删除成功');
      this.getData();
    },

    /**
     * 获取广告
     */
    async getData() {
      const res = await getAds();
      const arr = res.map(item => {
        item.html = item.html.replace('{picture_path}', item.img).replace('{comments}', item.comments);
        return item;
      });
      this.SET_SHOW({ adsList: arr });
    }
  },
  watch: {
    show_edit_ad(show) {
      if (show) {
        this.init();
        this.isAdd = !this.ad_info;
        this.adId = this.ad_info && this.ad_info.id;
        this.adSelectedIndex = (this.ad_info && this.ad_info.templateId - 1) || 0;
        this.adText = this.ad_info && this.ad_info.comments;
        this.imgUrl = this.ad_info && this.ad_info.img;
      }
    }
  },
  computed: mapState({
    show_edit_ad: state => state.show.show_edit_ad,
    ad_info: state => state.show.ad_info,
    token: state => state.show.token,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid
  })
}
</script>

<style lang="less" scoped>
/deep/ .el-textarea__inner:focus {
  border-color: #bda169;
}
/deep/ .el-textarea__inner {
  height: 150px !important;
}
.left-img-container>div {
  width: 200px;
  height: 107px;
}
/deep/ .el-upload {
  width: 100% !important;
  height: 100% !important;
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 299;
  color: @themeFontColor;
  display: flex;
  justify-content: center;
  align-items: center;
  .dialog-container {
    width: 43%;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    padding: 20px;
    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #E3E6EB;
      padding-bottom: 16px;
      .title {
        font-size: 20px;
        color: #537286;
        font-weight: bold;
      }
      .icon-close {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }
    .item-container {
      .item-title {
        font-size: 16px;
        color: #537286;
        padding: 14px 0;
        font-weight: bold;
      }
      .ad-type-container {
        display: flex;
        align-items: center;
        .ad-item {
          width: 217px;
          flex: 1;
          height: 107px;
          background: #FAFAFA;
          border: 2px solid #B2C3CD;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          overflow: hidden;
          box-sizing: border-box;
          border-radius: 8px;
          cursor: pointer;
          .bingo {
            width: 20px;
            height: 20px;
            background: #BDA169;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom-left-radius: 7px;
            position: absolute;
            top: 0;
            right: 0;
            .icon-bingo {
              width: 17px;
              height: 17px;
            }
          }
          .ad-shan {
            width: 40px;
            height: 40px;
          }
          .op-8 {
            opacity: .4;
          }
          .ad-bottom-text {
            position: absolute;
            z-index: 9;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 28px;
            line-height: 28px;
            background: #AEAEAE;
            text-align: center;
            font-size: 14px;
            color: #000;
            font-weight: bold;
          }
          .ad-shan-container {
            height: 90px;
            background: white;
            width: 100%;
            margin: 0 30px;
            border-radius: 50% 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            opacity: .8;
          }

          .ad-center-text {
            position: absolute;
            z-index: 9;
            left: 0;
            width: 100%;
            height: 28px;
            line-height: 28px;
            text-align: center;
            font-size: 14px;
            color: #000;
            font-weight: bold;
          }
        }
        .ad-item-active {
          border: 2px solid #BDA169;
        }
        .m-l-r-20 {
          margin: 0 20px;
        }
      }

      .upload-container {
        display: flex;
        align-items: center;
        .left-img-container {
          position: relative;
          width: 200px;
          height: 107px;
          border-radius: 8px;
          overflow: hidden;
          border: 2px solid #B2C3CD;
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #FAFAFA;
          cursor: pointer;
          .img-show {
            position: absolute;
            z-index: 5;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
          }
          .add-img-container {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            flex-direction: column;
          }
          .delete-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20px;
            background: #636363;
            z-index: 9;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            .icon-delete {
              width: 16px;
              height: 16px;
            }
            .text-delete {
              font-size: 12px;
              color: white;
              margin-left: 6px;
            }
          }
        }
        .tips {
          color: #BDA169;
          font-size: 16px;
          margin-left: 14px;
        }
        .el-icon-plus {
          font-size: 20px;
          color: #000;
          opacity: .5;
          font-weight: bold;
        }
        .upload-text {
          color: #000;
          opacity: .8;
          margin-top: 5px;
        }
      }
    }
    .btn-container {
      display: flex;
      padding-top: 20px;
      .btn {
        width: 106px;
        height: 48px;
        line-height: 38px;
        border-radius: 4px;
        font-size: 17px;
        text-align: center;
        font-weight: bold;
      }
      .del {
        color: #F64C4C;
        border: 1px solid #F64C4C;
      }
      .right-area {
        display: flex;
        flex: 1;
        justify-content: flex-end;
      }
      .cancel {
        color: #BDA169;
        border: 1px solid #BDA169;
      }
      .save {
        background: #BDA169;
        color: white;
      }
      .m-l-10 {
        margin-left: 10px;
      }
    }
  }
}
</style>
