<style lang="less">
.com_pad1_add_edit_supplier {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 300;
  color: @themeFontColor;
  display: flex;
  justify-content: center;
  align-items: center;
}
.com_supdiv {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 706px;
  height: 423px;
  border-radius: 10px;
  z-index: 150;
  overflow: hidden;
}
.com_supl15 {
  font-size: 18px;
  line-height: 26px;
  margin-top: 20px;
  margin-left: 19px;
  font-weight: bold;
}
.com_sup18 {
  float: left;
  font-size: 16px;
  margin-left: 42px;
  margin-top: 20px;
}
.com_sup19 {
  overflow: hidden;
  margin-bottom: 24px;
  margin-left: 0px;
}
.com_sup19 input {
  font-size: 16px;
  width: 520px;
  height: 44px;
  line-height: 40px;
  float: left;
  text-indent: 20px;
  font-weight: Regular;
  margin-left: -15px;
  border: 1px solid #E3E6EB;
  border-radius: 4px;
}
.com_supa2 {
  color:@themeFontColor;
  font-size: 18px;
  width: 100px;
  font-weight: 700;
  float: left;
  line-height: 30px;
  margin-top: 8px;
  margin-left: 0px;
}
.com_pad27 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.com_pad28 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.com_pad29 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.com_sup1 {
  width: 120px;
  height: 44px;
  margin-top: 0px;
  margin-left: 20px;
  float: right;
  color: #fff;
  background-color: @themeBackGroundColor;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
}
.com_sup2 {
  width: 120px;
  height: 44px;
  margin-top: 0px;
  margin-left: 10px;
  float: right;
  color: @themeBackGroundColor;
  background: #fff;
  border: 1px solid  @themeBackGroundColor;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
}
.com_sup3 {
  width: 120px;
  height: 44px;
  margin-top: 0px;
  margin-left: 42px;
  float: left;
  color: #FF5555;
  background-color: #fff;
  font-size: 20px;
  font-weight: normal;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #FF5555;
}
.com_supban3 {
  width: 120px;
  height: 44px;
  margin-top: 0px;
  margin-left: 42px;
  float: left;
  color: @themeBackGroundColor;
  background-color: #fff;
  font-size: 20px;
  font-weight: normal;
  text-align: center;
  line-height: 40px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid @themeBackGroundColor;
}
.pad_sup1 {
  height: 60px;display: flex;justify-content: space-between;
  align-items: center; border-bottom: 1px solid #E3E6EB;margin: 0 30px;
}
#input {
  background: @input_backgroundColor;
}
.level {
  z-index: 800 !important;
}
</style>
<template>
  <!--新增供应商弹出框-->
  <div v-if="showAddEditSupplier" class="com_pad1_add_edit_supplier">
    <div
      v-show="show_delete"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 700;background: rgba(0,0,0,.5);;"
      @click="show_delete = false"
    ></div>
    <confirm-dialog
      :visible.sync="show_delete"
      :message="infoText"
      customClass="level"
      confirmText="禁用"
      @confirm="continueDelete"
      :closeOnClickModal="false" />
    <div class="com_supdiv" v-show="!show_delete">
      <div class="pad_sup1">
        <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;"
          v-show="suppliersDetail.toString() === ''">
          新增供应商
        </div>
        <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;"
          v-show="suppliersDetail.toString() !== ''">
          编辑供应商
        </div>
        <div
          style="font-size: 30px;color: #8298A6;cursor: pointer;"
          @click="closeAddSuppliers()"
        >×</div>
      </div>
      <!-- <div class="com_supl15" v-show="suppliersDetail.toString() === ''">
        新增供应商
      </div>
      <div class="com_supl15" v-show="suppliersDetail.toString() !== ''">
        修改供应商
      </div> -->
      <div style="overflow: hidden;">
        <div class="com_sup18">
          <div class="com_sup19" style="margin-left: -9px;margin-top:5px;">
            <div class="com_supa2"><span style="color:red">*</span>供应商</div>
            <input
              type="text"
              id="input"
              v-model="oneSupplier.name"
              v-focus-select="'autoFocus,focusSelect'"
              maxlength="60"
              style="margin-left: -7px;padding-right:20px;"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="oneSupplier.name = oneSupplier.name.replace(/[$']/g, '')"
            />
          </div>
          <div class="com_sup19">
            <div class="com_supa2">
              <div style="float: left;">联系人</div>
            </div>
            <input
              type="text"
              id="input"
              v-model="oneSupplier.contacter"
              v-focus-select="'focusSelect'"
              style="width:200px;padding-right:20px;"
              maxlength="20"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="oneSupplier.contacter = oneSupplier.contacter.replace(/[$']/g, '')"
            />
            <div class="com_supa2"
              style="margin-left:30px;">
              <div style="float: left;">联系电话</div>
            </div>
            <!-- <input
              type="text"
              style="width:205px;padding-right:20px;"
              oninput="value = value.replace(/[^0-9]/g, '')"
              v-model="oneSupplier.mobile"
              maxlength="11"
            /> -->
            <input
              type="text"
              id="input"
              style="width:205px;padding-right:20px;"
              v-input-phone
              v-focus-select="'focusSelect'"
              maxlength="11"
              v-model="oneSupplier.mobile"
            />
          </div>
          <div class="com_sup19">
            <div class="com_supa2">
              <div style="float: left;">地址</div>
            </div>
            <input
              type="text"
              id="input"
              v-model="oneSupplier.addr"
              v-focus-select="'focusSelect'"
              style="padding-right:20px;"
              maxlength="60"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="oneSupplier.addr = oneSupplier.addr.replace(/[$']/g, '')"
            />
          </div>
          <div class="com_sup19">
            <div class="com_supa2">
              <div style="float: left;">备注</div>
            </div>
            <input
              type="text"
              id="input"
              style="font-size: 16px;padding-right:20px;"
              maxlength="60"
              v-model="oneSupplier.remark"
              v-focus-select="'focusSelect'"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="oneSupplier.remark = oneSupplier.remark.replace(/[$']/g, '')"
            />
          </div>
        </div>
      </div>
      <div style="margin-top:1px;">
        <div
        class="com_sup1"
        style="margin-right: 59px;"
        @click="submitSuppliers()"
        >
          保存
        </div>
        <div
          class="com_sup2"
          @click="closeAddSuppliers()"
        >
          取消
        </div>
        <div
          v-show="suppliersDetail.toString() !== '' && suppliersDetail.id !== 1"
          @click="suppliersDetail.isDeleted === 0 ? deleteSuppliers() : reuseSuppliers()"
          :class="suppliersDetail.isDeleted === 0 ? 'com_sup3' : 'com_supban3'"
        >
          {{suppliersDetail.isDeleted === 0 ? '禁用' : '启用'}}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import ConfirmDialog from '@/common/components/ConfirmDialog';

export default {
  components: {
    ConfirmDialog
  },
  data() {
    return {
      oneSupplier: {
        name: '', // 供应商
        contacter: '', // 联系人
        mobile: '', // 手机号
        addr: '', // 地址
        remark: '' // 备注
        // fingerprint: '',
        // create_by: '', // uid
        // revisetime: ''
      },
      multipleSelection: [],
      show_delete: false, // 删除提示框
      upload_data: {},
      submit_click: false,
      pinyin: false,
      infoText: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    deleteSuppliers() {
      supplierService.hasGoodsSupplier({fingerprint: this.suppliersDetail.fingerprint}, res => {
        this.infoText = res ? '该供应商下有商品存在，禁用<br/>后将清空商品的供应商信息，<br/>确定继续吗？' : '禁用后将无法在商品管理、<br/>进货时选择此供应商';
        this.show_delete = true;
        this.SET_SHOW({ showAddEditSupplier: true });
      });
    },
    reuseSuppliers() {
      var that = this;
      var data = {
        status: this.suppliersDetail.isDeleted === 0 ? 1 : 0,
        revise_by: this.loginInfo.uid,
        fingerprint: this.suppliersDetail.fingerprint
      };
      supplierService.updateSupplierStatus(data, () => {
        that.SET_SHOW({ returnSuppliersDetail: [] });
        demo.msg('success', '启用供应商成功');
        this.$event.$emit('refreshSuppliers', true);
        that.show_delete = false;
        that.closeAddSuppliers();
      });
    },
    continueDelete() {
      var that = this;
      var data = {
        status: this.suppliersDetail.isDeleted === 0 ? 1 : 0,
        revise_by: this.loginInfo.uid,
        fingerprint: this.suppliersDetail.fingerprint
      };
      supplierService.updateSupplierStatus(data, () => {
        that.SET_SHOW({ returnSuppliersDetail: [] });
        demo.msg('success', '禁用供应商成功');
        this.$event.$emit('refreshSuppliers', true);
        that.show_delete = false;
        that.closeAddSuppliers();
      });
    },
    submitSuppliers() {
      var that = this;
      if (this.submit_click === true) {
        return;
      }
      this.submit_click = true;
      setTimeout(function() {
        that.submit_click = false;
      }, that.clickInterval);
      if (this.oneSupplier.name === '') {
        demo.msg('warning', that.$msg.enter_supplier_name);
        return;
      }
      if (this.suppliersDetail.toString() === '') {
        // 新增供应商接口
        this.SET_SHOW({ returnSuppliersDetail: this.oneSupplier });
        this.oneSupplier.create_by = this.loginInfo.uid;
        this.oneSupplier.fingerprint = md5(this.oneSupplier.name + '_' + this.oneSupplier.mobile);
        supplierService.insertSupplier(this.oneSupplier, res => {
          console.log(demo.t2json(res), '新增成功');
          if (demo.t2json(res).code === 0) {
            demo.msg('success', '新增供应商成功');
            this.$event.$emit('refreshSuppliers', true);
            that.closeAddSuppliers();
          } else if (demo.t2json(res).code === -1) {
            demo.msg('warning', '该供应商已存在');
          } else {
            console.log(demo.t2json(res).code);
          }
        });
      } else {
        // 修改供应商接口
        this.SET_SHOW({ returnSuppliersDetail: this.oneSupplier });
        console.log(this.oneSupplier, '修改oneSupplier');
        // this.oneSupplier.revisetime = new Date().format('yyyy-MM-dd hh:mm:ss');
        this.oneSupplier.revise_by = this.loginInfo.uid;
        // this.oneSupplier.id = this.suppliersDetail.id;
        // 根据return code判断供应商名称是否已经存在
        supplierService.updateSupplier(this.oneSupplier, res => {
          console.log(demo.t2json(res));
          if (demo.t2json(res).code === 0) {
            demo.msg('success', '编辑供应商成功');
            this.$event.$emit('refreshSuppliers', true);
            that.closeAddSuppliers();
          } else if (demo.t2json(res).code === -1) {
            demo.msg('warning', '供应商名称重复');
          } else {
            console.log(demo.t2json(res).code);
          }
        });
      }
    },
    closeAddSuppliers() {
      this.SET_SHOW({ suppliersDetail: [] });
      this.oneSupplier = {
        name: '', // 供应商
        contacter: '', // 联系人
        mobile: '', // 手机号
        addr: '', // 地址
        remark: '' // 备注
        // fingerprint: '',
        // create_by: '', // uid
        // revisetime: ''
      };
      this.SET_SHOW({ showAddEditSupplier: false });
    }
  },
  watch: {
    suppliersDetail() {
      if (this.suppliersDetail.toString() !== '') {
        this.oneSupplier = _.cloneDeep(this.suppliersDetail);
      }
    }
  },
  computed: mapState({
    showAddEditSupplier: state => state.show.showAddEditSupplier,
    suppliersDetail: state => state.show.suppliersDetail,
    returnSuppliersDetail: state => state.show.returnSuppliersDetail,
    token: state => state.show.token,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    loginInfo: state => state.show.loginInfo,
    clickInterval: state => state.show.clickInterval
  }),
  mounted() {
    this.upload_data = {
      type: 1,
      sys_uid: this.sysUid,
      sys_sid: this.sysSid
    };
  }
};
</script>
