<template>
<!-- 供应商详情弹窗 -->
  <div v-if="showSupplierInformation">
    <div class="content">
      <div class="content-center">
        <div class="center-top">
          <div class="top-title">{{ supplierTitle }}</div>
          <div class="top-close" @click="close">×</div>
        </div>
        <div class="center-boder"></div>
        <div class="centet-title">
          <div class="title">基本信息</div>
          <div class="title-list">
            <div class="list-content">
              <span class="list-left">供应商：</span>
              <el-popover
                :disabled="isNullOrTrimEmpty(detailTitle.supplierName).toString().length < 30"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="detailTitle.supplierName">
                  <div slot="reference" class="list-right pc_pay199">{{ detailTitle.supplierName || '-' }}</div>
              </el-popover>
            </div>
            <div class="list-content">
              <div class="list-title">
                <div class="title-contact">
                  <span class="list-left">联系人：</span>
                  <el-popover
                    :disabled="isNullOrTrimEmpty(detailTitle.supplierContacter).toString().length < 20"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="detailTitle.supplierContacter">
                      <div slot="reference" class="list-right__content pc_pay199">{{ detailTitle.supplierContacter || '-' }}</div>
                  </el-popover>
                </div>
                <div class="title-contact">
                  <span class="list-left">联系电话：</span>
                  <span class="list-right__phone">{{ detailTitle.supplierMobile || '-' }}</span>
                </div>
              </div>
            </div>
            <div class="list-content">
              <span class="list-left">地址：</span>
              <el-popover
                :disabled="isNullOrTrimEmpty(detailTitle.supplierAddr).toString().length < 30"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="detailTitle.supplierAddr">
                  <div slot="reference" class="list-right pc_pay199">{{ detailTitle.supplierAddr || '-' }}</div>
              </el-popover>
            </div>
            <div class="list-content">
              <span class="list-left">备注：</span>
              <el-popover
                :disabled="isNullOrTrimEmpty(detailTitle.supplierRemark).toString().length < 30"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="detailTitle.supplierRemark">
                  <div slot="reference" class="list-right pc_pay199">{{ detailTitle.supplierRemark || '-' }}</div>
              </el-popover>
            </div>
          </div>
        </div>
        <div class="centet-table">
          <div class="table-top">
            <div class="title">进货历史（最近3次）</div>
            <el-popover
              :disabled="isNullOrTrimEmpty(detailTitle.name).toString().length < 40"
              popper-class="pc_pay192 popper_self"
              placement="top"
              trigger="hover"
              :content="detailTitle.name">
                <div slot="reference" class="goods-name pc_pay199">商品名称：{{ detailTitle.name || '-' }}</div>
            </el-popover>
          </div>
          <div class="table-content">
            <el-table
              :data="dataList"
              :header-cell-style="{background:'#F5F8FB',color:'#537286'}"
              :row-class-name="tableRowClassName"
              style="font-size: 14px;color: #567485;">
              <el-table-column
                prop="createAt"
                width="150px"
                label="进货时间">
                <template slot-scope="scope">
                  {{ scope.row.createAt || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="purPrice"
                align="center"
                label="进货价">
                <template slot-scope="scope">
                  {{ scope.row.purPrice || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="supplierName"
                show-overflow-tooltip
                label="供应商">
                <template slot-scope="scope">
                  {{ scope.row.supplierName || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="supplierContacter"
                show-overflow-tooltip
                label="联系人">
                <template slot-scope="scope">
                  {{ scope.row.supplierContacter || '-' }}
                </template>
              </el-table-column>
              <el-table-column
                prop="supplierMobile"
                width="110px"
                label="联系电话">
                <template slot-scope="scope">
                  {{ scope.row.supplierMobile || '-' }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'SupplierInformation',
  data() {
    return {
      showSupplierInformation: false, // 是否显示弹窗
      dataList: []
    }
  },
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    supplierTitle: {
      type: String,
      default: '供应商信息'
    },
    detailTitle: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.showSupplierInformation = val;
      },
      immediate: true
    },
    detailTitle: {
      handler(val) {
        const params = {goodFingerprint: this.detailTitle.fingerprint};
        purchaseService.getPurGoodsSupplier(params, res => {
          this.dataList = _.cloneDeep(res);
        })
      },
      immediate: true
    }
  },
  computed: {
  },
  methods: {
    close() {
      this.$emit('cancel');
      this.$emit('update:visible', false);
    },
    isNullOrTrimEmpty(obj) {
      if (obj === null || obj === undefined || obj === '') {
        return '';
      }
      return obj;
    },
    // 表格单双行颜色
    tableRowClassName({row, rowIndex}) {
      console.log(row, rowIndex);
      if ((rowIndex + 1) % 2 === 0) {
        return 'success-row';
      }
      return '';
    }
  }
}
</script>
<style scoped lang="less">
/deep/ .el-table .success-row {
  background: #FAFAFA !important;
}
.list-right__content {
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  color: @themeFontColor;
  display: flex;
  align-items: center;
  .content-center {
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 681px;
    border-radius: 10px;
    overflow: hidden;
    .center-top {
      display: flex;
      justify-content: space-between;
      padding: 10px 20px;
      align-items: center;
      .top-title {
        color: @themeFontColor;
        font-weight: 700;
        font-size: 20px;
      }
      .top-close {
        cursor: pointer;
        font-size: 30px;
        color: #8298A6;
      }
    }
    .center-boder {
      margin: 0 20px;
      border: 1px solid #E3E6EB;
    }
    .centet-title {
      padding: 20px 20px;
      color: @homeColor;
      .title {
        font-size: 16px;
        font-weight: 700;
      }
      .title-list {
        margin-top: 20px;
        padding: 10px 10px 10px 10px;
        background: @input_backgroundColor;
        border-radius: 5px;
        .list-content {
          padding: 5px 5px 5px 5px;
          width: 100%;
          font-size: 14px;
          display: flex;
          align-items: center;
          font-weight: 500;
          .list-left {
            display: inline-block;
            width: 100px;
          }
          .list-right {
            width: 500px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .list-title {
            display: flex;
            columns: row;
            align-items: center;
            .title-contact {
              width: 50%;
              display: flex;
              columns: row;
              align-items: center;
              .list-right__phone {
                 width: 200px;
                 display: inline-block;
                 overflow: hidden; // 溢出隐藏
                 white-space: nowrap; // 强制一行
                 text-overflow: ellipsis; // 文字溢出显示省略号
              }
            }

          }
        }
      }
    }
    .centet-table {
      padding: 20px 20px;
      color: #537286;
      .table-top {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 700;
        .title {
          color: @themeFontColor;
          width: 200px;
        }
        .goods-name {
          color: @text;
          max-width: 450px;
          display: inline-block;
          overflow: hidden; // 溢出隐藏
          white-space: nowrap; // 强制一行
          text-overflow: ellipsis; // 文字溢出显示省略号
        }
      }
      .table-content {
        margin-top: 20px;
        border: 1px solid #f2f2f2;
        border-radius: 5px;
        overflow: hidden;
      }
    }
  }
}
</style>
