<style lang="less" scoped>
.pc_det34 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_det34 input {
  width: 237px;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 16px;
  margin-top: 8px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_det35 {
  width: 18px;
  height: 18px;
  margin-top: 12px;
  margin-left: 14px;
  float: left;
  cursor: pointer;
}
.psk_det11 {
  width: 325px;
  height: 38px;
  border: 1px solid #bbb;
  border-radius: 19px;
  margin: 0 auto;
  margin-top: 20px;
  display: none;
}
.psk_det12 {
  width: 24px;
  height: 24px;
  margin-top: 6px;
  margin-left: 10px;
  float: left;
}
.psk_det13 {
  width: 250px;
  height: 28px;
  line-height: 28px;
  margin-left: 3px;
  font-size: 14px;
  margin-top: 4px;
  border: none;
  color: #666;
  float: left;
}
.psk_det14 {
  width: 16px;
  height: 16px;
  margin-top: 10px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.psk_det18 {
  overflow: hidden;
  width: 100%;
}
.psk_det19 {
  color: @themeBackGroundColor;
  font-weight: bold;
  overflow: hidden;
  padding-left: 15px;
  padding-right: 15px;
  line-height: 50px;
  font-size: 15px;
}
.psk_det19 div {
  float: left;
}
.psk_det2 {
  margin-top: 5px;
  overflow: hidden;
  font-size: 15px;
}
.psk_det2 div {
  float: left;
}
.psk_det21 {
  margin-top: 10px;
  font-size: 15px;
}
.psk_det22 {
  padding-left: 15px;
  padding-right: 15px;
  overflow: hidden;
  padding-bottom: 5px;
  cursor: pointer;
}
.psk_det222 {
  background: @themeBackGroundColor;
  color: #FFF
}
.psk_det23 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 180px;
  font-size: 16px;
  border-top: 2px dashed #e5e5e5;
}
.psk_det24 {
  overflow: hidden;
  line-height: 18px;
  margin-top: 25px;
}
.psk_det24 div {
  float: left;
  width: 45%;
  text-indent: 40px;
}
.psk_det26 {
  font-size: 16px;
  color: @themeBackGroundColor;
  font-weight: bold;
}
.psk_det27 {
  overflow: hidden;
  line-height: 18px;
  margin-top: 24px;
}
.psk_det27 div {
  float: left;
  width: 33%;
  text-indent: 40px;
}
.psk_det31 {
  width: 400px;height: 62px;
  background: @linearBackgroundColor;
  color: #FFF;line-height: 62px;font-size: 16px;text-indent: 32px;
  position: absolute;left: 0;bottom: 0;
}
.psk_det32 {
  float: right;
  width: 130px;
  height: 44px;
  line-height: 44px;
  background: @themeBackGroundColor;
  color: white;
  text-align: center;
  font-size: 18px;
  border-radius: 22px;
  margin-right: 10px;
  cursor: pointer;
  font-weight: bold;
}
.psk_det33 {
  float: left;width: 100px;height: 44px;background: @themeBackGroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: 700;border-radius: 22px;margin-left: 20px;cursor: pointer;
}
.psk_det34 {
  float: right;margin-right: 10px;
  width: 110px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 22px;
  font-weight: 700;
  font-size: 18px;
  color: #FFF;line-height: 44px;
  text-align: center;
  cursor: pointer;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-weight: normal;
  }
  .el-input__suffix {
    top: 2px;
  }
}
.date_picker_container{
  float: left;
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  display: flex;
  align-items: center;
  margin-left: 10px;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  color: @themeFontColor;
  font-size: 16px;
  padding-right: 0;
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  color: @themeFontColor;background: #FFF;
}
/deep/ .el-icon-date:before {
  content: '';
}
.el-table--border::after, .el-table--group::after, .el-table::before {
  background-color: #fff;
}
.isInPutIng  {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)"
    style="width: 100%;height: 100%;color: #567485;font-weight: bold;">
    <div style="width: 100%;height: 100%;padding: 10px;background: #F5F8FB;">
      <div style="overflow: hidden;font-size: 16px;height: 44px;">
        <div style='float: left;'>
          <div
            class='pc_det34'
            :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng1'"
          >
            <input
              @focus='inputing_keyword = true'
              @blur='inputing_keyword = false'
              type='text'
              placeholder='商品名称/条码/首字母/扫码/备注'
              v-model='keyword'
              id='goods_keyword'
              style="margin-left:20px;font-weight: normal;"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="keyword = $goodsNameFormat(keyword)"
              @keydown.enter="inputSelectHandler('goods_keyword')"
            />
            <img
              alt=""
              class='pc_det35'
              v-show="keyword != ''"
              @click="inputFocus('goods_keyword')"
              src='../image/pc_clear_input.png'
            />
          </div>
        </div>
        <div
          class="date_picker_container"
          :class="focusDate ? 'isInPutIng' : 'isInPutIng1'"
        >
          <el-date-picker
            v-model="fromDate"
            type="date"
            placeholder="开始日期"
            style="height:44px"
            @focus="focusDate = true"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
          <div style="font-size: 16px;color: #567485;margin-top: 2px;font-weight: normal;">至</div>
          <el-date-picker
            v-model="toDate"
            type="date"
            placeholder="结束日期"
            style="height:44px"
            @focus="focusDate = true"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
        </div>
        <div @click="currentPage = 1;getLeftList('', true)" class="psk_det33">查询</div>
        <div class="psk_det34" @click="getLeftList('excel', true)">导出表格</div>
      </div>
      <div
        style="width: 100%;height: calc(100% - 102px);margin-top: 10px;font-size: 16px;border: 1px solid #e3e6eb;
        position: relative;border-radius: 5px;overflow: hidden;background: #FFF;"
      >
        <div style="height: 100%;">
          <div
            style="width: 400px;float: left;border-right: 1px solid #DEE2E6;height:100%;"
          >
            <div id="leftList_id" style="height: calc(100% - 60px);overflow: scroll;">
              <div class="psk_det18"
                v-for="(lef,index1) in leftList_total"
                v-bind:key="index1"
              >
                <div class="psk_det19">
                  <div style="float: left;">{{lef.optDate}}</div>
                  <div style="float: right;right: 0;" v-show="!$employeeAuth('purchase_price')">{{Number(lef.dayTotalMoney).toFixed(2)}}</div>
                </div>
                <div
                  v-for="(le,index2) in lef.details"
                  v-bind:key="le.index2"
                  class="psk_det22"
                  @click="goodsDetail(le);change_color = index1 + '_' + index2"
                  :class="{psk_det222 : change_color == index1 + '_' + index2}"
                >
                  <div class="psk_det2">
                      <div style="width: 120px;">盘点</div>
                      <div style="float: right;margin-right: 0;"
                        v-show="!$employeeAuth('purchase_price')">
                        {{Number(le.diffAmt).toFixed(2)}}</div>
                    </div>
                    <div class="psk_det21">
                      <div>{{le.createAt.split(' ')[1]}}</div>
                    </div>
                </div>
              </div>
              <div style="height: 15px;"></div>
            </div>
            <!--合计按钮-->
            <div class="psk_det31">
              <div style="float:left">
                 <span>共</span>
              <span>{{isNaN(total_oreder) ? '0.00' : total_oreder}}</span>
              <span>单</span>
              </div>
              <div style="float: right" v-show="!$employeeAuth('purchase_price')">
                <span>合计:</span>
                <span style="float:right;margin-right:20px">¥ {{ $toDecimalFormat(total_all_money, 2, true) }}</span>
              </div>
            </div>
          </div>
          <!--右边部分-->
          <div v-if="show_detail" style="width: calc(100% - 400px);float: left;height: calc(100% - 20px);position: relative;">
            <div style="height: 105px;overflow: hidden;line-height: 16px;">
              <div style="color: #d5aa76;font-size: 16px;margin-top: 25px;margin-left: 30px;font-weight: bold;">盘点单详情</div>
              <div style="overflow: hidden;color: #567485;font-weight: bold;font-size: 15px;margin-top: 33px;margin-left: 30px;">
                单号：{{right_msg.code}}
              </div>
            </div>
            <div style="width: calc(100% - 20px);margin-left: 10px;overflow: hidden;">
              <el-table
                ref="multipleTable"
                empty-text=" "
                :data="right_list"
                :height="table_height"
                tooltip-effect="dark"
                style="float: left;width: 100%;font-size: 16px;font-weight: normal;"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  show-overflow-tooltip
                  width="60"
                  align="center"
                ></el-table-column>
                <el-table-column label="商品名称" min-width="110">
                  <template slot-scope="scope">
                    <div>{{ scope.row.name }}</div>
                    <div style="color: #B2C3CD;">{{ scope.row.code }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="进货价" width="125" align="right" v-if="!$employeeAuth('purchase_price')">
                  <template slot-scope="scope">¥ {{ $toDecimalFormat(scope.row.price, 6) }}</template>
                </el-table-column>
                <el-table-column label="盘前库存" width="125" align="right" prop="accountQty"></el-table-column>
                <el-table-column label="盘后库存" width="125" align="right" prop="actualQty"></el-table-column>
                <el-table-column label="盈亏数" width="125" align="right" prop="diffQty"></el-table-column>
                <el-table-column label="盈亏金额" width="150" align="right" v-if="!$employeeAuth('purchase_price')">
                  <template slot-scope="scope">¥ {{ Number(scope.row.diffAmt).toFixed(2) }}</template>
                </el-table-column>
              </el-table>
            </div>
            <!--合计部分-->
            <div style="height: 35px;line-height: 35px;text-indent: 40px;"></div>
            <div class="psk_det23">
              <div class="psk_det27">
                <div>收银员：{{right_msg.employeeNumber}}</div>
              </div>
              <div class="psk_det27">
                <div>盘前库存总数：{{right_msg.accountQty}}</div>
                <div>盘后库存总数：{{right_msg.actualQty}}</div>
                <div>盈亏总数：{{right_msg.diffQty}}</div>
              </div>
              <div class="psk_det27">
                <div style="width: 66%;">商品总盈亏金额：
                  <span v-if="!$employeeAuth('purchase_price')">¥ {{Number(right_msg.diffAmt).toFixed(2)}}</span>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="psk_det27">
                <div style="width: calc(100% - 20px);">
                  说明：{{right_msg.remark === '' ? '-' : right_msg.remark}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
export default {
  data() {
    return {
      loading: false,
      fromDate: '',
      toDate: '',
      focusDate: false,
      change_color: '',
      // 供应商选择id
      company_id: '',
      keyword: '',
      // 左侧列表
      leftList_total: [],
      // 右侧详细信息（单号等）
      right_msg: [],
      // 右侧详情列表
      right_list: [],
      table_height: 0,
      total_all_money: 0,
      total_oreder: 0,
      start_date: '2000-01-01',
      end_date: '2047-12-30',
      delete_order: false,
      delete_goods: false,
      show_detail: false,
      stop_choose: false,
      // 允许监听进行请求加载
      allow_loading: false,
      pd_stop_choose: false,
      allow_list: false,
      inputing_keyword: false,
      pinyin: false
    };
  },
  created() {
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.table_height = $(window).height() - 460;
    demo.actionLog(logList.clickStockInventoryDetail);
  },
  mounted() {
    $('#goods_keyword').focus();
    this.SET_SHOW({
      isLogo: true
    });
    this.SET_SHOW({
      isHeader: true
    });
    this.allow_list = false;
    this.getLeftList('');
    var that = this;
    // 起始加载时，监听事件生效，多请求两次
    setTimeout(function() {
      that.allow_loading = true;
      that.pd_stop_choose = true;
    }, 100);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    inputFocus(id) {
      this.keyword = '';
      $('#' + id).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    goodsDetail(json) {
      var that = this;
      console.log(Number(json.id), 9999);
      // 销售明细的单条商品详情
      inventoryService.getInventoryAndItemsById(Number(json.id), function(res) {
        console.log(res);
        that.right_msg = res.inventories;
        that.right_list = res.inventories.items;
        console.log(that.right_list, 'that.right_list');
      });
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_check_detail', action: 'reportFormLog', description});
      }
    },
    exportSalesExcel(exportList) {
      exportList = exportList.map(item => {
        return {
          ...item,
          'gCode': item.gCode == null ? '' : item.gCode,
          'accountQty': isNaN(item.accountQty) ? item.accountQty : Number(item.accountQty),
          'actualQty': isNaN(item.actualQty) ? item.actualQty : Number(item.actualQty),
          'diffAmt': isNaN(item.diffAmt) ? item.diffAmt : Number(item.diffAmt),
          'diffQty': isNaN(item.diffQty) ? item.diffQty : Number(item.diffQty)
        };
      });
      var field_mapping = {};
      if (!this.$employeeAuth('purchase_price')) {
        field_mapping = {
          单号: 'code',
          盘点时间: 'createAt',
          收银员: 'employeeNumber',
          商品名称: 'itemName',
          商品条码: 'gCode',
          进货价: 'purPrice',
          盘前库存: 'accountQty',
          盘后库存: 'actualQty',
          盈亏数: 'diffQty',
          盈亏金额: 'diffAmt',
          盘前库存总数: 'totalAccountQty',
          盘后库存总数: 'totalActualQty',
          盈亏总数: 'totalDiffQty',
          商品总盈亏金额: 'totalDiffAmt',
          说明: 'remark'
        };
      } else {
        field_mapping = {
          单号: 'code',
          盘点时间: 'createAt',
          收银员: 'employeeNumber',
          商品名称: 'itemName',
          盘前库存: 'accountQty',
          盘后库存: 'actualQty',
          盈亏数: 'diffQty',
          说明: 'remark'
        };
      }
      if (exportList.length === 0) {
        demo.msg('warning', '暂无符合条件数据，请重新选择条件');
      } else {
        this.$makeExcel(exportList, field_mapping, '盘点明细' + new Date().format('yyyyMMddhhmmss'));
      }
    },
    getLeftList(str, logFlg) {
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      this.allow_list = true;
      if (this.pd_stop_choose === true) {
        this.stop_choose = true;
      }
      var that = this;
      setTimeout(function() {
        that.stop_choose = false;
      }, 1000);
      // 销售明细左侧列表
      this.loading = true;
      var data = {
        from: this.fromDate,
        to: this.toDate,
        keyword: this.keyword
      };
      // this.reportFormLog(_.cloneDeep(data), str === 'excel' ? '盘点明细导出表格' : '盘点明细查询');
      if (str === 'excel') {
        demo.actionLog(logList.clickStockInventoryDetailExportExcel);
        inventoryService.detailExport(data, function(res) {
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
          var exportList = demo.t2json(res);
          console.log(exportList, 'exportList');
          that.exportSalesExcel(exportList);
        }).catch(() => {
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
        });
      } else {
        inventoryService.detailReports(data, function(res) {
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
          console.log(res);
          that.leftList_total = [];
          // 这个接口返回空，为res = {}
          if (res.days.length === 0) {
            that.total_all_money = 0;
            that.total_oreder = 0;
            that.show_detail = false;
            demo.msg('warning', '暂无数据');
          } else {
            that.leftList_total = res.days;
            that.total_oreder = res.inventoryCount;
            that.total_all_money = res.totalMoney;
            that.show_detail = true;
            that.change_color = '0_0';
            that.goodsDetail(res.days[0].details[0]);
          }
        },
        function(res) {
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
          console.error(res);
        }
        );
      }
    }
  },
  computed: mapState({
    loginInfo: state => state.show.loginInfo,
    username: state => state.show.username,
    delayedTime: state => state.show.delayedTime,
    phone: state => state.show.phone,
    clickInterval: state => state.show.clickInterval
  })
};
</script>
