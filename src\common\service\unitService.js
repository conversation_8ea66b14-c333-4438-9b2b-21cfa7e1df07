import dao from '../dao/dao';

const unitService = {
  /**
   * 获取所有商品单位
   * @param {*} onSuccess
   * @param {*} onFail
   */
  search: function (onSuccess, onFail) {
    dao.exec(sqlApi.unitSearch, onSuccess, onFail);
  },

  /**
   * 判断商品单位是否存在
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  exist: function (data, onSuccess, onFail) {
    var datacp = _.cloneDeep(data);
    datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');

    dao.exec(sqlApi.unitExist.format(datacp), onSuccess, onFail);
  },

  use: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.unitUse.format(data), onSuccess, onFail);
  },

  weight: function (onSuccess, onFail) {
    dao.exec(sqlApi.unitWeight, onSuccess, onFail);
  },

  batchPutItem: function(data, onSuccess, onFail) {
    let insertUnitList = data;
    if (insertUnitList.length > 0) {
      let insertList = insertUnitList.slice(0, 500);
      insertUnitList = insertUnitList.length > 500 ? insertUnitList.slice(500) : [];
      let unitValues = '';
      insertList.map(name => {
        unitValues += `('${name}', '','${md5(name)}'),`;
      });
      // 插入数据sql生成完了
      dao.exec(sqlApi.unitBatchInsert.format(unitValues.substr(0, unitValues.length - 1)), () => {
        this.batchPutItem(insertUnitList, onSuccess, onFail);
      });
    } else {
      onSuccess();
    }
  },

  batchPut: function (data, onSuccess, onFail) {
    let unitList = data;
    // 插入商品单位
    dao.exec(sqlApi.unitBatchSelect, res => {
      const unitsNames = demo.t2json(res);
      // 获取需要插入数据
      const insertUnitList = unitList.filter(unitName => unitsNames.every(units => units.name !== unitName));
      // 更新原有删除数据
      dao.exec(sqlApi.unitBatchUpdate.format(unitList.join("','")), () => { this.batchPutItem(insertUnitList, onSuccess, onFail); }, onFail);
    }, onFail);
  },

  /**
   * 新增商品单位
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (data, onSuccess, onFail) {
    if (+data.is_deleted === 1) {
      this.use(data, function (res) {
        if (demo.t2json(res)[0].cnt > 0) {
          demo.$toast('商品使用中，不允许删除');
        } else {
          dao.exec(sqlApi.unitDel.format(data), onSuccess, onFail);
        }
      });
    } else {
      var datacp = _.cloneDeep(data);
      datacp.name = datacp.name.replace(/'/g, '‘').replace(/;/g, '；');

      this.exist(data, res => {
        var json = demo.t2json(res);
        if (json.length === 0) {
          data.fingerprint = md5(data.name);
          datacp.fingerprint = data.fingerprint;
          if (data.id === undefined) {
            this.add(datacp, onSuccess, onFail);
          } else {
            dao.exec(sqlApi.unitUpdate.format(datacp), onSuccess, onFail);
          }
        } else {
          demo.$toast('数据重复');
        }
      }, onFail);
    }
  },

  add: function (datacp, onSuccess, onFail) {
    dao.exec(sqlApi.unitInsertExist.format(datacp), unitListJson => {
      var unitList = demo.t2json(unitListJson);
      var unit = unitList.find(e => e.name === datacp.name && +e.isDeleted === 1);
      if (unit) {
        dao.exec(sqlApi.unitDeleteRecovery.format(unit.id), onSuccess, onFail);
      } else {
        dao.exec(sqlApi.unitInsert.format(datacp), onSuccess, onFail);
      }
    }, onFail);
  }

};

window.unitService = unitService;
export default unitService;
