﻿global.sqlApi = {
  existsTable: `SELECT count(1) as cnt from sqlite_master WHERE type = 'table' and tbl_name = '{tblName}'`,
  unitSearch: 'select id,name,pinyin,fingerprint from units where is_deleted=0 order by id',
  unitExist: `select id, name from units where name in ('{name}') and is_deleted=0`,
  unitInsertExist: `select id,name,fingerprint,is_deleted from units where name = '{name}' or fingerprint='{fingerprint}';`,
  unitWeight: `select id, name from goods where is_deleted =0 and unit_fingerprint in (select fingerprint from units where name in
    ('千克','克','斤','公斤','两','g','G','kg','Kg','kG', 'KG'));`,
  unitInsert: `insert into units(name, pinyin, fingerprint) values('{name}','{pinyin}','{fingerprint}')`,
  unitUpdate: `update units set name='{name}', pinyin='{pinyin}', fingerprint='{fingerprint}',is_synced=0 where id={id}`,
  unitDel: `update units set is_synced=0, is_deleted=1 where name='{name}'`,
  unitUse: `select count(*) as cnt from goods where is_deleted=0 and unit_fingerprint in ('{unitFingerprint}')`,
  unitBatchSelect: `select id, name from units;`,
  unitBatchUpdate: `update units set is_synced=0, is_deleted=0 where is_deleted = 1 and name in ('{0}');`,
  unitBatchInsert: `insert into units(name, pinyin, fingerprint) VALUES {0};`,
  unitDeleteRecovery: `update units set is_synced=0, is_deleted=0 where is_deleted = 1 and id = '{0}';`,

  getTypes:
    `select {columns} from types
    {wheres}
    {orderBys}
    {limit};`,
  getUnits:
    `select {columns} from units
    {wheres}
    {orderBys}
    {limit};`,
  goodsIfExists:
    `with tmp as (
      {0}
    ), tmp1 as (
      select barcode.ext_barcode, barcode.good_fingerprint
      from goods_ext_barcode as barcode
      inner join tmp
      on barcode.ext_barcode = tmp.code
      where barcode.is_deleted = 0
    )
    select {1}
    from goods
    left join tmp
    on goods.code = tmp.code
    left join tmp1
    on goods.fingerprint = tmp1.good_fingerprint
    where goods.is_deleted = 0
    and (
      tmp.code is not null
      or
      tmp1.good_fingerprint is not null
    );`,
  goodsBatchInsert:
    `insert into goods(name, first_letters, pinyin, code, sale_price, vip_price, type_fingerprint, unit_fingerprint, fingerprint)
    values ('{name}', '{firstLetters}', '', '{code}', {salePrice}, {vipPrice}, '{typeFingerprint}', '{unitFingerprint}', '{fingerprint}');
    insert into goods_attributes(is_sale, type, manufacture_date, fingerprint, create_by, revise_by)
    values (1, 'goods', {manufactureDate}, '{fingerprint}', '{uid}', '{uid}');`,

  /**
   * 查询包含此分类的二级分类
   */
  typeIdAndTwoTypeId: `select fingerprint from types WHERE fingerprint in ('{fingerprint}') or parent_fingerprint in ('{fingerprint}')`,
  typeSearch:
    `select id, name, icon, pinyin, sortno, is_deleted, is_synced,
      parent_fingerprint, fingerprint, create_at, revise_at, sync_at
    from types
    where is_deleted=0
    order by case when sortno is null then 999 else sortno end, id;`,
  typeInsertExist: `select id,name from types where name in ('{name}') and is_deleted=0`,
  typeInsertExistSelect: `select id,name,fingerprint,is_deleted from types where name in ('{name}') or fingerprint='{fingerprint}';`,
  typeUpdateExist: `select id,name from types where is_deleted=0 and id!={id} and name='{name}' `,
  typeInsert:
    `insert into types(name, pinyin, icon, parent_fingerprint, fingerprint) values
    ('{name}','{pinyin}','{icon}','{parentFingerprint}','{fingerprint}');`,
  typeBatchSelect: `select id, name from types;`,
  typeBatchUpdate: `update types set is_synced=0, is_deleted=0 where is_deleted = 1 and parent_id !=0 and name in ('{0}');`,
  typeBatchInsert: `insert into types(parent_id, name, pinyin, fingerprint) VALUES {0};`,
  typeUpdate: `update types set name='{name}', pinyin='{pinyin}', parent_fingerprint='{parentFingerprint}', is_synced=0 where id={id}`,
  typeUpdateParent: `
  update types set name='{name}', pinyin='{pinyin}', is_synced=0 where id={id};
  update types set parent_fingerprint='{fingerprint}', is_synced=0 where parent_fingerprint='{parentFingerprint}';
  `,
  typeDel: `update types set is_synced=0,is_deleted=1, revise_at=datetime('now','localtime') where parent_fingerprint in ('{fingerprint}') or fingerprint in ('{fingerprint}');`,
  typeUse: `select count(*) as cnt from goods where is_deleted=0 and type_fingerprint in ('{typeFingerprint}')`,
  typeOrderByUpdate: `update types set sortno={sortno}, is_synced=0, revise_at=datetime('now','localtime') where id={id};`,
  typeDeleteRecovery: `update types set is_synced=0, is_deleted=0 where is_deleted = 1 and id = '{0}';`,
  typeDelCheck: `select id, name from types where fingerprint = '{fingerprint}' and is_deleted = 0`,
  getImagesByFingerprints: `select * from images where fingerprint in ({0});`,
  goodsSelectWheres: `select {columns} from goods {wheres} {orderBy} {limit};`,
  getGoodsByFingerprints: `select * from goods where fingerprint in ({0});`,
  getGoodsByCodesAndFingerprints: `select * from goods where code in ({0}) or fingerprint in ({1});`,
  goods_unit: ` a.unit_fingerprint in (select fingerprint from units where id in (20,21,29,31)) and `,
  goods_nounit: ' a.type_fingerprint in (select fingerprint from types where id in ({type})) and',
  goods_nounit_del: ' a.type_fingerprint in (select fingerprint from types) and ',
  goodsSearch:
    `with tmp_suppliers as (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
      ,s.addr as supplier_addr,s.remark as supplier_remark
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    )
    ,tmpGoodIds as (
       select distinct a.id,a.fingerprint 
       from goods a
       LEFT join goods_attributes ga on a.fingerprint = ga.fingerprint
       LEFT JOIN goods_ext_barcode 
        on a.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        left join tmp_suppliers on tmp_suppliers.fingerprint=a.fingerprint
       WHERE {wheres}
       {supplierWhere}
       order by {orderBys}
      limit {limit} offset {offset}
     )
     select a.id, a.major_code,a.name,b.id as type_id, ifnull(bb.name || ' / ', '') || b.name as type_name, a.first_letters, a.pinyin,
     a.code, ifnull(nullif(a.image,''),b.icon) image, a.pur_price, a.sale_price,
     a.vip_price, IFNULL(ROUND(a.cur_stock,3), 0) as cur_stock, a.init_stock, a.init_price, a.init_amt,
     round(a.min_stock, 3) as min_stock, round(a.max_stock, 3) as max_stock,
     a.has_image, a.remark, a.packing, a.specs,ga.manufacture_date,ga.expiry_days,
     a.is_vip_disc, a.is_deleted, a.is_new, a.is_synced,
     a.type_fingerprint, a.unit_fingerprint, a.fingerprint, a.create_at,
     a.revise_at, a.sync_at, c.url, c.local_url, c.fingerprint as img_fingerprint,
     e.name as unit_name, p.is_sendscale as isBarcodeScalesGoods, p.model as weighModelVal, p.expire_date, p.tare,
     case when a.max_stock = 0 and a.min_stock = 0 then ''
          when a.cur_stock <= a.max_stock and a.cur_stock >= a.min_stock then '正常'
          when a.cur_stock<a.min_stock then '不足'
          when a.cur_stock>a.max_stock then '过剩'
     else '' end as stockStatus
     , ga.sort
     ,supplier_id,supplier_fingerprint,supplier_name,supplier_contacter,supplier_mobile,supplier_addr,supplier_remark
     from goods a
     left join product_scale p on a.fingerprint = p.good_fingerprint
     left join types b on a.type_fingerprint = b.fingerprint
     left join types bb on b.parent_fingerprint = bb.fingerprint and b.is_deleted = 0
     left join goods_attributes ga on a.fingerprint = ga.fingerprint and ga.is_deleted =0
     left join (
        select * from images where id in (
            select max(id) as id from images where is_deleted= 0 and good_fingerprint in (
                select fingerprint from tmpGoodIds
            ) group by good_fingerprint
        )
     ) c
     on a.fingerprint = c.good_fingerprint and c.is_deleted = 0
     left join units e ON a.unit_fingerprint = e.fingerprint
     and e.is_deleted = 0
     left join tmp_suppliers on tmp_suppliers.fingerprint=a.fingerprint
     where a.id in (select id from tmpGoodIds)
     order by {orderBys};`,
  goodsSearch2:
    `with tmp_suppliers as (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
      ,s.addr as supplier_addr,s.remark as supplier_remark
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    )
    ,tmpGoodIds as (
        select distinct a.id,a.fingerprint 
        from goods a
        LEFT join goods_attributes ga on a.fingerprint = ga.fingerprint
        LEFT JOIN goods_ext_barcode 
        on a.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        left join tmp_suppliers on tmp_suppliers.fingerprint=a.fingerprint
        WHERE {wheres}
        {supplierWhere}
        order by {orderBys}
        limit {limit} offset {offset}
      )
      select a.id, a.major_code,a.name,b.id as type_id, ifnull(bb.name || ' / ', '') || b.name as type_name, a.first_letters, a.pinyin,
      a.code, ifnull(nullif(a.image,''),b.icon) image, a.pur_price, a.sale_price,
      a.vip_price, IFNULL(ROUND(a.cur_stock,3), 0) as cur_stock, a.init_stock, a.init_price, a.init_amt,
      round(a.min_stock, 3) as min_stock, round(a.max_stock, 3) as max_stock,
      a.has_image, a.remark, a.packing, a.specs,ga.manufacture_date,ga.expiry_days,
      a.is_vip_disc, a.is_deleted, a.is_new, a.is_synced,
      a.type_fingerprint, a.unit_fingerprint, a.fingerprint, a.create_at,
      a.revise_at, a.sync_at, c.url, c.local_url, c.fingerprint as img_fingerprint,
      e.name as unit_name, 0 as isBarcodeScalesGoods, 0 as weighModelVal, 0 as expire_date, 0 as tare,
      case when a.max_stock = 0 and a.min_stock = 0 then ''
          when a.cur_stock <= a.max_stock and a.cur_stock >= a.min_stock then '正常'
          when a.cur_stock<a.min_stock then '不足'
          when a.cur_stock>a.max_stock then '过剩'
      else '' end as stockStatus
      , ga.sort
      ,supplier_id,supplier_fingerprint,supplier_name,supplier_contacter,supplier_mobile,supplier_addr,supplier_remark
      from goods a
      left join types b on a.type_fingerprint = b.fingerprint
      left join types bb on b.parent_fingerprint = bb.fingerprint and b.is_deleted = 0
      left join goods_attributes ga on a.fingerprint = ga.fingerprint and ga.is_deleted =0
      left join (
         select * from images where id in (
             select max(id) as id from images where is_deleted= 0 and good_fingerprint in (
                 select fingerprint from tmpGoodIds
             ) group by good_fingerprint
         )
      ) c
      on a.fingerprint = c.good_fingerprint and c.is_deleted = 0
      left join units e ON a.unit_fingerprint = e.fingerprint
      and e.is_deleted = 0
      left join tmp_suppliers on tmp_suppliers.fingerprint=a.fingerprint
      where a.id in (select id from tmpGoodIds)
      order by {orderBys};`,
  goodSearchWithSuppliers:
    `with tmpGoodIds as (
        select distinct a.id,a.fingerprint 
        from goods a
        LEFT JOIN goods_ext_barcode 
        on a.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        WHERE a.is_deleted=0 {wheres}
        order by {orderBys}
      limit {limit} offset {offset}
      )
      select a.id, a.name,b.id as type_id, ifnull(bb.name || ' / ', '') || b.name as type_name, a.first_letters, a.pinyin,
      a.major_code, a.code, a.image, a.pur_price, a.sale_price,
      a.vip_price, IFNULL(a.cur_stock, 0) as cur_stock, a.init_stock, a.init_price, a.init_amt,
      a.min_stock, a.max_stock, a.has_image, a.remark, a.packing, a.specs,ga.manufacture_date,ga.expiry_days,
      a.is_vip_disc, a.is_deleted, a.is_new, a.is_synced,
      a.type_fingerprint, a.unit_fingerprint, a.fingerprint, a.create_at,
      a.revise_at, a.sync_at, c.url, c.local_url, c.fingerprint as img_fingerprint,
      e.name as unit_name,s.fingerprint as supplier_fingerprint from goods a
      left join types b on a.type_fingerprint = b.fingerprint
      left join types bb on b.parent_fingerprint = bb.fingerprint
      and b.is_deleted = 0
      left join (
        select * from images where id in (
            select max(id) as id from images where is_deleted= 0 and good_fingerprint in (
                select fingerprint from tmpGoodIds
            ) group by good_fingerprint
        )
      ) c
      on a.fingerprint = c.good_fingerprint and c.is_deleted = 0
      left join units e ON a.unit_fingerprint = e.fingerprint
      and e.is_deleted = 0
      left join (select max(id) as id,good_fingerprint,pur_fingerprint from purchase_items GROUP BY good_fingerprint) pi ON pi.good_fingerprint = a.fingerprint
      left join (select max(create_at) as create_at,supplier_fingerprint,fingerprint from purchases where is_deleted = 0 GROUP BY supplier_fingerprint ) p ON pi.pur_fingerprint = p.fingerprint
      left join suppliers s ON s.fingerprint = p.supplier_fingerprint
      left join goods_attributes ga on a.fingerprint = ga.fingerprint and ga.is_deleted =0
      where a.id in (select id from tmpGoodIds)
      order by {orderBys};`,
  oneGoods: `with tmpGoodIds as (
    SELECT id,fingerprint from goods where major_code in ('{majorCode}') and is_deleted=0
    )
    select a.id, a.name,b.id as type_id, ifnull(bb.name || ' / ', '') || b.name as type_name, a.first_letters, a.pinyin,
         a.major_code, a.code, a.image, a.pur_price, a.sale_price,
         a.vip_price, a.cur_stock, a.init_stock, a.init_price, a.init_amt,
         a.min_stock, a.max_stock, a.has_image, a.remark, a.packing, a.specs,
         a.is_vip_disc, a.is_deleted, a.is_new, a.is_synced,
         a.type_fingerprint, a.unit_fingerprint, a.fingerprint, a.create_at,
         a.revise_at, a.sync_at, c.id as images_id, c.url, c.local_url, c.fingerprint as img_fingerprint, e.name as unit_name
         from goods a
         left join types b on a.type_fingerprint = b.fingerprint
         left join types bb on b.parent_fingerprint = bb.fingerprint
         and b.is_deleted = 0
         left join (
            select * from images where id in (
                select max(id) as id from images where is_deleted= 0 and good_fingerprint in (
                    select fingerprint from tmpGoodIds
                ) group by good_fingerprint
            )
         ) c
         on a.fingerprint = c.good_fingerprint and c.is_deleted = 0
         left join units e ON a.unit_fingerprint = e.fingerprint
         and e.is_deleted = 0
         WHERE
         a.is_deleted = 0 and
         a.id in( select id from tmpGoodIds );
         `,
  goodsGroupSearch: `
  with tmpGoodIds as (
    SELECT distinct id,fingerprint
    from goods
  )
    select a.id, a.name,b.id as type_id, ifnull(bb.name || ' / ', '') || b.name as type_name, a.first_letters, a.pinyin,
         a.major_code, a.code, a.image, a.pur_price, a.sale_price,
         a.vip_price, a.cur_stock, a.init_stock, a.init_price, a.init_amt,
         a.min_stock, a.max_stock, a.has_image, a.remark, a.packing, a.specs,
         a.is_vip_disc, a.is_deleted, a.is_new, a.is_synced,ga.manufacture_date,ga.expiry_days,
         a.type_fingerprint, a.unit_fingerprint, a.fingerprint, a.create_at,
         a.revise_at, a.sync_at, c.url, c.local_url, c.fingerprint as img_fingerprint,
         e.name as unit_name from goods a
         left join types b on a.type_fingerprint = b.fingerprint
         left join types bb on b.parent_fingerprint = bb.fingerprint and b.is_deleted = 0
         left join goods_attributes ga on a.fingerprint = ga.fingerprint and ga.is_deleted =0
         left join (
            select * from images where id in (
                select max(id) as id from images where is_deleted= 0 and good_fingerprint in (
                    select fingerprint from tmpGoodIds
                ) group by good_fingerprint
            )
         ) c
         on a.fingerprint = c.good_fingerprint and c.is_deleted = 0
         left join units e ON a.unit_fingerprint = e.fingerprint
         and e.is_deleted = 0
         WHERE
         a.id in( select id from tmpGoodIds )
         and a.is_deleted = 0 {wheres}
         order by {orderBys};
         `,
  goodsSearchCnt:
    ` select count(distinct a.id) as cnt
      from goods a
      LEFT JOIN goods_ext_barcode 
      on a.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      left join (
        select gs.fingerprint
        ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
        ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
        from goods_suppliers gs
        inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
        where gs.is_del=0 and s.is_deleted=0
      ) tmp_suppliers on tmp_suppliers.fingerprint = a.fingerprint
      WHERE a.is_deleted = 0 {wheres}
      {supplierWhere}`,
  goods_hot:
    `select p.id, p.major_code,p.name, p.first_letters, p.pinyin
    ,p.code, ifnull(nullif(p.image,''),pt.icon) image, p.pur_price, p.sale_price
    ,p.vip_price, IFNULL(ROUND(p.cur_stock,3), 0) as cur_stock, p.init_stock, p.init_price, p.init_amt
    ,round(p.min_stock, 3) as min_stock, round(p.max_stock, 3) as max_stock
    ,p.has_image, p.remark, p.packing, p.specs
    ,p.is_vip_disc, p.is_deleted, p.is_new, p.is_synced
    ,p.type_fingerprint, p.unit_fingerprint, p.fingerprint, p.create_at
    ,p.revise_at, p.sync_at
    ,case when p.max_stock = 0 and p.min_stock = 0 then ''
        when p.cur_stock <= p.max_stock and p.cur_stock >= p.min_stock then '正常'
        when p.cur_stock<p.min_stock then '不足'
        when p.cur_stock>p.max_stock then '过剩'
    else '' end as stockStatus
    ,pt.id as type_id, ifnull(ptt.name || ' / ', '') || pt.name as type_name
    ,u.name as unit_name
    ,pi.url, pi.local_url, pi.fingerprint as img_fingerprint
    ,ga.manufacture_date,ga.expiry_days,ga.sort
    ,0 as isBarcodeScalesGoods, 0 as weighModelVal, 0 as expire_date, 0 as tare
    from sales s
    inner join sale_items si on s.fingerprint = si.sale_fingerprint
    left join goods p on p.fingerprint = si.good_fingerprint
    left join goods_attributes ga on p.fingerprint = ga.fingerprint and ga.is_deleted =0
    left join images pi on p.fingerprint = pi.good_fingerprint
    left join types pt on p.type_fingerprint = pt.fingerprint
    left join types ptt on pt.parent_fingerprint = ptt.fingerprint
    left join units u on p.unit_fingerprint = u.fingerprint
    where s.is_deleted = 0
    and p.is_deleted = 0
    and s.opt_date >= date('now','localtime','-1 month','+1 day')
    and s.opt_date <= datetime('now','localtime')
    group by si.good_fingerprint
    order by sum(si.qty) desc
    limit 30`,
  good_update_exist_byid: 'select 1 no from goods where is_deleted=0 and id={id}',
  good_update_exist_byname: "select 1 no from goods where id!={id} and name='{name}' ",
  good_update_exist_bycode: "select 1 no from goods where id!={id} and (name='{name}' or code='{code}')",
  good_insert_exist_byname: "select name from goods where (name='{name}' or fingerprint='{fingerprint}') ",
  good_insert_exist_bycode: "select name from goods where (name='{name}' or fingerprint='{fingerprint}' or code='{code}')",
  goodsUpdateCode:
    `update goods set code='{code}', is_synced=0, revise_at=datetime('now','localtime') where id={id};`,
  goodInsertExist:
    `with tmpGoodIds as (
      select distinct a.id, a.fingerprint,
        case when a.name = '{name}' then '商品名重复'
          when a.fingerprint = '{fingerprint}' then '商品唯一标识码重复'
          else '商品条码重复'
        end as errMsg
      from goods a
			left join goods_ext_barcode as geb
			on geb.good_fingerprint = a.fingerprint
			and geb.is_deleted = 0
      where a.is_deleted = 0
      and (a.name = '{name}' or a.fingerprint = '{fingerprint}' or (a.code = '{code}' and a.code != '') or
			(geb.ext_barcode = '{code}' and geb.ext_barcode != ''))
    ), tmp_image_url as (
      select img.id, img.url, img.local_url, img.good_fingerprint, img.fingerprint
      from images as img
      inner join tmpGoodIds as tmp
      on img.good_fingerprint = tmp.fingerprint
      where img.is_deleted= 0
      order by img.id limit 1
    ), tmp_types as (
      select * from types where is_deleted=0
    )
    select
      a.id, a.name, a.code, a.unit_fingerprint, e.name as unit_name, a.type_fingerprint,
      ifnull(ff.name || ' / ', '') || f.name as type_name, a.pur_price, a.sale_price,
      a.pinyin, a.init_price, a.init_stock, a.init_amt, a.vip_price, a.cur_stock, a.min_stock, a.max_stock, a.has_image,
      c.url, c.local_url, c.fingerprint as img_fingerprint, a.fingerprint,
      b.errMsg, case when a.is_deleted = 0 then '正常' else '已删除' end status, a.first_letters
    from goods a
    inner join tmpGoodIds b on a.id = b.id
    left join tmp_image_url c on a.fingerprint = c.good_fingerprint
    left join units e on a.unit_fingerprint = e.fingerprint and e.is_deleted = 0
    left join tmp_types f on a.type_fingerprint = f.fingerprint
    left join tmp_types ff on f.parent_fingerprint = ff.fingerprint
    order by a.id;`,
  checkGoodCodeAndGoodExtBarcode:
    `with tmp_goods AS(
    SELECT goods.id,goods.name,goods.cur_stock,goods.code,goods.fingerprint,CASE WHEN ext.id <> null and ext.is_deleted THEN 0 ELSE 1 END AS is_del
    FROM goods
    LEFT JOIN goods_ext_barcode ext ON goods.fingerprint=ext.good_fingerprint
    WHERE goods.is_deleted = 0
    AND goods.code = '{code}'
  )
  ,tmp_goods_ext_barcode AS(
    SELECT id,'' AS name,0 AS cur_stock,ext_barcode AS code,good_fingerprint AS fingerprint, 0 AS is_del FROM goods_ext_barcode
    WHERE is_deleted = 0
    AND ext_barcode = '{code}'
  )
  ,tmp_result AS(
    SELECT id,name,cur_stock,code,fingerprint,is_del,1 as isGoods FROM tmp_goods
    union all
    SELECT id,name,cur_stock,code,fingerprint,is_del,0 as isGoods FROM tmp_goods_ext_barcode
  )
  SELECT max(id) AS id,max(name) AS name,max(cur_stock) AS curStock,code,fingerprint,max(is_del) AS isDel,isGoods 
  FROM tmp_result 
  GROUP BY code,fingerprint
  ORDER BY is_del;
  `,
  good_nocode: `select id from goods where (code is null or trim(code) = '');`,
  goodExistById: `select * from goods where fingerprint in ('{0}') and is_deleted=0;`,
  generateMajorCode:
    `with tmp as(
      select distinct substr(major_code, 6) + 0 as account
      from goods
      where substr(major_code, 1, 5) = '{yy}'||substr('000'||'{deviceCode}', -3, 3)
      and length(major_code) = 10
    )
    select case when not exists (select * from tmp where account = 1) then 1 else min(account) + 1 end as orderNo
    from tmp
    where not exists (select * from tmp as tmp1 where tmp1.account = tmp.account + 1);`,
  generateMadeCode:
    `with tmp as(
      select distinct substr(code, 6) + 0 as account
      from goods
      where substr(code, 1, 5) = '{yy}'||substr('000'||'{deviceCode}', -3, 3)
      and length(code) = 10
    )
    select case when not exists (select * from tmp where account = 1) then 1 else min(account) + 1 end as orderNo
    from tmp
    where not exists (select * from tmp as tmp1 where tmp1.account = tmp.account + 1);`,
  generateCodes:
    `with recursive orders(i) as (
      select 1
      union all
      select i+1 from orders
      limit 999
    )
    select orders.i as orderNo
    from orders
    left join goods
    on '{majorCode}' || substr('000'||orders.i, -3) = goods.code
    and goods.is_deleted=0
    where goods.id is null
    and '{majorCode}' || substr('000'||orders.i, -3) not in ({codes})
    order by orders.i limit {count};`,
  getAllExistCodes:
    `select code from goods where is_deleted=0 and length(code)=13 and substr(code, 1, 6)='{prefix}';`,
  getBatchImportAllExistCodes:
    `select code from goods where is_deleted=0 and length(code)=13 and substr(code, 1, 6)='{prefix}'
    union
    select code from import_goods where length(code)=13 and substr(code, 1, 6)='{prefix}';`,
  updateImportGoodsCode: `update import_goods set code='{code}' where id={id};`,
  updateImportGoodsErrMsg: `update import_goods set err_msg = err_msg || '{errMsg}' where id={id};`,
  importGoodsTruncate:
    `delete from import_goods;
    update sqlite_sequence set seq = 0 where name = 'import_goods';`,
  importGoodsInsert:
    `insert into import_goods(id, major_code, code, name, pinyin, first_letters, type, sub_type, unit, brand, season, sex,
      composition, level, specs1, specs2, specs3, code, sale_prc, vip_prc, pur_prc, init_prc, 
      cur_stock, manufacture_date, expiry_days, err_msg) values (
        {id}, {majorCode}, {code}, {name}, {pinyin}, {firstLetters}, {type}, {subType}, {unit}, {brand}, {season}, {sex}, {composition},
        {level}, {specs1}, {specs2}, {specs3}, {code}, {salePrc}, {vipPrc}, {purPrc}, {initPrc}, 
        {curStock}, {manufactureDate}, {expiryDays}, {errMsg}
      );`,
  importGoodsUpdateErrMsgName:
    `with tmp as (
      select distinct name from goods where is_deleted=0
    )
    update import_goods set err_msg = err_msg || '商品名称已存在；'
    where name in (select name from tmp);`,
  importGoodsUpdateErrMsgCode:
    `with tmp as (
      select code from goods where is_deleted=0
      union all
      select ext_barcode from goods_ext_barcode where is_deleted = 0    
    )
    update import_goods set err_msg = err_msg || '条码已存在；'
    where code in (select code from tmp);`,
  getImportGoods:
    `select id, major_code, name, pinyin, first_letters, type, sub_type, unit, code,
      sale_prc, vip_prc, pur_prc, init_prc, cur_stock , manufacture_date, expiry_days, err_msg
    from import_goods order by id;`,
  goodUpdateSalePrice:
    `update goods set
      sale_price=round({salePrice}, 2),
      is_synced=0
    where id={id}
    and round(sale_price, 2)!=round({salePrice}, 2);`,
  getGoodsByWheres:
    `select * from goods where 1=1 {wheres};`,
  getGoodsJoinProductScaleByWheres:
    ` select goods.*,ps.is_sendscale 
      from goods 
      LEFT JOIN product_scale ps on ps.good_fingerprint = goods.fingerprint
      where  1=1 {wheres};`,
  getDeleteGoodsByNameOrCode:
  ` select goods.fingerprint 
    from goods left join goods_ext_barcode geb on goods.fingerprint = geb.good_fingerprint 
    WHERE geb.ext_barcode in ('{code}') and goods.is_deleted = 1`,
  getGoodsCodeByWheres:
    ` select code,is_deleted from goods where 1=1 {wheres}
      union all
      select ext_barcode,is_deleted from goods_ext_barcode where 1=1 {wheres1}`,
  getGoodsChkDupMajorCode:
    `with tmpGoods as (
      select id, major_code from goods where is_deleted=0 and major_code='{1}'
    )
    select id, major_code from tmpGoods
    where id not in (
      select id from tmpGoods
      where major_code in (
        select major_code from tmpGoods
        where id={0}
      )
    );`,
  getGoodsChkDupCode:
    `select * from goods
    where is_deleted=0
    and code in ({codes})
    and id not in ({ids});`,
  getGoodsInfo1ById: `select info1 from goods where is_deleted=0 and id = {0}`,
  imagesInsert:
    `insert into images(url, local_url, "order", good_fingerprint, fingerprint) values `,
  imagesInsertValues:
    `({url}, {localUrl}, {order}, {goodFingerprint}, {fingerprint})`,
  imagesUpdate:
    `update images set url={url}, local_url={localUrl}, is_synced=0, revise_at=datetime('now','localtime') where id={id};`,
  goodsInsert:
    `insert into goods(name, first_letters, pinyin, major_code, code, image, pur_price, sale_price, vip_price,
    cur_stock, init_stock, init_price, init_amt, min_stock, max_stock, has_image, remark, packing, specs, is_vip_disc,
    type_fingerprint, unit_fingerprint, fingerprint) values `,
  goodsInsertValues:
    `({name},{firstLetters},{pinyin},null,{code},{image},round({purPrice}, 6),round({salePrice}, 2),round({vipPrice}, 2),
    round({curStock}, 3),round({initStock}, 3),round({initPrice}, 2),round({initAmt}, 2),round({minStock}, 3),round({maxStock}, 3),
    {hasImage},{remark},{packing},{specs},{isVipDisc},{typeFingerprint},{unitFingerprint},{fingerprint})`,
  goodsExtBarcodeInsert:
    `
  INSERT INTO "goods_ext_barcode"("good_fingerprint", "fingerprint", "ext_barcode") VALUES 
  `,
  goodsExtBarcodeInsertValues:
    `
  ('{goodFingerprint}', '{fingerprint}', '{extBarcode}')
  `,
  goodsExtBarcodeSearchByGoodsFingerprint:
    `
  SELECT good_fingerprint AS goodsFingerprint,ext_barcode AS extBarcode,fingerprint FROM goods_ext_barcode
  WHERE is_deleted=0 AND good_fingerprint in({0});
  `,
  goodsExtBarcodeUpdateCode:
    `
  UPDATE goods_ext_barcode
  SET ext_barcode='{extBarcode}',is_synced=0,revise_at=datetime('now','localtime')
  WHERE fingerprint='{fingerprint}';
  `,
  goodsExtBarcodeUpdateIsDeleted:
    `
  UPDATE goods_ext_barcode
  SET is_deleted=1,is_synced=0,revise_at=datetime('now','localtime')
  WHERE fingerprint='{fingerprint}';
  `,
  getGoodsAttributesCountByFingerprint: `select count(1) from goods_attributes where type={type} and fingerprint = {fingerprint}`,
  goodsAttributesInsert:
    `insert into goods_attributes
    (is_sale, "type", sort, manufacture_date, expiry_days, info1,
    sale_count, sale_time, options, is_set_sale, is_stock_management, is_valuate, fingerprint, is_deleted) values `,
  goodsAttributesInsertValues:
    `(1,'goods',1,{manufactureDate},{expiryDays},'','',
    '','',0,0,0,{fingerprint},0)`,
  goodsUpdate:
    `update goods set name={name}, first_letters={firstLetters}, pinyin={pinyin}, major_code={majorCode}, code={code}, image={image},
      pur_price=round({purPrice}, 6), sale_price=round({salePrice}, 2), vip_price=round({vipPrice}, 2), cur_stock=round({curStock}, 3),
      init_stock=round({initStock}, 3), init_price=round({initPrice}, 2), init_amt=round({initAmt}, 2), min_stock=round({minStock}, 3),
      max_stock=round({maxStock}, 3), has_image={hasImage}, remark={remark}, packing={packing}, specs={specs}, is_vip_disc={isVipDisc},
      type_fingerprint={typeFingerprint}, unit_fingerprint={unitFingerprint},
      is_synced=0, revise_at=datetime('now','localtime')
      where id={id};
      insert  into  goods_attributes
						  (is_sale, "type", sort, manufacture_date, expiry_days, info1,
						  sale_count, sale_time, options, is_set_sale, is_stock_management, is_valuate, fingerprint, is_deleted) 
			        values (1,'goods',1,{manufactureDate},{expiryDays},'','','','',0,0,0,{fingerprint},0)
      on CONFLICT(fingerprint) do
      update  set manufacture_date ={manufactureDate}, expiry_days ={expiryDays}, is_synced=0, revise_at=datetime('now','localtime');`,
  goodsDelete:
    `update goods set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and fingerprint in ({0});
    update goods_attributes set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and fingerprint in ({0});
    update images set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and good_fingerprint in ({0});
    update goods_ext_barcode set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and good_fingerprint in ({0});
    update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_del=0 and fingerprint in ({0});`,
  updateExpiryDaysManufactureDate:
    `update goods_attributes set 
    revise_by= {reviseBy},
    manufacture_date= '{manufactureDate}', 
    expiry_days= {expiryDays},  
    is_synced=0, 
    revise_at=datetime('now','localtime') 
    where fingerprint = '{fingerprint}'`,
  // 删除称重类别
  goodsDelByWeighingTypes: `update goods set is_synced=0, is_deleted=1, revise_at=datetime('now','localtime')  where
                            unit_fingerprint in (
                                  select fingerprint from units where name in ('千克', '克', '斤', '公斤', '两', 'g', 'G', 'kg', 'Kg', 'kG', 'KG')
                            );`,
  goodsDelByTypes:
  ` update goods set is_synced=0, is_deleted=1, revise_at=datetime('now','localtime') where type_fingerprint in ('{fingerprint}');
    update goods_attributes set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and fingerprint in (select fingerprint from goods where type_fingerprint in ('{fingerprint}'));
    update images set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and good_fingerprint in (select fingerprint from goods where type_fingerprint in ('{fingerprint}'));
    update goods_ext_barcode set is_deleted=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_deleted=0 and good_fingerprint in (select fingerprint from goods where type_fingerprint in ('{fingerprint}'));
    update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime')
    where is_del=0 and fingerprint in (select fingerprint from goods where type_fingerprint in ('{fingerprint}'));`,
  good_use:
    `with tmp as (select fingerprint from goods where id in ({id}))
    select distinct 1 from sale_items where good_fingerprint in (select fingerprint from tmp) and is_deleted=0 union
    select 1 from purchase_items where good_fingerprint in (select fingerprint from tmp) and is_deleted=0 union
    select 1 from inventory_items where good_fingerprint in (select fingerprint from tmp) and is_deleted=0`,
  goodsExchangeSearch: 'select name, fingerprint from goods where is_deleted=0 {0};',
  goods_stockStatisticsReports:
    `select goods.name, goods.code, goods.cur_stock, goods.fingerprint,GROUP_CONCAT(goods_ext_barcode.ext_barcode) as ext_barcode
     from goods
     left join goods_ext_barcode 
     on goods_ext_barcode.good_fingerprint = goods.fingerprint 
     and goods_ext_barcode.is_deleted = 0
     where goods.fingerprint in ({0})
     GROUP BY goods.fingerprint;`,
  good_batch_put:
    `with tmp_put_values as (
      {0}
    )
    update goods set
        pinyin = (select pinyin from tmp_put_values b where goods.name = b.name),
        code = (select code from tmp_put_values b where goods.name = b.name),
        unit_fingerprint = (select unit_fingerprint from tmp_put_values b where goods.name = b.name),
        type_fingerprint = (select type_fingerprint from tmp_put_values b where goods.name = b.name),
        pur_price = (select pur_prc from tmp_put_values b where goods.name = b.name),
        sale_price = (select sale_prc from tmp_put_values b where goods.name = b.name),
        vip_price = (select trade_prc from tmp_put_values b where goods.name = b.name),
        cur_stock = (select round(cur_stock, 3) from tmp_put_values b where goods.name = b.name),
        min_stock = (select round(min_stock, 3) from tmp_put_values b where goods.name = b.name),
        first_letters = (select first_letters from tmp_put_values b where goods.name = b.name),
        is_deleted = 0,
        is_synced = 0
      where name = (select name from tmp_put_values b where goods.name = b.name)
        and name not in (select name from (select a.name, count(a.name) as count from goods a inner join tmp_put_values b on a.name = b.name group by a.name) where count >1)
        and code not in (select code from (select a.code, count(a.code) as count from goods a inner join tmp_put_values b
        on a.code = b.code where a.code != '' group by a.code) where count >1)
        and code not in (select a.code from goods a inner join tmp_put_values b on a.code = b.code where a.name != b.name and a.code !='')
        and is_deleted = 1;

      with tmp_put_values as (
        {0}
      ), tmp_put_select as (
        {1}
      )

      select distinct err.name,pinyin,code,unit,type,pur_prc, insert into goods(name, pinyin, code, unit_fingerprint, type_fingerprint, pur_price, sale_price, vip_price, cur_stock, min_stock, fingerprint, first_letters)
      select name, pinyin, code, unit_fingerprint, type_fingerprint, pur_prc, sale_prc, trade_prc, round(cur_stock, 3), round(min_stock, 3), fingerprint, first_letters
      from tmp_put_values where fingerprint not in
      (select fingerprint from goods) and name not in (select name from goods) and code not in(select code from goods where code != '' and code is not null);

      with tmp_put_values as (
        {0}
      ), tmp_put_select as (
        {1}
      ), tmp_result as (
        select * from tmp_put_select where name not in (select name from goods where id between {2} and (select max(id) from goods)) and name not in (select name from ({3}))
        union all
        select a.* from tmp_put_select a where a.code in (select a.code from goods a inner join tmp_put_values b on a.code = b.code where a.name != b.name and a.code !='')
        union all
        select a.* from tmp_put_select a where a.name in (select name from
          (select a.name,count(a.name) as count from goods a inner join tmp_put_values b on a.name = b.name group by a.name) where count>1)
      )sale_prc,trade_prc,cur_stock,min_stock,fingerprint
        ,case when exists(select code from goods where code = err.code and err.code != '') then '商品条码重复;'
          when exists(select name from goods where name = err.name) then '商品名重复;'
          when exists(select fingerprint from goods where fingerprint = err.fingerprint) then '商品唯一标识码重复;'
          else '此商品名或条码已经添加过，请勿重复添加;' end as err
      from tmp_result err`,
  good_batch_select:
    `select * from goods
    where cur_stock != 0
    and name in (
      select name from ({0})
      where name in (select name from goods where id between {1} and (select max(id) from goods))
      or name in (select name from ({2}))
    );`,
  good_batch_select_deleted: `select * from ({1}) where cur_stock != 0 and name in (select name from ({0}));`,
  good_batch_put_values_deleted:
    `select '{id}' as id, '{name}' as name, '{pinyin}' as  pinyin, '{code}' as code,
    '{unit_fingerprint}' as unit_fingerprint, '{type_fingerprint}' as type_fingerprint,
    '{pur_price}' as pur_price, '{sale_price}' as sale_price, -'{cur_stock}' as cur_stock,
    '{fingerprint}' as fingerprint, '{first_letters}' as first_letters union all`,
  good_batch_put_values:
    `select '{name}' as name,'{pinyin}' as pinyin,'{code}' as code,'{unit_fingerprint}' as unit_fingerprint,
    '{type_fingerprint}' as type_fingerprint, '{pur_prc}' as pur_prc,'{sale_prc}' as sale_prc,'{trade_prc}' as trade_prc,
    round({cur_stock}, 3) as cur_stock, 0 as min_stock, '{fingerprint}' as fingerprint, '{first_letters}' as first_letters union all `,
  good_batch_put_select_values:
    `select '{name}' as name, '{pinyin}' as pinyin, '{code}' as code, '{unit_fingerprint}' as unit_fingerprint, '{unit}' as unit,
      '{type_fingerprint}' as type_fingerprint, '{type}' as type, '{pur_prc}' as pur_prc, '{sale_prc}' as sale_prc, '{trade_prc}' as trade_prc,
      round({cur_stock}, 3) as cur_stock, 0 as min_stock, '{fingerprint}' as fingerprint, '{first_letters}' as first_letters union all `,
  good_batch_new:
    `select name,code
    from goods
    where create_at = (select max(create_at) from goods);`,
  good_batch_max_id: `select max(id) as id from goods`,
  updateGoodsPackingByFingerprint: `update goods set packing={packing}, is_synced=0 where fingerprint={fingerprint};`,
  getGoodsAfterUpdateStockWheres:
    `select id, name, pur_price, cur_stock, {qty} as qty, cur_stock - {qty} as leftStock, is_deleted, packing, fingerprint
    from goods where id = {id} and cur_stock < {qty};`,
  getGoodsAfterUpdateStock:
    `select id, name, pur_price, cur_stock, {qty} as qty, cur_stock - {qty} as leftStock, is_deleted, packing, fingerprint
    from goods where id = {id} `,
  getParentGoods:
    `select id, name, pur_price, cur_stock, packing, fingerprint from goods where is_deleted=0 and fingerprint in ({0});`,
  getParentGoodsWithoutSelf:
    `select id, name, pur_price, cur_stock, packing, fingerprint from goods where is_deleted=0 and fingerprint in ({0}) and id not in ({1});`,
  getPackingGoodsList:
    ` select distinct goods.id, goods.name, goods.code,goods.fingerprint, goods.first_letters, goods.packing,group_concat(goods_ext_barcode.ext_barcode,',') as ext_barcode,
        case when goods.code is null or trim(goods.code)='' then name
          else name || '(' || goods.code || ')'
        end as goodName
      from goods
      left join goods_ext_barcode 
      on goods_ext_barcode.good_fingerprint = goods.fingerprint 
      and goods_ext_barcode.is_deleted = 0
      where goods.is_deleted = 0
      and goods.packing is not null
      and trim(goods.packing) != ''
      GROUP BY goods.id`,
  getGoodsWithErrorFirstLetters:
    `select id, name, first_letters from goods
    where first_letters is null or trim(first_letters)='' or first_letters like '%{first_letters}%';`,
  updateGoodsWithErrorFirstLetters: `update goods set is_synced=0, first_letters='{1}' where id={0};`,
  updateGoodsMinAndMaxStock:
    `update goods set min_stock=round({minStock}, 3), max_stock=round({maxStock}, 3), is_synced=0,
    revise_at=strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')
    where id in ({ids});`,
  goodsStockWarningReportsHeader:
    `select goods.pur_price as purPrice, goods.cur_stock as curStock, goods.min_stock as minStock, goods.max_stock as maxStock
    from goods
    left join types
    on goods.type_fingerprint = types.fingerprint
    {wheres}
    and (goods.cur_stock < goods.min_stock or goods.cur_stock > goods.max_stock);`,
  goodsStockWarningReportsCount:
    ` select count(distinct goods.id) as cnt
      from goods
      left join types
      on goods.type_fingerprint = types.fingerprint
      LEFT JOIN goods_ext_barcode 
      on goods.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      left join units
      on goods.unit_fingerprint = units.fingerprint
      left join goods_attributes ga 
      on goods.fingerprint = ga.fingerprint and ga.is_deleted =0
      left join (
        select gs.fingerprint
        ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
        ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
        from goods_suppliers gs
        inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
        where gs.is_del=0 and s.is_deleted=0
      ) tmp_suppliers on tmp_suppliers.fingerprint = goods.fingerprint
      {wheres}`,
  goodsStockWarningReportsData:
    `select distinct goods.id, goods.name, goods.code, goods.type_fingerprint as typeId, ifnull(tt.name || ' / ', '') || types.name as typeName, goods.unit_fingerprint as unitId,
            units.name as unitName, goods.pur_price as purPrice, goods.cur_stock as curStock,goods.fingerprint,
            goods.min_stock as minStock,supplier_name as supplierName,supplier_contacter as supplierContacter,supplier_mobile as supplierMobile,supplier_addr as supplierAddr,supplier_remark as supplierRemark,
            goods.max_stock as maxStock,
            case when goods.cur_stock <= goods.max_stock and goods.cur_stock >= goods.min_stock then '正常'
                 when goods.cur_stock<goods.min_stock then '不足'
                 when goods.cur_stock>goods.max_stock then '过剩'  
            end as status
    from goods
    left join types
    on goods.type_fingerprint = types.fingerprint
    LEFT JOIN goods_ext_barcode 
    on goods.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    left join types tt 
    on types.parent_fingerprint = tt.fingerprint
    left join units
    on goods.unit_fingerprint = units.fingerprint
    left join goods_attributes ga 
    on goods.fingerprint = ga.fingerprint and ga.is_deleted =0
    left join (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile,s.addr as supplier_addr,s.remark as supplier_remark
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = goods.fingerprint
    {wheres}
    order by goods.{orderBy} {sort}
    {limit};`,
  goodsExpirationWarningReportsData:
    `select distinct goods.id, goods.name, goods.code, goods.type_fingerprint as typeId, ifnull(tt.name || ' / ', '') || types.name as typeName, goods.unit_fingerprint as unitId,
            units.name as unitName, goods.fingerprint, goods.pur_price as purPrice, goods.cur_stock as curStock,
            ga.manufacture_date as manufactureDate,supplier_name as supplierName,
						ga.expiry_days as expiryDays,
						JULIANDAY(ga.manufacture_date)-JULIANDAY(date())+ga.expiry_days | 0  as overdueDate
    from goods
    LEFT JOIN goods_ext_barcode 
    on goods.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    left join types
    on goods.type_fingerprint = types.fingerprint
    left join types tt 
    on types.parent_fingerprint = tt.fingerprint
    left join units
    on goods.unit_fingerprint = units.fingerprint
    left join goods_attributes ga 
    on goods.fingerprint = ga.fingerprint and ga.is_deleted =0
    left join (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = goods.fingerprint
    {wheres}
    order by goods.{orderBy} {sort}
    {limit};`,
  sale_ranking:
    `select p.id as good_id, sum(si.qty) as sumQty, sum(si.disc * si.amt) as sumAmt, p.name,p.specs
    from sales s
    inner join sale_items si on s.fingerprint = si.sale_fingerprint
    left join (
    select id,fingerprint,type_fingerprint,name,pinyin,code,unit_fingerprint,specs,cur_stock,first_letters from goods
    union all
    select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,
    'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,'' as first_letters
    ) p on si.good_fingerprint = p.fingerprint
    where s.is_deleted = 0 and s.opt_date >= {0} and s.opt_date <= {1}
    group by si.good_fingerprint order by {2} desc limit 5;`,
  sale_profit:
    `select sum(si.qty) as sumQty, sum(s.disc * si.amt) as sumAmt, p.name, sum(round(si.qty * ifnull(si.pur_price, p.pur_price), 2)) as costAmt
    from sales s
    inner join sale_items si on s.fingerprint = si.sale_fingerprint
    left join goods p on si.good_fingerprint = p.fingerprint
    where s.is_deleted = 0 and s.opt_date >= {0} and s.opt_date <= {1}
    group by si.good_fingerprint`,
  sale_profit_sumAmt:
    `select sum(s.disc_amt) as sumAmt
    from sales s
    where s.is_deleted = 0 and s.opt_date >= {0} and s.opt_date <= {1}`,
  saleDetailByCode: `select id, fingerprint from sales where code = '{0}' and is_deleted = 0`,
  saleDetail:
    `select a.id, a.opt_date, a.code,round(sum(round(b.pur_price * b.qty, 2)),2) as sum_pur_price, a.in_out, a.account_id, a.remark, a.bill_amt, a.disc_amt, a.disc, a.pay_amt,
      a.owe_amt, a.change_amt, '零售客户' as supplier_name, c.name as account_name, a.uid as uid, a.create_at, a.fingerprint, a.vipid, a.vipname, a.vipmobile, a.info1
    from (select * from sales where fingerprint = '{0}' and is_deleted = 0) a
    left join accounts c on a.account_id = c.id
    left join (SELECT pur_price,qty,sale_fingerprint from sale_items WHERE sale_fingerprint = '{0}') b on a.fingerprint = b.sale_fingerprint
    group by a.id;`,
  saleItemDetail:
    `WITH 
    refund_summary AS (
        SELECT 
            s.refund_fingerprint,
            si.good_fingerprint,
            SUM(si.qty) AS total_qty,
            SUM(si.amt) AS total_amt,
            substr(si.fingerprint, 1, length(si.fingerprint) - 7) AS fingerprint_base
        FROM 
            sale_items si
        JOIN 
            sales s ON si.sale_fingerprint = s.fingerprint
        WHERE 
            s.in_out = '2' 
            AND s.refund_fingerprint = '{0}' 
            AND si.is_deleted = 0
        GROUP BY 
            s.refund_fingerprint, si.good_fingerprint,fingerprint_base
    )
    SELECT 
        a.good_fingerprint,
        a.mprice,
        a.pur_price,
        a.disc,
        a.price,
        a.qty,
        a.amt,
        a.fingerprint,
        b.type_fingerprint,
        ifnull(dd.name || ' / ', '') || d.name AS type_name,
        a.item_disc,
        CASE 
            WHEN b.is_deleted = 1 THEN '(已删除) ' 
            ELSE '' 
        END || b.name AS good_name,
        b.pinyin,
        b.code,
        b.unit_fingerprint,
        e.name AS unit_name,
        ifnull(c.url, '') AS url,
        ifnull(c.local_url, '') AS localUrl,
        b.specs,
        a.qty AS sale_qty,
        COALESCE(rs.total_qty, 0) AS refunded_qty,
        a.qty + COALESCE(rs.total_qty, 0) AS remaining_qty,
        COALESCE(rs.total_amt, 0) AS refunded_amt
    FROM (
        SELECT 
            * 
        FROM 
            sale_items 
        WHERE 
            sale_fingerprint = '{0}' 
            AND is_deleted = 0
    ) a
    INNER JOIN (
        SELECT 
            id,
            fingerprint,
            type_fingerprint,
            name,
            pinyin,
            code,
            unit_fingerprint,
            specs,
            is_deleted 
        FROM 
            goods
        UNION ALL
        SELECT 
            0, 
            '646b141adf1d4e1b880d373a00dd3025', 
            '16c8c8f0082449d33ff8d1a2ada934f5', 
            '直接收款', 
            'zjsk', 
            '-', 
            '0', 
            '{"unlock":{},"lock":{}}', 
            0
    ) b ON a.good_fingerprint = b.fingerprint
    LEFT JOIN (
        SELECT 
            MAX(create_at), 
            good_fingerprint, 
            url, 
            local_url 
        FROM 
            images 
        WHERE 
            is_deleted = 0 
        GROUP BY 
            good_fingerprint
    ) c ON a.good_fingerprint = c.good_fingerprint
    LEFT JOIN 
        types d ON b.type_fingerprint = d.fingerprint
    LEFT JOIN 
        types dd ON d.parent_fingerprint = dd.fingerprint
    LEFT JOIN 
        units e ON b.unit_fingerprint = e.fingerprint
    LEFT JOIN 
        refund_summary rs ON a.fingerprint = rs.fingerprint_base
    ORDER BY 
        a.id;`,
  sales_byday_select:
    `select sbp.id, c.id as acct_id, sbp.remark, sbp.pay_amt, c.name as account_name, a.create_at
    from sale_blend_pays sbp
    inner join (select * from sales where id = {0} and is_deleted = 0) a
    on sbp.sale_fingerprint = a.fingerprint
    left join accounts c on sbp.account_id = c.id
    group by sbp.id;`,
  sales_byday_item:
    `select a.id, a.opt_date, a.code, a.supplier_fingerprint, a.account_id, a.bill_amt, a.disc_amt, a.disc,
      a.pay_amt, a.owe_amt, a.change_amt, a.remark, a.uid, a.revise_at, a.create_at,
      b.name as company_name, b.mobile as company_mobile, c.name as acct_name
    from sales a
    left join suppliers b on a.supplier_fingerprint = b.fingerprint
    left join accounts c on a.account_id = c.id
    left join clerks d on a.uid=d.uid
    where a.opt_date between '{from}' and '{to}' {sql} and a.is_deleted = 0
    group by a.id
    order by a.opt_date desc, a.code desc
    limit {limit} offset {offset};`,
  sale_search_byid:
    `select distinct a.id from sales a left join sale_items b on a.fingerprint = b.sale_fingerprint
    where a.opt_date between '{from}' and '{to}' and a.is_deleted = 0 and b.is_deleted = 0
    and ((a.remark like '%{condition}%' or a.code like '%{condition}%') or b.remark like '%{condition}%');`,
  sale_search_amt:
    `select sum(disc_amt) as sum_disc_amt, sum(bill_amt) as sum_bill_amt, sum(pay_amt) as sum_pay_amt,
    sum(disc_amt -pay_amt) as sum_real_owe_amt from (select sum(distinct(disc_amt)) as disc_amt, sum(distinct(bill_amt)) as bill_amt,
    sum(distinct(pay_amt)) as pay_amt from sales a where a.opt_date between '{from}' and '{to}' {sql} and a.is_deleted=0 group by a.id);`,
  sale_search_byday:
    `select a.opt_date, sum(a.disc_amt) as day_sum_disc_amt, count(1) as day_count_disc_amt
    from (
      select opt_date, disc_amt
      from sales a
      where a.opt_date between '{from}' and '{to}' {sql} and a.is_deleted=0 " +
      group by a.id
    ) a
    group by a.opt_date
    order by a.opt_date desc;`,
  sale_search_byday_item:
    `select a.id, a.opt_date, a.in_out, a.code, a.supplier_id, a.account_id,
      a.bill_amt, a.disc_amt, a.disc, a.pay_amt, a.owe_amt, a.change_amt, a.remark,
      b.name as company_name, b.mobile as company_mobile, c.name as account_name,
      a.uid , a.revise_at,a.create_at, a.vipid, a.vipname, a.vipmobile,
      case when abs(a.disc_amt - a.pay_amt ) < 0.01 then 0
        else a.disc_amt - a.pay_amt
      end as real_owe_amt
    from sales a
      left join suppliers b on a.supplier_fingerprint=b.fingerprint
      left join accounts c on a.account_id=c.id
      left join clerks d on a.uid=d.uid
    where a.opt_date between '{from}' and '{to}' {sql} and a.is_deleted = 0
    group by a.id
    order by a.create_at desc, a.code desc
    limit {limit} offset {offset};`,
  salesInsert:
    `insert into sales (uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark,
      vipid, vipname, vipmobile, account_id, fingerprint, info1, info2, refund_fingerprint) values `,
  salesInsertValues:
    `({uid}, {optDate}, {code}, {inOut}, round({billAmt}, 2), round({discAmt}, 2), round({disc}, 2), round({payAmt}, 2), round({oweAmt}, 2),
    round({changeAmt}, 2), {remark}, {vipid}, {vipname}, {vipmobile}, {accountId}, {fingerprint}, {info1}, {info2}, {refundFingerprint})`,
  saleItemsInsert:
    `insert into sale_items (pur_price, price, qty, amt, disc, item_disc, sale_fingerprint, good_fingerprint, fingerprint, mprice) values `,
  saleItemsInsertValues:
    `(round({purPrice}, 6), round({price}, 2), round({qty}, 3), round({amt}, 2), {disc}, {itemDisc}, {saleFingerprint}, {goodFingerprint}, {fingerprint}, {mprice})`,
  saleUpdateGoods: `update goods set cur_stock = cur_stock - round({qty}, 3) where fingerprint={goodFingerprint};`,
  accountsUpdate:
    `update accounts set cur_amt = cur_amt + round({curAmt}, 2) where id={id};`,
  blend_insert_item:
    `insert into blend_items (account_id, pay_amt, remark, fingerprint, sale_fingerprint) values
    ('{account_id}', '{pay_amt}', '{remark}', '{fingerprint}', '{sale_fingerprint}');`,
  saleGetTotal:
    `CREATE INDEX IF NOT EXISTS idx$sales$refund_fingerprint ON sales(refund_fingerprint);
      select count(1) as salesCount, sum(pay_amt) as totalMoney from sales where is_deleted=0 {0};`,
  saleGetTotalWithGoods:
    `CREATE INDEX IF NOT EXISTS idx$sales$refund_fingerprint ON sales(refund_fingerprint);
    with tmp as (
      select distinct sales.id, sales.pay_amt
      from sales
      inner join sale_items as items
      on sales.fingerprint=items.sale_fingerprint
      inner join (
        select distinct	goods.id,goods.fingerprint,goods.pinyin,goods.code,goods.unit_fingerprint,
              goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
        from goods
        LEFT JOIN goods_ext_barcode 
        on goods.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        where 1=1 {1}
        union all
        select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,
        'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,'' as first_letters,0
        where 1=1 {2}
      ) goods
      on items.good_fingerprint=goods.fingerprint
      where sales.is_deleted=0
      {0}
    )
    select count(1) as salesCount, sum(pay_amt) as totalMoney
    from tmp;`,
  saleGetDayTotal:
    `select strftime('%Y-%m-%d', opt_date) as opt_date, sum(pay_amt) as dayTotalMoney from sales
    where is_deleted=0 {0};`,
  saleGetDayTotalWithGoods:
    `with tmp as (
      select distinct sales.id, strftime('%Y-%m-%d', sales.opt_date) as opt_date, sales.pay_amt
      from sales
      inner join sale_items as items
      on sales.fingerprint=items.sale_fingerprint
      inner join (
        select distinct	goods.id,goods.fingerprint,goods.pinyin,goods.code,goods.unit_fingerprint,
              goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
        from goods
        LEFT JOIN goods_ext_barcode 
        on goods.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        where 1=1 {1}
        union all
        select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,
		          '16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,
              '' as first_letters,0 as is_deleted
        where 1=1 {2}
      ) goods
      on items.good_fingerprint=goods.fingerprint
      where sales.is_deleted=0
      {0}
    )
    select opt_date, sum(pay_amt) as dayTotalMoney
    from tmp
    group by opt_date
    order by opt_date desc;`,
  saleGetDetails:
    `SELECT 
    sales.id,
    sales.uid,
    strftime('%Y-%m-%d', sales.opt_date) AS opt_date,
    sales.code,
    sales.in_out,
    sales.bill_amt,
    sales.disc_amt,
    sales.disc,
    sales.pay_amt,
    sales.owe_amt,
    sales.change_amt,
    sales.remark,
    sales.account_id,
    a.name AS account_name,
    strftime('%Y-%m-%d %H:%M:%S', sales.create_at) AS create_at,
    strftime('%Y-%m-%d %H:%M:%S', sales.revise_at) AS revise_at,
    sales.is_deleted,
    sales.is_synced,
    sales.fingerprint,
    sales.vipid,
    sales.vipname,
    sales.vipmobile,
    sales.info1,
    sales.info2,
    sales.refund_fingerprint,
    CASE 
        WHEN refund_summary.total_qty IS NULL THEN 'No Refund'
        WHEN ROUND(refund_summary.total_qty, 3) = ROUND(sale_summary.total_qty, 3) THEN 'Full Refund'
        ELSE 'Partial Refund'
    END AS refund_status
FROM 
    sales 
INNER JOIN 
    accounts a ON sales.account_id = a.id
LEFT JOIN (
    SELECT 
        si.sale_fingerprint,
        SUM(si.qty) AS total_qty
    FROM 
        sale_items si
    JOIN 
        sales  ON si.sale_fingerprint = sales.fingerprint
    WHERE 
        1=1 {0}
    GROUP BY 
        si.sale_fingerprint
) AS sale_summary ON sales.fingerprint = sale_summary.sale_fingerprint
LEFT JOIN (
    SELECT 
        sales.refund_fingerprint,
        SUM(ABS(si.qty)) AS total_qty
    FROM 
        sale_items si
    JOIN 
        sales  ON si.sale_fingerprint = sales.fingerprint
    WHERE 
        sales.in_out = '2'
    GROUP BY 
        sales.refund_fingerprint
) AS refund_summary ON sales.fingerprint = refund_summary.refund_fingerprint
WHERE 
    sales.is_deleted = 0
AND 
    1=1 {0} {1}
limit {4}, {5};`,
  saleGetDetailsWithGoods:
    `WITH sales_data AS (
      SELECT
      sales.id,
      sales.uid,
      strftime('%Y-%m-%d', sales.opt_date) AS opt_date,
      sales.code,
      sales.in_out,
      sales.bill_amt,
      sales.disc_amt,
      sales.disc,
      sales.pay_amt,
      sales.owe_amt,
      sales.change_amt,
      sales.remark,
      sales.account_id AS accountSyncG,
      accounts.name AS account_name,
      strftime('%Y-%m-%d %H:%M:%S', sales.create_at) AS create_at,
      strftime('%Y-%m-%d %H:%M:%S', sales.revise_at) AS revise_at,
      sales.is_deleted,
      sales.is_synced,
      sales.fingerprint,
      sales.vipid,
      sales.vipname,
      sales.vipmobile,
      sales.info1,
      sales.info2,
      sales.refund_fingerprint
      FROM
      sales
      INNER JOIN accounts ON sales.account_id = accounts.id
      INNER JOIN sale_items AS items ON sales.fingerprint = items.sale_fingerprint
      INNER JOIN (
        SELECT DISTINCT
          goods.id,
          goods.fingerprint,
          goods.pinyin,
          goods.code,
          goods.unit_fingerprint,
          goods.type_fingerprint,
          goods.name,
          goods.specs,
          goods.cur_stock,
          goods.first_letters,
          goods.is_deleted
        FROM
          goods
          LEFT JOIN goods_ext_barcode ON goods.fingerprint = goods_ext_barcode.good_fingerprint
          AND goods_ext_barcode.is_deleted = 0
        WHERE
          1 = 1 {2}
        UNION ALL
        SELECT
          0 AS id,
          '646b141adf1d4e1b880d373a00dd3025' AS fingerprint,
          'zjsk' AS pinyin,
          '-' AS code,
          '0' AS unit_fingerprint,
          '16c8c8f0082449d33ff8d1a2ada934f5' AS type_fingerprint,
          '直接收款' AS name,
          '{"unlock":{},"lock":{}}' AS specs,
          0 AS cur_stock,
          '' AS first_letters,
          0 AS is_deleted 
        WHERE
          1 = 1 
          {3}
        ) goods ON items.good_fingerprint = goods.fingerprint 
      WHERE
        sales.is_deleted = 0 {0}
      ),
      refund_data AS (
        SELECT
          sd.fingerprint AS original_fingerprint,
          total_refund_qty
        FROM
          sales_data sd
          INNER JOIN (
            select s.refund_fingerprint,
            SUM(ABS(si.qty)) total_refund_qty
            FROM sales s
            inner join sale_items si on s.fingerprint = si.sale_fingerprint
            where s.refund_fingerprint != '' AND s.refund_fingerprint IS NOT NULL
            GROUP BY s.refund_fingerprint
          ) a on sd.fingerprint = a.refund_fingerprint
      ),
      final_data AS (
        SELECT
          sale.*,
          a.qty AS sale_qty,
          COALESCE(refund_data.total_refund_qty, 0) AS refund_qty,
          (a.qty - COALESCE(refund_data.total_refund_qty, 0))  AS remaining_qty,
          CASE
            WHEN COALESCE(refund_data.total_refund_qty, 0) = 0 THEN 'No Refund'
            WHEN COALESCE(refund_data.total_refund_qty, 0) >= a.qty THEN 'Full Refund'
            ELSE 'Partial Refund'
          END AS refund_status
        FROM (
          SELECT SUM(items.qty) qty,sale.fingerprint
          FROM sales_data sale INNER JOIN 
          (SELECT sale_fingerprint,qty FROM sale_items items where sale_fingerprint in (SELECT fingerprint FROM sales_data))
            items ON sale.fingerprint = items.sale_fingerprint
            GROUP BY sale.fingerprint
          ) a INNER JOIN sales_data sale on a.fingerprint = sale.fingerprint
            left join refund_data ON sale.fingerprint = refund_data.original_fingerprint
      )
      SELECT DISTINCT
        id,
        uid,
        opt_date,
        code,
        in_out,
        bill_amt,
        disc_amt,
        disc,
        pay_amt,
        owe_amt,
        change_amt,
        remark,
        accountSyncG,
        account_name,
        create_at,
        revise_at,
        is_deleted,
        is_synced,
        fingerprint,
        vipid,
        vipname,
        vipmobile,
        info1,
        info2,
        sale_qty,
        refund_qty,
        remaining_qty,
        refund_status,
        refund_fingerprint
      FROM
        final_data sales
      {1} 
      LIMIT {4}, {5};`,
  doSalesExcelExportCount:
    `select sales.id,items.id as itemId,strftime('%Y-%m-%d %H:%M:%S', sales.create_at) as create_at
      from sales
      inner join sale_items as items
      on sales.fingerprint=items.sale_fingerprint
      inner join (
        select distinct	goods.id,goods.fingerprint,goods.pinyin,goods.code,goods.unit_fingerprint,
              goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
        from goods
        LEFT JOIN goods_ext_barcode 
        on goods.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
        where 1=1 {1}
        union all
        select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,
		         '16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,
             '' as first_letters,0 as is_deleted
        where 1=1 {2}
      ) goods
      on items.good_fingerprint=goods.fingerprint
      left join clerks on sales.uid = clerks.uid
      left join accounts on sales.account_id = accounts.id
      where sales.is_deleted=0
      {0}`,
  doSalesExcelExport:
    `WITH sbp_sale AS (
      SELECT
        sbp.account_id,
        sbp.sale_fingerprint,
        CASE
          WHEN sbp.account_id = 1 THEN
            sbp.pay_amt + sales.change_amt 
          ELSE 
            sbp.pay_amt 
        END AS pay_amt 
      FROM
        sale_blend_pays AS sbp
      INNER JOIN sales ON sales.fingerprint = sbp.sale_fingerprint 
    ),
    sbp AS (
      SELECT
        GROUP_CONCAT(acct.name || ' ' || sbp.pay_amt) AS pay_amt,
        sbp.sale_fingerprint 
      FROM
        sbp_sale AS sbp
      INNER JOIN accounts AS acct ON acct.id = sbp.account_id 
      GROUP BY
        sbp.sale_fingerprint 
    ),
    refund_codes AS (
      SELECT
        r.refund_fingerprint,
        GROUP_CONCAT(r.code, '; ') AS refundCodes
      FROM
        sales r
      WHERE
        r.refund_fingerprint IS NOT NULL {3}
      GROUP BY
        r.refund_fingerprint
    ),
    sale_codes AS (
      SELECT
        s.fingerprint AS sale_fingerprint,
        GROUP_CONCAT(s.code, '; ') AS saleCodes
      FROM
        sales s
      WHERE
        s.refund_fingerprint IS NOT NULL {4}
      GROUP BY
        s.fingerprint
    ),
    refund_quantities AS (
      SELECT
        refund.refund_fingerprint,
        substr(refund_items.fingerprint, 1, length(refund_items.fingerprint) - 7) AS item_fingerprint,
        SUM(ABS(refund_items.qty)) AS refundQty
      FROM
        sales refund
      INNER JOIN sale_items AS refund_items ON refund.fingerprint = refund_items.sale_fingerprint
      AND length(refund_items.fingerprint) > 36
      WHERE
        refund.refund_fingerprint IS NOT NULL {5}
      GROUP BY
        refund.refund_fingerprint,
        refund_items.fingerprint
    ),
    tmp AS (
      SELECT
        sales.id,
        sales.code,
        strftime('%Y-%m-%d %H:%M:%S', sales.create_at) AS create_at,
        COALESCE(tt.name || ' / ', '') || t.name AS category,
        CASE
            WHEN sales.in_out = 1 THEN
                CASE
                    WHEN r_qty.refundQty > 0 THEN '收款(已退)'
                    ELSE '收款'
                END
            ELSE '退款'
        END AS inOut,
        sales.vipname,
        sales.remark,
        ROUND(sales.disc_amt, 2) AS discAmt,
        ROUND(sales.bill_amt - sales.disc_amt, 2) AS prefAmt,
        ROUND(sales.disc_amt + sales.change_amt, 2) AS actualReceiveAmt,
        ROUND(sales.change_amt, 2) AS changeAmt,
        ROUND(sales.pay_amt, 2) AS payAmt,
        CASE
          WHEN clerks.employee_number IS NULL OR TRIM(clerks.employee_number) = '' THEN '管理员' 
          ELSE TRIM(clerks.employee_number) 
        END AS employeeNumber,
        CASE
          WHEN sales.account_id = 99 AND sbp.sale_fingerprint IS NOT NULL THEN sbp.pay_amt 
          ELSE accounts.name 
        END AS accountName,
        items.id AS itemId,
        CASE
          WHEN goods.is_deleted = 1 THEN '(已删除) ' 
          ELSE '' 
        END || goods.name AS goodName,
        ROUND(ABS(items.qty), 3) AS qty,
        ROUND(items.pur_price, 6) AS pur_price,
        ROUND(items.price, 2) AS price,
        items.item_disc AS itemDisc,
        ROUND(items.amt, 2) AS amt,
        goods.specs,
        goods.code AS goodsCode,
        r_codes.refundCodes,
        s_codes.saleCodes,
        COALESCE(r_qty.refundQty, 0) AS refundQty
      FROM
        sales
      INNER JOIN sale_items AS items ON sales.fingerprint = items.sale_fingerprint
      LEFT JOIN refund_codes r_codes ON sales.fingerprint = r_codes.refund_fingerprint
      LEFT JOIN sale_codes s_codes ON sales.refund_fingerprint = s_codes.sale_fingerprint
      LEFT JOIN refund_quantities r_qty ON sales.fingerprint = r_qty.refund_fingerprint AND r_qty.item_fingerprint = items.fingerprint
      INNER JOIN (
        SELECT DISTINCT
          goods.id,
          goods.fingerprint,
          goods.pinyin,
          goods.code,
          goods.unit_fingerprint,
          goods.type_fingerprint,
          goods.name,
          goods.specs,
          goods.cur_stock,
          goods.first_letters,
          goods.is_deleted 
        FROM
          goods
        LEFT JOIN goods_ext_barcode ON goods.fingerprint = goods_ext_barcode.good_fingerprint 
        AND goods_ext_barcode.is_deleted = 0 
        WHERE
          1 = 1  {1}
        UNION ALL
        SELECT
          0 AS id,
          '646b141adf1d4e1b880d373a00dd3025' AS fingerprint,
          'zjsk' AS pinyin,
          '-' AS code,
          '0' AS unit_fingerprint,
          '16c8c8f0082449d33ff8d1a2ada934f5' AS type_fingerprint,
          '直接收款' AS name,
          '{"unlock":{},"lock":{}}' AS specs,
          0 AS cur_stock,
          '' AS first_letters,
          0 AS is_deleted 
        WHERE
          1 = 1 {2}
      ) goods ON items.good_fingerprint = goods.fingerprint
      LEFT JOIN types AS t ON t.fingerprint = goods.type_fingerprint
      LEFT JOIN types tt ON t.parent_fingerprint = tt.fingerprint
      LEFT JOIN clerks ON sales.uid = clerks.uid
      LEFT JOIN accounts ON sales.account_id = accounts.id
      LEFT JOIN sbp ON sbp.sale_fingerprint = sales.fingerprint 
      WHERE
        sales.is_deleted = 0 {0}

    ),
    tmp_all AS (
      SELECT DISTINCT
        1 AS oid,
        id,
        code,
        create_at,
        inOut,
        employeeNumber,
        vipname,
        ROUND(SUM(ROUND(qty * pur_price, 2)), 2) AS sum_pur_price,
        '-' AS itemId,
        '-' AS goodName,
        '-' AS qty,
        '-' AS price,
        '-' AS itemDisc,
        '-' AS amt,
        payAmt,
        discAmt,
        prefAmt,
        actualReceiveAmt,
        changeAmt,
        accountName,
        remark,
        '{"unlock":{},"lock":{}}' AS specs,
        '-' AS goodsCode,
        '-' AS category,
        refundCodes,
        saleCodes,
        CASE
          WHEN SUM(refundQty) > 0 AND SUM(refundQty) < SUM(qty) THEN '部分退货' 
          WHEN SUM(refundQty) = SUM(qty) THEN '整单退货' 
          ELSE '' 
        END AS isRefund,
        '' AS refundQty,
        '' AS remainingQty 
      FROM
        tmp 
      GROUP BY
        code 
      UNION ALL
      SELECT
        2 AS oid,
        id,
        code,
        create_at,
        inOut,
        employeeNumber,
        vipname,
        '' AS sum_pur_price,
        CAST(itemId AS TEXT),
        CAST(goodName AS TEXT),
        CAST(qty AS TEXT),
        CAST(price AS TEXT),
        CAST(itemDisc AS TEXT),
        CAST(amt AS TEXT),
        0.00 AS discAmt,
        0.00 AS payAmt,
        0.00 AS prefAmt,
        0.00 AS actualReceiveAmt,
        0.00 AS changeAmt,
        '' AS accountName,
        '' AS remark,
        specs,
        goodsCode,
        category,
        '' AS refundCodes,
        '' AS saleCodes,
        '' AS isRefund,
        refundQty,
        ROUND(qty - refundQty, 3) AS remainingQty 
      FROM
        tmp 
    ) 
SELECT
  id,
  code,
  create_at,
  inOut,
  employeeNumber,
  vipname,
  itemId,
  goodName,
  qty,
  CAST(refundQty AS TEXT) AS refundQty,
  CAST(remainingQty AS TEXT) AS remainingQty,
  CASE
    WHEN oid = 1 THEN
      CASE
        WHEN inOut = '退款' THEN CAST(ROUND(payAmt + sum_pur_price, 2) as VARCHAR)
        ELSE CAST(ROUND(payAmt - sum_pur_price, 2) as VARCHAR)
      END
    ELSE '-' 
  END AS profits,
  price,
  itemDisc,
  amt,
  discAmt,
  prefAmt,
  actualReceiveAmt,
  changeAmt,
  accountName,
  remark,
  specs,
  goodsCode,
  category,
  CASE
    WHEN oid = 1 THEN refundCodes 
    ELSE '' 
  END AS refundCodes,
  CASE
    WHEN oid = 1 THEN saleCodes 
    ELSE '' 
  END AS saleCodes,
  isRefund 
FROM
  tmp_all 
ORDER BY
  create_at DESC,
  id,
  oid,
  itemId;`,
  salesDeleteBatch:
    `create temp table tmpSales as select id, fingerprint from sales where id in ({id}) and is_deleted!={isDel};
    update sales set is_synced=0, is_deleted={isDel} where id in (select id from tmpSales);
    update sale_items set is_synced=0, is_deleted={isDel} where sale_fingerprint in (select fingerprint from tmpSales);
    with tmp_accts as (
      select account_id, round(sum(pay_amt), 2) as pay_amt
      from sales
      where id in (select id from tmpSales)
      group by account_id
    )
    update accounts set cur_amt=
      case when {isDel}=1 then cur_amt - (select pay_amt from tmp_accts where account_id=accounts.id)
        else cur_amt + (select pay_amt from tmp_accts where account_id=accounts.id)
      end
    where fingerprint in (select account_id from tmp_accts);
    with tmp_stock as (
      select good_fingerprint, round(sum(qty), 3) as qty
      from sale_items
      where sale_fingerprint in (select fingerprint from tmpSales)
      group by good_fingerprint
    )
    update goods set cur_stock=
      case when {isDel}=1 then cur_stock + (select qty from tmp_stock where good_fingerprint=goods.fingerprint)
        else cur_stock - (select qty from tmp_stock where good_fingerprint=goods.fingerprint)
      end
    where fingerprint in (select good_fingerprint from tmp_stock);
    drop table tmpSales;`,
  getAcctsById:
    `select id, name, in_out, init_amt, cur_amt, create_at, revise_at, is_deleted, is_synced, fingerprint
    from accounts
    where is_deleted = 0
    and id in ({0});`,
  saleBlendPaysInsert:
    `insert into sale_blend_pays(pay_amt, remark, account_id, sale_fingerprint, fingerprint) `,
  saleBlendPaysInsertWith:
    `with tmp as (select id from sales where fingerprint = '{0}' limit 1) `,
  saleBlendPaysSelect:
    `select round({payAmt}, 2), {remark}, {acctId}, {saleFingerprint}, {fingerprint} `,
  updateAcctsByBlendPays:
    `with tmp as (
      select accounts.id, round(blend.pay_amt + accounts.cur_amt, 2) as cur_amt
      from sale_blend_pays as blend
      inner join accounts
      on blend.account_id = accounts.id
      where blend.sale_fingerprint = '{0}'
    )
    update accounts set
      cur_amt = (select cur_amt from tmp where id = accounts.id)
    where id in (select id from tmp);`,
  clerk_upsertShopusers:
    `delete from clerks; update sqlite_sequence set seq = 0 where name = 'clerks';
    insert into clerks(uid,name,role,status,privilege,phone,email,gender,avatar)
    select {uid}, '{name}', {role}, {status}, '{privilege}', '{phone}', '{email}', '{gender}', '{avatar}'`,
  clerk_up_name: `update clerks set name = '{name}' where uid = {uid}`,
  clerk_employee_del: `delete from clerks where employee_number is not null;`,
  clerk_employee_upsertShopusers: `insert into clerks(uid,name,role,status,privilege,phone,employee_number,password) values `,
  clerk_employee_upsertShopusers_values: `({uid}, '{name}', {role}, {status}, '{privilegeFunctionName}', '{phone}', '{employeeNumber}', '{password}'),`,
  clerk_shopusers: `select * from clerks where phone = '{phone}'`,
  clerk_employees: `select * from clerks where employee_number = '{employeenumber}'`,
  clerkEmployeeCheck: `select count(1) as iftrue from clerks where employee_number = '{employeeNumber}' and password = '{userpwd}'`,
  get_all_shop_users: `select * from clerks`,
  getAllClerks:
    `select uid as value,
      case when employee_number is null or trim(employee_number)='' then '管理员'
      else employee_number
      end as label from clerks
    order by id;`,
  getStoreInfo:
    `select id, guid, name, addr, industry, contacter, tel, phone, qq, wechat, is_deleted isDeleted, is_synced isSynced, fingerprint,
      discount_settings discountSettings, settings, strftime('%Y-%m-%d %H:%M:%S', create_at) createAt,
      strftime('%Y-%m-%d %H:%M:%S', revise_at) reviseAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) syncAt
    from storeinfo where id={id};`,
  updateStoreInfo:
    `update storeinfo set name={name}, addr={addr}, industry={industry}, contacter={contacter}, tel={tel}, phone={phone}, qq={qq},
      wechat={wechat}, is_deleted={is_deleted}, is_synced=0, fingerprint={fingerprint}, discount_settings={discountSettings}, settings={settings}
    where guid={guid};`,
  updateDiscountSettings:
    `update storeinfo set is_synced=0, discount_settings={discountSettings} where id={id};`,
  updateStoreInfoSettings:
    `update storeinfo set is_synced=0, settings={settings} where id={id};`,
  settingCurrentDate: `select datetime('now','localtime') as currentdate`,
  settingLogin:
    `select case when a.hours>2 then 0 else 1 end as flag
    from (select (julianday(datetime('now','localtime')) - ifnull((select julianday(value) from settings where key='{key}'), 0))*24 as hours) a;`,
  settingPut: `replace into settings(key,value,remark) {select};`,
  settingGet: `select key,value,remark from settings {where};`,
  // 离线登陆，判断用户名密码是否正确。1：正确；0：错误
  offlineLogin: `select case count(1) when 2 then 1 else 0 end as iftrue from settings where (key='useruid' and value='{username}') or (key='userpwd' and value='{userpwd}');`,
  image_put:
    `insert into images(url, local_url, is_deleted, is_synced, good_fingerprint, fingerprint)
    select '{url}', '{localUrl}', {is_deleted}, '{is_synced}', '{good_fingerprint}', '{fingerprint}'
    where not exists (select 1 from images where good_fingerprint='{good_fingerprint}');
    update images set url='{url}', local_url='{localUrl}', is_deleted={is_deleted}, is_synced='{is_synced}'
    where good_fingerprint='{good_fingerprint}';`,
  image_get: `select * from images where good_fingerprint='{good_fingerprint}'`,
  image_del_get_by_good: `select * from images where good_fingerprint in (select fingerprint from goods where is_deleted=1 and fingerprint='{fingerprint}');`,

  updateSettings:
    `update settings set value={1}, remark=strftime('%Y-%m','now','localtime') where key='{0}';`,
  orderGet:
    `select '{type}' || strftime('%Y%m','now','localtime') || substr('00000'||(ifnull(max(substr(code,10,instr(substr(code,10),'.')-1)), 0)+1), -5, 5)
    || '.{deviceCode}' as code from {table} where substr(opt_date, 1, 7) = strftime('%Y-%m','now','localtime');`,
  getOrderNo:
    `with recursive orders(i) as (
      select 1
      union all
      select i+1 from orders
      limit 99999
    )
    select '{type}' || strftime('%Y%m','now','localtime') || substr('0000' || i, -5, 5)
      || '.{deviceCode}' as code
    from orders
    where i > (
      select cast(ifnull(max(substr(code, 10, 5)), 0) as int)
      from {table}
      where substr(opt_date, 1, 7) = strftime('%Y-%m','now','localtime')
    )
    order by i
    limit {limit};`,
  getSalesNo:
    `select ifnull(max(substr(code, 10, 5))+1, 1) as salesNo
    from sales
    where opt_date = date('now', 'localtime')
    and substr(code, instr(code, '.')+1) = '{deviceCode}';`,
  purchasesInsert:
    `insert into purchases(uid, code, in_out, bill_amt, disc_amt, pay_amt, owe_amt, disc, remark, account_id, supplier_fingerprint, fingerprint) values `,
  purchasesInsertValues:
    `({uid}, {code}, {inOut}, round({billAmt}, 2), round({discAmt}, 2), round({payAmt}, 2), round({oweAmt}, 2), {disc}, {remark},
    {accountId}, {supplierFingerprint}, {fingerprint})`,
  purchaseItemsInsert:
    `insert into purchase_items(disc, price, qty, amt, good_fingerprint, pur_fingerprint, fingerprint) values `,
  purchaseItemsInsertValues:
    `({disc},  round({price}, 6), round({qty}, 3), round({amt}, 2), {goodFingerprint}, {purFingerprint}, {fingerprint})`,
  purchasePut:
    `insert into purchases(uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, remark,
      supplier_fingerprint, account_id, fingerprint)
    values ('{uid}',strftime('%Y-%m-%d','now','localtime'),'{code}','{in_out}','{bill_amt}','{disc_amt}','{disc}',
      '{pay_amt}','{owe_amt}','{remark}','{supplier_fingerprint}','{account_id}','{fingerprint}');
    insert into purchase_items(pur_id, good_id, in_out, disc, price, qty, amt, remark, fingerprint) values {values};
    update accounts set is_synced=0, cur_amt=cur_amt - {pay_amt} where id={account_id};`,
  purchaseUpdateGoods:
    `update goods set pur_price=round({purPrice}, 6), cur_stock=cur_stock+round({curStock}, 3), is_synced=0 
    ,sale_price=round({salePrice}, 2) ,vip_price=round({vipPrice}, 2) where id = {id};
     insert into   goods_attributes
                (is_sale, "type", sort, manufacture_date, info1,
                sale_count, sale_time, options, is_set_sale, is_stock_management, is_valuate, fingerprint, is_deleted) 
            values (1,'goods',1,{manufactureDate},'','','','',0,0,0,{goodFingerprint},0)
    on CONFLICT(fingerprint) do
    update  set manufacture_date ={manufactureDate}, is_synced=0, revise_at=datetime('now','localtime');`,
  purchaseExists:
    `select case when not exists(select 1 from suppliers where is_deleted=0 and id={supplier_id}) then 1
    when not exists(select 1 from accounts where is_deleted=0 and id={account_id}) then 2
    when exists(select 1 from purchases where is_deleted=0 and code='{code}') then 3
    else 0 end as val;`,
  getPurchaseId:
    `((select id from purchases where fingerprint = '{purFingerprint}'),'{good_id}','{in_out}','{disc}','{price}',round({qty}, 3),'{amt}','{remark}','{fingerprint}')`,
  purchase_search:
    `select a.*,
      b.name as company_name,
      b.mobile as company_mobile,
      c.name as account_name
    from purchases a
    left join suppliers b
    on a.supplier_fingerprint = b.fingerprint
    and b.is_deleted=0
    left join accounts c
    on a.account_id= c.id
    and c.is_deleted=0
    where a.opt_date between '{from}' and '{to}'
    and a.is_deleted=0
     and ('{supplier_fingerprint}'='' or a.supplier_fingerprint='{supplier_fingerprint}')
     and ('{account_id}'='' or a.account_id='{account_id}')
     and ('{in_out}'='' or a.in_out='{in_out}')
     and ('{search_data}'=''
       or a.code like '%{search_data}%'
       or a.remark like '%{search_data}%'
       or b.name like '%{search_data}%'
      or a.id in (
        select pur_id
        from purchase_items
        where is_deleted=0
         and (remark like '%{search_data}%' or good_fingerprint in (select fingerprint from goods where is_deleted=0 and name like '%{search_data}%'))
      )
    )
    order by a.opt_date desc, a.code desc;`,
  purchaseSearchDetail:
    `select a.*,
      b.name as supplier_name, b.mobile as company_mobile, c.name as account_name, d.good_fingerprint, d.disc as item_disc, d.price, d.qty,
      d.amt, a.remark as item_remark, case when e.is_deleted=1 then '(已删除) ' else '' end || e.name as good_name,
      e.code as good_code, e.unit_fingerprint,f.name as unit_name,e.specs,
      ifnull((select url from images where is_deleted = 0 and good_fingerprint=e.fingerprint order by create_at desc limit 1), '') as url,
      ifnull((select local_url from images where is_deleted = 0 and good_fingerprint=e.fingerprint order by create_at desc limit 1), '') as localUrl
    from purchases a
    left join suppliers b
    on a.supplier_fingerprint = b.fingerprint
    left join accounts c
    on a.account_id= c.id
    and c.is_deleted=0
    inner join purchase_items d
    on a.fingerprint=d.pur_fingerprint
    and d.is_deleted=0
    inner join goods e
    on d.good_fingerprint = e.fingerprint
    left join units f
    on e.unit_fingerprint= f.fingerprint
    and f.is_deleted=0
    where a.id = {id}
    and a.is_deleted = 0;`,
  purchase_delete:
    `update purchases set is_synced=0,is_deleted=1 where fingerprint={fingerprint};
    update accounts set cur_amt=cur_amt+(select pay_amt from purchases where fingerprint={fingerprint} and account_id=accounts.id)
    where exists(select 1 from purchases where fingerprint={fingerprint} and account_id=accounts.id);
    update purchase_items set is_synced=0,is_deleted=1 where pur_fingerprint={fingerprint};
    update goods set cur_stock=cur_stock-(select round(qty, 3) from purchase_items where pur_fingerprint={fingerprint} and good_fingerprint=goods.fingerprint)
    where exists(select 1 from purchase_items where pur_fingerprint={fingerprint} and good_fingerprint=goods.fingerprint);`,
  purchasesDeleteBatch:
    `create temp table tmpPurchases as select id, fingerprint from purchases where id in ({id}) and is_deleted!={isDel};
    update purchases set is_deleted={isDel}, is_synced=0 where id in (select id from tmpPurchases);
    update purchase_items set is_synced=0, is_deleted={isDel} where pur_fingerprint in (select fingerprint from tmpPurchases);
    with tmp_accts as (
      select account_id, round(sum(pay_amt), 2) as pay_amt
      from purchases
      where id in (select id from tmpPurchases)
      group by account_id
    )
    update accounts set cur_amt = cur_amt + (select pay_amt from tmp_accts where account_id=accounts.id)
    where id in (select account_id from tmp_accts);
    with tmp_stock as (
      select good_fingerprint, round(sum(qty), 3) as qty
      from purchase_items
      where pur_fingerprint in (select fingerprint from tmpPurchases)
      group by good_fingerprint
    )
    update goods set cur_stock = cur_stock - (select qty from tmp_stock where good_fingerprint=goods.fingerprint)
    where fingerprint in (select good_fingerprint from tmp_stock);
    drop table tmpPurchases;`,
  purchaseGetTotal:
    'select count(1) as pursCount, sum(pay_amt) as totalMoney from purchases ' +
    'where is_deleted=0 {0};',
  purchaseGetTotalWithGoods:
    `with tmp as(
      select distinct
      purchases.id,
      purchases.pay_amt
    from purchases
    inner join purchase_items on purchases.fingerprint = purchase_items.pur_fingerprint
    inner join goods on purchase_items.good_fingerprint = goods.fingerprint
    left join goods_ext_barcode on goods_ext_barcode.good_fingerprint = goods.fingerprint and goods_ext_barcode.is_deleted = 0
    where purchases.is_deleted = 0 {0}
    )

    select count(1) as pursCount,sum(pay_amt) as totalMoney from tmp;`,
  purchaseGetDayTotal:
    "select strftime('%Y-%m-%d', opt_date) as opt_date, sum(pay_amt) as dayTotalMoney from purchases " +
    'where is_deleted=0 {0};',
  purchaseGetDayTotalWithGoods:
    `with tmp as (
      select distinct
        purchases.id,
        strftime('%Y-%m-%d', purchases.opt_date) as opt_date,
        purchases.pay_amt
      from purchases
      inner join purchase_items on purchases.fingerprint = purchase_items.pur_fingerprint
      inner join goods on purchase_items.good_fingerprint = goods.fingerprint
      left join goods_ext_barcode on goods_ext_barcode.good_fingerprint = goods.fingerprint and goods_ext_barcode.is_deleted = 0
      where purchases.is_deleted=0 {0}
    )
    select opt_date, sum(pay_amt) as dayTotalMoney from tmp group by opt_date order by opt_date desc;`,
  purchaseGetDetails:
    "select purchases.id, purchases.uid, strftime('%Y-%m-%d', purchases.opt_date) as opt_date, purchases.code, purchases.in_out, " +
    'purchases.bill_amt, purchases.disc_amt, purchases.disc, purchases.pay_amt, purchases.owe_amt, purchases.remark, purchases.supplier_fingerprint, ' +
    'purchases.account_id, accounts.name as account_name, ' +
    "strftime('%Y-%m-%d %H:%M:%S', purchases.create_at) as create_at, strftime('%Y-%m-%d %H:%M:%S', purchases.revise_at) as revise_at, " +
    'purchases.is_deleted, purchases.is_synced, purchases.fingerprint, c.name as supplier_name ' +
    'from purchases ' +
    'inner join accounts on purchases.account_id=accounts.id ' +
    'left join suppliers c on purchases.supplier_fingerprint = c.fingerprint ' +
    'where purchases.is_deleted=0 {0};',
  purchaseGetDetailsWithGoods:
    `select distinct purchases.id, purchases.uid, strftime('%Y-%m-%d', purchases.opt_date) as opt_date, purchases.code, purchases.in_out,
    purchases.bill_amt, purchases.disc_amt, purchases.disc, purchases.pay_amt, purchases.owe_amt, purchases.remark, purchases.supplier_fingerprint,
    purchases.account_id, accounts.name as account_name,
    strftime('%Y-%m-%d %H:%M:%S', purchases.create_at) as create_at, strftime('%Y-%m-%d %H:%M:%S', purchases.revise_at) as revise_at,
    purchases.is_deleted, purchases.is_synced, purchases.fingerprint
    from purchases
    inner join accounts on purchases.account_id=accounts.id
    inner join purchase_items
    on purchases.fingerprint = purchase_items.pur_fingerprint
    inner join goods
    on purchase_items.good_fingerprint = goods.fingerprint
    left join goods_ext_barcode 
    on goods_ext_barcode.good_fingerprint = goods.fingerprint 
    and goods_ext_barcode.is_deleted = 0
    where purchases.is_deleted = 0 {0};
    `,
  inventory_select_maxid: `select ifnull((select seq from sqlite_sequence where name ='inventories'), 0) as id`,
  getInventoriesSeq: `select seq from sqlite_sequence where name ='inventories';`,
  inventory_batchPut_insert:
    `insert into inventories (uid,opt_date,code,account_qty,actual_qty,diff_qty,diff_amt,remark,is_deleted,is_synced,fingerprint,deal_type)
  values('{uid}', date('now', 'localtime'), '{code}', round({account_qty}, 3), round({actual_qty}, 3), round({diff_qty}, 3), '{diff_amt}', '{remark}',
  '{is_deleted}', '{is_synced}', '{fingerprint}', '{deal_type}');`,
  inventoriesInsert:
    `insert into inventories(uid, code, account_qty, actual_qty, diff_qty, diff_amt, deal_type, remark, fingerprint) values `,
  inventoriesInsertValues:
    `({uid}, {code}, round({accountQty}, 3), round({actualQty}, 3), round({diffQty}, 3), round({diffAmt}, 2), {dealType}, {remark}, {fingerprint})`,
  inventoryItemsInsert:
    `insert into inventory_items(account_qty, actual_qty, diff_qty, diff_amt, price, good_fingerprint, fingerprint, inventory_fingerprint) values `,
  inventoryItemsInsertValues:
    `(round({accountQty}, 3), round({actualQty}, 3), round({diffQty}, 3), round({diffAmt}, 2), round({price}, 6),
    {goodFingerprint}, {fingerprint}, {inventoryFingerprint})`,
  updateCurStockByFingerprint:
    `update goods set cur_stock = round({curStock}, 3) where fingerprint = {fingerprint};`,
  updatePurPriceByFingerprint:
    `update goods set pur_price = round({purPrice}, 6), is_synced=(case when pur_price = round({purPrice}, 6) then is_synced else 0 end) where fingerprint = {fingerprint};`,
  inventory_put:
    `insert into inventories(uid,opt_date,code,account_qty,actual_qty,diff_qty,diff_amt,is_deleted,is_synced,fingerprint,deal_type,remark)
    values('{uid}', date('now', 'localtime'), '{code}', round({account_qty}, 3), round({actual_qty}, 3), round({diff_qty}, 3),
    '{diff_amt}', '{is_deleted}', '{is_synced}', '{fingerprint}', '{deal_type}', '{remark}');
    insert into inventory_items(good_fingerprint, account_qty, actual_qty, diff_qty, price, diff_amt, is_deleted, is_synced,
    fingerprint, inventory_fingerprint)
    values('{good_fingerprint}', round({account_qty}, 3), round({actual_qty}, 3), round({diff_qty}, 3), '{price}',
    '{diff_amt}', '{is_deleted}', '{is_synced}', '{inventory_fingerprint}', '{fingerprint}');
    update goods set cur_stock = cur_stock + round({diff_qty}, 3) where fingerprint = '{good_fingerprint}';`,
  inventory_put_notupdatestock:
    `insert into inventories(uid,opt_date,code,account_qty,actual_qty,diff_qty,diff_amt,is_deleted,is_synced,fingerprint,deal_type)
    values('{uid}', date('now', 'localtime'), '{code}', round({account_qty}, 3), round({actual_qty}, 3), round({diff_qty}, 3),
    '{diff_amt}', '{is_deleted}', '{is_synced}', '{fingerprint}', '{deal_type}');
    insert into inventory_items(id, good_fingerprint, account_qty, actual_qty, diff_qty, price, diff_amt, is_deleted, is_synced,
    fingerprint, inventory_fingerprint)
    values(last_insert_rowid(), '{good_fingerprint}', round({account_qty}, 3), round({actual_qty}, 3), round({diff_qty}, 3), '{price}',
    '{diff_amt}', '{is_deleted}', '{is_synced}', '{inventory_fingerprint}', '{fingerprint}');`,
  inventoriesGetTotal:
    `select count(1) as inventoryCount, sum(diff_amt) as totalMoney
     from inventories
     where is_deleted=0 and deal_type=7 {0};`,
  inventoriesGetTotalWithGoods:
    `with tmp as (
       select distinct inventories.id, inventories.diff_amt
       from inventories
       inner join inventory_items on inventories.fingerprint = inventory_items.inventory_fingerprint
       inner join goods on inventory_items.good_fingerprint = goods.fingerprint
       LEFT JOIN goods_ext_barcode 
        on goods.fingerprint = goods_ext_barcode.good_fingerprint 
        and goods_ext_barcode.is_deleted = 0
       where inventories.is_deleted=0 and inventories.deal_type=7 {0}
       )
       select count(1) as inventoryCount, sum(diff_amt) as totalMoney  from tmp;
     `,
  inventoriesGetDayTotal:
    `select strftime('%Y-%m-%d', opt_date) as opt_date, sum(diff_amt) as dayTotalMoney
     from inventories
     where is_deleted=0 and deal_type=7 {0}
     group by opt_date
     order by opt_date desc;`,
  inventoriesGetDayTotalWithGoods:
    `with tmp as (
       select distinct strftime('%Y-%m-%d', inventories.opt_date) as opt_date, inventories.diff_amt, inventories.id
       from inventories
       inner join inventory_items on inventories.fingerprint = inventory_items.inventory_fingerprint
       inner join goods on inventory_items.good_fingerprint = goods.fingerprint
       LEFT JOIN goods_ext_barcode 
       on goods.fingerprint = goods_ext_barcode.good_fingerprint 
       and goods_ext_barcode.is_deleted = 0
       where inventories.is_deleted=0 and inventories.deal_type=7 {0}
      )
       select opt_date as opt_date, sum(diff_amt) as dayTotalMoney
       from tmp
       group by opt_date
       order by opt_date desc;
     `,
  inventoriesGetDetails:
    `select id, uid, strftime('%Y-%m-%d', opt_date) as opt_date, code, account_qty, actual_qty, diff_qty, diff_amt, remark,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as create_at, strftime('%Y-%m-%d %H:%M:%S', revise_at) as revise_at,
      is_deleted, is_synced, fingerprint
     from inventories
     where is_deleted=0 and deal_type=7 {0}
     order by create_at desc, id desc;`,
  inventoriesGetDetailsWithGoods:
    ` select distinct inventories.id, inventories.uid, strftime('%Y-%m-%d', inventories.opt_date) as opt_date, inventories.code,
       inventories.account_qty, inventories.actual_qty, inventories.diff_qty, inventories.diff_amt, inventories.remark,
       strftime('%Y-%m-%d %H:%M:%S', inventories.create_at) as create_at, strftime('%Y-%m-%d %H:%M:%S', inventories.revise_at) as revise_at,
       inventories.is_deleted, inventories.is_synced, inventories.fingerprint
       from inventories
       inner join inventory_items on inventories.fingerprint = inventory_items.inventory_fingerprint
       inner join goods on inventory_items.good_fingerprint = goods.fingerprint
       LEFT JOIN goods_ext_barcode 
       on goods.fingerprint = goods_ext_barcode.good_fingerprint 
       and goods_ext_barcode.is_deleted = 0
       where inventories.is_deleted=0 and inventories.deal_type=7 {0}
       order by inventories.create_at desc, inventories.id desc;
     `,
  inventoriesGetById:
    `select id, uid, strftime('%Y-%m-%d', opt_date) as opt_date, code, account_qty, actual_qty, diff_qty, diff_amt, remark,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as create_at, strftime('%Y-%m-%d %H:%M:%S', revise_at) as revise_at,
      is_deleted, is_synced, fingerprint, (
        select case when employee_number is null or trim(employee_number)='' then '管理员'
          else trim(employee_number)
          end as employee_number
        from clerks where uid=inventories.uid
      ) as employee_number
     from inventories where id={0};`,
  inventoryItemsGetByPid:
    `select distinct items.id, items.good_fingerprint, items.account_qty, items.actual_qty, items.diff_qty, items.price,
    items.diff_amt, items.is_deleted, items.is_synced, items.fingerprint, items.inventory_fingerprint,code,
    case when goods.is_deleted=1 then '(已删除) ' else '' end || goods.name as name, goods.specs
    from inventory_items as items
    left join (
      select distinct goods.id,goods.fingerprint,goods.pinyin,goods.code,goods_ext_barcode.ext_barcode,goods.unit_fingerprint,
          goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
      from goods
      LEFT JOIN goods_ext_barcode 
      on goods.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      union all
      select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,
          'zjsk' as pinyin,'-' as code,'-' as ext_barcode,'0' as unit_fingerprint,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,'' as first_letters,0
    ) as goods on items.good_fingerprint=goods.fingerprint
    where items.inventory_fingerprint='{0}'
    order by items.id;`,
  inventoriesDeleteBatch:
    `create temp table tmpInventories as
     select id from inventories where id in ({id}) and is_deleted!={isDel};
     update inventories set is_synced=0, is_deleted={isDel} where id in (select id from tmpInventories);
     update inventory_items set is_synced=0, is_deleted={isDel} where inventory_id in (select id from tmpInventories);
     with tmp_stock as (
       select good_id, round(sum(diff_qty), 3) as diff_qty
       from inventory_items
       where inventory_id in (select id from tmpInventories)
       group by good_id
     )
     update goods set cur_stock=
       case when {isDel}=1 then cur_stock - (select diff_qty from tmp_stock where good_id=goods.id)
         else cur_stock + (select diff_qty from tmp_stock where good_id=goods.id)
       end
     where id in (select good_id from tmp_stock);
     drop table tmpInventories;`,
  record_bills_getCount: `select count(1) as cnt from record_bills where is_deleted=0;`,
  record_bills_select:
    `select id, info, uid, is_deleted, is_synced, fingerprint,
      strftime('%Y-%m-%d %H:%M:%S', create_at) createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) reviseAt,
      strftime('%Y-%m-%d %H:%M:%f', sync_at) syncAt {column}
      from record_bills where is_deleted=0 {page};`,
  record_bills_insert: `insert into record_bills (info, uid, fingerprint) values ({info}, {uid}, {fingerprint});`,
  record_bills_update: `update record_bills set info={info}, revise_at=datetime('now', 'localtime'), is_synced=0 where id={id};`,
  record_bills_delete: `update record_bills set is_deleted=1, is_synced=0 where id={id};`,
  record_bills_truncate: `update record_bills set is_deleted=1, is_synced=0 where is_deleted=0;`,
  getRecordBillsById:
    `select id, info, create_at, revise_at, sync_at, uid, is_deleted, is_synced, fingerprint from record_bills where id={id};`,
  updateRecordBillsInfo:
    `update record_bills set info={info}, revise_at=datetime('now', 'localtime') where id={id};`,
  goodsSaleReport: `
    WITH s as (
      select good_fingerprint,
             round(sum(si.qty), 3) as salesnumber,
             round(sum(si.amt), 2) as totalsalesprice,
             round(sum(round(si.qty * si.pur_price, 2)), 2) as totalstockprice,
             round(sum(si.amt*si.disc), 2) as money,
             round(round(sum(si.amt*si.disc), 2)-round(sum(round(si.qty * si.pur_price, 2)), 2), 2) as profits,
             case when sum(si.amt*si.disc) <= 0 then null else round(round((sum(round(si.amt*si.disc, 2))-sum(round(si.qty*si.pur_price, 2))), 2) / round(sum(round(si.amt*si.disc, 2)), 2), 4) end as profitsRate
      from sales s
      inner join sale_items si on s.fingerprint = si.sale_fingerprint
            and s.is_deleted = 0
            {date}
      where s.is_deleted = 0
      group by good_fingerprint
  ),S2 as (
      select distinct	goods.id,goods.fingerprint,goods.pinyin,goods.code,goods.unit_fingerprint,
              goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
      from goods
      LEFT JOIN goods_ext_barcode
      on goods.fingerprint = goods_ext_barcode.good_fingerprint
      and goods_ext_barcode.is_deleted = 0
      where goods.fingerprint in (select good_fingerprint from s)
            {goods}
            {weighUnits} 
      union all
      select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,
          '16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,
          '' as first_letters,0 as is_deleted
      where 1=1 {zhiJieGoods}
  )
  select g.code,
        case when g.is_deleted=1 then '(已删除) ' else '' end || g.name as name,
        ifnull(tt.name || ' / ', '') || t.name as category,
        ifnull(g.cur_stock,0) as stock,
        s.*,
        supplier_name as supplierName
  from s
  inner join (
      SELECT *
      from S2 
      where fingerprint in (select good_fingerprint from s)
  ) g on s.good_fingerprint = g.fingerprint
  left join types as t on t.fingerprint=g.type_fingerprint
  left join types tt on t.parent_fingerprint = tt.fingerprint
  left join (
      select gs.fingerprint,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name,
             s.contacter as supplier_contacter,s.mobile as supplier_mobile
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
  ) tmp_suppliers on tmp_suppliers.fingerprint = g.fingerprint
  where 1=1 {notWeighUnits} {supplier}
  {orderBy}
  limit ({currentPage}-1)*{pageSize},{pageSize};
  `,
  goodsSaleReportCount: `
    WITH s as (
      select good_fingerprint
      from sales s
      inner join sale_items si on s.fingerprint = si.sale_fingerprint
            and s.is_deleted = 0
            {date}
      where s.is_deleted = 0
      group by good_fingerprint
    ),S2 as (
        select distinct goods.id, goods.fingerprint,goods.pinyin,goods.code,
              goods.unit_fingerprint,goods.type_fingerprint,goods.name,goods.specs,
              goods.cur_stock,goods.first_letters,goods.is_deleted
        from goods
        LEFT JOIN goods_ext_barcode on goods.fingerprint = goods_ext_barcode.good_fingerprint 
            and goods_ext_barcode.is_deleted = 0
        where goods.fingerprint in (select good_fingerprint from s)
              {goods}
              {weighUnits} 
        union all
      select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,
              '16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,
              '' as first_letters,0 as is_deleted
            where 1=1 {zhiJieGoods}
    )
    select count(1) as count
    from s
    inner join (
        SELECT * from S2 where fingerprint in (select good_fingerprint from s)
    ) g on s.good_fingerprint = g.fingerprint
    left join types as t on t.fingerprint=g.type_fingerprint
    left join types tt on t.parent_fingerprint = tt.fingerprint
    left join (
      select gs.fingerprint,s.fingerprint as supplier_fingerprint
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = g.fingerprint
    where 1=1 {notWeighUnits} {supplier}`,
  goodsSaleReportSum: `
    WITH s as (
      select  good_fingerprint,
              sum(si.qty) AS salesnumber,
              sum(si.amt) AS totalsalesprice,
              round(sum(round(si.qty * si.pur_price, 2)), 2) as totalstockprice,
              round(sum(si.amt*si.disc), 2) as money,
              round(round(sum(si.amt*si.disc), 2)-round(sum(round(si.qty * si.pur_price, 2)), 2), 2) as receipt
      from sales s
      inner join sale_items si on s.fingerprint = si.sale_fingerprint
            and s.is_deleted = 0
            {date}
      where s.is_deleted = 0
      group by good_fingerprint
    ),S2 as (
      select distinct	goods.id,goods.fingerprint,goods.pinyin,goods.code,goods.unit_fingerprint,
              goods.type_fingerprint,goods.name,goods.specs,goods.cur_stock,goods.first_letters,goods.is_deleted
      from goods
      LEFT JOIN goods_ext_barcode on goods.fingerprint = goods_ext_barcode.good_fingerprint
           and goods_ext_barcode.is_deleted = 0
      where goods.fingerprint in (select good_fingerprint from s)
            {goods}
            {weighUnits} 
      union all
      select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,
          '16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'{"unlock":{},"lock":{}}' as specs, 0 as cur_stock,
          '' as first_letters,0 as is_deleted
      where 1=1 {zhiJieGoods}
    )
    select round(sum(s.salesnumber), 3) AS salesnumber,
           round(sum(s.totalsalesprice), 2) AS totalsalesprice,
           sum(s.totalstockprice) AS totalstockprice,
           sum(s.money) AS money,
           sum(s.receipt)  AS receipt
    from s
    inner join (
        SELECT *
        from S2 
        where fingerprint in (select good_fingerprint from s)
    ) g on s.good_fingerprint = g.fingerprint
    left join types as t on t.fingerprint=g.type_fingerprint
    left join types tt on t.parent_fingerprint = tt.fingerprint
    left join (
      select gs.fingerprint,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name,
             s.contacter as supplier_contacter,s.mobile as supplier_mobile
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = g.fingerprint
    where 1=1 {notWeighUnits} {supplier};
  `,
  // 库存变动明细
  goodsCountSelectByName:
    `select count(distinct goods.id) as cnt  
      from goods
      LEFT JOIN goods_ext_barcode 
      on goods.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      where goods.is_deleted=0 {wheres}
    `,
  goodsSelectByName:
    `select distinct goods.id,
            goods.name,
            goods.first_letters,
            goods.pinyin,
            goods.major_code,
            goods.code,
            goods.image,
            goods.pur_price,
            goods.sale_price,
            goods.vip_price,
            IFNULL(goods.cur_stock,0) as cur_stock,
            goods.init_stock,
            goods.init_price,
            goods.init_amt,
            goods.min_stock,
            goods.max_stock,
            goods.has_image,
            goods.remark,
            goods.packing,
            goods.specs,
            goods.is_vip_disc,
            goods.is_deleted,
            goods.is_new,
            goods.is_synced,
            goods.type_fingerprint,
            goods.unit_fingerprint,
            goods.fingerprint,
            goods.create_at,
            goods.revise_at,
            goods.sync_at,
            goods.stock_sync_at 
    from goods
    LEFT JOIN goods_ext_barcode 
    on goods.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    where goods.is_deleted=0 {wheres}
    order by goods.id 
    limit {pageSize} offset {pageSize} * ({page}-1);`,
  getSaleItemsByGoodId:
    `select sales.id, 'sales' as 'tableName',
      case when sales.in_out=1 then '商品销售' else '客户退款' end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', sales.create_at) as create_at,
      -items.qty as qty,
      ifnull((case when sales.in_out=1 then '销售单号：' else '退款单号：' end) || sales.code, '') as remark
     from sales
     inner join sale_items as items
     on sales.fingerprint=items.sale_fingerprint
     where sales.is_deleted=0
     and items.is_deleted=0
     and items.good_fingerprint='{goodId}'
     {wheres}
     order by sales.create_at desc
     {limit};`,
  getPurItemsByGoodId:
    `select pur.id, 'purchases' as 'tableName',
      case when pur.in_out=1 then '进货' else '退货' end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', pur.create_at) as create_at,
      items.qty,
      (case when pur.in_out=1 then '进货单号：' else '退货单号：' end) || pur.code as remark
     from purchases as pur
     inner join purchase_items as items
     on pur.fingerprint=items.pur_fingerprint
     where pur.is_deleted=0
     and items.is_deleted=0
     and items.good_fingerprint='{goodId}'
     {wheres}
     order by pur.create_at desc
     {limit};`,
  getInventoryItemsByGoodId:
    `select inv.id, 'inventories' as 'tableName',
      case
       when inv.deal_type=1 or inv.deal_type=6 then '初始库存'
       when inv.deal_type=9 then '初始库存(抵消)'
       when inv.deal_type=2 then '库存修改'
       when inv.deal_type=3 or inv.deal_type=4 then '会员存取'
       when inv.deal_type=5 then '兑换礼品'
       when inv.deal_type=7 then '库存盘点'
       when inv.deal_type=8 then '拆包'
       else ''
      end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', inv.create_at) as create_at,
      items.diff_qty as qty,
      case
       when inv.deal_type=7 then '盘点单号：' || inv.code
       when inv.deal_type=3 or inv.deal_type=4 or inv.deal_type=5 then '会员：' || ifnull(inv.remark, '')
       else ''
      end as remark
     from inventories as inv
     inner join inventory_items as items
     on inv.fingerprint=items.inventory_fingerprint
     where inv.is_deleted=0
     and items.is_deleted=0
     and items.good_fingerprint='{goodId}'
     {wheres}
     order by inv.create_at desc
     {limit};`,
  getAllByGoodId:
    `with tmp_sales as (
      select sales.id, 'sales' as 'tableName',
        case when sales.in_out=1 then '商品销售' else '客户退款' end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', sales.create_at) as create_at,
        -items.qty as qty,
        ifnull((case when sales.in_out=1 then '销售单号：' else '退款单号：' end) || sales.code,'') as remark
      from sales
      inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
      where sales.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint='{goodId}'
      {wheres_sales}
    ), tmp_purs as (
      select pur.id, 'purchases' as 'tableName',
        case when pur.in_out=1 then '进货' else '退货' end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', pur.create_at) as create_at,
        items.qty,
        (case when pur.in_out=1 then '进货单号：' else '退货单号：' end) || ifnull(pur.code,'') as remark
      from purchases as pur
      inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
      where pur.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint='{goodId}'
      {wheres_pur}
    ), tmp_invs as (
      select inv.id, 'inventories' as 'tableName',
        case
        when inv.deal_type=1 or inv.deal_type=6 then '初始库存'
        when inv.deal_type=9 then '初始库存(抵消)'
        when inv.deal_type=2 then '库存修改'
        when inv.deal_type=3 or inv.deal_type=4 then '会员存取'
        when inv.deal_type=5 then '兑换礼品'
        when inv.deal_type=7 then '库存盘点'
        when inv.deal_type=8 then '拆包'
        else ''
        end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', inv.create_at) as create_at,
        items.diff_qty as qty,
        case
        when inv.deal_type=7 then '盘点单号：' || inv.code
        when inv.deal_type=3 or inv.deal_type=4 or inv.deal_type=5 then '会员：' || ifnull(inv.remark, '')
        else ''
        end as remark
      from inventories as inv
      inner join inventory_items as items
      on inv.fingerprint=items.inventory_fingerprint
      where inv.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint='{goodId}'
      {wheres_inv}
    ), tmp as (
      select id, tableName, changeType, create_at, qty, remark, 3 as sort from tmp_sales union all
      select id, tableName, changeType, create_at, qty, remark, 1 from tmp_purs union all
      select id, tableName, changeType, create_at, qty, remark, 2 from tmp_invs
    )
    select * from tmp order by create_at desc, sort desc {limit};`,
  getTotalInfoByGoodId:
    `with tmp_goods as (
      select goods.name, goods.cur_stock || ' ' || ifnull(units.name, '') as cur_stock, goods.fingerprint
      from goods
      left join units on goods.unit_fingerprint=units.fingerprint
      where goods.fingerprint='{goodId}'
    ), tmp_sales as (
      select sales.in_out, sum(-items.qty) as qty
      from sales
      inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
      where sales.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint=tmp_goods.fingerprint
      {wheres_sales}
      group by sales.in_out
    ), tmp_purs as (
      select sum(items.qty) as qty
      from purchases as pur
      inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
      where pur.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint=tmp_goods.fingerprint
      {wheres_pur}
    ), tmp_invs as (
      select inv.deal_type, sum(items.diff_qty) as qty
      from inventories as inv
      inner join inventory_items as items on inv.fingerprint=items.inventory_fingerprint
      where inv.is_deleted=0
      and items.is_deleted=0
      and items.good_fingerprint=tmp_goods.fingerprint
      {wheres_inv}
      group by inv.deal_type
    )
    select name, cur_stock, fingerprint as goodFingerprint,
      (select qty from tmp_sales where in_out=1) as salesCount,
      (select qty from tmp_sales where in_out=2) as salesBackCount,
      (select qty from tmp_purs) as pursAndBackCount,
      (select qty from tmp_invs where deal_type=7) as invsCount,
      (select sum(qty) from tmp_invs where deal_type in (3,4)) as accessCount,
      (select qty from tmp_invs where deal_type=5) as exchangeCount
    from tmp_goods`,
  getSalesInfoById:
    `select sales.id, sales.in_out, sales.code,
      case when clerks.employee_number is null or trim(clerks.employee_number)='' then '管理员'
        else trim(clerks.employee_number)
      end as employee_number,
      sales.vipname,
      sales.bill_amt,
      sales.pay_amt,
      sales.bill_amt-sales.pay_amt as discountAmount,
      sales.change_amt,
      accounts.name as acctName,
      sales.remark,
      sales.fingerprint
    from sales
    left join clerks
    on sales.uid=clerks.uid
    left join accounts
    on sales.account_id=accounts.id
    where sales.id={0};`,
  getSaleItemsInfoById:
    `select goods.name, items.qty, items.price,
      (items.item_disc*100) || '%' as disc,
      items.amt as amt
    from sale_items as items
    inner join goods on items.good_fingerprint=goods.fingerprint
    where items.sale_fingerprint='{0}'
    order by items.id;`,
  getPurchasesInfoById:
    `select pur.id, pur.in_out, pur.code,
      case when clerks.employee_number is null or trim(clerks.employee_number)='' then '管理员'
        else trim(clerks.employee_number)
      end as employee_number,
      suppliers.name as supplierName,
      accounts.name as acctName,
      pur.bill_amt,
      pur.pay_amt,
      (pur.disc*100) as disc,
      pur.remark,
      pur.fingerprint
    from purchases as pur
    left join clerks
    on pur.uid=clerks.uid
    left join suppliers on pur.supplier_fingerprint=suppliers.fingerprint
    left join accounts
    on pur.account_id=accounts.id
    where pur.id={0};`,
  getPurItemsInfoById:
    `select items.id, goods.name, items.price, goods.code, units.name as unitsName,
      items.qty, items.amt
    from purchase_items as items
    inner join goods on items.good_fingerprint=goods.fingerprint
    left join units on goods.unit_fingerprint=units.fingerprint
    where items.pur_fingerprint='{0}'
    order by items.id;`,
  getInventoriesInfoById:
    `select inv.id, inv.code,
      case when clerks.employee_number is null or trim(clerks.employee_number)='' then '管理员'
        else trim(clerks.employee_number)
      end as employee_number,
      inv.account_qty, inv.actual_qty, inv.diff_qty, inv.diff_amt, inv.remark
      ,inv.fingerprint
    from inventories as inv
    left join clerks
    on inv.uid=clerks.uid
    where inv.id={0};`,
  getInvItemsInfoById:
    `select items.id, goods.name, items.price,
      items.account_qty, items.actual_qty, items.diff_qty, items.diff_amt
    from inventory_items as items
    inner join goods on items.good_fingerprint=goods.fingerprint
    where items.inventory_fingerprint='{0}'
    order by items.id;`,
  // 促销管理
  adSearchEdit:
    `select ad.id,ad.template_fingerprint,ad.picture_path_pc,ad.picture_path_mobile,ad.comments,ad.is_deleted,ad.is_default,replace(replace(tem.html,
    '{picture_path}',ad.picture_path_pc),'{comments}',ifnull(ad.comments,'')) as html from advertisements ad
    inner join templates tem on ad.template_fingerprint = tem.fingerprint where ad.is_deleted = 0 and ad.is_default = 0 order by ad.is_default, ad.id;`,
  adSearchShow:
    `select ad.id,ad.template_fingerprint,ad.picture_path_pc,ad.picture_path_mobile,ad.comments,ad.is_deleted,ad.is_default,
    replace(replace(tem.html,'{picture_path}',ad.picture_path_pc),'{comments}',ifnull(ad.comments,'')) as html from advertisements ad
    inner join templates tem on ad.template_fingerprint = tem.fingerprint where ad.is_deleted = 0
    and ((ad.is_default=0 and exists(select 1 from advertisements where is_default=0 and is_deleted=0))
    or (ad.is_default=1 and not exists(select 1 from advertisements where is_default=0 and is_deleted=0))) order by ad.is_default, ad.id;`,
  adDelete: `update advertisements set is_deleted=1,is_synced=0 where id={id};`,
  adUpdate:
    `update advertisements set template_fingerprint = {template_fingerprint},picture_path_pc = '{picture_path_pc}',picture_path_mobile = '{picture_path_mobile}',
    comments='{comments}',is_synced=0 where id = {id};`,
  adInsert:
    `insert into advertisements (template_fingerprint,picture_path_pc,picture_path_mobile,comments,fingerprint) values
    ({template_fingerprint},'{picture_path_pc}','{picture_path_mobile}','{comments}','{fingerprint}');`,
  adTempSearch: `select template_fingerprint,replace(html,'{comments}','广告语') as html from templates where is_deleted = 0 order by template_fingerprint;`,
  adSearchMobile:
    `select ad.id,ad.template_fingerprint,ad.picture_path_pc,ad.picture_path_mobile,ad.comments,ad.is_deleted,ad.is_default,
    replace(replace(tem.html,'{picture_path}',ad.picture_path_mobile),'{comments}',ifnull(ad.comments,'')) as html
    from advertisements ad inner join templates tem on ad.template_fingerprint = tem.fingerprint
    where ad.is_deleted = 0 and ad.is_default = 0 order by ad.is_default, ad.id;`,
  // ----------------------交接班 begin----------------------
  // 交接班结束时间
  getShiftHistories:
    ` select strftime('%Y-%m-%d %H:%M:%S', begin_date) as end_date
    from shifthistories
    where uid='{uid}' and begin_date > '{start}'
    order by begin_date limit 1;
  `,
  // 商品销售
  changeProductSales:
    `with tmp_sales as (
      select id, account_id, pay_amt, fingerprint
      from sales
      where is_deleted=0
      and uid={uid}
      and create_at between '{start}' and '{end}'
    ), tmp_all as (
      select account_id, pay_amt from tmp_sales where account_id!=99
      union all
      select account_id, pay_amt from sale_blend_pays where sale_fingerprint in (select fingerprint from tmp_sales where account_id=99)
    ), tmp_goodsales as (
      select
        pay_amt,
        case account_id
          when 1 then '现金支付'
          when 2 then '银行存款'
          when 3 then '线下支付'
          when 4 then '线下支付'
          when 5 then '线下支付'
          when 6 then '会员卡支付'
          when 7 then '扫码付-微信'
          when 8 then '扫码付-支付宝'
          else null
        end as payType,
        case account_id when 1 then 1
            when 2 then 2
            when 3 then 5
            when 4 then 5
            when 5 then 5
            when 6 then 6
            when 7 then 4
            when 8 then 3
            else null
        end as id
      from tmp_all
    )
    select round(sum(pay_amt), 2) as money, count(1) as count, payType, id, '商品销售' as subType
    from tmp_goodsales
    group by payType, id
    order by id;`,
  // 支付统计，商品销售
  changePayProductSales:
    `with tmp_sales as (
      select id, account_id, pay_amt, fingerprint
      from sales
      where is_deleted=0
      and uid={uid}
      and create_at between '{start}' and '{end}'
    ), tmp_all as (
      select account_id, pay_amt from tmp_sales where account_id!=99
      union all
      select account_id, pay_amt from sale_blend_pays where sale_fingerprint in (select fingerprint from tmp_sales where account_id=99)
    ), tmp_goodsales as (
      select
        pay_amt,
        case account_id
          when 1 then '现金支付'
          when 2 then '银行存款'
          when 3 then '线下支付-POS'
          when 4 then '线下支付-微信'
          when 5 then '线下支付-支付宝'
          when 6 then '会员卡支付'
          when 7 then '扫码付-微信'
          when 8 then '扫码付-支付宝'
          else null
        end as subType,
        case account_id when 1 then 1
            when 2 then 2
            when 3 then 3
            when 4 then 4
            when 5 then 5
            when 6 then 6
            when 7 then 7
            when 8 then 8
            else null
        end as id
      from tmp_all
    )
    select round(sum(pay_amt), 2) as money, count(1) as count, subType, id, '商品销售' as payType
    from tmp_goodsales
    group by subType, id
    order by id;`,
  // 应收现金
  changeCashReceivable:
    `with tmp as (
    select id, pay_amt, disc_amt, in_out, account_id, fingerprint
    from sales
    where is_deleted=0
    and uid={uid}
    and create_at between '{start}' and '{end}'
    and account_id in (1, 99)
  ), tmp_all as (
    select
      pay_amt as money
      ,'商品销售' as type
      ,8 as id
    from tmp
    where in_out=1
    and account_id = 1
    union all
    select
      blend.pay_amt as money
      ,'商品销售' as type
      ,8 as id
    from tmp as sales
    inner join sale_blend_pays as blend
    on sales.fingerprint = blend.sale_fingerprint
    where sales.in_out=1
    and sales.account_id = 99
    and blend.account_id = 1
    union all
    select
      abs(disc_amt) as money
      ,'商品退款' as type
      ,10 as id
    from tmp
    where in_out=2
    and account_id = 1
  )
  select
    sum(money) as money
    ,count(type) as count
    ,type
    ,id as num
  from tmp_all
  group by type
  order by id;`,
  // 插入交接班履历数据
  insert_shift_histories: `insert into shifthistories
  (uid, employee_number, name, begin_date, create_by, fingerprint)
  VALUES (
  {uid},case when '{employee_number}'='null' then null else '{employee_number}' end,'{name}',strftime('%Y-%m-%d %H:%M:%S','now','localtime'),'{create_by}','{fingerprint}'
  );`,
  // 商品销售报表
  changeProductSalesReport: `select
    g.code
    ,g.specs
    ,g.name
    ,ifnull(tt.name || ' / ', '') || t.name as category
    ,round (sum(si.qty), 3) as count
    ,round(sum(round(si.amt*si.disc, 2)), 2) as money
  from sales as s
  inner join sale_items as si on s.fingerprint=si.sale_fingerprint
  inner join (
  select id,fingerprint,type_fingerprint,name,pinyin,code,unit_fingerprint,specs,cur_stock from goods
  union all
  select 0 as id,'646b141adf1d4e1b880d373a00dd3025' as fingerprint,'16c8c8f0082449d33ff8d1a2ada934f5' as type_fingerprint,'直接收款' as name,'zjsk' as pinyin,'-' as code,'0' as unit_fingerprint,'{"unlock":{},"lock":{}}' as specs,0 as cur_stock
  ) as g on g.fingerprint=si.good_fingerprint
  left join types as t on t.fingerprint=g.type_fingerprint
  left join types tt on t.parent_fingerprint = tt.fingerprint
  where 1=1
  and uid='{uid}'
  and s.is_deleted = 0
  and s.create_at between '{start}' and '{end}'
  group by g.code ,g.name ,t.name
  order by g.name,g.code;`,
  // 交接班并登出，插入交接班履历数据
  change_add_shift_histories: `update shifthistories
    set
    sales_amt ='{sales_amt}'
    , pay_amt ='{pay_amt}'
    , vip_charge ='{vip_charge}'
    , cash_amt ='{cash_amt}'
    , remark ='{remark}'
    , revise_by ='{revise_by}'
    , end_date ='{end_date}'
    , is_synced =0
    where fingerprint='{fingerprint}';`,
  // 交接班报表
  shiftHistoryReports:
    `select id, uid, case when employee_number is null or trim(employee_number)='' then '管理员' else name end as name,
        ifnull(employee_number, '') as employee_number,
        ifnull(strftime('%Y-%m-%d %H:%M:%S', begin_date), '') as begin_date,
        ifnull(strftime('%Y-%m-%d %H:%M:%S', end_date), '') as end_date,
        ifnull(sales_amt, '0') as sales_amt, ifnull(pay_amt, '0') as pay_amt, ifnull(vip_charge, '0') as vip_charge, ifnull(cash_amt, '0') as cash_amt, create_by, revise_by, is_synced, fingerprint,
        strftime('%Y-%m-%d %H:%M:%S', create_at) as create_at,
        strftime('%Y-%m-%d %H:%M:%S', revise_at) as revise_at,
        ifnull(remark, '') as remark
     from shifthistories {wheres}
     order by begin_date desc
     limit {limit} offset {offset};`,
  shiftHistoryReportsCount:
    `select count(1) as totalCount from shifthistories {wheres};`,
  shiftHistoryReportsTotal:
    `select ifnull(sum(sales_amt), '0') as salesAmtTotal, ifnull(sum(pay_amt), '0') as payAmtTotal,
      ifnull(sum(vip_charge), '0') as vipChargeTotal, ifnull(sum(cash_amt), '0') as cashAmtTotal
      from shifthistories {wheres};`,
  // ----------------------交接班 end----------------------
  // ----------------------供应商 begin--------------------
  // 检索供应商
  searchSuppliers:
    `select id,name,contacter,mobile,addr,coalesce(remark,'-') as remark,is_deleted,fingerprint
  from suppliers {wheres}
  order by create_at desc
  limit {limit} offset {offset};`,
  // 根据条件，获取供应商总数
  getSupplierCount:
    `select count(*) as cnt
  from suppliers {wheres};`,
  // 更新供应商
  updateSupplier:
    `update suppliers
  set name={name}
  , contacter={contacter}
  , mobile={mobile}
  , addr={addr}
  , is_synced==0
  , remark={remark}
  , revise_by ={revise_by}
  where fingerprint={fingerprint}`,
  // 更新供应商状态
  updateSupplierStatus:
    `update suppliers
  set is_deleted={status}
  , is_synced==0
  , revise_by ={revise_by}
  where fingerprint={fingerprint};
  update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime')
  where is_del=0 and supplier_fingerprint = {fingerprint};`,
  // 新增供应商
  insertSupplier:
    `insert into suppliers(name, contacter, mobile, addr, remark, create_by, create_at, revise_by, revise_at, is_deleted, is_synced, fingerprint)
  VALUES ({name}, {contacter}, {mobile}, {addr}, {remark}, {create_by}, strftime('%Y-%m-%d %H:%M:%S','now','localtime'), NULL, NULL, 0, 0, {fingerprint});`,
  // 获取供应商下拉列表
  getSupplierDropDownList: `select id, name, fingerprint from suppliers where is_deleted=0 order by create_at desc;`,
  // 获取供应商下拉列表
  getDetailSupplierDropDownList: `select id, name, fingerprint from suppliers order by create_at desc;`,
  // 根据供应商名字，获取供应商
  getSupplierByName:
    `select *
  from suppliers
  where name={name};`,
  // 根据fingerprint获取供应商详情
  getSupplierByFingerprint: `select * from suppliers where fingerprint='{fingerprint}';`,
  // 获取商品与供应商关系
  getGoodsSupplierByFingerprint: `SELECT * FROM goods_suppliers WHERE supplier_fingerprint = '{fingerprint}' and is_del = 0 limit 1`,
  // 编辑供应商、判断供应商
  getSupplierByNameNotId:
    `select *
  from suppliers
  where name={name}
  and fingerprint<>{fingerprint};`,
  // ----------------------供应商 end----------------------
  // ----------------------商品管理 begin------------------
  // 批量设置商品状态
  setGoodsStatus:
    `update goods
  set is_deleted={goodsStatus},
  type_id=(select case when (select is_deleted from types where id = goods.type_id) = 0 then type_id else 2 end),
  is_synced = 0
  where id in({goodsList});`,
  // 批量设置商品分类
  setGoodsType:
    `update goods
  set type_fingerprint='{goodsType}'
  ,is_synced = 0
  where fingerprint in('{goodsList}');`,
  // 批量设置商品单位
  setGoodsUnit:
    `update goods
  set unit_fingerprint='{goodsUnit}'
  ,is_synced = 0
  where id in({goodsList});`,
  // 设置商品规格
  upGoodsSpecs: `update goods set specs = {specs}, is_synced = 0 where id = {id};`,
  // 批量设置商品品牌
  setGoodsBrand:
    `update goods
  set unit_id={goodsUnit}
  ,is_synced = 0
  where id in({goodsList});`,
  // 隐藏未启用商品
  hideNotEnabledGoods: 'a.is_deleted=0 and',
  // ----------------------商品管理 end--------------------
  // ---------------------- 库存统计 begin ---------------------------
  stockStatisticsPriceReports:
    `with goods_now as(
      SELECT id, name, type_fingerprint, round(pur_price*cur_stock, 2) as purPrice, round(sale_price*cur_stock, 2) as salePrice,  cur_stock
      FROM goods
      where is_deleted = 0
    ),
    stockStatisticsPrice as (
      select type_fingerprint,sum(cur_stock) as curStockSum,sum(salePrice) as salePriceSum,sum(purPrice) as purPriceSum
      from goods_now
      group by type_fingerprint
    )
    select
      ty.id,ifnull(tyy.name || ' / ', '') || ty.name as name,stock.curStockSum,stock.salePriceSum,stock.purPriceSum
    from stockStatisticsPrice stock
    inner join types ty on stock.type_fingerprint = ty.fingerprint
    left join types tyy on ty.parent_fingerprint = tyy.fingerprint
    order by stock.type_fingerprint
    limit {limit} offset {offset};`,
  stockStatisticsPriceReportsTotal:
    `with goods_now as(
      SELECT id, name, type_fingerprint, round(pur_price*cur_stock, 2) as purPrice, round(sale_price*cur_stock, 2) as salePrice,  cur_stock
      FROM goods
      where is_deleted = 0
    )
    select
      ifnull(sum(cur_stock), 0) as curStockAmtTotal,
      ifnull(sum(salePrice), 0) as salePriceAmtTotal,
      ifnull(sum(purPrice), 0) as purPriceAmtTotal
    from
      goods_now;`,
  stockStatisticsPriceReportsCount:
    `select count(DISTINCT a.type_fingerprint) as totalCount from goods a
    inner join types b on a.type_fingerprint = b.fingerprint
    where a.is_deleted = 0;`,
  stockStatisticsPriceReportsExport:
    `with goods_now as(
      SELECT id, name, type_fingerprint, (pur_price*cur_stock) as purPrice, (sale_price*cur_stock) as salePrice,  cur_stock
      FROM goods
      where is_deleted = 0
    ),
    stockStatisticsPrice as (
      select type_fingerprint,sum(cur_stock) as curStockSum,sum(salePrice) as salePriceSum,sum(purPrice) as purPriceSum
      from goods_now
      group by type_fingerprint
    )
    select
      ty.id,ifnull(tyy.name || ' / ', '') || ty.name as name,stock.curStockSum,stock.salePriceSum,stock.purPriceSum
    from stockStatisticsPrice stock
    inner join types ty on stock.type_fingerprint = ty.fingerprint
    left join types tyy on ty.parent_fingerprint = tyy.fingerprint
    order by stock.type_fingerprint;`,
  // ---------------------- 库存统计 end ---------------------------
  // ---------------------- 商品库存查询 begin ----------------------
  stockGoodsReports:
    `select distinct a.id, a.name,a.code,a.type_fingerprint,ifnull(bb.name || ' / ', '') || b.name as typeName, a.sale_price, a.pur_price, a.cur_stock,
      a.vip_price,a.unit_fingerprint,c.name as unitName ,a.fingerprint, a.specs,supplier_name as supplierName
      ,supplier_contacter as supplierContacter,supplier_mobile as supplierMobile,supplier_addr as supplierAddr,supplier_remark as supplierRemark
    from goods a
    left join types b on a.type_fingerprint = b.fingerprint
    left join types bb on b.parent_fingerprint = bb.fingerprint
    left join units c on a.unit_fingerprint = c.fingerprint
    left join (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile,s.addr as supplier_addr,s.remark as supplier_remark
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = a.fingerprint
    LEFT JOIN goods_ext_barcode 
    on a.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    where a.is_deleted = 0
    {wheres}
    {orderBy}
    limit {limit} offset {offset};`,
  stockGoodsReportsTotal:
    `with tmp as(
      select distinct a.id,
          a.cur_stock,
          round(a.pur_price*a.cur_stock, 2) as purPriceAmtTotal,
          round(a.sale_price*a.cur_stock, 2) as salePriceAmtTotal,
          round(a.vip_price*a.cur_stock, 2) as vipPriceAmtTotal
      from goods a
      LEFT JOIN goods_ext_barcode 
      on a.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      left join types b on a.type_fingerprint = b.fingerprint
      left join (
        select gs.fingerprint
        ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
        ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
        from goods_suppliers gs
        inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
        where gs.is_del=0 and s.is_deleted=0
      ) tmp_suppliers on tmp_suppliers.fingerprint = a.fingerprint
      where a.is_deleted = 0 {wheres}
    )
    SELECT count(id) as goodAmtTotal,
           sum(cur_stock) as curStockAmtTotal,
           sum(purPriceAmtTotal) as purPriceAmtTotal,
           sum(salePriceAmtTotal) as salePriceAmtTotal,
           sum(vipPriceAmtTotal) as vipPriceAmtTotal
    from tmp;`,
  stockGoodsReportsCount:
    `select count(distinct a.id) as totalCount
    from goods a
    LEFT JOIN goods_ext_barcode 
    on a.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    left join types b on a.type_fingerprint = b.fingerprint
    left join (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = a.fingerprint
    where a.is_deleted = 0 {wheres};`,
  stockGoodsReportsExport:
    `select distinct a.name,a.code,ifnull(bb.name || ' / ', '') || b.name as typename, a.sale_price, a.pur_price,
    a.vip_price, a.cur_stock, ifnull(c.name, '') as unitname, a.specs,supplier_name as supplierName
    from goods a
    LEFT JOIN goods_ext_barcode 
    on a.fingerprint = goods_ext_barcode.good_fingerprint 
    and goods_ext_barcode.is_deleted = 0
    left join types b on a.type_fingerprint = b.fingerprint
    left join types bb on b.parent_fingerprint = bb.fingerprint
    left join units c on a.unit_fingerprint = c.fingerprint
    left join (
      select gs.fingerprint
      ,s.id as supplier_id,s.fingerprint as supplier_fingerprint,s.name as supplier_name
      ,s.contacter as supplier_contacter,s.mobile as supplier_mobile
      from goods_suppliers gs
      inner join suppliers s on gs.supplier_fingerprint=s.fingerprint
      where gs.is_del=0 and s.is_deleted=0
    ) tmp_suppliers on tmp_suppliers.fingerprint = a.fingerprint
    where a.is_deleted = 0
    {wheres}
    {orderBy};`,
  // ---------------------- 商品库存查询 end ----------------------
  // 获取当天库存有变动的商品
  getGoodsByStockChanged:
    `with tmp_purs as (
      select distinct good_id from purchase_items
      where pur_id in (select id from purchases where opt_date=date('now','localtime'))
    ), tmp_sales as (
      select distinct good_id from sale_items
      where sale_fingerprint in (select fingerprint from sales where opt_date=date('now','localtime'))
    ), tmp_inventories as (
      select distinct good_id from inventory_items
      where inventory_id in (select id from inventories where opt_date=date('now','localtime'))
    )

    select fingerprint, cur_stock
    from goods
    where id in (
      select good_id from tmp_purs union
      select good_id from tmp_sales union
      select good_id from tmp_inventories
    );`,
  // 获取 sales、sale_items、purchases、purchase_items、inventories、inventory_items 的条数
  getSalePurInvCount:
    `select (select count(1) from sales) as salesCount,
    (select count(1) from sale_items) as saleItemsCount,
    (select count(1) from purchases) as pursCount,
    (select count(1) from purchase_items) as purItemsCount,
    (select count(1) from inventories) as invsCount,
    (select count(1) from inventory_items) as invItemsCount;`,
  purchase_export_data:
    `with tmp as (
    select
        purchases.id as id,
        purchases.code as code,
        purchases.create_at as create_time,
        ifnull(suppliers.name, '-') as supplier_name,
        case when clerks.employee_number is null or trim(clerks.employee_number)='' then '管理员'
          else trim(clerks.employee_number)
        end as employeeNumber,
        case when goods.is_deleted=1 then '(已删除) ' else '' end || goods.name as item_name,
        purchase_items.price as purPrice,
        goods.code as item_code,
        ifnull(units.name,'-') as unit_name,
        round(purchase_items.qty,3) as qty,
        round(purchase_items.amt,2) as price,
        round(purchases.bill_amt,2) as bill_amt,
        (purchases.disc*100) || '%' as disc,
        round(purchases.pay_amt,2) as pay_amt,
        purchases.remark as remark,
        purchases.opt_date as opt_date,
        ifnull(accounts.name, '') as account_name,
        case when purchases.in_out = 1 then '进货' else '退货' end as in_out_name,
        purchase_items.id as items_id
        ,purchase_items.good_fingerprint
      from purchases
      inner join purchase_items on purchases.fingerprint = purchase_items.pur_fingerprint
      inner join goods on purchase_items.good_fingerprint = goods.fingerprint
      left join goods_ext_barcode 
      on goods_ext_barcode.good_fingerprint = goods.fingerprint 
      and goods_ext_barcode.is_deleted = 0
      left join suppliers on purchases.supplier_fingerprint = suppliers.fingerprint
      left join accounts
      on purchases.account_id = accounts.id
      left join clerks
      on purchases.uid = clerks.uid
      left join units on goods.unit_fingerprint = units.fingerprint
      where purchases.is_deleted = 0 {0}
  ), tmp_all as (
    select
      distinct 1 as oid, id, code, create_time, opt_date, '-' as supplier_name, employeeNumber,
      '-' as item_name, null as purPrice,'-' as item_code, '-' as unit_name, '-' as qty,
      '-' as price, bill_amt, disc, pay_amt, ifnull(remark,'') as remark, account_name, in_out_name, '-' as items_id,'-' as good_fingerprint
    from tmp
    union
    select 2 as oid, id, code, create_time, opt_date, supplier_name, employeeNumber, item_name, purPrice, item_code, unit_name,
      qty, price, 0.00 as bill_amt, 0.00 as disc, 0.00 as pay_amt, '' as remark, '' as account_name, in_out_name, items_id, good_fingerprint
    from tmp
  )
  select code, strftime('%Y-%m-%d %H:%M:%S', create_time) as create_time, opt_date, in_out_name, supplier_name, employeeNumber, item_name, purPrice,
    item_code, unit_name, qty, price, bill_amt, disc, pay_amt, account_name, remark, ifnull(p.specs,'{"unlock":{},"lock":{}}') as specs
  from tmp_all left join (select fingerprint,specs from goods union all select '646b141adf1d4e1b880d373a00dd3025','{"unlock":{},"lock":{}}') p on good_fingerprint=p.fingerprint`,
  inventoriesGetToExport:
    `with tmp as (
    select distinct
        inventories.code as code,
        inventories.create_at as create_at,
        case when clerks.employee_number is null or trim(clerks.employee_number)='' then '管理员'
            else trim(clerks.employee_number)
          end as employeeNumber,
        case when goods.name is null then '-'
        else
          case when goods.is_deleted=1 then '(已删除) ' || goods.name
          else goods.name end
        end as item_name,
        items.price as purPrice,
        round(items.account_qty,3) as account_qty,
        round(items.actual_qty,3) as actual_qty,
        round(items.diff_qty,3) as diff_qty,
        round(items.diff_amt,2) as diff_amt,
        round(inventories.account_qty,3) as totalAccountQty,
        round(inventories.actual_qty,3) as totalActualQty,
        round(inventories.diff_qty,3) as totalDiffQty,
        round(inventories.diff_amt,2) as totalDiffAmt,
        IFNULL(inventories.remark,'') as remark,
        inventories.opt_date as opt_date,
        inventories.id,
        items.id as items_id,
        goods.specs,
        goods.code as gCode
      from inventories
      inner join inventory_items as items on inventories.fingerprint = items.inventory_fingerprint
      left join goods on items.good_fingerprint=goods.fingerprint
      LEFT JOIN goods_ext_barcode 
      on goods.fingerprint = goods_ext_barcode.good_fingerprint 
      and goods_ext_barcode.is_deleted = 0
      left join clerks
      on inventories.uid = clerks.uid
      where inventories.is_deleted = 0 and inventories.deal_type = 7 {0}
  ), tmp_all as (
    select
      distinct
        1 as oid,
        code,
        create_at,
        employeeNumber,
        '-' as item_name,
        '-' as purPrice,
        '-' as account_qty,
        '-' as actual_qty,
        '-' as diff_qty,
        '-' as diff_amt,
        totalAccountQty,
        totalActualQty,
        totalDiffQty,
        totalDiffAmt,
        remark,
        id,
        '-' as items_id,
        '{"unlock":{},"lock":{}}' as specs,
        '-' as gCode
      from tmp
      union
      select
        2 as oid,
        code,
        create_at,
        employeeNumber,
        item_name,
        purPrice,
        account_qty,
        actual_qty,
        diff_qty,
        diff_amt,
        0.00 as totalAccountQty,
        0.00 as totalActualQty,
        0.00 as totalDiffQty,
        0.00 as totalDiffAmt,
        '' as remark,
        id,
        items_id,
        specs,
        gCode
      from tmp
  )
  select
    code,
    strftime('%Y-%m-%d %H:%M:%S', create_at) as create_at,
    employeeNumber,
    item_name,
    purPrice,
    account_qty,
    actual_qty,
    diff_qty,
    diff_amt,
    totalAccountQty,
    totalActualQty,
    totalDiffQty,
    totalDiffAmt,
    specs,
    remark,
    gCode
   from tmp_all order by create_at desc, id, oid, items_id asc;`,
  posterValues: `
  select {id}, '{img}', '{url}', '{startDate}', '{endDate}', {changeTime}, {index}, {isDeleted} union`,
  insertPosters: `
   DROP TABLE IF EXISTS "poster";
    CREATE TABLE "poster" (
      "id" integer NOT NULL,
      "img" text,
      "url" text,
      "start_date" timestamp,
      "end_date" timestamp,
      "change_time" integer,
      "orderIndex" integer NOT NULL,
      "is_del" integer,
      PRIMARY KEY ("id")
    );
   insert into poster(id,img,url,start_date,end_date,change_time,'orderIndex',is_del) `,
  // 批量设置商品价格
  setGoodsPrice:
    `update goods
  set is_synced = 0,
  {0};`,
  good_updateCode: 'update goods set code={0}, is_synced=0 where id={1};',
  goodMakeCode:
    `with tmp as(
      select substr(code, 7)+0 as scode
      from goods
      where substr(code, 1, 6)='{prefix}'
      and length(code)=13
    ),tmp_all as(
      select substr(ext_barcode, 7)+0 as scode
      from goods_ext_barcode
      where substr(ext_barcode, 1, 6)='{prefix}'
      and length(ext_barcode)=13
			union all 
			select * from tmp
    )
    select case when not exists(select 1 from tmp_all where scode = 1) then 1 else min(scode) + 1 end as minCode
    from tmp_all as a
    where not exists (select 1 from tmp_all as b where b.scode = a.scode + 1)`,
  goodBatchImportMakeCode:
    `with tmp as(
      select substr(code, 7)+0 as scode
      from goods
      where substr(code, 1, 6)='{prefix}'
      and length(code)=13
      union
      select substr(code, 7)+0 as scode
      from import_goods
      where substr(code, 1, 6)='{prefix}'
      and length(code)=13
    )
    select
      case when not exists(select 1 from tmp where scode = 1) then 1
        else min(scode) + 1
      end as minCode
    from tmp as a
    where not exists (select 1 from tmp as b where b.scode = a.scode + 1);`,
  goodsSelectMaxCode:
    `with recursive tmp(i) as (
      select 0
      union all
      select i+1 from tmp
      limit 1000
    ), tmp1 as (
      select cast(substr(CODE,5) as int) as code
      from goods
      where is_deleted = 0
      and length(code) = 7
      and code like '20{deviceCode}%'
    )
    select tmp.i as maxcode
    from tmp
    left join tmp1
    on tmp.i = tmp1.code
    where tmp1.code is null
    limit {count};`,
  initTypes:
    `insert into types (parent_id,name,pinyin,createtime,revisetime,is_deleted,is_synced,fingerprint,sortno,parent_fingerprint,sync_at) values
    (1,'其他分类','qitafenlei',datetime('now','localtime'),datetime('now','localtime'),0,1,'97663c441d35c9b9674e57356b33c407',0,'',datetime('now','localtime'));`,
  getPoster:
    `select id,img as img,change_time as changeTime,orderIndex,url
    from poster
    where is_del = 0
    and start_date <= datetime('now','localtime')
    and datetime('now','localtime') <= end_date
    order by orderIndex asc;`,
  tableTruncate:
    `delete from {0};
    update sqlite_sequence set seq=0 where name='{0}';`,
  clerksTruncate:
    `delete from clerks where employee_number is not null;
    update sqlite_sequence set seq=1 where name='clerks';`,
  initUnits:
    `INSERT INTO units
    (id, "name", pinyin, is_del, sync_g, info1, info2, industry)values
    (1, '份', 'fen', 0, '2a5da6a04b01473f96468e6f75b83db6', NULL, NULL, 'pos'),
    (2, '包', 'bao', 0, '5442dfce9bae4548d3851889266c5381', NULL, NULL, 'pos'),
    (3, '听', 'ting', 0, '8cbe1c93ad66a95e65967982465b3064', NULL, NULL, 'pos'),
    (4, '卷', 'juan', 0, '7088e18ac923d385adbf143d41783b06', NULL, NULL, 'pos'),
    (5, '双', 'shuang', 0, 'ff8f4688ff3fae8b0c163671fec8d125', NULL, NULL, 'pos'),
    (6, '台', 'tai', 0, 'f923606a39e1c3ac0d65fddb97da80f1', NULL, NULL, 'pos'),
    (7, '个', 'ge', 0, '930882bb0a8c7b53565de4f4d59f5ca7', NULL, NULL, 'pos'),
    (8, '块', 'kuai', 0, '7084eb1b158c90d066fd8d4ddd4cb2a9', NULL, NULL, 'pos'),
    (9, '套', 'tao', 0, '7ac6cc82d06be365952207c9edf0fec3', NULL, NULL, 'pos'),
    (10, '把', 'ba', 0, 'a37a9be784a431e193ac3d73e1f50a3b', NULL, NULL, 'pos'),
    (11, '支', 'zhi', 0, '3e8ecac2d9775b0604dd197a441dda3e', NULL, NULL, 'pos'),
    (12, '条', 'tiao', 0, 'cc1bacb5117aa3f27e5e430cd28dbaed', NULL, NULL, 'pos'),
    (13, '板', 'ban', 0, '19aefe96dd65a90040f690099225c099', NULL, NULL, 'pos'),
    (14, '桶', 'tong', 0, '49e5211320e5c5dd8b7063f4b9d785a0', NULL, NULL, 'pos'),
    (15, '片', 'pian', 0, 'b26fc74c5735d58c82ebc97ca1a40e63', NULL, NULL, 'pos'),
    (16, '版', 'ban', 0, '47595ba3562ede817b40287d907c3d40', NULL, NULL, 'pos'),
    (17, '瓶', 'ping', 0, '0c88bfc874afbba17eeff5139390d2c4', NULL, NULL, 'pos'),
    (18, '盒', 'he', 0, '4051db50b9374683961ba1f1f9d2d318', NULL, NULL, 'pos'),
    (19, '盘', 'pan', 0, 'ff4efcaf1f0b845e973be50d57ccbe53', NULL, NULL, 'pos'),
    (20, '筒', 'tong', 0, 'aa488129b561822eaeee8996985a9f90', NULL, NULL, 'pos'),
    (21, '箱', 'xiang', 0, '231206dd93ae98fc432839da2713a282', NULL, NULL, 'pos'),
    (22, '组', 'zu', 0, '442c6ae209ebb953c177b36141362906', NULL, NULL, 'pos'),
    (23, '千克', 'qianke', 0, '181ba9e28338c4174331a6d2d0407465', NULL, NULL, 'pos'),
    (24, '克', 'ke', 0, 'f18c57a7f24744e192aed7410fe9fcc7', NULL, NULL, 'pos'),
    (25, '斤', 'jin', 0, '3bcc941502f2b6fed99ca827abe376dd', NULL, NULL, 'pos'),
    (26, '公斤', 'gongjin', 0, 'cb8c7f5d68bfe0b73586708b45cfa826', NULL, NULL, 'pos'),
    (27, '两', 'liang', 0, '0bff8e5ca64a100c5ba8f6ac7a8e8276', NULL, NULL, 'pos');`,
  initSuppliers:
    `insert into suppliers (
    name,contacter,tel,mobile,birthday,wechat,qq,mail,addr,postcode,init_amt,cur_amt,disc,
    createtime,revisetime,is_deleted,is_synced,fingerprint,remark,create_by,revise_by
    ) values
    ('零散供应商','','','','','','','','','',0.0,0.0,1.0,datetime('now','localtime'),datetime('now','localtime'),0,1,'a812adcad0da5076388a48e6866258bd',null,null,null),
    ('零售客户','','','','','','','','','',0.0,0.0,1.0,datetime('now','localtime'),datetime('now','localtime'),0,1,'3380922606ba526531c21de524fd584d',null,null,null),
    ('批发客户','','','','','','','','','',0.0,0.0,1.0,datetime('now','localtime'),datetime('now','localtime'),0,1,'7959f6f599737617774d304e323e6927',null,null,null);`,

  clearCurStock: `update goods set cur_stock=0;`,
  findScaleitem:
    `select
    s.sendscale_fingerprint,s.sendscale_name,s.scale_brand_code,s.scale_type_code,l.scale_brand_name,l.scale_type_name,s.scale_ip,s.port,s.remark,
    g.fingerprint, g.code, g.name, g.sale_price, p.plu_code,p.hot_key, g.type_fingerprint
  from
    sendscale s
  inner join
    sendscale_product p on s.sendscale_fingerprint = p.sendscale_fingerprint
  inner join
    goods g on p.good_fingerprint = g.fingerprint
  inner join
    product_scale ps on p.good_fingerprint = ps.good_fingerprint
  inner join
    scale_list l on s.scale_brand_code = l.scale_brand_code and s.scale_type_code = l.scale_type_code
  where
    s.is_del = 0
    and p.is_del= 0
    and ps.is_sendscale = 1
    and g.is_deleted = 0
    and l.is_del = 0
  order by s.revise_at desc;`,
  deleteScale:
    `update sendscale set is_del = 1,is_sync = 0 where sendscale_fingerprint='{sendscaleFingerprint}';`,
  selectAllGoodsList:
    `select g.name,g.code,g.sale_price,g.fingerprint,p.good_fingerprint,p.model as weighModelVal,p.expire_date as expireDate,p.tare,g.type_fingerprint, ifnull(tt.name || ' / ', '') || t.name as typeName
    from product_scale p
    join goods g on g.fingerprint = p.good_fingerprint
    join types t on t.fingerprint = g.type_fingerprint
    left join types tt on t.parent_fingerprint = tt.fingerprint
    where g.is_deleted =0
    and p.is_sendscale = 1
    and t.is_deleted =0;`,
  selectAllTypesList:
    `select DISTINCT t.name,t.fingerprint as typeFingerprint
    from product_scale p
    join goods g on g.fingerprint = p.good_fingerprint
    join types t on t.fingerprint = g.type_fingerprint
    where g.is_deleted =0
    and p.is_sendscale = 1
    and t.is_deleted =0;`,
  insertSendScale:
    `insert into sendscale (sendscale_fingerprint,sendscale_name,scale_brand_code,scale_type_code,scale_ip,port,remark,is_del,create_by,revise_by,create_at,revise_at)
    values ('{sendscaleFingerprint}','{sendscaleName}',{scaleBrandCode},{scaleTypeCode},'{scaleIp}',{port},'{remark}',0,'{uid}','{uid}',datetime('now','localtime'),datetime('now','localtime'));`,
  insertSendscaleProduct:
    `replace into sendscale_product(sendscale_product_fingerprint,sendscale_fingerprint,good_fingerprint,plu_code,hot_key,is_del,is_sync,create_by,revise_by,create_at,revise_at)
    values('{sendscaleProductFingerprint}','{sendscaleFingerprint}','{fingerprint}',{pluCode},{hotKey},0,0,'{uid}','{uid}',datetime('now','localtime'),datetime('now','localtime'));`,
  updateSendscale:
    `update sendscale set sendscale_name='{sendscaleName}', scale_brand_code = {scaleBrandCode},scale_type_code = {scaleTypeCode}, scale_ip='{scaleIp}',
    port = {port},remark='{remark}', is_del =0, is_sync =0, revise_by ='{uid}', revise_at = datetime('now','localtime') ,sync_at=datetime('now','localtime')
    where sendscale_fingerprint ='{sendscaleFingerprint}';`,
  deleteSendscaleProduct: `update sendscale_product set is_del = 1, is_sync= 0 where sendscale_fingerprint ='{sendscaleFingerprint}';`,
  insertSendscaleHistory:
    `insert into sendscale_history (sendscale_history_fingerprint,sendscale_fingerprint,sendscale_name,scale_ip,port,
      remark,scale_brand_name,scale_type_name,good_fingerprint,type_fingerprint,code,name,sale_price,plu_code,hot_key,is_sendscale,model,expire_date,tare,create_at,revise_at)
      values ('{sendscaleHistoryFingerprint}','{sendscaleFingerprint}','{sendscaleName}','{scaleIp}',{port},'{remark}','{scaleBrandName}','{scaleTypeName}','{fingerprint}','{typeFingerprint}',
      '{code}','{name}','{salePrice}',{pluCode},{hotKey},{isSendscale},'{model}','{expireDate}','{tare}',datetime('now','localtime'),datetime('now','localtime'));`,
  productScale:
    `insert into product_scale (good_fingerprint,is_sendscale,model,expire_date,tare,is_sync,create_at,revise_at) values
    ('{fingerprint}','{isBarcodeScalesGoods}','{weighModelVal}','{expireDate}','{tare}',0,datetime('now','localtime'),datetime('now','localtime'));`,
  updateProductScale:
    `replace into product_scale (good_fingerprint,is_sendscale,model,expire_date,tare,is_sync) values
    ('{fingerprint}','{isBarcodeScalesGoods}','{weighModelVal}','{expireDate}','{tare}',0);`,
  deleteProductScale:
    `update product_scale set is_sendscale={isBarcodeScalesGoods} ,is_sync = 0 where good_fingerprint = '{fingerprint}';`,
  selectGood: `select code from goods where fingerprint ='{fingerprint}';`,
  selectScaleList: `select * from scale_list where is_del = 0`,
  selectNames: `select s.sendscale_name||g.name as sumName, s.sendscale_fingerprint,s.sendscale_name,s.scale_ip,s.port,
  s.remark,l.scale_brand_name,l.scale_type_name,g.fingerprint,g.type_fingerprint,g.code,g.name,g.sale_price,p.plu_code, p.hot_key,c.is_sendscale,c.model,c.expire_date,c.tare
  from sendscale_product p
  left join product_scale c on c.good_fingerprint  = p.good_fingerprint
  left join sendscale s on s.sendscale_fingerprint = p.sendscale_fingerprint
  inner join goods g on p.good_fingerprint = g.fingerprint
  inner join scale_list l on s.scale_brand_code  = l.scale_brand_code and s.scale_type_code = l.scale_type_code
  where s.sendscale_fingerprint = '{sendscaleFingerprint}'
  and s.is_del = 0
  and c.is_sendscale = 1
  and g.is_deleted = 0
  and l.is_del = 0
  and p.is_del = 0;`,
  isExist: `select count(*) as cnt from sqlite_master where type='table' and name='{0}';`,
  syncTmpScaleListTruncate:
    `delete from scale_list;`,
  syncTmpScaleListInsert:
    `insert into scale_list(scale_brand_code, scale_brand_name, scale_type_code, scale_type_name, port, hotkey_qty, is_del, create_at, revise_at, sync_at) values `,
  syncTmpScaleListInsertValues:
    `({scaleBrandCode},'{scaleBrandName}',{scaleTypeCode},'{scaleTypeName}',{port}, {hotkeyQty}, {isDel}, strftime('%Y-%m-%d %H:%M:%S', '{createAt}'), strftime('%Y-%m-%d %H:%M:%S', '{reviseAt}'), strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime'))`,
  initScaleList:
    `CREATE TABLE if not exists "scale_list" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "scale_brand_code" integer NOT NULL,
      "scale_brand_name" text NOT NULL,
      "scale_type_code" integer NOT NULL,
      "scale_type_name" text NOT NULL,
      "port" integer NOT NULL,
      "hotkey_qty" integer NOT NULL,
      "is_del" integer NOT NULL default  0,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NOT NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00'
    );`,
  selectsendscaleFingerprint: `select count(*) as num from sendscale where sendscale_fingerprint ='{sendscaleFingerprint}';`,
  deleteClerksFromSettings:
    `delete from settings where key in ('employeenumber', 'employeenumber_list', 'employeeremember');`,
  getNullCodeCounts: `SELECT count(1) as num FROM "goods" where is_deleted=0 and (code is null or trim(code) = '');`,
  getNullCodeGoods: `SELECT * FROM "goods" where is_deleted=0 and (code is null or trim(code) = '');`,
  getClerks:
    `select id, uid, name, employee_number, role, status, privilege, phone, email, gender, avatar, password
    from clerks
    order by id;`,
  clerksReplace:
    `insert into clerks (uid, name, employee_number, "role", status, privilege, phone, email, gender, avatar)
    values ({uid}, '{name}', '{employeeNumber}', {role}, {status}, '{privilege}', '{phone}', '{email}', '{gender}', '{avatar}')
    on conflict(uid) do update set 
      name=excluded.name,
      employee_number=excluded.employee_number,
      "role"=excluded.role,
      status=excluded.status,
      privilege=excluded.privilege, 
      phone=excluded.phone,
      email=excluded.email,
      gender=excluded.gender,
      avatar=excluded.avatar;`,
  clerksSeqReset:
    `update sqlite_sequence set
			seq=(select ifnull(max(id), 0) from clerks)
		where name='clerks';`,
  initSortData:
    `insert into goods_attributes(is_sale, sort, fingerprint, type, create_at, revise_at)
      select 1, 1, a.fingerprint, 'goods', a.create_at, a.revise_at from goods a
      where a.fingerprint not in (select fingerprint from goods_attributes);`,
  initGoodsSort:
    `with tmp as (SELECT ROW_NUMBER () OVER (ORDER BY sort ASC,create_at DESC) as num,fingerprint FROM goods_attributes)
    UPDATE goods_attributes as a
    SET sort = (select num from tmp as b where a.fingerprint=b.fingerprint)
    ,is_synced = 0, revise_by = '{uid}', revise_at = datetime('now','localtime');`,
  updateGoodSort:
    `UPDATE goods_attributes 
    SET sort = {sortRule}
    ,is_synced = 0, revise_by = '{uid}', revise_at = datetime('now','localtime')
    WHERE sort BETWEEN {beginSort} and {endSort};
    UPDATE goods_attributes 
    SET sort = {sort}
    ,is_synced = 0, revise_by = '{uid}', revise_at = datetime('now','localtime')
    WHERE fingerprint = '{fingerprint}';
    select sort,fingerprint from goods_attributes WHERE sort BETWEEN {beginSort} and {endSort};`,
  hasUnuploadedProducts:
    `select count(1) as cnt
    from sales
    inner join sale_items as items
    on sales.fingerprint = items.sale_fingerprint
    inner join goods
    on items.good_fingerprint = goods.fingerprint
    where sales.code = '{code}'
    and sales.is_synced=0
    and items.is_synced=0
    and goods.is_synced=0
    and goods.sync_at='2000-01-01 00:00:00';`,
  getSales:
    `select id, uid, strftime('%Y-%m-%d', opt_date) as optOn, code, in_out as tye,refund_fingerprint,
      bill_amt as billAmt, disc_amt as discAmt, disc, pay_amt as payAmt, owe_amt as oweAmt, change_amt as changeAmt,
      remark, vipid, vipname, vipmobile, is_deleted as isDel, account_id as acctId, fingerprint as syncG, info1,info2,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt
    from sales
    where is_synced=0
    and code = '{0}';`,
  getSaleItems:
    `select id, pur_price as oprc, price as prc, qty, amt, disc, item_disc as itemDisc, is_deleted as isDel,
      fingerprint as syncG, sale_fingerprint as saleSyncG, good_fingerprint as productSyncG, mprice as mprc
    from sale_items
    where is_synced = 0
    and sale_fingerprint = '{0}';`,
  getSaleBlendPays:
    `select id, pay_amt, remark, account_id as acctId, sale_fingerprint as saleFingerprint, fingerprint
    from sale_blend_pays
    where sale_fingerprint = '{0}';`,
  updateSales:
    `update sales set is_synced=1, sync_at='{syncAt}' where id={saleId};
    update sale_items set is_synced=1 where id in ({itemIds});`,
  insertOrUpdategoodsSuppliers:
  `insert into goods_suppliers (supplier_fingerprint, fingerprint) values ('{supplierFingerprint}','{fingerprint}')
    on CONFLICT(fingerprint) do
    update set supplier_fingerprint ='{supplierFingerprint}', is_del =0, is_synced=0, revise_at=datetime('now','localtime');`,
  getPurGoodsSupplier:
  `with tmp as (
    select p.supplier_fingerprint,pi.good_fingerprint,pi.price as pur_price
    ,strftime('%Y-%m-%d %H:%M:%S', p.create_at) as create_at
    from purchases p
    inner join purchase_items pi on p.fingerprint=pi.pur_fingerprint
    where p.is_deleted=0 and pi.is_deleted=0 and in_out=1
    and pi.good_fingerprint='{goodFingerprint}'
    order by p.create_at desc limit 3
    )
    select tmp.*
    ,s.name as supplier_name,s.contacter as supplier_contacter,s.mobile as supplier_mobile,s.addr as supplier_addr
    from tmp
    left join suppliers s on tmp.supplier_fingerprint=s.fingerprint
    order by create_at desc;`,
  getSaleItemsByType:
    `select sales.id, 'sales' as 'tableName',
      case when sales.in_out=1 then '商品销售' else '客户退款' end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', sales.create_at) as create_at,
      -items.qty as qty,
      ifnull((case when sales.in_out=1 then '销售单号：' else '退款单号：' end) || sales.code, '') as remark,
      goods.name
     from sales
     inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
     inner join goods on items.good_fingerprint = goods.fingerprint
     where sales.is_deleted=0
     and items.is_deleted=0
     and goods.is_deleted=0
     {wheres}
     {salesOrderBy}
     {limit};
     `,
  getSaleItemsCountByType:
    `select count(1) as cnt 
    from sales
    inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
    inner join goods on items.good_fingerprint = goods.fingerprint
    where sales.is_deleted=0
    and items.is_deleted=0
    and goods.is_deleted=0
    {wheres}
     `,
  getPurItemsByType:
    `select pur.id, 'purchases' as 'tableName',
      case when pur.in_out=1 then '进货' else '退货' end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', pur.create_at) as create_at,
      items.qty,
      (case when pur.in_out=1 then '进货单号：' else '退货单号：' end) || pur.code as remark,
      goods.name
     from purchases as pur
     inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
     inner join goods on items.good_fingerprint = goods.fingerprint
     where pur.is_deleted=0
     and items.is_deleted=0
     and goods.is_deleted=0
     {wheres}
     {purOrderBy}
     {limit};`,
  getPurItemsCountByType:
     `select count(1) as cnt
      from purchases as pur
      inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where pur.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres};`,
  getInventoryItemsByType:
    `select inv.id, 'inventories' as 'tableName',
      case
       when inv.deal_type=1 or inv.deal_type=6 then '初始库存'
       when inv.deal_type=9 then '初始库存(抵消)'
       when inv.deal_type=2 then '库存修改'
       when inv.deal_type=3 or inv.deal_type=4 then '会员存取'
       when inv.deal_type=5 then '兑换礼品'
       when inv.deal_type=7 then '库存盘点'
       when inv.deal_type=8 then '拆包'
       else ''
      end as changeType,
      strftime('%Y-%m-%d %H:%M:%S', inv.create_at) as create_at,
      items.diff_qty as qty,
      case
       when inv.deal_type=7 then '盘点单号：' || inv.code
       when inv.deal_type=3 or inv.deal_type=4 or inv.deal_type=5 then '会员：' || ifnull(inv.remark, '')
       else ''
      end as remark,
      goods.name
     from inventories as inv
     inner join inventory_items as items on inv.fingerprint=items.inventory_fingerprint
     inner join goods on items.good_fingerprint = goods.fingerprint
     where inv.is_deleted=0
     and items.is_deleted=0
     and goods.is_deleted=0
     {wheres}
     {invOrderBy}
     {limit};`,
  getInventoryItemsCountByType:
     `select count(1) as cnt
      from inventories as inv
      inner join inventory_items as items on inv.fingerprint=items.inventory_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where inv.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres};`,
  getAllByType:
    `with tmp_sales as (
      select sales.id, 'sales' as 'tableName',
        case when sales.in_out=1 then '商品销售' else '客户退款' end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', sales.create_at) as create_at,
        -items.qty as qty,
        ifnull((case when sales.in_out=1 then '销售单号：' else '退款单号：' end) || sales.code,'') as remark
        ,goods.name,goods.first_letters
      from sales
      inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where sales.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_sales}
    ), tmp_purs as (
      select pur.id, 'purchases' as 'tableName',
        case when pur.in_out=1 then '进货' else '退货' end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', pur.create_at) as create_at,
        items.qty,
        (case when pur.in_out=1 then '进货单号：' else '退货单号：' end) || ifnull(pur.code,'') as remark
        ,goods.name,goods.first_letters
      from purchases as pur
      inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where pur.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_pur}
    ), tmp_invs as (
      select inv.id, 'inventories' as 'tableName',
        case
        when inv.deal_type=1 or inv.deal_type=6 then '初始库存'
        when inv.deal_type=9 then '初始库存(抵消)'
        when inv.deal_type=2 then '库存修改'
        when inv.deal_type=3 or inv.deal_type=4 then '会员存取'
        when inv.deal_type=5 then '兑换礼品'
        when inv.deal_type=7 then '库存盘点'
        when inv.deal_type=8 then '拆包'
        else ''
        end as changeType,
        strftime('%Y-%m-%d %H:%M:%S', inv.create_at) as create_at,
        items.diff_qty as qty,
        case
        when inv.deal_type=7 then '盘点单号：' || inv.code
        when inv.deal_type=3 or inv.deal_type=4 or inv.deal_type=5 then '会员：' || ifnull(inv.remark, '')
        else ''
        end as remark
        ,goods.name,goods.first_letters
      from inventories as inv
      inner join inventory_items as items on inv.fingerprint=items.inventory_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where inv.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_inv}
    ), tmp as (
      select id, tableName, changeType, create_at, qty, remark, 3 as sort, name,first_letters from tmp_sales union all
      select id, tableName, changeType, create_at, qty, remark, 1, name,first_letters from tmp_purs union all
      select id, tableName, changeType, create_at, qty, remark, 2, name,first_letters from tmp_invs
    )
    select * from tmp
    {allOrderBy}
    {limit};`,
  getAllCountByType:
    `with tmp_sales as (
      select count(1) as cnt
      from sales
      inner join sale_items as items on sales.fingerprint=items.sale_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where sales.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_sales}
    ), tmp_purs as (
      select count(1) as cnt
      from purchases as pur
      inner join purchase_items as items on pur.fingerprint=items.pur_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where pur.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_pur}
    ), tmp_invs as (
      select count(1) as cnt
      from inventories as inv
      inner join inventory_items as items on inv.fingerprint=items.inventory_fingerprint
      inner join goods on items.good_fingerprint = goods.fingerprint
      where inv.is_deleted=0
      and items.is_deleted=0
      and goods.is_deleted=0
      {wheres_inv}
    ), tmp as (
      select cnt from tmp_sales union all
      select cnt from tmp_purs union all
      select cnt from tmp_invs
    )
    select sum(cnt) as cnt from tmp;`,
  getRefundFingerprints:
    `select fingerprint from sales where in_out = 2 and refund_fingerprint = '{fingerprint}'`,
  getRefundSaleItemSum:
    `select id, good_fingerprint, sum(qty) as backSumQty,sum(amt) as backSumAmt
    from sale_items where sale_fingerprint in ('{fingerprints}') group by good_fingerprint`,
  getBackOrdersSales:
    `select id, uid, code, bill_amt, disc_amt, disc, pay_amt, remark, vipname, account_id, fingerprint, strftime('%Y-%m-%d %H:%M:%S', create_at) createAt 
    from sales where in_out = 2 and fingerprint = '{fingerprint}'`,
  getBackOrdersSaleItem:
    `select id, pur_price, price, qty, amt, disc, item_disc, good_fingerprint, abs((pur_price * qty)) as purPriceTotal
    from sale_items where sale_fingerprint = '{saleFingerprint}'`,
  batchUpdateGoodsStock: `update goods set cur_stock={batchStockNum} where fingerprint in ('{fingerprints}')`,
  inventoryItemsInsertVal:
  `(round({accountQty}, 3), round({actualQty}, 3), round({diffQty}, 3), round({diffAmt}, 2), round({price}, 6),
  '{goodFingerprint}', '{fingerprint}', '{inventoryFingerprint}')`,
  periodTimeSaleCount: `SELECT count(distinct opt_date) as cnt FROM sales WHERE opt_date between '{0}' and '{1}'`,
  updateRefundFingerprint: `update sales set refund_fingerprint='{0}' where fingerprint='{1}';`,
  getGoodNameByFingerprint: `select name, pur_price, cur_stock, sale_price, fingerprint from goods where fingerprint in {0}`
};
