<style lang='less' scoped>
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
.pc_meg {
  background: linear-gradient(180deg, #F6EEE1 0%, rgba(255, 255, 255, 0) 100%);
  overflow: auto;
  .dialog_header{
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E3E6EB;
    margin: 0 20px;
    .header_title{
      color: @themeFontColor;
      font-size: 18px;
      font-weight: bold;
      line-height: 60px;
    }
    .icon_close{
      font-size: 40px;
      color: #8298A6;
      cursor: pointer;
    }
  }
}
.pc_meg0 {
  height: 100px;
  width: 64%;
  margin: 0 auto;
  .pc_meg1 {
    height: 100px;
    width: 100px;
    background: linear-gradient(180deg, #E3D2B5 0%, #DBBE91 100%);
    border-radius: 12px;
    display: flex;
    float: left;
    .pc_meg11 {
      height: 53px;
      width: 53px;
      margin: 0 auto;
      margin-top: 24px;
    }
  }
  .pc_meg4 {
    font-style: normal;
    font-weight: bold;
    float: left;
    width: 80%;
    margin-left: 26px;
    .pc_meg2 {
      font-size: 20px;
      line-height: 26px;
      color: #7F6C40;
    }
    .pc_meg3 {
      font-size: 16px;
      line-height: 21px;
      color: @themeBackGroundColor;
      margin-top: 22px;
      font-weight: normal;
    }
  }
}
.pc_meg5 {
  width: 64%;
  margin: 31px auto 0px auto;
  .pc_meg6 {
    height: 100px;
    border-radius: 4px;
    border: 1px solid #CFA26B;
    box-sizing: border-box;
    display: inline-block;
    cursor: pointer;
    .pc_meg33 {
      display: inline-block;
      background: linear-gradient(90deg, #5E5A44 0%, #302D31 100%);
      border-radius: 8px 8px 8px 0px;
      width: 82px;
      height: 24px;
      font-size: 12px;
      line-height: 24px;
      color: #FFFFFF;
      text-align: center;
      position: relative;
      top: -15px;
      left: -1px;
    }
    .pc_meg7 {
      font-size: 20px;
      color: #D1B889;
      text-align: center;
      top: -10px;
      position: relative;
    }
    .pc_meg8 {
      font-size: 28px;
      letter-spacing: -0.05em;
      color: @themeBackGroundColor;
      text-align: center;
      top: -10px;
      position: relative;
    }
    .pc_meg10 {
      color: #FFFFFF;
    }
  }
  .pc_meg9 {
    background: linear-gradient(180deg, #D8B774 0%, #B48B5A 100%);
  }
}
.pc_meg12 {
  width: 64%;
  margin: 38px auto 100px auto;
  .pc_meg13 {
    width: 100%;
  }
}
.pc_meg15 {
  margin-top: 30px;
  width: 100%;
  padding: 0 20px;
  display: flex;
  .pc_meg16 {
    display: flex;
    height: 80px;
    width: 80px;
    background: linear-gradient(180deg, #E3D2B5 0%, #DBBE91 100%);
    border-radius: 12px;
    .pc_meg17 {
      height: 42.4px;
      width: 42.4px;
      margin: 0 auto;
      margin-top: 20px;
    }
  }
  .pc_meg20 {
    margin-left: 14px;
    width: 170px;
    .pc_meg18 {
      font-style: normal;
      font-weight: bold;
      font-size: 18px;
      line-height: 24px;
    }
  }
  .pc_meg19 {
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    color: #B1C3CD;
    line-height: 18px;
  }
  .pc_meg21 {
    .pc_meg22 {
      margin: 0 auto;
      text-align: center;
      font-size: 28px;
      line-height: 33px;
      letter-spacing: -0.05em;
      color: #FF6159;
      margin-top: 25px;
    }
  }
}
.pc_meg23 {
  width: 100%;
  padding: 30px 20px;
  height: 328px;
  .pc_meg24 {
    margin-left: 98px;
  }
  .pc_meg24_wait {
    margin-left: 55px;margin-left: 132px;
    margin-top: 60px;
    height: 100px;
    width: 100px;
  }
  .pc_meg30 {
    font-size: 16px;
    line-height: 23px;
    color: @themeFontColor;
    text-align: center;
    margin-top: 20px;
  }
}
.pc_meg34 {
  color: #B2C3CD;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  margin-top: 6px;
}
.pc_meg25 {
  width: 100%;
  padding-top: 35px;
  padding-bottom: 30px;
  .pc_meg26 {
    margin: 0 auto;
    background: #CFA26B;
    border-radius: 50%;
    height: 60px;
    width: 60px;
    .pc_meg29 {
      margin-left: 10px;
      margin-top: 10px;;
    }
  }
}
.pc_meg31 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
  .pc_meg32 {
    position: absolute;
    width: 450px;
    min-height: 280px;
    background: #fff;
    border-radius: 6px;
    padding-bottom: 10px;
    transform: translate(-50%, -50%);
    top: 50%;
    left:50%;
  }
}
.pc_meg27 {
  font-size: 24px;
  line-height: 40px;
  text-align: center;
  letter-spacing: -0.408px;
  color: @themeFontColor;
}
.pc_meg28 {
  display: inline-block;
  width: 140px;
  height: 50px;
  text-align: center;
  background: @themeFontColor;
  border-radius: 4px;
  font-weight: bold;
  font-size: 20px;
  line-height: 48px;
  letter-spacing: -0.408px;
  color: #FFFFFF;
  margin-top: 32px;
  cursor: pointer;
}
</style>
<template>
  <div class="pc_meg">
    <div style="font-family: Microsoft YaHei, sans-serif;" v-html="qrCode">
    </div>
    <div class="pc_meg0">
      <div class="pc_meg1">
        <img alt="" class="pc_meg11" src="../../image/pc_messageLog.png"/>
      </div>
      <div class="pc_meg4">
        <div class="pc_meg2">短信通知</div>
        <div class="pc_meg3">专用短信通道（发送账户变动、积分兑换、次卡消息等短信），一次发送方便，稳定性好（每条短信限制60字，超出另计费）</div>
      </div>
    </div>
    <div class="pc_meg5">
      <div
        :key="'main' + index"
        v-for="(item, index) in payList"
        @click="buyMsg(index, item, $event)"
        class="pc_meg6"
        :style="index !== payList.length -1 ? 'width:' + megWidth + ';margin-right:' + megMarginWidth : 'width:' + megWidth">
        <div :style="Number(item.price) === 0 ? 'visibility:visible' : 'visibility:hidden'" class="pc_meg33">初次购买免费</div>
        <div :key="'inner1' + index" class="pc_meg7">{{item.count}} 条</div>
        <div :key="'inner2' + index" class="pc_meg8">
          <img alt="" v-if="BuyIndex === index" src="../../image/pc_moneyLogWhite.png"/>
          <img alt="" v-else src="../../image/pc_moneyLogBrown.png"/>{{item.price}}
          </div>
      </div>
    </div>
    <div class="pc_meg12">
      <img alt="" class="pc_meg13" src="../../image/pc_messageInfo.png"/>
    </div>
    <el-dialog
      :visible.sync="showBuy"
      width="433px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">结算</div>
        <div
          class="icon_close"
          @click="showBuy = false"
        >×</div>
      </div>
      <div class="pc_meg15">
        <div class="pc_meg16">
          <img alt="" class="pc_meg17" src="../../image/pc_messageLog.png"/>
        </div>
        <div class="pc_meg20">
          <div class="pc_meg18">购买短信</div>
          <div class="pc_meg19" style="margin-top: 12px">价格：{{price}}</div>
          <div class="pc_meg19" style="margin-top: 8px">数量：{{count}}</div>
        </div>
        <div class="pc_meg21">
          <div class="pc_meg19" style="margin-top: 5px;text-align: center;">待支付</div>
          <div class="pc_meg22">
            <img alt="" src="../../image/pc_moneyLogRed.png"/>{{price}}</div>
        </div>
      </div>
      <div v-show="price === '0.00'" style="padding-bottom: 30px;margin-top: 10px;">
        <div class="pc_meg28" style="margin-left: 50px;margin-right: 40px;" @click="showBuy = false">取消</div>
        <div class="pc_meg28" style="background: #CFA26B" @click="sureBuy">确定购买</div>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="showSuccess"
      width="433px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="pc_meg25">
        <div class="pc_meg26" :style="status === 1 ? 'background: #FF6159' : ''">
          <el-image :src="statusLogo" class="pc_meg29"/>
        </div>
        <div class="pc_meg27" style="margin-top: 16px;padding-left: 10px;">{{statusMsg}}</div>
        <div class="pc_meg27">{{infoMsg}}</div>
        <div class="pc_meg28" style="margin-left: 70px;margin-right: 28px;" @click="closeTip">取消</div>
        <div class="pc_meg28" style="background: #B4995A" @click="go">{{rightButton}}</div>
      </div>
    </el-dialog>
    <div
      class='pc_meg31'
      v-show='showUltimate'
    >
      <div class="pc_meg32">
        <div class="pc_meg27" style="padding-top: 30px;font-weight: bold;">提示</div>
        <div class="pc_meg27" style="padding: 15px 48px 0px 48px;">该套餐只面向专业版以上用户开放购买，是否升级{{$t('components.header.header_user')}}版本？</div>
        <div class="pc_meg28" style="margin-left: 70px;margin-right: 28px;" @click="showUltimate = false">取消</div>
        <div class="pc_meg28" style="background: #B4995A" @click="goUltimate">去升级</div>
      </div>
    </div>
    <vPcSMSContract v-if="isShowSMSContract" :isLogin="false" :fromType="2" @closeSMS="closeSMS" @sureSMS="sureSMS"></vPcSMSContract>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog, Table, TableColumn, Pagination } from 'element-ui';
import vPcSMSContract from '@/components/pc_sms_contract.vue';
import logList from '@/config/logList';
export default {
  components: {
    [Dialog.name]: Dialog,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    vPcSMSContract
  },
  data () {
    return {
      timers: 60,
      win: null,
      winLoop: null,
      payList: [],
      showBuy: false,
      BuyIndex: -1,
      megWidth: ((this.screenWidth * 0.64) / 5 * 0.95) + 'px',
      megMarginWidth: ((this.screenWidth * 0.64) / 5 * 0.05) + 'px',
      price: '0.00',
      count: '0',
      isShowSMSContract: false,
      showSuccess: false,
      status: 0,
      statusMsg: '购买成功！',
      infoMsg: '是否跳转到短信设置页面？',
      rightButton: '设置短信',
      statusLogo: require('../../image/pc_msgMark.png'),
      set_ip: (typeof returnCitySN != 'undefined' && returnCitySN.cip) ? returnCitySN.cip : '127.0.0.1',
      qrCode: '',
      lockPay: false,
      timer: null, // 定义定时器
      timerCount: null, // 定义计时器
      showUltimate: false,
      paidItemCode: '',
      aLiOutTradeNo: '',
      waitImg: require('../../image/zgzn-pos/pc_cloud_loading.gif')
    };
  },
  created () {
    this.getPaidItems();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    sureSMS() {
      this.isShowSMSContract = false;
      this.showSuccess = true;
    },
    closeSMS() {
      this.isShowSMSContract = false;
    },
    setShowSuccess() {
      if (this.$employeeAuth('create_vips') || this.$employeeAuth('edit_vips') || this.$employeeAuth('cancel_vips') ||
        this.$employeeAuth('exchange_vip_points') || this.$employeeAuth('recharge_give_money') || this.$employeeAuth('recharge_give_money_revoke')) {
        this.isShowSMSContract = true;
      } else {
        demo.msg('success', '购买成功！');
      }
    },
    closeTip() {
      this.showSuccess = false;
      this.aLiOutTradeNo = '';
    },
    sureBuy() {
      const param = {
        'paidServiceCode': '3',
        'paidItemCode': this.paidItemCode,
        'phone': this.sysUid,
        'outTradeNo': this.generateUUID() + '_' + this.sysUid + '_' + '1',
        'type': '0',
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.insertItemPurchase, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          this.showBuy = false;
          this.setShowSuccess();
          this.getPaidItems();
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    goUltimate() {
      this.showUltimate = false;
      this.SET_SHOW({ showUpgrade: true });
    },
    go() {
      this.showSuccess = false;
      if (this.rightButton === '设置短信') {
        this.SET_SHOW({ selectRow: 11 });
        this.SET_SHOW({ memberIndex: 'four' });
        this.SET_SHOW({ isMemberSetting: true });
        this.SET_SHOW({ isShortMessage: false });
      } else if (this.rightButton === '联系客服') {
        this.SET_SHOW({ showCustomerService: true });
      } else if (this.rightButton === '重新支付') {
        this.showBuy = true;
      } else {
        this.shortPayQuery(this.aLiOutTradeNo, 1);
      }
    },
    buyMsg(index, item, event) {
      this.clearTimer();
      this.aLiOutTradeNo = '';
      this.qrCode = '';
      if (this.ultimate === null) {
        this.showUltimate = true;
        return;
      }
      if (pos.network.isConnected()) {
        this.BuyIndex = index;
        this.price = item.price;
        this.count = item.count;
        demo.actionLog({page: 'short_message', action: 'clickItemReadyBuy', description: `点击条目准备购买,条数：${this.count}，金额：${this.price}`});
        this.paidItemCode = item.code;
        let elements = document.getElementsByClassName('pc_meg6');
        for (let element of elements) {
          element.classList.remove('pc_meg9');
          element.children[0].classList.remove('pc_meg10');
          element.children[1].classList.remove('pc_meg10');
          element.children[2].classList.remove('pc_meg10');
        }
        event.currentTarget.children[0].classList.add('pc_meg10');
        event.currentTarget.children[1].classList.add('pc_meg10');
        event.currentTarget.children[2].classList.add('pc_meg10');
        event.currentTarget.classList.add('pc_meg9');
        this.lockPay = false;
        if (Number(item.price) !== 0) {
          this.insertItemPurchase(item.code);
        } else {
          this.showBuy = true;
        }
      } else {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    aliBuy() {
      document.forms[0].target = 'newWindow';
      this.win = window.open('about:blank', 'newWindow');
      this.winLoop = setInterval(() => {
        console.log('open page listening+');
        if (this.win.closed) {
          console.log('winLoop clearInterval+');
          this.win = null;
          this.clearTimer();
          clearInterval(this.winLoop);
          demo.actionLog({page: 'short_message', action: 'payCodeDialogClose', description: '支付码弹窗关闭'});
        }
      }, 1500);
      this.$once('hook:beforeDestroy', () => {
        clearInterval(this.winLoop);
      });
      document.forms[0].submit();
      this.win.focus();
    },
    // 获取短信套餐
    getPaidItems () {
      let param = {
        'paidServiceCode': 3,
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.getPaidItems, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          this.payList = obj.data;
          this.megWidth = ((this.screenWidth * 0.64) / this.payList.length * 0.95) + 'px';
          this.megMarginWidth = ((this.screenWidth * 0.64) / this.payList.length * 0.05) + 'px';
          this.searchShortMsg();
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    insertItemPurchase(paidItemCode) {
      if (this.win !== null) {
        this.win.close();
        this.clearTimer();
        clearInterval(this.winLoop);
      }
      const param = {
        'paidServiceCode': '3',
        'paidItemCode': paidItemCode,
        'phone': this.sysUid,
        'type': '1',
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.insertItemPurchase, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === 200) {
          this.qrCode = obj.data.qr_code;
          setTimeout(() => {
            this.aliBuy();
          }, 0);
          // this.createTimer(aLiOutTradeNo);
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    generateUUID() {
      var d = new Date().getTime();
      if (window.performance && typeof window.performance.now === 'function') {
        d += performance.now();
      }
      var uuid = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
      });
      return uuid;
    },
    // 查询支付结果
    shortPayQuery (outTradeNo, flag) {
      const param = {
        'outTradeNo': outTradeNo
      };
      if (pos.network.isConnected()) {
        demo.$http.post(this.$rest.shortPayQuery, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.message === 'success') {
              this.dataFormat(res.data.trade_state, flag);
            } else {
              if (flag === 1) {
                this.reloadPay();
              }
            }
          });
      } else {
        this.clearTimer();
        this.showBuy = false;
        this.status = 1;
        this.statusLogo = require('../../image/Vector.png');
        this.statusMsg = '网络已断开，';
        this.infoMsg = '请联网后重新查询支付结果！';
        this.rightButton = '重新查询';
        this.setShowSuccess();
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    dataFormat(trade_state, flag) {
      if (trade_state === 'TRADE_SUCCESS') {
        demo.actionLog({page: 'short_message', action: 'paySuccess', description: '购买短信条数支付成功'});
        this.clearTimer();
        this.showBuy = false;
        this.status = 0;
        this.statusLogo = require('../../image/pc_msgMark.png');
        this.statusMsg = '购买成功！';
        this.infoMsg = '是否跳转到短信设置页面？';
        this.rightButton = '设置短信';
        this.setShowSuccess();
      } else if ((trade_state === 'WAIT_BUYER_PAY' || trade_state === 'TRADE_FINISHED') && flag === 1) {
        this.reloadPay();
      } else {
        // todo
      }
    },
    reloadPay() {
      this.status = 1;
      this.statusLogo = require('../../image/Vector.png');
      this.statusMsg = '未支付！';
      this.infoMsg = '订单还未支付，请扫码支付！';
      this.rightButton = '重新查询';
      this.setShowSuccess();
      this.createTimer(this.aLiOutTradeNo);
    },
    createTimer(aLiOutTradeNo) {
      const that = this;
      this.timers = 60;
      setTimeout(() => {
        that.timer = setInterval(() => { // 创建定时器
          that.timers--;
          that.shortPayQuery(aLiOutTradeNo, 0);
        }, 2000);
      }, 3000);
    },
    clearTimer() { // 清除定时器
      if (this.timer === null) {
        setTimeout(() => {
          clearInterval(this.timer);
          this.timer = null;
        }, 3000);
      } else {
        clearInterval(this.timer);
        this.timer = null;
      }
      clearInterval(this.timerCount);
      this.timerCount = null;
    },
    searchShortMsg() {
      const param = {
        'phone': this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.selectPaySuccessCounts, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(rs => {
        const obj = rs.data;
        console.log(obj, 'obj');
        if (obj.code === 200) {
          if (obj.data.counts === 0) {
            this.payList.forEach(item => {
              if (item.isFree === 1) {
                item.price = '0.00';
              }
            });
          }
        } else {
          this.payList.forEach(item => {
            if (item.isFree === 1) {
              item.price = '0.00';
            }
          });
        }
      });
    }
  },
  watch: {
    timers() {
      if (this.timers === 0) {
        this.clearTimer();
      }
    },
    screenWidth() {
      this.megWidth = ((this.screenWidth * 0.64) / this.payList.length * 0.95) + 'px';
      this.megMarginWidth = ((this.screenWidth * 0.64) / this.payList.length * 0.05) + 'px';
    },
    showBuy() {
      if (this.showBuy === false) {
        this.clearTimer();
      }
    },
    showSuccess() {
      if (this.showSuccess === false) {
        this.clearTimer();
      }
    }
  },
  computed: mapState({
    isHome: state => state.show.isHome,
    loginTime: state => state.show.loginTime,
    sysUid: state => state.show.sys_uid,
    ultimate: state => state.show.ultimate,
    screenWidth: state => state.show.screenWidth,
    isAliPay: state => state.show.isAliPay
  }),
  beforeDestroy() {
    clearInterval(this.timer);
    this.timer = null;
    this.clearTimer();
    if (this.win !== null) {
      this.win.close();
    }
  }
};
</script>
