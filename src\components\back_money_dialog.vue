<template>
  <div v-loading.fullscreen.lock="loading"
    :element-loading-text="loadingText"
    element-loading-background="rgba(0, 0, 0, 0.5)"
    class="pc_fip16">
    <div class="pc_fip17">
      <div class="pc_fip18_1">
        <div class="pc_fip19">应退金额</div>
        <el-popover
          :disabled="params.shouldBackMoney.toString().length < 11"
          popper-class="pc_pay192 popper_self"
          placement="top"
          trigger="hover"
          :content="'¥ ' + params.shouldBackMoney.toString()"
        >
          <div slot="reference" class="pc_fip2_1 theme-color">
            <span>¥</span>
            {{ params.shouldBackMoney || '0.00' }}
          </div>
        </el-popover>
      </div>
      <div class="divider"></div>
      <div class="pc_fip18_1">
        <div class="pc_fip19">实退金额</div>
        <el-popover
          :disabled="params.realBackMoney.toString().length < 11"
          popper-class="pc_pay192 popper_self"
          placement="top"
          trigger="hover"
          :content="'¥ ' + params.realBackMoney.toString()"
        >
          <div slot="reference" class="pc_fip2_1 theme-color">
            <span>¥</span>
            {{ params.realBackMoney || '0.00' }}
          </div>
        </el-popover>
      </div>
    </div>
    <div class="remark-container">
      <div class="remark">备注</div>
      <div class="input-container">
        <cj-input v-model="remark" height="50" maxlength="50" fontSize="15" clear-trigger="always" placeholder="请输入备注" />
      </div>
    </div>
    <div class="back-type-container">
      <div class="pc_fip61">选择退款方式</div>
      <el-popover placement="right" content="仅支持“扫码付”原路退回退款" popper-class="pc_fip5" trigger="click">
        <div slot="reference" class="pc_fip62">？</div>
      </el-popover>
      <div class="pc_fip21" @click="button_paying ? changePrintTicketStatus() : ''">
        <img v-show="!permit_print" src="../image/zgzn-pos/pc_goods_checkbox1.png" class="cb" />
        <img v-show="permit_print" src="../image/zgzn-pos/pc_goods_checkbox2.png" class="cb" />
        <div class="print-title">支付完成打印小票</div>
      </div>
    </div>
    <div class="pay-type">
      <div class="pc_fip51">
        <img src="../image/zgzn-pos/pc_pay_returngoods.png" class="bingo" />
        {{ payWayFormat(params) }}
      </div>
    </div>
    <div style="overflow: hidden">
      <div class="pc_fip37" @click="closeDialog">取消</div>
      <div v-show="!button_paying" class="pc_fip36">退款中</div>
      <div v-show="button_paying" @click="refundConfirm" class="pc_fip35">确认</div>
    </div>
    <div style="height: 40px"></div>
    <!-- 确认退款二次确认框 -->
    <confirm-dialog
      :visible.sync="showConfirmBackMoneyDialog"
      message="退款金额将<span style='color: #FF0000'>实时到账</span>，<br/>
        退款成功将<span style='color: #FF0000'>无法撤销</span>，
        请确认无<br/>误后操作"
      @confirm="confirmBackMoney"
      :closeOnClickModal="false"
    />
    <!-- 扫码付账户余额不足提示框 -->
    <confirm-dialog
      :visible.sync="showBalanceNotEnoughDialog"
      title="退款失败"
      message="<div style='color: red'>扫码付账户余额不足，<br/>请线下处理</div>"
      :showCancel="false"
      @confirm="showBalanceNotEnoughDialog = false"
      :closeOnClickModal="false"
    />
    <!-- 积分不足提示框 -->
    <confirm-dialog
      :visible.sync="showPointsNotEnoughDialog"
      title="提示"
      :message="`会员积分不足，是否继续退款<br/>
        <span style='color: #ADC1CF; font-size: 18px'>
          退款需扣除${vipPoints.newPoint}积分<br/>
          选择继续退款，会员积分会产生负数
        </span>
        `"
      confirm-text="继续退款"
      @confirm="(showPointsNotEnoughDialog = false), refund()"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="refreshTipsShow"
      :message="`销售单状态异常<br/>请刷新后重试`"
      @confirm="refreshConfirm"
      @cancel="$emit('backDetail', true), closeScreen2()"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="syncFailShow"
      :message="`退款成功，云同步失败<br/>
        请稍后手动云同步或等待下次自<br/>
        动云同步`"
      :show-cancel="false"
      @confirm="syncFailShow = false"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="timeoutShow"
      :message="`请求超时，未查询到结果`"
      :show-cancel="false"
      confirm-text="再次查询"
      @confirm="getPayStatus"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="timeout2Show"
      :message="`
        请求超时，未查询到结果<br/>
        请返回销售明细确认销售单状态
      `"
      :show-cancel="false"
      confirm-text="返回销售明细"
      @confirm="moduleSync('pos'), $emit('backDetail')"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="timeout3Show"
      :message="`
        请求超时，请稍后手动云同步<br/>
        确认是否退货退款成功，防止重复退单
      `"
      :show-cancel="false"
      confirm-text="确认"
      @confirm="timeout3Show = false"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible.sync="refundFailShow"
      title="退款失败"
      :message="`
        当前会员的单据信息重复，请<br/>
        联系系统售后处理
      `"
      :throttle="false"
      :show-cancel="false"
      confirm-text="知道了"
      @confirm="refundFailShow = false"
      :closeOnClickModal="false"
    />
  </div>
</template>

<script>
import logList from '@/config/logList';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import CjInput from '@/common/components/CjInput';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import { orderRefund } from '@/api/order';
import { getPayStatus } from '@/api/pay';
import { generateSignature } from '@/utils/util.js';
import { checkVipIntegral, getVipInfo } from '@/api/vip';
export default {
  components: {
    CjInput,
    ConfirmDialog
  },
  props: {
    params: {
      type: Object
    }
  },
  data() {
    return {
      remark: '', // 退货备注
      button_paying: true, // 支付按钮状态
      showConfirmBackMoneyDialog: false, // 确认退款弹窗
      showBalanceNotEnoughDialog: false, // 随行付余额不足弹窗
      showPointsNotEnoughDialog: false, // 积分不足弹窗
      vipPoints: {},
      vipInfo: {
        // 会员信息
        member_name: '会员名',
        member_value: '',
        member_money_name: '余额',
        member_money_value: '',
        member_mobile_name: '会员手机号',
        member_mobile_value: '',
        member_point_name: '积分',
        member_point_value: 0
      },
      refreshTipsShow: false, // 是否显示刷新弹窗
      payWays: {
        1: '现金记账',
        3: 'POS记账',
        4: '微信记账',
        5: '支付宝记账',
        6: '会员卡退款',
        7: '原路退回（微信-扫码付）',
        8: '原路退回（支付宝-扫码付）'
      },
      loading: false, // 接口请求loading
      syncFailShow: false, // 退货完成后拉取云端退货单数据失败弹窗
      loadingText: '', // loading文本
      timer: null, // 倒计时
      timeoutShow: false, // 接口请求失败弹窗
      timeout2Show: false, // 再次查询失败弹窗
      timeout3Show: false, // 非扫码付退款接口超时提醒弹窗
      refundFailShow: false // 云端会员销售量履历重复导致退款失败的信息是否显示
    };
  },
  created() {
    console.log('======退货退款参数：', this.params);
    this.screen2Format();
    this.getRefundInfo(this.params.backParams.saleFingerprint);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 副屏显示
     */
    screen2Format() {
      if (this.params.backParams.leftGoodsList) {
        const showList = this.params.backParams.leftGoodsList.map(good => {
          return {
            name: good.goodName,
            sale_price: good.price,
            salePrice: good.price,
            number: Math.abs(good.qty),
            amt: Math.abs(good.amt),
            unitName: good.unitName
          };
        });
        const reducePrice = this.params.backParams.discAmt - this.params.backParams.billAmt
        const subData = {
          screen2ShowList: showList,
          screen2ReducePrice: reducePrice.toFixed(2),
          screen2ShowFinalPrice: Math.abs(this.params.backParams.discAmt).toFixed(2),
          screen2ShowMember: false,
          screen2ReturnGoods: true
        };
        demo.screen2(subData, 24);
      }
    },
    /**
     * 关闭副屏显示
     */
    closeScreen2() {
      const subData = {
        screen2ShowList: []
      };
      demo.screen2(subData, 27);
    },
    /**
     * 支付方式格式化
     */
    payWayFormat({ accountId, combinedItem: pays }) {
      if (this.payWays[accountId]) {
        return this.payWays[accountId];
      } else {
        const text = `${pays[0].accountName},${pays[1].accountName}`;
        return text;
      }
    },
    /**
     * 切换控制打印小票勾选状态
     */
    changePrintTicketStatus() {
      const data = [{ key: settingService.key.print_small_ticket_afterpay, value: !this.permit_print }];
      settingService.put(data, () => {
        this.SET_SHOW({ permit_print: !this.permit_print });
      });
    },
    /**
     * 关闭弹窗
     */
    closeDialog() {
      this.closeScreen2();
      this.$emit('closeDialog');
    },
    /**
     * 获取销售单关联的最新的退货单中的refundInfo
     * @param {string} fingerprint 销售单的fingerprint
     */
    getRefundInfo(fingerprint) {
      saleService.getLastreFundInfoBySaleFingerPrint(fingerprint, ([order]) => {
        console.warn('refundOrder', order);
        if (order && order.info1 && order.info1.includes('refundInfo')) {
          const refundInfo = JSON.parse(order.info1).refundInfo;
          this.params.backParams.refundInfo = refundInfo;
        } else {
          this.params.backParams.refundInfo = '';
        }
      }, () => {
        this.params.backParams.refundInfo = '';
      })
    },
    refundConfirm() {
      // 扫码付退货退款显示二次确认框
      if ([7, 8].includes(this.params.accountId)) {
        this.showConfirmBackMoneyDialog = true;
        return;
      }
      this.confirmBackMoney();
    },
    /**
     * 确认退款
     */
    async confirmBackMoney() {
      if (!pos.network.isConnected()) {
        demo.msg('error', '网络已断开，请检查您的网络设置后重试');
        return;
      }
      if (this.params.backParams.vipname) {
        this.checkVipIntegral();
      } else {
        this.refund();
      }
    },
    // 会员积分检查
    async checkVipIntegral() {
      const vipInfo = {
        vipId: this.params.backParams.vipId,
        phone: this.params.backParams.vipMobile,
        outTradeNo: this.params.backParams.outTradeNo,
        money: -this.params.backParams.billAmt,
        acctId: this.params.accountId
      };
      const { code, data, msg } = await checkVipIntegral(vipInfo);
      if (code === '0') {
        console.warn('data', data);
        if (data) {
          this.vipPoints = data;
          this.showPointsNotEnoughDialog = true;
        } else {
          // 积分够扣除
          this.refund();
        }
      } else if (code === '31') {
        this.moduleSync('pos-sales', false);
      } else {
        demo.msg('error', msg);
      }
    },
    // 云端退货退款方法
    refund() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
        return;
      }
      if (this.loading) {
        return;
      }
      this.showConfirmBackMoneyDialog = false;
      this.loading = true;
      this.refundTimer();
      orderService.get(
        { type: 'XSD' },
        async res => {
          const saleNo = demo.t2json(res)[0].code;
          localStorage.setItem('saleNo', saleNo);
          this.params.backParams.newOutTradeNo = saleNo;
          this.params.backParams.storeName = this.username;
          this.params.backParams.remark = this.remark;
          const time = new Date().getTime();
          const signParam = {
            deviceCode: this.params.backParams.deviceCode,
            newOutTradeNo: this.params.backParams.newOutTradeNo,
            outTradeNo: this.params.backParams.outTradeNo,
            payAmt: this.params.backParams.payAmt,
            refundType: this.params.backParams.refundType,
            saleFingerprint: this.params.backParams.saleFingerprint,
            ts: time
          };
          const sign = generateSignature(signParam, this.commonSign);
          this.params.backParams.ts = time;
          this.params.backParams.sign = sign;
          const type = `${this.params.backParams.refundType ? '整单' : '部分'}退款`
          const parText = `参数${JSON.stringify(this.params.backParams)}`
          try {
            const { code, msg } = await orderRefund(this.params.backParams);
            if (code === 200) {
              // 退货退款成功后调用pos模块云同步
              demo.msg('success', '退货成功');
              this.moduleSync('pos');
              if (this.params.accountId === 1) {
                this.openMoneybox();
              }
              this.$_actionLog(logList.refund, `${type}成功，${parText}`);
            } else if (code === 20000) {
              // 当前退货的销售单云端不存在需要调用sales表云同步后重新调用退货退款
              this.moduleSync('pos-sales', true);
              this.$_actionLog(logList.refund, `${type}失败，销售单云端不存在，${parText}`);
            } else if (code === 20001) {
              this.loading = false;
              clearInterval(this.timer);
              this.showBalanceNotEnoughDialog = true;
              this.$_actionLog(logList.refund, `${type}失败，余额不足，${parText}`);
            } else if (code === 20002) {
              // 当前退货的销售单可能其他端已进行进行退货操作，需要调用sales表云同步后返回部分退货退款页面刷新列表
              this.moduleSync('pos-sales', false);
              this.$_actionLog(logList.refund, `${type}失败，其他端易操作，${parText}`);
            } else if (code === 20003) {
              this.loading = false;
              clearInterval(this.timer);
              this.refundFailShow = true;
              this.$_actionLog(logList.refund, `${type}失败，${msg}，${parText}`);
            } else {
              // 非请求超时或非扫码付单时关闭倒计时
              if (code !== '10002') {
                this.loading = false;
                clearInterval(this.timer);
                demo.msg('error', msg);
              }
              this.$_actionLog(logList.refund, `${type}失败，${msg}，${parText}`);
            }
            if (code !== 200) {
              localStorage.removeItem('saleNo');
            }
          } catch (error) {
            clearInterval(this.timer);
            this.loading = false;
            if (!this.timeoutShow && !this.timeout2Show) {
              this.timeoutShow = true;
            }
            this.$_actionLog(logList.refund, `${type}失败，${JSON.stringify(error)}，${parText}`);
          }
        },
        err => {}
      );
    },
    /**
     * 模块/表云同步方法
     */
    moduleSync(module, bool) {
      syncService.clearDataInfo(() => {
        this.loading = false;
        clearInterval(this.timer);
        if (module === 'pos') {
          // 退货退款成功
          this.judgePrintTicket(this.params.backParams.newOutTradeNo);
          this.closeScreen2();
          this.$emit('backDetail', true);
        } else if (bool) {
          this.refund();
        } else {
          this.refreshTipsShow = true;
        }
        this.showConfirmBackMoneyDialog = false;
      }, error => {
        this.loading = false;
        if (module === 'pos') {
          this.syncFailShow = true;
        } else {
          demo.msg('error', msg);
        }
      }, module)
    },
    /**
     * 支付结果状态查询
     */
    async getPayStatus() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
        return;
      }
      if (this.loading) {
        return;
      }
      this.timeoutShow = false;
      this.loading = true;
      this.refundTimer();
      const params = {
        refundorderid: this.params.backParams.refundOrderId,
        mchid: this.appSecret
      };
      this.$_actionLog(logList.refund, `再次查询，${JSON.stringify(this.params.backParams)}`);
      try {
        const res = await getPayStatus(params);
        if (res.code === 'ok' && res.return_code === '0000') {
          demo.msg('success', '退货成功');
          this.moduleSync('pos');
        } else {
          this.loading = false;
          this.timeout2Show = true;
        }
        this.$_actionLog(logList.refund, `再次查询结果，${JSON.stringify(res)}`);
      } catch (error) {
        this.loading = false;
        this.timeout2Show = true;
        this.$_actionLog(logList.refund, `再次查询结果异常，网络${pos.network.isConnected() ? '正常' : '异常'}`);
      }
    },
    /**
     * 接口请求倒计时
     */
    refundTimer() {
      let no = 39;
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.loadingText = `处理中，请稍后... (${no}秒)`;
      this.timer = setInterval(() => {
        no--;
        if (no < 0) {
          clearInterval(this.timer);
          this.loading = false;
          if ([7, 8].includes(this.params.accountId)) {
            this.timeoutShow = true;
          } else {
            this.timeout3Show = true;
          }
        } else {
          this.loadingText = `处理中，请稍后... (${no}秒)`;
        }
      }, 1000)
    },
    /**
     * 刷新部分退货列表
     */
    refreshConfirm() {
      this.refreshTipsShow = false;
      this.closeScreen2();
      this.$emit('refresh');
    },

    /**
     * 判断是否需要打印小票
     */
    judgePrintTicket(newOutTradeNo) {
      if (this.permit_print) {
        if (!this.setting_small_printer) {
          return demo.msg('warning', this.$msg.not_setting_small_printer);
        }
        this.printTicket(newOutTradeNo);
      }
    },

    /**
     * 打印小票
     */
    async printTicket(newOutTradeNo) {
      const printGoodsList = _.cloneDeep(this.params.backParams.leftGoodsList);
      for (let i = 0; i < printGoodsList.length; i++) {
        const goodsItem = printGoodsList[i];
        goodsItem.amt = Math.abs(Number(goodsItem.amt));
        goodsItem.qty = Math.abs(Number(goodsItem.qty)).toFixed(this.weighUnitList.indexOf(goodsItem.unitName) !== -1 ? 3 : 2);
        goodsItem.itemDisc = +goodsItem.itemDisc;
      }
      const printParams = {
        printername: this.setting_small_printer,
        storename: this.username,
        pay_amt: -(+this.params.shouldBackMoney),
        change_amt: 0,
        createAt: new Date().format('yyyy-MM-dd hh:mm:ss'),
        goods: printGoodsList,
        operater: this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员',
        printNum: demo.$store.state.show.smallPrinterCopies
      };
      // 会员退(查询会员信息，退货小票需显示会员信息)
      if (this.params.accountId === 6) {
        const params = {
          systemName: $config.systemName,
          phone: this.sys_uid,
          id: this.params.backParams.vipId
        };
        if (!pos.network.isConnected()) {
          return demo.msg('warning', '本地网络处于离线状态，会员/云同步功能无法使用');
        }
        const res = await getVipInfo(params);
        if (res) {
          this.vipInfo.member_value = res.name;
          this.vipInfo.member_money_value = Number(res.hasMoney).toFixed(2);
          this.vipInfo.member_mobile_value = res.mobile;
          this.vipInfo.member_point_value = res.integral;
        }
      }
      pos.printer.printPOSTimes(this.params.accountId === 6 ? Object.assign(printParams, this.vipInfo) : printParams,
        { from: 'sales_detail', orderno: newOutTradeNo, buy_back: 2 });
    },
    // 现金收款的情况下打开钱箱
    openMoneybox() {
      if (!this.setting_hasmoneybox) {
        return;
      }
      try {
        if (
          this.setting_moneybox == null ||
          this.setting_moneybox.trim() === ''
        ) {
          if (
            this.setting_small_printer == null ||
            this.setting_small_printer.trim() === ''
          ) {
            demo.msg('warning', this.$msg.select_printer);
            return;
          }
          external.openCashBox(
            this.setting_small_printer,
            function () {
              //
            },
            () => {
              demo.msg('warning', this.$msg.cash_box_in_error);
            }
          );
        } else {
          external.openCashBox_separate(
            this.setting_moneybox,
            2400,
            8,
            function () {
              //
            },
            () => {
              demo.msg('warning', this.$msg.cash_box_in_error);
            }
          );
        }
      } catch (e) {
        demo.msg('warning', '钱箱异常，请检查钱箱');
      }
    }
  },
  computed: mapState({
    setting_hasmoneybox: state => state.show.setting_hasmoneybox,
    setting_moneybox: state => state.show.setting_moneybox,
    setting_small_printer: state => state.show.setting_small_printer,
    permit_print: state => state.show.permit_print,
    loginInfo: state => state.show.loginInfo,
    username: state => state.show.username,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    storeList: state => state.show.storeList,
    token: state => state.show.token,
    commonSign: state => state.show.commonSign,
    weighUnitList: state => state.show.weighUnitList,
    appSecret: state => state.show.app_secret
  })
};
</script>

<style lang="less" scoped>
.theme-color {
  color: @themeBackGroundColor;
}
.back-type-container {
  overflow: hidden;
}
.popover-bg {
  background: #000;
  color: #fff;
}
.print-title {
  font-size: 18px;
}
.cb {
  margin-top: -1px;
}
.remark-container {
  .remark {
    font-size: 18px;
    color: @themeFontColor;
    font-weight: 700;
    line-height: 18px;
    margin-top: 24px;
    margin-left: 20px;
  }
  .input-container {
    margin: 14px 20px 10px 20px;
    font-weight: normal !important;
  }
}
.divider {
  height: 91px;
  width: 1px;
  background: #e3e6eb;
  margin: 20px 0;
}
.pc_fip2_1 {
  margin-top: 9px;
  font-size: 35px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
}
.pc_fip21 {
  line-height: 21px;
  font-size: 20px;
  font-weight: 700;
  float: right;
  cursor: pointer;
  color: @themeFontColor;
  margin-left: 25px;
  margin-right: 22px;
  margin-top: 18px;
}
.pc_fip21 img {
  float: left;
}
.pc_fip21 div {
  float: left;
  margin-left: 10px;
}
.pc_fip61 {
  font-size: 18px;
  color: @themeFontColor;
  font-weight: 700;
  line-height: 18px;
  margin-top: 20px;
  float: left;
  margin-left: 20px;
}
.pc_fip16 {
  width: 680px;
  border-radius: 10px;
  background: #f5f8fb;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_fip17 {
  width: 637px;
  height: 131px;
  background-color: #ffffff;
  border-radius: 10px;
  border: solid 1px #e3e6eb;
  display: flex;
  color: @themeFontColor;
  margin: 0 auto;
  margin-top: 40px;
  font-weight: 700;
}
.pc_fip18_1 {
  width: 50%;
  margin-top: 28px;
  text-align: center;
}
.pc_fip19 {
  font-size: 18px;
  line-height: 18px;
}
.pc_fip2 {
  margin-top: 22px;
  font-size: 35px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  outline: 0 !important;
}
.pc_fip62 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  overflow: hidden;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: right;
  line-height: 20px;
  float: left;
  margin-top: 20px;
  margin-left: 12px;
}
.pc_fip35 {
  width: 250px;
  height: 60px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 20px;
}
.pc_fip36 {
  width: 250px;
  height: 60px;
  background: rgba(125, 125, 125, 0.5);
  border-radius: 10px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 50px;
}
.pc_fip37 {
  width: 250px;
  height: 60px;
  background: rgba(125, 125, 125, 0.5);
  border-radius: 4px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 80px;
}

.pay-type {
  overflow: hidden;
  margin-left: 20px;
}

.pc_fip51 {
  float: left;
  min-width: 200px;
  height: 50px;
  padding: 0 10px;
  background: #fff;
  text-align: center;
  margin-right: 13px;
  line-height: 50px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
  font-size: 16px;
  font-weight: 700;
  position: relative;
  color: @themeBackGroundColor;
}

.bingo {
  top: 0;
  right: 0;
  position: absolute;
}
</style>
