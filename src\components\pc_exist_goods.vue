<style lang="less">
.com_exist1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 3000;
  color: @themeFontColor;
  .el-table th {
    height: 50px;
    background: #FAFAFA;
  }
  .el-table td {
    height: 50px;
    padding: 5px 0;
  }
  .el-table {
    font-size: 16px;
    color: @themeFontColor;
  }
  .el-table__empty-block {
    height: 150px !important;
  }
}
.com_exist2 {
  background: #fff;
  margin: 0 auto;
  position: relative;
  width: 900px;
  height: 365px;
  margin-top: 168px;
  border-radius: 10px;
  z-index: 1500;
  overflow: hidden;
}
.com_exist3 {
  margin: 0 30px;
  margin-top: 20px;
}
.com_exist4 {
  width: 70px;
  height: 36px;
  border: 1px solid #CFA26B;
  box-sizing: border-box;
  border-radius: 4px;
  color: #CFA26B;
  text-align: center;
  line-height: 32px;
}
.com_exist5 {
  font-size: 16px;
  color: #B2C3CD;
  float: right;
  display: flex;
  margin-top: 20px;
  line-height: 45px;
  .com_exist5_1 {
    background: #CFA26B;
    border: 1px solid #CFA26B;
    box-sizing: border-box;
    border-radius: 4px;
    width: 110px;
    height: 44px;
    text-align: center;
    font-size: 18px;
    color: #FFFFFF;
    line-height: 40px;
    display: inline;
    margin-left: 22px;
    cursor: pointer;
  }
}
.com_exist6{
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #CFA26B;
    text-align: center;
    color: #FFFFFF;
    line-height: 20px;
    font-size: 16px;
    margin-top: 1px;
    cursor: pointer;
    display: inline-block;
    margin-left: 6px;
}
.com_exist7 {
  padding: 8px 16px;
}
.com_exist8 {
  height: 60px;display: flex;justify-content: space-between;align-items: center;
  border-bottom: 1px solid #E3E6EB;margin: 0 30px;
}
</style>
<template>
  <!--新增商品弹出框-->
  <div v-show="showExistGoods" class="com_exist1">
    <div class="com_pad11"></div>
    <div class="com_exist2">
      <div class="com_exist8">
        <div style="color: @fontColor;font-size: 18px;font-weight: bold;line-height: 60px;">
          编辑商品选择
        </div>
        <div
          style="font-size: 30px;color: #8298A6;cursor: pointer;"
          @click="closeExistGoods()"
        >×</div>
      </div>
      <div class="com_exist3" style="overflow: auto;">
        <el-table
          :data="existTableData"
          stripe
          border
          height="200"
          style="width: 100%; overflow:auto;">
          <el-table-column
            prop="name"
            label="商品名称"
            align="left"
            show-overflow-tooltip
            min-width="36%">
          </el-table-column>
          <el-table-column
            prop="code"
            label="商品条码"
            align="left"
            show-overflow-tooltip
            min-width="18%">
          </el-table-column>
          <el-table-column
            label="原因"
            align="left"
            show-overflow-tooltip
            min-width="24%">
            <template slot-scope="scope">
              <div>
                {{scope.row.errMsg}}
                <el-popover
                  @show="setClass"
                  popper-class="pc_pay192 com_exist7"
                  placement="bottom-end"
                  width="326"
                  trigger="click"
                  content="商品唯一标识码是由商品名称或条码加密后产生">
                  <div v-show="scope.row.errMsg === '商品唯一标识码重复'" class="com_exist6" slot="reference">?</div>
                </el-popover>
                </div>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="status"
            label="状态"
            align="left"
            min-width="10%">
          </el-table-column>
          <el-table-column
            align="center"
            min-width="12%">
            <template slot-scope="scope">
              <div @click="handleClick(scope.row)" class="com_exist4">编辑</div>
            </template>
          </el-table-column>
        </el-table>
        <div class="com_exist5">
          商品库里存在重复商品，继续新增商品需修改重复的信息
          <div class="com_exist5_1" @click="closeExistGoods()">返回新增</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {};
  },
  methods: {
    ...mapActions([SET_SHOW]),
    setClass () {
      let arr = document.getElementsByClassName('popper__arrow');
      for (let i = 0; i < arr.length; i++) {
        arr[i].setAttribute('class', 'popper__arrow pc_pay193');
      }
    },
    closeExistGoods () {
      this.SET_SHOW({ showExistGoods: false });
    },
    handleClick (row) {
      if (row.errMsg === '商品名及商品条码重复') {
        this.SET_SHOW({ nameExist: true });
        this.SET_SHOW({ codeExist: true });
      }
      if (row.errMsg === '商品唯一标识码重复') {
        this.SET_SHOW({ nameExist: false });
        this.SET_SHOW({ codeExist: false });
      }
      this.SET_SHOW({ showExistGoods: false });
      if (row.status === '已删除') {
        row.status = '禁用';
      }
      this.SET_SHOW({ goodsDetail: row });
    }
  },
  watch: {},
  computed: mapState({
    loginInfo: state => state.show.loginInfo,
    showExistGoods: state => state.show.showExistGoods,
    existTableData: state => state.show.existTableData,
    nameExist: state => state.show.nameExist,
    codeExist: state => state.show.codeExist
  })
};
</script>
