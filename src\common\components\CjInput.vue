<template>
  <div class="content" :style="{ width: `${width}${isNaN(width) ? '' : 'px'}` }">
    <div
      v-if="label"
      class="label"
      :class="[
        {
          labelRequired: required
        },
        labelClass
      ]"
      :style="labelStyle"
    >
      {{ label }}
    </div>
    <div
      class="inputWrap"
      :class="[
        {
          inputBorder: border,
          inputDisabled: disabled,
          activeClass: isFocus,
          inputError: errorMessage
        },
        styleClass
      ]"
      :style="wrapStyle"
    >
      <slot v-if="$slots.leftIcon" name="leftIcon"></slot>
      <div v-if="leftIcon" class="cjInputLeftIcon">
        <i :class="[leftIcon]" @click="leftIconClick" />
      </div>
      <div class="inputValue">
        <div class="inputBody">
          <input
            ref="input"
            class="input"
            :style="inputStyle"
            v-model="inputValue"
            :readonly="readonly"
            :disabled="disabled"
            :placeholder="placeholder"
            @blur="onBlur"
            @focus="onFocus"
            @input="onInput"
          />
          <i v-show="clearShow" class="cjInputClear el-icon-error" @mousedown.stop="handleClear" />
          <div v-if="rightIcon" class="cjInputRightIcon">
            <i :class="[rightIcon]" @click="rightIconClick" />
          </div>
          <slot v-if="$slots.rightIcon" name="rightIcon"></slot>
        </div>
      </div>
      <!-- 错误信息提示 -->
      <div
        v-if="errorMessage"
        class="errorMessage"
        :style="{
          textAlign: errorMessageAlign
        }"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>
<script>
import { goodsNameFormat, vipNameFormat } from '@/common/inputLimitUtils.js';
import { stringFormat, intNumberFormat } from '@/common/utils/zgzn-input-utils.esm.js';
import { isEmpty } from 'frontend-utils';
// 全角转半角
const fullToHalf = str => {
  // ￥的编码为\uFFE5,取消该匹配
  return str.replace(/[\uFF00-\uFFE4\uFFE6-\uFFEF]/g, function (c) {
    return String.fromCharCode(c.charCodeAt(0) - 0xfee0);
  });
};
// 半角转全角
const halfToFull = str => {
  return str.replace(/[\x21-\x7E]/g, function (c) {
    return String.fromCharCode(c.charCodeAt(0) + 0xfee0);
  });
};
export default {
  name: 'CjInput',
  props: {
    value: {
      type: [Number, String],
      default: ''
    },
    // 输入框左侧文本
    label: {
      type: String,
      default: ''
    },
    // 输入框类型, 可选值为 text文本 tel电话号码 digit正整数 number数值 password密码 goodsName商品名称/首字母/条码/扫码
    // vipName 会员名称 stock库存 price金额
    type: {
      type: String,
      default: 'text'
    },
    // 输入框的宽度，存数字时单位为px
    width: {
      type: [Number, String],
      default: '100%'
    },
    // 输入框的高度，存数字时单位为px
    height: {
      type: [Number, String],
      default: 48
    },
    // 字体大小，单位为px
    fontSize: {
      type: [Number, String],
      default: 16
    },
    // 背景颜色
    backgroundColor: {
      type: String,
      default: '#FFFFFF'
    },
    // 内边距，用于调整输入框距离外边框间距
    padding: {
      type: String,
      default: '0px 10px'
    },
    // 输入的最大字符长度，默认0不限制长度，只在type=text生效
    maxlength: {
      type: [Number, String],
      default: 0
    },
    // 是否过滤特殊字符，只在type=text生效
    specialFilter: {
      type: Boolean,
      default: false
    },
    // 是否过滤emoji字符，只在type=text生效
    emojiFilter: {
      type: Boolean,
      default: true
    },
    // 是否过滤中文字符，只在type=text生效
    chineseFilter: {
      type: Boolean,
      default: false
    },
    // 汉字/全角字符是否按照双倍长度计算，只在type=text生效
    chineseDouble: {
      type: Boolean,
      default: false
    },
    // 数值类型最大值，默认null不做限制，只在type=number生效
    maxNumber: {
      type: Number,
      default: null
    },
    // 数值类型最小值，默认null不做限制，只在type=number生效
    minNumber: {
      type: Number,
      default: null
    },
    // 半角全角控制，默认半角half，可选全角full，不做控制''，只在type=digit,number,text生效
    halfFull: {
      type: String,
      default: 'half'
    },
    // 数值舍入方法，可选round四舍五入，floor向下舍入，ceil向上舍入，只在type=number生效
    roundType: {
      type: String,
      default: ''
    },
    // 最大小数点位数，只在type=number生效
    decimal: {
      type: Number,
      default: -1
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: '请输入...'
    },
    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },
    // 是否禁用输入框
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否显示必填标识
    required: {
      type: Boolean,
      default: false
    },
    // 是否可清除
    clearable: {
      type: Boolean,
      default: true
    },
    // 显示清除按钮的时机，focus获取焦点切有值时显示，always有值时一直显示
    clearTrigger: {
      type: String,
      default: 'focus'
    },
    // 清除成功后是否自动获取焦点
    clearFocus: {
      type: Boolean,
      default: true
    },
    // 是否自动获取焦点
    autofocus: {
      type: Boolean,
      default: false
    },
    // 获取焦点时是否选中输入框中的内容
    focusSelect: {
      type: Boolean,
      default: false
    },
    // 底部错误提示文案，为空时不展示
    errorMessage: {
      type: String,
      default: ''
    },
    // 错误提示文案对齐方式，可选值为 center right
    errorMessageAlign: {
      type: String,
      default: 'left'
    },
    // 内容格式化方法
    formatter: {
      type: Function,
      default: null
    },
    // 格式化函数触发的时机，可选值为 onBlur
    formatTrigger: {
      type: String,
      default: 'onChange'
    },
    // label宽度
    labelWidth: {
      type: [Number, String],
      default: '6.2em'
    },
    // label字体大小
    labelFontSize: {
      type: Number,
      default: 18
    },
    // 左侧label文字对齐方式，可选center,right
    labelAlign: {
      type: String,
      default: 'left'
    },
    // 左侧文本额外类名
    labelClass: {
      type: String,
      default: ''
    },
    // 风格 square 方形输入框 circle 椭圆输入框
    styleClass: {
      type: String,
      default: 'square'
    },
    // 输入内容对齐方式，可选值center，right
    inputAlign: {
      type: String,
      default: 'left'
    },
    // 左侧图标名称，element-ui的icon
    leftIcon: {
      type: String,
      default: ''
    },
    // 左侧图标名称，element-ui的icon
    rightIcon: {
      type: String,
      default: ''
    },
    // 是否开启防抖
    debounced: {
      type: Boolean,
      default: false
    },
    // 防抖延迟量
    delay: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      inputValue: '',
      isFocus: false,
      timer: null
    };
  },
  computed: {
    // 整体样式
    wrapStyle() {
      const height = `${this.height}${isNaN(this.height) ? '' : 'px'}`;
      const fontSize = `${this.fontSize}${isNaN(this.fontSize) ? '' : 'px'}`;
      return `minHeight: ${height}; fontSize: ${fontSize}; backgroundColor: ${this.backgroundColor}; padding: ${this.padding}`;
    },
    // label样式
    labelStyle() {
      const hasunit = isNaN(this.labelWidth);
      return `width: ${this.labelWidth}${hasunit ? '' : 'px'}; textAlign: ${this.labelAlign}; fontSize: ${this.labelFontSize}px`;
    },
    // 输入内容样式
    inputStyle() {
      return `textAlign: ${this.inputAlign}`;
    },
    // 是否显示清除图标
    clearShow() {
      if (this.clearable && String(this.inputValue) && !this.readonly) {
        if (this.clearTrigger === 'focus') {
          return this.isFocus;
        }
        return true;
      } else {
        return false;
      }
    }
  },
  watch: {
    value: {
      handler(newValue) {
        // 处理直接修改引用组件的v-model值时不触发input事件的问题
        if (newValue !== this.inputValue) {
          this.inputValue = newValue;
          this.onInput();
        }
      },
      immediate: true
    }
  },
  mounted() {
    if (this.autofocus) {
      this.$nextTick(() => {
        this.$refs.input.focus();
      });
    }
  },
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    blur() {
      this.$refs.input.blur();
    },
    // 失去焦点
    onBlur(e) {
      this.isFocus = false;
      if (this.formatTrigger === 'onBlur' && typeof this.formatter === 'function') {
        this.inputValue = this.formatter(this.inputValue);
        this.$emit('input', this.inputValue);
      }
      this.$emit('blur', e);
    },
    // 获取焦点
    onFocus(e) {
      this.isFocus = true;
      this.$emit('focus', e);
      if (this.focusSelect && this.inputValue) {
        this.$refs.input.select();
      }
    },
    /**
     * 整数数字输入格式化
     * @param {Number,string} inputValue 要格式化的数字
     * @param {object} option 配置项，详见defaultOption
     * @returns 格式化后的数字字符串（由于需处理正则替换，所以转为字符串），需自行转为数字（注：全角数字只能为字符串，不可转为数字）
     */
    floatNumberFormat(inputValue, option = {}) {
      const defaultOption = {
        maxNumber: null, // 最大值，默认null不限制
        minNumber: null, // 最小值，默认null不限制
        halfFull: 'half', // 半角全角控制，默认半角half，可选全角full，不做控制''
        decimal: -1, // 最大小数点位数
        isZeroPad: false, // 是否零填充
        roundType: '', // 数字舍入方法，可选round四舍五入，floor向下舍入，ceil向上舍入
        symbol: '', // 数字符号，可选-负数，+正数，''不做控制
        default: 0 // 默认值，输入值不符合要求时返回
      };
      const mergeOption = { ...defaultOption, ...option };
      // 不符合字符串或者数字的值直接返回默认值
      if (typeof inputValue !== 'string' && (isNaN(inputValue) || inputValue === null)) {
        return mergeOption.default;
      }
      // 将输入值转为字符串方面后续正则处理
      let value = String(inputValue);
      // 先转为半角进行处理，防止传入的为全角
      value = fullToHalf(value);
      if (value === '.') {
        value = '0.'
      }
      // 是否为负数
      const isMinus = value.indexOf('-') === 0 || mergeOption.symbol === '-';
      // 去除多余小数点正则
      const pointReg = /^([^.]*\.)|\.+/g;
      // 去除所有非数字或者小数点正则
      const numberReg = /[^\d.]/g;
      value = value.replace(pointReg, '$1').replace(numberReg, '');
      // 最大小数点位数
      if (mergeOption.decimal > -1) {
        // 10的 n 次幂
        const multiplier = 10 ** mergeOption.decimal;
        // 是否处理舍入，必须设定最大小数点位数才能生效
        // 输入值切分成单个字符的数组，反转，判断小数点下标是否>=传入的小数点后几位
        if (mergeOption.roundType && value.split('').reverse().indexOf('.') >= mergeOption.decimal) {
          value = String(Math[mergeOption.roundType](value * multiplier) / multiplier);
        } else if (value.includes('.')) {
          // 不处理舍入 判断是否包含小数点
          // 包含 则根据小数点后几位进行截取
          value = value.slice(0, value.indexOf('.') + mergeOption.decimal + (mergeOption.decimal > 0 ? 1 : 0));
        }
      }
      // 去除头部的无效0
      const arr = value.split('.');
      value = `${arr[0] !== '' ? Number(arr[0]) : arr[0]}${arr[1] !== undefined ? `.${arr[1]}` : ''}`;
      value = `${isMinus && mergeOption.symbol !== '+' ? '-' : ''}${value}`;
      // 是否设置最大值
      if (value !== '' && !isNaN(mergeOption.maxNumber) && !isEmpty(mergeOption.maxNumber) && Number(value) > Number(mergeOption.maxNumber)) {
        value = String(mergeOption.maxNumber);
      }
      // 是否设置最小值
      if (value !== '' && !isNaN(mergeOption.minNumber) && !isEmpty(mergeOption.minNumber) && Number(value) < Number(mergeOption.minNumber)) {
        value = String(mergeOption.minNumber);
      }
      if (mergeOption.halfFull === 'full') {
        // 字符串输入才可以转全角半角
        value = halfToFull(value);
      }
      return value;
    },
    // 输入值
    onInput() {
      const value = this.inputValue;
      switch (this.type) {
        case 'tel':
          this.inputValue = intNumberFormat(value);
          break;
        case 'digit':
          this.inputValue = intNumberFormat(value, {
            maxNumber: this.maxNumber,
            minNumber: this.minNumber,
            halfFull: this.halfFull,
            symbol: '+'
          });
          break;
        case 'number':
          this.inputValue = this.floatNumberFormat(value, {
            maxNumber: this.maxNumber,
            minNumber: this.minNumber,
            halfFull: this.halfFull,
            roundType: this.roundType,
            decimal: this.decimal
          });
          break;
        case 'goodsName':
          this.inputValue = goodsNameFormat(value);
          break;
        case 'vipName':
          this.inputValue = vipNameFormat(value);
          break;
        case 'stock':
          this.inputValue = this.floatNumberFormat(value, {
            maxNumber: 99999.999,
            minNumber: -99999.999,
            decimal: 3
          });
          break;
        case 'price':
          this.inputValue = this.floatNumberFormat(value, {
            maxNumber: this.maxNumber === null ? 999999.99 : this.maxNumber,
            minNumber: 0,
            decimal: 2,
            symbol: '+'
          });
          break;
        case 'text':
          this.inputValue = stringFormat(value, {
            maxLen: this.maxlength,
            specialFilter: this.specialFilter,
            emojiFilter: this.emojiFilter,
            chineseFilter: this.chineseFilter,
            chineseDouble: this.chineseDouble,
            halfFull: this.halfFull
          });
          break;
      }
      if (this.formatTrigger === 'onChange' && typeof this.formatter === 'function') {
        this.inputValue = this.formatter(this.inputValue);
      }
      this.handleModel();
    },
    // 清除
    handleClear(e) {
      this.inputValue = '';
      this.$emit('input', '');
      this.$emit('clear', e);
      if (this.clearFocus) {
        setTimeout(() => {
          this.focus();
        }, 0);
      }
    },
    // 赋值
    handleModel() {
      if (!this.debounced) {
        this.$emit('input', this.inputValue);
      } else {
        if (this.timer) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.$emit('input', this.inputValue);
        }, this.delay);
      }
    },
    // 左侧图片点击
    leftIconClick(e) {
      this.$emit('click-left-icon', e);
    },
    // 右侧图标点击
    rightIconClick(e) {
      this.$emit('click-right-icon', e);
    }
  }
};
</script>
<style lang="less" scoped>
.content {
  position: relative;
  display: flex;
  align-items: center;
  .label {
    flex-shrink: 0;
    font-weight: bold;
  }
}
.inputWrap {
  display: flex;
  margin: 0;
  position: relative;
  box-sizing: border-box;
  align-items: center;
  flex-grow: 1;
  transition: border-color 0.3s ease-in-out;
  max-width: 100%;
  .cjInputLeftIcon {
    margin-right: 4px;
    color: #c0c4cc;
  }
  .inputValue {
    flex-grow: 1;
    .inputBody {
      display: flex;
      align-items: center;
      .cjInputClear {
        flex-shrink: 0;
        cursor: pointer;
        font-size: 24px;
        color: #c8c9cc;
        padding-left: 8px;
      }
      .cjInputRightIcon {
        padding-left: 8px;
        color: #c0c4cc;
      }
    }
  }
  .input {
    width: 100%;
    border: none;
    flex-grow: 1;
    margin: 0;
    padding: 0;
    background-color: inherit;
  }
  .input:disabled {
    cursor: not-allowed;
    opacity: 1;
    color: #c8c9cc;
    -webkit-text-fill-color: #c8c9cc;
    background-color: transparent;
  }
  .input::placeholder {
    color: #c8c9cc;
  }
  .errorMessage {
    position: absolute;
    bottom: -20px;
    width: 100%;
    left: 0;
    height: 18px;
    line-height: 18px;
    font-size: 14px;
    color: #ee0a24;
  }
}
.labelRequired::before {
  content: '*';
  color: #ee0a24;
  font-size: 18px;
  z-index: 1;
  position: absolute;
  left: -10px;
}
.inputBorder {
  border: 1px solid #e5e8ec;
}
.square {
  border-radius: 5px;
}
.circle {
  border-radius: 24px;
}
.inputDisabled .label {
  color: #c8c9cc;
}
.activeClass {
  border-color: @themeBackGroundColor;
}
.inputError {
  border-color: #ee0a24;
}
.large {
  font-size: 18px;
  padding: 16px;
}
</style>
