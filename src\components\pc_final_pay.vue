<style lang="less" scoped>
.pay-button {
  border-radius: 5px;
  margin-top: 10px;
  border: 1px solid #bda169;
  color: #bda169;
  background: #fff;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
.pay-title {
  margin-top: 5px;
  font-weight: 600;
  text-align: center;
  font-size: 14px;
  color: #567485;
}
.pay-code {
  margin: 0 auto;
  margin-top: 12px;
  overflow: hidden;
  display: flex;
}
.pay-qrcode {
  width: 122px;
  margin-top: 3px;
}
.pc_fin_pay_calculator_settlement {
  height: 320px;
  width: 655px;
  margin: 0 auto;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 22px;
  position: relative;
  background: #fff;
}
.pc_fin_pay_calculator_settlement table {
  text-align: center;
  color: #688392;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  font-size: 36px;
  margin-top: -1px;
  margin-left: -1px;
}
.pc_fin_pay_calculator_settlement table td {
  border: 1px solid #e3e6eb;
  width: 20%;
  cursor: pointer;
}
.pc_pay_calculator_blue {
  color: @themeBackGroundColor !important;
}
.pc_fip1 {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(80, 80, 80, 0.55);
  top: 0;
  left: 0;
  z-index: 2000;
  overflow: auto;
}
.pc_fip11 {
  width: 894px;
  background: #f5f8fb;
  overflow: hidden;
  color: @themeFontColor;
  z-index: 10;
  margin: 0 auto;
  border-radius: 10px;
  position: relative;
}
.pc_fip12 {
  height: 140px;
  overflow: hidden;
  width: 658px;
  background-color: #ffffff;
  border-radius: 10px;
  margin-top: 14px;
  margin-left: 30px;
  display: flex;
  font-weight: 700;
}
.pc_fip13 {
  width: 152px;
  height: 140px;
  background-color: #ffffff;
  border-radius: 8px;
  border: solid 1px #e3e6eb;
  text-align: center;
  line-height: 140px;
  font-size: 20px;
  font-weight: 700;
  color: @themeFontColor;
  margin-left: 22px;
  cursor: pointer;
  margin-top: 36px;
  position: relative;
}
.pc_fip14 {
  width: 152px;
  height: 116px;
  border-radius: 8px;
  border: solid 1px #e3e6eb;
  background-color: #ffffff;
  margin-left: 22px;
  margin-top: 15px;
  cursor: pointer;
  text-align: center;
  font-size: 19px;
  font-weight: bold;
  line-height: 118px;
  position: relative;
}
.pc_fip14 img {
  position: absolute;
  right: -1px;
  margin-top: -1px;
  width: 34px;
  height: 35px;
}
.pc_fip14_1 {
  position: relative;
  width: 152px;
  height: 120px;
  border-radius: 8px;
  border: solid 1px #e3e6eb;
  background-color: #ffffff;
  margin-left: 22px;
  margin-top: 15px;
  cursor: pointer;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  overflow: hidden;
}
.pc_fip14_2 {
  position: relative;
  width: 152px;
  height: 84px;
  border-radius: 8px;
  border: solid 1px #e3e6eb;
  background-color: #ffffff;
  margin-left: 22px;
  margin-top: 14px;
  cursor: pointer;
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  line-height: 84px;
}
.pc_fip14_2 img {
  position: absolute;
  right: -1px;
  margin-top: -1px;
  width: 34px;
  height: 35px;
}
.payWayPaying {
  .pc_fip13::after,
  .pc_fip14_2::after,
  .pc_fip14::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
  }
}
.autoPayCancel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
}
.smf_guide_gif {
  left: 0;
  top: 0;
  width: 113px !important;
  height: 22px !important;
}
.pc_fip15 {
  line-height: 21px;
  font-size: 20px;
  font-weight: bold;
  color: @themeFontColor;
  margin-top: 28px;
  margin-left: 30px;
  display: inline-block;
  position: relative;
}
.edit-point-disable-mark {
  width: 24px;
  height: 24px;
  position: absolute;
  margin: 0 !important;
  background-color: rgba(0, 0, 0, 0.2);
  cursor: not-allowed !important;
}
.pc_fip15 div {
  margin-left: 10px;
  display: inline-block;
  cursor: pointer;
}
.pc_fip15 img {
  vertical-align: top;
  cursor: pointer;
}
.pc_fip16 {
  width: 680px;
  border-radius: 10px;
  background: #f5f8fb;
  margin: 0 auto;
  margin-top: 141px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_fip17 {
  width: 637px;
  height: 131px;
  background-color: #ffffff;
  border-radius: 10px;
  border: solid 1px #e3e6eb;
  display: flex;
  color: @themeFontColor;
  margin: 0 auto;
  margin-top: 52px;
  font-weight: 700;
}
.pc_fip18 {
  width: 33%;
  margin: 24px 0px 20px;
  text-align: center;
}
.pc_fip18_1 {
  width: 50%;
  margin-top: 28px;
  text-align: center;
}
.pc_fip19 {
  font-size: 18px;
  line-height: 18px;
}
.pc_fip2 {
  margin-top: 22px;
  font-size: 35px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  outline: 0 !important;
}
.pc_fip2_1 {
  margin-top: 9px;
  font-size: 35px;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
}
.pc_fip21 {
  line-height: 21px;
  font-size: 20px;
  font-weight: 700;
  margin-top: 28px;
  float: right;
  cursor: pointer;
  color: @themeFontColor;
  margin-left: 25px;
  position: relative;
}
.pc_fip21 img {
  float: left;
}
.pc_fip21 div {
  float: left;
  margin-left: 10px;
}

.pc_fip22 {
  width: 165px;
  height: 145px;
  border: 2px solid #41af37;
  border-radius: 10px;
  text-align: center;
  float: left;
  margin-left: 135px;
}
.pc_fip22 img {
  width: 40px;
  height: 40px;
  margin-top: 30px;
}
.pc_fip22 div {
  line-height: 16px;
  color: #41af37;
  font-size: 16px;
  margin-top: 15px;
}
.pc_fip23 {
  overflow: hidden;
  font-size: 20px;
  font-weight: bold;
  color: @themeBackGroundColor;
  margin-top: 15px;
}
.pc_fip27 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_fip28 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  /* margin-left: 72px; */
  margin-left: 56px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.pc_fip29 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.shortCutTag {
  display: inline-block;
  margin-top: 0px !important;
  background-color: @input_backgroundColor;
  border-radius: 4px;
  font-size: 12px;
  height: 30px;
  width: 60px;
  text-align: center;
  line-height: 30px;
  color: @text;
}
.pc_fip3 {
  width: 120px;
  height: 100px;
  border: 1px solid #41af37;
  border-radius: 10px;
  text-align: center;
  float: left;
  margin-left: 30px;
}
.pc_fip3 img {
  width: 40px;
  height: 40px;
  margin-top: 17px;
}
.pc_fip3 div {
  line-height: 16px;
  color: #41af37;
  font-size: 16px;
  margin-top: 10px;
}
.pc_fip31 {
  margin: 0;
  margin-left: 30px;
  margin-top: 21px;
  width: 658px;
}
.pc_fip32 {
  width: 193px;
  height: 281px;
  border: 2px solid @input_backgroundColor;
  border-radius: 8px;
  float: left;
  margin-left: 20px;
  margin-top: 20px;
  cursor: pointer;
  background: @input_backgroundColor;
}
.pc_fip32 div {
  overflow: hidden;
  width: 110px;
  margin: 0 auto;
}
.pc_fip32 img {
  width: 110px;
  margin-top: 75px;
}
.pc_fip33 {
  width: 400px;
  margin: 0 auto;
}
.pc_fip34 {
  text-align: center;
  margin: 0 auto;
  margin-top: 55px;
  font-size: 48px;
  width: 100px;
  height: 100px;
  line-height: 100px;
  background: #8e8e8e;
  border-radius: 50%;
  color: #fff;
}
.pc_fip35 {
  width: 250px;
  height: 60px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 20px;
}
.pc_fip36 {
  width: 250px;
  height: 60px;
  background: rgba(125, 125, 125, 0.5);
  border-radius: 10px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 50px;
}
.pc_fip37 {
  width: 250px;
  height: 60px;
  background: rgba(125, 125, 125, 0.5);
  border-radius: 4px;
  text-align: center;
  line-height: 60px;
  font-size: 20px;
  color: #fff;
  font-weight: 700;
  margin-top: 27px;
  cursor: pointer;
  float: left;
  margin-left: 80px;
}
.pc_fip38 {
  overflow: hidden;
}
.pc_fip38 input {
  width: 376px;
  height: 60px;
  line-height: 60px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border: 1px solid #bbb;
  float: left;
  margin-top: 30px;
  margin-left: 40px;
  text-indent: 20px;
  font-size: 20px;
  font-weight: bold;
}
.pc_fip38 div {
  width: 160px;
  height: 60px;
  line-height: 60px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background: @themeBackGroundColor;
  text-align: center;
  color: #fff;
  float: left;
  margin-top: 30px;
  font-size: 20px;
  font-weight: bold;
}
.pc_fip39 {
  text-align: center;
  margin-top: 95px;
  font-size: 30px;
  color: @themeFontColor;
  line-height: 30px;
}
.payFailTitle {
  display: flex;
  align-items: center;
  justify-content: center;
  color: @warningRed;
  font-weight: 500;
  margin-top: 55px;
  .cj-icon {
    font-size: 48px;
    margin-right: 16px;
  }
}
.failTips {
  font-size: 20px;
  text-align: center;
  margin-top: 16px;
}
.pc_fip_btn1 {
  border-radius: 4px;
  text-align: center;
  font-size: 20px;
  width: 388px;
  height: 50px;
  line-height: 50px;
  color: #fff;
  margin: 0 auto;
  background: @themeBackGroundColor;
  cursor: pointer;
}
.pc_fip_btn2 {
  text-align: center;
  font-size: 16px;
  width: 120px;
  line-height: 44px;
  color: @themeBackGroundColor;
  margin: 70px auto 0;
  cursor: pointer;
}
.pc_fip4 {
  overflow: hidden;
  margin-top: 125px;
}
.pc_fip4 div {
  border-radius: 4px;
  text-align: center;
  font-size: 20px;
  width: 150px;
  height: 54px;
  line-height: 54px;
  color: #fff;
  float: left;
  background: #909399;
  margin-left: 35px;
}
.pc_fip41 {
  text-align: center;
  margin-top: 32px;
  font-size: 20px;
  color: #999;
  line-height: 20px;
}
.pc_fip42 {
  border-radius: 4px;
  text-align: center;
  margin: 0 auto;
  margin-top: 32px;
  font-size: 20px;
  width: 150px;
  height: 54px;
  line-height: 54px;
  background: #ff6159;
  color: #fff;
}
.pc_fip43 {
  width: 160px;
  height: 60px;
  line-height: 60px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background: rgba(125, 125, 125, 0.5);
  text-align: center;
  color: #fff;
  float: left;
  margin-top: 30px;
  font-size: 20px;
  font-weight: bold;
}
.pc_fip44 {
  margin: 0;
  margin-left: 41px;
  margin-top: 16px;
  width: 637px;
  height: 320px;
}
.pc_fip45 {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
  z-index: 30;
}
.pc_fip46 {
  width: 130px;
  height: 156px;
  position: absolute;
  bottom: 0;
  right: 0;
  cursor: pointer;
  z-index: 40;
}
.pc_fip47 {
  width: 834px;
  height: 372px;
  border: 1px solid #e3e6eb;
  border-radius: 8px;
  margin: 0 auto;
  margin-top: 23px;
  background: #fff;
}
.pc_fip48 {
  width: 650px;
  margin: 0 auto 12px;
  margin-top: 30px;
}
.pc_fip48 div {
  text-align: center;
  margin-top: 35px;
  font-size: 17px;
  color: #b4b8bf;
  line-height: 16px;
}
.pc_fip49 {
  width: 100%;
  margin-top: 22px;
  max-width: 678px;
  margin-left: 30px;
}
.pc_fip49 table {
  border-collapse: inherit;
  border-spacing: 10px;
  width: 100%;
  margin-left: -10px;
}

.pc_fip49 td {
  border: 1px solid #e3e6eb;
  width: 20%;
  background: #fff;
  height: 70px;
  border-radius: 4px;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: @themeFontColor;
}
.pc_fip50_div {
  color: @themeFontColor;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
}
.pc_fip51 {
  float: left;
  width: 150px;
  height: 60px;
  background: #fff;
  text-align: center;
  margin-right: 13px;
  line-height: 60px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 20px;
  font-size: 18px;
  font-weight: 700;
  position: relative;
}
.pc_combine_1 {
  height: 131px;
  overflow: hidden;
  width: 637px;
  background-color: #ffffff;
  border-radius: 10px;
  border: solid 1px #e3e6eb;
  margin-top: 22px;
  margin-left: 41px;
  display: flex;
  font-weight: bold;
}
.pc_combine_12 {
  height: 132px;
  overflow: hidden;
  width: 833px;
  background-color: #ffffff;
  border-radius: 10px;
  margin-top: 14px;
  margin-left: 30px;
  display: flex;
  font-weight: 700;
}
.pc_combine_17 {
  width: 100%;
  padding: 17px 30px 0px 30px;
  /deep/.el-checkbox-group {
    display: flex;
    justify-content: space-between;
  }
  /deep/.el-checkbox-button {
    border-radius: 4px;
    height: 50px;
    /deep/.el-checkbox-button__inner {
      border-radius: 4px !important;
      border: none;
      font-size: 18px;
      color: @themeFontColor;
      font-weight: 700;
      height: 50px;
    }
  }
  /deep/.el-checkbox-button.is-disabled {
    display: flex;
    border-radius: 4px;
    // border: 1px solid #E3E6EB;
    /deep/.el-checkbox-button__inner {
      border-radius: 4px !important;
      font-size: 18px;
      color: #e3e6eb;
      // font-family: 'Microsoft YaHei';
      font-weight: 700;
    }
  }
  /deep/.el-checkbox-button__inner {
    border-radius: 4px !important;
    border-left: 1px solid #e3e6eb;
  }
  /deep/.el-checkbox-button.is-focus {
    display: flex;
    // border: 1px solid #B4995A;
    // color: #B4995A;
    border-radius: 4px !important;
    .el-checkbox-button__inner {
      border-radius: 4px !important;
      // border: 1px solid #E3E6EB;
      color: @themeFontColor;
    }
  }
  /deep/.el-checkbox-button.is-checked {
    .el-checkbox-button__inner {
      color: #fff;
    }
  }
}
.checkbox_flex {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.pc_combine_18 {
  width: 50%;
  text-align: center;
  font-size: 20px;
  margin: 20px 0;
}
.pc_combine_20 {
  font-size: 20px;
  line-height: 22px;
}
.pc_combine_19 {
  font-size: 18px;
  line-height: 40px;
  display: inline-block;
  height: 76px;
}
.pc_combine_49 {
  width: 100%;
  margin: 12px 31px 10px 31px;
  max-width: 832px;
  display: flex;
  justify-content: space-between;
  .pc_combine_choose {
    width: 310px;
    height: 312px;
    /deep/.el-input__inner {
      height: 54px;
      font-size: 26px;
      line-height: 22px;
      font-weight: 700;
      text-align: right;
      background: url(../image/icon_rmb.png) no-repeat 0 0;
      background-size: 26px 26px;
      background-position: 20px 14px;
    }
    /deep/.el-input__inner:disabled {
      opacity: 0.5;
    }
  }
  .pc_combine_choose1 {
    width: 310px;
    height: 130px;
    border: 1px solid #fff;
    background: #fff;
    border-radius: 6px;
  }
  .pc_combine_choose2 {
    width: 310px;
    height: 130px;
    margin-top: 10px;
    border: 1px solid #fff;
    background: #fff;
    border-radius: 6px;
  }
}
.pc_combine_49 table {
  border-collapse: inherit;
  border-spacing: 10px;
  width: 536px;
  margin-top: -10px;
}
.pc_combine_49 td {
  width: 120px !important;
  background: #fff;
  height: 60px;
  border-radius: 4px;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: @themeFontColor;
}
.pc_combine_45 {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: not-allowed;
  z-index: 30;
}
.pc_combine_46 {
  width: 130px;
  height: 156px;
  position: absolute;
  bottom: 0;
  right: 0;
  cursor: pointer;
  z-index: 40;
}
.pc_pop_18 {
  color: rgba(255, 255, 255, 0.9) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  border: 0px;
  padding: 7px;
  min-width: 1px;
  margin-bottom: 0px;
}
.pc_pop_19 {
  margin-top: 15px;
  font-size: 35px;
  width: 200px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
  outline: 0 !important;
}
.pc_combine_15 {
  line-height: 21px;
  font-size: 18px;
  font-weight: bold;
  color: @themeFontColor;
  margin-top: 21px;
  margin-left: 30px;
  display: inline-block;
  position: relative;
}
.pc_combine_15 img {
  vertical-align: top;
  cursor: pointer;
}
.pc_combine_15 div {
  margin-left: 10px;
  display: inline-block;
  cursor: pointer;
}
.pc_combine_21 {
  line-height: 21px;
  font-size: 18px;
  font-weight: 700;
  margin-top: 21px;
  float: right;
  margin-right: 30px;
  cursor: pointer;
  color: @themeFontColor;
  margin-left: 25px;
}
.pc_combine_21 img {
  float: left;
}
.pc_combine_21 div {
  float: left;
  margin-left: 10px;
}
.icon_close {
  font-size: 30px;
  color: #8298a6;
  cursor: pointer;
  float: right;
  margin-right: 50px;
  margin-top: -10px;
  display: flex;
  align-items: center;
}
.memberPass_div {
  /deep/ .el-input__inner {
    margin-left: 30px;
    margin-right: 30px;
    width: 320px;
    height: 60px;
    border-radius: 4px;
    background-color: #f5f8fb;
    border: 0px;
    color: @themeBackGroundColor;
    font-size: 20px;
    font-weight: bold;
  }
  .member_password_input {
    background-color: #f5f8fb;
    border-radius: 4px;
    width: 320px;
    height: 60px;
    border: 0px;
    color: @themeFontColor;
    font-size: 20px;
    font-weight: bold;
    padding-left: 30px;
  }
}
.pc_pass1 {
  overflow: hidden;
  margin-top: 40px;
  font-size: 20px;
}
.pc_pass2 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
  font-weight: normal;
}
.pc_pass3 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background-color: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-weight: normal;
}
.pc_fip52 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 100;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}
.pc_fip53 {
  width: 450px;
  height: 300px;
  border-radius: 6px;
  background: #fff;
  margin: 0 auto;
  margin-top: 240px;
  overflow: hidden;
}
.pc_fip54 {
  line-height: 24px;
  font-size: 24px;
  margin-top: 40px;
  color: @themeFontColor;
  font-weight: bold;
  text-align: center;
}
.pc_fip55 {
  font-size: 24px;
  line-height: 24px;
  margin-top: 30px;
  text-align: center;
  color: @themeFontColor;
}
.pc_fip56 {
  font-size: 18px;
  line-height: 18px;
  margin-top: 25px;
  text-align: center;
  color: #b2c3cd;
  font-weight: normal;
}
.pc_fip57 {
  font-size: 18px;
  line-height: 18px;
  margin-top: 15px;
  text-align: center;
  color: #b2c3cd;
  font-weight: normal;
}
.pc_fip58 {
  overflow: hidden;
  margin-top: 30px;
}
.pc_fip59 {
  width: 140px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  float: left;
  margin-left: 70px;
  font-size: 20px;
}
.pc_fip6 {
  width: 140px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 4px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  margin-left: 30px;
  font-size: 20px;
}
.pc_rqv53 {
  width: 450px;
  height: 300px;
  border-radius: 6px;
  background: #fff;
  margin: 0 auto;
  margin-top: 240px;
  overflow: hidden;
}
.pc_rqv54 {
  line-height: 40px;
  font-size: 24px;
  margin-top: 40px;
  color: @themeFontColor;
  font-weight: bold;
  text-align: center;
}
.pc_rqv55 {
  font-size: 24px;
  line-height: 35px;
  margin-top: 30px;
  text-align: center;
  color: @themeFontColor;
  padding: 0 85px;
}
.pc_rqv58 {
  overflow: hidden;
  margin-top: 30px;
  display: flex;
}
.pc_rqv6 {
  width: 140px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 4px;
  justify-content: center;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  font-size: 20px;
  margin: 0 auto;
  cursor: pointer;
}
.pc_paycode_span {
  color: @themeBackGroundColor;
  cursor: pointer;
  font-size: 18px;
  font-family: Microsoft YaHei, sans-serif;
  font-weight: 600;
}
.pay_active_border {
  border: 2px solid @themeFocusBorderColor;
}
.pc_jinjian_code_img {
  color: rgba(255, 255, 255, 0.9) !important;
  background: #fff !important;
  margin-top: 12px !important;
  border: 0px;
  left: 727px !important;
}
.pc_fip61 {
  font-size: 18px;
  color: @themeFontColor;
  font-weight: 700;
  line-height: 18px;
  margin-top: 20px;
  float: left;
  margin-left: 20px;
}
.pc_fip62 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  overflow: hidden;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: right;
  line-height: 20px;
  float: left;
  margin-top: 20px;
  margin-left: 12px;
}
.pc_fip63 {
  width: 270px;
  height: 40px;
  border-radius: 20px;
  overflow: hidden;
  margin: 5px 0 0 360px;
  background: #fff;
  position: relative;
  .normal_div {
    width: 100px;
    float: left;
    line-height: 40px;
    height: 40px;
    font-size: 18px;
    border-radius: 20px;
    text-align: center;
    font-weight: 700px;
    cursor: pointer;
  }
  .showTag {
    width: 170px;
  }
}
.payKindPaying::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
}
.pc_fip64 {
  position: relative;
  z-index: 800;
  height: 276px;
  margin: 0 auto;
  margin-top: 240px;
  background: #fff;
  width: 450px;
  overflow: hidden;
  border-radius: 5px;
}
.pc_fip65 {
  width: 328px;
  overflow: hidden;
  white-space: nowrap;
  font-size: 18px;
  text-overflow: ellipsis;
  float: left;
  margin-left: 40px;
  font-weight: 500;
  span {
    font-size: 24px;
    font-weight: bold;
  }
}
.pc_fip65_1 {
  margin-left: 30px;
  font-weight: 500;
  font-size: 18px;
  span {
    font-size: 24px;
    font-weight: bold;
  }
}
.pc_fip65_2 {
  font-size: 18px;
  line-height: 30px;
  margin-top: 25px;
  margin-left: 40px;
  font-weight: 500;
  span {
    font-size: 25px;
    font-weight: bold;
  }
  .spe-color {
    color: @themeBackGroundColor;
  }
  .warning-color {
    color: @warningRed;
  }
}
.pc_fip66 {
  float: left;
  width: 275px;
  margin-top: 40px;
  margin-left: 47px;
  font-size: 14px;
  color: @themeFontColor;
  font-weight: bold;
}
.pc_fip67 {
  font-size: 16px;
  width: 100%;
  text-align: center;
  color: #1f2a65;
  font-weight: bold;
  margin-top: -10px;
}
.pc_fip68 {
  font-size: 16px;
  width: 100%;
  text-align: center;
  color: #41af37;
  font-weight: bold;
  margin-top: -6px;
}
.pc_fip69 {
  font-size: 16px;
  width: 100%;
  text-align: center;
  color: #00a0e9;
  font-weight: bold;
  margin-top: -4px;
}
.isPayIndex {
  border-color: @themeBackGroundColor;
  color: @themeBackGroundColor;
}
.isPayIndex1 {
  border-color: #e3e6eb;
  color: @themeFontColor;
}
.payWay {
  background: @themeBackGroundColor;
  color: #fff;
}
.payWay1 {
  color: @themeFontColor;
}
/deep/.el-dialog__header {
  display: none;
}
.pc_fip191 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin: 0 auto;
  background: @themeBackGroundColor;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.no-smf__div {
  margin-top: 70px;
  display: flex;
  flex-direction: column;
  i {
    margin-right: 5px;
  }
  &--refresh {
    width: 114px;
    height: 42px;
    background: @themeBackGroundColor;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    border-radius: 8px;
    cursor: pointer;
    font-weight: normal;
    font-size: 16px;
  }
  &--status {
    display: inline-block;
    width: 191px;
    color: @warningRed;
    margin-top: 10px;
  }
}
.smf-status {
  height: 200px;
  &__title {
    width: 100%;
    text-align: center;
    font-size: 25px;
    color: @themeFontColor;
    font-weight: bold;
  }
  &__text {
    width: 100%;
    text-align: center;
    font-size: 25px;
    color: @themeFontColor;
    margin-top: 25px;
  }
  &__div {
    overflow: hidden;
    margin-top: 30px;
    font-size: 24px;
  }
  &__btn {
    width: 138px;
    height: 50px;
    text-align: center;
    line-height: 48px;
    margin: 0 auto;
    background: @themeBackGroundColor;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
  }
}
#pcFip2 {
  color: @themeBackGroundColor;
}
#againAttempt {
  background: @themeBackGroundColor;
  cursor: pointer;
}
#payment {
  color: @text;
}
.edit-point {
  width: 68px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  color: @themeBackGroundColor;
  border-radius: 4px;
  border: 1px solid @themeBackGroundColor;
  font-size: 16px;
  font-weight: normal;
  margin-left: 0px !important;
}
.edit-point-disable {
  color: #C8C9CC;
  border-color: #C8C9CC;
  cursor: not-allowed !important;
}
.point-mo {
  margin-right: 10px;
}
.tag {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 11;
  overflow: auto;
  display: flex;
  &-main {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 30px 40px;
    width: 502px;
    background: rgb(255, 255, 255);
    border-radius: 6px;
    margin: auto;
    overflow: auto;
    &_title {
      font-weight: 700;
      font-size: 24px;
      color: @themeFontColor;
      text-align: center;
      span {
        color: #ff6759;
      }
    }
    &_input {
      margin-top: 20px;
      /deep/ .el-input__inner {
        height: 60px;
        font-size: 24px;
        background: #f5f8fb;
      }
    }
    &_button {
      margin-top: 30px;
      text-align: center;
      font-size: 20px;
      div {
        cursor: pointer;
        width: 200px;
        height: 50px;
        line-height: 50px;
        display: inline-block;
        border-radius: 4px;
      }
      &__cancel {
        background: @themeFontColor;
        color: #ffffff;
        margin-right: 16px;
      }
      &__sure {
        background: @themeBackGroundColor;
        color: #ffffff;
      }
    }
  }
}
.point-st {
  max-width: 398px;
  word-break: break-all;
}
</style>
<template>
  <!-- 弹出结算界面的计算器 -->
  <div v-show="settlement" class="pc_fip1" :style="quickPayType ? 'visibility: hidden;' : ''">
    <el-dialog :visible="smfOpenStatus === '已开通'" width="450px" append-to-body :show-close="false" :close-on-click-modal="false">
      <div class="smf-status">
        <div class="smf-status__title">开通成功</div>
        <div class="smf-status__text">扫码支付已开通</div>
        <div class="smf-status__div">
          <div class="smf-status__btn" @click="smfOpenStatus = ''">确定</div>
        </div>
      </div>
    </el-dialog>
    <div v-show="show_continue_backPayVip || show_continue_backPayVipPoint" class="pc_fip52">
      <div class="pc_fip53">
        <div class="pc_fip54">提示</div>
        <div class="pc_fip55">会员积分不足，是否继续退款</div>
        <div class="pc_fip56">退款需扣除{{ show_continue_backPayVip_json.newPoint }}积分</div>
        <div class="pc_fip57">选择继续退款，会员积分会产生负数</div>
        <div class="pc_fip58">
          <div
            class="pc_fip59"
            @click="
              show_continue_backPayVip = false;
              show_continue_backPayVipPoint = false;
              closeSettlement();
            "
          >
            取消
          </div>
          <div class="pc_fip6" @click="continueBackPayVip()">继续退款</div>
        </div>
      </div>
    </div>
    <div v-show="showVipDetailTimeout" class="pc_fip52">
      <div class="pc_rqv53">
        <div class="pc_rqv54">提示</div>
        <div class="pc_rqv55">请求超时，导致开单失败请检查网络后重新结算</div>
        <div class="pc_rqv58">
          <div
            class="pc_rqv6"
            @click="
              showVipDetailTimeout = false;
              sonarRecode2();
            "
          >
            确定
          </div>
        </div>
      </div>
    </div>
    <div v-if="showPointTag" class="pc_fip52">
      <div class="pc_rqv53" style="height: 270px; width: 390px">
        <div class="pc_rqv54" style="margin-top: 20px">提示</div>
        <div class="pc_rqv55" style="padding: 0 33px">抵扣金额到“分”，反算后将使用{{ realPoint }}积分。</div>
        <div class="pc_rqv58">
          <div class="pc_rqv6" @click="sureEditPoint()">知道了</div>
        </div>
      </div>
    </div>
    <div style="position: fixed; width: 100%; height: 100%; z-index: 5"></div>
    <!-- 弹框内容 -->
    <div v-show="pc_return_goods" class="pc_fip16">
      <!-- 应退、实退、退款方式白色框 -->
      <div class="pc_fip17">
        <div class="pc_fip18_1">
          <div class="pc_fip19">应退金额</div>
          <el-popover
            :disabled="showFinalPrice.toString().length < 11"
            popper-class="pc_pay192 popper_self"
            placement="top"
            trigger="hover"
            :content="'¥' + showFinalPrice.toString()"
          >
            <div slot="reference" class="pc_fip2_1">
              <span>¥</span>
              {{ showFinalPrice }}
            </div>
          </el-popover>
        </div>
        <div class="pc_fip18_1">
          <div class="pc_fip19">实退金额</div>
          <el-popover
            :disabled="showFinalPrice.toString().length < 11"
            popper-class="pc_pay192 popper_self"
            placement="top"
            trigger="hover"
            :content="'¥' + showFinalPrice.toString()"
          >
            <div slot="reference" class="pc_fip2_1" style="color: @themeBackGroundColor">
              <span>¥</span>
              {{ showFinalPrice }}
            </div>
          </el-popover>
        </div>
      </div>
      <div style="overflow: hidden">
        <div class="pc_fip61">选择退款方式</div>
        <el-popover
          placement="right"
          content="仅会员卡退款支持原路退回退款，其它退款方式均需线下自行处理退款"
          popper-class="pc_fip5"
          width="324"
          style="background: #000; color: #fff"
          trigger="click"
        >
          <div slot="reference" class="pc_fip62">？</div>
        </el-popover>
        <div
          v-show="from !== 'vip_times_card' && from !== 'member_cancel'"
          class="pc_fip21"
          style="margin-right: 22px; margin-top: 18px"
          @click="button_paying ? clickButton('changePermitPrint') : ''"
        >
          <img alt="" v-show="!permit_print" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
          <img alt="" v-show="permit_print" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
          <div style="font-size: 18px">支付完成打印小票</div>
        </div>
      </div>
      <div style="overflow: hidden; margin-left: 20px">
        <div
          class="pc_fip51"
          v-for="(pa, index) in payWay"
          :key="index"
          @click="choosePay(index, pa.acctsId)"
          :class="index === payIndex ? 'isPayIndex' : 'isPayIndex1'"
          v-show="index !== 4 || (from === 'detail_member' && index === 4 && accountId === 6)"
        >
          <img alt="" src="../image/zgzn-pos/pc_pay_returngoods.png" v-show="index === payIndex" style="top: 0; right: 0; position: absolute" />
          {{ pa.name }}
        </div>
      </div>
      <div style="overflow: hidden">
        <div class="pc_fip37" @click="returnMoneySubmit('cancel')">取消</div>
        <div v-show="!button_paying" class="pc_fip36">退款中</div>
        <!-- 完成按钮 -->
        <div v-show="button_paying" @click="returnMoneySubmit('confirm')" class="pc_fip35">确认</div>
      </div>
      <div style="height: 40px"></div>
    </div>
    <div v-show="showMemberPassIpt" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 3000">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)"></div>
      <div class="memberPass_div pc_fip64">
        <div class="payWay1" style="width: 100%; text-align: center; font-size: 25px; margin-top: 36px; font-weight: bold">请输入支付密码</div>
        <div class="payWay1" style="width: 100%; margin-left: 60px; font-size: 25px; margin-top: 28px; font-weight: 100">
          <input
            class="member_password_input"
            id="combinedPayInput"
            type="password"
            v-model="input_member_password"
            @input="input_member_password = $allNumberLimit(input_member_password)"
            maxlength="6"
          />
        </div>
        <div class="pc_pass1">
          <div class="pc_pass2" @click="showMemberPassIpt = false">取消(Esc)</div>
          <div class="pc_pass3" @click="combinedCheckPass()">确定(Enter)</div>
        </div>
      </div>
    </div>
    <div
      class="pc_fip11"
      :style="isContentCombinedPay ? 'height: 640px;margin-top: 54px;' : 'height: 607px;margin-top: 57px;'"
      v-show="!pc_return_goods"
    >
      <div style="position: absolute; bottom: 8px; font-weight: bold; left: 45px; font-size: 18px" v-show="showMember && false">
        会员卡余额：¥ {{ member_money }}
      </div>
      <div style="float: left; overflow: hidden" :style="isSinglePayWay ? 'width: 689px;' : 'width: 894px;'">
        '
        <!-- 整单支付&组合支付 -->
        <div v-if="isContentCombinedPay" class="pc_fip50_div">
          <div class="icon_close" v-show="!isSinglePayWay" @click="closeSettlement('uninit')">
            <div style="font-size: 14px">
              （Esc）
              <i class="el-icon-close" style="font-size: 18px; font-weight: bold; margin-top: 14px"></i>
            </div>
          </div>
          <div class="pc_fip63" :class="{ payKindPaying: barcode_pay_status === 'paying' }">
            <div class="normal_div" @click="!button_paying ? '' : payTypeChange(0)" :class="isSinglePayWay ? 'payWay' : 'payWay1 showTag'">
              <div style="display: flex; justify-content: center; align-items: center">
                <span style="display: inline-block">整单支付</span>
                <div v-show="!isSinglePayWay" class="shortCutTag" style="margin-left: 5px; height: 24px; line-height: 24px; font-weight: bold">
                  Ctrl+Tab
                </div>
              </div>
            </div>
            <div class="normal_div" :class="!isSinglePayWay ? 'payWay' : 'payWay1 showTag'" @click="!button_paying ? '' : payTypeChange(1)">
              <div style="display: flex; justify-content: center; align-items: center">
                <span style="display: inline-block">组合支付</span>
                <div v-show="isSinglePayWay" class="shortCutTag" style="margin-left: 5px; height: 24px; line-height: 24px; font-weight: bold">
                  Ctrl+Tab
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 整单支付内容 -->
        <div v-show="isSinglePayWay">
          <!-- 应收实收找零 -->
          <div class="pc_fip12">
            <div class="pc_fip18">
              <div class="pc_fip19">
                应
                <span v-show="buy_back === 1">收</span>
                <span v-show="buy_back === 2">退</span>
              </div>
              <el-popover
                :disabled="showFinalPrice.toString().length < 11"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="'¥' + showFinalPrice.toString()"
              >
                <div slot="reference" class="pc_fip2">
                  <span>¥</span>
                  {{ showFinalPrice }}
                </div>
              </el-popover>
            </div>
            <div class="pc_fip18" style="border-left: 1px solid #e5e5e5; border-right: 1px solid #e5e5e5">
              <div class="pc_fip19">
                实
                <span v-show="buy_back === 1">收</span>
                <span v-show="buy_back === 2">退</span>
              </div>
              <div v-show="buy_back === 1">
                <!-- <el-popover
                  :disabled="Number(rightKeyword).toFixed(2).toString().length<9"
                  popper-class="pc_pay192 popper_self"
                  placement="top"
                  trigger="hover"
                  :content="'¥' + Number(rightKeyword).toFixed(2).toString()">
                  <div slot="reference" class="pc_fip2" id="pcFip2" v-show="rightKeyword.toString().length > 0 || Number(rightKeyword) > 0">
                    <span>¥</span>{{Number(rightKeyword).toFixed(2)}}
                  </div>
                </el-popover> -->
                <div class="pc_fip2" id="pcFip2" v-show="Number(rightKeyword) != 0 && rightKeyword != '' && !no_inputPrice">
                  <span>¥</span>
                  {{ Number(rightKeyword).toFixed(2) }}
                </div>
                <div class="pc_fip2" id="pcFip2" v-show="(Number(rightKeyword) == 0 || rightKeyword == '') && !no_inputPrice">
                  <span>¥</span>
                  0.00
                </div>
                <div v-show="no_inputPrice" class="pc_fip2" id="pcFip2">
                  <span>¥</span>
                  {{ Number(showFinalPrice).toFixed(2) }}
                  <!-- <el-popover
                    :disabled="showFinalPrice.toString().length<11"
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :content="'¥' + showFinalPrice.toString()">
                    <div slot="reference" class="pc_fip2" id="pcFip2"><span>¥</span>{{showFinalPrice}}</div>
                  </el-popover> -->
                </div>
              </div>
              <div class="pc_fip2" v-show="buy_back === 2" id="pcFip2">
                <span>¥</span>
                {{ Number(showFinalPrice).toFixed(2) }}
              </div>
            </div>
            <div class="pc_fip18">
              <div class="pc_fip19">找零</div>
              <el-popover
                :disabled="(Number(rightKeyword) - Number(showFinalPrice)).toFixed(2).toString().length < 9"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="'¥' + (Number(rightKeyword) - Number(showFinalPrice)).toFixed(2).toString()"
              >
                <div slot="reference" class="pc_fip2" v-show="Number(rightKeyword) > Number(showFinalPrice)" style="color: #ff6159">
                  <span>¥</span>
                  {{ (Number(rightKeyword) - Number(showFinalPrice)).toFixed(2) }}
                </div>
              </el-popover>
              <div class="pc_fip2" v-show="Number(rightKeyword) <= Number(showFinalPrice)" style="color: #ff6159">
                <span>¥</span>
                0.00
              </div>
            </div>
          </div>
          <!-- 积分打印 -->
          <div v-show="!ifautoCash" style="overflow: hidden">
            <!-- 现金div -->
            <div
              class="pc_fip15"
              @click="button_paying ? clickButton('changeUseIntegral') : ''"
              v-show="
                showMember && Number(member_integral) > 0 && showMemberIntegral && Number(final_integral_money) > 0 && from !== 'vip_times_card'
              "
            >
              <div v-if="barcode_pay_status !== 'new'" class="edit-point-disable-mark"></div>
              <img alt="" v-show="!use_integral" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
              <img alt="" v-show="use_integral" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
              <div class="point-st">
                使用账户
                <span style="color: #ff6159">{{ final_integral }}</span>
                积分抵现
                <span style="color: #ff6159">{{ final_integral_money }}</span>
                <span class="point-mo">元</span>
                <div v-if="$employeeAuth('modify_points_redemption')" class="edit-point"
                  :class="{'edit-point-disable': barcode_pay_status !== 'new'}"
                  @click.stop="openEditPoint()">
                  <i class="el-icon-edit"></i>
                  修改
                </div>
              </div>
            </div>
            <div
              v-show="from !== 'vip_times_card'"
              class="pc_fip21"
              :class="{ payKindPaying: barcode_pay_status === 'paying' }"
              @click="button_paying ? clickButton('changePermitPrint') : ''"
            >
              <img alt="" v-show="!permit_print" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
              <img alt="" v-show="permit_print" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
              <div>支付完成打印小票</div>
            </div>
            <div class="pc_fip21" v-show="from === 'vip_times_card'" style="height: 20px"></div>
          </div>
          <!-- 小键盘 -->
          <div v-show="!ifautoCash && acctsId === 1 && !lock_screen" class="pc_fip49">
            <div v-show="buy_back === 2" class="pc_fip45"></div>
            <div v-show="buy_back === 2" class="pc_fip46" @click="clickButton('back_paying')"></div>
            <table>
              <tr>
                <td @click="inputCalculator('7')">7</td>
                <td @click="inputCalculator('8')">8</td>
                <td @click="inputCalculator('9')">9</td>
                <td @click="plusMoney(100)" class="pc_pay_calculator_blue">¥100</td>
                <td @click="inputCalculator('back')" style="font-size: 20px; font-weight: bold">
                  <div style="line-height: 26px" class="pc_pay_calculator_blue">
                    删除
                    <br />
                    <span style="font-size: 16px">(Bksp)</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td @click="inputCalculator('4')">4</td>
                <td @click="inputCalculator('5')">5</td>
                <td @click="inputCalculator('6')">6</td>
                <td @click="plusMoney(50)" class="pc_pay_calculator_blue">¥50</td>
                <td @click="inputCalculator('del')" style="font-size: 20px; font-weight: bold">
                  <div style="line-height: 26px" class="pc_pay_calculator_blue">
                    清空
                    <br />
                    <span style="font-size: 16px">(Del)</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td @click="inputCalculator('1')">1</td>
                <td @click="inputCalculator('2')">2</td>
                <td @click="inputCalculator('3')">3</td>
                <td @click="plusMoney(10)" class="pc_pay_calculator_blue">¥10</td>
                <td
                  v-show="button_paying"
                  id="againAttempt"
                  style="font-size: 20px; font-weight: bold"
                  rowspan="2"
                  @click="
                    acctsId = 1;
                    clickButton('cash');
                  "
                >
                  <div style="line-height: 26px; color: #fff !important" class="pc_pay_calculator_blue">
                    确认
                    <br />
                    <span style="font-size: 16px">(Enter)</span>
                  </div>
                </td>
                <td v-show="!button_paying" style="font-size: 20px; background: rgba(125, 125, 125, 0.5); font-weight: bold" rowspan="2">支付中</td>
              </tr>
              <tr>
                <td @click="inputCalculator('0')">0</td>
                <td @click="rightKeyword.indexOf('.') == -1 ? inputCalculator('.') : ''">.</td>
                <td @click="inputCalculator('00')">00</td>
                <td @click="plusMoney(5)" class="pc_pay_calculator_blue">¥5</td>
              </tr>
            </table>
          </div>
          <!-- 会员支付 -->
          <div v-show="!ifautoCash && acctsId === 8" class="pc_fin_pay_calculator_settlement pc_fip31">
            <div style="font-size: 20px; overflow: hidden; margin-top: 50px; font-weight: bold">
              <div class="pc_fip65">
                姓名：
                <span>{{ member_name }}</span>
              </div>
              <div class="pc_fip65_1">
                手机号：
                <span>{{ show_mobile }}</span>
              </div>
            </div>
            <div v-if="member_money - showFinalPrice >= 0" class="pc_fip65_2">
              <div style="display: inline-block; width: 325px">
                会员卡余额：
                <span class="spe-color">¥ {{ Number(member_money).toFixed(2) }}</span>
              </div>
              <div style="display: inline-block">
                消费后余额：
                <span>¥</span>
                <span v-text="Number(member_money - showFinalPrice < 0 ? 0 : member_money - showFinalPrice).toFixed(2)"></span>
              </div>
            </div>
            <div v-else class="pc_fip65_2">
              <div style="display: inline-block; width: 500px">
                会员卡余额：
                <span class="warning-color">¥ {{ Number(member_money).toFixed(2) }}</span>
                <span style="font-size: 18px">（余额不足请充值）</span>
              </div>
            </div>
            <div class="pc_fip38">
              <input type="password" maxlength="6" v-model="input_member_password" placeholder="请输入支付码" />
              <div class="pc_fip43" v-show="!showMemberComplete">读取中</div>
              <div @click="clickButton('member_card')" v-show="showMemberComplete && button_paying">
                确定
                <span style="font-size: 16px">(Enter)</span>
              </div>
              <div style="background: rgba(125, 125, 125, 0.5)" v-show="showMemberComplete && !button_paying">支付中</div>
            </div>
          </div>
          <!-- 未开通扫码付提示 -->
          <div v-show="!ifautoCash && acctsId === 6 && aid === '' && isConnected" class="pc_fin_pay_calculator_settlement pc_fip31">
            <div class="pc_fip66">
              <div id="pcFip2" style="font-size: 20px; line-height: 22px">您尚未开通扫码支付收银功能</div>
              <div style="margin-top: 15px; line-height: 19px; font-size: 17px">扫码收银功能的优势</div>
              <div style="height: 5px"></div>
              <div style="line-height: 16px; margin-top: 10px">1、极速扫顾客付款码收银，不丢单不抹零。</div>
              <div style="line-height: 16px; margin-top: 10px">2、微信支付宝 D+1 到银行卡。</div>
              <div class="no-smf__div">
                <div class="no-smf__div--refresh" @click="getSmfOpenStatus">
                  <i class="el-icon-refresh"></i>
                  刷新状态
                </div>
                <div class="no-smf__div--status" v-if="smfOpenStatus === '未开通'">
                  <i class="el-icon-warning"></i>
                  <span>未开通，请扫描右侧二维码</span>
                  <br />
                  <span>申请开通</span>
                </div>
              </div>
            </div>
            <div class="payWay1" style="float: left; margin-left: 10px; margin-top: 40px; font-weight: bold; font-size: 15px">
              <div style="overflow: hidden; margin-left: 100px">
                <img src="../image/pc_pay_wxlogo.png" style="float: left; width: 36px; margin-left: 11px" />
                <img src="../image/pc_pay_zfblogo.png" style="float: left; width: 36px; margin-left: 57px" />
              </div>
              <div style="overflow: hidden; margin-left: 100px">
                <span>微信支付</span>
                <span style="margin-left: 21px">支付宝支付</span>
              </div>
              <div class="pay-code">
                <div v-if="showKefu">
                  <div class="pay-qrcode" ref="kefuSrcUrl"></div>
                  <div class="pay-button" style="margin-left: 3px">客服协助开通</div>
                  <div class="pay-title">
                    ↑请使用微信
                    <br />
                    “扫一扫”
                  </div>
                </div>
                <div :style="!showKefu ? 'margin-left: 120px;' : 'margin-left: 25px;'">
                  <div class="qrcode" ref="qrCodeUrl" style="margin-top: 3px"></div>
                  <div class="pay-button" style="margin-top: 11px">自助扫码开通</div>
                  <div class="pay-title">
                    ↑请使用手机浏览
                    <br />
                    器扫描二维码
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 断网扫码付提示 -->
          <div v-show="!ifautoCash && acctsId === 6 && !isConnected" style="text-align: center" class="pc_fin_pay_calculator_settlement pc_fip31">
            <svg style="margin-top: 40px" width="144" height="126" viewBox="0 0 144 126" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M72.0008 106.002C76.5228 106.002 80.6775 107.585 83.9455 110.22L72.0008 125.002L60.0562
                110.22C63.4347 107.483 67.6529 105.993 72.0008 106.002V106.002ZM13.7848 0.824219L125.739 112.785L116.784
                121.74L83.9772 88.9336C80.1144 87.6518 76.0707 86.9996 72.0008 87.0019C62.9568 87.0019 54.6475 90.1622
                48.1242 95.4379L40.1695 85.5832C48.4918 78.8412 58.7445 74.9238 69.4422 74.3986L53.0008 57.9572C43.9579
                60.5205 35.5291 64.8921 28.2248 70.8075L20.2575 60.9529C27.0836 55.4253 34.7512 51.027 42.9688 47.9252L28.5035
                33.4536C21.3031 36.8825 14.5324 41.149 8.33183 46.1645L0.364502 36.3162C6.1595 31.6296 12.4168 27.5065 19.0605
                24.0169L4.82317 9.78589L13.7848 0.824219V0.824219ZM87.8342 57.1402L73.3688 42.6812L72.0008 42.6685C91.6025 42.6685
                109.602 49.5149 123.744 60.9529L115.777 70.8075C107.616 64.1966 98.0624 59.5235 87.8342 57.1402V57.1402ZM72.0008
                11.0019C99.1392 11.0019 124.061 20.4829 143.631 36.3162L135.67 46.1645C117.66 31.5767 95.1775 23.633 72.0008
                23.6686C66.4402 23.6686 60.9808 24.1119 55.6608 24.9795L44.9258 14.2319C53.5962 12.1229 62.6718 11.0019
                72.0008 11.0019Z"
                fill="#E3E6EB"
              />
            </svg>
            <div id="pcFip2" style="margin-top: 20px; font-size: 20px">没有网络链接，</div>
            <div id="pcFip2" style="font-size: 20px">请检查网络并重新登录</div>
          </div>
          <!-- 扫码 -->
          <div v-show="!ifautoCash && acctsId === 6 && aid !== '' && barcode_pay_status == 'new'" class="pc_fip31" style="text-align: center">
            <div class="pc_fip48">
              <img style="width: 90%; height: 100%" src="../image/pc_pay_smgun.png" />
              <div>请使用扫码枪扫描客户付款码</div>
            </div>
            <el-popover v-show="isShowQrCode" popper-class="pc_jinjian_code_img" placement="top-end" trigger="click">
              <div style="text-align: center">
                <div class="qrcodenew" ref="qrCodeUrlNew" style="width: 140px; margin: 10px"></div>
                <div><span style="color: #b4b8bf">使用手机浏览器扫描二维码</span></div>
              </div>
              <span slot="reference" class="pc_paycode_span">开启扫码付款/微信实名认证/申请进度查询 ></span>
            </el-popover>
          </div>
          <!-- 线下支付 -->
          <div v-show="acctsId === 7" class="pc_fin_pay_calculator_settlement pc_fip31">
            <div
              class="pc_fip32"
              :class="payActiveIndex === 3 ? 'pay_active_border' : ''"
              @click="
                acctsId = 3;
                rightKeyword = showFinalPrice;
                clickButton('pos');
              "
            >
              <div><img alt="" src="../image/pc_pay_select_pos.png" /></div>
              <div class="pc_fip67">(F5)</div>
            </div>
            <div
              class="pc_fip32"
              :class="payActiveIndex === 4 ? 'pay_active_border' : ''"
              @click="
                acctsId = 4;
                rightKeyword = showFinalPrice;
                clickButton('wechat');
              "
            >
              <div><img alt="" style="margin-top: 71px" src="../image/pc_pay_select_wechat.png" /></div>
              <div class="pc_fip68">(F6)</div>
            </div>
            <div
              class="pc_fip32"
              :class="payActiveIndex === 5 ? 'pay_active_border' : ''"
              @click="
                acctsId = 5;
                rightKeyword = showFinalPrice;
                clickButton('alipay');
              "
            >
              <div><img alt="" style="margin-top: 69px" src="../image/pc_pay_select_zfb.png" /></div>
              <div class="pc_fip69">(F7)</div>
            </div>
          </div>
          <!-- 扫码支付中 -->
          <div v-show="acctsId === 6 && (barcode_pay_status == 'paying' || payQueryStatus)" class="pc_fip31" style="height: 360px">
            <div class="pc_fip33">
              <div class="pc_fip34">
                {{ lock_number }}
              </div>
              <div class="pc_fip41">正在处理中，请稍后...</div>
            </div>
          </div>
          <!-- 支付失败 -->
          <div v-show="acctsId === 6 && barcode_pay_status == 'fail' && !payQueryStatus" class="pc_fip31" style="height: 520px">
            <div class="pc_fip33" style="width: 580px">
              <div class="pc_fip39 payFailTitle">
                <i class="cj-icon cj-icon-close-circle" />
                <div>未查询到支付结果</div>
              </div>
              <div class="failTips">
                <div>支付结果可能有延迟，若顾客已完成支付</div>
                <div>请点击“再次查询”</div>
              </div>
              <div style="overflow: hidden; margin-top: 15px">
                <div id="againAttempt" class="pc_fip_btn1" @click="clickButton('checkZfStatus')">再次查询</div>
                <div class="pc_fip_btn2" @click="clickButton('cancel_barcode_fail')">重新扫码</div>
                <!-- <div @click="failButSaveSales()">
                  保留订单
                </div> -->
              </div>
            </div>
          </div>
          <!-- 会员卡充值中 -->
          <div v-show="from === 'member_recharge' && lock_screen == true && member_recharge_fail == false" class="pc_fip44">
            <div class="pc_fip33">
              <div class="pc_fip34">
                {{ lock_number }}
              </div>
              <div class="pc_fip41">扣款成功，会员卡充值中...</div>
            </div>
          </div>
          <!-- 会员卡充值失败 -->
          <div v-show="from === 'member_recharge' && lock_screen == true && member_recharge_fail == true" class="pc_fip31">
            <div class="pc_fip33">
              <div class="pc_fip39">未查询到充值结果</div>
              <div class="payWay1" style="text-align: center; margin-top: 20px; font-size: 16px; line-height: 16px">扣款成功，会员卡充值失败</div>
              <div class="pc_fip4">
                <div style="cursor: pointer" @click="clickButton('member_recharge_fail')">取消</div>
                <div id="againAttempt" style="color: #fff; margin-left: 30px; cursor: pointer" @click="clickButton('searchMember')">再次尝试</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 组合支付内容 -->
        <div v-show="!isSinglePayWay && isContentCombinedPay">
          <!-- 组合支付应收实收 -->
          <div class="pc_combine_12">
            <div class="pc_combine_18">
              <div class="pc_combine_20">
                应
                <span v-show="buy_back === 1">收</span>
                <span v-show="buy_back === 2">退</span>
              </div>
              <el-popover
                :disabled="showFinalPrice.toString().length < 11"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="'¥' + showFinalPrice.toString()"
              >
                <div slot="reference" class="pc_pop_19">
                  <span>¥</span>
                  {{ showFinalPrice }}
                </div>
              </el-popover>
            </div>
            <div v-show="button_paying" class="pc_combine_18" style="border-left: 1px solid #e5e5e5; border-right: 1px solid #e5e5e5">
              <div class="pc_combine_20">
                实
                <span v-show="buy_back === 1">收</span>
                <span v-show="buy_back === 2">退</span>
              </div>
              <div v-show="buy_back === 1">
                <el-popover
                  :disabled="Number(rightKeyword).toFixed(2).toString().length < 9"
                  popper-class="pc_pay192 popper_self"
                  placement="top"
                  trigger="hover"
                  :content="'¥' + Number(rightKeyword).toFixed(2).toString()"
                >
                  <div slot="reference" class="pc_pop_19" id="pcFip2">
                    <span>¥</span>
                    {{ Number(rightKeyword).toFixed(2) }}
                  </div>
                </el-popover>
              </div>
              <div class="pc_pop_19" v-show="buy_back === 2" id="pcFip2">
                <span>¥</span>
                {{ showFinalPrice }}
              </div>
            </div>
            <!-- 组合支付支付中显示总结算金额 -->
            <div v-show="!button_paying" class="pc_combine_18" style="border-left: 1px solid #e5e5e5; border-right: 1px solid #e5e5e5">
              <div class="pc_combine_20">实收</div>
              <div>
                <el-popover
                  :disabled="Number(combinedTotalPrice).toFixed(2).toString().length < 9"
                  popper-class="pc_pay192 popper_self"
                  placement="top"
                  trigger="hover"
                  :content="'¥' + Number(combinedTotalPrice).toFixed(2).toString()"
                >
                  <div slot="reference" class="pc_pop_19" id="pcFip2">
                    <span>¥</span>
                    {{ Number(combinedTotalPrice).toFixed(2) }}
                  </div>
                </el-popover>
              </div>
            </div>
          </div>
          <!-- 积分打印 -->
          <div v-show="!ifautoCash" style="overflow: hidden">
            <!-- 现金div -->
            <div
              class="pc_combine_15"
              @click="button_paying ? clickButton('changeUseIntegral') : ''"
              v-show="
                showMember && Number(member_integral) > 0 && showMemberIntegral && Number(final_integral_money) > 0 && from !== 'vip_times_card'
              "
            >
              <div v-if="barcode_pay_status !== 'new'" class="edit-point-disable-mark"></div>
              <img alt="" v-show="!use_integral" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
              <img alt="" v-show="use_integral" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
              <div class="point-st" style="width: 590px">
                使用账户
                <span style="color: #ff6159">{{ final_integral }}</span>
                积分抵现
                <span style="color: #ff6159">{{ final_integral_money }}</span>
                <span class="point-mo">元</span>
                <div v-if="$employeeAuth('modify_points_redemption')"
                  class="edit-point" :class="{'edit-point-disable': barcode_pay_status !== 'new'}"
                  @click.stop="openEditPoint()">
                  <i class="el-icon-edit"></i>
                  修改
                </div>
              </div>
            </div>
            <div v-show="from !== 'vip_times_card'" class="pc_combine_21" @click="button_paying ? clickButton('changePermitPrint') : ''">
              <img alt="" v-show="!permit_print" src="../image/zgzn-pos/pc_goods_checkbox1.png" />
              <img alt="" v-show="permit_print" src="../image/zgzn-pos/pc_goods_checkbox2.png" />
              <div>支付完成打印小票</div>
            </div>
          </div>
          <!-- 多选组合支付方式 -->
          <div class="pc_combine_17">
            <el-checkbox-group
              v-model="conbinaPayWayVal"
              :max="2"
              :fill="$t('image.homeImage.color')"
              @change="handlePaywayChange"
              :disabled="!button_paying"
            >
              <el-checkbox-button v-for="item in conbinaPayWayArr" v-show="item.value !== 6 || showMember" :key="item.value" :label="item.value">
                <div class="checkbox_flex">
                  <img
                    :src="conbinaPayWayVal.indexOf(item.value) === -1 ? item.icon : item.iconChecked"
                    alt=""
                    style="width: 22px; margin-right: 8px"
                  />
                  {{ item.label + item.hotKey }}
                </div>
              </el-checkbox-button>
            </el-checkbox-group>
          </div>
          <!-- 组合支付金额 -->
          <div v-show="!ifautoCash && !lock_screen" class="pc_combine_49">
            <div v-show="buy_back === 2" class="pc_combine_45"></div>
            <div v-show="buy_back === 2" class="pc_combine_46" @click="clickButton('back_paying')"></div>
            <div class="pc_combine_choose">
              <div class="pc_combine_choose1">
                <div style="margin: 17px 0 17px 20px">
                  <span id="payment" style="font-weight: 700; font-size: 18px" :class="chkNameArr[0] === undefined ? '' : 'payWay1'">
                    {{ chkNameArr[0] === undefined ? '未选择支付方式' : chkNameArr[0] + '支付' }}
                  </span>
                  <span id="pcFip2" style="font-size: 18px; font-weight: 500; float: right; margin-right: 30px" v-show="chkNameArr[0] === '现金'">
                    找零: {{ (Number(combinedAmount1) + Number(combinedAmount2) - showFinalPrice).toFixed(2) }}
                  </span>
                  <span id="pcFip2" style="font-size: 18px; font-weight: 500; float: right; margin-right: 30px" v-show="chkNameArr[0] === '会员'">
                    余额: {{ Number(member_money).toFixed(2) }}
                  </span>
                </div>
                <el-input
                  style="width: 260px; height: 54px; margin-left: 16px; background-color: #f5f8fb"
                  maxlength="10"
                  v-model="combinedAmount1"
                  :disabled="conbinaPayWayVal.length < 1 || !button_paying"
                  ref="combinedAmount1"
                  id="combinedAmount1"
                  @focus="
                    combinedFocus = 1;
                    selectContent(1);
                  "
                  @input="combinedAmount1 = $payPriceLimit(combinedAmount1)"
                  placeholder="0.00"
                  @blur="blurCombinePayMoney(0)"
                />
              </div>
              <div class="pc_combine_choose2">
                <div style="margin: 17px 0 17px 20px">
                  <span id="payment" style="font-weight: 700; font-size: 18px" :class="chkNameArr[1] === undefined ? '' : 'payWay1'">
                    {{ chkNameArr[1] === undefined ? '未选择支付方式' : chkNameArr[1] + '支付' }}
                  </span>
                  <span id="pcFip2" style="font-size: 18px; font-weight: 500; float: right; margin-right: 30px" v-show="chkNameArr[1] === '现金'">
                    找零: {{ (Number(combinedAmount1) + Number(combinedAmount2) - showFinalPrice).toFixed(2) }}
                  </span>
                  <span id="pcFip2" style="font-size: 18px; font-weight: 500; float: right; margin-right: 30px" v-show="chkNameArr[1] === '会员'">
                    余额: {{ Number(member_money).toFixed(2) }}
                  </span>
                </div>
                <el-input
                  style="width: 260px; height: 54px; margin-left: 16px; background-color: #f5f8fb"
                  maxlength="10"
                  :disabled="conbinaPayWayVal.length < 2 || !button_paying"
                  v-model="combinedAmount2"
                  id="combinedAmount2"
                  ref="combinedAmount2"
                  @input="combinedAmount2 = $payPriceLimit(combinedAmount2)"
                  @focus="
                    combinedFocus = 2;
                    selectContent(2);
                  "
                  @blur="blurCombinePayMoney(1)"
                  placeholder="0.00"
                ></el-input>
              </div>
            </div>
            <!-- 小键盘 -->
            <table>
              <tr>
                <td class="combinedKeybord" @click="combinedInputCalc('7')">7</td>
                <td class="combinedKeybord" @click="combinedInputCalc('8')">8</td>
                <td class="combinedKeybord" @click="combinedInputCalc('9')">9</td>
                <td class="combinedKeybord" @click="combinedInputCalc('back')" style="font-size: 20px; font-weight: bold">
                  <div style="line-height: 26px" class="pc_pay_calculator_blue">删除</div>
                </td>
              </tr>
              <tr>
                <td class="combinedKeybord" @click="combinedInputCalc('4')">4</td>
                <td class="combinedKeybord" @click="combinedInputCalc('5')">5</td>
                <td class="combinedKeybord" @click="combinedInputCalc('6')">6</td>
                <td class="combinedKeybord" @click="combinedInputCalc('del')" style="font-size: 20px; font-weight: bold">
                  <div style="line-height: 26px" class="pc_pay_calculator_blue">清空</div>
                </td>
              </tr>
              <tr>
                <td class="combinedKeybord" @click="combinedInputCalc('1')">1</td>
                <td class="combinedKeybord" @click="combinedInputCalc('2')">2</td>
                <td class="combinedKeybord" @click="combinedInputCalc('3')">3</td>
                <td v-show="button_paying" id="againAttempt" style="font-size: 20px; font-weight: bold" rowspan="2" @click="combinedPayCheck">
                  <div style="line-height: 26px; color: #fff">
                    确认
                    <br />
                    [Enter]
                  </div>
                </td>
                <td v-show="!button_paying" style="font-size: 20px; background: rgba(125, 125, 125, 0.5); font-weight: bold" rowspan="2">支付中</td>
              </tr>
              <tr>
                <td class="combinedKeybord" @click="combinedInputCalc('0')">0</td>
                <td class="combinedKeybord" @click="combinedInputCalc('.')">.</td>
                <td class="combinedKeybord" @click="combinedInputCalc('00')">00</td>
              </tr>
            </table>
          </div>
        </div>
      </div>

      <!-- 取消按钮 -->
      <div v-show="ifautoCash" class="pc_fip13" style="float: left; height: 140px"
        :class="{ autoPayCancel: barcode_pay_status === 'paying' }"
        @click="clickButton('cancel_pay_autocash')">取消</div>

      <div style="clear: both" v-show="ifautoCash"></div>

      <!-- 自助收银的两个二维码 -->
      <div v-show="ifautoCash" class="pc_fip47">
        <div class="pc_fip48">
          <img style="width: 100%" src="../image/pc_pay_smgun.png" />
          <div>请使用扫码枪扫描您付款码</div>
        </div>
      </div>

      <div
        v-show="ifpay && ifautoCash"
        style="height: 100%; text-align: center; position: absolute; width: 100%; padding: 40px; z-index: 999999; top: 0"
      >
        <div style="width: 100%; height: 100%; border: 1px solid #e3e6eb; border-radius: 8px; overflow: hidden; background: #fff">
          <img src="../image/pay_success.jpg" style="width: 484px; height: 416px; margin-top: 111px; margin-left: 0px" />
        </div>
      </div>

      <!-- 右侧支付方式 -->
      <div
        v-show="!ifautoCash && lock_screen == false"
        :class="{ payWayPaying: barcode_pay_status === 'paying' }"
        style="overflow: hidden; float: left"
      >
        <div class="pc_fip13" :style="isContentCombinedPay ? 'margin-top: 80px' : 'margin-top: 36px'" @click="clickButton('cancel_pay_sale')">
          取消
          <span style="font-size: 16px">(Esc)</span>
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" @click="handleClickChange(1)" :style="showAcctsId(1)">
          现金支付
          <span style="font-size: 16px">(F1)</span>
          <img v-show="acctsId === 1" src="../image/zgzn-pos/pc_fin_select.png" />
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" @click="handleClickChange(7)" :style="showAcctsId(7)">
          线下支付
          <span style="font-size: 16px">(F2)</span>
          <img v-show="acctsId === 7" src="../image/zgzn-pos/pc_fin_select.png" />
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" @click="handleClickChange(6)" :style="showAcctsId(6)">
          扫码支付
          <span style="font-size: 16px">(F3)</span>
          <img v-show="acctsId === 6" src="../image/zgzn-pos/pc_fin_select.png" />
          <img class="smf_guide_gif" v-if="appSecret === ''" src="../image/smf_guide.gif" />
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" v-show="showMember" @click="handleClickChange(8)" :style="showAcctsId(8)">
          会员支付
          <span style="font-size: 16px">(F4)</span>
          <img v-show="acctsId === 8" src="../image/zgzn-pos/pc_fin_select.png" />
        </div>
      </div>
      <!-- 右侧支付方式 -->
      <div v-show="!ifautoCash && lock_screen == true && isSinglePayWay" style="overflow: hidden; float: left">
        <div class="pc_fip13" style="background: #fff; color: #b4b8bf" :style="isContentCombinedPay ? 'margin-top: 80px' : 'margin-top: 36px'">
          取消
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" style="background: #fff">现金支付</div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" style="background: #fff">线下支付</div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" style="background: #fff">
          扫码支付
          <img class="smf_guide_gif" v-if="appSecret === ''" src="../image/smf_guide.gif" />
        </div>
        <div :class="showMember ? 'pc_fip14_2' : 'pc_fip14'" v-show="showMember" style="background: #fff">会员支付</div>
      </div>
    </div>
    <!-- final_pay_end -->
    <!-- 找零过高异常提示弹窗 -->
    <confirm-dialog
      :visible.sync="changeErrorTips"
      message="实收金额超出应收金额过多，<br/>请核实再收银，以防对账误差！"
      cancel-text="直接收银"
      confirm-text="我要检查"
      :throttle-time="1000"
      :close-on-click-modal="false"
      @cancel="clickButton('cash')"
      @confirm="changeErrorTips = false"
    ></confirm-dialog>
    <!-- 积分修改弹窗 -->
    <div v-if="editPoint" class="tag">
      <div class="tag-main">
        <div class="tag-main_title">
          积分抵现
          <span>￥{{ temFinalIntegralMoney }}</span>
        </div>
        <div class="tag-main_input">
          <el-input v-model="temFinalIntegral" @input="checkInput()"></el-input>
        </div>
        <div class="tag-main_button">
          <div class="tag-main_button__cancel" @click="editPoint = false">取消</div>
          <div class="tag-main_button__sure" @click="beforeointSure()">确定</div>
        </div>
      </div>
    </div>
    <confirm-dialog
      :visible.sync="refundFailShow"
      title="退款失败"
      :message="`
        当前会员的单据信息重复，请<br/>
        请联系系统售后处理
      `"
      :throttle="false"
      :show-cancel="false"
      confirm-text="知道了"
      @confirm="refundFailShow = false"
      :closeOnClickModal="false"
    />
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import QRCode from 'qrcodejs2';
import { getWxQrCode, getServiceImgUrl } from '@/api/wxQrCode';
import hotkeys from 'hotkeys-js';
import { Keyevent } from '@/utils/keyEvent.js';
import logList from '@/config/logList';
import { getZfbAuth, qcTradePay, qcQuery } from '@/api/pay';
import ConfirmDialog from '@/common/components/ConfirmDialog';
export default {
  components: {
    ConfirmDialog
  },
  data() {
    return {
      isPaySuccess: false, // 是否支付成功，用于防止重复做单
      payQueryStatus: false, // 支付状态
      queryTimer: null, // 主动查询计时器
      payFailMsg: '', // 扫码付支付失败原因
      temFinalIntegral: 0,
      isShowQrCode: false, // 是否显示提示二维码
      temFinalIntegralMoney: 0,
      editPoint: false,
      number_interval: null,
      isConnected: pos.network.isConnected(),
      isCash: 0,
      searchFlag: false,
      smfOpenStatus: '',
      payWay: [
        { name: '现金记账', acctsId: 1 },
        { name: 'POS记账', acctsId: 3 },
        { name: '微信记账', acctsId: 4 },
        { name: '支付宝记账', acctsId: 5 },
        { name: '会员卡退款', acctsId: 6 }
      ],
      detail_json: {},
      show_continue_backPayVip: false,
      show_continue_backPayVipPoint: false,
      show_continue_backPayVip_json: {
        newPoint: 0,
        oldPoint: 0
      },
      showVipDetailTimeout: false,
      show_continue_backPayVip_data: {},
      backStockData: {},
      payIndex: 0,
      buy_back: '',
      pc_return_goods: '',
      returnOutTradeNo: '',
      showFinalPrice: '',
      showMember: '',
      accountId: '',
      acctsId: '',
      hasVipAcctsIdArr: [1, 7, 6, 8],
      acctsIdArr: [1, 7, 6],
      ifautoCash: '',
      ifpay: '',
      rightKeyword: '',
      recordInputStyle: 0,
      member_password: '',
      input_member_password: '',
      member_money: '',
      // 结算字段
      companiesId: 2,
      member_mobile: '',
      member_id: '',
      member_name: '',
      member_integral: '',
      // 选择会员积分时，展现的要用final_integral积分，抵扣final_integral_money的钱
      final_integral: '',
      final_integral_money: '',
      show_mobile: '',
      // 初始化获取表单单号
      orderno: '',
      // 最后一次扫码付单号,再次查询用
      lastOutTradeNo: '',
      // 打开收银界面时的时间，请求第三方接口用
      only_time: '',
      pay_type: '',
      // 会员支付按钮-确定/支付中
      button_paying: true,
      // 点击结算后，初始的实收 = 应收
      no_inputPrice: true,
      set_ip: typeof returnCitySN != 'undefined' && returnCitySN.cip ? returnCitySN.cip : '127.0.0.1',
      reduce_time: '',
      pay_name: '',
      log_info: {
        from: '',
        pay_type: [],
        orderno: '',
        barcode_data: [],
        barcode_payinfo: [],
        member_data_pay_info: [],
        detail_data: [],
        member_data: []
      },
      showMemberComplete: false,
      from: '',
      barcode_pay_status: 'new',
      kefuSrc: '',
      lock_screen: false,
      lock_number: 0,
      barcode_count: 0,
      lock_check_zf_status: false,
      member_recharge_fail: false,
      lock_max_number: 20,
      use_integral: false,
      codeString: '',
      lastTime: 0,
      inputNum: '',
      payActiveIndex: 4,
      payActiveArr: [3, 4, 5],
      qrcode: null,
      qrcodenew: null,
      showMemberIntegral: false, // 根据设置显示是否抵扣现金
      printCardData: [],
      isSinglePayWay: true, // 整单支付
      conbinaPayWayArr: [
        {
          label: '现金',
          hotKey: '(F1)',
          value: 1,
          icon: require('../image/com_cash.png'),
          iconChecked: require('../image/com_cash_reverse.png')
        },
        {
          label: 'POS',
          hotKey: '(F5)',
          value: 3,
          icon: require('../image/com_pos.png'),
          iconChecked: require('../image/com_pos.png')
        },
        {
          label: '微信',
          hotKey: '(F6)',
          value: 4,
          icon: require('../image/com_wechat.png'),
          iconChecked: require('../image/com_wechat_reverse.png')
        },
        {
          label: '支付宝',
          hotKey: '(F7)',
          value: 5,
          icon: require('../image/com_zfb.png'),
          iconChecked: require('../image/com_zfb_reverse.png')
        },
        {
          label: '会员',
          hotKey: '(F4)',
          value: 6,
          icon: require('../image/com_vip.png'),
          iconChecked: require('../image/com_vip_reverse.png')
        }
      ],
      conbinaPayWayVal: [], // 多选按钮value select
      combinedAmount1: '',
      combinedAmount2: '',
      combinedFinalPay: [], // 最终支付金额 index0为第一栏 index1为第二栏
      combinedFocus: 0,
      // combiendWaitingPay: false,
      chkNameArr: [],
      saleWithoutStock: '', // 是否允许零库存销售 1:允许 0:不允许
      autoPacking: 1, // 是否允许自动拆包 1:允许 0:不允许
      showMemberPassIpt: false,
      combinedTotalPrice: '',
      stopMemberPay: false,
      tmp_use_integral: false,
      returnGoodsFlg: false,
      keyEvent: null, // 扫码或者键盘监听
      changeErrorTips: false, // 是否显示找零异常提示
      oFinalIntefral: 0,
      temShowFinalPrice: '',
      showPointTag: false,
      realPoint: 0,
      refundFailShow: false // 会员充值撤销失败原因是否显示
    };
  },
  watch: {
    showFinalPrice() {
      this.getKexian(this.showFinalPrice, 2);
      this.kexianDataFormat();
    },
    isSinglePayWay() {
      this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
    },
    acctsId() {
      console.log(this.acctsId, 'localTime:' + new Date().getTime());
      CefSharp.PostMessage(`acctsId: ${this.acctsId}, localTime: ${new Date().getTime()}`);
      this.inputNum = '';
      this.isConnected = pos.network.isConnected();
    },
    finalPayParam() {
      if (this.finalPayParam === '') {
        return;
      }
      this.from = this.finalPayParam.from;
      this.buy_back = this.finalPayParam.buy_back;
      this.pc_return_goods = this.finalPayParam.pc_return_goods;
      this.returnOutTradeNo = this.finalPayParam.returnOutTradeNo;
      this.showFinalPrice = this.finalPayParam.showFinalPrice;
      this.showMember = this.finalPayParam.showMember;
      this.acctsId = this.finalPayParam.acctsId;
      this.ifautoCash = this.finalPayParam.ifautoCash;
      this.ifpay = this.finalPayParam.ifpay;
      this.left_goods_list = this.finalPayParam.left_goods_list;
      this.totalPrice = this.finalPayParam.totalPrice;
      this.rightKeyword = this.finalPayParam.rightKeyword;
      this.member_mobile = this.finalPayParam.member_mobile;
      this.member_id = this.finalPayParam.member_id;
      this.preDisc = this.finalPayParam.preDisc;
      this.accountId = this.finalPayParam.accountId;
      if (this.from === 'member_cancel' || this.from === 'detail_member') {
        this.cancel_vipData = this.finalPayParam.cancel_vipData;
        this.showFinalPrice = this.cancel_vipData.money;
        this.pc_return_goods = true;
        this.showMember = true;
        if (this.from === 'detail_member') {
          this.member_name = this.finalPayParam.member_name;
          this.left_goods_list = _.cloneDeep(this.finalPayParam.left_goods_list);
          for (var i = 0; i < this.left_goods_list.length; i++) {
            this.left_goods_list[i].id = this.left_goods_list[i].good_id;
            this.left_goods_list[i].name = this.left_goods_list[i].good_name;
            this.left_goods_list[i].salePrice = this.left_goods_list[i].price;
            this.left_goods_list[i].vipPrice = '0';
            this.left_goods_list[i].number = this.left_goods_list[i].qty;
          }
        }
      } else if (this.from === 'pay') {
        this.member_name = this.finalPayParam.member_name;
      } else {
        console.log(this.from, '其他from');
      }
      if (!this.temShowFinalPrice) {
        this.temShowFinalPrice = this.showFinalPrice;
      }
    },
    settlement() {
      this.input_member_password = '';
      if (this.settlement) {
        CefSharp.PostMessage('打开结算');
        setTimeout(() => {
          this.hotKeySet();
        });
        this.getOrderidTime();
      } else {
        this.payQueryStatus = false;
        this.closeInval();
        this.temShowFinalPrice = '';
        CefSharp.PostMessage('关闭结算');
        this.lock_screen = true;
        // 当lock_number == ''时，不执行lock_number ++
        this.lock_number = '';
        // this.startScannerHandler(false);
        if (this.from === 'vip_times_card') {
          this.$log.info('vip_times_card', this.log_info);
        } else {
          this.$log.info('pay', this.log_info);
        }
        this.cancelListenevent();
        this.hotKeyCancel();
      }
    },
    conbinaPayWayVal() {
      this.isCash = _.find(this.conbinaPayWayVal, o => {
        return o === 1;
      });
    },
    rightKeyword() {
      if (this.rightKeyword.toString().indexOf('.') !== -1 && this.rightKeyword.toString().length - this.rightKeyword.toString().indexOf('.') === 4) {
        this.rightKeyword = this.rightKeyword.toString().substring(0, this.rightKeyword.length - 1);
      }
      if (Number(this.rightKeyword) > 0 && Number(this.showFinalPrice) > 0) {
        this.kexianDataFormat();
      }
    },
    combinedAmount1() {
      this.rightKeyword = (Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2);
    },
    combinedAmount2() {
      this.rightKeyword = (Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2);
    },
    showMemberPassIpt() {
      if (this.showMemberPassIpt) {
        setTimeout(() => {
          document.getElementById('combinedPayInput').focus();
        }, 50);
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    beforeointSure() {
      if (demo.isNullOrTrimEmpty(this.temFinalIntegral)) {
        demo.msg('warning', '请输入积分');
        return;
      }
      if (Number(this.temFinalIntegralMoney) < 0.01) {
        demo.msg('warning', '抵扣金额应大于0.01');
        return;
      }
      this.realPoint = 0;
      const point = Number(this.point_setting.score_deduction.consume_score);
      const money = Number(this.point_setting.score_deduction.deduction_money);
      const midMoney = (Number(this.temFinalIntegral) / point) * money * 100;
      const finalMoney = Math.floor(midMoney.toFixed(2)) / 100;
      if (finalMoney > 0 && !Number.isInteger(midMoney)) {
        this.realPoint = Math.floor((Math.floor(midMoney.toFixed(2)) * point) / (money * 100));
        this.showPointTag = true;
      } else {
        this.sureEditPoint();
      }
    },
    // 获取支付宝验证信息
    creatZfbCode() {
      getZfbAuth()
        .then(res => {
          const data = _.cloneDeep(res.data);
          if (data === 1) {
            this.isShowQrCode = true;
          } else {
            this.isShowQrCode = false;
          }
        })
        .catch(() => {
          this.isShowQrCode = false;
        });
    },
    // 积分输入check
    checkInput() {
      this.temFinalIntegral = this.$stockLimit({ data: this.temFinalIntegral, max: this.oFinalIntefral, min: 1, decimals: 0 });
      this.countPoint();
    },
    // 积分抵扣计算
    countPoint() {
      const point = this.point_setting.score_deduction.consume_score;
      const money = this.point_setting.score_deduction.deduction_money;
      const midMoney = (Number(this.temFinalIntegral) / Number(point)) * Number(money) * 100;
      this.temFinalIntegralMoney = (Math.floor(midMoney.toFixed(2)) / 100).toFixed(2);
    },
    // 确认修改积分抵扣
    sureEditPoint() {
      this.final_integral_money = this.temFinalIntegralMoney;
      this.final_integral = this.realPoint > 0 ? this.realPoint : this.temFinalIntegral;
      if (this.use_integral) {
        this.showFinalPrice = (Number(this.temShowFinalPrice) - Number(this.final_integral_money)).toFixed(2);
        demo.screen2(
          {
            screen2ShowFinalPrice: this.showFinalPrice,
            screen2ReducePrice: (Number(this.settlementReducePrice) + Number(this.final_integral_money)).toFixed(2)
          },
          4
        );
      }
      this.editPoint = false;
      this.showPointTag = false;
    },
    // 积分抵扣弹出框
    openEditPoint() {
      if (this.barcode_pay_status !== 'new') {
        return;
      }
      this.temFinalIntegral = this.final_integral;
      this.temFinalIntegralMoney = this.final_integral_money;
      this.editPoint = true;
    },
    // 获取微信二维码
    async getKefuCacheCodeImg() {
      const kefuImgUrl = await getServiceImgUrl();
      if (!kefuImgUrl) {
        return;
      }
      this.clearQRCode();
      let qr_data = {
        text: kefuImgUrl,
        width: 122,
        height: 122,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
    },
    clearQRCode() {
      if (this.$refs.kefuSrcUrl) {
        this.$refs.kefuSrcUrl.innerHTML = '';
      }
    },
    // 获取微信二维码
    async getHelpQrCode() {
      if (pos.network.isConnected()) {
        const param = {
          type: 'service'
        };
        const { weComUrl } = await getWxQrCode(param);
        if (weComUrl) {
          let qr_data = {
            text: weComUrl,
            width: 122,
            height: 122,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
          };
          this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
          settingService.put({ key: 'serviceImgUrl', value: weComUrl }); // 缓存图片地址
        } else {
          this.getKefuCacheCodeImg();
        }
      } else {
        this.getKefuCacheCodeImg();
      }
    },
    // 鼠标切换支付方式
    handleClickChange(type) {
      if (this.barcode_pay_status === 'paying' || this.payQueryStatus) {
        return;
      }
      this.acctsId = type;
      // actionlog上报
      const kind = this.buy_back === 1 ? '收' : '退';
      const money = `应${kind}${this.showFinalPrice},实${kind}${this.rightKeyword || this.showFinalPrice}`;
      const list = {
        1: logList.cashPay,
        7: logList.posPay,
        6: logList.scanPay,
        8: logList.vipPay
      };
      const description = {
        1: `整单支付-点击切换现金支付,${money}`,
        7: `整单支付-点击切换线下支付,${money}`,
        6: `整单支付-点击切换扫码支付,${money}`,
        8: `整单支付-点击切换会员支付,${money}`
      };
      this.$_actionLog(list[type], description[type]);
    },
    // 扫码或者键盘监听事件
    handleKeyEvent({ type, code }) {
      // 如果打开了积分修改弹框，则禁止键盘的监听事件
      if (this.editPoint || this.barcode_pay_status === 'paying') {
        return;
      }
      if (type === 1 && code.length >= 16) {
        CefSharp.PostMessage(`$scanner code:${code}, lock_screen: ${this.lock_screen}, isSinglePayWay: ${this.isSinglePayWay}`);
        if (!this.lock_screen && this.isSinglePayWay && !this.pc_return_goods) {
          this.acctsId = 6;
          if (!pos.network.isConnected()) {
            return demo.msg('warning', '网络已断开，请联网后使用扫码付功能');
          }
          if (this.aid === '') {
            return demo.msg('warning', '未开通扫码付，请联系客服开通');
          }
          if (Number(this.showFinalPrice) === 0) {
            return demo.msg('warning', '使用扫码支付时应收金额应大于0元');
          }
          this.lock_screen = true;
          this.codeString = code;
          this.rightKeyword = this.showFinalPrice;
          this.lock_number = this.lock_max_number;
          this.clickButton('barcode');
          this.barcodeZfbWxPay();
          const money = `应收${this.showFinalPrice},实收${this.rightKeyword || this.showFinalPrice}`;
          this.$_actionLog(logList.scanPay, `整单支付-扫码自动切换扫码支付,${money}`);
        }
      } else if (type === 2) {
        if (code === 'Enter') {
          this.codeString = '';
          if (this.isSinglePayWay) {
            this.sonarKeydown1();
          } else {
            if (this.combinedFocus === 1) {
              // 输入状态中时先失焦
              this.blurCombinePayMoney(0);
            } else if (this.combinedFocus === 2) {
              this.blurCombinePayMoney(1);
            } else {
              // 组合支付结算
              this.combinedPayCheck();
            }
          }
        } else {
          // 如果键盘输入的时数字/小数点，且支付方式为现金支付时，输入实收金额
          if (!isNaN(code) || code === '.') {
            if (this.acctsId === 1) {
              if (code === '.' && !this.rightKeyword.toString().includes('.')) {
                this.inputCalculator(code);
              } else if (code !== '.' && !(code === '0' && this.rightKeyword === '0.0')) {
                // 输入的时数字映射到实收（如果当前实收以为0且输入的code也为0时不生效）
                this.inputCalculator(code);
              }
            }
          } else {
            // 键盘输入的是非数字的且允许范围内的快捷键
            if (!this.settlement || this.ifautoCash || this.buy_back === 2 || !this.button_paying) {
              return;
            }
            const kind = this.buy_back === 1 ? '收' : '退';
            const money = `应${kind}${this.showFinalPrice},实${kind}${this.rightKeyword || this.showFinalPrice}`;
            switch (code) {
              case 'F1': // 切换现金支付
                if (this.isSinglePayWay) {
                  this.acctsId = 1;
                  this.$_actionLog(logList.cashPay, `整单支付-键盘切换现金支付,${money}`);
                } else {
                  // 组合支付时增加支付方式
                  this.shortCutPayway(1);
                }
                break;
              case 'F2': // 切换线下支付
                this.acctsId = 7;
                this.$_actionLog(logList.posPay, `整单支付-键盘切换线下支付,${money}`);
                break;
              case 'F3': // 切换扫码支付
                this.acctsId = 6;
                this.$_actionLog(logList.scanPay, `整单支付-键盘切换扫码支付,${money}`);
                break;
              case 'F4': // 切换会员支付
                // if (this.isSinglePayWay) {
                //   this.acctsId = 8;
                //   this.$_actionLog(logList.vipPay, `整单支付-键盘切换会员支付,${money}`)
                // } else {
                //   // 组合支付时增加支付方式
                //   this.shortCutPayway(6);
                // }
                if (this.showMember) {
                  if (this.isSinglePayWay) {
                    this.acctsId = 8;
                    this.$_actionLog(logList.vipPay, `整单支付-键盘切换会员支付,${money}`);
                  } else {
                    // 组合支付时增加支付方式
                    this.shortCutPayway(6);
                  }
                }
                break;
              case 'F5': // 线下支付切换pos支付
                if (this.isSinglePayWay) {
                  if (this.acctsId === 7 && this.lock_screen === false) {
                    this.acctsId = 3;
                    this.rightKeyword = this.showFinalPrice;
                    this.clickButton('pos', true);
                    this.$_actionLog(logList.posPay, `整单支付-线下支付-已POS支付,${money}`);
                  }
                } else {
                  // 组合支付时增加支付方式
                  this.shortCutPayway(3);
                }
                break;
              case 'F6': // 线下支付切换微信支付
                if (this.isSinglePayWay) {
                  if (this.acctsId === 7 && this.lock_screen === false) {
                    this.acctsId = 4;
                    this.rightKeyword = this.showFinalPrice;
                    this.clickButton('wechat', true);
                    this.$_actionLog(logList.posPay, `整单支付-线下支付-已微信支付,${money}`);
                  }
                } else {
                  // 组合支付时增加支付方式
                  this.shortCutPayway(4);
                }
                break;
              case 'F7': // 线下支付切换支付宝支付
                if (this.isSinglePayWay) {
                  // 如果当前在线下支付切未锁屏状态直接结算
                  if (this.acctsId === 7 && this.lock_screen === false) {
                    this.acctsId = 5;
                    this.rightKeyword = this.showFinalPrice;
                    this.clickButton('alipay', true);
                    this.$_actionLog(logList.posPay, `整单支付-线下支付-已支付宝支付,${money}`);
                  }
                } else {
                  // 组合支付时增加支付方式
                  this.shortCutPayway(5);
                }
                break;
              case 'ArrowRight': // 线下支付右移
                this.directionhandle(code);
                break;
              case 'ArrowLeft': // 线下支付左移
                this.directionhandle(code);
                break;
              case 'Escape': // 关闭支付弹窗
                // esc 快捷键
                this.clickButton('cancel_pay_sale');
                break;
              case 'Backspace': // 实收金额删除
                if (this.isSinglePayWay) {
                  this.inputCalculator('back');
                  this.inputNum = '';
                }
                break;
              case 'Delete': // 清空实收金额
                this.inputCalculator('del');
                this.inputNum = '';
                break;
            }
          }
        }
      }
    },
    directionhandle(code) {
      if (this.acctsId === 7) {
        // 线下支付时可左右切换
        let t = _.cloneDeep(this.payActiveArr);
        let cur = t.indexOf(this.payActiveIndex);
        if (code === 'ArrowLeft') {
          // 左
          this.payActiveIndex = cur === 0 ? t.pop() : t[cur - 1];
        } else {
          // 右
          this.payActiveIndex = cur === t.length - 1 ? t.shift() : t[cur + 1];
        }
      }
    },
    kexianDataFormat() {
      let kexian1 = !this.isSinglePayWay && this.isCash === 1 && Number(this.rightKeyword) >= Number(this.showFinalPrice);
      let kexian2 = Number(this.rightKeyword) >= Number(this.showFinalPrice);
      let kexian3 = Number(this.rightKeyword) === Number(this.showFinalPrice);
      if (kexian1) {
        if (kexian3) {
          this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
        } else {
          this.getKexian((Number(this.combinedAmount1) + Number(this.combinedAmount2) - Number(this.showFinalPrice)).toFixed(2), 4);
        }
      }
      if (kexian2) {
        if (kexian3) {
          this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
        } else {
          this.getKexian((Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2), 4);
        }
      }
    },
    getKexian(v, t) {
      if (this.showKexian) {
        external.customerdisplay({
          displaydata: v,
          displaytype: t,
          port: this.kexianValue,
          baudrate: '2400',
          databits: '8',
          parity: '0',
          stopBits: '1'
        });
      }
    },
    continueBackPayVip() {
      var data = _.cloneDeep(this.cancel_vipData);
      if (this.show_continue_backPayVipPoint) {
        // 退款积分不足
        let backData = _.cloneDeep(this.show_continue_backPayVip_data);
        backData.checkPoint = 0;
        this.backPayVip(backData, 3);
      } else {
        var create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
        this.backStockFunction(data, create_time, 1);
      }
    },
    choosePay(m, n) {
      this.payIndex = m;
      this.acctsId = n;
    },
    creatQrCode() {
      var qr_data = {
        text:
          this.$rest.qrcode +
          '/?key=' +
          window.btoa(
            this.sys_uid.substring(0, 3) + '****' + this.sys_uid.substring(7, 11) + '&' + $config.subName + '&' + $config.systemName + '&4'
          ),
        width: 122,
        height: 122,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.qrcode = new QRCode(this.$refs.qrCodeUrl, qr_data);
      this.qrcodenew = new QRCode(this.$refs.qrCodeUrlNew, qr_data);
    },
    addListenevent() {
      setTimeout(() => {
        if (!this.settlement) {
          return;
        }
        console.log('添加keydown');
        // document.body.addEventListener('keydown', this.keydown, false);
        this.keyEvent = new Keyevent(this.handleKeyEvent);
        this.keyEvent.start();
        CefSharp.PostMessage('添加keydown');
      }, 100);
    },
    cancelListenevent() {
      console.log('移除keydown');
      // document.body.removeEventListener('keydown', this.keydown, false);
      if (this.keyEvent) {
        this.keyEvent.stop();
        this.keyEvent = null;
      }
      CefSharp.PostMessage('移除keydown');
    },
    hotKeySet() {
      let that = this;
      hotkeys('ctrl+tab', function (event, handler) {
        switch (handler.key) {
          case 'ctrl+tab': // 整单/组合支付切换
            if (that.barcode_pay_status === 'paying') {
              return;
            }
            if (that.settlement && that.button_paying && that.isContentCombinedPay) {
              that.controlTabShortCut();
            }
            event.preventDefault();
            break;
          default:
            console.log('');
        }
      });
    },
    controlTabShortCut() {
      if (this.button_paying) {
        this.payTypeChange(this.isSinglePayWay ? 1 : 0);
        if (this.isSinglePayWay) {
          demo.actionLog(logList.combinationPay);
        }
      }
    },
    hotKeyCancel() {
      hotkeys.unbind(this.hotkeysString);
    },
    sonarKeydown1() {
      var flag1 = !this.button_paying || this.inputNum.length > 0;
      if (flag1) {
        return;
      }
      var that = this;
      setTimeout(function () {
        CefSharp.PostMessage(
          `codeString: ${that.codeString}, buy_back: ${that.buy_back}, acctsId: ${that.acctsId}, button_paying: ${that.button_paying}`
        );
        var flag2 = that.codeString === '' && that.buy_back === 1;
        if (flag2) {
          if (that.acctsId === 1) {
            // 现金完成enter 快捷键
            that.clickButton('cash', true);
            // 现金完成enter 快捷键埋点
            that.$_actionLog(logList.cashPay, `整单支付-键盘快捷键cash结算`);
          }
          if (that.acctsId === 8 && that.button_paying && that.settlement) {
            // 会员完成enter 快捷键
            that.clickButton('member_card');
          }
          if (that.acctsId === 7 && that.button_paying) {
            // 线下
            that.acctsId = that.payActiveIndex;
            that.rightKeyword = that.showFinalPrice;
            const payText = that.acctsId === 3 ? 'pos' : that.acctsId === 4 ? 'wechat' : 'alipay';
            that.clickButton(payText, true);
            // 线下enter快捷结算埋点
            that.$_actionLog(logList.cashPay, `整单支付-键盘快捷键${payText}结算`);
          }
        }
      }, 200);
    },
    sonarKeydown2(_key) {
      if (this.acctsId !== 1) {
        return;
      }
      if (this.acctsId === 1) {
        var nextTime = new Date().getTime();
        this.inputNum += _key > 70 ? _key - 96 : _key - 48;
        var that = this;
        setTimeout(function () {
          if (that.inputNum.length === 1) {
            that.inputCalculator(that.inputNum);
            that.inputNum = '';
          }
        }, 50);
        this.lastTime = nextTime;
      }
    },
    keydown() {
      if (!this.settlement || this.ifautoCash || this.buy_back === 2 || !this.button_paying) {
        return;
      }
      if (this.isSinglePayWay) {
        this.singleKeydown();
      } else {
        this.unSingleKeydown();
      }
    },
    // 监听按键事件，用于扫码枪扫码
    singleKeydown() {
      let _key = window.event.keyCode;
      if (this.directionControlCheck(_key)) {
        // 方向键配合enter选择支付方式
        this.directionControl(_key);
        return;
      }
      // if ((_key === 13) || (_key === 108)) {
      //   this.sonarKeydown1();
      //   return;
      // }
      if (_key === 190 || _key === 110) {
        // if (this.rightKeyword.toString().indexOf('.') === -1) {
        //   this.inputCalculator('.');
        // }
      } else if ((_key - 96 > -1 && _key - 96 < 10) || (_key - 48 > -1 && _key - 48 < 10)) {
        // this.sonarKeydown2(_key);
      } else {
        this.sonarKeydown3(_key);
      }
    },
    unSingleEscKey() {
      if (this.showMemberPassIpt) {
        this.showMemberPassIpt = false;
      } else {
        this.closeSettlement('uninit');
      }
    },
    unSingleKeydown() {
      let _key = window.event.keyCode;
      if (_key === 27) {
        // ese
        this.unSingleEscKey();
        return;
      }
      if (_key === 112) {
        // F1 现金
        this.shortCutPayway(1);
        return;
      }
      if (_key === 115 && this.showMember) {
        // F4 会员
        this.shortCutPayway(6);
        return;
      }
      if (_key === 116) {
        // F5 pos
        this.shortCutPayway(3);
        return;
      }
      if (_key === 117) {
        // F6 wechat
        this.shortCutPayway(4);
        return;
      }
      if (_key === 118) {
        // F7 zfb
        this.shortCutPayway(5);
        return;
      }
      // if ((_key === 13) || (_key === 108)) { // enter
      //   if (this.combinedFocus === 1) { // 输入状态中时先失焦
      //     this.blurCombinePayMoney(0);
      //   } else if (this.combinedFocus === 2) {
      //     this.blurCombinePayMoney(1);
      //   } else { // 组合支付结算
      //     this.combinedPayCheck();
      //   }
      // }
      console.log('');
    },
    shortCutPayway(val) {
      let index = this.conbinaPayWayVal.indexOf(val);
      if (index !== -1) {
        this.conbinaPayWayVal.splice(index, 1);
      } else if (this.conbinaPayWayVal.length < 2) {
        this.conbinaPayWayVal.push(val);
      }
      this.handlePaywayChange(this.conbinaPayWayVal);
    },
    directionControlCheck(_key) {
      //     ↑
      // ← enter  →
      //     ↓
      // return _key === 37 || _key === 38 || _key === 39 || _key === 40;
      return _key === 37 || _key === 39;
    },
    directionControl(_key) {
      // 右侧支付方式上下切换
      // let tmp = _.cloneDeep(this.showMember ? this.hasVipAcctsIdArr : this.acctsIdArr);
      // let curIndex = tmp.indexOf(this.acctsId);
      // if (_key === 38) { // 上
      //   this.acctsId = curIndex === 0 ? tmp.pop() : tmp[curIndex - 1];
      //   this.payActiveIndex = 4;
      // } else if (_key === 40) { // 下
      //   this.acctsId = curIndex === tmp.length - 1 ? tmp.shift() : tmp[curIndex + 1];
      //   this.payActiveIndex = 4;
      // }
      if (this.acctsId === 7) {
        // 线下支付时可左右切换
        this.directionControlOffline(_key);
      }
    },
    directionControlOffline(_key) {
      let t = _.cloneDeep(this.payActiveArr);
      let cur = t.indexOf(this.payActiveIndex);
      if (_key === 37) {
        // 左
        this.payActiveIndex = cur === 0 ? t.pop() : t[cur - 1];
      } else if (_key === 39) {
        // 右
        this.payActiveIndex = cur === t.length - 1 ? t.shift() : t[cur + 1];
      }
    },
    sonarKeydown3(_key) {
      if (_key === 8 && this.acctsId === 1) {
        // 删除backspace 快捷键
        this.inputCalculator('back');
        this.inputNum = '';
      } else if (_key === 46 && this.acctsId === 1) {
        // 清空del 快捷键
        this.inputCalculator('del');
        this.inputNum = '';
      } else if (_key === 27) {
        // esc 快捷键
        this.clickButton('cancel_pay_sale');
      } else if (_key === 112) {
        // F1 快捷键
        this.acctsId = 1;
      } else if (_key === 113) {
        // F2 快捷键
        this.acctsId = 7;
      } else if (_key === 114) {
        // F3 快捷键
        this.acctsId = 6;
      } else if (_key === 115 && this.showMember) {
        // F4 快捷键
        this.acctsId = 8;
      } else {
        this.sonarKeydown4(_key);
      }
    },
    sonarKeydown4(_key) {
      if (_key === 116 && this.acctsId === 7 && this.lock_screen === false) {
        // F5 快捷键
        this.acctsId = 3;
        this.rightKeyword = this.showFinalPrice;
        this.clickButton('pos');
      } else if (_key === 117 && this.acctsId === 7 && this.lock_screen === false) {
        // F6 快捷键
        this.acctsId = 4;
        this.rightKeyword = this.showFinalPrice;
        this.clickButton('wechat');
      } else if (_key === 118 && this.acctsId === 7 && this.lock_screen === false) {
        // F7 快捷键
        this.acctsId = 5;
        this.rightKeyword = this.showFinalPrice;
        this.clickButton('alipay');
      } else {
        console.log('无对应按键');
      }
    },
    showAcctsId(n) {
      return this.acctsId === n ? this.$t('image.homeImage.showAccts') : this.$t('image.homeImage.showAccts1');
    },
    getOrderidSuccess() {
      this.barcode_count = 0;
      this.lock_screen = false;
      this.lock_check_zf_status = false;
      this.barcode_pay_status = 'new';
      this.member_recharge_fail = false;
      // this.startScannerHandler(true);

      this.SET_SHOW({ completePay: false });
      var isMemberRecharge = this.from === 'member_recharge';
      var isVipTimesCard = this.from === 'vip_times_card';
      var goods = [];
      if (isMemberRecharge || isVipTimesCard) {
        if (isMemberRecharge) {
          goods = [
            {
              name: '会员充值',
              salePrice: this.finalPayParam.member_data.money,
              number: '1',
              disc: 100,
              amt: this.finalPayParam.member_data.money
            }
          ];
        }
        if (isVipTimesCard) {
          var old_list = this.finalPayParam.left_goods_list;
          for (var i = 0; i < old_list.length; i++) {
            var m_list = {
              name: old_list[i].name,
              salePrice: old_list[i].price,
              number: '1',
              disc: 100,
              amt: old_list[i].price
            };
            goods.push(m_list);
          }
        }
        let subDate = {
          screen2MemberPayType: 1,
          screen2ShowList: goods,
          screen2ShowFinalPrice: this.finalPayParam.totalPrice,
          screen2ReducePrice: 0,
          screen2ShowMember: true,
          screen2MemberName: this.memberDetail.name,
          screen2MemberPhone: this.memberDetail.mobile,
          screen2MemberMoney: this.memberDetail.has_money,
          screen2MemberPoint: this.memberDetail.integral
        };
        demo.screen2(subDate, 2);
      }
      this.pay_name = '';
      this.no_inputPrice = true;
      this.button_paying = true;
      if (this.member_id !== '' && this.member_id !== undefined) {
        this.getMemberMoney();
      }
      this.getKexian(Number(this.showFinalPrice).toFixed(2), 2);
    },
    searchMember() {
      var that = this;
      this.lock_number = this.lock_max_number;
      var online = false;
      if (this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10) {
        online = true;
      }

      var member_data = {
        systemName: $config.systemName,
        subName: $config.subName,
        phone: this.finalPayParam.member_data.sysUid,
        sysSid: this.finalPayParam.member_data.sysSid,
        storeName: this.username,
        vipId: this.finalPayParam.member_data.vipId,
        originId: 2,
        type: 1, // 1-充值；2-消费。默认1
        acctId: online ? this.acctsId - 2 : this.acctsId, // 默认1-现金
        money: this.finalPayParam.member_data.money,
        giveMoney: this.finalPayParam.member_data.giveMoney,
        createUser: this.loginInfo.uid,
        outTradeNo: this.orderno,
        usePoint: false,
        point: 0,
        pointMoney: 0,
        sales: []
      };
      if (pos.network.isConnected()) {
        demo.$http
          .post(this.$rest.pc_searchMemberPay, member_data, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            }
          })
          .then(
            function (res) {
              that.searchFlag = false;
              // 补充
              if (res.data.return_code === '0000' && res.data.result_code === 'success') {
                // 成功
                that.memberRechargeSuccess(member_data);
                that.closeSettlement();
              } else {
                // 失败
                demo.msg('warning', '会员充值查询失败，请稍后再试');
                that.closeSettlement();
              }
            },
            function (error) {
              console.error(error);
              demo.msg('warning', '会员充值查询失败，请稍后再试');
              that.closeSettlement();
              that.searchFlag = false;
            }
          );
      } else {
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    clickButton(n, boolean) {
      CefSharp.PostMessage('结算按钮：' + n);
      /* 支付按钮通过n不同，调用不同方法
        n == 'cash' → 现金完成
        n == 'pos' → POS完成
        n == 'wechat' → 线下微信
        n == 'alipay' → 线下支付宝
        n == 'member_card' → 会员支付
        n == 'barcode' → 扫码支付
        n == 'back' → 退款完成
        n == 'changePermitPrint' → 是否打印小票
        n == 'checkZfStatus' → check支付状态
        n == 'back_paying' → 退款结算
      */
      const logPayType = ['cash', 'pos', 'wechat', 'alipay'];
      if (!boolean && logPayType.includes(n)) {
        this.$_actionLog(logList.cashPay, `整单支付-鼠标点击${n}结算`);
      }
      // 现金收银如果找零超过200且当前弹窗不在显示状态时显示提示弹窗
      if (+this.rightKeyword - this.showFinalPrice >= 200 && n === 'cash' && !this.changeErrorTips) {
        this.changeErrorTips = true;
        return;
      }
      var flag_n1 = this.getClickButtonFlg1(n);
      if (flag_n1) {
        demo.msg('warning', '单号异常，请重新结算');
        return;
      }
      var button_log = {
        pay_type: n,
        click_date: new Date()
      };
      if (n === 'barcode') {
        button_log.pay_type = this.isWechatAlipay(this.codeString) === 1 ? n + 'alipay' : n + 'wechat';
      }
      this.log_info.pay_type.push(button_log);
      var flag_n2 = this.cashOnlineFlg(n);
      if (flag_n2) {
        if (Number(this.rightKeyword) > 10000000) {
          demo.msg('warning', '单笔订单不能超过1000万！');
          return;
        }
        this.buyComplete();
      }
      var flag_n3 = n === 'changeUseIntegral';
      if (flag_n3 && this.barcode_pay_status === 'new') {
        this.changeUseIntegral();
      }
      var flag_n4 = n === 'back' || n === 'back_paying';
      if (flag_n4) {
        // this.backComplete();
        if (this.member_id !== '' && this.member_id !== undefined && this.from === 'pay') {
          this.recordMemberPoint();
        } else {
          this.backComplete(10);
        }
      }
      var flag_n5 = n === 'changePermitPrint';
      if (flag_n5) {
        this.changePermitPrint();
      }
      this.sonarClickButton(n);
    },
    getClickButtonFlg1(n) {
      console.log(this.orderno, n, this.from, '=== getClickButtonFlg1');
      return this.orderno === '' && n !== 'cancel_pay_sale' && this.from !== 'member_cancel';
    },
    cashOnlineFlg(n) {
      return n === 'cash' || n === 'pos' || n === 'wechat' || n === 'alipay';
    },
    sonarClickButton(n) {
      var flag_n6 = n === 'checkZfStatus';
      if (flag_n6) {
        this.checkZfStatus();
      }
      var flag_n8 = n === 'member_card';
      if (flag_n8) {
        this.memberPay();
      }
      var flag_n9 = n === 'cancel_barcode_paying' || n === 'cancel_barcode_fail';
      if (flag_n9) {
        this.cancelBarcode();
      }
      if (n === 'member_recharge_fail') {
        demo.msg('warning', '会员充值异常，请联系客服或与顾客协商解决');
        this.closeSettlement();
      }
      if (n === 'searchMember' && !this.searchFlag) {
        this.searchFlag = true;
        this.searchMember();
      }
      var flag_n10 = n === 'cancel_pay_back' || n === 'cancel_pay_sale' || n === 'cancel_pay_autocash';
      if (flag_n10 && this.barcode_pay_status !== 'paying') {
        this.closeSettlement(n);
      }
    },
    changeUseIntegral() {
      // 如果是扫码支付中，取消切换积分是否抵扣
      if (this.lock_screen === true) {
        return;
      }
      if (this.use_integral === true) {
        this.use_integral = false;
        this.showFinalPrice = (Number(this.final_integral_money) + Number(this.showFinalPrice)).toFixed(2);
        demo.screen2({ screen2ShowFinalPrice: this.showFinalPrice, screen2ReducePrice: Number(this.settlementReducePrice).toFixed(2) }, 3);
        this.$_actionLog(logList.deductionPoints, `取消选择积分抵现`);
      } else {
        this.use_integral = true;
        this.showFinalPrice = (Number(this.showFinalPrice) - Number(this.final_integral_money)).toFixed(2);
        demo.screen2(
          {
            screen2ShowFinalPrice: this.showFinalPrice,
            screen2ReducePrice: (Number(this.settlementReducePrice) + Number(this.final_integral_money)).toFixed(2)
          },
          4
        );
        this.$_actionLog(logList.deductionPoints, `勾选选择积分抵现`);
      }
    },
    checkZfStatus(timeOut, boolean) {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
        return;
      }
      if (this.lock_check_zf_status === true) {
        return;
      }
      // 用户主动发起查询将扫码付具体原因清空
      if (!boolean) {
        this.payFailMsg = '';
      }
      this.lock_check_zf_status = true;
      var head_code = this.codeString.toString().substring(0, 2);
      if (head_code === '28') {
        this.checkZfbWx('zfb', timeOut, boolean);
      } else {
        this.checkZfbWx('wx', timeOut, boolean);
      }
    },
    checkZfbWx(str, timeOut, boolean) {
      const pay_data = {
        outTradeNo: this.lastOutTradeNo + '_' + this.sys_uid + '_' + this.sysId,
        mchid: this.appSecret
      };
      this.payQueryStatus = true;
      if (!boolean) {
        this.lock_number = this.lock_max_number;
        this.barcode_pay_status = 'paying';
        this.payWaitingCalc();
      }
      CefSharp.PostMessage('再次查询' + str + '开始：' + JSON.stringify(pay_data));
      qcQuery(pay_data, timeOut)
        .then(
          res => {
            CefSharp.PostMessage(`${boolean ? '系统主动' : '用户主动'}查询成功回调：` + JSON.stringify(res));
            this.lock_check_zf_status = false;
            console.log(res, 'check_zfb_wx成功回调');
            if (res.trade_state === 'success') {
              if (this.isPaySuccess) {
                return;
              }
              this.isPaySuccess = true;
              // 获取扫码付回调信息(存储到本地sales表info1里，退货需要)
              const { mch_id, out_trade_no, total_fee } = res;
              this.scanPayInfo = {
                mchid: mch_id,
                outTradeNo: out_trade_no,
                totalFee: total_fee
              };
              if (str === 'zfb' || str === 'wx') {
                this.acctsId = str === 'zfb' ? 10 : 9;
                if (this.member_id !== '' && this.member_id !== undefined) {
                  this.recordMemberPoint();
                } else {
                  this.backComplete(str === 'zfb' ? 1 : 2);
                }
              } else {
                console.log(str);
              }
            } else if (res.trade_state === 'new') {
              this.payQueryStatus = false;
              if (!boolean || this.barcode_pay_status === 'fail') {
                this.barcode_pay_status = 'fail';
                this.closeInval();
              }
              if (!boolean) {
                demo.msg('warning', '订单支付中');
              }
            } else {
              this.payQueryStatus = false;
              if (!boolean || this.barcode_pay_status === 'fail') {
                this.barcode_pay_status = 'fail';
                this.closeInval();
              }
              if (!boolean) {
                demo.msg('warning', '订单支付失败');
              }
            }
          },
          error => {
            this.payQueryStatus = false;
            CefSharp.PostMessage(`${boolean ? '系统主动' : '用户主动'}查询失败回调：` + JSON.stringify(error));
            console.error(error);
            if (!boolean || this.barcode_pay_status === 'fail') {
              demo.msg('warning', '查询支付状态失败，请稍后再试');
              this.barcode_pay_status = 'fail';
              this.closeInval();
            }
            if (!boolean) {
              demo.msg('warning', '查询支付状态失败，请稍后再试');
            }
          }
        )
        .catch(error => {
          this.payQueryStatus = false;
          CefSharp.PostMessage(`${boolean ? '系统主动' : '用户主动'}查询失败回调：` + JSON.stringify(error));
          console.error(error);
          if (!boolean || this.barcode_pay_status === 'fail') {
            demo.msg('warning', '查询支付状态失败，请稍后再试');
            this.barcode_pay_status = 'fail';
            this.closeInval();
          }
        });
    },
    cancelBarcode() {
      this.lock_number = '';
      this.lock_screen = false;
      this.barcode_pay_status = 'new';
    },
    getOrderidTime() {
      console.log('初始化订单开始');
      this.only_time = new Date().getTime();
      // 页面初始化，获取订单号
      var data = {
        type: this.from === 'member_recharge' || this.from === 'vip_times_card' || this.from === 'member_cancel' ? 'VIP' : 'XSD'
      };
      orderService.get(
        data,
        res => {
          this.setOrderid(res, '成功');
        },
        err => {
          this.setOrderid(err, '失败');
        }
      );
    },
    setOrderid(res, flag) {
      this.orderno = demo.t2json(res)[0].code;
      console.log(this.orderno, flag + '新单号');
      this.log_info.orderno = this.orderno;
      this.getOrderidSuccess();
      this.getOrderidOtherInit();
    },
    getOrderidOtherInit() {
      this.initCombinePay();
      if (this.from === 'detail_member' && this.accountId === 6) {
        this.payIndex = 4;
        this.acctsId = 6;
        return;
      } else if (
        this.from === 'member_cancel' ||
        (this.from === 'detail_member' && this.accountId !== 6) ||
        (this.from === 'pay' && this.pc_return_goods)
      ) {
        this.payIndex = 0;
        this.acctsId = 1;
      } else {
        this.payIndex = 0;
      }
      this.log_info = {
        from: this.from,
        pay_type: [],
        orderno: '',
        barcode_data: [],
        barcode_payinfo: [],
        member_data_pay_info: [],
        member_data: [],
        detail_data: []
      };
      this.inputNum = '';
      this.showMemberIntegral = false;
      this.use_integral = false;
      this.member_integral = 0;
      this.showMemberComplete = false;
      this.payActiveIndex = 4;
      console.log(this.rightKeyword, 555);
      this.checkQuickPay();
      if (!this.keyEvent) {
        this.addListenevent();
      }
    },
    checkQuickPay() {
      if (this.quickPayType) {
        let qpt = this.quickPayType;
        this.SET_SHOW({ quickPayType: '' });
        switch (qpt) {
          case 'cash':
            this.acctsId = 1;
            this.clickButton('cash');
            break;
          case 'returnCash':
            this.acctsId = 1;
            this.returnMoneySubmit('confirm');
            break;
          case 'pos':
            this.acctsId = 3;
            this.rightKeyword = this.showFinalPrice;
            this.clickButton('pos');
            break;
          case 'returnPos':
            this.acctsId = 3;
            this.returnMoneySubmit('confirm');
            break;
          case 'wechat':
            this.acctsId = 4;
            this.rightKeyword = this.showFinalPrice;
            this.clickButton('wechat');
            break;
          case 'returnWechat':
            this.acctsId = 4;
            this.returnMoneySubmit('confirm');
            break;
          case 'alipay':
            this.acctsId = 5;
            this.rightKeyword = this.showFinalPrice;
            this.clickButton('alipay');
            break;
          case 'returnAlipay':
            this.acctsId = 5;
            this.returnMoneySubmit('confirm');
            break;
          case 'combinePay':
            this.rightKeyword = this.showFinalPrice;
            this.payTypeChange(1);
            break;
          default:
            console.log('');
            break;
        }
      }
    },
    /**
     * 支付完成后是否打印小票check回调
     */
    changePermitPrint() {
      var data = [{ key: settingService.key.print_small_ticket_afterpay, value: !this.permit_print }];
      settingService.put(data, res => {
        this.SET_SHOW({ permit_print: !this.permit_print });
      });
    },
    sonarDataForamt(res) {
      if (res.data.data) {
        if (
          demo.t2json(res.data.data.pointSetting).score_deduction.enable &&
          demo.t2json(res.data.data.pointSetting).score_deduction.deduction_flg === 'exchange'
        ) {
          this.showMemberIntegral = true;
        }
        this.point_setting = res.data.data.pointSetting ? demo.t2json(res.data.data.pointSetting) : this.point_setting;
        var point = this.point_setting.score_deduction.consume_score;
        var money = this.point_setting.score_deduction.deduction_money;
        if (Math.ceil((Number(this.showFinalPrice) / Number(money)) * Number(point)) > this.member_integral) {
          this.final_integral_money = (Math.floor(((Number(this.member_integral) / Number(point)) * Number(money) * 100).toFixed(2)) / 100).toFixed(
            2
          );
          this.final_integral = Number(((this.final_integral_money / Number(money)) * Number(point)).toFixed(0));
        } else {
          this.final_integral = Math.ceil(((Number(this.showFinalPrice) / Number(money)) * Number(point)).toFixed(5));
          this.final_integral_money = this.showFinalPrice;
        }
        this.oFinalIntefral = _.cloneDeep(this.final_integral);
      }
    },
    getIntegralRatio() {
      demo.$http
        .post(
          this.$rest.pc_vipGetSetting,
          {
            phone: this.sys_uid,
            sysSid: this.sys_sid,
            systemName: $config.systemName
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then(res => {
          this.showMemberIntegral = false;
          if (res.data.code === '0') {
            this.sonarDataForamt(res);
          } else {
            if (res.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
              demo.msg('warning', res.data.msg);
            }
          }
        });
    },
    getMemberMoney() {
      var that = this;
      var vipdata = {
        systemName: $config.systemName,
        // 'sysUid':that.sys_uid,
        phone: that.sys_uid
        // 'sysSid':that.sys_sid,
      };
      vipdata.id = that.member_id;
      if (pos.network.isConnected()) {
        demo.$http
          .post(that.$rest.pc_getVipById, vipdata, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
          .then(function (rs) {
            that.showMemberComplete = true;
            that.member_money = rs.data.data.hasMoney;
            that.member_password = rs.data.data.password;
            //  that.member_id = rs.data.data.id
            that.member_name = rs.data.data.name;
            that.pay_type = rs.data.data.payType;
            that.member_mobile = rs.data.data.mobile;
            that.member_integral = rs.data.data.integral;
            that.show_mobile = that.member_mobile.toString().substring(0, 3) + '****' + that.member_mobile.toString().substring(7, 11);
            that.getIntegralRatio();
            if (that.from === 'vip_times_card') {
              demo.screen2(
                {
                  screen2ShowMember: true,
                  screen2MemberMoney: that.member_money
                },
                5
              );
            }
          });
      } else {
        that.member_money = '';
        that.member_password = '';
        that.member_name = '';
        that.pay_type = '';
        that.member_mobile = '';
        that.show_mobile = '';
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    getMemberIntegral(create_time, memberNewData, showMeberDeep, flg, data) {
      var that = this;
      var vipdata = {
        systemName: $config.systemName,
        phone: that.sys_uid
      };
      if (this.finalPayParam && this.from !== 'vip_times_card') {
        vipdata.id = flg === 1 ? that.finalPayParam.member_data.vipId : that.finalPayParam.member_id;
      } else if (this.from === 'vip_times_card') {
        vipdata.id = that.finalPayParam.member_id;
      } else {
        vipdata.id = that.member_id;
      }
      if (pos.network.isConnected()) {
        demo.$http
          .post(that.$rest.pc_getVipById, vipdata, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
          .then(function (rs) {
            memberNewData.member_name = '会员名';
            memberNewData.member_value = rs.data.data.name;
            memberNewData.member_money_name = '余额';
            memberNewData.member_money_value = Number(rs.data.data.hasMoney).toFixed(2);
            memberNewData.member_mobile_name = '会员手机号';
            memberNewData.member_mobile_value = rs.data.data.mobile;
            memberNewData.member_point_name = '积分';
            memberNewData.member_point_value = Number(rs.data.data.integral);
            if (that.from === 'vip_times_card') {
              that.printCardData.member_name = '会员名';
              that.printCardData.member_value = rs.data.data.name;
              that.printCardData.member_money_name = '余额';
              that.printCardData.member_money_value = Number(rs.data.data.hasMoney).toFixed(2);
              that.printCardData.member_mobile_name = '会员手机号';
              that.printCardData.member_mobile_value =
                rs.data.data.mobile.toString().substring(0, 3) + '****' + rs.data.data.mobile.toString().substring(7, 11);
              that.printCardData.member_point_name = '积分';
              that.printCardData.member_point_value = Number(rs.data.data.integral);
            }
            flg === 1 ? that.printData(create_time, memberNewData, showMeberDeep) : that.saveLastOrderDetail(create_time, memberNewData, data);
          })
          .catch(function () {
            that.closeAndInit();
          });
      } else {
        this.closeAndInit();
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    startScannerHandler(isstart) {
      console.log(this.aid, 'this.aid');
      if (isstart) {
        this.scannerHandler();
      } else {
        this._scanner.cancel();
      }
      CefSharp.PostMessage(`startScannerHandler(${isstart})`);
      this.isStart = isstart;
    },
    scannerHandler() {
      var that = this;
      this._scanner = this.$scanner({
        delay: 2000,
        callback: v => {
          CefSharp.PostMessage(`$scanner v:${v}, lock_screen: ${that.lock_screen}, isSinglePayWay: ${that.isSinglePayWay}`);
          if (v.length === 18 && !that.lock_screen && that.isSinglePayWay && !that.pc_return_goods && that.acctsId === 6) {
            that.acctsId = 6;
            if (!pos.network.isConnected()) {
              demo.msg('warning', '网络已断开，请联网后使用扫码付功能');
              return;
            }
            if (this.aid === '') {
              demo.msg('warning', '未开通扫码付，请联系客服开通');
              return;
            }
            if (Number(this.showFinalPrice) === 0) {
              demo.msg('warning', '使用扫码支付时应收金额应大于0元');
              return;
            }
            that.lock_screen = true;
            that.codeString = v;
            that.rightKeyword = that.showFinalPrice;
            // 支付等待、计数器++
            that.lock_number = this.lock_max_number;
            that.clickButton('barcode');
            that.barcodeZfbWxPay();
          }
        }
      });
    },
    getSmfOpenStatus() {
      if (pos.network.isConnected()) {
        demo.$http
          .post(this.$rest.getaid, { sysUid: this.sys_uid, aid: 'pay' })
          .then(rs => {
            var res = rs.data;
            if (res.code === '0' && res.data !== null) {
              var data = res.data;
              if (data) {
                this.SET_SHOW({ aid: data.aid, app_secret: data.appSecret, sysId: data.sysId });
                this.smfOpenStatus = '已开通';
                this.creatZfbCode();
                return;
              }
            }
            this.SET_SHOW({ aid: '', app_secret: '', sysId: '' });
            this.smfOpenStatus = '未开通';
          })
          .catch(() => {
            this.SET_SHOW({ aid: '', app_secret: '', sysId: '' });
            demo.msg('warning', this.$msg.get_aid_fail);
            this.smfOpenStatus = '未开通';
          });
      }
    },
    sonarBarcode(payType) {
      this.acctsId = payType == 1 ? 10 : 9;
      if (this.member_id !== '' && this.member_id !== undefined) {
        this.recordMemberPoint();
      } else {
        this.backComplete(10);
      }
      console.log('barcode付款成功');
    },
    barcodeZfbWxPay() {
      // 开始计数
      this.payWaitingCalc();
      var payType = this.isWechatAlipay(this.codeString); // 1:支付宝、 2:微信
      this.isPaySuccess = false;
      this.barcode_count++;
      let timestamp = new Date().getTime();
      this.lastOutTradeNo = this.orderno.replace('XSD', '').replace('.', '_') + '_' + payType + '_' + this.barcode_count + '_' + timestamp;
      var pay_data = {
        subject: '订单名称',
        body: '商品描述',
        totalFee: (Number(this.showFinalPrice) * 100).toFixed(0).toString(),
        outTradeNo: this.lastOutTradeNo,
        spbillCreateIp: this.set_ip,
        payType: payType + '',
        mchid: this.appSecret,
        authCode: this.codeString
      };
      this.log_info.barcode_data.push(pay_data);
      if (pos.network.isConnected()) {
        this.barcode_pay_status = 'paying';
        this.createQueryTimer();
        CefSharp.PostMessage('扫码付开始：' + JSON.stringify(pay_data));
        qcTradePay(pay_data)
          .then(
            res => {
              if (this.queryTimer) {
                clearTimeout(this.queryTimer);
              }
              CefSharp.PostMessage('扫码付成功回调：' + JSON.stringify(res));
              console.log(JSON.stringify(res), '线上支付成功回调');
              this.log_info.barcode_payinfo.push(res);
              // 店主取消结算
              if (this.barcode_pay_status === 'new' || this.barcode_pay_status === 'fail') {
                return;
              }
              if (
                this.orderno.replace('XSD', '').replace('.', '_') + '_' + payType + '_' + this.barcode_count + '_' + timestamp !==
                pay_data.outTradeNo
              ) {
                return;
              }
              // 支付成功
              this.sonarCodePayComplited(res, payType);
            },
            error => {
              if (!this.payQueryStatus) {
                this.barcode_pay_status = 'fail';
                this.closeInval();
              }
              CefSharp.PostMessage('扫码付失败回调：' + JSON.stringify(error));
              if (this.queryTimer) {
                clearTimeout(this.queryTimer);
              }
            }
          )
          .catch(err => {
            if (!this.payQueryStatus) {
              this.barcode_pay_status = 'fail';
              this.closeInval();
            }
            CefSharp.PostMessage('扫码付失败回调：' + JSON.stringify(err));
            if (this.queryTimer) {
              clearTimeout(this.queryTimer);
            }
          });
      } else {
        this.lock_screen = false;
        demo.msg('warning', '网络已断开，请恢复网络后重试或选择其他支付方式');
        if (!this.payQueryStatus) {
          this.closeInval();
        }
        if (this.voiceOff) {
          this.tryPlayVoice('支付失败，请重新支付', this.soundVolume * 4);
        }
        CefSharp.PostMessage('扫码付时网络断开');
      }
    },
    // 扫码接口被动结果没有获取到10s倒计时，开始并行主动查询
    createQueryTimer() {
      if (this.queryTimer) {
        clearTimeout(this.queryTimer);
      }
      this.queryTimer = setTimeout(() => {
        this.checkZfStatus(5000, true);
      }, 10000);
    },
    closeInval() {
      CefSharp.PostMessage(`closeInval: ${this.number_interval}`);
      clearInterval(this.number_interval);
      this.number_interval = null
      this.lock_number = '';
      if (this.payFailMsg) {
        demo.msg('error', this.payFailMsg);
        if (this.voiceOff) {
          this.tryPlayVoice(this.payFailMsg, this.soundVolume * 4);
        }
        this.payFailMsg = '';
      }
    },
    tryPlayVoice(str, volume) {
      try {
        external.playVoice(str, volume);
      } catch (e) {
        external.playVoice(str);
      }
    },
    sonarCodePayComplited(rd, payType) {
      if (rd.result_code === 'success') {
        this.payFailMsg = '';
        if (this.isPaySuccess) {
          return;
        }
        this.isPaySuccess = true;
        // 获取扫码付回调信息(存储到本地sales表info1里，退货需要)
        const { mch_id, out_trade_no, total_fee } = rd;
        this.scanPayInfo = {
          mchid: mch_id,
          outTradeNo: out_trade_no,
          totalFee: total_fee
        };
        this.sonarBarcode(payType);
      } else {
        this.barcode_pay_status = 'fail';
        this.lock_screen = false;
        this.payFailMsg = rd.err_code_des || '支付超时，请确认';
      }
      if (!this.payQueryStatus) {
        this.closeInval();
      }
    },
    isWechatAlipay(codeString) {
      var payType;
      var head_code = codeString.toString().substring(0, 2);
      if (head_code === '28') {
        payType = 1;
      } else {
        payType = 2;
      }
      return payType;
    },

    payWaitingCalc() {
      if (this.number_interval) {
        clearInterval(this.number_interval);
      }
      this.number_interval = setInterval(() => {
        if (this.lock_number === '') {
          return;
        } else {
          this.lock_number--;
        }

        if (this.lock_number < 0) {
          this.barcode_pay_status = 'fail';
          this.member_recharge_fail = true;
          this.lock_screen = false;
          this.lock_number = '';
          this.closeInval();
          demo.msg('error', '支付超时，请确认');
          if (this.voiceOff) {
            this.tryPlayVoice('支付超时，请确认', this.soundVolume * 4);
          }
        }
      }, 1000);
    },
    sonarPrintOrder1(type) {
      var acctsName = '';
      if (type === 1) {
        acctsName = '现金';
      } else if (type === 2) {
        acctsName = '银行存款';
      } else if (type === 3) {
        acctsName = 'POS收银';
      } else if (type === 4) {
        acctsName = '微信';
      } else if (type === 5) {
        acctsName = '支付宝';
      } else if (type === 8 || type === 6) {
        acctsName = '会员支付';
      } else if (type === 9) {
        acctsName = '微信(扫码付)';
      } else if (type === 10) {
        acctsName = '支付宝(扫码付)';
      } else {
        console.log('其他');
      }
      return acctsName;
    },
    sonarPrintOrder2(final_list) {
      for (var i = 0; i < final_list.length; i++) {
        if (!final_list[i].isMemberDayGoods) {
          final_list[i].salePrice = final_list[i].vipPrice == 0 || final_list[i].vipPrice == '' ? final_list[i].salePrice : final_list[i].vipPrice;
        }
      }
      return final_list;
    },
    sonarPrintOrder3(final_list) {
      for (var k = 0; k < final_list.length; k++) {
        var num = '';
        var flag_print4 = this.weighUnitList.indexOf(final_list[k].unitName) !== -1;
        if (flag_print4) {
          num = Number(final_list[k].number).toFixed(3).toString();
        } else {
          num = Number(final_list[k].number).toFixed(2).toString();
        }
        final_list[k].number = num;
      }
      return final_list;
    },
    printOrder(create_time) {
      let memberNewData = {
        member_name: '',
        member_value: '',
        member_money_name: '',
        member_money_value: '',
        member_mobile_name: '',
        member_mobile_value: '',
        member_point_name: '',
        member_point_value: 0
      };
      let showMemberDeep = _.cloneDeep(this.showMember);
      if (this.showMember || this.from === 'member_recharge' || this.from === 'vip_times_card') {
        this.getMemberIntegral(create_time, memberNewData, showMemberDeep, 1);
      } else {
        this.printData(create_time, memberNewData, showMemberDeep);
      }
    },
    sonarCheck() {
      let flag_print1 = this.showFinalPrice == 0 && this.left_goods_list.length === 0;
      if (flag_print1) {
        return false;
      }
      return true;
    },
    sonarCheck2() {
      let flag_print2 = this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10;
      if (flag_print2) {
        return true;
      }
      return false;
    },
    printData(create_time, memberNewData, showMemberDeep) {
      this.closeAndInit();
      var acctsName = '';
      let payWay1 = '';
      let payWay2 = '';
      if (this.isSinglePayWay) {
        acctsName = this.sonarPrintOrder1(this.acctsId);
      } else {
        payWay1 = this.sonarPrintOrder1(this.conbinaPayWayVal[0]);
        payWay2 = this.sonarPrintOrder1(this.conbinaPayWayVal[1]);
        acctsName = '组合支付';
      }
      try {
        if (this.from !== 'vip_times_card') {
          if (!this.sonarCheck()) {
            return;
          }
          this.notTimesCardPrint(create_time, memberNewData, showMemberDeep, payWay1, payWay2, acctsName);
        } else {
          this.printCardData['accts'] = acctsName;
          this.ckCard();
        }
      } catch (e) {
        console.log(e, 'error eee');
        setTimeout(function () {
          demo.msg('warning', '打印设备异常，请检查小票打印机！');
        }, 2000);
      }
    },
    notTimesCardPrint(create_time, memberNewData, showMemberDeep, payWay1, payWay2, acctsName) {
      console.log('开始打印');
      var online = this.sonarCheck2();
      if (this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10) {
        online = true;
      }
      var final_list = _.cloneDeep(this.left_goods_list);
      if (showMemberDeep && this.pay_type === 2) {
        final_list = this.sonarPrintOrder2(final_list);
      }
      final_list = this.sonarPrintOrder3(final_list);
      var print_data = {
        conbinaPayWayVal: this.conbinaPayWayVal,
        combinedFinalPay: this.combinedFinalPay,
        payWay1: payWay1,
        payWay2: payWay2,
        printername: this.setting_small_printer,
        storename: this.username,
        accts: acctsName,
        pay_amt: online ? Number(this.showFinalPrice).toFixed(2) : Number(this.rightKeyword).toFixed(2),
        change_amt: online ? '0.00' : (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2),
        createAt: create_time,
        remark: 'Tel:' + this.phone,
        member_name: memberNewData.member_name,
        member_value: memberNewData.member_value,
        member_money_name: memberNewData.member_money_name,
        member_money_value: memberNewData.member_money_value,
        member_mobile_name: memberNewData.member_mobile_name,
        final_integral_money: this.use_integral === false ? '0.00' : this.final_integral_money,
        member_mobile_value:
          memberNewData.member_mobile_value.toString().substring(0, 3) + '****' + memberNewData.member_mobile_value.toString().substring(7, 11),
        member_point_name: memberNewData.member_point_name,
        member_point_value: memberNewData.member_point_value,
        goods: final_list
      };
      print_data.operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
      print_data.printNum = demo.$store.state.show.smallPrinterCopies;

      console.log('打印数据', print_data);
      pos.printer.printPOSTimes(print_data, this);
    },
    sonarRecode1(res) {
      this.log_info.member_data_pay_info.push(res);
      if (res.data === undefined || res.data.code !== '0') {
        if (this.from === 'member_recharge') {
          this.member_recharge_fail = true;
          demo.msg('warning', '会员信息插入失败，请核对实际支付情况');
        } else if (this.from === 'vip_times_card') {
          demo.msg('warning', res.data.msg);
          this.SET_SHOW({ settlement: false });
        } else {
          this.SET_SHOW({ settlement: false });
          demo.msg('warning', '会员信息插入失败，请核对实际支付情况');
        }
      } else {
        if (this.from === 'vip_times_card') {
          demo.msg('success', '购买成功');
          this.SET_SHOW({ showTimesCard: false });
          var s_data = {
            screen2ReceiveMoney: 0
          };
          s_data.screen2ShowMember = false;
          s_data.screen2MemberMoney = 0;
          this.printCardData = this.finalPayParam;
          this.printCardData.createtime = this.finalPayParam.vip_times_card.localOperateTime;
          this.openCashbox(this.printCardData.createtime);
        } else if (this.acctsId === 6) {
          demo.msg('success', '会员卡扣款成功');
          // if (!this.isSinglePayWay) { // 组合支付情况，会员卡支付部分完成后进行其他
          //   this.combinedMemberPaySuccess();
          // } else {
          //   this.backComplete(20);
          // }
          this.backComplete(20);
        } else {
          this.backComplete(20);
        }
      }
    },
    sonarRecode2(error) {
      if (this.from === 'member_recharge') {
        this.member_recharge_fail = true;
        demo.msg('warning', '会员信息插入失败，请核对实际支付情况');
      } else if (this.from === 'vip_times_card') {
        demo.msg('warning', '会员信息插入失败');
      } else {
        this.SET_SHOW({ settlement: false });
        demo.msg('warning', this.buy_back === 1 ? '会员信息插入失败，请核对实际支付情况' : '会员信息插入失败');
      }
      console.error(error);
    },
    recordMemberPoint() {
      var online = this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10;
      this.button_paying = false;
      console.log(new Date().getTime());
      console.log('改变支付状态button_paying');
      var member_data = {};
      let acctIdTmp = online ? this.acctsId - 2 : this.acctsId;
      let scoreRuleTmp = {
        rulePoint: this.point_setting === undefined ? 0 : this.point_setting.score_deduction.consume_score,
        ruleMoney: this.point_setting === undefined ? 0 : this.point_setting.score_deduction.deduction_money
      };
      if (this.from === 'vip_times_card') {
        member_data = this.finalPayParam.vip_times_card;
        member_data.acctId = acctIdTmp;
        member_data.money = Number(this.showFinalPrice) * -1;
        member_data.outTradeNo = this.orderno;
      } else {
        member_data = {
          systemName: $config.systemName,
          subName: $config.subName,
          phone: this.sys_uid,
          sysSid: this.sys_sid,
          vipId: this.member_id,
          type: this.pc_return_goods ? 3 : 2, // 1-充值；2-消费。默认1
          originId: this.pc_return_goods ? 4 : 2,
          acctId: acctIdTmp, // 默认1-现金
          money: Number(this.showFinalPrice) * (this.pc_return_goods ? 1 : -1),
          createUser: this.loginInfo.uid,
          outTradeNo: this.orderno,
          storeName: this.username,
          usePoint: this.use_integral,
          scoreRule: scoreRuleTmp,
          point: Number(this.final_integral),
          pointMoney: Number(this.final_integral_money),
          sales: this.subParams(2)
        };
      }
      member_data.localOperateTime = new Date().format('yyyy-MM-dd hh:mm:ss');
      member_data.info = {
        receivedMoney: this.rightKeyword,
        changeMoney: (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2)
      };
      this.log_info.member_data.push(member_data);
      this.conbinaPayData(member_data);
      console.log(member_data, 'member_data=====');
      if (pos.network.isConnected()) {
        var that = this;
        CefSharp.PostMessage(
          '会员支付开始 → ID：' +
            member_data.vipId +
            ';时间：' +
            member_data.localOperateTime +
            ';支付方式：' +
            member_data.acctId +
            ';单号：' +
            member_data.outTradeNo
        );
        demo.$http
          .post(that.$rest.pc_addvipDetails, member_data, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
          .then(
            function (res) {
              console.log(res, '会员支付 then > res:');
              // 服务器连接超时或网络抖动导致返回异常
              if (res.data && (res.data.code === '10001' || res.data.code === '10002')) {
                // 根据会员id和订单号再次查询
                CefSharp.PostMessage('会员支付时网络波动或服务器连接超时，再次查询：' + JSON.stringify(res));
                that.queryRecordMemberPoint(member_data);
                return;
              }
              CefSharp.PostMessage('会员支付成功：' + JSON.stringify(res));
              that.sonarRecode1(res);
            },
            function (error) {
              console.log(error, '会员支付 then > error:');
              CefSharp.PostMessage('会员支付失败：' + JSON.stringify(error));
              that.sonarRecode2(error);
            }
          )
          .catch(err => {
            console.log(err, '会员支付失败 catch > err:');
            // that.queryRecordMemberPoint(member_data);
          });
      } else {
        this.button_paying = true;
        this.SET_SHOW({ settlement: false });
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
        CefSharp.PostMessage('会员支付时网络处于离线状态');
      }
    },
    queryRecordMemberPoint(member_data, queryTimes) {
      let times = queryTimes || 0;
      if (times > 2) {
        // 超过三次不再查询
        // 查询会员支付记录失败，跳到强提示页面
        this.queryRecordMemberPointFail();
        return;
      }
      let params = {
        vipId: member_data.vipId,
        outTradeNo: member_data.outTradeNo
      };
      setTimeout(() => {
        if (pos.network.isConnected()) {
          demo.$http
            .post(this.$rest.getVipTradeStatus, params)
            .then(res => {
              console.log(res, 'queryRecordMemberPoint res:');
              let resData = res.data;
              // 查询到支付成功的记录
              if (resData && resData.data && resData.data.length) {
                this.sonarRecode1(res);
              } else {
                // 未查询到记录
                this.queryRecordMemberPointFail();
              }
            })
            .catch(() => {
              this.queryRecordMemberPointFail();
            });
        } else {
          // 终端未恢复网络
          this.queryRecordMemberPoint(member_data, times++);
        }
      }, 1000);
    },
    queryRecordMemberPointFail() {
      console.log('queryRecordMemberPointFail');
      this.showVipDetailTimeout = true;
    },
    conbinaPayData(member_data) {
      if (this.acctsId === 99) {
        let changeAmt = (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2);
        member_data.accounts = [
          {
            acctId: this.conbinaPayWayVal[0],
            money: this.conbinaPayWayVal[0] === 1 ? (this.combinedFinalPay[0] - changeAmt) * -1 : this.combinedFinalPay[0] * -1
          },
          {
            acctId: this.conbinaPayWayVal[1],
            money: this.conbinaPayWayVal[1] === 1 ? (this.combinedFinalPay[1] - changeAmt) * -1 : this.combinedFinalPay[1] * -1
          }
        ];
      }
    },
    sonarSubParams() {
      var sub_data = [];
      for (var i = 0; i < this.left_goods_list.length; i++) {
        var json = _.cloneDeep(this.left_goods_list[i]);
        json.goodId = json.id;
        json.itemDisc = Number(json.disc / 100)
          .toFixed(2)
          .toString();
        json.item_disc = Number(json.disc / 100)
          .toFixed(2)
          .toString();
        json.purPrice = json.purPrice === undefined ? '0' : json.purPrice;
        json.price = json.finalPrice === '' ? '0' : json.finalPrice;
        json.disc = '1';
        json.qty = this.buy_back === 1 ? json.number : Number(json.number) * -1;
        // json.amt =
        //   this.buy_back === 1
        //     ? Number(json.finalPrice) * Number(json.number) * Number(json.itemDisc)
        //     : -1 * Math.abs(Number(json.finalPrice) * Number(json.number) * Number(json.itemDisc));
        // json.remark = '';
        json.amt = this.buy_back === 1 ? Number(json.amt) : -1 * Math.abs(Number(json.amt));
        json.remark = '';
        sub_data.push(json);
      }
      console.log(this.buy_back, this.pc_return_goods, sub_data, 999111);
      return sub_data;
    },
    subParams() {
      var create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
      var sub_data = this.sonarSubParams();
      if (this.buy_back === 2) {
        this.showFinalPrice = Math.abs(Number(this.showFinalPrice)) * -1;
        this.totalPrice = Math.abs(Number(this.totalPrice)) * -1;
      }
      if ((this.acctsId === 4 || this.acctsId === 5) && this.isSinglePayWay) {
        this.rightKeyword = this.showFinalPrice;
      }
      var online = false;
      if (this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10) {
        online = true;
      }
      var data = {
        code: this.orderno,
        optDate: create_time.split(' ')[0],
        inOut: this.buy_back,
        supplierId: this.companiesId,
        billAmt: this.totalPrice,
        discAmt: this.showFinalPrice,
        disc: Number(this.totalPrice) === 0 ? 1 : (Number(this.showFinalPrice) / Number(this.totalPrice)).toFixed(6),
        payAmt: this.showFinalPrice,
        oweAmt: this.showFinalPrice,
        changeAmt: online ? '0.00' : (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2),
        remark: this.payRemark + this.pay_name,
        vipid: this.showMember ? this.member_id : null,
        vipname: this.showMember ? this.member_name : '',
        vipmobile: this.showMember ? this.member_mobile : '',
        saleItems: sub_data,
        createtime: create_time,
        isVipDay: this.isVipDay,
        acctsId: this.acctsId,
        preDisc: this.preDisc
      };
      // 如果acctId为8，为会员支付，对应数据库account_id的值为6
      // if (this.buy_back === 2) {
      //   data.accountId = '1';
      // } else {
      data.accountId = online ? this.acctsId - 2 : this.acctsId;
      console.log(data, **********);
      // }
      return data;
    },
    sonarBackPayVipSuccess() {
      if (this.from === 'detail_member') {
        this.backStockFunction(this.detail_json.data, this.detail_json.create_time, 2);
      } else {
        this.closeSettlement();
      }
    },
    backPayVipFlg() {
      return this.from === 'member_cancel' || this.from === 'detail_member';
    },
    backPayVip(data, n) {
      var b_data = {};
      if (this.backPayVipFlg()) {
        b_data = _.cloneDeep(data);
        b_data.useNegativeIntegral = 1;
        b_data.originId = this.from === 'member_cancel' ? '6' : b_data.originId;
      } else {
        b_data = {
          systemName: $config.systemName,
          subName: $config.subName,
          uid: data.uid,
          vipId: data.vipid,
          outTradeNo: data.code,
          type: 3,
          originId: 4,
          localOperateTime: data.createtime,
          storeName: this.username,
          checkPoint: data.checkPoint
        };
      }
      b_data.subName = $config.subName;
      b_data.systemName = $config.systemName;
      b_data.newOutTradeNo = this.orderno;
      b_data.phone = this.sys_uid;
      b_data.acctId = this.acctsId;
      // if (this.from === 'member_cancel') {
      //   b_data.acctId = 6;
      // } else {
      //   b_data.acctId = this.acctsId;
      // }
      b_data.sysSid = this.sys_sid;
      b_data.localOperateTime = new Date().format('yyyy-MM-dd hh:mm:ss');
      var that = this;
      demo.$http
        .post(that.$rest.pc_refundvip, b_data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(
          function (res) {
            if (res.data.code === '20003') {
              that.button_paying = true;
              that.refundFailShow = true;
              return;
            }
            if (res.data.code !== '0') {
              demo.msg('error', res.data.msg);
              that.closeSettlement();
              return;
            }
            if (res.data.data === null) {
              demo.msg('success', '退款完成');
              that.show_continue_backPayVipPoint = false;
              that.show_continue_backPayVip = false;
              let create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
              if (JSON.stringify(that.backStockData) === '{}') {
                that.sonarBackPayVipSuccess();
                that.openCashbox(create_time);
              } else {
                let subData = _.cloneDeep(that.backStockData);
                that.backStockData = {};
                that.backStockFunction(subData, create_time, 55);
              }
            } else {
              that.show_continue_backPayVipPoint = true;
              that.show_continue_backPayVip_json = res.data.data;
              that.show_continue_backPayVip_data = _.cloneDeep(b_data);
            }
          },
          function (error) {
            that.sonarRecode2(error);
          }
        );
    },
    sonarBackComplete(create_time) {
      var data = this.subParams(1);
      data.localOperateTime = new Date().format('yyyy-MM-dd hh:mm:ss');
      data.info = {
        receivedMoney: this.rightKeyword,
        changeMoney: (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2)
      };
      this.log_info.detail_data.push(data);
      console.log(JSON.stringify(data), '结算数据');
      if (this.use_integral === true) {
        data.remark = data.remark + ' 使用账户' + this.final_integral + '积分抵现' + this.final_integral_money + '元';
      }
      if (!this.isSinglePayWay) {
        // 组合支付状态下修改并新增参数
        data.accountId = 99;
        let changeAmt = (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2);
        data.blendPays = [
          {
            acctId: this.conbinaPayWayVal[0],
            payAmt: this.conbinaPayWayVal[0] === 1 ? this.combinedFinalPay[0] - changeAmt : this.combinedFinalPay[0],
            remark: data.remark
          },
          {
            acctId: this.conbinaPayWayVal[1],
            payAmt: this.conbinaPayWayVal[1] === 1 ? this.combinedFinalPay[1] - changeAmt : this.combinedFinalPay[1],
            remark: data.remark
          }
        ];
      }
      if (this.from === 'detail_member') {
        var sitems = _.cloneDeep(this.cancel_vipData.left_goods_list);
        for (var i = 0; i < this.cancel_vipData.left_goods_list.length; i++) {
          sitems[i].qty = Number(this.cancel_vipData.left_goods_list[i].qty) * -1;
          sitems[i].amt = Number(this.cancel_vipData.left_goods_list[i].amt) * -1;
        }
        data.saleItems = sitems;
      }
      if (data.accountId === 6 && this.buy_back === 2 && (this.from === 'detail_member' || this.from === 'member_cancel')) {
        data.checkPoint = 1;
        this.backPayVip(_.cloneDeep(this.cancel_vipData), 1);
        this.detail_json = {
          data: data,
          create_time: create_time
        };
      }
      this.sonarBackComplete1(data, create_time);
    },
    sonarBackComplete1(data, create_time) {
      if (this.buy_back === 2) {
        // 退货
        if (this.from !== 'detail_member' || data.acctsId !== 6) {
          this.sonarBackComplete2(data, create_time);
        }
      } else {
        let memberNewData = {
          member_name: '',
          member_value: '',
          member_money_name: '',
          member_money_value: '',
          member_mobile_name: '',
          member_mobile_value: '',
          member_point_name: '',
          member_point_value: 0
        };
        let showMemberDeep = _.cloneDeep(this.showMember);
        if (this.showMember) {
          this.getMemberIntegral(create_time, memberNewData, showMemberDeep, 2, data);
        } else {
          this.saveLastOrderDetail(create_time, memberNewData, data);
        }
      }
    },
    saveLastOrderDetail(create_time, memberNewData, data) {
      var acctsName = '';
      let payWay1 = '';
      let payWay2 = '';
      if (this.isSinglePayWay) {
        acctsName = this.sonarPrintOrder1(this.acctsId);
      } else {
        payWay1 = this.sonarPrintOrder1(this.conbinaPayWayVal[0]);
        payWay2 = this.sonarPrintOrder1(this.conbinaPayWayVal[1]);
        acctsName = '组合支付';
      }
      var online = this.sonarCheck2();
      var flag_print2 = this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10;
      if (flag_print2) {
        online = true;
      }
      var final_list = _.cloneDeep(this.left_goods_list);
      final_list = this.sonarPrintOrder3(final_list);
      var print_data = {
        conbinaPayWayVal: this.conbinaPayWayVal,
        combinedFinalPay: this.combinedFinalPay,
        payWay1: payWay1,
        payWay2: payWay2,
        printername: this.setting_small_printer,
        storename: this.username,
        accts: acctsName,
        pay_amt: online ? Number(this.showFinalPrice).toFixed(2) : Number(this.rightKeyword).toFixed(2),
        change_amt: online ? '0.00' : (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2),
        createAt: create_time,
        remark: 'Tel:' + this.phone,
        member_name: memberNewData.member_name,
        member_value: memberNewData.member_value,
        member_money_name: memberNewData.member_money_name,
        member_money_value: memberNewData.member_money_value,
        member_mobile_name: memberNewData.member_mobile_name,
        final_integral_money: this.use_integral === false ? '0.00' : this.final_integral_money,
        member_mobile_value:
          memberNewData.member_mobile_value.toString().substring(0, 3) + '****' + memberNewData.member_mobile_value.toString().substring(7, 11),
        member_point_name: memberNewData.member_point_name,
        member_point_value: memberNewData.member_point_value,
        goods: final_list
      };
      print_data.operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
      print_data.printNum = demo.$store.state.show.smallPrinterCopies;
      const vipInfo = {
        member_money_value: memberNewData.member_money_value,
        final_integral_money: this.use_integral === false ? '0.00' : this.final_integral_money,
        member_point_value: memberNewData.member_point_value
      };
      const infoObj = {};
      if (memberNewData.member_name) {
        infoObj.vipInfo = vipInfo;
      }
      console.warn('scanPayInfo', this.scanPayInfo);
      if (this.scanPayInfo) {
        infoObj.payInfo = this.scanPayInfo;
      }
      data.info1 = Object.keys(infoObj).length > 0 ? JSON.stringify(infoObj) : '';
      data.info2 = '';
      this.registerFunction(data, create_time);
      this.SET_SHOW({ lastOrderDetail: [Object.assign(_.cloneDeep(data), print_data)] });
    },
    sonarBackComplete2(data, create_time) {
      if (this.from === 'pay') {
        this.backStockFunction(data, create_time, 3);
        return;
      }
      var that = this;
      var b_data = _.cloneDeep(this.cancel_vipData);
      b_data.acctId = this.acctsId;
      b_data.phone = this.sys_uid;
      b_data.newOutTradeNo = this.orderno;
      b_data.sysSid = this.sys_sid;
      b_data.localOperateTime = new Date().format('yyyy-MM-dd hh:mm:ss');
      b_data.systemName = $config.systemName;
      b_data.subName = $config.subName;
      b_data.localOperateTime = new Date().format('yyyy-MM-dd hh:mm:ss');
      demo.$http
        .post(this.$rest.pc_refundvip, b_data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(
          function (res) {
            if (res.data.code === '20003') {
              that.button_paying = true
              that.refundFailShow = true;
              return;
            }
            if (res.data.code !== '0') {
              demo.msg('error', res.data.msg);
              that.closeSettlement();
              return;
            }
            if (res.data.data === null) {
              demo.msg('success', '退款完成');
              that.backStockFunction(data, create_time, 4);
            } else {
              that.show_continue_backPayVipPoint = true;
              that.show_continue_backPayVip_json = res.data.data;
              that.show_continue_backPayVip_data = _.cloneDeep(b_data);
              that.backStockData = _.cloneDeep(data);
            }
          },
          function (error) {
            that.sonarRecode2(error);
          }
        );
    },
    registerFunction(data, create_time) {
      var that = this;
      data.saleWithoutStock = this.saleWithoutStock;
      data.autoPacking = this.autoPacking;
      console.log(JSON.stringify(data), '结算总数据');
      saleService.settlement(
        data,
        function () {
          that.settlementSuccess(data, create_time);
        },
        function (res) {
          demo.msg('error', res);
          that.closeSettlement();
        }
      );
    },
    backStockFunction(data, create_time, n) {
      console.log(n, 1122222222222);
      var that = this;
      data.saleWithoutStock = this.saleWithoutStock;
      data.autoPacking = this.autoPacking;
      data.returnOutTradeNo = this.returnOutTradeNo;
      data.refundFingerprint = this.finalPayParam.refundFingerprint;
      console.log(data, '退货接口参数data');
      saleService.singleSettlement(
        data,
        function () {
          that.settlementSuccess(data, create_time);
        },
        function (res) {
          demo.msg('error', res);
          that.closeSettlement();
        }
      );
    },
    settlementSuccess(data, create_time) {
      var that = this;
      var msg = '';
      this.scanPayInfo = null;
      if (that.buy_back === 1) {
        that.payVideo(data, 'pay');
        msg = '收银完成';
      } else if (that.buy_back === 2) {
        msg = '退货完成';
        if (that.isDetail) {
          that.SET_SHOW({ detailBackPay: true });
        }
      } else {
        console.log('其他');
      }
      this.getKexian('', 0);
      that.SET_SHOW({ completePay: true });
      that.discountIndex = null;
      that.reduceIndex = null;
      that.SET_SHOW({ finalPayParam: '' });
      demo.msg('success', msg);
      // 调钱箱
      that.openCashbox(create_time);
    },
    backComplete(n) {
      // this.button_paying = !this.button_paying;
      console.log(n, 2569);
      this.button_paying = false;
      var create_time = new Date().format('yyyy-MM-dd hh:mm:ss');
      var that = this;
      if (this.from === 'pay' || this.from === 'detail_member') {
        this.sonarBackComplete(create_time);
      } else if (this.from === 'member_recharge') {
        this.lock_number = this.lock_max_number;
        var online = false;
        if (this.acctsId === 8 || this.acctsId === 9 || this.acctsId === 10) {
          online = true;
        }
        var member_data = {
          systemName: $config.systemName,
          subName: $config.subName,
          phone: this.finalPayParam.member_data.sysUid,
          sysSid: this.finalPayParam.member_data.sysSid,
          storeName: this.username,
          vipId: this.finalPayParam.member_data.vipId,
          originId: 1,
          type: 1, // 1-充值；2-消费。默认1
          acctId: online ? this.acctsId - 2 : this.acctsId, // 默认1-现金
          money: this.finalPayParam.member_data.money,
          giveMoney: this.finalPayParam.member_data.giveMoney,
          createUser: this.loginInfo.uid,
          outTradeNo: this.orderno,
          usePoint: false,
          point: 0,
          pointMoney: 0,
          localOperateTime: new Date().format('yyyy-MM-dd hh:mm:ss'),
          info: {
            receivedMoney: this.rightKeyword,
            changeMoney: (Number(this.rightKeyword) - Number(this.showFinalPrice)).toFixed(2)
          }
        };
        demo.$http
          .post(that.$rest.pc_addvipDetails, member_data, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
          .then(
            function (res) {
              if (res.data.code == 0) {
                that.memberRechargeSuccess(member_data);
                that.openCashbox(create_time);
              } else {
                that.member_recharge_fail = true;
                demo.msg('warning', '会员充值失败，请稍后再试');
              }
            },
            function (error) {
              that.member_recharge_fail = true;
              demo.msg('warning', '会员充值失败，请稍后再试');
              console.error(error);
            }
          );
      } else if (this.from === 'member_cancel') {
        this.backPayVip(this.cancel_vipData, 2);
      } else {
        console.log('其他');
      }
    },
    memberRechargeSuccess(member_data) {
      this.payVideo(member_data, 'member');
      demo.msg('success', '充值成功');
    },
    sonarCashbox(create_time) {
      var that = this;
      if ((this.permit_print && !this.ifautoCash && this.from !== 'vip_times_card') || (this.permitCardPrint && this.from === 'vip_times_card')) {
        if (this.setting_small_printer != undefined && this.setting_small_printer != null && this.setting_small_printer.trim() != '') {
          this.printOrder(create_time);
        } else {
          this.closeAndInit();
          setTimeout(function () {
            demo.msg('warning', that.$msg.not_setting_small_printer);
          }, 3000);
        }
      } else {
        this.closeAndInit();
      }
    },
    closeAndInit() {
      CefSharp.PostMessage(`准备关闭收银弹窗 ifautoCash${this.ifautoCash}`);
      if (!this.ifautoCash) {
        this.closeSettlement();
      }
      if (this.ifautoCash) {
        this.ifpay = true;
        setTimeout(() => {
          this.left_goods_list = [];
          this.closeSettlement();
          this.ifpay = false;
        }, 3000);
      }
    },
    openCashbox(create_time) {
      var that = this;
      // 调打印机
      this.sonarCashbox(create_time);

      if (that.acctsId === 1 || (that.acctsId === 99 && that.chkNameArr.indexOf('现金') !== -1)) {
        try {
          if (that.setting_hasmoneybox == 'false') {
          } else if (that.setting_moneybox == null || that.setting_moneybox.trim() === '') {
            if (that.setting_small_printer == null || that.setting_small_printer.trim() === '') {
            } else {
              external.openCashBox(
                that.setting_small_printer,
                function (res) {
                  console.log(res, '钱箱弹出成功');
                  that.$_actionLog(logList.openMoneybox, `结算-自动打开钱箱成功`);
                },
                function (res) {
                  demo.msg('warning', that.$msg.cash_box_in_error);
                  console.log(res);
                  that.$_actionLog(logList.openMoneybox, `结算-自动打开钱箱失败,${that.$msg.cash_box_in_error}`);
                }
              );
            }
          } else {
            external.openCashBox_separate(
              that.setting_moneybox,
              2400,
              8,
              function (res) {
                console.log(res, '打开钱箱成功');
                that.$_actionLog(logList.openMoneybox, `结算-自动打开钱箱成功`);
              },
              function (res) {
                demo.msg('warning', that.$msg.cash_box_in_error);
                console.error(res);
                that.$_actionLog(logList.openMoneybox, `结算-自动打开钱箱失败,${that.$msg.cash_box_in_error}`);
              }
            );
          }
        } catch (e) {
          setTimeout(function () {
            demo.msg('warning', '钱箱设备异常，请检查钱箱！');
          }, 4000);
        }
      }
    },
    closeSettlement(n) {
      CefSharp.PostMessage(`修改客显`);
      this.getKexian('', 0);
      this.codeString = '';
      this.showMember = false;
      CefSharp.PostMessage(`关闭收银弹窗`);
      this.$nextTick(() => {
        this.button_paying = true;
        this.SET_SHOW({
          settlement: false,
          isContentCombinedPay: false
        });
        this.$nextTick(() => {
          this.$forceUpdate();
        });
        CefSharp.PostMessage(`修改全局状态settlement关闭收银弹窗`);
        if ((n !== 'cancel_pay_sale' && n !== 'uninit') || this.showInputRecharge) {
          let goodsList = this.judgeGoodList();
          const s_data2 = {
            screen2ShowMember: false,
            screen2ShowList: this.from === 'vip_times_card' ? [] : goodsList,
            screen2ShowFinalPrice: 0,
            screen2ReceiveMoney: 0,
            screen2MemberName: '',
            screen2MemberPoint: '',
            screen2MemberPhone: ''
          };
          demo.screen2(s_data2, 8);
        }
      })
    },
    failButSaveSales() {
      var headCode = this.codeString.toString().substring(0, 2);
      if (headCode === '28') {
        this.acctsId = 10;
      } else {
        this.acctsId = 9;
      }
      this.backComplete(3);
    },
    buyComplete() {
      if (Number(this.showFinalPrice) > 10000000) {
        demo.msg('warning', '单笔订单不能超过1000万！');
        return;
      }
      if (this.no_inputPrice && this.isSinglePayWay) {
        this.rightKeyword = this.showFinalPrice;
      }
      if (Number(this.rightKeyword) < Number(this.showFinalPrice)) {
        demo.msg('warning', this.$msg.received_cannot_less_than_receivable);
      } else if (!this.lock_screen) {
        this.lock_screen = true;
        if ((this.member_id !== '' && this.member_id !== undefined && this.showMember) || this.from === 'vip_times_card') {
          this.recordMemberPoint();
        } else {
          this.backComplete(3);
        }
      } else {
        console.log('其他');
      }
    },
    inputCalculator(str) {
      this.no_inputPrice = false;
      // 点击数字和固定金额
      // 如果数字，则记录 recordInputStyle = 0
      // 如果固定金额，则记录 recordInputStyle = 1
      // 跟上次输入方式不一样，清空
      if (this.recordInputStyle === 1) {
        this.rightKeyword = '';
      }
      this.recordInputStyle = 0;
      if (str === 'back') {
        this.rightKeyword = this.rightKeyword.toString();
        if (this.rightKeyword.length > 0) {
          this.rightKeyword = this.rightKeyword.substring(0, this.rightKeyword.length - 1);
        }
      } else if (str === 'del') {
        this.rightKeyword = '';
      } else if (str === '.' && this.rightKeyword === '') {
        this.rightKeyword += '0.';
      } else {
        var n = this.rightKeyword.toString() + str;
        this.rightKeyword = n;
        if (Number(this.rightKeyword > 999999.99)) {
          this.rightKeyword = '999999.99';
        }
      }
    },
    plusMoney(n) {
      this.no_inputPrice = false;
      // 点击数字和固定金额
      // 如果数字，则记录 recordInputStyle = 0
      // 如果固定金额，则记录 recordInputStyle = 1
      // 跟上次输入方式不一样，清空
      if (this.recordInputStyle === 0) {
        this.rightKeyword = '';
      }
      this.recordInputStyle = 1;
      if (Number(this.rightKeyword) + Number(n) > 99999.0) {
        this.rightKeyword = 99999.0;
      } else {
        this.rightKeyword = Number(this.rightKeyword) + Number(n);
      }
      this.inputNum = '';
    },
    memberPay() {
      console.log('进入');
      if (this.stopMemberPay) {
        return;
      }
      this.stopMemberPay = true;
      setTimeout(() => {
        this.stopMemberPay = false;
      }, 1500);
      console.log('执行');
      if (this.member_password !== this.input_member_password) {
        demo.msg('warning', '支付码错误，请重新输入');
        if (this.voiceOff) {
          this.tryPlayVoice('支付码错误，请重新输入', this.soundVolume * 4);
        }
        return;
      } else if (Number(this.member_money) < Number(this.showFinalPrice) && this.isSinglePayWay) {
        demo.msg('error', '会员余额不足，请提醒顾客充值或更换支付方式');
        if (this.voiceOff) {
          this.tryPlayVoice('会员余额不足，请充值或更换支付方式', this.soundVolume * 4);
        }
        return;
      } else {
        console.log('其他');
      }
      if (!this.isSinglePayWay) {
        this.rightKeyword = this.chkNameArr[0] === '会员' ? this.combinedAmount1 : this.combinedAmount2;
      } else {
        this.rightKeyword = this.showFinalPrice;
      }
      this.lock_screen = true;
      this.recordMemberPoint();
    },
    payVideo(data, type) {
      if (!this.voiceOff) {
        return;
      }
      if (type === 'member') {
        this.memberVideo(data);
        return;
      }
      let changeMoneyFormat = data.info.changeMoney + '';
      let receivedMoneyFormat = data.info.receivedMoney + '';
      const msg = this.acctsId === 9 || this.acctsId === 10 ? '扫码' : this.acctsId === 1 ? '现金' : '';
      this.formatVideo(changeMoneyFormat, receivedMoneyFormat, `${msg}收款成功，收款`, '找零');
    },
    memberVideo(data) {
      if (!this.voiceOff) {
        return;
      }
      let changeMoneyFormat = data.giveMoney === null ? '0.00' : data.giveMoney + '';
      let receivedMoneyFormat = data.money + '';
      this.formatVideo(changeMoneyFormat, receivedMoneyFormat, '充值成功，充值', '赠送');
    },
    formatVideo(changeMoneyFormat, receivedMoneyFormat, video1, vodeo2) {
      let change = changeMoneyFormat;
      let receive = receivedMoneyFormat;
      if (changeMoneyFormat.indexOf('.00') !== -1) {
        change = changeMoneyFormat.split('.')[0];
      }
      if (receivedMoneyFormat.indexOf('.00') !== -1) {
        receive = receivedMoneyFormat.split('.')[0];
      }
      if (Number(change) === 0) {
        this.tryPlayVoice(video1 + Number(receive) + '元', this.soundVolume * 4);
      } else {
        this.tryPlayVoice(video1 + Number(receive) + '元，' + vodeo2 + Number(change) + '元', this.soundVolume * 4);
      }
    },
    /**
     * 查询已有次卡
     */
    ckCard() {
      let that = this;
      demo.$http
        .post(
          this.$rest.pc_getTimesCardPurchase,
          {
            phone: that.printCardData.vip_times_card.phone,
            sysSid: that.sys_sid,
            systemName: $config.systemName,
            vipId: that.finalPayParam.member_id,
            name: '',
            isShowOverdue: true
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then(res => {
          if (res.data.code === '0') {
            that.ckCardCallbackSuccess(res);
          } else {
            demo.msg('warning', '获取云端数据失败，请重试');
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    ckCardCallbackSuccess(res) {
      // res.data.code === '0'
      let that = this;
      let ck_all = res.data.data;
      let purchases = [];
      ck_all.map(e => {
        e.surplusTimes = e.restTimes;
        if (e.isDeleted === 0) {
          if (e.endDate === '永久') {
            purchases.push(e.name + '，' + (e.surplusTimes !== '-' ? '剩余' + e.surplusTimes + '次' : '不限次数') + '，有效时间' + e.endDate);
          } else {
            purchases.push(
              e.name + '，' + (e.surplusTimes !== '-' ? '剩余' + e.surplusTimes + '次' : '不限次数') + '，有效时间' + e.startDate + '至' + e.endDate
            );
          }
        }
      });
      // if (that.printCardData.print_data) {
      //   that.printCardData.print_data.operater = '管理员';
      // }
      that.printCardData.purchase = purchases;
      that.printCardData.printNum = demo.$store.state.show.smallPrinterCopies;
      that.printCardData.createAt = that.printCardData.createAt || that.printCardData.createtime;
      pos.printer.printPOSTimes(that.printCardData, that);
    },
    // =========组合支付相关===============
    blurCombinePayMoney(n) {
      // 失焦组装组合支付金额
      if (n === 0) {
        this.blurCombinePayMoneyFst();
      } else {
        this.blurCombinePayMoneySecond();
      }
      this.setCombinedVal();
    },
    blurCombinePayMoneyFst() {
      if (Number(this.combinedAmount1) > 999999.99) {
        this.combinedAmount1 = '999999.99';
      } else {
        this.combinedAmount1 = Number(this.combinedAmount1).toFixed(2);
      }
      if (Number(this.combinedAmount1) > Number(this.showFinalPrice) && this.chkNameArr[0] !== '现金') {
        demo.msg('warning', this.chkNameArr[0] + '支付金额不能超过应付金额');
        return;
      }
      if (Number(this.combinedAmount1) <= Number(this.showFinalPrice) && this.chkNameArr[0] !== '现金') {
        this.combinedAmount2 = (Number(this.showFinalPrice) - Number(this.combinedAmount1)).toFixed(2);
      }
      if (
        Number((Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2)) <= Number(this.showFinalPrice) &&
        this.chkNameArr[0] === '现金'
      ) {
        this.combinedAmount2 = (Number(this.showFinalPrice) - Number(this.combinedAmount1)).toFixed(2);
      }
    },
    blurCombinePayMoneySecond() {
      if (Number(this.combinedAmount2) > 9999999.99) {
        this.combinedAmount2 = '9999999.99';
      } else {
        this.combinedAmount2 = Number(this.combinedAmount2).toFixed(2);
      }
      if (Number(this.combinedAmount2) > Number(this.showFinalPrice) && this.chkNameArr[1] !== '现金') {
        demo.msg('warning', this.chkNameArr[1] + '支付金额不能超过应付金额');
        return;
      }
      if (Number(this.combinedAmount2) <= Number(this.showFinalPrice) && this.chkNameArr[1] !== '现金') {
        this.combinedAmount1 = (Number(this.showFinalPrice) - Number(this.combinedAmount2)).toFixed(2);
      }
      if (
        Number((Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2)) <= Number(this.showFinalPrice) &&
        this.chkNameArr[1] === '现金'
      ) {
        this.combinedAmount1 = (Number(this.showFinalPrice) - Number(this.combinedAmount2)).toFixed(2);
      }
    },
    setCombinedVal() {
      // 设置最终支付金额
      this.$set(this.combinedFinalPay, 0, this.combinedAmount1);
      this.$set(this.combinedFinalPay, 1, this.combinedAmount2);
      this.rightKeyword = (Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2);
      this.combinedFocus = 0;
      this.no_inputPrice = false;
    },
    combinedPayCheck() {
      // 支付前输入校验
      // if () {
      //   if (Number(this.rightKeyword) > 10000000) {
      //     demo.msg('warning', '单笔订单不能超过1000万！');
      //     return;
      //   }
      // }
      if (this.orderno === '' && n !== 'cancel_pay_sale') {
        // 单号异常
        demo.msg('warning', '单号异常，请重新结算');
        return;
      }
      if (this.conbinaPayWayVal.length < 2) {
        demo.msg('warning', '只使用一种支付方式请切换到整单支付');
        return;
      }
      if (Number(this.combinedAmount1) === 0 || Number(this.combinedAmount2) === 0) {
        demo.msg('warning', '只使用一种支付方式请切换到整单支付');
        return;
      }
      if (this.conbinaPayWayVal.indexOf(6) !== -1) {
        // 存在会员卡支付时判断余额
        let spendVipMoney = this.conbinaPayWayVal.indexOf(6) === 0 ? this.combinedAmount1 : this.combinedAmount2;
        if (this.member_money - spendVipMoney < 0) {
          demo.msg('warning', '会员卡余额不足，请更换支付方式');
          return;
        }
      }
      this.combinedPayCheckAgain();
    },
    combinedPayCheckAgain() {
      if (isNaN(this.combinedAmount1) || isNaN(this.combinedAmount2)) {
        demo.msg('warning', '支付金额有误，请重新输入');
        return;
      }
      if (Number(this.combinedAmount1) > Number(this.showFinalPrice) && this.conbinaPayWayVal[0] !== 1) {
        demo.msg('warning', this.chkNameArr[0] + '支付金额不能超过应付金额');
        return;
      }
      if (Number(this.combinedAmount2) > Number(this.showFinalPrice) && this.conbinaPayWayVal[1] !== 1) {
        demo.msg('warning', this.chkNameArr[1] + '支付金额不能超过应付金额');
        return;
      }
      if (
        Number((Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2)) > Number(this.showFinalPrice) &&
        this.conbinaPayWayVal.indexOf(1) === -1
      ) {
        demo.msg('warning', '实付金额已超出应收金额');
        return;
      }
      if (Number((Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2)) < Number(this.showFinalPrice)) {
        demo.msg('warning', '实付金额不足');
        return;
      }
      if (this.conbinaPayWayVal.indexOf(6) !== -1) {
        // 包含会员卡支付
        this.combinedVipPay();
      } else {
        this.combinedPay();
      }
    },
    combinedVipPay() {
      if (this.showMemberPassIpt) {
        // 会员支付确认
        this.combinedCheckPass();
      } else {
        this.showMemberPassIpt = true;
      }
    },
    combinedCheckPass() {
      if (this.member_password !== this.input_member_password) {
        demo.msg('warning', '支付码错误，请重新输入');
        if (this.voiceOff) {
          this.tryPlayVoice('支付码错误，请重新输入', this.soundVolume * 4);
        }
        return;
      }
      this.showMemberPassIpt = false;
      this.combinedPay();
    },
    combinedPay() {
      // 组合支付确认结账
      console.log(this.conbinaPayWayVal, '混合支付选定值');
      this.button_paying = false;
      this.combinedTotalPrice = this.rightKeyword; // 显示组合支付总金额
      this.acctsId = 99;
      this.buyComplete();
    },
    initCombinePay() {
      this.isSinglePayWay = true;
      this.conbinaPayWayVal = [];
      this.combinedAmount1 = '';
      this.combinedAmount2 = '';
      this.combinedFinalPay = [];
      this.combinedFocus = 0;
      this.chkNameArr = [];
      this.combinedTotalPrice = '';
    },
    payTypeChange(n) {
      // 整单支付&组合支付切替
      if (n === 0) {
        this.isSinglePayWay = true;
        this.no_inputPrice = true;
        this.rightKeyword = '';
      } else {
        this.isSinglePayWay = false;
        this.$nextTick(() => {
          for (let i = 0; i < document.getElementsByClassName('combinedKeybord').length; i++) {
            document.getElementsByClassName('combinedKeybord')[i].addEventListener('mousedown', function (e) {
              // 添加组合支付小键盘
              e.preventDefault();
            });
          }
        });
        // this.cancelListenevent();
        this.rightKeyword = (Number(this.combinedAmount1) + Number(this.combinedAmount2)).toFixed(2);
      }
      var s_data2 = {
        screen2isSinglePayWay: this.isSinglePayWay,
        screen2ShowMember: this.showMember
      };
      demo.screen2(s_data2, 30);
      demo.actionLog(logList.combinationPay);
    },
    handlePaywayChange(checkArr) {
      // 支付方式多选按钮change
      this.chkNameArr = [];
      for (let i = 0; i < checkArr.length; i++) {
        this.chkNameArr.push(this.getPayWayLabelByVal(checkArr[i]));
      }
      if (this.chkNameArr.length === 1) {
        if (this.combinedAmount1 === '' && this.combinedAmount2 === '') {
          this.combinedAmount1 = this.showFinalPrice;
        }
        this.$nextTick(() => {
          document.getElementById('combinedAmount1').focus();
        });
      } else if (this.chkNameArr.length === 2) {
        this.$nextTick(() => {
          document.getElementById('combinedAmount2').focus();
        });
      } else {
        console.log('do nothing');
      }
    },
    getPayWayLabelByVal(val) {
      // 多选按钮根据id找name
      let label = '';
      for (let i = 0; i < this.conbinaPayWayArr.length; i++) {
        if (this.conbinaPayWayArr[i].value === val) {
          label = this.conbinaPayWayArr[i].label;
        }
      }
      return label;
    },
    combinedInputCalc(str) {
      // 组合支付小键盘
      // 如果数字，则记录 recordInputStyle = 0
      // 跟上次输入方式不一样，清空
      if (this.combinedFocus === 0) {
        return;
      }
      this.recordInputStyle = 0;
      let combineIptTmp;
      let selectStartIndex;
      let selectEndIndex;
      let selectOffset;
      let inputObj;
      // let inputVal;
      if (this.combinedFocus === 1) {
        combineIptTmp = this.combinedAmount1;
        inputObj = document.getElementById('combinedAmount1');
        selectStartIndex = inputObj.selectionStart;
        selectEndIndex = inputObj.selectionEnd;
      } else {
        combineIptTmp = this.combinedAmount2;
        inputObj = document.getElementById('combinedAmount2');
        selectStartIndex = inputObj.selectionStart;
        selectEndIndex = inputObj.selectionEnd;
      }
      // 获取输入框下标位置
      if (str === 'back') {
        // if (combineIptTmp === '0.00') {
        //   combineIptTmp = '';
        // }
        if (selectStartIndex === selectEndIndex) {
          if (combineIptTmp.toString().length > 0) {
            combineIptTmp = combineIptTmp.substring(0, selectStartIndex - 1) + '' + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
            selectOffset = -1;
          }
        } else {
          combineIptTmp = combineIptTmp.substring(0, selectStartIndex) + '' + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
          selectOffset = 0;
        }
        setTimeout(() => {
          // 删除时光标处理
          this.setCaretPosition(inputObj, selectStartIndex + selectOffset);
        }, 0);
      } else if (str === 'del') {
        combineIptTmp = '';
      } else if (str === '.') {
        if (combineIptTmp === '' || combineIptTmp === '0.00') {
          combineIptTmp = '0.';
          selectOffset = 2;
        } else if (selectStartIndex === selectEndIndex) {
          // 无选中部分
          if (combineIptTmp.indexOf('.') === -1 && combineIptTmp.length < 10) {
            combineIptTmp = combineIptTmp.substring(0, selectStartIndex) + '.' + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
            selectOffset = 1;
          } else {
            selectOffset = 0;
          }
        } else {
          // 有选中部分
          if (combineIptTmp.indexOf('.') === -1 && combineIptTmp.length < 10) {
            combineIptTmp = combineIptTmp.substring(0, selectStartIndex) + '.' + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
            selectOffset = 1;
          } else if (combineIptTmp.substring(selectStartIndex, selectEndIndex).indexOf('.') !== -1) {
            combineIptTmp = combineIptTmp.substring(0, selectStartIndex) + '.' + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
            selectOffset = 1;
          } else {
            return;
          }
        }
        if (combineIptTmp === '.') {
          combineIptTmp = '0.';
          selectOffset = 2;
        }
        setTimeout(() => {
          // 删除时光标处理
          this.setCaretPosition(inputObj, selectStartIndex + selectOffset);
        }, 0);
      } else if (str === '00') {
        let newStr = '';
        if (selectStartIndex === selectEndIndex) {
          // 无选中部分
          if (combineIptTmp.toString().length <= 8) {
            selectOffset = 2;
            newStr = str;
          } else if (combineIptTmp.toString().length === 9) {
            selectOffset = 1;
            newStr = '0';
          } else {
            selectOffset = 0;
            return;
          }
          combineIptTmp =
            combineIptTmp.toString().substring(0, selectStartIndex) +
            newStr +
            combineIptTmp.toString().substring(selectEndIndex, combineIptTmp.length);
          setTimeout(() => {
            // 光标处理
            this.setCaretPosition(inputObj, selectStartIndex + selectOffset);
          }, 0);
        } else {
          // 有选中部分
          let oldLength = combineIptTmp.length;
          let newCombineIptTmp = combineIptTmp.substring(0, selectStartIndex) + combineIptTmp.substring(selectEndIndex, combineIptTmp.length);
          if (newCombineIptTmp.toString().length <= 8) {
            selectOffset = 2;
            newStr = str;
          } else {
            selectOffset = 1;
            newStr = '0';
          }
          combineIptTmp = combineIptTmp.substring(0, selectStartIndex) + newStr + combineIptTmp.substring(selectEndIndex, oldLength);
          setTimeout(() => {
            // 光标处理
            this.setCaretPosition(inputObj, selectStartIndex + selectOffset);
          }, 0);
        }
      } else {
        if (selectStartIndex === selectEndIndex) {
          // 无选中部分
          if (combineIptTmp.toString().length === 10) {
            return;
          }
          if (combineIptTmp.toString().indexOf('.') !== -1 && selectStartIndex - combineIptTmp.toString().indexOf('.') > 2) {
            return;
          }
          combineIptTmp =
            combineIptTmp.toString().substring(0, selectStartIndex) +
            str +
            combineIptTmp.toString().substring(selectStartIndex, combineIptTmp.toString().length);
          if (selectStartIndex !== combineIptTmp.toString().length) {
            setTimeout(() => {
              // 光标处理
              this.setCaretPosition(inputObj, selectStartIndex + 1);
            }, 0);
          }
        } else {
          // 有选中部分
          combineIptTmp =
            combineIptTmp.toString().substring(0, selectStartIndex) + str + combineIptTmp.toString().substring(selectEndIndex, combineIptTmp.length);
          setTimeout(() => {
            // 光标处理
            this.setCaretPosition(inputObj, selectStartIndex + 1);
          }, 0);
        }
      }
      if (this.combinedFocus === 1) {
        this.combinedAmount1 = combineIptTmp;
      } else {
        this.combinedAmount2 = combineIptTmp;
      }
      this.rightKeyword = (Number(this.combinedAmount1) + Number(this.combinedAmount2)).toString();
    },
    setCaretPosition(ctrl, pos) {
      // 设置光标位置函数
      if (ctrl.setSelectionRange) {
        ctrl.focus();
        ctrl.setSelectionRange(pos, pos);
      } else if (ctrl.createTextRange) {
        var range = ctrl.createTextRange();
        range.collapse(true);
        range.moveEnd('character', pos);
        range.moveStart('character', pos);
        range.select();
      } else {
        console.log('');
      }
    },
    selectContent(n) {
      // 选中内容
      this.$nextTick(() => {
        document.getElementById('combinedAmount' + n).select();
      });
    },
    returnMoneySubmit(n) {
      this.returnGoodsFlg = true;
      this.getKexian('', 0);
      if (n === 'cancel') {
        this.clickButton('cancel_pay_back');
      } else {
        this.clickButton('back');
      }
    },
    judgeGoodList() {
      if (!this.left_goods_list || this.returnGoodsFlg) {
        this.returnGoodsFlg = false;
        return [];
      } else {
        return this.left_goods_list.length === 0 ? [] : this.left_goods_list;
      }
    }
  },
  mounted() {
    this.creatZfbCode();
    this.creatQrCode();
    if (this.showKefu) {
      this.getHelpQrCode();
    }
    let store = _.cloneDeep(this.storeList);
    let stock = '1';
    if (store[0].settings) {
      stock = demo.t2json(store[0].settings).setSystem ? demo.t2json(store[0].settings).setSystem : '1';
    }
    this.saleWithoutStock = stock;
    if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
      this.autoPacking = 1;
    } else {
      this.autoPacking = demo.t2json(this.storeList[0].settings).autoPacking === '1' ? 1 : 0;
    }
  },
  beforeDestroy() {
    this.closeInval();
    this.cancelListenevent();
    this.temShowFinalPrice = '';
  },
  computed: mapState({
    settlement: state => state.show.settlement,
    isDetail: state => state.show.isDetail,
    isContentCombinedPay: state => state.show.isContentCombinedPay,
    finalPayParam: state => state.show.finalPayParam,
    aid: state => state.show.aid,
    loginInfo: state => state.show.loginInfo,
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    setting_small_printer: state => state.show.setting_small_printer,
    payRemark: state => state.show.payRemark,
    username: state => state.show.username,
    setting_hasmoneybox: state => state.show.setting_hasmoneybox,
    setting_moneybox: state => state.show.setting_moneybox,
    permit_print: state => state.show.permit_print,
    settlementReducePrice: state => state.show.settlementReducePrice,
    isVipDay: state => state.show.isVipDay,
    permitCardPrint: state => state.show.permit_card_print,
    storeList: state => state.show.storeList,
    voiceOff: state => state.show.voiceOff,
    appSecret: state => state.show.app_secret,
    sysId: state => state.show.sysId,
    quickPayType: state => state.show.quickPayType,
    memberDetail: state => state.show.member_detail,
    weighUnitList: state => state.show.weighUnitList,
    showInputRecharge: state => state.show.showInputRecharge,
    soundVolume: state => state.show.soundVolume,
    kexianValue: state => state.show.kexianValue,
    showKexian: state => state.show.showKexian,
    showKefu: state => state.show.showKefu
  })
};
</script>
