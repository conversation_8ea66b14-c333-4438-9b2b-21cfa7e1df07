export const SET_SHOW = 'SET_SHOW'; // 存入数据
const brandName = '掌柜智囊';
export default {
  // 放置公用状态
  state: {
    industry: 'pos',
    systemName: 'zgzn',
    weightUnits: ['千克', '克', '斤', '公斤', '两', 'g', 'G', 'kg', 'Kg', 'kG', 'KG'],
    isHeader: false,
    // isHeader: true,
    isLogin: true,
    isFirstLogin: true,
    isHome: false,
    isFirstHome: false,
    zgcmAppletBase64: '',
    isGoods: false,
    isEmployee: false,
    isCheck: false,
    isDetail: false,
    isStock: false,
    isPay: false,
    isLogo: false,
    isHelp: false,
    isUser: false,
    isSetting: false,
    isScreen2: false,
    isForgetpwd: false,
    isRegister: false,
    isChooseIndustry: false,
    isAddstore: false,
    isMember: false,
    settlement: false,
    isReport: false,
    isGuide: false, // 引导页页面
    isTag: false, // 吊牌打印页面
    isPrintSetting: false, // 配件设置
    isShortMessage: false, // 短信购买页面
    isMessageHistory: false, // 短信报表页面
    isMemberSetting: false, // 会员设置
    show_pick_up: false, // 取件
    isOnceCard: false, // 次卡报表
    isChangeShifts: false, // 交接班
    isReportForms: false, // 报表
    isStockStatistics: false, // 库存统计
    isChangeShiftsRecord: false, // 交接班记录
    isStockRecord: false, // 商品库存查询
    isStockStatisticsReport: false, // 商品库存统计
    isSupplierManage: false, // 供应商管理
    showSettingGoodsSupplierDialog: false, // 给商品设置供应商
    isStockInventory: false, // 库存盘点
    showAddEditSupplier: false, // 新增/修改供应商
    isDepositRecord: false, // 寄存明细
    isDepositResidue: false, // 寄存剩余
    isVipValueAnalysis: false, // 会员价值分析
    isVipIncreaseStatistics: false, // 会员增长统计
    isVipConsumeCollection: false, // 会员消费汇总
    isVipRechargeStatistics: false, // 会员充值统计
    isVipRechargeDetail: false, // 会员充值明细
    isPointsUseDetail: false, // 积分使用明细
    isStockChangeDetail: false, // 库存报表-变动明细
    isStockCheckDetail: false, // 库存报表-盘点明细
    isStockWarningDetail: false, // 库存报表-库存预警
    isOverdueWarningDetail: false, // 库存报表-过期预警
    isBuySoftware: false,
    isSizeSetting: false,
    isQKD: true,
    showPaySetting: false, // 配件设置窗口
    showMemberGoods: false,
    showTimesCard: false,
    isSizeTemplate: false,
    showTimesCardTab: false,
    isGoodsUnpack: false,
    setRecycle: false, // 商品回收站按钮显示与隐藏
    isAliPay: false, // 支付宝支付页面
    isGoodsSort: false, // 商品自定义排序页面
    isPartlyBack: false, // 部分退货页面
    isRepositoryNotice: false, // 抖音视频号引流
    vipId: '',
    showSizeColor: 'close',
    showMemberExchange: false,
    memberIndex: 'first',
    selectName: '',
    settingIndex: 'tab0',
    employeeAuth: [
      'create_products',
      'import_products',
      'products_curstock_inventories',
      'create_vips',
      'edit_vips',
      'cancel_vips',
      'exchange_vip_points',
      'recharge_give_money',
      'modify_sale_price',
      'use_cashier_discount',
      'cashier_direct',
      'purchase',
      'return_purchase',
      'supplier_management',
      'create_employee',
      'edit_employee',
      'show_shift_records',
      'use_settings',
      'show_details',
      'home_price',
      'delete_products',
      'recharge_give_money_revoke'
    ],
    partitionId: '',
    sizeColorDetail: {},
    tabIndexOnceCard: 1, // 次卡报表tap页
    tabIndexShort: 1, // 短信发送记录tap页
    // 设置页-会员设置-弹框
    pcSmgFrom: '',
    newMajorCode: '', // 收银台新增完商品，需要知道保存成功后的货号
    // 打开结算页面
    finalPayParam: {
      // 从哪个页面来，现在有结算pay和会员member_recharge
      from: '',
      buy_back: '',
      pc_return_goods: '',
      showFinalPrice: '',
      showMember: '',
      acctsId: '',
      ifautoCash: '',
      aid: '',
      ifpay: '',
      member_money: '',
      member_password: ''
    },
    completePay: false,
    // 副屏欢迎光临、结算列表
    // showHygl: true,
    waitReloadAd: false,
    showPage: 1,
    showKefu: false,
    //  页面中使用
    showAddGoods: false,
    refleshGoodsList: false,
    showAddEmployee: 'close',
    showInputRecharge: '',
    detailBackPay: false, // 明细页面退款是否完成
    eyeClose: false,
    member_detail: {
      name: '',
      mobile: '',
      password: '',
      disc: '',
      has_money: '',
      birthday: '',
      addr: '',
      remark: '',
      pay_type: 1
    },
    showMemberExchangeId: '',
    showMemberExchangeName: '',
    showMemberExchangeIntegral: '',
    //  goods页面全局变量,初始化是true,显示大表格
    // 点击库存盘点变为false,显示小表格
    goodsHomeStock: true,
    // pc明细页，默认头部三个tab选中第一个
    pc_detail_tab: 1,
    // 判断从哪里点进明细页，0:home 1:报表
    fromReport: 0,
    // 判断从哪里点进进货明细页，0:进货 1:报表
    fromDetail: 0,
    // 退货完成返回收银台弹窗
    showBackCompleteDialog: false,
    pcLockScreenConfirm: false, // 锁屏前确认
    pcLockScreen: false,
    //  收银台-退货功能
    pc_return_goods: false,
    showSelManage: false,
    // 打开管理分类界面
    showTypeManage: false,
    goodsCurTypeFingerprint: '',
    // 管理分类
    // selManageType: '',
    selManageTypeName: '',
    showCategoryManage: false,
    // 传秤设置
    showScalesManage: false,
    scannerChangeStart: false, // 秤相关数据加载完成后开始监听
    curScaleChanged: false, // 当前秤是否修改过
    saveScaleConfirm: false, // 传秤管理切换与退出前确认窗口
    // 一品多码
    //  打开管理品牌界面
    showBrandManage: false,
    // 是否修改了分类
    isChangeCategory: false,
    //  打开管理单位界面
    showUnitManage: false,
    //  点击分类类别是否选中到新增商品
    agreeChooseCategory: false,
    //  打开新增商品时默认类别为其他类别
    addGoodsCategory: '其他分类',
    addGoodsCategoryId: '1',
    addGoodsCategoryfingerprint: '97663c441d35c9b9674e57356b33c407',
    addGoodsCategoryFrom: '',
    //  打开新增商品时默认单位为空
    addGoodsUnit: '',
    addGoodsUnitId: '',
    addGoodsUnitfingerprint: '',
    // 打开新增商品时默认品牌为空
    addGoodsBrandId: '',
    addGoodsBrand: '',
    //  准备编辑商品时,
    goodsDetail: [],
    //  编辑商品成功时,返回修改后的数据进行渲染
    returnGoodsDetail: [],
    // 云端数据通过其他端末已删除,显示弹窗跳到登陆页面
    delReLogin: false,
    // token异常调出重新登录页面
    tokenReLogin: false,
    // ===
    // 新增&修改供应商
    suppliersDetail: [],
    returnSuppliersDetail: [],
    // 选择的供应商详情
    selectedSuppliersDetail: {},
    // 是否从库存盘点页面返回商品管理页面
    toGoodsManageFmInven: false,
    // 库存盘点页面退出时为保存商品列表长度
    pddGoodListLength: 0,
    // 有缓存盘点单数据时返回
    confirmInvToGood: false,
    // 批量修改商品flag
    batchUpdateGoods: '',
    // 通过批量修改商品类别显示类别列表
    batchUpdateGoodsType: false,
    // 批量修改商品类别完成
    batchUpdateGoodsTypeId: '',
    // 批量修改商品类别fingerprint
    batchUpdateGoodsTypeFingerprint: '',
    // 通过批量修改商品单位显示单位列表
    batchUpdateGoodsUnit: false,
    // 批量修改商品单位完成
    batchUpdateGoodsUnitId: '',
    batchUpdateGoodsUnitFingerprint: '',
    batchUpdateGoodsBrand: false,
    batchUpdateGoodsBrandId: '',
    // 通过检索未入库商品进入新增商品页面
    midGoodsName: '',
    // 商品管理页面通过扫码枪扫描未入库商品进入新增商品页面
    scanGoodsCode: '',
    // 显示删除店铺数据
    showDelStoreData: false,
    // 清空全部数据
    showDelAllData: false,
    // 是否显示发送清空验证码
    isResend: true,
    // 支付页面是否包含组合支付
    isContentCombinedPay: false,
    // 版本对比弹窗
    isVesionCompare: false,
    onlyShowBuyChoose: false,
    showTemporaryTip: false,
    showSigningDialog: false,
    msSignType: 'ps',
    // 激活码近期最后忽略的日期 (yyyy-MM-dd)
    lastTemporaryIgnoreDate: '',
    // 版本功能列表
    verFunctionList: [
      { label: '商品基础进销存', level: '0' },
      { label: '会员管理功能', level: '1' },
      { label: '店铺经营分析报表', level: '1' },
      { label: '员工管理体系', level: '1' },
      { label: '交接班查账', level: '1' },
      { label: '组合支付', level: '1' },
      { label: '商品大小包拆包', level: '1' },
      { label: '微信消息推送', level: '2' },
      { label: '自定义打印模板', level: '2' },
      { label: '会员商品寄存', level: '2' }
    ],
    shortCutList: [
      { type: 'group', label: '极速结算快捷键' },
      { keyChar: ['Alt', 'X'], label: '现金支付快速结算' },
      { keyChar: ['Alt', 'W'], label: '微信支付快速结算' },
      { keyChar: ['Alt', 'Z'], label: '支付宝支付快速结算' },
      { keyChar: ['Alt', 'P'], label: 'POS支付快速结算' },
      { keyChar: ['Shift', 'Space'], label: '组合支付快速结算' },
      { keyChar: ['Space'], necessary: true, label: '结算' },
      { type: 'group', label: '购物列表快捷键' },
      { keyChar: ['Ctrl', 'Tab'], label: '收银/退货(整单/组合支付)' },
      { keyChar: ['↑'], label: '购物列表向上选中商品' },
      { keyChar: ['↓'], label: '购物列表向下选中商品' },
      { keyChar: ['Ctrl', '←'], label: '选中商品数量减少' },
      { keyChar: ['Ctrl', '→'], label: '选中商品数量增加' },
      { keyChar: ['Ctrl', 'Delete'], label: '删除当前选中商品' },
      { keyChar: ['Ctrl', 'L'], label: '显示上一单' },
      { keyChar: ['Enter'], label: '显示上一单中打小票' },
      { keyChar: ['Ctrl', 'Q'], label: '收银台清空列表' },
      { keyChar: ['Ctrl', 'B'], label: '收银台添加备注' },
      { keyChar: ['Ctrl', 'Y'], label: '修改应收' },
      // { keyChar: ['Ctrl', 'G', 'S'], label: '修改单品折扣/保存' },
      { type: 'group', label: '基础快捷键' },
      { keyChar: ['F1'], label: '快捷键说明' },
      { keyChar: ['F2'], label: '取单' },
      { keyChar: ['F3'], label: '挂单' },
      { keyChar: ['F4'], label: '选择会员' },
      { keyChar: ['F5'], label: '优惠折扣' },
      { keyChar: ['F6'], label: '手选商品' },
      { keyChar: ['F8'], label: '直接收款' },
      { type: 'group', label: '系统快捷键' },
      { keyChar: ['F9'], label: '配件设置' },
      { keyChar: ['F10'], label: '销售单' },
      { keyChar: ['F11'], label: '开钱箱' },
      { keyChar: ['F12'], label: '锁屏' },
      { keyChar: ['Esc'], label: '返回' }
    ],
    showShortCutList: false,
    trialDay: '',
    // 从进货页面进入新增商品时禁用更改库存数量
    addProdFromStock: false,
    // 称重单位名列表
    weighUnitList: ['斤', '克', '两', '千克', '公斤', 'g', 'G', 'kg', 'Kg', 'kG', 'KG'],
    // 扫码枪扫描的条码
    scanerObj: '',
    // zgzn窗体是否被激活
    zgznActive: true,
    // 是否显示上一单
    showLastOrder: false,
    // 上一单data
    lastOrderDetail: [],
    // 会员Esc监听
    memberShortCutExit: false,
    // 群客多固定签名
    qkdSign: 'JH1RNyEVFX7UYznBbXu7rNwq5IEtVD',
    // 群客多MQTT Content
    qkdMqttInfo: {},
    // 掌柜参谋商家助手Mqtt Response
    zgcmMqttInfo: {},
    autoLogin: false,
    showAccountLogin: false,
    // ===
    commonSign: '2CE6027DE518F78ACB4A49717C6E1C4B',
    //  登陆信息
    loginInfo: [],
    //  登录时请求的表头店铺名称
    username: '',
    // 店主姓名
    shopKeepName: '',
    //  收银台页面，是否退出收银台/是否到退货、进货
    showPayChangeStatus1: false,
    showPayChangeStatus2: false,
    showPayExitMsg: false,
    showGoodsSort: false,
    showDetail: false,
    // 收银台左侧列表数量
    payListLength: 0,

    // 明细页面，右下角信息在退货时储存
    returnGoodsMsg: '',
    // 收银台结算时的备注
    payRemark: '',

    // 进货左侧列表数量
    stockListLength: 0,
    // 是否弹出库存页确认跳转的弹框
    stockDelList: false,
    // 切换明细前是否要清空进货/退货列表
    stockDelList2: false,
    // 回到主页前是否要清空进货/退货列表
    stockDelList3: false,
    // 点击数据同步后，主页刷新
    homeF5: false,
    password: '',
    registerPassword: '',
    // 如果是进货界面没有商品，储存并准备在新增商品中新增
    stockCode: '',
    // 新增完商品后，存到全局中
    addStockGoods: [],
    // 进货单/退货单页面选进货还是退货
    buyOrder: true,
    showRepeatChoose: false,

    // 设置页面的小票打印机、标价签打印机、收钱箱COM端口号
    setting_small_printer: '', // 'POS58 v2.0'
    setting_label_printer: '',
    setting_tip_printer: '',
    setting_tag_printer: '',
    setting_moneybox: '', // 'COM3'
    setting_hasmoneybox: false,
    hasWeigh: false, // 是否启用电子秤
    weighShowInPay: true, // 收银台显示电子秤重量
    weighValue: '', // 电子秤端口
    weighTypeValue: '', // 电子秤类型
    weighList: [], // 电子秤端口list
    weighTypeList: [], // 电子秤类型list
    weighStatus: '·未连接', // 电子秤状态
    weighSet: 0, // 设置页面电子秤重量
    showKexian: false, // 是否启用客显
    kexianValue: '', // 客显端口
    kexianList: [], // 客显端口list
    permit_print: false, // 是否允许打印机打印小票
    permit_card_print: false, // 是否允许打印机打印次卡小票
    permitPointExchangePrint: false, // 积分兑换是否打印小票
    print_cols: '58', // 小票打印规格
    print_cols_label: '60', // 条码纸规格
    tagPrintCols: '60', // 吊牌纸规格
    screen2ShowList: '', // 副屏商品列表
    isOnceOpen: true, // 是否是第一次打开称重商品
    isConnectWeight: false, // 是否成功连接电子秤

    // 优惠金额
    screen2ReducePrice: '',
    // 总金额
    screen2TotalPrice: '',
    // 显示实收
    screen2ReceiveMoney: '',
    // 主屏收银是否是退货状态
    screen2ReturnGoods: false,
    // 显示应收金额
    screen2ShowFinalPrice: '',
    // 会员余额
    screen2ShowMember: false,
    screen2MemberMoney: 0,
    // 是否会员价 1：原价 2：会员价
    screen2MemberPayType: '',
    // 关注公众号，副屏url显示
    screen2ShowFollowErweima: '',
    // 副屏url
    screen2FollowErweima: '',
    screen2ErweimaMobile: '',
    screen2ErweimaName: '',
    // 准备切换新支付方式
    // screen2_new_paystyle: false,
    // 注册用
    sys_uid: '',
    sys_sid: '',
    token: '',
    ultimate: false,
    employeeDetail: {},
    settlementReducePrice: '',
    storeList: [],
    // 判断打开app到登录页，还是退出到登录页
    ifLogin: true,
    isSyncing: false, // 是否正在执行云同步，如果是（true），则5分钟自动云同步停止
    ifautoCash: false, // 是否自助收银
    autoCashShow: false, // 是否：点击解除自助收银，弹出密码框
    aid: '', // XorPay
    app_secret: '', // XorPay
    sysId: '',
    phone: '', // 登陆手机号
    isSyncingLogin: false, // 是否是登录成功后的首次同期
    devicecode: '',
    // 快捷键结算
    quickPayType: '',
    // 广告设置
    show_edit_ad: false,
    ad_info: {},
    set_show_ad: false,
    isadedit: false,
    ad_list: [],
    reloadAdBeforeDestroy: false,
    goodsNameLength: 60,
    showAddMember: 'close',
    isVipDay: false, // 是否是会员日
    // 收银员名字
    // cashier: ''
    isOpenSMS: false,
    delayedTime: 400, // 搜索延时请求时间
    syncInterval: 1800000, // 云同步间隔时间ms
    cardNo: '', // 会员卡号
    useCardReader: 0, // 读卡设备开关(默认：关)
    remark_content: '谢谢惠顾，欢迎下次光临！',
    show_loading: false,
    network: true, // 网络连接状态
    clickInterval: 3000, // 点击间隔，防止二重点击3s
    uid: 0, // 店主uid
    fingerprint: '', // 交接班退出时更新履历唯一标识
    screenWidth: '',
    screenHeight: '',
    codeTimer: 300,
    showNovice: false, // 新手引导页是否打开标识符
    suggestPrice: '', // 售价
    loginTime: '', // 最近一次登陆时间
    endDate: '', // 退出时间
    changeUid: '', // 交接班补班id
    isRepair: false, // 是否是补班
    isNewChange: false, // 是否为新增交接班记录
    changeId: '', // 操作的交接班记录id
    nowChangeId: '', // 当前交接班记录id
    changeFrom: 'home', // 判断是从哪个页面进入交接班页面的
    changeFingerprint: '', // 交接班补班唯一标识
    changeNumber: '', // 交接班登陆人工号
    changeName: '', // 交接班登陆人姓名
    showTipsDialog: false, // 退出弹窗控制
    showExistGoods: false, // 重复商品编辑页面弹出框标识
    existTableData: [], // 重复商品data
    nameExist: false, // 重复商品名check
    codeExist: false, // 重复条码check
    sysEndDate: '', // 软件到期时间
    period: -1,
    msgFrom: 0, // 从哪个页面进入短信页面
    selectRow: 1,
    showCustomerService: false, // 联系客服弹窗
    smallPrinterCopies: 1, // 小票默认打印份数
    labelPrinterCopies: 1, // 条码默认打印份数
    tipPrinterCopies: 1, // 标价签默认打印份数
    labelPrintMap: {
      '64': { width: 60, height: 40 },
      '60': { width: 40, height: 60 },
      '40': { width: 40, height: 30 }
    },
    memberInfoArr: [
      { key: 'memberName', title: '会员名', flag: true },
      { key: 'memberPhone', title: '会员手机号', flag: true },
      { key: 'memberMoney', title: '会员卡余额', flag: true },
      { key: 'memberPoints', title: '会员积分', flag: true }
    ], // 会员信息开关设置
    logAndCode: [
      {
        key: 'logo',
        title: '打印店铺logo',
        flag: false,
        url: 'https://www.zgpos.com/local/logo/logo.png',
        hasImg: true,
        remark: '（建议图片尺寸300*100像素）'
      },
      {
        key: 'code',
        title: '打印商家二维码',
        flag: false,
        url: 'https://www.zgpos.com/local/logo/qrcode.png',
        hasImg: true,
        remark: '（建议图片尺寸400*400像素）'
      }
    ], // 二维码跟logo开关设置
    labelItem: [
      { key: 'commodityName', title: '商品名称', flag: true },
      { key: 'commodityPrice', title: '商品售价', flag: true },
      { key: 'commodityLabel', title: '商品条码', flag: true },
      { key: 'commodityDate', title: '生产日期', flag: false },
      { key: 'commodityEndDate', title: '保质期', flag: false }
    ], // 条码需要打印的信息
    tagModels: [
      {
        key: 'model1',
        name: '模板1',
        flag: true,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      },
      {
        key: 'model2',
        name: '模板2',
        flag: false,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      },
      {
        key: 'model3',
        name: '模板3',
        flag: false,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      },
      {
        key: 'model4',
        name: '模板4',
        flag: false,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      },
      {
        key: 'model5',
        name: '模板5',
        flag: false,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      },
      {
        key: 'model6',
        name: '模板6',
        flag: false,
        subModel: [
          { key: 'brand', title: '品牌', flag: true, value: brandName, label: '', size: 10, titleKey: '品牌' },
          { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
          { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
          { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
          { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
          { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
          { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
          { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
          { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
          { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
          { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
        ]
      }
    ],
    font_value: `"仿宋, 12pt, style=Bold"`,
    line_height: '60',
    sale_price_fontsize: '12',
    vip_price_fontsize: '16',
    setTipDefault: false, // 标价签默认打印份数开关
    setLabelDefault: false, // 条码默认打印份数开关
    adsList: [],
    settingFrom: 'home',
    showUpgrade: false,
    printVipPrice: true,
    voiceOff: true, // 语音播报开关
    soundVolume: 1, // 音量
    scanCodeSound: true, // 扫码提示音
    showHotGood: true, // 是否显示热销分类
    defaultAcctsId: '', // 收银台默认支付方式, '':未选择  1: 现金支付  7: 线下支付  6: 扫码支付
    isSupplierGlobal: null, // 进货供应商
    isAverageGlobal: null, // 进货平均进价
    defaultSettlementKey: 'Enter', // 默认结算按键
    showPayPrint: false,
    noticeList: [], // 首页广告
    estimatedProfit: 'day',
    showChooseWeight: false,
    printMode: 1,
    tagIsTransverse: true, // 打印预览
    codePrintValue: 'code', // 打印条码
    isAuto: false,
    tokenExpired: false,
    tokenExpiredMsg: 'Token 失效',
    chooseList: [],
    isShortMessageMass: false, // 短信群发页面
    isImportVip: false, // 会员导入页面
    shortTotal: 0, // 剩余短信条数
    chooseLists: [], // 会员导入选中数据
    shortInfo: '', // 短信群发内容
    shortPhones: '', // 短信群发手机号
    shortNum: 0,
    isHasEndDate: false,
    changeShiftRemark: '',
    clickFrom: 'vip',
    showDetailDialog: false,
    showTableDialog: false,
    isFirst: true,
    showCustomerServe: true,
    showSupplierDialgEdit: false, // 供应商选择是否编辑框
    supplierGoodsList: [] // 供应商商品列表
  },
  mutations: {
    [SET_SHOW](state, store) {
      try {
        Object.assign(state, store);
      } catch (error) {
        CefSharp.PostMessage(`mutations方法报错,store为${JSON.stringify(store)},报错为${JSON.stringify(error)}`);
      }
    }
  },
  actions: {
    [SET_SHOW]({ commit }, store) {
      try {
        commit(SET_SHOW, store);
      } catch (error) {
        CefSharp.PostMessage(`commit方法报错,store为${JSON.stringify(store)},报错为${JSON.stringify(error)}`);
      }
    }
  }
};
