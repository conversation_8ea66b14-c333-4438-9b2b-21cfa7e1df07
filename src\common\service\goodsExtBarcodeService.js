import dao from '../dao/dao';

const goodsExtBarcodeService = {
  /**
   * 商品扩展条码信息
   * @param {*} goods
   * @returns
   */
  addExtBarcodeAttribute: async function (goods, onSuccess) {
    // 商品fingerprint
    var goodsFingerprintArray = [];
    goods.forEach(item => {
      goodsFingerprintArray.push(item.fingerprint);
    });
    var param = "'" + goodsFingerprintArray.join("','") + "'";
    if (goodsFingerprintArray.length > 0) {
      // 根据商品fingerprint把商品扩展信息抽出
      const res = await dao.asyncExec(sqlApi.goodsExtBarcodeSearchByGoodsFingerprint.format(param));
      goods.forEach(item => {
        item.isCodesGoods = 0;
        var extBarcodes = [];
        res.forEach(ext => {
          if (ext.goodsFingerprint == item.fingerprint) {
            extBarcodes.push(ext);
          }
        });
        item.extendCodes = extBarcodes;
        if (extBarcodes.length > 0) {
          item.isCodesGoods = 1;
        }
      });
    }
    onSuccess(goods);
  },
  /**
   * 保存商品扩展码表数据
   * @param {*} goodsParams
   * @param {*} onSuccess
   * @param {*} onFail
  */
  insert: function (goodsParams, onSuccess, onFail) {
    var goods = goodsParams.goods[0];
    var goodsFingerprint = goods.fingerprint;
    var isCodesGoods = goods.isCodesGoods;
    var extendCodes = goods.extendCodes;
    if (demo.isNullOrTrimEmpty(isCodesGoods) || isCodesGoods != 1) {
      onSuccess();
      return;
    }
    var sql = this.createGoodsExtBarcodeInsertSql(goodsFingerprint, extendCodes);
    if (sql === '') {
      onSuccess();
    } else {
      dao.transaction(sql, onSuccess, onFail);
    }
  },
  /**
   * 生成保存商品扩展码表sql
   * @param {*} goodsFingerprint
   * @param {*} extendCodes
   * @returns
   */
  createGoodsExtBarcodeInsertSql: function (goodsFingerprint, extendCodes) {
    var sql = '';
    if (demo.isNullOrTrimEmpty(goodsFingerprint) || extendCodes.length <= 0) {
      return sql;
    }
    // 新增的扩展码
    let insertExtendCodes = _.filter(extendCodes, item => {
      return item.action === 'insert';
    });
    if (insertExtendCodes) {
      var goodsExtBarcodeInsert = sqlApi.goodsExtBarcodeInsert + sqlApi.goodsExtBarcodeInsertValues + ';';
      _.forEach(insertExtendCodes, item => {
        if (demo.isNullOrTrimEmpty(item.goodsFingerprint)) {
          item.goodFingerprint = goodsFingerprint;
        }
        item.fingerprint = commonService.guid();
        sql += goodsExtBarcodeInsert.format(item);
      });
    }
    return sql;
  },
  /**
   * 商品扩展条码编辑
   * @param {*} goods
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  updateGoodsExtBarcode(goods, onSuccess, onFail) {
    // 新增为一品一码
    var items = goods.items[0];
    var goodsFingerprint = goods.fingerprint;
    var extendCodes = items.extendCodes;

    var sql = '';
    // 删除的扩展码
    let deleteExtendCodes = _.filter(extendCodes, item => {
      return item.action === 'delete';
    });

    // 删除扩展条码sql
    if (deleteExtendCodes) {
      deleteExtendCodes.forEach(item => {
        sql += sqlApi.goodsExtBarcodeUpdateIsDeleted.format(item);
      });
    }
    // 新增扩展条码sql
    sql += this.createGoodsExtBarcodeInsertSql(goodsFingerprint, extendCodes);
    if (sql === '') {
      onSuccess();
    } else {
      dao.transaction(sql, onSuccess, onFail);
    }
  }
};

window.goodsExtBarcodeService = goodsExtBarcodeService;
export default goodsExtBarcodeService;
