import dao from '../dao/dao';
import logList from "../../config/logList";
import stringUtils from '../stringUtils';

const inventoryService = {
  /**
   * <AUTHOR>
   * 取件时批量生成盘点单
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  batchPut: function (data, onSuccess, onFail) {
    let params = {
      uid: demo.$store.state.show.loginInfo.uid,
      remark: data.remark || '',
      is_deleted: data.is_deleted || '0',
      is_synced: data.is_synced || '0',
      in_out: data.is_synced || 1,
      dealType: data.dealType
    };
    dao.exec(sqlApi.goodExistById.format(data.good_fingerprint),
      goodsList => {
        if (goodsList.length > 0) {
          orderService.get({
            'type': 'PDD'
          }, res => {
            params.code = demo.t2json(res)[0].code;
            params.inventoryItems = data.goods;
            params.updateStock = data.updateStock;
            this.insert(params, onSuccess, onFail);
          }, onFail);
        } else {
          demo.$toast('商品不存在');
        }
      }, onFail
    );
  },

  /**
   * 盘点
   * params.updateStock=1：更新库存
   * params.updatePurPrice=1：更新进价
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  insert: async function (params, onSuccess, onFail) {
    console.log(params, 'paramsparams');
    let itemsLength = params.inventoryItems.length;
    if (itemsLength === 0) {
      onFail('盘点明细不能为空');
      return;
    }

    params.uid = demo.$store.state.show.loginInfo.uid;
    params.accountQty = 0;
    params.actualQty = 0;
    params.diffQty = 0;
    params.diffAmt = 0;
    params.inOut = params.inOut || 1;
    params.remark = params.remark || null;
    if (demo.isNullOrTrimEmpty(params.code)) {
      let code = await orderService.syncGet({type: 'PDD'});
      params.code = code[0].code;
    }
    var listLog = [];
    listLog[0] = params.code;
    let obj = _.cloneDeep(logList.inventories);
    obj.description = obj.description.format(listLog);
    demo.actionLog(obj);
    params.fingerprint = commonService.guid();

    var batchSize = 50;
    let inventoryItemsInsert = sqlApi.inventoryItemsInsert;
    let updateCurStockSql = '';
    let updatePurPriceSql = '';
    _.forEach(params.inventoryItems, (item, index) => {
      item.inOut = params.inOut;
      item.diffQty = item.actualQty - item.accountQty;
      item.diffAmt = Number(item.price * item.diffQty).toFixed(2);
      item.remark = params.remark;
      item.inventoryFingerprint = params.fingerprint;
      item.fingerprint = commonService.guid();

      params.accountQty += +item.accountQty;
      params.actualQty += +item.actualQty;
      params.diffQty += +item.diffQty;
      params.diffAmt += +item.diffAmt;
      inventoryItemsInsert += sqlApi.inventoryItemsInsertValues.format(demo.sqlConversion(item));
      let index1 = index + 1;
      if (index1 === itemsLength) {
        inventoryItemsInsert += ';';
      } else if (index1 % batchSize === 0) {
        inventoryItemsInsert += ';' + sqlApi.inventoryItemsInsert;
      } else {
        inventoryItemsInsert += ',';
      }

      let params1 = {};
      params1.curStock = item.actualQty;
      params1.purPrice = item.price;
      params1.fingerprint = item.goodFingerprint;
      let params2 = demo.sqlConversion(params1);
      updateCurStockSql += sqlApi.updateCurStockByFingerprint.format(params2);
      updatePurPriceSql += sqlApi.updatePurPriceByFingerprint.format(params2);
    });
    let sql = sqlApi.inventoriesInsert + sqlApi.inventoriesInsertValues.format(demo.sqlConversion(params)) + ';' + inventoryItemsInsert;
    if (+params.updateStock === 1) {
      sql += updateCurStockSql;
    }
    if (+params.updatePurPrice === 1) {
      sql += updatePurPriceSql;
    }
    if (+params.exec === 0) {
      onSuccess(sql);
    } else {
      dao.transaction(sql, onSuccess, onFail);
    }
  },

  /**
   * 兑换商品后更新库存
   * var params = {
   *    autoPacking: 0,
   *    items: [{
   *        goods_id: 1,
   *        fingerprint: '612d738f5ee9090b3b22a4490f68aab3',
   *        cur_stock: 5,
   *        pick_up_num: -6,
   *        pur_price: 1
   *    }]
   * }
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateStockAfterExchangeGood: function (params, onSuccess, onFail) {
    this.getInventoryCode(res => {
      params.uid = demo.$store.state.show.loginInfo.uid;
      params.code = res;
      params.updateStock = 1;
      params.updatePurPrice = 0;
      params.dealType = 5;
      params.inventoryItems = params.items;

      var good = params.items[0];
      good.accountQty = good.cur_stock;
      good.actualQty = +good.cur_stock + +good.pick_up_num;
      good.goodFingerprint = good.fingerprint;
      good.price = good.purPrice;

      var goodParam = {};
      goodParam.id = good.goods_id;
      goodParam.qty = -good.pick_up_num;

      dao.exec(sqlApi.getGoodsAfterUpdateStockWheres.format(goodParam), res1 => {
        var notEnoughGoods = demo.t2json(res1);
        if (notEnoughGoods.length === 0 || +params.autoPacking === 0) {
          this.insert(params, onSuccess, onFail);
          return;
        }

        var notEnoughGood = notEnoughGoods[0];
        var packing = notEnoughGood.packing;
        if (demo.isNullOrTrimEmpty(packing) || demo.isNullOrTrimEmpty(demo.t2json(packing).parent)) {
          this.insert(params, onSuccess, onFail);
          return;
        }

        var parent = demo.t2json(packing).parent;
        if (+parent.count > 0 && !demo.isNullOrTrimEmpty(parent.fingerprint)) {
          this.packingGoods(params, notEnoughGood, parent, onSuccess, onFail);
        } else {
          this.insert(params, onSuccess, onFail);
        }
      }, onFail);
    }, onFail);
  },

  packingGoods: function(params, notEnoughGood, parent, onSuccess, onFail) {
    dao.exec(sqlApi.getParentGoodsWithoutSelf.format('\'' + parent.fingerprint + '\'', notEnoughGood.id), res2 => {
      var parentGoods = demo.t2json(res2);
      if (parentGoods.length === 0) {
        this.insert(params, onSuccess, onFail);
        return;
      }

      var parentGood = parentGoods[0];
      var packingCount = Math.abs(notEnoughGood.leftStock);
      if (packingCount > +notEnoughGood.qty) {
        packingCount = +notEnoughGood.qty;
      }
      var parentGoodCounts = Math.ceil(packingCount / parent.count);
      if (parentGoodCounts > +parentGood.curStock) {
        parentGoodCounts = +parentGood.curStock;
      }
      if (parentGoodCounts <= 0) {
        this.insert(params, onSuccess, onFail);
        return;
      }

      var pGood = {};
      pGood.goods_id = parentGood.id;
      pGood.fingerprint = parentGood.fingerprint;
      pGood.goodFingerprint = parentGood.fingerprint;
      pGood.cur_stock = parentGood.curStock;
      pGood.pick_up_num = -parentGoodCounts;
      pGood.purPrice = parentGood.purPrice;
      pGood.price = parentGood.purPrice;
      pGood.accountQty = pGood.cur_stock;
      pGood.actualQty = pGood.cur_stock - parentGoodCounts;

      params.inventoryItems.push(pGood);
      params.inventoryItems[0].actualQty += parentGoodCounts * parent.count;
      params.dealType = 8;
      this.insert(params, onSuccess, onFail);
    });
  },

  // 获取盘点单号
  getInventoryCode: function (onSuccess, onFail) {
    orderService.get({
      'type': 'PDD'
    }, res => {
      onSuccess(res[0].code);
    }, onFail);
  },
  getSearchWheresTotal: function (data) {
    var wheresTotal = '';
    if (data.hasOwnProperty('from')) {
      wheresTotal += "and inventories.opt_date >= '" + data.from + "' ";
    }
    if (data.hasOwnProperty('to')) {
      wheresTotal += "and inventories.opt_date <= '" + data.to + "' ";
    }
    if (!demo.isNullOrTrimEmpty(data.keyword)) {
      data.keyword = data.keyword.replace(/'/g, '‘').replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      wheresTotal +=
        ` and (goods.name like '%${data.keyword}%' ESCAPE '/'
            or goods.pinyin like '%${data.keyword}%' ESCAPE '/'
            or goods.code like '%${data.keyword}%' ESCAPE '/'
            or goods_ext_barcode.ext_barcode like '%${data.keyword}%' ESCAPE '/'
            or inventories.remark like '%${data.keyword}%' ESCAPE '/'
            or goods.first_letters like '%${data.keyword}%' ESCAPE '/') `;
    }
    return wheresTotal;
  },
  // 盘点明细导出
  detailExport: function (data, onSuccess, onFail) {
    var wheresDate = this.getSearchWheresTotal(data);
    dao.exec(
      sqlApi.inventoriesGetToExport.format(wheresDate),
      onSuccess,
      onFail
    );
  },

  /**
   * 盘点明细查询
   * inventoryService.detailReports({"from":"2020-11-01", "to":"2020-11-31"}, res => {console.log(res);});
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  detailReports: function (data, onSuccess, onFail) {
    var that = this;
    var wheresTotal = that.getSearchWheresTotal(data);
    var inventoriesGetTotal, inventoriesGetDayTotal, inventoriesGetDetails;
    if (demo.isNullOrTrimEmpty(data.keyword)) {
      inventoriesGetTotal = sqlApi.inventoriesGetTotal;
      inventoriesGetDayTotal = sqlApi.inventoriesGetDayTotal;
      inventoriesGetDetails = sqlApi.inventoriesGetDetails;
    } else {
      inventoriesGetTotal = sqlApi.inventoriesGetTotalWithGoods;
      inventoriesGetDayTotal = sqlApi.inventoriesGetDayTotalWithGoods;
      inventoriesGetDetails = sqlApi.inventoriesGetDetailsWithGoods;
    }

    dao.exec(
      inventoriesGetTotal.format(wheresTotal),
      function (result) {
        var totalRes = demo.t2json(result)[0];
        dao.exec(
          inventoriesGetDayTotal.format(wheresTotal),
          function (result1) {
            var dayTotalRes = demo.t2json(result1);

            dao.exec(
              inventoriesGetDetails.format(wheresTotal),
              function (result2) {
                var dayDetailsRes = demo.t2json(result2);

                that.doForExec(dayTotalRes, dayDetailsRes);

                totalRes.days = dayTotalRes;
                onSuccess(totalRes);
              },
              onFail
            );
          },
          onFail
        );
      },
      onFail
    );
  },
  doForExec: function (day_total_res, day_details_res) {
    var j = 0;
    for (var i = 0; i < day_total_res.length; i++) {
      var day_total = day_total_res[i];
      day_total.details = [];

      for (j; j < day_details_res.length; j++) {
        var day_details = day_details_res[j];

        if (day_total.optDate === day_details.optDate) {
          day_total.details.push(day_details);
        } else {
          break;
        }
      }
    }
  },

  /**
   * 获取某条盘点明细的详细信息
   * inventoryService.getInventoryAndItemsById(1, res => {console.log(res);});
   * @param {*} id
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getInventoryAndItemsById: function (id, onSuccess, onFail) {
    var result = {};
    dao.exec(
      sqlApi.inventoriesGetById.format(id),
      function (res) {
        var res1 = demo.t2json(res);
        if (res1.length === 0) {
          onSuccess(result);
          return;
        }
        result.inventories = res1[0];
        dao.exec(
          sqlApi.inventoryItemsGetByPid.format(result.inventories.fingerprint),
          function (res2) {
            result.inventories.items = specsService.specStringToArray(res2);
            onSuccess(result);
          },
          onFail
        );
      },
      onFail
    );
  },

  /**
   * 库存变动明细
   * var params = {"type":1, "goodId":1, "dateFrom":"2020-11-01", "dateTo":"2020-11-31", "pageSize":1, "pageCount":10};
   * inventoryService.stockChangeReports(params, res => {console.log(res)});
   *
   * params.type: 0:全部变动、1:商品销售、2:客户退款、3:进货、4:退货、5:库存盘点、6:会员存取、7:兑换礼品
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  stockChangeReports: function async (params, onSuccess, onFail) {
    params.limit = '';
    params.wheres_sales = '';
    params.wheres_pur = '';
    params.wheres_inv = '';
    params.salesOrderBy = '';
    params.purOrderBy = '';
    params.invOrderBy = '';
    params.allOrderBy = '';

    if (!demo.isNullOrTrimEmpty(params.pageSize) && !demo.isNullOrTrimEmpty(params.pageCount)) {
      params.limit = `limit ` + params.pageCount + ` offset (` + params.pageSize + ` - 1) * ` + params.pageCount;
    }

    if (!demo.isNullOrTrimEmpty(params.dateFrom)) {
      params.wheres_sales += `and sales.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
      params.wheres_pur += `and pur.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
      params.wheres_inv += `and inv.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
    }
    if (!demo.isNullOrTrimEmpty(params.dateTo)) {
      params.wheres_sales += `and sales.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
      params.wheres_pur += `and pur.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
      params.wheres_inv += `and inv.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
    }

    // 创建时间和商品名称排序
    if (!demo.isNullOrTrimEmpty(params.column) && !demo.isNullOrTrimEmpty(params.order)) {
      let createAt = 'create_at';
      if (params.column === 'createAt') {
        params.salesOrderBy = 'order by sales.' + createAt + ' ' + params.order;
        params.purOrderBy = 'order by pur.' + createAt + ' ' + params.order;
        params.invOrderBy = 'order by inv.' + createAt + ' ' + params.order;
        params.allOrderBy = 'order by ' + createAt + ' ' + params.order;
      } else if (params.column === 'name') {
        let goodsNameOrderBy = 'order by goods.first_letters COLLATE NOCASE' + params.order;
        params.salesOrderBy = goodsNameOrderBy;
        params.purOrderBy = goodsNameOrderBy;
        params.invOrderBy = goodsNameOrderBy;
        params.allOrderBy = 'order by first_letters COLLATE NOCASE ' + params.order;
      }
    } else {
      // 默认排序
      params.salesOrderBy = 'order by sales.create_at desc';
      params.purOrderBy = 'order by pur.create_at desc';
      params.invOrderBy = 'order by inv.create_at desc';
      params.allOrderBy = 'order by create_at desc';
    }

    var result = {};
    if (stringUtils.isBlank(params.goodId)) {
      this.typeFunc(result, params, onSuccess, onFail);
    } else {
      dao.exec(sqlApi.getTotalInfoByGoodId.format(params), suc => {
        var sucObj = demo.t2json(suc);
        if (sucObj.length > 0) {
          result.totalInfo = sucObj[0];
        } else {
          result.totalInfo = {};
        }
        this.typeFunc(result, params, onSuccess, onFail);
      }, onFail);
    }
  },
  typeFunc(result, params, onSuccess, onFail) {
    var doFunc;
    if (+params.type === 1 || +params.type === 2) {
      // 1:商品销售、2:客户退款
      doFunc = this.getSaleItemsByGoodId;
    } else if (+params.type === 3 || +params.type === 4) {
      // 3:进货、4:退货
      doFunc = this.getPurItemsByGoodId;
    } else if (+params.type === 5 || +params.type === 6 || +params.type === 7 || +params.type === 8 || +params.type === 10) {
      // 5:库存盘点、6:会员存取、7:积分兑换商品、8:拆包、10：库存修改
      doFunc = this.getInventoryItemsByGoodId;
    } else {
      // 0:全部变动
      doFunc = this.getAllByGoodId;
    }
    doFunc(params, suc1 => {
      result.datas = demo.t2json(suc1);
      onSuccess(result);
    }, onFail);
  },
  /**
   * 1:商品销售、2:客户退款
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getSaleItemsByGoodId: async function (params, onSuccess, onFail) {
    params.wheres = 'and sales.in_out=' + params.type + ' ';

    if (!demo.isNullOrTrimEmpty(params.dateFrom)) {
      params.wheres += `and sales.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
    }
    if (!demo.isNullOrTrimEmpty(params.dateTo)) {
      params.wheres += `and sales.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
    }
    let oneGood = !stringUtils.isBlank(params.goodId);
    if (oneGood) {
      dao.exec(sqlApi.getSaleItemsByGoodId.format(params), onSuccess, onFail);
    } else {
      let data = await dao.asyncExec(sqlApi.getSaleItemsByType.format(params), onSuccess, onFail);
      let count = await dao.asyncExec(sqlApi.getSaleItemsCountByType.format(params), onSuccess, onFail);
      const res = {
        count: demo.t2json(count)[0]['cnt'],
        data: demo.t2json(data)
      };
      onSuccess(res);
    }
  },
  /**
   * 3:进货、4:退货
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getPurItemsByGoodId: async function (params, onSuccess, onFail) {
    params.wheres = 'and pur.in_out=' + (params.type === 3 ? 1 : 2) + ' ';

    if (!demo.isNullOrTrimEmpty(params.dateFrom)) {
      params.wheres += `and pur.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
    }
    if (!demo.isNullOrTrimEmpty(params.dateTo)) {
      params.wheres += `and pur.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
    }
    let oneGood = !stringUtils.isBlank(params.goodId);
    if (oneGood) {
      dao.exec(sqlApi.getPurItemsByGoodId.format(params), onSuccess, onFail);
    } else {
      let data = await dao.asyncExec(sqlApi.getPurItemsByType.format(params), onSuccess, onFail);
      let count = await dao.asyncExec(sqlApi.getPurItemsCountByType.format(params), onSuccess, onFail);
      const res = {
        count: demo.t2json(count)[0]['cnt'],
        data: demo.t2json(data)
      };
      onSuccess(res);
    }
  },
  /**
   * inventories表中deal_type字段 1新增商品设定库存，2编辑商品修改库存，3寄存，4取件，5兑换礼品，6批量导入设定库存，7盘点，8拆包，9批量导入抵消
   * 5:库存盘点、6:会员存取、7:兑换礼品、8:拆包
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getInventoryItemsByGoodId: async function (params, onSuccess, onFail) {
    params.wheres = 'and inv.deal_type in ({0}) ';
    if (params.type === 5) {
      // 查询盘点变动(包含新增、批量导入、盘点、批量抵消)
      params.wheres = params.wheres.format('1,6,7,9');
    } else if (params.type === 6) {
      // 查询会员存取(包含会员寄件、取件)
      params.wheres = params.wheres.format('3,4');
    } else if (params.type === 8) {
      // 查询拆包
      params.wheres = params.wheres.format('8');
    } else if (params.type === 10) {
      // 查询库存变动
      params.wheres = params.wheres.format('2');
    } else {
      // 查询兑换礼品
      params.wheres = params.wheres.format('5');
    }

    if (!demo.isNullOrTrimEmpty(params.dateFrom)) {
      params.wheres += `and inv.opt_date >= date(replace('` + params.dateFrom + `', '/', '-')) `;
    }
    if (!demo.isNullOrTrimEmpty(params.dateTo)) {
      params.wheres += `and inv.opt_date <= date(replace('` + params.dateTo + `', '/', '-')) `;
    }
    let oneGood = !stringUtils.isBlank(params.goodId);
    if (oneGood) {
      dao.exec(sqlApi.getInventoryItemsByGoodId.format(params), onSuccess, onFail);
    } else {
      let data = await dao.asyncExec(sqlApi.getInventoryItemsByType.format(params), onSuccess, onFail);
      let count = await dao.asyncExec(sqlApi.getInventoryItemsCountByType.format(params), onSuccess, onFail);
      const res = {
        count: demo.t2json(count)[0]['cnt'],
        data: demo.t2json(data)
      };
      onSuccess(res);
    }
  },
  /**
   * 0:全部变动
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getAllByGoodId: async function (params, onSuccess, onFail) {
    let oneGood = !stringUtils.isBlank(params.goodId);
    if (oneGood) {
      dao.exec(sqlApi.getAllByGoodId.format(params), onSuccess, onFail);
    } else {
      let data = await dao.asyncExec(sqlApi.getAllByType.format(params), onSuccess, onFail);
      let count = await dao.asyncExec(sqlApi.getAllCountByType.format(params), onSuccess, onFail);
      const res = {
        count: demo.t2json(count)[0]['cnt'],
        data: demo.t2json(data)
      };
      onSuccess(res);
    }
  },

  /**
   * 库存变动明细--详情
   * var params = {"tableName":"sales", "id":1};
   * inventoryService.getInfoById(params, res => {console.log(res)});
   *
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getInfoById: function (params, onSuccess, onFail) {
    var doFunc;
    if (params.tableName === 'sales') {
      // 销售单详情
      doFunc = this.getSalesInfoById;
    } else if (params.tableName === 'purchases') {
      // 进货单详情
      doFunc = this.getPurchasesInfoById;
    } else if (params.tableName === 'inventories') {
      // 盘点单详情
      doFunc = this.getInventoriesInfoById;
    } else {
      onSuccess({});
      return;
    }

    doFunc(params.id, onSuccess, onFail);
  },

  /**
   * 销售单详情
   * @param {*} saleId
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getSalesInfoById: function (saleId, onSuccess, onFail) {
    dao.exec(sqlApi.getSalesInfoById.format(saleId), suc => {
      dao.exec(sqlApi.getSaleItemsInfoById.format(suc[0].fingerprint), suc1 => {
        var result = {};

        var sucObj = demo.t2json(suc);
        if (sucObj.length > 0) {
          result.sales = sucObj[0];
        } else {
          result.sales = {};
        }

        result.sale_items = demo.t2json(suc1);
        onSuccess(result);
      }, onFail);
    }, onFail);
  },
  /**
   * 进货单详情
   * @param {*} purId
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getPurchasesInfoById: function (purId, onSuccess, onFail) {
    dao.exec(sqlApi.getPurchasesInfoById.format(purId), suc => {
      dao.exec(sqlApi.getPurItemsInfoById.format(suc[0].fingerprint), suc1 => {
        var result = {};

        var sucObj = demo.t2json(suc);
        if (sucObj.length > 0) {
          result.pur = sucObj[0];
        } else {
          result.pur = {};
        }

        result.pur_items = demo.t2json(suc1);
        onSuccess(result);
      }, onFail);
    }, onFail);
  },
  /**
   * 盘点单详情
   * @param {*} invId
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getInventoriesInfoById: function (invId, onSuccess, onFail) {
    dao.exec(sqlApi.getInventoriesInfoById.format(invId), suc => {
      dao.exec(sqlApi.getInvItemsInfoById.format(suc[0].fingerprint), suc1 => {
        var result = {};

        var sucObj = demo.t2json(suc);
        if (sucObj.length > 0) {
          result.inv = sucObj[0];
        } else {
          result.inv = {};
        }

        result.inv_items = demo.t2json(suc1);
        onSuccess(result);
      }, onFail);
    }, onFail);
  },

  /**
   * 盘点单批量删除
   * inventoryService.deleteBatch({'id':'1,2,3'}, res=>{console.log(res)}, res=>{console.log(res)})
   * 批量启用: inventoryService.deleteBatch({'id':'1,2,3', 'isDel':0}, onSuccess, onFail)
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  deleteBatch: function(params, onSuccess, onFail) {
    if (params.isDel !== 0) {
      params.isDel = 1;
    }
    dao.transaction(sqlApi.inventoriesDeleteBatch.format(params), onSuccess, onFail);
  }
};
window.inventoryService = inventoryService;
export default inventoryService;
