<template>
<!-- 退货单详情 -->
  <div v-if="showReturnTitle" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="content">
      <div class="content-center">
        <div class="center-top">
          <div class="top-title">{{ supplierShow ? '关联退货单' : '关联销售单' }}</div>
          <div class="top-close" @click="close">×</div>
        </div>
        <div class="centet-table">
            <div class="table-content">
              <div class="order">
                <div class="order-title">
                  <div class="title-code">{{(supplierShow ? '退货单号：' : '销售单号：') + orderTime.code}}</div>
                  <div class="title-time">{{(supplierShow ? '退货时间：' : '销售时间：') + dateFormat(orderTime.createAt)}}</div>
                </div>
                <div class="order-print" @click="printList">打印</div>
            </div>
            <div class="order-table">
              <el-table
                :data="right_list"
                :row-class-name="tableRowClassName"
                :header-cell-style="{background:'#F5F8FB',color:'#537286'}"
                style="font-size: 14px;color: #567485;">
                  <el-table-column
                    width="60px"
                    type="index"
                    align="center"
                    label="序号">
                  </el-table-column>
                  <el-table-column
                    align="left"
                    label="品名/条码">
                    <template slot-scope="scope">
                      <div>
                        <el-popover
                          :disabled="isNullOrTrimEmpty(scope.row.goodName).toString().length < 30"
                          popper-class="pc_pay192 popper_self"
                          placement="top"
                          trigger="hover"
                          :content="scope.row.goodName">
                          <div slot="reference" class="list-right__content pc_pay199">{{ scope.row.goodName || '-' }}</div>
                        </el-popover>
                        <el-popover
                          :disabled="isNullOrTrimEmpty(scope.row.code).toString().length < 20"
                          popper-class="pc_pay192 popper_self"
                          placement="top"
                          trigger="hover"
                          :content="scope.row.code">
                          <div slot="reference" class="list-right__content pc_pay199" style="color: #8FA8BA">{{ scope.row.code || '-' }}</div>
                        </el-popover>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    align="right"
                    :label="supplierShow ? '退货数量' : '销售数量'">
                    <template slot-scope="scope">
                    {{ scope.row.qty || '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    align="right"
                    label="单价">
                    <template slot-scope="scope">
                    ￥{{ Number(scope.row.price).toFixed(2) || '0.00' }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="110px"
                    align="right"
                    label="折扣">
                    <template slot-scope="scope">
                      {{ (Number(scope.row.itemDisc) * 100).toFixed(0) }}%
                    </template>
                  </el-table-column>
                  <el-table-column
                    width="110px"
                    align="right"
                    label="小计">
                    <template slot-scope="scope">
                    ￥{{ Number(scope.row.amt).toFixed(2) || '0.00' }}
                    </template>
                  </el-table-column>
              </el-table>
            </div>
            <!-- 退货 -->
            <div class="order-bottom" v-if="supplierShow">
              <div>收银员：{{right_msg.uid_no}}</div>
              <div class="order-bottom__price">
                <div class="mode">{{'应退：¥ ' + Math.abs(Number(right_msg.discAmt)).toFixed(2)}}</div>
                <div class="remark">{{'实退：¥ ' + Math.abs(Number(right_msg.discAmt)).toFixed(2)}}</div>
                <div>单品利润：¥ {{Number(right_msg.discAmt - right_msg.sumPurPrice).toFixed(2)}}</div>
              </div>
              <div class="order-bottom__price">
                <div class="mode">结算方式：
                  <span v-if="combinedItem.length === 2">
                    {{combinedItem[0].accountName}}¥
                    {{+combinedItem[0].acctId == 1 ? Number(right_msg.changeAmt + combinedItem[0].payAmt).toFixed(2) : Number(combinedItem[0].payAmt).toFixed(2)}} +
                    {{combinedItem[1].accountName}}¥
                    {{+combinedItem[1].acctId == 1 ? Number(right_msg.changeAmt + combinedItem[1].payAmt).toFixed(2) : Number(combinedItem[1].payAmt).toFixed(2)}}
                  </span>
                  <span v-else>
                    <span>
                      {{((right_msg.accountId === 7 || right_msg.accountId === 8) &&
                        right_msg.inOut === 2) ?
                        getAccountName(right_msg.accountName) :
                        right_msg.accountName}}
                    </span>
                  </span></div>
                <div class="remark">备注：{{right_msg.remark || '无'}}</div>
              </div>
            </div>
            <div v-else>
              <!-- 进货 -->
              <div class="order-bottom">
                <div class="order-bottom__price">
                <div class="mode">收银员：{{right_msg.uid_no}}</div>
                <div class="remark" v-if="right_msg.vipname">会员：{{right_msg.vipname}}</div>
              </div>
              <div class="order-bottom__price">
                <div class="mode">{{'优惠：¥ ' + (Number(right_msg.billAmt) - Number(right_msg.discAmt)).toFixed(2) }}</div>
                <div class="remark">{{'应收：¥ ' + Math.abs(Number(right_msg.discAmt)).toFixed(2)}}</div>
                <div>单笔利润：¥ {{Number(right_msg.discAmt - right_msg.sumPurPrice).toFixed(2)}}</div>
              </div>
              <div class="order-bottom__price">
                <div class="mode">结算方式：
                  <span v-if="combinedItem.length === 2">
                    {{combinedItem[0].accountName}}¥
                    {{+combinedItem[0].acctId == 1 ? Number(right_msg.changeAmt + combinedItem[0].payAmt).toFixed(2) : Number(combinedItem[0].payAmt).toFixed(2)}} +
                    {{combinedItem[1].accountName}}¥
                    {{+combinedItem[1].acctId == 1 ? Number(right_msg.changeAmt + combinedItem[1].payAmt).toFixed(2) : Number(combinedItem[1].payAmt).toFixed(2)}}
                  </span>
                  <span v-else>
                    {{right_msg.accountName}}
                  </span></div>
                <div class="remark">{{'实收：¥ ' + Math.abs(Number(right_msg.discAmt)).toFixed(2)}}</div>
                <div>找零：¥ {{Number(right_msg.changeAmt).toFixed(2)}}</div>
              </div>
              <div class="remark">备注：{{right_msg.remark || '无'}}</div>
             </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { SET_SHOW } from '@/store/show';
import { mapState, mapActions } from 'vuex';
export default {
  name: 'ReturnTitle',
  data() {
    return {
      showReturnTitle: false, // 是否显示弹窗
      print_click: false,
      dataList: []
    }
  },
  created() {
  },
  mounted() {
  },
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    // 单号,时间
    orderTime: {
      type: Object,
      default: () => {}
    },
    // 销售单还是退货单
    supplierShow: {
      type: Boolean,
      default: false
    },
    right_list: {
      type: Array,
      default: () => []
    },
    // 组合支付
    combinedItem: {
      type: Array,
      default: () => []
    },
    right_msg: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.showReturnTitle = val;
      },
      immediate: true
    },
    loading: {
      handler(newVal) {
        if (newVal) {
          setTimeout(() => {
            this.$emit('closeLoading');
          }, this.delayedTime);
        }
      },
      immediate: true,
      deep: true
    }
  },
  computed: mapState({
    setting_small_printer: state => state.show.setting_small_printer,
    clickInterval: state => state.show.clickInterval,
    loginInfo: state => state.show.loginInfo,
    delayedTime: state => state.show.delayedTime,
    username: state => state.show.username,
    phone: state => state.show.phone
  }),
  methods: {
    ...mapActions([SET_SHOW]),
    // 处理文字
    getAccountName(e) {
      if (e === '微信(扫码付)') {
        return '原路退回（微信-扫码付）';
      } else if (e === '支付宝(扫码付)') {
        return '原路退回（支付宝-扫码付）';
      }
      return e;
    },
    // 打印
    printList() {
      var that = this;
      let list = [];
      if (this.print_click === true) {
        return false;
      }
      // demo.actionLog(logList.clickSaleDetailPrint);
      this.print_click = true;
      setTimeout(function() {
        that.print_click = false;
      }, that.clickInterval);
      if (this.setting_small_printer === undefined || this.setting_small_printer === null || this.setting_small_printer.trim() === '') {
        // this.openSetting();
        demo.msg('warning', '请设置打印机');
        return false;
      }
      let payWay1 = '';
      let payWay2 = '';
      let combinedFinalPay = [];
      if (this.combinedItem.length === 2) {
        payWay1 = this.combinedItem[0].accountName === '会员卡' ? '会员支付' : this.combinedItem[0].accountName;
        payWay2 = this.combinedItem[1].accountName === '会员卡' ? '会员支付' : this.combinedItem[1].accountName;
        combinedFinalPay.push(Number(this.combinedItem[0].payAmt).toFixed(2));
        combinedFinalPay.push(Number(this.combinedItem[1].payAmt).toFixed(2));
      }
      if (this.supplierShow) {
        list = _.cloneDeep(this.right_list).map(item => {
          return {
            ...item,
            number: Math.abs(item.qty),
            amt: Math.abs(item.amt)
          }
        })
      } else {
        list = _.cloneDeep(this.right_list);
      }
      let print_data = {
        // conbinaPayWayVal: [], // 组合支付
        combinedFinalPay: combinedFinalPay, // 组合支付分别钱
        payWay1: payWay1,
        payWay2: payWay2,
        printername: this.setting_small_printer,
        storename: that.username + '（补）', // 店员名
        accts: this.right_msg.accountName === '会员卡' ? '会员支付' : this.right_msg.accountName, // 支付方式
        pay_amt: (Number(this.right_msg.payAmt) + Number(this.right_msg.changeAmt)).toFixed(2), // 优惠金额
        change_amt: Number(this.right_msg.changeAmt).toFixed(2), // 实退金额
        createAt: this.dateFormat(this.orderTime.createAt), // 交易时间
        remark: 'Tel:' + that.phone, // 店主手机号
        goods: list
      };
      let vipInfo = {};
      if (this.right_msg.info1 && JSON.parse(this.right_msg.info1).vipInfo && this.right_msg.vipid) {
        vipInfo = JSON.parse(this.right_msg.info1).vipInfo || {};
        vipInfo.member_name = '会员名';
        vipInfo.member_value = this.right_msg.vipname;
        vipInfo.member_money_name = '余额';
        vipInfo.member_mobile_name = '会员手机号';
        vipInfo.member_mobile_value = this.right_msg.vipmobile.toString().substring(0, 3) + '****' + this.right_msg.vipmobile.toString().substring(7, 11);
        vipInfo.member_point_name = '积分';
      }
      print_data.operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
      print_data.printNum = demo.$store.state.show.smallPrinterCopies;
      console.log('打印数据', print_data);
      pos.printer.printPOSTimes(Object.assign(print_data, vipInfo), Object.assign({'from': this.supplierShow ? 'pay' : 'sales_detail', 'buy_back': this.supplierShow ? 2 : 1, 'orderno': this.orderTime.code}));
    },
    close() {
      this.$emit('cancel');
      this.$emit('update:visible', false);
    },
    dateFormat(dateStr) {
      return dateStr ? dateStr.replace('T', ' ').split('.')[0] : dateStr;
    },
    // 表格单双颜色
    tableRowClassName({row, rowIndex}) {
      console.log(row, rowIndex);
      if ((rowIndex + 1) % 2 === 0) {
        return 'success-row';
      }
      return '';
    },
    isNullOrTrimEmpty(obj) {
      if (obj === null || obj === undefined || obj === '') {
        return '';
      }
      return obj;
    }
  }
}
</script>
<style scoped lang="less">
/deep/ .el-table .success-row {
  background: #FAFAFA !important;
}
.list-right__content {
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 30;
  color: @themeFontColor;
  display: flex;
  align-items: center;
  .content-center {
    background: @butFontColor;
    margin: 0 auto;
    position: relative;
    width: 966px;
    border-radius: 10px;
    overflow: hidden;
    .center-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 10px;
      .top-title {
        color: @themeFontColor;
        font-weight: 700;
        flex-grow: 1;
        font-size: 20px;
        text-align: center;
      }
      .top-close {
        color: #8298A6;
        margin-left: auto;
        font-size: 30px;
        margin-top: -5px;
        font-weight: 200;
        cursor: pointer;
      }
    }
    .center-boder {
      margin: 0 20px;
      border: 1px solid #E3E6EB;
    }
    .centet-title {
      padding: 20px 20px;
      color: @homeColor;
      .title {
        font-size: 16px;
        font-weight: 700;
      }
      .title-list {
        margin-top: 20px;
        padding: 10px 10px 10px 10px;
        background: @input_backgroundColor;
        border-radius: 5px;
        .list-content {
          padding: 5px 5px 5px 5px;
          width: 100%;
          font-size: 14px;
          display: flex;
          align-items: center;
          font-weight: 500;
          .list-left {
            display: inline-block;
            width: 100px;
          }
          .list-right {
            width: 500px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .list-title {
            display: flex;
            columns: row;
            align-items: center;
            .title-contact {
              width: 50%;
              display: flex;
              columns: row;
              align-items: center;
              .list-right__phone {
                 width: 200px;
                 display: inline-block;
                 overflow: hidden; // 溢出隐藏
                 white-space: nowrap; // 强制一行
                 text-overflow: ellipsis; // 文字溢出显示省略号
              }
            }
          }
        }
      }
    }
    .centet-table {
      padding: 10px;
      color: @areaFontColor;
      .table-top {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 700;
        .title {
          color: @themeFontColor;
          width: 200px;
        }
        .goods-name {
          color: @text;
          max-width: 450px;
          display: inline-block;
          overflow: hidden; // 溢出隐藏
          white-space: nowrap; // 强制一行
          text-overflow: ellipsis; // 文字溢出显示省略号
        }
      }
      .table-content {
        padding: 5px;
        background: @butFontColor;
        border-radius: 5px;
        overflow: hidden;
        .order {
          display: flex;
          justify-content: space-between;
          padding: 10px 20px;
          overflow: hidden;
          font-weight: bold;
          font-size: 16px;
          align-items: center;
          color: @homeColor;
          .order-title {
            position: relative;
          }
          .order-print {
            width: 90px;
            height: 36px;
            color: @themeBackGroundColor;
            background-color:@themeButtonBackGroundColor;
            border-radius: 18px;
            float: right;
            margin-left: 20px;
            cursor: pointer;
            text-align: center;
            line-height: 36px;
            font-size: 16px;
          }
        }
        .order-table {
          padding: 5px;
          height: 440px;
          overflow: auto;
        }
        .order-bottom {
          padding: 5px 10px;
          .order-bottom__price {
            display: flex;
            justify-content: flex-start;
            .mode {
              width: 40%;
            }
            .remark {
              width: 40%;
            }
          }
        }
      }
    }
  }
  .centet-table {
    background: @input_backgroundColor;
  }
}
</style>
