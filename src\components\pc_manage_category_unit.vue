<style lang='less' scoped>
.com_pmcu1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 300;
}
.com_pmcu11 {
  width: 680px;
  height: 670px;
  margin: 0 auto;
  margin-top: 32px;
  background: #f4f6f8;
  border-radius: 2px;
  border: 1px solid #e5e5e5;
  position: relative;
  z-index: 150;
}
.com_pmcu12 {
  width: 100%;
  border-bottom: 1px solid #e5e5e5;
  height: 47px;
  line-height: 47px;
  font-size: 16px;
  background: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}
.com_pmcu12 div {
  float: left;
}
.com_pmcu13 {
  color: @themeBackGroundColor;
  margin-left: 37px;
  cursor: pointer;
  font-size: 15px;
}
.com_pmcu14 {
  color: @themeFontColor;
  font-size: 15px;
  font-weight: bold;
  width: 60px;
  text-align: center;
}
.com_pmcu15 {
  color: @themeBackGroundColor;
  margin-left: 146px;
  cursor: pointer;
}
.com_pmcu16 {
  color: #f26c30;
  margin-left: 55px;
  cursor: pointer;
}
.com_pmcu17 {
  width: 320px;
  height: 38px;
  border: 1px solid #dcdfe6;
  border-radius: 3px;
  margin: 0 auto;
  margin-top: 0px;
}
.com_pmcu2 {
  width: 270px;
  height: 28px;
  line-height: 28px;
  margin-left: 15px;
  font-size: 16px;
  margin-top: 4px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.com_pmcu21 {
  width: 16px;
  height: 16px;
  margin-top: 10px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.com_pmcu22 {
  overflow: hidden;
  margin-top: 35px;
  font-size: 16px;
  letter-spacing: 4px;
}
.com_pmcu23 {
  width: 79px;
  height: 38px;
  color: #fff;
  line-height: 36px;
  margin-left: 100px;
  float: left;
  border-radius: 4px;
  text-indent: 21px;
  cursor: pointer;
  background: @themeFontColor;
}
.com_pmcu24 {
  width: 79px;
  height: 38px;
  line-height: 36px;
  border: 1px solid @themeBackGroundColor;
  text-indent: 21px;
  margin-left: 14px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.com_pmcu25 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
}
.com_pmcu26 {
  width: 372px;
  height: 217px;
  background: #fff;
  margin: 0 auto;
  margin-top: 115px;
  position: relative;
  border-radius: 4px;
}
.com_pmcu27 {
  height: 85px;
  line-height: 85px;
  text-align: center;
  font-size: 18px;
  color: @themeFontColor;
}
.com_pmcu28 {
  position: absolute;
  top: 27px;
  right: 27px;
  font-size: 13px;
  line-height: 10px;
  color: #9099b2;
  cursor: pointer;
}
.com_pmcu29 {
  height: 46px;
  border-bottom: 1px solid #e5e5e5;
  background: #fff;
  width: 100%;
  line-height: 46px;
  color: @themeFontColor;
  font-size: 15px;
  position: relative;
  overflow: hidden;
}
.com_pmcu29:hover {
  background: #f4f6f8;
}
.com_pmcu3 {
  width: 16px;
  height: 16px;
  margin-top: -2px;
  margin-right: 22px;
  margin-left: 7px;
  float: left;
}
.com_pmcu31 {
  width: 20px;
  height: 20px;
  margin-top: 12px;
  margin-right: 22px;
  right: 20px;
  float: right;
  background-image: url(../image/pc_edit_mouseleave.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.com_pmcu31:hover {
  background-image: url('../image/pc_edit_mouseover.png');
}
.com_pmcu32 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  margin-left: 92px;
  float: left;
  border-radius: 4px;
  text-align: center;
  font-size: 24px;
  letter-spacing: 0px;
  cursor: pointer;
  background: @themeFontColor;
  color: #fff;
}
.com_pmcu33 {
  width: 118px;
  height: 58px;
  line-height: 58px;
  border: 1px solid @themeBackGroundColor;
  text-align: center;
  margin-left: 14px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 24px;
  letter-spacing: 0px;
}
.category_search_div {
  height: 50px;
  /deep/.el-input__inner {
    border-radius: 22px;
    font-size: 16px;
    font-weight: normal;
    font-family: Microsoft YaHei UI, sans-serif;
  }
}
</style>

<template>
  <div>
    <!-------------------类别管理部分开始---------------->
    <div v-show="showSelManage" class="com_pmcu1">
      <div
        style="position: fixed;width: 100%;height: 100%;z-index: 120;"
      ></div>
      <div class="com_pmcu11">
        <div class="com_pmcu12" v-show="!show_editing_category">
          <div class="com_pmcu13" @click="close_category_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">类别管理</div>
          <div class="com_pmcu15" @click="show_editing_category = true">编辑</div>
          <div class="com_pmcu16" @click="show_add_category = true;inputFocus('new_category')">新增</div>
        </div>
        <div class="com_pmcu12" v-show="show_editing_category">
          <div class="com_pmcu13" @click="close_category_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">类别管理</div>
          <div
            class="com_pmcu16"
            @click="show_editing_category = false"
            style="color: #d5aa76;margin-left: 210px;"
          >完成</div>
        </div>
        <div style="height: 670px;overflow: scroll;padding-left: 6px;">
          <div style="height: 55px;"></div>
          <div class="category_search_div">
            <el-input v-model.trim="category_keyword" placeholder="请输入类别名"></el-input>
          </div>
          <div v-for="(ca,index) in category_list" v-bind:key="index" v-show="ca.name != '称重类别'">
            <div
              class="com_pmcu29"
              @click="agreeChooseCategory && !show_editing_category ? input_category(ca.id,ca.name) : ''"
            >
              <div style="width: 45px;height: 45px;float: left;">
                <img
                  alt=""
                  v-show="show_editing_category"
                  @click="sure_delete_category(ca.fingerprint)"
                  class="com_pmcu3" style="margin-top: 15px;margin-left: 15px;"
                  src="../image/pc_delete_category.png"
                />
              </div>

              <div style="float: left;width: 420px;">{{ca.name}}</div>
              <img alt="" v-show="show_editing_category && index == 0" src="../image/pc_category_up_grey.png"
              style="width: 35px;height: 35px;
                margin-top: -5px;margin-left: 30px;cursor: pointer;" />
              <img alt="" v-show="show_editing_category && index != 0" src="../image/pc_category_up1.png"
              style="width: 35px;height: 35px;
                margin-top: -5px;margin-left: 30px;cursor: pointer;" @click="orderChange('up',index)" />
              <img alt="" v-show="show_editing_category && index + 1 == category_list.length"
              src="../image/pc_category_down_grey.png"
                style="width: 35px;height: 35px;margin-top: -5px;margin-left: 15px;cursor: pointer;" />
              <img alt="" v-show="show_editing_category && index + 1 != category_list.length"
              src="../image/pc_category_down1.png"
                style="width: 35px;height: 35px;margin-top: -5px;margin-left: 15px;cursor: pointer;" @click="orderChange('down',index)" />
              <!--铅笔图标 ↓-->
              <div v-show="show_editing_category" class="com_pmcu31" @click="edit_category(ca)"></div>
            </div>
          </div>
          <div
            class="com_pmcu29" style="text-indent: 45px;" v-if="'其他类别'.indexOf(category_keyword) !== -1"
            @click="agreeChooseCategory && !show_editing_category ? input_category('2','其他类别') : ''"
          >其他类别</div>
        </div>
      </div>
    </div>
    <!--新增类别弹出框-->
    <div v-show="show_add_category" class="com_pmcu25" style="z-index: 300;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">新增类别</div>
        <div @click="show_add_category = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_category ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            class="com_pmcu2"
            id="new_category"
            @focus="inputing_category = true"
            @blur="inputing_category = false"
            type="text"
            placeholder="输入类别名称(限5个字)"
            maxlength="5"
            v-model="category"
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="category = category.replace(/[$']/g, '')"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="category != ''"
            @click="category = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="show_add_category = false;category = ''">取消</div>
          <div class="com_pmcu24" @click="add_category()">新增</div>
        </div>
      </div>
    </div>
    <!--修改类别弹出框-->
    <div v-show="show_edit_category" class="com_pmcu25" style="z-index: 400;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">修改类别</div>
        <div @click="show_edit_category = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_category ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            class="com_pmcu2"
            id="edit_category"
            @focus="inputing_category = true"
            @blur="inputing_category = false"
            type="text"
            placeholder="输入类别名称(限5个字)"
            maxlength="5"
            v-model="category"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="category != ''"
            @click="category = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div
            class="com_pmcu23"
            @click="show_edit_category = false;category = '';set_index = ''"
          >取消</div>
          <div class="com_pmcu24" @click="save_edit_category()">保存</div>
        </div>
      </div>
    </div>
    <!-------------------类别管理部分结束---------------->

    <!-------------------单位管理部分---------------->
    <div v-show="showUnitManage" class="com_pmcu1">
      <div
        style="position: fixed;width: 100%;height: 100%;z-index: 120;"
        @click="close_unit_manage()"

      ></div>
      <div class="com_pmcu11">
        <div class="com_pmcu12" v-show="!show_editing_unit">
          <div class="com_pmcu13" @click="close_unit_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">单位</div>
          <div class="com_pmcu15" @click="show_editing_unit = true">编辑</div>
          <div class="com_pmcu16" @click="show_add_unit = true;inputFocus('new_unit')">新增</div>
        </div>
        <div class="com_pmcu12" v-show="show_editing_unit">
          <div class="com_pmcu13" @click="close_unit_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">单位</div>
          <div
            class="com_pmcu16"
            @click="show_editing_unit = false"
            style="color: #d5aa76;margin-left: 210px;"
          >完成</div>
        </div>
        <div style="height: 670px;overflow: scroll;">
          <div style="height: 60px;"></div>
          <div v-for="un in unit_list" v-bind:key="un.id">
            <div class="com_pmcu29" @click="!show_editing_unit ? input_unit(un.id,un.name) : ''">
              <span style="margin-left: 20px;">{{un.name}}</span>
              <!--删除图标 ↓-->
              <img
                alt=""
                v-show="show_editing_unit && un.name !== '斤' && un.name !== '克' && un.name !== '两' && un.name !=='千克' && un.name !== '公斤'"
                class="com_pmcu3"
                style="margin-right: 20px;float: right;margin-top: 15px;"
                src="../image/pc_delete_category.png"
                @click="sure_delete_unit(un.name)"
              />
              <span
                v-show="un.name === '斤' || un.name === '克' || un.name === '两' || un.name === '千克' || un.name === '公斤'"
                style="color: #00A0E8;float: right;margin-right: 20px;"
              >称重</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--新增单位弹出框-->
    <div v-show="show_add_unit" class="com_pmcu25" style="z-index: 300;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">新增单位</div>
        <div @click="show_add_unit = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_unit ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            id="new_unit"
            class="com_pmcu2"
            @focus="inputing_unit = true"
            @blur="inputing_unit = false"
            type="text"
            placeholder="输入单位名称(限5个字)"
            maxlength="5"
            v-model="unit"
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="unit = unit.replace(/[$']/g, '')"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="unit !== ''"
            @click="unit = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="show_add_unit = false;unit = ''">取消</div>
          <div class="com_pmcu24" @click="add_unit()">新增</div>
        </div>
      </div>
    </div>
    <!--修改单位弹出框-->
    <div v-show="show_edit_unit" class="com_pmcu25" style="z-index: 400;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">修改单位</div>
        <div @click="show_edit_unit = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_unit ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            id="edit_unit"
            class="com_pmcu2"
            @focus="inputing_unit = true"
            @blur="inputing_unit = false"
            type="text"
            placeholder="输入单位名称(限5个字)"
            maxlength="5"
            v-model="unit"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="unit !== ''"
            @click="unit = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="show_edit_unit = false;unit = '';set_index = ''">取消</div>
          <div class="com_pmcu24" @click="save_edit_unit()">保存</div>
        </div>
      </div>
    </div>
    <!-------------------单位管理部分结束---------------->

    <!-------------------品牌管理部分开始---------------->
    <div v-show="showBrandManage" class="com_pmcu1">
      <div
        style="position: fixed;width: 100%;height: 100%;z-index: 120;"
        @click="close_brand_manage()"
      ></div>
      <div class="com_pmcu11">
        <div class="com_pmcu12" v-show="!show_editing_brand">
          <div class="com_pmcu13" @click="close_brand_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">品牌管理</div>
          <div class="com_pmcu15" @click="show_editing_brand = true">编辑</div>
          <div class="com_pmcu16" @click="show_add_brand = true;inputFocus('new_brand')">新增</div>
        </div>
        <div class="com_pmcu12" v-show="show_editing_brand">
          <div class="com_pmcu13" @click="close_brand_manage()">关闭</div>
          <div class="com_pmcu14" style="margin-left: 242px;">品牌管理</div>
          <div
            class="com_pmcu16"
            @click="show_editing_brand = false"
            style="color: #d5aa76;margin-left: 210px;"
          >完成</div>
        </div>
        <div style="height: 670px;overflow: scroll;">
          <div style="height: 60px;"></div>
          <div v-for="un in brand_list" v-bind:key="un.id">
            <div class="com_pmcu29" @click="!show_editing_brand ? input_brand(un.id,un.name) : ''">
              <span style="margin-left: 20px;">{{un.name}}</span>
              <!--铅笔图标 ↓-->
              <div v-show="show_editing_brand" class="com_pmcu31" @click="edit_brand(un)"></div>
              <!--删除图标 ↓-->
              <img
                alt=""
                v-show="show_editing_brand"
                class="com_pmcu3"
                style="margin-right: 20px;float: right;margin-top: 15px;"
                src="../image/pc_delete_category.png"
                @click="sure_delete_brand(un.name)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--新增品牌弹出框-->
    <div v-show="show_add_brand" class="com_pmcu25" style="z-index: 300;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">新增品牌</div>
        <div @click="show_add_brand = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_brand ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            id="new_brand"
            class="com_pmcu2"
            @focus="inputing_brand = true"
            @blur="inputing_brand = false"
            type="text"
            placeholder="输入品牌名称(限10个字)"
            maxlength="10"
            v-model="brand"
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="brand = brand.replace(/[$']/g, '')"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="brand !== ''"
            @click="brand = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="show_add_brand = false;brand = ''">取消</div>
          <div class="com_pmcu24" @click="add_brand()">新增</div>
        </div>
      </div>
    </div>
    <!--修改品牌弹出框-->
    <div v-show="show_edit_brand" class="com_pmcu25" style="z-index: 400;">
      <div class="com_pmcu26">
        <div class="com_pmcu27">修改品牌</div>
        <div @click="show_edit_brand = false" class="com_pmcu28">✕</div>
        <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div>
        <div
          class="com_pmcu17"
          :style="inputing_brand ? 'border-color: #d5aa76' : 'border-color: #DCDFE6'"
        >
          <input
            id="edit_brand"
            class="com_pmcu2"
            @focus="inputing_brand = true"
            @blur="inputing_brand = false"
            type="text"
            placeholder="输入单位名称(限10个字)"
            maxlength="10"
            v-model="brand"
          />
          <img
            alt=""
            class="com_pmcu21"
            v-show="brand !== ''"
            @click="brand = ''"
            src="../image/pc_clear_input.png"
          />
        </div>
        <div class="com_pmcu22">
          <div class="com_pmcu23" @click="show_edit_brand = false;brand = '';set_index = ''">取消</div>
          <div class="com_pmcu24" @click="save_edit_brand()">保存</div>
        </div>
      </div>
    </div>
    <!-------------------品牌管理部分结束---------------->

    <!--删除最终确认提示弹出框-->
    <div v-show="show_delete_unit || show_delete_brand" class="com_pmcu25" style="z-index: 500;">
      <div class="com_pmcu26" style="width: 450px;height: 250px;">
        <div class="com_pmcu27" style="font-size: 24px;">提示</div>
        <div
          style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;margin-top: 12px;color: #567485"
        >
          是否删除商品<span v-show="show_delete_unit">单位</span><span v-show="show_delete_unit">品牌</span>?
        </div>
        <div class="com_pmcu22" style="margin-top: 39px;">
          <div
            class="com_pmcu32"
            @click="cancel()"
          >取消</div>
          <div
            class="com_pmcu33"
            @click="sure()"
          >确定</div>
        </div>
      </div>
    </div>

    <!--删除类别或单位的最终确认提示弹出框-->
    <div v-show="show_delete_category" class="com_pmcu25" style="z-index: 500;">
      <div class="com_pmcu26" style="width: 450px;height: 300px;">
        <div class="com_pmcu27" style="font-size: 24px;font-weight: 700;color: #567485;">提示</div>
        <div style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;margin-top: 12px;color: #567485">
          该分类下有商品存在，
        </div>
        <div style="width: 100%;text-align: center;font-size: 25px;font-weight: 100;color: #567485">
          商品也将一并删除。
        </div>
        <div class="com_pmcu22" style="margin-top: 39px;display: flex;justify-content: center;">
          <div
            class="com_pmcu32"
            style="width: 180px; height: 50px;margin: 0px;float: none; line-height: 50px;"
            @click="show_delete_category = false;category = '';set_index = ''"
          >取消删除</div>
          <div style="width:30px"></div>
          <div
            class="com_pmcu33"
            style="width: 180px; height: 50px;margin: 0px;float: none; line-height: 50px;"
            @click="continue_delete_category()"
          >一并删除</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';

export default {
  data() {
    return {
      add_goods: {
        category: '其他类别',
        unit: ''
      },
      category: '',
      unit: '',
      brand: '',
      category_list: [],
      unit_list: [],
      brand_list: [],
      inputing_category: false,
      show_add_category: false,
      show_editing_category: false,
      show_edit_category: false,
      set_index: '',
      show_delete_category: false,
      inputing_unit: false,
      show_add_unit: false,
      show_editing_unit: false,
      show_edit_unit: false,
      show_delete_unit: false,
      //      储存要编辑的类别/单位 信息
      set_detail: '',
      add_click: false,
      pinyin: false,
      inputing_brand: false,
      show_editing_brand: false,
      show_add_brand: false,
      show_edit_brand: false,
      show_delete_brand: false
    };
  },
  watch: {
    showSelManage() {
      this.category_keyword = '';
      this.getAllCategory();
      this.inputing_category = false;
      this.show_add_category = false;
      this.show_editing_category = false;
      this.show_edit_category = false;
      this.set_index = '';
      this.show_delete_category = false;
    },
    showUnitManage() {
      this.get_all_unit();
      this.inputing_unit = false;
      this.show_add_unit = false;
      this.show_editing_unit = false;
      this.show_edit_unit = false;
      this.set_index = '';
      this.show_delete_unit = false;
    },
    showBrandManage() {
      this.getAllBrand();
      this.inputing_brand = false;
      this.show_add_brand = false;
      this.show_editing_brand = false;
      this.show_edit_brand = false;
      this.set_index = '';
      this.show_delete_brand = false;
    },
    category_keyword() {
      if (this.categoryTimer) {
        clearTimeout(this.categoryTimer);
      }
      this.categoryTimer = setTimeout(() => {
        this.getAllCategory();
      }, this.delayedTime);
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    inputFocus(sid) {
      setTimeout(function() {
        $('#' + sid).focus();
      }, 10);
    },
    orderChange(str, index) {
      // str == up 排序上升
      // str == down 排序下降
      var mid = '';
      if (str === 'up') {
        mid = _.cloneDeep(this.category_list[index - 1]);
        this.category_list[index - 1] = this.category_list[index];
        this.category_list[index] = mid;
      } else if (str === 'down') {
        mid = _.cloneDeep(this.category_list[index + 1]);
        this.category_list[index + 1] = this.category_list[index];
        this.category_list[index] = mid;
      } else {
        console.log('其他');
      }
      this.$forceUpdate();
      console.log(this.category_list);
      var id_list = [];
      for (var i = 0; i < this.category_list.length; i++) {
        id_list.push({id: '', sortno: ''});
        id_list[i].id = this.category_list[i].id;
        id_list[i].sortno = i;
      }
      typeService.orderbyUpdate(id_list, function(res) {
        console.log(res, '排序成功');
      });
    },
    add_category() {
      var that = this;
      this.category = this.category.trim();
      if (this.add_click == true) {
        return;
      }
      this.add_click = true;
      setTimeout(function() {
        that.add_click = false;
      }, that.clickInterval);

      if (this.category == '') {
        demo.msg('warning', '请输入类别名称');
        return;
      }
      if (this.category == '称重类别') {
        demo.msg('warning', '称重类别已存在，请添加称重商品');
        return;
      }
      if (this.category == '热销' || this.category == '全部分类' || this.category == '其他类别') {
        demo.msg('warning', '类别已存在！');
        return;
      }
      var data = {
        parent_id: 1,
        name: this.category,
        pinyin: '',
        is_deleted: 0
      };
      typeService.put(data, function(res) {
        var json = demo.t2json(res);
        if (json.length == 0) {
          demo.msg('success', '新增类别成功');
          that.getAllCategory();
        }
      });
      this.category = '';
      this.show_add_category = false;
    },
    // 编辑时提交全部信息，获取存取也要存所有
    edit_category(det) {
      this.set_detail = det;
      this.category = det.name;
      this.show_edit_category = true;
      this.inputFocus('edit_category');
    },
    save_edit_category() {
      if (this.category == '') {
        demo.msg('warning', '请输入类别名称');
        return;
      }
      if (this.category == '称重类别') {
        demo.msg('warning', '称重类别已存在，请添加称重商品');
        return;
      }
      if (this.category === '热销' || this.category === '全部分类' || this.category === '其他类别') {
        demo.msg('warning', '类别已存在！');
        return;
      }
      var that = this;
      // 判断类别名称是否重复
      var data = { name: this.category, id: this.set_detail.id };
      typeService.exist(
        data,
        function(rs) {
          var res = demo.t2json(rs);
          if (res.length > 0) {
            demo.msg('warning', '类别已存在！');
          } else {
            var data2 = {
              name: that.category,
              id: that.set_detail.id,
              parent_id: that.set_detail.parent_id,
              pinyin: that.set_detail.pinyin,
              is_deleted: 0
            };
            typeService.put(data2, function() {
              demo.msg('success', '保存类别成功');
              that.getAllCategory();
              that.SET_SHOW({ isChangeCategory: true });
            });
          }

          that.show_edit_category = false;
          that.category = '';
          that.set_detail = '';
        },
        function(error) {
          console.error(error);
        }
      );
    },
    sure_delete_category(fingerprint) {
      typeservice.use(fingerprint, (res) => {
        if (demo.t2json(res)[0].cnt > 0) {
          if (this.$employeeAuth('delete_products')) {
            this.show_delete_category = true;
            this.set_index = fingerprint;
          } else {
            demo.msg('warning', '商品使用中，不允许删除');
          }
        } else {
          this.set_index = fingerprint;
          this.continue_delete_category();
        }
      });
    },
    continue_delete_category() {
      this.show_delete_category = false;
      goodService.deleteByTypes({fingerprint: [this.set_index], isDelType: '1'}, res => {
        demo.msg('success', '删除商品类别成功');
        this.getAllCategory();
      })
      this.set_index = '';
      this.category = '';
      this.SET_SHOW({ isChangeCategory: true });
    },
    add_unit() {
      var that = this;
      this.unit = this.unit.trim();
      if (this.add_click) {
        return;
      }
      this.add_click = true;
      setTimeout(function() {
        that.add_click = false;
      }, that.clickInterval);
      if (this.unit === '') {
        demo.msg('warning', '请输入单位名称');
        return;
      }
      var data = { name: this.unit, pinyin: '', is_deleted: 0 };
      unitService.put(data, function() {
        demo.msg('success', '新增单位成功');
        that.get_all_unit();
      });
      this.unit = '';
      this.show_add_unit = false;
    },
    edit_unit(n) {
      this.set_index = n;
      this.unit = this.unit_list[n].name;
      this.show_edit_unit = true;
    },
    save_edit_unit() {
      this.unit_list[this.set_index].name = this.unit;
      this.show_edit_unit = false;
      this.unit = '';
      this.set_index = '';
    },
    sure_delete_unit(id) {
      this.show_delete_unit = true;
      this.set_index = id;
    },
    continue_delete_unit() {
      this.show_delete_unit = false;
      var that = this;
      var data = { is_deleted: 1, name: this.set_index };
      unitService.put(data, function() {
        demo.msg('success', '删除商品单位成功');
        that.get_all_unit();
      });
      this.set_index = '';
      this.unit = '';
    },
    add_brand() {
      var that = this;
      this.unit = this.brand.trim();
      if (this.add_click) {
        return;
      }
      this.add_click = true;
      setTimeout(function() {
        that.add_click = false;
      }, that.clickInterval);
      if (this.brand === '') {
        demo.msg('warning', '请输入品牌名称');
        return;
      }
      var data = { name: this.brand, pinyin: '', is_deleted: 0 };
      unitService.put(data, function() {
        demo.msg('success', '新增品牌成功');
        that.getAllBrand();
      });
      this.brand = '';
      this.show_add_brand = false;
    },
    edit_brand(det) {
      this.set_detail = det;
      this.brand = det.name;
      this.show_edit_brand = true;
      this.inputFocus('edit_brand');
    },
    save_edit_brand() {
      if (this.brand == '') {
        demo.msg('warning', '请输入类别名称');
        return;
      }
      var that = this;
      // 判断类别名称是否重复
      var data = { name: this.brand, id: this.set_detail.id };
      typeService.exist(
        data,
        function(rs) {
          var res = demo.t2json(rs);
          if (res.length > 0) {
            demo.msg('warning', '品牌已存在！');
          } else {
            var data2 = {
              name: that.brand,
              id: that.set_detail.id,
              parent_id: that.set_detail.parent_id,
              pinyin: that.set_detail.pinyin,
              is_deleted: 0
            };
            typeService.put(data2, function() {
              demo.msg('success', '保存品牌成功');
              that.getAllBrand();
            });
          }
          that.show_edit_brand = false;
          that.brand = '';
          that.set_detail = '';
        },
        function(error) {
          console.error(error);
        }
      );
    },
    sure_delete_brand(id) {
      this.show_delete_brand = true;
      this.set_index = id;
    },
    continue_delete_brand() {
      this.show_delete_brand = false;
      var that = this;
      var data = { is_deleted: 1, name: this.set_index };
      unitService.put(data, function() {
        demo.msg('success', '删除商品品牌成功');
        that.getAllBrand();
      });
      this.set_index = '';
      this.brand = '';
    },
    input_category(cid, name) {
      this.SET_SHOW({ addGoodsCategoryId: cid });
      this.SET_SHOW({ addGoodsCategory: name });
      this.close_category_manage();
      if (this.batchUpdateGoodsType === true) {
        this.SET_SHOW({ batchUpdateGoodsTypeId: cid });
        this.SET_SHOW({ batchUpdateGoodsType: false });
      }
    },
    input_unit(uid, name) {
      this.SET_SHOW({ addGoodsUnit: name });
      this.SET_SHOW({ addGoodsUnitId: uid });
      this.close_unit_manage();
      if (this.batchUpdateGoodsUnit === true) {
        this.SET_SHOW({ batchUpdateGoodsUnitId: uid });
        this.SET_SHOW({ batchUpdateGoodsUnit: false });
      }
    },
    input_brand(bid, name) {
      this.SET_SHOW({ addGoodsBrandId: bid });
      this.SET_SHOW({ addGoodsBrand: name });
      this.close_brand_manage();
      if (this.batchUpdateGoodsBrand === true) {
        this.SET_SHOW({ batchUpdateGoodsBrandId: cid });
        this.SET_SHOW({ batchUpdateGoodsBrand: false });
      }
    },
    close_category_manage() {
      this.SET_SHOW({ showSelManage: false });
      this.category_keyword = '';
    },
    close_unit_manage() {
      this.SET_SHOW({ showUnitManage: false });
    },
    close_brand_manage() {
      this.SET_SHOW({ showBrandManage: false });
    },
    // 获取所有类别
    getAllCategory() {
      this.alltype = '';
      var that = this;
      let name;
      if (this.category_keyword !== '') {
        name = this.category_keyword;
      }
      typeservice.search(function(res) {
        var json = demo.t2json(res);
        for (var i = 0; i < json.length; i++) {
          if (json[i].id == 1 || json[i].id == 2 || json[i].name == '称重类别') {
            json.splice(i, 1);
            i--;
          }
        }
        if (json.length > 0) {
          that.category_list = json;
        } else {
          that.category_list = [];
        }
      }, () => {
        console.log('exec do nothing!');
      }, name);
    },
    // 获取所有单位
    get_all_unit() {
      var that = this;
      unitService.search(function(res) {
        var json = demo.t2json(res);
        if (json.length > 0) {
          that.unit_list = json;
        } else {
          that.unit_list = [];
        }
      });
    },
    // 获取所有品牌
    getAllBrand() {
      // todo
    },
    cancel() {
      this.show_delete_unit = false;
      this.unit = '';
      this.show_delete_category = false;
      this.category = '';
      this.show_delete_brand = false;
      this.brand = '';
      this.set_index = '';
    },
    sure() {
      if (this.show_delete_category) {
        this.continue_delete_category();
      } else if (this.show_delete_unit) {
        this.continue_delete_unit();
      } else {
        this.continue_delete_brand();
      }
    }
  },
  computed: mapState({
    showSelManage: state => state.show.showSelManage,
    showUnitManage: state => state.show.showUnitManage,
    showBrandManage: state => state.show.showBrandManage,
    agreeChooseCategory: state => state.show.agreeChooseCategory,
    clickInterval: state => state.show.clickInterval,
    delayedTime: state => state.show.delayedTime,
    batchUpdateGoodsType: state => state.show.batchUpdateGoodsType,
    batchUpdateGoodsTypeId: state => state.show.batchUpdateGoodsTypeId,
    batchUpdateGoodsUnit: state => state.show.batchUpdateGoodsUnit,
    batchUpdateGoodsUnitId: state => state.show.batchUpdateGoodsUnitId,
    batchUpdateGoodsBrand: state => state.show.batchUpdateGoodsBrand,
    batchUpdateGoodsBrandId: state => state.show.batchUpdateGoodsBrandId
  })
};
</script>
