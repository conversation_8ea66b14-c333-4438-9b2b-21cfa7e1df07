<template>
  <!-- 会员消费汇总 -->
  <div class="vip_consume_collection_container">
    <div class="vip_consume_collection_container_content">
      <div class="top">
        <div class="top_left">
          <div class="top_left_title">统计时间：</div>
          <div
            class="date_picker_container"
            @click="focusDate = true"
            :style="focusDate ? 'border-color: #d5aa76' : 'border-color: #E3E6EB'"
          >
            <el-date-picker
              v-model="fromDate"
              type="date"
              placeholder="开始日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
            >
            </el-date-picker>
            <div style="font-size: 16px;color: #567485">至</div>
            <el-date-picker
              v-model="toDate"
              type="date"
              placeholder="结束日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
            >
            </el-date-picker>
          </div>
        </div>
        <div class="top_right">
          <div class="btn_export_excel" @click="currentPage = 1;httpRequest('查询')">查询</div>
        </div>
      </div>
      <div class="center" style="height: 520px;">
        <div
          ref="chart" v-show="show_echarts"
          style="width: 1000px;height:500px;margin: 0 auto;margin-top: 20px;"
        ></div>
      </div>
      <div class="bottom" v-show="false">
        <div class="bottom_top">
          <div style="margin-left:10px;font-weight:bold;color: #567485">汇总统计</div>
          <div class="btn_export_excel" @click="httpRequest('导出表格')">导出表格</div>
        </div>
        <div class="collection_statistics">
          <el-table
            :data="tableData"
            height="100%"
          >
            <el-table-column
              prop="id"
              label="序号"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="username"
              label="店铺名称"
              align="center"
              width="400"
            >
            </el-table-column>
            <el-table-column
              prop="orderNum"
              label="消费笔数"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="money"
              label="消费总额"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="cardMoney"
              label="会员卡消费金额"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="consumePoint"
              label="消费积分"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="givePoint"
              label="赠送积分"
              align="center"
            >
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import echarts from 'echarts';
export default {
  data () {
    return {
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      show_echarts: '',
      tableData: []
    };
  },
  mounted () {
    let today = new Date();
    this.fromDate = new Date(today.setDate(today.getDate() - 7)).format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.httpRequest('查询');
  },
  methods: {
    httpRequest(str) {
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      demo.actionLog({page: 'pc_vip_consume_collection', action: 'httpRequest', description: '会员消费汇总' + str});
      var url = '';
      if (str === '导出表格') {
        url = this.$rest.pc_getVipConsumeListExport;
      } else if (str === '查询') {
        url = this.$rest.pc_getVipConsumeList;
      } else {
        console.log(str);
      }
      var sub_data = {
        'sysSid': this.sysSid,
        'fromDate': this.fromDate,
        'toDate': this.toDate,
        'phone': this.phone,
        // 'uid': this.sysUid,
        'systemName': $config.systemName
      };

      var that = this;
      demo.$http.post(url, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          that.parseResult(res, str);
        });
    },
    /**
     * 对应sonar
     */
    parseResult (res, str) {
      let that = this;
      if (res.data.code === '0' && str === '查询') {
        if (res.data.data.length === 0) {
          that.show_echarts = false;
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          return;
        }
        res.data.data.total[0].username = that.username;
        that.tableData = res.data.data.total;
        that.show_echarts = true;
        that.makeEcharts(res.data.data.datalist.dateGroup, res.data.data.datalist.wasteGroup, res.data.data.datalist.orderGroup);
      } else if (res.data.code === '0' && str === '导出表格') {
        if (res.data.data.length > 0) {
          res.data.data[0].username = that.username;
          var field_mapping = {
            序号: 'id',
            店铺名称: 'username',
            消费笔数: 'orderNum',
            消费总额: 'integral',
            会员卡消费金额: 'cardMoney',
            消费积分: 'consumePoint',
            赠送积分: 'givePoint'
          };
          that.$makeExcel(res.data.data, field_mapping, '会员消费汇总' + new Date().format('yyyyMMddhhmmss'));
        } else {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          that.show_echarts = false;
        }
      } else {
        demo.msg('warning', res.data.msg);
      }
    },
    makeEcharts(x, y1, y2) {
      var chart = echarts.init(this.$refs.chart);
      var options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          left: 50,
          itemGap: 57,
          data: [
            {
              name: '　消费总额',
              textStyle: {
                fontSize: 16,
                color: '#567485'
              }
            },
            {
              name: '　交易笔数',
              textStyle: {
                fontSize: 16,
                color: '#567485'
              }
            }
          ]
        },
        xAxis: [
          {
            type: 'category',
            data: x,
            axisLabel: {
              fontSize: 16,
              color: '#567485',
              margin: 15
            },
            axisLine: {
              lineStyle: {
                color: '#567485'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              fontSize: 16,
              formatter: '{value}元',
              color: '#567485',
              margin: 15
            },
            axisLine: {
              lineStyle: {
                show: false,
                color: '#567485'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#E3E6EB'
              }
            }
          },
          {
            type: 'value',
            axisLabel: {
              fontSize: 16,
              formatter: '{value}笔',
              color: '#567485',
              margin: 15
            },
            axisLine: {
              lineStyle: {
                show: false,
                color: '#567485'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '　消费总额',
            type: 'bar',
            itemStyle: {
              normal: {
                color: '#D5AA76'
              }
            },
            data: y1
          },
          {
            name: '　交易笔数',
            type: 'line',
            yAxisIndex: 1,
            lineStyle: {
              width: 3,
              color: '#FF554C'
            },
            data: y2
          }
        ]
      };
      chart.setOption(options);
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    username: state => state.show.username,
    phone: state => state.show.phone
  })
};
</script>

<style lang='less' scoped>
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  padding-right: 0;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #F5F8FB;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  margin-left: 20px;
  display: flex;
  align-items: center;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .el-input--suffix .el-input__inner {
  color: #C0C4CC;background: #F5F8FB;
}
.vip_consume_collection_container{
  background: #F5F8FB;
  font-size: 16px;
  color: @themeFontColor;
  .vip_consume_collection_container_content{
    overflow: auto;
    background: white;
    height: calc(100vh - 70px);
    margin: 10px;
    border-radius: 4px;
    border: 1px solid #E3E6EB;
    .top{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 80px;
      padding: 10px;
      margin: 0 20px;
      background: white;
      border-bottom: 1px dashed #E3E6EB;
      .top_left{
        display: flex;
        align-items: center;
        .top_left_title{
          color: @themeFontColor;
          font-size: 16px;
        }
      }
      .top_right{
        display: flex;
        align-items: center;
        .btn_export_excel{
          width: 100px;
          height: 44px;
          line-height: 44px;
          background: @themeBackGroundColor;
          color: white;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          cursor: pointer;
          border-radius: 22px;
        }
      }
    }
    .bottom{
      .bottom_top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 90px;
        border-top: 1px dashed #E3E6EB;
        margin: 0 20px;
        margin-top: 30px;
        .btn_export_excel{
          width: 130px;
          height: 44px;
          line-height: 44px;
          background: @themeBackGroundColor;
          color: white;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          border-radius: 22px;
          cursor: pointer;
          margin-right: 10px;
        }
      }
      .collection_statistics{
        height: 102px;
        margin: 0 10px;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
      }
    }
  }
}
</style>
