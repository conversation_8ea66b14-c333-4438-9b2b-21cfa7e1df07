module.exports = {
  theme: {
    backGroundColor: '#B4995A',
    fontColor: '#1D324F'
  },
  page: {
    register: {
      declaration: '《 大云系列产品隐私声明 》',
      declaration_url: 'http://duobao.trechina.cn/bdccai/agreement.html'
    },
    setting: {
      business_scope: [
        { label: '零售杂货' },
        { label: '烘焙茶饮' },
        { label: '文体玩具' },
        { label: '母婴行业' },
        { label: '服饰鞋包' },
        { label: '美业理疗' },
        { label: '餐饮快餐' },
        { label: '其它' }
      ],
      del_data_div_txt: '为防止产生异常数据，请确认已关闭其它设备上的大云数智程序'
    },
    home: {
      privacyStatement: '用户隐私声明',
      registeredStatement: '用户注册声明'
    }
  },
  components: {
    pc_version_compare: {
      startEmploy: '开始使用',
      buyNow: '立即购买'
    },
    header: {
      header_user: '掌柜智囊'
    }
  },
  image: {
    homeImage: {
      logo: '../../image/bdccai-poss/pc_login_logo.png',
      cash: '../../image/bdccai-poss/pc_home_cash.png',
      changeshifts: '../../image/bdccai-poss/pc_home_changeshifts.png',
      detail: '../../image/bdccai-poss/pc_home_detail.png',
      employee: '../../image/bdccai-poss/pc_home_employee.png',
      good: '../../image/bdccai-poss/pc_home_good.png',
      member: '../../image/bdccai-poss/pc_home_member.png',
      stock: '../../image/bdccai-poss/pc_home_stock.png',
      color: '#B4995A',
      editionColor: '#278FF5',
      editionBackground: 'linear-gradient(156.99deg, rgba(208, 244, 255, 0.5) 10.75%, rgba(169, 210, 248, 0.5) 47.67%, rgba(100, 188, 237, 0.5) 87.8%);',
      goodsBut: '#B4995A'
    }
  }
}
