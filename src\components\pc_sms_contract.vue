<style lang="less" scoped>
@import '../less/common.less';
/deep/.pc_popup_lg {
  width: 1007px!important;
  padding: 20px 30px;
}
/deep/.top-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #4B4B4B;
  height: 20px;
  margin-bottom: 20px;
}
.content {
  font-size: 16px;
  color: #4B4B4B;
  white-space: pre-wrap;
  background: #F5F5F5;
  height: 400px;
  overflow: auto;
}
.bottom {
  &-flex {
    margin-top: 15px;
    display: flex;
    justify-content: left;
    align-items: center;
  }
  &-radio {
    margin-right: 10px;
    display: inline-block;
  }
  &-text {
    color: #F00;
    font-size: 16px;
    display: inline-block;
    width: 879px;
  }
  &-button_agree {
    border-radius: 6px;
    background: #BDA169;
    text-align: center;
    width: 400px;
    height: 48px;
    line-height: 48px;
    color: #FFFFFF;
    font-size: 24px;
    cursor: pointer;
    margin: 0 auto;
    margin-top: 20px;
  }
  &-button_disagree {
    text-align: center;
    color: #8E8E8E;
    font-size: 16px;
    cursor: pointer;
    margin-top: 10px;
  }
}
.title-prompt {
  color: #567485;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 30px;
}
.content-prompt {
  color: #567485;
  font-size: 24px;
  font-weight: 400;
  text-align: center;
}
.buttom-prompt {
  margin-top: 30px;
  div {
    text-align: center;
    color: #FFFFFF;
    height: 50px;
    width: 140px;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-size: 20px;
    line-height: 50px;
  }
  &_cancel {
    background: #567485;
    margin-right: 30px;
  }
  &_sure {
    background: #B4995A;
  }
}
.two-page {
  height: 550px;
  text-align: center;
  &_close {
    color: #000000;
    cursor: pointer;
    font-size: 22px;
    position: absolute;
    top: 0;
    right: 12px;
  }
  &_icon {
    padding-top: 30px;
  }
  &_title {
    color: #1F1F1F;
    font-size: 24px;
    font-weight: 500;
    margin-top: 20px;
  }
  &_tag {
    font-size: 16px;
    color: #4B4B4B;
    font-weight: 400;
    margin-top: 20px;
    margin-bottom: 20px;
    &__number {
      display: inline-block;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #F5F5F5;
      color: #8E8E8E;
    }
    &__text {
      display: inline-block;
      padding-left: 6px;
    }
    &__line {
      display: inline-block;
      padding: 0 10px;
    }
  }
  &_img {
    width: 222px;
    height: 234px;
    border-radius: 16px;
    background: #F5F5F5;
    padding: 32px 20px;
    text-align: center;
    margin: 0 auto;
    &__qrcode {
      width: 160px;
      height: 160px;
      margin-left: 11px;
    }
    &__tag {
      font-size: 14px;
      font-weight: 400;
      color: #BDA169;
      margin-top: 6px;
      text-align: center;
    }
  }
}
.refresh-button {
  margin: 0 auto;
  margin-top: 20px;
  background: #BDA169;
  color: #FFFFFF;
  text-align: center;
  cursor: pointer;
  width: 50px;
  height: 32px;
  line-height: 32px;
  border-radius: 6px;
  font-size: 16px;
}
.loading-div {
  width: 200px;
  height: 200px;
  border-radius: 16px;
  background: #F5F5F5;
  margin: 0 auto;
}
.loading-i {
  font-size: 60px;
  line-height: 200px;
}
</style>
<template>
  <div v-if="showDialog">
    <div class="pc_popup">
      <div class="pc_popup_lg">
        <div v-if="showOnePage">
          <div class="content">
            <div class="top-title">
              短信服务用户告知书
            </div>
            <div style="padding-left: 10px;margin-top: -30px;">
        依据《中华人民共和国民法典》及《互联网信息服务管理办法》等相关法律法规的规定，用户不利用短信群发功能制作、复制、发布、传播含有下列内容的信息：
1. 违反宪法所确定的基本原则的；
2. 危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；
3. 损坏国家荣誉和利益的；
4. 煽动民族仇恨、民族歧视，破坏民族团结的；
5. 破坏国家民族宗教政策，宣扬邪教和封建迷信的；
6. 散布谣言，扰乱社会秩序，破坏社会稳定的；
7. 散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；
8. 侮辱或者诽谤他人，侵害他人合法权益的；
9. 含有法律、法规禁止的其他内容的；
10. 与实际业务不相关的内容的。
        发送内容不得违反《中华人民共和国电信条例》、《中国移动短信类集团信息化产品业务使用协议》等的有关政策、法规、法令，严格禁止违反国家法律法规、损害国家利益、社会公共利益及乙方利益的信息发布或传播。
        若用户违反本告知书约定发送违规信息，则平台有权拒绝提供本服务，视情况暂停或停止用户的使用权，同时，由于用户违反行为造成平台短信通道关闭风险，若平台因此遭受损失（包括但不限于为主张权利所支出的诉讼费、律师费、公证费、担保费、差旅费等费用），用户应全部赔偿，平台仍有权视情况决定是否将用户相关软件使用方列入黑名单。
            </div>
            <div class="top-title" style="margin-top: -60px; ">
              用户承诺书
            </div>
            <div style="padding-left: 10px;margin-top: -30px;">
              我承诺已认真阅读短信服务用户告知，并遵守相关法律、法规及平台相关规定，如有违反承担相应的责任包括但不限于刑事、行政处理以及赔偿平台的损失。
            </div>
          </div>
          <div class="bottom">
            <div class="bottom-flex">
              <div class="bottom-radio">
                <el-checkbox v-model="agreeCkeck"></el-checkbox>
              </div>
              <div class="bottom-text">
                我已认真阅读以上内容，我将全权代表本店铺全员遵守平台短信服务相关规定，承担相应的法律责任。
              </div>
            </div>
            <div class="bottom-button_agree" @click="agree()">{{ oldRegister? '同意' : '同意并继续' }}</div>
            <div class="bottom-button_disagree" @click="disagree()">不同意并关闭所有短信通知</div>
          </div>
        </div>
        <div v-else class="two-page">
          <div class="two-page_close" @click="detectinfoCheck('close')">×</div>
          <div class="two-page_icon">
            <img src="../image/pc_sms_icon.png" />
          </div>
          <div class="two-page_title">
            即时短信通知，轻松留住客户
          </div>
          <div class="two-page_tag">
            <div class="two-page_tag__number">1</div>
            <div class="two-page_tag__text">扫码开通短信服务</div>
            <div class="two-page_tag__line">
              <svg width="28" height="2" viewBox="0 0 28 2" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line y1="1" x2="28" y2="1" stroke="#E1E1E1"/>
              </svg>
            </div>
            <div class="two-page_tag__number">2</div>
            <div class="two-page_tag__text">人脸识别认证</div>
          </div>
          <div v-if="!isSMSErr" class="two-page_img">
            <div class="qrcode two-page_img__qrcode" ref="qrCodeUrl"></div>
            <div class="two-page_img__tag">
              请使用微信扫一扫，在线开通
            </div>
          </div>
          <div v-else>
            <img v-if="!isLoading" src="../image/pc_sms_err.png">
            <div v-else class="loading-div">
              <i class="el-icon-loading loading-i"></i>
            </div>
            <div @click="refresh" class="refresh-button">刷新</div>
          </div>
        </div>
      </div>
    </div>
    <div class="pc_popup" style="z-index: 31" v-if="isCloseMpt">
      <div class="pc_popup_lg" style="width: 470px!important;height: 290px;padding: 20px 70px;">
        <div style="cursor: pointer;text-align: right;margin-right: -40px;" @click="isCloseMpt = false">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1.55537 0L0.141159 1.41421L6.64165 7.9147L0 14.5563L1.41421
              15.9706L8.05586 9.32891L14.2833 15.5563L15.6975 14.1421L9.47007 7.9147L15.5563 1.82843L14.1421
              0.414214L8.05586 6.50049L1.55537 0Z" fill="#567485"/>
          </svg>
        </div>
        <div class="title-prompt">提示</div>
        <div class="content-prompt">若不同意，短信通知相关功能将无法使用</div>
        <div class="buttom-prompt">
          <div class="buttom-prompt_cancel" @click="isCloseMpt = false">取消</div>
          <div class="buttom-prompt_sure" @click="sureDisagree()">确定</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import QRCode from 'qrcodejs2';
export default {
  props: {
    isLogin: {
      type: Boolean,
      default: false
    },
    fromType: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      showDialog: false,
      agreeCkeck: false,
      isCloseMpt: false,
      noticeArr: [
        { key: 'short_balance_notice', notice_title: '账户变动短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12消费57元，余120元，余298积分', flag: false },
        { key: 'short_integral_change_notice', notice_title: '积分兑换短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12进行了积分兑换，共使用99积分', flag: false },
        { key: 'short_vip_card_notice', notice_title: '次卡消息短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12购买了(摇摇车)次卡，共1张', flag: false }
      ],
      showOnePage: true,
      qrcode: null,
      oldRegister: true,
      isSMSErr: false,
      isLoading: false
    };
  },
  mounted() {
    this.getValues();
  },
  methods: {
    refresh() {
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
      }, 1000)
      this.detectinfoCheck('init')
    },
    registerDateCheck() {
      const param = {
        type: this.fromType
      };
      demo.$http.get(this.$rest.qrCodePopup, {
        params: param
      })
        .then(rs => {
          const obj = rs.data;
          if (obj.data) {
            this.oldRegister = false;
            if (!this.showDialog) {
              this.detectinfoCheck('init');
            }
          } else {
            this.oldRegister = true;
          }
        });
    },
    detectinfoCheck(type) {
      const params = {
        systemName: $config.systemName,
        subName: $config.subName,
        phone: $userinfo.sysUid
      }
      demo.$http.post(this.$rest.detectinfoCheck, params)
        .then(res => {
          if (res.data.code === 200) {
            if (res.data.data.flag) {
              this.showDialog = false;
              if (type === 'close') {
                this.$emit('sureSMS');
              }
            } else {
              if (type === 'init') {
                this.isLoading = false;
                this.showDialog = true;
                this.showOnePage = false;
                setTimeout(() => {
                  this.creatQrCode();
                }, 0);
              } else {
                this.close()
              }
            }
          } else {
            if (type === 'close') {
              this.close();
            } else {
              this.showErr();
            }
          }
        }).catch(() => {
          if (type === 'close') {
            this.close();
          } else {
            this.showErr();
          }
        });
    },
    showErr() {
      this.showDialog = true;
      this.showOnePage = false;
      this.isSMSErr = true;
    },
    creatQrCode() {
      var qr_data = {
        text:
          this.$rest.detectinfo +
          '?key=' +
          window.btoa(
            $userinfo.sysUid + '&' + $config.subName + '&' + $config.systemName
          ),
        width: 160,
        height: 160,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.qrcode = new QRCode(this.$refs.qrCodeUrl, qr_data);
    },
    ...mapActions([SET_SHOW]),
    close() {
      this.showDialog = false;
      this.$emit('closeSMS');
    },
    // 获取短信确认值
    getValues() {
      const param = {
        'key': 'sms_confirm'
      };
      demo.$http.get(this.$rest.getValues, {
        params: param
      })
        .then(rs => {
          const obj = rs.data;
          if (!demo.isNullOrTrimEmpty(obj.data) && obj.data === 'true') {
            if (this.isLogin) {
              this.getNoticeFlag();
            } else {
              this.searchSMS();
            }
          }
        });
    },
    // 获取会员短信发送设置开关状态
    getNoticeFlag() {
      this.SET_SHOW({isOpenSMS: false});
      const param = {
        'systemName': $config.systemName,
        'phone': $userinfo.sysUid,
        'sysSid': $userinfo.sysSid
      };
      demo.$http.post(this.$rest.pc_vipGetSetting, param)
        .then(rs => {
          const obj = rs.data;
          if (obj.code === '0') {
            if (obj.data && obj.data.notifySetting) {
              const notifySetting = demo.t2json(obj.data.notifySetting);
              if (notifySetting.short_balance_notice || notifySetting.short_integral_change_notice || notifySetting.short_vip_card_notice) {
                this.searchSMS();
              }
            }
          }
        });
    },
    searchSMS() { // 查询短信发送风险授权记录
      const param = {
        systemName: $config.systemName,
        sysUid: $userinfo.sysUid,
        subName: $config.subName
      }
      demo.$http.post(this.$rest.getOne, param)
        .then(res => {
          if (demo.isNullOrTrimEmpty(res.data.data)) {
            this.showDialog = true;
          }
          if (this.fromType !== 0) {
            this.getPublicConfig();
          }
        })
        .catch(err => {
          console.log(err, 'err');
        });
    },
    // 获取人脸核身的开关
    getPublicConfig() {
      const param = {
        key: 'Base,OtherOptions,enableFaceDetection'
      }
      this.$http.get(this.$rest.getPublicConfig, {
        params: param
      }).then(res => {
        if (res.data.data === '1') {
          this.registerDateCheck();
        }
      }).catch(err => {
        console.log(err, 'err');
      });
    },
    upLoadPushMsg () { // 用户拒绝授权短信发送风险后关闭会员短信发送开关
      const notifySetting = Object.assign(...this.noticeArr.map(notice => {
        let r = {};
        r[notice.key] = notice.flag;
        return r;
      }));
      const param = {
        'systemName': $config.systemName,
        'phone': $userinfo.sysUid,
        'sysSid': $userinfo.sysSid,
        'notifySetting': notifySetting,
        'createUser': $userinfo.sysUid
      };
      demo.$http.post(this.$rest.pc_settingVip, param)
        .then(rs => {
          const obj = rs.data;
          if (obj.code !== '0') {
            CefSharp.PostMessage("用户点击不同意关闭短信通知开关");
          }
        });
    },
    agree() { // 用户同意短信风险授权协议
      if (!this.agreeCkeck) {
        demo.msg('warning', '请先勾选已阅读相关内容');
        return;
      }
      if (!pos.network.isConnected()) {
        demo.msg('warning', '请检查网络是否连接');
      } else {
        let param = {
          systemName: $config.systemName,
          sysUid: this.sys_uid,
          subName: $config.subName,
          employeeNumber: this.loginInfo.employeeNumber
        };
        demo.$http.post(this.$rest.agreeShort, param)
          .then(res => {
            if (res.data && res.data.code === 200) {
              if (this.oldRegister) {
                this.showDialog = false;
                this.$emit('sureSMS');
              } else {
                this.showOnePage = false;
                setTimeout(() => {
                  this.creatQrCode();
                }, 0);
              }
              demo.actionLog(logList.agreeContract);
              CefSharp.PostMessage("用户点击同意短信服务用户告知书");
            } else {
              CefSharp.PostMessage("用户点击同意短信服务用户告知书报错：" + JSON.stringify(res.data));
              demo.msg('warning', res.data.data);
            }
          })
          .catch(err => {
            demo.msg('warning', err);
          });
      }
    },
    disagree() { // 用户不同意短信风险授权协议
      if (pos.network.isConnected()) {
        this.isCloseMpt = true;
      } else {
        demo.actionLog(logList.disagreeContract);
        CefSharp.PostMessage("用户点击不同意短信服务用户告知书");
        this.showDialog = false;
        this.$emit('closeSMS');
      }
    },
    sureDisagree() { // 用户不同意短信风险授权协议时log记录及功能关闭处理
      demo.actionLog(logList.disagreeContract);
      CefSharp.PostMessage("用户点击不同意短信服务用户告知书");
      this.upLoadPushMsg();
      this.showDialog = false;
      this.$emit('closeSMS');
    }
  },
  computed: {
    ...mapState({
      sys_uid: state => state.show.sys_uid,
      loginInfo: state => state.show.loginInfo
    })
  }
};
</script>
