import _ from 'lodash';
import rest from '../../config/rest';
import dao from '../dao/dao';
import { StringBuilder } from '../stringUtils';
import clerkService from './clerkService';
import superSyncService from './superSyncService';

const syncService = {

  syncId: '',
  syncUpDate: '',
  syncUpCnt: 0,
  syncDownDate: '',
  minStartSyncAt: '2000-01-01 00:00:00',
  startSyncAt: '',
  endSyncAt: '',
  // 回拨时间：30分钟
  backMinutes: 30,
  backStartSyncAt: '',
  goodsUpCnt: 500,
  getUpFuncs: function (type) {
    if (type === 'pos') {
      return [this.syncSuppliersUp, this.syncInventoriesUp, this.syncPurchasesUp, this.syncSalesUp, this.syncRecordBillsUp];
    }
    if (type === 'pos-sales') {
      return [this.syncSalesUp];
    }
    return [this.syncStoreInfoUp, this.syncTypesUp, this.syncUnitsUp, this.syncSuppliersUp, this.syncGoodsUp,
      this.syncGoodsExtBarcodeUp, this.syncGoodsSuppliersUp, this.syncInventoriesUp, this.syncPurchasesUp,
      this.syncSalesUp, this.syncShiftHistoriesUp, this.syncRecordBillsUp, this.syncProductScaleUp, this.syncSendscaleUp,
      this.syncSendscaleProductUp, this.syncSendscaleHistoryUp];
  },

  /**
   * storeInfo上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncStoreInfoUp: function (onSuccess, onFail) {
    var that = this;
    console.log(this, that, this.syncUpDate, that.syncUpDate);
    dao.exec(syncSqlApi.syncGetStoreInfo.format(that.syncUpDate), storeInfos => {
      if (storeInfos.length === 0) {
        onSuccess();
      } else {
        var ids = _.map(storeInfos, 'id');
        demo.$http.post(rest.upSysInfo, storeInfos, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateStoreInfo.format(ids.join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },

  /**
   * types上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncTypesUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetTypes.format(that.syncUpDate), types => {
      if (types.length === 0) {
        onSuccess();
      } else {
        var ids = _.map(types, 'id');
        demo.$http.post(rest.upTypes, types, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateTypes.format(ids.join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },

  /**
   * units上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncUnitsUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetUnits.format(that.syncUpDate), units => {
      if (units.length === 0) {
        onSuccess();
      } else {
        var ids = _.map(units, 'id');
        demo.$http.post(rest.upUnits, units, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateUnits.format(ids.join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },

  /**
   * 供应商上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncSuppliersUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetSuppliers.format(that.syncUpDate), suppliers => {
      if (suppliers.length === 0) {
        onSuccess();
      } else {
        demo.$http.post(rest.upCompanies, suppliers, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateSuppliers.format(_.map(suppliers, 'id').join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },

  /**
   * goods上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncGoodsUp: function () {
    return new Promise((resolve, reject) => {
      const params = {
        syncAt: this.syncUpDate,
        limit: this.goodsUpCnt,
        offset: 0
      };

      this.syncGoodsLoop(params, resolve, reject);
    });
  },

  syncGoodsLoop: function (params, onSuccess, onFail) {
    let upGoods;
    this.getUpGoods(params)
      .then(res => {
        if (res === 0) {
          onSuccess();
          return;
        }

        res.picsList = [];
        upGoods = res;
        return this.syncGoodsDoUp(res);
      })
      .then(res => {
        return this.syncGoodsUpdate(upGoods, res);
      })
      .then(() => {
        this.syncGoodsLoop(params, onSuccess, onFail);
      })
      .catch(onFail);
  },

  getUpGoods: function (params) {
    return new Promise((resolve, reject) => {
      const res = {};
      let goodsLength = 0;
      dao.asyncExec(syncSqlApi.syncGetGoods.format(params))
        .then(goods => {
          res.productsList = goods;
          goodsLength = goods.length;
          return dao.asyncExec(syncSqlApi.syncGetGoodsAttributes.format(params.syncAt));
        })
        .then(attributes => {
          res.productAttributeList = attributes;
          if (goodsLength === 0 && attributes.length === 0) {
            resolve(0);
            return;
          }
          resolve(res);
        })
        .catch(reject);
    });
  },

  syncGoodsDoUp: function (params) {
    return new Promise((resolve, reject) => {
      demo.$http.post(rest.upProducts, params, {
        maxContentLength: Infinity,
        timeout: 300000
      })
        .then(resolve)
        .catch(reject);
    });
  },

  syncGoodsUpdate: function (params, res) {
    return new Promise((resolve, reject) => {
      const data = res.data;
      if (+data.code !== 200) {
        reject(res);
        return;
      }

      this.syncUpCnt = 1;
      const goodIds = _.map(params.productsList, 'id');
      const attributeIds = _.map(params.productAttributeList, 'id');
      const sql = new StringBuilder();
      sql
        .append(syncSqlApi.syncUpdateGoods.format(goodIds.join(), this.syncUpDate))
        .append(syncSqlApi.syncUpdateAttributes.format(attributeIds.join(), this.syncUpDate));

      dao.transaction(sql.toString(), resolve, reject);
    });
  },

  /**
   * goodsExtBarcode 上传
   */
  syncGoodsExtBarcodeUp(onSuccess, onFail) {
    const that = this;
    dao.asyncExec(syncSqlApi.syncGetGoodsExtBarcode.format(that.syncUpDate)).then(goodsExtBarCode => {
      if (goodsExtBarCode.length === 0) {
        onSuccess();
        return;
      }
      const goodsExtBarcodeIds = _.map(goodsExtBarCode, 'id');
      demo.$http.post(rest.upProductsExtBarcode, goodsExtBarCode, {
        maxContentLength: Infinity,
        timeout: 300000
      })
        .then(res => {
          if (+res.data.code === 200) {
            that.syncUpCnt = 1;
            const sql = syncSqlApi.syncUpdateGoodsExtBarcode.format(goodsExtBarcodeIds.join(), that.syncUpDate);
            dao.transaction(sql, onSuccess, onFail);
          } else {
            onSuccess(res);
          }
        })
        .catch(onFail);
    }).catch(onFail);
  },

  /**
   * goodsSuppliers上传
   * @returns
   */
  syncGoodsSuppliersUp: function() {
    return new Promise((resolve, reject) => {
      const params = {
        syncAt: this.syncUpDate,
        limit: 200
      };
      this.syncGoodsSuppliersLoop(params, resolve, reject);
    });
  },

  syncGoodsSuppliersLoop: function (params, onSuccess, onFail) {
    let cnt;
    dao.asyncExec(syncSqlApi.syncGetGoodsSuppliers.format(params))
      .then(res => {
        cnt = res.length;
        if (cnt === 0) {
          return;
        }

        return this.syncGoodsSuppliersDoUp(params.syncAt, res);
      })
      .then(() => {
        if (cnt < params.limit) {
          onSuccess();
          return;
        }

        this.syncGoodsSuppliersLoop(params, onSuccess, onFail);
      })
      .catch(onFail);
  },

  syncGoodsSuppliersDoUp: function(syncAt, productCompaniesList) {
    return new Promise((resolve, reject) => {
      demo.$http.post(rest.upProductCompanies, {
        syncAt,
        productCompaniesList
      }, {
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(res => {
          const data = res.data;
          if (+data.code === 200) {
            this.syncUpCnt = 1;
            const ids = _.map(productCompaniesList, 'id').join();
            dao.exec(syncSqlApi.syncUpdateGoodsSuppliers.format(ids, this.syncUpDate), resolve, reject);
          } else {
            reject(data.msg);
          }
        })
        .catch(reject);
    });
  },

  /**
   * inventories上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncInventoriesUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetInventories.format(that.syncUpDate), inventories => {
      if (inventories.length === 0) {
        onSuccess();
        return;
      }

      var invFingerprints = _.map(inventories, 'syncG');
      var invFingerprintsParam = '\'' + invFingerprints.join('\',\'') + '\'';
      dao.exec(syncSqlApi.syncGetInventoryItems.format(invFingerprintsParam, that.syncUpDate), inventoryItems => {
        if (inventoryItems.length === 0) {
          onSuccess();
          return;
        }

        var itemsGroup = _.groupBy(inventoryItems, 'inventorySyncG');
        inventories.forEach(inv => {
          var items = itemsGroup[inv.syncG];
          if (items === undefined) {
            inv.items = [];
          } else {
            inv.items = items;
          }
        });

        demo.$http.post(rest.upInventories, inventories, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            that.syncUpCnt = 1;
            var data = res.data;
            var idList = _.map(inventories, 'id');
            var validIdList = data.subData.validIdList;
            var invalidIdList = _.difference(idList, validIdList);

            var sql = '';
            if (validIdList.length > 0) {
              sql += syncSqlApi.syncUpdateInventories.format(validIdList.join(), that.syncUpDate, 1);
            }
            if (invalidIdList.length > 0) {
              sql += syncSqlApi.syncUpdateInventories.format(invalidIdList.join(), that.syncUpDate, 2);
            }

            dao.transaction(sql, onSuccess, onFail);
          })
          .catch(onFail);
      }, onFail);
    }, onFail);
  },

  /**
   * sales上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncSalesUp() {
    return new Promise((resolve, reject) => {
      this.upSaleUpdateParams = {
        salesIds: [],
        inValidIdList: []
      };

      const params = {
        syncAt: this.syncUpDate,
        limit: rest.syncUpCount,
        offset: 0
      };
      this.syncSalesLoop(params, () => {
        delete this.upSaleUpdateParams;
        resolve();
      }, err => {
        delete this.upSaleUpdateParams;
        reject(err);
      });
    });
  },
  syncSalesLoop(params, onSuccess, onFail) {
    const params1 = {
      syncAt: this.syncUpDate
    };
    this.getUpSales(params).then(res => {
      Object.assign(params1, res);
      if (params1.sales !== undefined) {
        return this.syncSalesDoUp(params1);
      }
    }).then(() => {
      if (params1.sales !== undefined) {
        this.upSaleUpdateParams.salesIds =
          this.upSaleUpdateParams.salesIds.concat(params1.sales.map(i => { return i.id; }));
      } else if (params.offset === 0) {
        onSuccess();
        return;
      }
      if (params1.sales === undefined || params1.sales.length < params.limit) {
        this.upSalesUpdate(this.upSaleUpdateParams).then(onSuccess).catch(onFail);
        return;
      }

      params.offset += params.limit;
      this.syncSalesLoop(params, onSuccess, onFail);
    }).catch(err => {
      this.upSalesUpdate(this.upSaleUpdateParams).then(() => {
        onFail(err);
      }).catch(err1 => {
        onFail(err, err1);
      });
    });
  },
  getUpSales(params) {
    return new Promise((resolve, reject) => {
      const data = {};
      this.getSales(params).then(sales => {
        if (sales.length === 0) {
          resolve(data);
          return;
        }

        data.sales = sales;
        return this.getSaleItems(params);
      }).then(saleItems => {
        data.saleItems = saleItems;
        return this.getSaleBlendPays(params);
      }).then(saleBlendPays => {
        data.saleBlendPays = saleBlendPays;
        resolve(data);
      }).catch(reject);
    });
  },
  getSales(params) {
    return new Promise((resolve, reject) => {
      dao.exec(syncSqlApi.getSales.format(params), resolve, reject);
    });
  },
  getSaleItems(params) {
    return new Promise((resolve, reject) => {
      dao.exec(syncSqlApi.getSaleItems.format(params), resolve, reject);
    });
  },
  getSaleBlendPays(params) {
    return new Promise((resolve, reject) => {
      dao.exec(syncSqlApi.getSaleBlendPays.format(params), resolve, reject);
    });
  },
  syncSalesDoUp(params) {
    return new Promise((resolve, reject) => {
      demo.$http.post(rest.upSales, params, {
        maxContentLength: Infinity,
        timeout: 60000
      }).then(res => {
        const data = res.data;
        if (+data.code === 200) {
          this.syncUpCnt = 1;
          if (!demo.isNullOrTrimEmpty(data.inValidIdList)) {
            this.upSaleUpdateParams.inValidIdList =
              this.upSaleUpdateParams.inValidIdList.concat(data.inValidIdList.map(i => { return i.id; }));
          }
          resolve();
        } else {
          reject(data.msg);
        }
      }).catch(reject);
    });
  },
  upSalesUpdate(params) {
    return new Promise((resolve, reject) => {
      if (params.salesIds.length === 0) {
        resolve();
        return;
      }
      let validIdList = _.difference(params.salesIds, params.inValidIdList);
      let count;
      let start;
      let end;
      let ids = params.salesIds;
      let loopIds;
      const params1 = {
        syncAt: this.syncUpDate
      };
      const sbd = new StringBuilder();
      count = Math.ceil(ids.length / rest.syncUpCount);
      for (let j = 0; j < count; j++) {
        start = rest.syncUpCount * j;
        end = rest.syncUpCount * (j + 1);
        loopIds = validIdList.slice(start, end);
        params1.salesIds = loopIds.join();
        params1.isSynced = 1;
        sbd.append(syncSqlApi.updateSales.format(params1));
      }
      if (!demo.isNullOrTrimEmpty(params.inValidIdList)) {
        params1.salesIds = params.inValidIdList.join();
        params1.isSynced = 2;
        sbd.append(syncSqlApi.updateSales.format(params1));
      }
      dao.transaction(sbd.toString(), resolve, reject);
    });
  },

  /**
   * purchases上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncPurchasesUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetPurchases.format(that.syncUpDate), purchases => {
      if (purchases.length === 0) {
        onSuccess();
        return;
      }

      var purFingerprints = _.map(purchases, 'syncG');
      var purFingerprintsParam = '\'' + purFingerprints.join('\',\'') + '\'';
      dao.exec(syncSqlApi.syncGetPurItems.format(purFingerprintsParam, that.syncUpDate), purItems => {
        if (purItems.length === 0) {
          onSuccess();
          return;
        }

        var itemsGroup = _.groupBy(purItems, 'purSyncG');
        purchases.forEach(pur => {
          var items = itemsGroup[pur.syncG];
          if (demo.isNullOrTrimEmpty(items)) {
            pur.items = [];
          } else {
            pur.items = items;
          }
        });

        demo.$http.post(rest.upPurs, purchases, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            that.syncUpCnt = 1;
            var data = res.data;
            var idList = _.map(purchases, 'id');
            var validIdList = data.subData.validIdList;
            var invalidIdList = _.difference(idList, validIdList);

            var sql = '';
            if (validIdList.length > 0) {
              sql += syncSqlApi.syncUpdatePurchases.format(validIdList.join(), that.syncUpDate, 1);
            }
            if (invalidIdList.length > 0) {
              sql += syncSqlApi.syncUpdatePurchases.format(invalidIdList.join(), that.syncUpDate, 2);
            }

            dao.transaction(sql, onSuccess, onFail);
          })
          .catch(onFail);
      }, onFail);
    }, onFail);
  },

  /**
   * 交接班明细上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncShiftHistoriesUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetShiftHistories.format(that.syncUpDate), shiftHistories => {
      if (shiftHistories.length === 0) {
        onSuccess();
      } else {
        demo.$http.post(rest.upShiftHistories, shiftHistories, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateShiftHistories.format(_.map(shiftHistories, 'id').join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },

  /**
   * 挂单上传
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncRecordBillsUp: function (onSuccess, onFail) {
    var that = this;
    dao.exec(syncSqlApi.syncGetRecordBills.format(that.syncUpDate), recordBills => {
      if (recordBills.length === 0) {
        onSuccess();
      } else {
        demo.$http.post(rest.upRecordBills, recordBills, {
          maxContentLength: Infinity,
          timeout: 300000
        })
          .then(res => {
            var data = res.data;
            if (+data.code === 200) {
              that.syncUpCnt = 1;
              dao.transaction(syncSqlApi.syncUpdateRecordBills.format(_.map(recordBills, 'id').join(), that.syncUpDate), onSuccess, onFail);
            } else {
              onFail(res);
            }
          })
          .catch(onFail);
      }
    }, onFail);
  },
  syncProductScaleUp: function (onSuccess, onFail) {
    var that = this;
    var scale = 'product_scale';
    dao.exec(sqlApi.isExist.format(scale), res => {
      if (res[0].cnt > 0) {
        dao.exec(syncSqlApi.syncGetProductScale.format(that.syncUpDate), productScale => {
          if (productScale.length === 0) {
            onSuccess();
          } else {
            demo.$http.post(rest.upProductScale, productScale, {
              maxContentLength: Infinity,
              timeout: 300000
            })
              .then(res => {
                var data = res.data;
                if (+data.code === 200) {
                  that.syncUpCnt = 1;
                  dao.transaction(syncSqlApi.syncUpdateProductScale.format(_.map(productScale, 'id').join(), that.syncUpDate), onSuccess, onFail);
                } else {
                  onFail(res);
                }
              })
              .catch(onFail);
          }
        }, onFail);
      } else {
        onSuccess();
      }
    }, onFail);
  },
  syncSendscaleProductUp: function (onSuccess, onFail) {
    var that = this;
    var scale = 'sendscale_product';
    dao.exec(sqlApi.isExist.format(scale), res => {
      if (res[0].cnt > 0) {
        dao.exec(syncSqlApi.syncGetSendscaleProduct.format(that.syncUpDate), sendscaleProduct => {
          if (sendscaleProduct.length === 0) {
            onSuccess();
          } else {
            demo.$http.post(rest.upSendscaleProduct, sendscaleProduct, {
              maxContentLength: Infinity,
              timeout: 300000
            })
              .then(res => {
                var data = res.data;
                if (+data.code === 200) {
                  that.syncUpCnt = 1;
                  dao.transaction(syncSqlApi.syncUpdateSendscaleProduct.format(_.map(sendscaleProduct, 'id').join(), that.syncUpDate), onSuccess, onFail);
                } else {
                  onFail(res);
                }
              })
              .catch(onFail);
          }
        }, onFail);
      } else {
        onSuccess();
      }
    }, onFail);
  },
  syncSendscaleHistoryUp: function (onSuccess, onFail) {
    var that = this;
    var scale = 'sendscale_history';
    dao.exec(sqlApi.isExist.format(scale), res => {
      if (res[0].cnt > 0) {
        dao.exec(syncSqlApi.syncGetSendscaleHistory.format(that.syncUpDate), sendscaleHistory => {
          if (sendscaleHistory.length === 0) {
            onSuccess();
          } else {
            demo.$http.post(rest.upSendscaleHistory, sendscaleHistory, {
              maxContentLength: Infinity,
              timeout: 300000
            })
              .then(res => {
                var data = res.data;
                if (+data.code === 200) {
                  that.syncUpCnt = 1;
                  dao.transaction(syncSqlApi.syncUpdateSendscaleHistory.format(_.map(sendscaleHistory, 'id').join(), that.syncUpDate), onSuccess, onFail);
                } else {
                  onFail(res);
                }
              })
              .catch(onFail);
          }
        }, onFail);
      } else {
        onSuccess();
      }
    }, onFail);
  },
  syncSendscaleUp: function (onSuccess, onFail) {
    var that = this;
    var scale = 'sendscale';
    dao.exec(sqlApi.isExist.format(scale), res => {
      if (res[0].cnt > 0) {
        dao.exec(syncSqlApi.syncGetSendscale.format(that.syncUpDate), sendscale => {
          if (sendscale.length === 0) {
            onSuccess();
          } else {
            demo.$http.post(rest.upSendscale, sendscale, {
              maxContentLength: Infinity,
              timeout: 300000
            })
              .then(res => {
                var data = res.data;
                if (+data.code === 200) {
                  that.syncUpCnt = 1;
                  dao.transaction(syncSqlApi.syncUpdateSendscale.format(_.map(sendscale, 'id').join(), that.syncUpDate), onSuccess, onFail);
                } else {
                  onFail(res);
                }
              })
              .catch(onFail);
          }
        }, onFail);
      } else {
        onSuccess();
      }
    }, onFail);
  },
  syncDown: function (tbl, callBack, onSuccess, onFail, cnt, pageCount) {
    let sql = '';
    if (cnt === undefined) {
      cnt = 1;
      pageCount = Math.ceil(tbl.cnt / rest.syncDownCount);
      sql += tbl.sqlTruncate;
    }
    this.commonDownByPage(tbl, cnt, callBack, res => {
      sql += demo.isNullOrTrimEmpty(res) ? '' : res;
      if (cnt === pageCount) {
        dao.transaction(sql + tbl.sqlExec, onSuccess, onFail);
      } else {
        dao.transaction(sql, () => {
          this.syncDown(tbl, callBack, onSuccess, onFail, ++cnt, pageCount);
        }, onFail);
      }
    }, onFail);
  },

  commonDownByPage: function (params, currentPage, callBack, onSuccess, onFail) {
    demo.$http.post(params.url, {
      currentPage: currentPage,
      pageSize: rest.syncDownCount,
      startSyncAt: params.tbl === 'sales' ? this.backStartSyncAt : params.startSyncAt,
      endSyncAt: this.syncUpDate
    }, {
      maxContentLength: Infinity,
      timeout: 300000
    })
      .then(res => {
        if (
          params.url === rest.downTypesNew &&
          res.data.code === 200 &&
          res.data.data &&
          res.data.data.length > 0
        ) {
          res.data.data.forEach(item => {
            item.sortNo = item.sortNo === '' ? null : Number(item.sortNo)
          })
        }
        if (+res.data.code === 200) {
          var data = res.data.data;

          if (data.length > 0) {
            callBack(params, data, onSuccess);
          } else {
            onSuccess();
          }
        } else {
          onFail(res);
        }
      })
      .catch(onFail);
  },

  commonDownOne: function (params, data, onSuccess) {
    var sql = params.sqlTmpInsert;

    var length = data.length;
    for (var i = 0; i < length; i++) {
      sql += params.sqlTmpValues.format(demo.sqlConversion(data[i]));
      if (i === length - 1) {
        sql += ';';
      } else if ((i + 1) % sql_count === 0) {
        sql += ';' + params.sqlTmpInsert;
      } else {
        sql += ',';
      }
    }

    onSuccess(sql);
  },
  commonDownTwo: function (params, data, onSuccess) {
    let syncTmpInsert = params.sqlTmpInsert;
    let syncTmpItemsInsert = params.sqlTmpItemsInsert;
    let syncTmpBlendPaysInsert = params.sqlTmpBlendInsert;

    let dataLength = data.length;
    let itemCnt = 0;
    let blendCnt = 0;
    for (let i = 0; i < dataLength; i++) {
      let datum = data[i];
      syncTmpInsert += params.sqlTmpValues.format(demo.sqlConversion(datum));
      if (i === dataLength - 1) {
        syncTmpInsert += ';';
      } else if ((i + 1) % sql_count === 0) {
        syncTmpInsert += ';' + params.sqlTmpInsert;
      } else {
        syncTmpInsert += ',';
      }

      let items = datum.items;
      let itemsLength = items.length;
      for (let j = 0; j < itemsLength; j++) {
        let saleItem = items[j];
        syncTmpItemsInsert += params.sqlTmpItemsValues.format(demo.sqlConversion(saleItem));
        itemCnt++;
        if (i === dataLength - 1 && j === itemsLength - 1) {
          syncTmpItemsInsert += ';';
        } else if (itemCnt % sql_count === 0) {
          syncTmpItemsInsert += ';' + params.sqlTmpItemsInsert;
        } else {
          syncTmpItemsInsert += ',';
        }
      }

      if (params.sqlTmpBlendInsert !== undefined) {
        let blendPays = datum.blendPays;
        let blendPaysLength = blendPays.length;
        for (let j = 0; j < blendPaysLength; j++) {
          let blend = blendPays[j];
          syncTmpBlendPaysInsert += params.sqlTmpBlendValues.format(demo.sqlConversion(blend));
          blendCnt++;
          if (blendCnt % sql_count === 0) {
            syncTmpBlendPaysInsert += ';' + params.sqlTmpBlendInsert;
          } else {
            syncTmpBlendPaysInsert += ',';
          }
        }
      }
    }

    syncTmpInsert = dataLength === 0 ? '' : syncTmpInsert;
    syncTmpItemsInsert = itemCnt === 0 ? '' : syncTmpItemsInsert;
    syncTmpBlendPaysInsert = blendCnt === 0 ? '' : syncTmpBlendPaysInsert.substr(0, syncTmpBlendPaysInsert.length - 1) + ';';

    onSuccess(syncTmpInsert + syncTmpItemsInsert + syncTmpBlendPaysInsert);
  },
  tblParams: {
    sysinfo: {
      url: rest.downSysInfo,
      sqlTruncate: syncSqlApi.syncTmpStoreInfoTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpStoreInfoInsert,
      sqlTmpValues: syncSqlApi.syncTmpStoreInfoInsertValues,
      sqlExec: syncSqlApi.syncStoreInfoUpdate + syncSqlApi.syncStoreInfoInsert
    },
    types: {
      url: rest.downTypesNew,
      sqlTruncate: syncSqlApi.syncTmpTypesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpTypesInsert,
      sqlTmpValues: syncSqlApi.syncTmpTypesInsertValues,
      sqlExec: syncSqlApi.syncTypesUpdate + syncSqlApi.syncTypesInsert
    },
    units: {
      url: rest.downUnits,
      sqlTruncate: syncSqlApi.syncTmpUnitsTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpUnitsInsert,
      sqlTmpValues: syncSqlApi.syncTmpUnitsInsertValues,
      sqlExec: syncSqlApi.syncUnitsUpdate + syncSqlApi.syncUnitsInsert
    },
    companies: {
      url: rest.downCompanies,
      sqlTruncate: syncSqlApi.syncTmpSuppliersTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpSuppliersInsert,
      sqlTmpValues: syncSqlApi.syncTmpSuppliersInsertValues,
      sqlExec: syncSqlApi.syncSuppliersUpdate + syncSqlApi.syncSuppliersInsert
    },
    products: {
      url: rest.downProducts,
      sqlTruncate: syncSqlApi.syncTmpGoodsTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpGoodsInsert,
      sqlTmpValues: syncSqlApi.syncTmpGoodsInsertValues,
      sqlExec: syncSqlApi.syncGoodsUpdate + syncSqlApi.syncGoodsInsert
    },
    product_attributes: {
      url: rest.downProductsAttributes,
      sqlTruncate: syncSqlApi.syncTmpGoodsAttributesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpGoodsAttributesInsert,
      sqlTmpValues: syncSqlApi.syncTmpGoodsAttributesInsertValues,
      sqlExec: syncSqlApi.syncGoodsAttributesInsert
    },
    product_ext_barcode: {
      url: rest.downProductsExtBarcode,
      sqlTruncate: syncSqlApi.syncTmpGoodsExtBarcodeTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpGoodsExtBarcodeInsert,
      sqlTmpValues: syncSqlApi.syncTmpGoodsExtBarcodeInsertValues,
      sqlExec: syncSqlApi.syncGoodsExtBarcodeInsertOrUpdate
    },
    products_stock: {
      url: rest.downProductsStock,
      sqlTruncate: syncSqlApi.syncTmpStockTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpStockInsert,
      sqlTmpValues: syncSqlApi.syncTmpStockInsertValues,
      sqlExec: syncSqlApi.syncGoodsStockUpdate
    },
    product_companies: {
      url: rest.downProductCompanies,
      sqlTruncate: syncSqlApi.syncTmpProductCompaniesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpProductCompaniesInsert,
      sqlTmpValues: syncSqlApi.syncTmpProductCompaniesInsertValues,
      sqlExec: syncSqlApi.syncProductCompaniesSave
    },
    inventories: {
      url: rest.downInventories,
      sqlTruncate: syncSqlApi.syncTmpInventoriesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpInventoriesInsert,
      sqlTmpValues: syncSqlApi.syncTmpInventoriesInsertValues,
      sqlTmpItemsInsert: syncSqlApi.syncTmpInventoryItemsInsert,
      sqlTmpItemsValues: syncSqlApi.syncTmpInventoryItemsInsertValues,
      sqlExec:
        syncSqlApi.syncInventoriesUpdate +
        syncSqlApi.syncInventoriesInsert +
        syncSqlApi.syncInventoryItemsUpdate +
        syncSqlApi.syncInventoryItemsInsert
    },
    purs: {
      url: rest.downPurs,
      sqlTruncate: syncSqlApi.syncTmpPurchasesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpPurchasesInsert,
      sqlTmpValues: syncSqlApi.syncTmpPurchasesInsertValues,
      sqlTmpItemsInsert: syncSqlApi.syncTmpPurchaseItemsInsert,
      sqlTmpItemsValues: syncSqlApi.syncTmpPurchaseItemsInsertValues,
      sqlExec:
        syncSqlApi.syncPurchasesAccountsUpdate +
        syncSqlApi.syncPurchasesUpdate +
        syncSqlApi.syncPurchasesInsert +
        syncSqlApi.syncPurItemsUpdate +
        syncSqlApi.syncPurItemsInsert
    },
    sales: {
      url: rest.downSales,
      sqlTruncate: syncSqlApi.syncTmpSalesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpSalesInsert,
      sqlTmpValues: syncSqlApi.syncTmpSalesInsertValues,
      sqlTmpItemsInsert: syncSqlApi.syncTmpSaleItemsInsert,
      sqlTmpItemsValues: syncSqlApi.syncTmpSaleItemsInsertValues,
      sqlTmpBlendInsert: syncSqlApi.syncTmpSaleBlendPaysInsert,
      sqlTmpBlendValues: syncSqlApi.syncTmpSaleBlendPaysInsertValues,
      sqlExec:
        syncSqlApi.syncSalesAccountsUpdate +
        syncSqlApi.syncSalesInsertOrUpdate +
        syncSqlApi.syncSaleItemsInsertOrUpdate +
        syncSqlApi.syncSaleBlendPaysInsertOrUpdate
    },
    shifthistories: {
      url: rest.downShiftHistories,
      sqlTruncate: syncSqlApi.syncTmpShiftHistoriesTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpShiftHistoriesInsert,
      sqlTmpValues: syncSqlApi.syncTmpShiftHistoriesInsertValues,
      sqlExec: syncSqlApi.syncShiftHistoriesUpdate + syncSqlApi.syncShiftHistoriesInsert
    },
    record_bills: {
      url: rest.downRecordBills,
      sqlTruncate: syncSqlApi.syncTmpRecordBillsTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpRecordBillsInsert,
      sqlTmpValues: syncSqlApi.syncTmpRecordBillsInsertValues,
      sqlExec: syncSqlApi.syncRecordBillsUpdate + syncSqlApi.syncRecordBillsInsert
    },
    product_scale: {
      url: rest.downProductScale,
      sqlTruncate: syncSqlApi.syncTmpProductScaleTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpProductScaleInsert,
      sqlTmpValues: syncSqlApi.syncTmpProductScaleInsertValues,
      sqlExec: syncSqlApi.syncProductScaleUpdate + syncSqlApi.syncProductScaleInsert
    },
    sendscale_product: {
      url: rest.downSendscaleProduct,
      sqlTruncate: syncSqlApi.syncTmpSendscaleProductTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpSendscaleProductInsert,
      sqlTmpValues: syncSqlApi.syncTmpSendscaleProductInsertValues,
      sqlExec: syncSqlApi.syncSendscaleProductUpdate + syncSqlApi.syncSendscaleProductInsert
    },
    sendscale: {
      url: rest.downSendscale,
      sqlTruncate: syncSqlApi.syncTmpSendscaleTruncate,
      sqlTmpInsert: syncSqlApi.syncTmpSendscaleInsert,
      sqlTmpValues: syncSqlApi.syncTmpSendscaleInsertValues,
      sqlExec: syncSqlApi.syncSendscaleUpdate + syncSqlApi.syncSendscaleInsert
    }
  },

  getSyncDataSub: function (params) {
    return demo.$http.post(params.url, params.syncAts, {
      maxContentLength: Infinity,
      timeout: 300000
    });
  },
  getSyncDataResponseExec: function (tblInfo, res) {
    res.data.forEach(item => {
      tblInfo[item.tbl] = Object.assign(item, this.tblParams[item.tbl]);
    });
  },
  /**
   * 获取所有同期数据
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getSyncData: function (params, onSuccess, onFail) {
    const funcs = this.type === 'all' ? [
      this.getSyncDataSub(params.users), this.getSyncDataSub(params.products), this.getSyncDataSub(params.pos)
    ] : [this.getSyncDataSub(params.pos)];

    demo.$http.all(funcs)
      .then(demo.$http.spread((...res) => {
        var error = {};

        if (this.type === 'all') {
          const users = res[0];
          var usersData = users.data;
          if (+usersData.code !== 200) {
            error.users = users;
          }
          const products = res[1];
          var productsData = products.data;
          if (+productsData.code !== 200) {
            error.products = products;
          }
          const pos = res[2];
          var posData = pos.data;
          if (+posData.code !== 200) {
            error.pos = pos;
          }
        } else {
          const pos = res[0];
          var posData = pos.data;
          if (+posData.code !== 200) {
            error.pos = pos;
          }
        }
        if (_.keys(error).length > 0) {
          onFail(error);
          return;
        }

        var tblInfo = {};
        if (this.type === 'all') {
          this.getSyncDataResponseExec(tblInfo, usersData);
          this.getSyncDataResponseExec(tblInfo, productsData);
        }
        this.getSyncDataResponseExec(tblInfo, posData);
        onSuccess(tblInfo);
      }))
      .catch(onFail);
  },

  /**
   * 同期所有表：down
   * @param {*} tblInfo
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncDataDown: function (tblInfo, onSuccess, onFail) {
    let that = this;
    var result = {};

    if (tblInfo.hasOwnProperty('sysinfo') && tblInfo.sysinfo.cnt > 0) {
      that.syncDown(tblInfo.sysinfo, that.commonDownOne, () => {
        external.loginSynced();
        syncTypes();
      }, err => {
        result['sysinfo'] = err;
        onFail(result);
      });
    } else {
      syncTypes();
    }

    function syncTypes() {
      if (tblInfo.hasOwnProperty('types') && tblInfo.types.cnt > 0) {
        that.syncDown(tblInfo.types, that.commonDownOne, () => {
          syncUnits();
        }, err => {
          result['types'] = err;
          onFail(result);
        });
      } else {
        syncUnits();
      }
    };

    function syncUnits() {
      if (tblInfo.hasOwnProperty('units') && tblInfo.units.cnt > 0) {
        that.syncDown(tblInfo.units, that.commonDownOne, () => {
          syncSuppliers();
        }, err => {
          result['units'] = err;
          onFail(result);
        });
      } else {
        syncSuppliers();
      }
    };

    function syncSuppliers() {
      if (tblInfo.hasOwnProperty('companies') && tblInfo.companies.cnt > 0) {
        that.syncDown(tblInfo.companies, that.commonDownOne, () => {
          syncGoods();
        }, err => {
          result['companies'] = err;
          onFail(result);
        });
      } else {
        syncGoods();
      }
    };

    function syncGoods() {
      if (tblInfo.hasOwnProperty('products') && tblInfo.products.cnt > 0) {
        that.syncDown(tblInfo.products, that.commonDownOne, () => {
          syncGoodsAttr();
        }, err => {
          result['products'] = err;
          onFail(result);
        });
      } else {
        syncGoodsAttr();
      }
    };

    function syncGoodsAttr() {
      if (tblInfo.hasOwnProperty('product_attributes') && tblInfo.product_attributes.cnt > 0) {
        that.syncDown(tblInfo.product_attributes, that.commonDownOne, () => {
          syncGoodsExtBarcode();
        }, err => {
          result['product_attributes'] = err;
          onFail(result);
        });
      } else {
        syncGoodsExtBarcode();
      }
    };

    function syncGoodsExtBarcode() {
      if (tblInfo.hasOwnProperty('product_ext_barcode') && tblInfo.product_ext_barcode.cnt > 0) {
        that.syncDown(tblInfo.product_ext_barcode, that.commonDownOne, () => {
          syncGoodsSuppliers();
        }, err => {
          result['product_ext_barcode'] = err;
          onFail(result);
        });
      } else {
        syncGoodsSuppliers();
      }
    };

    function syncGoodsSuppliers() {
      if (tblInfo.hasOwnProperty('product_companies') && tblInfo.product_companies.cnt > 0) {
        that.syncDown(tblInfo.product_companies, that.commonDownOne, () => {
          syncProductsStock();
        }, err => {
          result['product_companies'] = err;
          onFail(result);
        });
      } else {
        syncProductsStock();
      }
    };

    function syncProductsStock() {
      if (tblInfo.hasOwnProperty('products_stock') && tblInfo.products_stock.cnt > 0) {
        that.syncDown(tblInfo.products_stock, that.commonDownOne, () => {
          syncInventories();
        }, err => {
          result['products_stock'] = err;
          onFail(result);
        });
      } else {
        syncInventories();
      }
    };

    function syncInventories() {
      if (tblInfo.hasOwnProperty('inventories') && tblInfo.inventories.cnt > 0) {
        that.syncDown(tblInfo.inventories, that.commonDownTwo, () => {
          syncPurchases();
        }, err => {
          result['inventories'] = err;
          onFail(result);
        });
      } else {
        syncPurchases();
      }
    };

    function syncPurchases() {
      if (tblInfo.hasOwnProperty('purs') && tblInfo.purs.cnt > 0) {
        that.syncDown(tblInfo.purs, that.commonDownTwo, () => {
          syncSales();
        }, err => {
          result['purchases'] = err;
          onFail(result);
        });
      } else {
        syncSales();
      }
    };

    async function syncSales() {
      if (tblInfo.hasOwnProperty('sales') && tblInfo.sales.cnt > 0) {
        let bigDataDownloadObj = $config.Base.OtherOptions.bigDataDownload;
        let downloadCountDefault = rest.syncDownCount;
        let downloadMonthCountDefault = 0;
        let bigDataDownload = false;

        if (bigDataDownloadObj && bigDataDownloadObj.pc) {
          let pcBigDataDownloadConfig = bigDataDownloadObj.pc;
          // 大量数据下载从本月往前推算的月数
          downloadMonthCountDefault = pcBigDataDownloadConfig.monthCount;
          // 大量数据单次下载数量
          downloadCountDefault = pcBigDataDownloadConfig.count;
          // 大量数据下载开关
          bigDataDownload = pcBigDataDownloadConfig.enable;
        }

        if (demo.$store.state.show.isSyncingLogin === true && tblInfo.sales.cnt > downloadCountDefault && bigDataDownload) {
          const a = new Date();

          let bigSalesDataDownload = 'bigSalesDataDownload';
          // 是否是第一次执行大量数据下载
          let initBigData = true;
          let initBigDataImporting = 0;
          let initBigDataSuccess = 1;
          let setting = new Promise(async (resolve, reject) => {
            await settingService.get({ key: bigSalesDataDownload }, res => {
              console.log('下载大量销售数据库配置:', res);
              resolve(res);
            }, err => {
              reject(err);
            });
          });
          setting.then(function (value) {
            return new Promise(async (resolve, reject) => {
              if (value[0]) {
                initBigData = +value[0].value === initBigDataSuccess;
                console.log('否直接导入销售单:', initBigData);
                resolve();
                return;
              }
              var data = [
                { key: bigSalesDataDownload, value: initBigDataImporting }
              ];
              await settingService.put(data, res => {}, err => { reject(err) });
              resolve();
            });
          }).then(function () {
            superSyncService.initial(that.backStartSyncAt, that.syncUpDate, initBigData, 0, downloadMonthCountDefault)
              .then(res => {
                if (res === undefined) {
                  return;
                }
                superSyncService.downloadAllFile(res).then(() => {
                  var data = [
                    { key: bigSalesDataDownload, value: initBigDataSuccess }
                  ];
                  settingService.put(data);
                  const b = new Date();
                  let msg = `[销售单大量数据下载]{0} 下载完成 √√√√√√ 总耗时={1}`.format(res, b - a);
                  console.log(msg);
                  CefSharp.PostMessage(msg);
                  syncShiftHistories();
                }).catch(err => {
                  result['sales'] = err;
                  onFail(result);
                });
              })
              .catch(err => {
                result['sales'] = err;
                onFail(result);
              });
          });
          return;
        }

        that.syncDown(tblInfo.sales, that.commonDownTwo, () => {
          syncShiftHistories();
        }, err => {
          result['sales'] = err;
          onFail(result);
        });
      } else {
        syncShiftHistories();
      }
    };

    function syncShiftHistories() {
      if (tblInfo.hasOwnProperty('shifthistories') && tblInfo.shifthistories.cnt > 0) {
        that.syncDown(tblInfo.shifthistories, that.commonDownOne, () => {
          syncRecordBills();
        }, err => {
          result['shifthistories'] = err;
          onFail(result);
        });
      } else {
        syncRecordBills();
      }
    };

    function syncRecordBills() {
      if (tblInfo.hasOwnProperty('record_bills') && tblInfo.record_bills.cnt > 0) {
        that.syncDown(tblInfo.record_bills, that.commonDownOne, () => {
          syncProductScale();
        }, err => {
          result['record_bills'] = err;
          onFail(result);
        });
      } else {
        syncProductScale();
      }
    };
    function syncProductScale() {
      if (tblInfo.hasOwnProperty('product_scale') && tblInfo.product_scale.cnt > 0) {
        dao.exec(sqlApi.isExist.format('product_scale'), res => {
          if (res[0].cnt > 0) {
            that.syncDown(tblInfo.product_scale, that.commonDownOne, () => {
              syncSendscale();
            }, err => {
              result['product_scale'] = err;
              onFail(result);
            });
          } else {
            dao.exec(scaleSqlApi.initScale, () => {
              that.syncDown(tblInfo.product_scale, that.commonDownOne, () => {
                syncSendscale();
              }, err => {
                result['product_scale'] = err;
                onFail(result);
              });
            }, onFail);
          }
        }, onFail);
      } else {
        syncSendscale();
      }
    };
    function syncSendscale() {
      if (tblInfo.hasOwnProperty('sendscale') && tblInfo.sendscale.cnt > 0) {
        dao.exec(sqlApi.isExist.format('sendscale'), res => {
          if (res[0].cnt > 0) {
            that.syncDown(tblInfo.sendscale, that.commonDownOne, () => {
              syncSendscaleProduct();
            }, err => {
              result['sendscale'] = err;
              onFail(result);
            });
          } else {
            dao.exec(scaleSqlApi.initScale, () => {
              that.syncDown(tblInfo.sendscale, that.commonDownOne, () => {
                syncSendscaleProduct();
              }, err => {
                result['sendscale'] = err;
                onFail(result);
              });
            }, onFail);
          }
        }, onFail);
      } else {
        syncSendscaleProduct();
      }
    };
    function syncSendscaleProduct() {
      if (tblInfo.hasOwnProperty('sendscale_product') && tblInfo.sendscale_product.cnt > 0) {
        dao.exec(sqlApi.isExist.format('sendscale_product'), res => {
          if (res[0].cnt > 0) {
            that.syncDown(tblInfo.sendscale_product, that.commonDownOne, () => {
              onSuccess();
            }, err => {
              result['sendscale_product'] = err;
              onFail(result);
            });
          } else {
            dao.exec(scaleSqlApi.initScale, () => {
              that.syncDown(tblInfo.sendscale_product, that.commonDownOne, () => {
                onSuccess();
              }, err => {
                result['sendscale_product'] = err;
                onFail(result);
              });
            }, onFail);
          }
        }, onFail);
      } else {
        onSuccess();
      }
    };
  },

  /**
   * 同期所有表：up
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncDataUp: function (onSuccess, onFail) {
    this.syncDataUpCall()
      .then(onSuccess)
      .catch(onFail);
  },
  syncDataUpCall: function () {
    return new Promise((resolve, reject) => {
      this.upFuncs = this.getUpFuncs(this.type);
      this.upFuncCnt = this.upFuncs.length;
      this.syncDataUpLoop(resolve, reject);
    });
  },
  syncDataUpLoop: function (onSuccess, onFail, index = 0) {
    if (index >= this.upFuncCnt) {
      onSuccess();
      return;
    }
    const func = this.upFuncs[index];
    const funcName = func.name;
    if (funcName === 'syncGoodsUp' || funcName === 'syncGoodsSuppliersUp' || funcName === 'syncSalesUp') {
      this[funcName]().then(() => {
        this.syncDataUpLoop(onSuccess, onFail, ++index);
      }).catch(onFail);
      return;
    }
    this[funcName](() => {
      this.syncDataUpLoop(onSuccess, onFail, ++index);
    }, onFail);
  },
  clearsCheck: function (onSuccess, onFail) {
    var info = {};
    if (!demo.isNullOrTrimEmpty($setting.info)) {
      info = demo.t2json($setting.info);
    }
    demo.$http.get(rest.listFromId, {
      params: {
        id: info.clearId || 0
      }
    }, {
      timeout: 60000
    })
      .then(res => {
        var res1 = res.data.data;
        var hisId = +res1.hisId;

        if (demo.isNullOrTrimEmpty(info.clearId)) {
          info.clearId = hisId;
          $setting.info = JSON.stringify(info);
          var sql = clearsSql.replaceClearId.format('info', $setting.info, '');
          dao.transaction(sql, onSuccess, onFail);
        } else {
          settingService.otherClears(hisId, res1.successes, onSuccess, onFail);
        }
      })
      .catch(onFail);
  },
  clearDataInfo: function (onSuccess, onFail, type = 'all') {
    if (demo.$store.state.show.isSyncing) {
      onSuccess(1);
      return;
    }
    this.syncUpCnt = 0;
    this.syncId = '';
    this.syncUpDate = '';
    this.syncDownDate = '';
    this.startSyncAt = '';
    this.endSyncAt = '';
    this.backStartSyncAt = '';
    this.type = type;
    var that = this;
    if (pos.network.isConnected()) {
      demo.$store.commit('SET_SHOW', { isSyncing: true });
      console.log('isSyncing => true');

      if (demo.$store.state.show.isSyncingLogin === false && type === 'all') {
        clerkService.overwrite(() => { }, () => {
          CefSharp.PostMessage('clerks下载失败');
        });
      }

      this.clearsCheck(() => {
        var createAt = "";
        if ($storeinfo.length > 0) {
          createAt = $storeinfo[0].createAt;
        }
        this.syncCheck(callBack, onFail, createAt);
      }, doFail);
    } else {
      onFail('网络连接异常，请检查网络');
    }
    function callBack(res1) {
      var data = res1.data;
      if (!demo.isNullOrTrimEmpty(res1.response) && !demo.isNullOrTrimEmpty(res1.response.data)) {
        data = res1.response.data;
      }
      if (!demo.isNullOrTrimEmpty(data) && +data.code === 40101) {
        demo.$store.commit('SET_SHOW', { tokenExpiredMsg: data.msg, tokenExpired: true, isSyncingLogin: false });
        onFail(data.msg);
        return;
      }
      that.clearDataInfoSonar(data, that, onSuccess, onFail);
    }
    function doFail(err) {
      demo.$store.commit('SET_SHOW', { isSyncing: false });
      var errmsg = (typeof (err) === "object") ? demo.t2json(err) : err;
      console.log(errmsg + ' isSyncing => false ');
      onFail(err);
    };
  },
  clearDataInfoSonar: function (data, that, onSuccess, onFail) {
    if (!demo.isNullOrTrimEmpty(data) && +data.code === 200) {
      if (data.data.modify === true) {
        demo.$store.commit('SET_SHOW', { delReLogin: true, isSyncing: false, isSyncingLogin: false });
        console.log('isSyncing => false');
      } else {
        if (+data.data.id > 0) {
          that.syncUpDate = demo.isNullOrTrimEmpty(data.data.createAt) ? '2000-01-01 00:00:00.000000' : data.data.createAt;
          that.syncDownDate = data.data.syncedAt;
          that.syncId = data.data.id;
          that.syncData(onSuccess, doFail);
        } else {
          demo.$store.commit('SET_SHOW', { isSyncing: false });
          onSuccess('存在正在同步的处理，请稍后再同步数据。');
        }
      }
    } else {
      doFail('');
    }
    function doFail(err) {
      demo.$store.commit('SET_SHOW', { isSyncing: false });
      var errmsg = (typeof (err) === "object") ? demo.t2json(err) : err;
      console.log(errmsg + ' isSyncing => false ');
      onFail(err);
    };
  },
  scaleTabExist(item1, sqls) {
    if (item1 === 'product_scale' || item1 === 'sendscale' || item === 'sendscale_product' || item === 'sendscale_history') {
      sqls += sqlApi.isExist.format(item1);
    }
    return sqls;
  },
  scaleTabTruncate(item, sql) {
    if (item === 'clerks') {
      sql += sqlApi.clerksTruncate + sqlApi.deleteClerksFromSettings;
    } else if (item === 'product_scale' && newFlags.productScale > 0) {
      sql += sqlApi.tableTruncate.format(item);
    } else if (item === 'sendscale' && newFlags.sendscale > 0) {
      sql += sqlApi.tableTruncate.format(item);
    } else if (item === 'sendscale_product' && newFlags.sendscaleProduct > 0) {
      sql += sqlApi.tableTruncate.format(item);
    } else if (item === 'sendscale_history' && newFlags.sendscaleHistory > 0) {
      sql += sqlApi.tableTruncate.format(item);
    } else {
      sql += sqlApi.tableTruncate.format(item);
    }
    if (item === 'types') {
      sql += sqlApi.initTypes;
    } else if (item === 'units') {
      sql += sqlApi.initUnits;
    } else if (item === 'suppliers') {
      sql += sqlApi.initSuppliers;
    } else {
      // do nothing
    }
    return sql;
  },
  tablesIncludes(tables, sql) {
    if (!tables.includes('goods') && tables.includes('purchases')) {
      sql += sqlApi.clearCurStock;
    }
    return sql;
  },
  syncFail: function (onfail, errMsg) {
    demo.$store.commit('SET_SHOW', { isSyncing: false });
    onfail(errMsg);
  },
  /**
   * 云同步
   * @param {*} onSuccess
   * @param {*} onFail
   */
  syncData: function (onSuccess, onFail) {
    var that = this;
    var beginTime = new Date();

    that.syncDataUp(() => {
      that.syncUpEnd(that.syncId, that.syncUpCnt);
      if (+$setting.syncHistoryId > 0 && demo.isNullOrTrimEmpty(that.syncDownDate)) {
        if (demo.$store.state.show.isSyncingLogin === true) {
          dao.exec(syncSqlApi.syncGetGoodsSuppliersCnt, res => {
            if (res[0]['cnt'] > 0) {
              console.log('******************没有需要下载的数据**************************');
              doSuccess();
              return;
            }
            const syncAts = [
              { 'syncAt': this.minStartSyncAt, 'endSyncAt': this.syncUpDate, 'tbl': 'product_companies' }
            ];
            demo.$http.post(rest.getSyncDataProducts, syncAts, {
              maxContentLength: Infinity,
              timeout: 300000
            })
              .then(resu => {
                const data = resu.data;
                if (+data.code !== 200) {
                  console.error(`获取product_companies下载数量异常`, data.msg);
                  doFail();
                  return;
                }

                const tblInfo = {};
                this.getSyncDataResponseExec(tblInfo, data);
                this.startSyncAt = this.minStartSyncAt;
                this.syncDataDown(tblInfo, result => {
                  doSuccess(result);
                }, doFail);
              })
              .catch(doFail);
          }, err => {
            console.error(`sql执行异常：${syncSqlApi.syncGetGoodsSuppliersCnt}`, err);
            doSuccess();
          });
        } else {
          console.log('******************没有需要下载的数据**************************');
          doSuccess();
        }
      } else {
        that.syncDownLoad(doSuccess, doFail);
      }
    }, err => {
      that.syncUpEnd(that.syncId, that.syncUpCnt);
      doFail(err);
    });

    function doSuccess(res) {
      demo.$store.commit('SET_SHOW', { isSyncing: false });
      if (that.type === 'all') {
        settingService.put({ key: 'syncHistoryId', value: that.syncId });
        $setting.syncHistoryId = that.syncId;
      }
      var endTime = new Date();
      console.log('**************************syncData：' + (endTime - beginTime) + '**************************');
      onSuccess(res);
    }
    function doFail(err) {
      demo.$store.commit('SET_SHOW', { isSyncing: false });
      onFail(err);
    }
  },

  syncDownLoad: async function (onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(this.syncDownDate)) {
      this.startSyncAt = this.minStartSyncAt;
      this.backStartSyncAt = this.minStartSyncAt;
    } else {
      this.startSyncAt = this.syncDownDate;
      const startSyncAtDate = new Date(this.startSyncAt);
      startSyncAtDate.setMinutes(startSyncAtDate.getMinutes() - this.backMinutes);
      this.backStartSyncAt = startSyncAtDate.format('yyyy-MM-dd hh:mm:ss.S');
    }
    this.endSyncAt = this.syncUpDate;

    let prodcutCompaniesStartSyncAt = this.startSyncAt;
    if (demo.$store.state.show.isSyncingLogin === true && +$setting.syncHistoryId > 0) {
      const res = await dao.asyncExec(syncSqlApi.syncGetGoodsSuppliersCnt);
      if (+res[0]['cnt'] === 0) {
        prodcutCompaniesStartSyncAt = this.minStartSyncAt;
      }
    }

    const syncAts = {
      users: {
        url: rest.getSyncDataUsers,
        syncAts: [
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'sysinfo' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'shifthistories' }
        ]
      },
      products: {
        url: rest.getSyncDataProducts,
        syncAts: [
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'products' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'product_attributes' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'product_ext_barcode' },
          { 'syncAt': prodcutCompaniesStartSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'product_companies' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'types' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'units' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'product_scale' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'sendscale' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'sendscale_product' }
        ]
      },
      pos: {
        url: rest.getSyncDataPos,
        syncAts: this.type === 'pos-sales' ? [
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'products_stock' },
          { 'syncAt': this.backStartSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'sales' }
        ] : [
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'companies' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'products_stock' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'purs' },
          { 'syncAt': this.backStartSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'sales' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'inventories' },
          { 'syncAt': this.startSyncAt, 'endSyncAt': this.endSyncAt, 'tbl': 'record_bills' }
        ]
      }
    };

    this.getSyncData(syncAts, tblInfo => {
      this.syncDataDown(tblInfo, (res) => {
        onSuccess(res);
      }, err => {
        onFail(err);
      });
    }, onFail);
  },

  /**
   * @param {*成功回调} callBack
   * @param {*失败回调} doFail
   * @param {*店铺创建时间 判断其他设备是否清空数据用} createAt
   */
  syncCheck: function (callBack, doFail, createAt) {
    var params = {
      syncHistoryId: $setting.syncHistoryId,
      deviceCode: $config.deviceCode
    };
    if (!demo.isNullOrTrimEmpty(createAt)) {
      params.createAt = createAt;
    }
    // 云同步前check  SyncCheck id, createAt, syncedAt, isModify
    demo.$http.post(rest.syncCheck, params, {
      maxContentLength: Infinity,
      timeout: 60000
    })
      .then(res1 => {
        callBack(res1, res1.data.data.id);
      })
      .catch(doFail);
  },
  /**
   *
   * @param {*} hasUp
   * 上传数据结束
   */
  syncUpEnd: function (syncId, hasUp) {
    var params = {
      syncId: syncId,
      hasUp: hasUp
    };
    demo.$http.post(rest.syncEnd, params, {
      maxContentLength: Infinity,
      timeout: 10000
    });
  }
};

window.syncService = syncService;
export default syncService;
