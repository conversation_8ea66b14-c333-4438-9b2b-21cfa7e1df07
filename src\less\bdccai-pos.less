
// 页面
@themeBackGroundColor: #B4995A;
@themeFontColor: #1D324F;
@themeButtonBackGroundColor: #FFF7E6;
// login
@loginColor: red;
@verification:#CCCCCC;
//header
@headerFontColor: #B4995A;
@versionIcon:#278FF5;
@butBackgroundColor:#FFF7E6;

// home
@fontWeight: normal;
@fontStyle: normal;
@homeColor: #567485;
@left_backgroundColor:  #243642;
@leftBut_backgroundColor:  #394A55;
@butFontColor: #FFFFFF;

// pc_print_setting
@butFontColor1: #FFFFFF;
//pc_add_goods


// setting
@selectRow: linear-gradient(0deg, #B4995A, #B4995A);

// 共同
@backgroundColor: #B4995A;
@color: #B4995A;
@areaColor: #567485;
@inventoryColor: #D2E4EE;
@linearBackgroundColor: linear-gradient(0deg, #B4995A, #B4995A);

//report
@reportButtonColor: #B4995A;
@reportFontColor: #B4995A;
@reportFontColor2: #B4995A;
@reportBackGroundColor: #B4995A;//linear-gradient(180deg, #D8B774 0%, #DEB071 100%);


// components
