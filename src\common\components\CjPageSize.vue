<template>
  <div class="page_size_wrap">
    <el-dropdown
      ref="dropdown"
      :trigger="trigger"
      :placement="placement"
      @command="handleClick"
      @visible-change="handleVisible">
      <div class="page_size_wrap--button">
        每页<span class="text">{{pageSize}}</span>条
        <div>
          <i class="el-icon-arrow-down arrow" :class="{'rotate': visiable}"></i>
        </div>
      </div>
      <el-dropdown-menu slot="dropdown" class="cj-dropdown-menu">
        <div class="page_size_wrap--content" :style="contentStyle">
          <div
            v-for="num in pageSizes"
            :key="num"
            class="pageItem"
            :class="{'active' : num === pageSize}"
            :style="itemStyle"
            @click.stop="handleClick(num)">
            {{num}}
          </div>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
export default {
  name: 'CjPageSize',
  props: {
    // 触发方式
    trigger: {
      type: String,
      default: 'click'
    },
    // 弹出位置
    placement: {
      type: String,
      default: 'top-end'
    },
    splitButton: {
      type: Boolean,
      default: true
    },
    // 列数
    columns: {
      type: Number,
      default: 2
    },
    // 当前页数，必传项
    currentPage: {
      type: Number,
      default: 1
    },
    // 每页显示条目个数，必传项
    pageSize: {
      type: Number,
      default: 20
    },
    // 分页key，用于分页组件重新渲染当前页数，必传项
    pageKey: {
      type: Number,
      default: 0
    },
    // 每页显示个数选择器的选项设置
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 50, 100, 200]
    },
    // 选项宽度
    itemWidth: {
      type: Number,
      default: 100
    },
    // 选项高度
    itemHeight: {
      type: Number,
      default: 50
    },
    // 选项字号
    fontSize: {
      type: Number,
      default: 16
    }
  },
  data() {
    return {
      visiable: false
    };
  },
  mounted() {
  },
  methods: {
    handleClick(val) {
      // 修改之前每页显示的条数
      const oldPageSize = this.pageSize;
      // 修改之前当前页首条数据的索引
      const oldItemIndex = (this.currentPage - 1) * oldPageSize;
      // 计算索引除以修改后每页显示的条数向下取整加一得出修改后的当前页数
      this.$emit('update:currentPage', Math.floor(oldItemIndex / val) + 1);
      // 修改之后每页显示的条数
      this.$emit('update:pageSize', val);
      // 修改组件key值用以重新渲染
      let newPageKey = this.pageKey + 1;
      this.$emit('update:pageKey', newPageKey);
      this.$emit('sizeChange');
      // 隐藏下拉菜单
      this.$refs['dropdown'].hide();
    },
    handleVisible(boolean) {
      // 更新下拉菜单显示状态
      this.visiable = boolean;
    }
  },
  computed: {
    // 项目样式
    itemStyle() {
      return `width: ${this.itemWidth}${isNaN(this.itemWidth) ? '' : 'px'};
        height: ${this.itemHeight}${isNaN(this.itemHeight) ? '' : 'px'};
        fontSize: ${this.fontSize}px`;
    },
    // 容器样式
    contentStyle() {
      return `width: ${this.itemWidth * this.columns}${isNaN(this.itemWidth) ? '' : 'px'}`;
    }
  }
};
</script>
<style lang="less" scoped>
/deep/.el-dropdown {
  color: @themeFontColor;
}
/deep/.el-dropdown-menu {
  padding: 0;
}
.cj-dropdown-menu {
  font-family: HarmonyOSSansSC, sans-serif !important;
  padding: 0;
  margin-bottom: 6px;
  /deep/.popper__arrow::after {
    display: none;
  }
}
.page_size_wrap {
  font-family: HarmonyOSSansSC, sans-serif !important;
  display: inline-block;
  color: @themeFontColor;
  &--button {
    height: 28px;
    line-height: 28px;
    border: 1px solid @themeFontColor;
    border-radius: 4px;
    padding: 0 6px;
    cursor: pointer;
    outline: none;
    display: flex;
    font-weight: normal;
    .text {
      text-align: center;
      min-width: 0;
      padding: 0 6px;
    }
    .arrow {
      transition: transform 0.3s ease;
      margin: 0 2px;
    }
    .rotate {
      transform: rotate(180deg);
    }
  }
  &--content {
    display: flex;
    flex-wrap: wrap;
    .pageItem {
      cursor: pointer;
      color:@themeFontColor;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .pageItem:hover {
      background: @input_backgroundColor;
    }
    .active {
      background: @input_backgroundColor !important;
    }
  }
}
</style>
