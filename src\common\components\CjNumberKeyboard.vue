<template>
  <div ref="keyboard" class="keyboardWrap" @click.stop>
    <div v-for="key in keyList" :key="`$${key}_id`"
      class="keyItem"
      :class="{ activeItem: key === clickValue }"
      @click="keyClick(key)">{{ key }}</div>
  </div>
</template>
<script>
export default {
  name: 'CjNumberKeyboard',
  data() {
    return {
      value: '',
      keyList: [7, 8, 9, 0, 4, 5, 6, '.', 1, 2, 3, '删除'],
      clickValue: '',
      el: null
    };
  },
  mounted() {
    document.addEventListener('mousedown', this.clickOutSide);
    this.$nextTick(() => {
      this.el = this.$refs.keyboard;
    });
  },
  beforeDestroy() {
    document.removeEventListener('mousedown', this.clickOutSide);
  },
  methods: {
    // 判断是否点击的外部
    clickOutSide(e) {
      if (!this.el.contains(e.target)) {
        this.$emit('close');
      }
    },
    // 按键点击
    keyClick(key) {
      this.clickValue = key;
      setTimeout(() => {
        this.clickValue = ''
      }, 100)
      this.$emit('change', key);
    }
  }
};
</script>
<style lang="less" scoped>
.wrap {
  position: relative;
  .mask {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 9998;
    top: 0;
    left: 0;
  }
}
.keyboardWrap {
  width: 100%;
  height: 280px;
  border-radius: 4px;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 24px;
  font-weight: bold;
  background-color: #ffffff;
  position: relative;
  .keyItem {
    width: 118px;
    height: 83px;
    text-align: center;
    line-height: 83px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px solid #CACACA;
    user-select: none;
  }
  .activeItem {
    color: #ffffff;
    background-color: @themeBackGroundColor;
  }
}
</style>
