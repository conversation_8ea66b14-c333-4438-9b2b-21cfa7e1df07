<style lang="less">
.com_pme17 {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 12;overflow: auto;
}
.dialog-title {
  .el-dialog__body {
    padding: 15px 20px!important;
    color: @themeFontColor!important;
    font-size: 14px!important;
    word-break: break-all!important;
  }
  .el-dialog__header {
    padding: 20px 20px 10px!important;
  }
}
.com_pme18 {
  height: 25px;
  color: @themeFontColor;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
  text-align: center;
  line-height: 50px;
}
.com_pme19 {
  overflow: hidden;
  margin: 5px 50px;
}
.com_pme19 input {
  width: 308px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.com_pme19 .el-date-editor.el-input, .el-date-editor.el-input__inner{
  width: 308px;
}
.com_pme19 .el-input__icon {
  line-height: 36px;
}
.com_pme33  {
  width: 218px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
  border-radius: 4px;
}
.com_pme34  {
  width: 308px;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
  background: #F5F8FB;
  border: none;
  border-radius: 4px;
  font-size: 16px;
}
.com_pme34 .el-textarea__inner {
  background: #F5F8FB;
  border: none;
  border-radius: 4px;
}
.com_pme36 {
  display:block;
  text-align:right;
  color: #B2C3CD;
  font-size: 16px;
  width: 200px;
  font-weight: normal;
  float: right;
  line-height: 18px;
  margin-top: 12px;
}
.com_pme37 {
  display:block;
  text-align:right;
  color: #B2C3CD;
  font-size: 18px;
  width: 160px;
  font-weight: normal;
  float: left;
  line-height: 18px;
  margin-top: 12px;
}

.com_pme20 {
  width: 154px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  text-align: center;
  line-height: 42px;
  margin-left: 24px;
  color: @themeBackGroundColor;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 700;
}
.com_pme21{
  width: 154px;
  height: 44px;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 44px;
  color: #fff;
  margin-left: 24px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 700;
}
.com_pme22{
  width: 154px;
  height: 54px;
  background-image: linear-gradient(90deg, #f1d3af, #cfa26b);
  text-align: center;
  line-height: 50px;
  color: #fff;
  font-size: 20px;
  float: left;
  margin-left: 20px;
  margin-top: 12px;
  cursor: pointer;
  border-radius: 4px;
}
.com_pme2{
  line-height: 40px;font-size: 16px;color: @themeFontColor;float: left;width: 150px;
}

/*  .com_pme2{
    position: absolute;
  }*/
.com_pme3{
  position: absolute;
  bottom: 20px;
  display: flex;
}
.com_pme49 {
  right: 100px;
  position: absolute;
  bottom: 20px;
}
.com_pme50 {
  color: #ff6159;
  font-size: 30px;
  font-weight: bold;
  float: left;
  margin-top: 5px;
  margin-left: 8px;
  height: 30px;
}
.com_pme51 {
  width: 70px;
  height: 40px;
  background-image: linear-gradient(90deg, #D8B774 0%, #DEB071 100%);
  border-radius: 5px;
  float: left;
  color: #fff;
  margin-left: 8px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}
.com_pme55{
  overflow: hidden;margin-top: 30px;font-size: 24px;
}
.com_pme56{
  width: 138px;height: 50px;text-align: center;line-height: 48px;background: @themeFontColor;
  color: #fff;margin-left: 72px;float: left;border-radius: 4px;cursor: pointer;
}
.com_pme57{
  width: 138px;height: 50px;text-align: center;line-height: 48px;margin-left: 30px;
  background:@themeBackGroundColor;color: #FFF;float: left;border-radius: 4px;cursor: pointer;
}
.com_pme58 {
  float: left;margin-left: 11px;margin-top: 0px;overflow: hidden;cursor: pointer;
}
.com_pme59 {
  float: left;width: 16px;height: 16px;background: @themeBackGroundColor;border: 2px solid @themeBackGroundColor;
  border-radius: 50%;overflow: hidden;position: relative;margin-top: 12px;
}
.com_pme6 {
  width: 6px;height: 6px;background: #FFF;margin-top: 3px;margin-left: 3px;border-radius: 50%;overflow: hidden;
}
.com_pme61 {
  float: left;margin-left: 10px;font-size: 16px;color: @themeFontColor;margin-top: 7px;
}
.com_pme62 {
  position: absolute;right: 20px;top: 1px;font-size: 30px;color: @themeFontColor;cursor: pointer;
}
.com_pme63 {
  overflow: hidden;
  margin: 5px 50px;
}
.com_pme63 input {
  width: 120px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: left;
  font-weight: bold;
  color: @themeFontColor;
}

.addMember .el-input--prefix .el-input__inner {
  padding-left: 30px;
  background: #f5f8fb;
}
.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}
.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}
.com_pme64 {
  line-height: 60px;width: calc(100% - 40px);margin-left: 20px;font-size: 16px;
  font-weight: bold;text-indent: 10px;border-bottom: 1px solid #E3E6EB;color: @themeFontColor;
}
.com_pme65 {
  position: absolute;right: 30px;top: 6px;font-size: 30px;color: #8298A6;cursor: pointer;
}
.com_pme66 {
  overflow: hidden;
}
.com_pme67 {
  width: 60px;
  height: 60px;
  background: @themeBackGroundColor;
  border-radius: 50%;
  float: left;
  font-size: 30px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 60px;
  text-align: center;
  margin-top: 21px;
  margin-left: 50px;
}
.com_pme68 {
  margin-left: 45px;float: left;
}
.com_pme69 {
  margin-top: 24px;font-size: 20px;font-weight: bold;line-height: 20px;
}
.com_pme7 {
  line-height: 16px;margin-top: 19px;
}
.com_pme71 {
  width: 90px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  font-weight: 700;
  color: @themeBackGroundColor;
  line-height: 38px;
  text-align: center;
  margin-top: 31px;
  float: left;
  margin-left: 15px;
  cursor: pointer;
}
.com_pme72 {
  background-image: url(../image/pc_member_bg.png);width: 600px;height: 120px;margin-top: 26px;margin-left: 50px;
}
.com_pme73 {
  float: left;margin-left: 39px;color: @themeBackGroundColor;
}
.com_pme74 {
  margin-top: 29px;line-height: 16px;
}
.com_pme75 {
  margin-top: 20px;line-height: 30px;font-size: 30px;font-weight: 700;
}
.com_pme76 {
  width: 120px;
  height: 50px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  font-size: 20px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 50px;
  text-align: center;
  float: right;
  margin-top: 35px;margin-right: 40px;
  cursor: pointer;
}
.com_pme77 {
  overflow: hidden;margin-left: 60px;color: @themeBackGroundColor;
}
.com_pme78 {
  margin-top: 27px;line-height: 16px;
}
.com_pme79 {
  font-size: 30px;line-height: 30px;margin-top: 15px;font-weight: 700;
}
.com_pme8 {
  float: left;
  width: 80px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  font-size: 16px;
  color: @themeBackGroundColor;
  line-height: 38px;
  text-align: center;
  margin-top: 46px;
  cursor: pointer;
}
.com_pme81 {
  float: left;width: 300px;
}
.com_pme82 {
  line-height: 16px;margin-top: 30px;overflow: hidden;
}
.com_pme82 .el-textarea__inner {
  background: #F5F8FB;border: none;padding: 6px 15px;font-size: 16px;color: @themeFontColor;
}
.com_pme83 {
  float: left;width: 95px;
}
.com_pme84 {
  float: left;color: #B2C4CD;width: 195px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap;;
}
.com_pme85 {
  float: left;color: #B2C4CD;
  width: 195px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.com_pme86 {
  width: 150px;
  height: 50px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  font-size: 20px;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 50px;
  text-align: center;
  float: left;
  cursor: pointer;
}
.com_pme87 {
  width: 120px;
  height: 50px;
  background: #EBEEF5;
  border-radius: 4px;
  font-size: 20px;
  font-weight: 700;
  color: @themeFontColor;
  line-height: 48px;
  border: 1px solid #D3D5D9;
  text-align: center;
  float: right;
  margin-right: 50px;
  cursor: pointer;
}
.com_pme88 {
  width: 700px;background: #FFF;border: 1px solid #e3e6eb;border-radius: 5px;min-height: 675px;
  margin: 0 auto;margin-top: 15px;position: relative;font-size: 16px;color: @themeFontColor;
}
.com_pme88 input {
  width: 200px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.com_pme89 {
  width: 200px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.com_pme9 {
  float: left;width: 75px;margin-top: 10px;
}
.com_pme91 {
  width: 200px;
  border: none;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
  background: #F5F8FB;margin-top: 2px;
}
.com_pme92 {
  opacity: 40%;
}
.add_member .el-dialog {
  border-radius: 5px;
}
.hide_btn {
  visibility: hidden;
}
.el-dialog {
  border-radius: 6px;
}
.add_member .el-dialog__header {
  padding: 0;
}
.add_member .el-dialog__body {
  padding: 0;
}
.active_tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
}
.active_tips_dialog .title{
  color: @themeFontColor;
  font-size: 24px;
  text-align: center;
  margin-top: 40px;
  font-weight: bold;
}
.active_tips_dialog .content{
  color: @themeFontColor;
  font-size: 24px;
  margin-top: 28px;
  text-align: center;
}
.active_tips_dialog .dialog_btn_container{
  display: flex;
  justify-content: center;
  margin-top: 50px;
  padding-bottom: 30px;
}
.active_tips_dialog .dialog_btn_container .btn{
  width: 140px;
  height: 50px;
  line-height: 38px;
  color: white;
  font-weight: bold;
  font-size: 20px;
}
.com_pme93 {
  width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;
}
.com_pme94 {
  width: 450px;height: 276px;margin: 0 auto;margin-top: 246px;background: #FFF;
  border-radius: 6px;color: @themeFontColor;overflow: hidden;
}
.com_pme95 {
  margin-top: 40px;text-align: center;font-size: 24px;line-height: 24px;font-weight: bold;
}
.com_pme96 {
  width: 320px;height: 60px;margin: 0 auto;margin-top: 35px;background: #F5F8FB;border-radius: 4px;
}
.com_pme97 {
  border: none;width: 290px;margin-left: 15px;font-size: 18px;
  color: @themeFontColor;margin-top: 15px;background: #F5F8FB;
}
.com_pme98 {
  width: 140px;height: 50px;margin-left: 70px;margin-top: 40px;background: @themeFontColor;
  border-radius: 4px;color: #FFF;font-size: 20px;line-height: 50px;text-align: center;
  font-weight: bold;float: left;cursor: pointer;
}
.com_pme99 {
  width: 140px;height: 50px;margin-left: 30px;margin-top: 40px;background: @themeBackGroundColor;
  border-radius: 4px;color: #FFF;font-size: 20px;line-height: 50px;text-align: center;
  font-weight: bold;float: left;cursor: pointer;
}
.com_pme100 {
  position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;
  background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;
}
.com_pme101 {
  width: 100%;text-align: center;font-size: 25px;color: @themeFontColor;margin-top: 40px;
}
.com_pme102 {
  width: 100%;text-align: center;font-size: 25px;color: @themeFontColor;margin-top: 25px;font-weight: 100;
}
.com_pme103 {
  position: relative;z-index: 800;height: 300px;margin: 0 auto;margin-top: 240px;
  background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;
}
.com_pme104 {
  width: 560px;background: #FFF;border: 1px solid #e3e6eb;
  border-radius: 5px;height: 630px;margin: 0 auto;position: relative;
}
.yes {
  border-color: @themeBackGroundColor;
}
.no {
  background: #FFF;
  border-color: #D2D5D9;
}
#showEdit {
  background: @themeFontColor;
}
#restartBut {
  background:@themeBackGroundColor;
}
</style>
<template>
  <div>
    <!-- 编辑会员积分弹出框 -->
    <div class="addMember" style="width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;" v-show="showEdit">
      <div style="width: 450px;height: 276px;margin: 0 auto;margin-top: 246px;background: #FFF;border-radius: 6px;color: #567485;overflow: hidden;">
        <div style="margin-top: 40px;text-align: center;font-size: 24px;line-height: 24px;font-weight: bold;">当前可用积分：{{detail.integral}}</div>
        <div style="width: 320px;height: 60px;margin: 0 auto;margin-top: 35px;background: #F5F8FB;border-radius: 4px;">
          <input type="text"
            ref="nowUsePoint"
            v-model="input_score"
            class="com_pme97"
            maxlength="7"
            @input="input_score = $countLimit(input_score)">
        </div>
        <div style="overflow: hidden;">
          <div id="showEdit"
          style="width: 140px;height: 50px;margin-left: 70px;margin-top: 40px;border-radius: 4px;
          color: #FFF;font-size: 20px;line-height: 50px;text-align: center;font-weight: bold;float: left;cursor: pointer;"
          @click="showEdit = false;">
          取消
          </div>
          <div
          class="com_pme99"
          @click="saveScore()">
          保存</div>
        </div>
      </div>
    </div>
    <!--新增会员弹出框-->
    <div
      v-show="showAddMember === 'new' || showAddMember === 'edit' || showAddMember === 'readonly'"
      class="com_pme17"
    >
      <div
        v-show="show_delete"
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
      >
        <div
          style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
          @click="show_delete = false"
        ></div>
        <div class="com_pme100">
          <div class="com_pme101">提示</div>
          <div class="com_pme102">确定{{detail.is_deleted == 0 ? '注销' : '激活'}}会员信息？</div>
          <div class=" com_pme55">
            <div
              class="com_pme56"
              @click="show_delete = false;"
            >取消</div>
            <div
              class="com_pme57"
              @click="show_delete = false;deleteMember()"
            >确定</div>
          </div>
        </div>
      </div>
      <div
        v-show="show_change_cardno"
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
      >
        <div
          style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
          @click="show_change_cardno = false;deleteCardno()"
        ></div>
        <div class="com_pme103">
          <div style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;">是否更换会员卡</div>
          <div style="width: 100%;text-align: center;font-size: 20px;color: #567485;margin-top: 25px;font-weight: 100;">
            旧卡号：<span style="font-size: 18px;font-weight: bold;">{{detail.code}}</span>
          </div>
          <div style="width: 100%;text-align: center;font-size: 20px;color: #567485;margin-top: 0px;font-weight: 100;">
            新卡号：<span style="font-size: 18px;font-weight: bold;">{{cardNo}}</span>
          </div>
          <div class=" com_pme55">
            <div
              class="com_pme56"
              @click="show_change_cardno = false;deleteCardno()"
            >取消</div>
            <div
              class="com_pme57"
              @click="show_change_cardno = false;detail.code = cardNo;"
            >确定</div>
          </div>
        </div>
      </div>
      <!--新增或编辑-->
      <div
        v-show="showAddMember === 'new'"
        class="com_pme104"
        :style="numberMargin"
      >
        <div
          class="com_pme18"
          v-show="showAddMember === 'new'"
        >新增会员</div>
        <div
          class="com_pme62"
          @click="cancel()"
        >×</div>
        <el-divider></el-divider>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;width: 67px;">姓名</div>
            <div class="com_pme50">*</div>
          </div>
          <el-input
            placeholder="请输入姓名"
            class="com_pme33"
            id="member_name"
            v-model="detail.name"
            maxlength="15"
            @input="detail.name = $vipNameFormat(detail.name)"
            @focus="selectText('member_name')"
          >
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;width: 67px;">手机号</div>
            <div class="com_pme50">*</div>
          </div>
          <el-input
            id="vipmobile"
            placeholder="请输入手机号"
            class="com_pme33"
            v-model="detail.mobile"
            maxlength="11"
            @input="detail.mobile = detail.mobile.replace(/[^\d]/g,'')"
            @focus="selectText('vipmobile')"
          >
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">性别</div>
          </div>
          <div
            class="com_pme58"
            @click="detail.sex = 1"
          >
            <div
              class="com_pme59"
              :class="detail.sex === 1 ? 'yes' : 'no'"
            >
              <div class="com_pme6"></div>
            </div>
            <div class="com_pme61">男</div>
          </div>
          <div
            class="com_pme58"
            style="margin-left: 20px;"
            @click="detail.sex = 2"
          >
            <div
              class="com_pme59"
              :class="detail.sex === 2 ? 'yes' : 'no'"
            >
              <div class="com_pme6"></div>
            </div>
            <div class="com_pme61">女</div>
          </div>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">会员卡号</div>
          </div>
          <el-input
            id="vipcode"
            placeholder="请输入会员卡号"
            class="com_pme33"
            v-model="detail.code"
            @input="detail.code = $vipCardNumberFormat(detail.code)"
            @focus="selectText('vipcode')"
          >
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">支付码</div>
          </div>
          <el-input
            id="vippassword"
            placeholder="请输入6位以内纯数字"
            type="password"
            class="com_pme33"
            v-model="detail.password"
            maxlength="6"
            @input="detail.password = detail.password.replace(/[^\d]/g,'')"
            @focus="selectText('vippassword')"
          >
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">价格等级</div>
          </div>
          <div
            class="com_pme58"
            @click="detail.pay_type = 1"
          >
            <div
              class="com_pme59"
              :class="detail.pay_type === 1 ? 'yes' : 'no'"
            >
              <div class="com_pme6"></div>
            </div>
            <div class="com_pme61">零售价</div>
          </div>
          <div
            class="com_pme58"
            style="margin-left: 20px;"
            @click="detail.pay_type = 2"
          >
            <div
              class="com_pme59"
              :class="detail.pay_type === 2 ? 'yes' : 'no'"
            >
              <div class="com_pme6"></div>
            </div>
            <div class="com_pme61">会员价</div>
          </div>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">会员折扣</div>
          </div>
          <el-input
            id="vipdisc"
            placeholder="0.1~10.0"
            class="com_pme33"
            v-model="detail.disc"
            @input="detail.disc = $memberDiscountLimit(detail.disc)"
            @blur="checkDisc()"
            @focus="selectText('vipdisc')"
          >
            <template slot="suffix">折</template>
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">生日</div>
          </div>
          <el-date-picker
            v-model="detail.birthday"
            type="date"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="请选择生日"
            style="float: right"
          >
          </el-date-picker>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">联系地址</div>
          </div>
          <el-input
            id="vipaddr"
            placeholder="请输入地址"
            class="com_pme33"
            v-model="detail.addr"
            maxlength="50"
            @focus="selectText('vipaddr')"
          >
          </el-input>
        </div>
        <div class="com_pme19">
          <div class="com_pme2">
            <div style="float: left;">备注</div>
          </div>
          <el-input
            id="viptextarea"
            type="textarea"
            placeholder="请输入备注二十字以内"
            v-model="detail.remark"
            maxlength="20"
            resize="none"
            class="com_pme34"
            @focus="selectText('viptextarea')"
          >
          </el-input>
        </div>
        <div class="com_pme3">
          <div
            v-show="permit_input"
            class="com_pme20"
            @click="cancelEdit()"
            :style="isPay ? 'margin-left: 84px;' : ''"
          >取消</div>
          <div v-show="permit_input && !isPay" class="com_pme20" @click="subMember(true)">
            保存并充值
          </div>
          <div
            v-show="permit_input"
            class="com_pme21"
            @click="subMember(false)"
            :style="isPay ? 'margin-left: 84px;' : ''"
          >保存</div>
        </div>
      </div>

      <!--只读&编辑-->
      <div
        v-show="showAddMember === 'readonly' || showAddMember === 'edit'"
        class="com_pme88"
      >
        <div class="com_pme64">会员信息</div>
        <div
          class="com_pme65"
          @click="showAddMember === 'edit' ? cancelEdit() : cancel()"
        >×</div>
        <div class="com_pme66">
          <div class="com_pme67">{{detail.name.substring(0, 1)}}</div>
          <!-- 只读 -->
          <div
            class="com_pme68" style="width: 280px;"
            v-show="showAddMember === 'readonly'"
          >
            <div class="com_pme69">{{detail.name}}</div>
            <div class="com_pme7">{{detail.mobile}}</div>
          </div>
          <!-- 编辑 -->
          <div
            class="com_pme68"
            style="width: 200px;margin-left: 30px;"
            v-show="showAddMember === 'edit'"
          >
            <div style="position: absolute;">
              <span style="position: relative;top: 25px;font-weight: bold;">会员姓名&nbsp;&nbsp;&nbsp;<span style="color: red;">*</span></span>
              <el-input
                placeholder="请输入姓名"
                class="com_pme89"
                id="member_name_edit"
                v-model="detail.name"
                maxlength="15"
                style="margin-top: 16px;font-size: 20px;"
                @focus="selectText('member_name_edit')"
                @input="detail.name = $vipNameFormat(detail.name)"
              >
              </el-input>
            </div>
            <div style="position: absolute;top: 115px">
              <span style="position: relative;top: 10px;font-weight: bold;">联系电话&nbsp;&nbsp;&nbsp;<span style="color: red;">*</span></span>
              <el-input
                id="vipmobileedit"
                placeholder="请输入手机号"
                class="com_pme89"
                v-model="detail.mobile"
                style="margin-top: 2px;"
                maxlength="11"
                @focus="selectText('vipmobileedit')"
                v-input-phone>
              </el-input>
            </div>
          </div>
          <div
            v-show="!permit_input"
            style="float: right;margin-right: 50px;"
          >
            <div
              class="com_pme71"
              :style="!$employeeAuth('cancel_vips') ? 'opacity: 40%;' : ''"
              @click="batchDelete()"
            >{{detail.is_deleted == 0 ? '注销' : '激活'}}</div>
            <div
              class="com_pme71"
              @click="editMember()"
              :style="styleObj(Number(detail.is_deleted))"
            >编辑</div>
          </div>
        </div>
        <div class="com_pme72">
          <div class="com_pme73">
            <div class="com_pme74">余额（元）</div>
            <div class="com_pme75">{{Number(detail.has_money).toFixed(2)}}</div>
          </div>
          <div
            v-show="showAddMember !== 'edit'"
            class="com_pme76"
            @click="isShowMember()"
            :style="detail.is_deleted == 0 ? '' : 'background: #BBB;'"
          >充值</div>
        </div>
        <div class="com_pme77">
          <div style="float: left;overflow: hidden;width: 130px;">
            <div class="com_pme78">可用积分</div>
            <div class="com_pme79">{{detail.integral}}</div>
          </div>
          <img alt="" v-show="loginInfo.employeeNumber === ''"
          @click="editScore()" src="../image/zgzn-pos/pc_member_score_edit.png"
          style="float: left;margin-top: 61px;margin-left: 5px;margin-right: 5px;">
          <div
            :class="['com_pme8',
            showAddMember === 'edit' || (point_setting.score_deduction.enable
              && point_setting.score_deduction.deduction_flg === 'exchange') || !show_point_gift
             ? 'hide_btn' : '']"
            @click="setMemberExchange(detail.id, detail.integral, detail.name)"
          >兑换</div>
          <div style="float: left;overflow: hidden;width: 180px;margin-left: 54px;">
            <div class="com_pme78">持有次卡</div>
            <div class="com_pme79">{{allCards}}<span style="font-size: 16px;">张{{restCards}}</span></div>
          </div>
          <div :class="['com_pme8', showAddMember === 'edit' ? 'hide_btn' : '']" @click="lookOnceCard">查看</div>
        </div>
        <!-- 编辑部分 -->
        <div
          v-show="showAddMember === 'edit'"
          style="overflow: hidden;margin-left: 58px;font-weight: bold;height: 240px;"
        >
          <div
            class="com_pme81"
            style="width: 280px;"
          >
            <div
              class="com_pme82"
              style="margin-top: 20px;"
            >
              <div class="com_pme9">会员卡号</div>
              <el-input
                id="vipcodeedit"
                placeholder="请输入会员卡号"
                class="com_pme89"
                v-model="detail.code"
                @input="detail.code = $vipCardNumberFormat(detail.code)"
                @focus="selectText('vipcodeedit')"
              >
              </el-input>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 10px;"
            >
              <div class="com_pme9">支付码</div>
              <el-input
                id="vippasswordedit"
                placeholder="请输入6位以内纯数字"
                type="password"
                class="com_pme89"
                v-model="detail.password"
                maxlength="6"
                @input="detail.password = $allNumberLimit(detail.password)"
                @focus="selectText('vippasswordedit')"
              >
              </el-input>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 10px;height: 36px;"
            >
              <div class="com_pme9">价格等级</div>
              <div
                class="com_pme58"
                @click="detail.pay_type = 1"
              >
                <div
                  class="com_pme59"
                  style="margin-top: 10px;"
                  :style="detail.pay_type === 1 ? 'background: #d5aa76;' : 'background: #FFF;border-color: #D2D5D9;'"
                >
                  <div class="com_pme6"></div>
                </div>
                <div
                  class="com_pme61"
                  style="margin-top: 10px;"
                >零售价</div>
              </div>
              <div
                class="com_pme58"
                style="margin-left: 20px;"
                @click="detail.pay_type = 2"
              >
                <div
                  class="com_pme59"
                  style="margin-top: 10px;"
                  :style="detail.pay_type === 2 ? 'background: #d5aa76;' : 'background: #FFF;border-color: #D2D5D9;'"
                >
                  <div class="com_pme6"></div>
                </div>
                <div
                  class="com_pme61"
                  style="margin-top: 10px;"
                >会员价</div>
              </div>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 10px;"
            >
              <div class="com_pme9">会员折扣</div>
              <el-input
                id="vipdiscedit"
                placeholder="0.1~10.0"
                class="com_pme89"
                v-model="detail.disc"
                @input="detail.disc = $memberDiscountLimit(detail.disc)"
                @blur="checkDisc()"
                @focus="selectText('vipdiscedit')"
              >
                <template slot="suffix">折</template>
              </el-input>
            </div>
          </div>
          <div
            class="com_pme81"
            style="margin-left: 20px;width: 280px;"
          >
            <div
              class="com_pme82"
              style="margin-top: 20px;"
            >
              <div class="com_pme9">会员生日</div>
              <el-date-picker
                v-model="detail.birthday"
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                placeholder="请选择生日"
                style="float: right;width: 200px;"
              >
              </el-date-picker>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 10px;height: 36px;"
            >
              <div class="com_pme9">性别</div>
              <div
                class="com_pme58"
                @click="detail.sex = 1"
              >
                <div
                  class="com_pme59"
                  style="margin-top: 10px;"
                  :style="detail.sex === 1 ? 'background: #d5aa76;' : 'background: #FFF;border-color: #D2D5D9;'"
                >
                  <div class="com_pme6"></div>
                </div>
                <div
                  class="com_pme61"
                  style="margin-top: 10px;"
                >男</div>
              </div>
              <div
                class="com_pme58"
                style="margin-left: 20px;"
                @click="detail.sex = 2"
              >
                <div
                  class="com_pme59"
                  style="margin-top: 10px;"
                  :style="detail.sex === 2 ? 'background: #d5aa76;' : 'background: #FFF;border-color: #D2D5D9;'"
                >
                  <div class="com_pme6"></div>
                </div>
                <div
                  class="com_pme61"
                  style="margin-top: 10px;"
                >女</div>
              </div>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 10px;"
            >
              <div class="com_pme9">联系地址</div>
              <el-input
                id="vipaddredit"
                placeholder="请输入地址"
                class="com_pme89"
                v-model="detail.addr"
                resize="none"
                maxlength="50"
                @focus="selectText('vipaddredit')"
              >
              </el-input>
            </div>
            <div
              class="com_pme82"
              style="margin-top: 8px;line-height: 24px;"
            >
              <div
                class="com_pme9"
                style="margin-top: 8px;"
              >备注</div>
              <el-input
                id="vipremarkedit"
                type="textarea"
                placeholder="请输入备注二十字以内"
                v-model="detail.remark"
                maxlength="20"
                resize="none"
                class="com_pme91"
                @focus="selectText('vipremarkedit')"
              >
              </el-input>
            </div>
          </div>
        </div>
        <!-- 展示部分 -->
        <div
          v-show="showAddMember === 'readonly'"
          style="overflow: hidden;margin-left: 58px;font-weight: bold;height: 240px;"
        >
          <div class="com_pme81">
            <div class="com_pme82">
              <div class="com_pme83">会员卡号</div>
              <div class="com_pme84">{{detail.code || '无'}}</div>
            </div>
            <div class="com_pme82">
              <div class="com_pme83">支付码</div>
              <div class="com_pme84">******</div>
            </div>
            <div class="com_pme82">
              <div class="com_pme83">价格等级</div>
              <div class="com_pme84">{{detail.pay_type == 1 ? '零售价' : '会员价'}}</div>
            </div>
            <div class="com_pme82">
              <div class="com_pme83">会员折扣</div>
              <div class="com_pme84">{{detail.disc}}折</div>
            </div>
          </div>
          <div class="com_pme81">
            <div class="com_pme82">
              <div class="com_pme83">会员生日</div>
              <div class="com_pme84">{{detail.birthday}}</div>
            </div>
            <div class="com_pme82">
              <div class="com_pme83">性别</div>
              <div class="com_pme84">{{detail.sex !== 0 ? (detail.sex === 1 ? '男':'女') : ''}}</div>
            </div>
            <div class="com_pme82">
              <div class="com_pme83">联系地址</div>
              <div
                class="com_pme85"
                style="line-height: 24px;margin-top: -4px;"
              >{{detail.addr}}</div>
            </div>
            <div
              class="com_pme82"
              style="line-height: 24px;margin-top: 26px;"
            >
              <div class="com_pme83">备注</div>
              <div class="com_pme85">{{detail.remark}}</div>
            </div>
          </div>
        </div>
        <div
          v-show="showAddMember === 'readonly'"
          style="overflow: hidden;margin-left: 50px;"
        >
          <div :style="detail.is_deleted == 0 ? '' : 'background: #BBB;border-color: #BBB;color: #FFF;'" class="com_pme86"
          @click="buyTimesCard()">购买次卡</div>
          <div style="float:right" v-show="ultimate === true">
            <div
              class="com_pme87"
              :style="detail.is_deleted == 0 ? '' : 'background: #BBB;border-color: #BBB;color: #FFF;'"
              @click="pickUp()"
            >取件</div>
            <div
              class="com_pme87"
              style="margin-right: 20px;"
              :style="detail.is_deleted == 0 ? '' : 'background: #BBB;border-color: #BBB;color: #FFF;'"
              @click="mail()"
            >寄件</div>
          </div>
        </div>
        <div
          v-show="showAddMember === 'edit'"
          style="overflow: hidden;margin-left: 50px;"
        >
          <div
            class="com_pme87"
            style="float: left;"
            @click="cancelEdit()"
          >取消</div>
          <div
            class="com_pme86"
            style="float: right;margin-right: 50px;width: 120px;"
            @click="subMember(false)"
          >保存</div>
        </div>
        <div style="height: 30px;"></div>
      </div>
    </div>
    <!--寄件弹窗-->
    <el-dialog
      title="寄件"
      :visible.sync="showMail"
      custom-class="dialog-title"
      v-if="showMail"
      @close='closeMail'
      :close-on-click-modal='false'
      destroy-on-close
      width='700px'
      top='3vh'
    >
      <Mail
        @dismissMailDialog='dismissMailDialog'
        :vip_id='detail.id'
        :vip_name='detail.name'
      />
    </el-dialog>
    <!--取件弹窗-->
    <el-dialog
      :title="pickUpTitleText"
      :visible.sync="showPickUp"
      custom-class="dialog-title"
      v-if="showPickUp"
      @close='closePickUp'
      :close-on-click-modal='false'
      destroy-on-close
      width='700px'
      top='3vh'
      :show-close='false'
    >
      <PickUp
        @dismissPickUpDialog='dismissPickUpDialog'
        @changeDialogTitle='changeDialogTitle'
        :vip_id='detail.id'
        :vip_name='detail.name'
      />
    </el-dialog>
    <!--激活会员弹窗-->
    <el-dialog
      :visible.sync="showActivatedVip"
      :show-close='false'
      :close-on-click-modal='false'
      width="450px"
      top='214px'
      custom-class='add_member'
    >
      <div class="active_tips_dialog">
        <div class="title">提示</div>
        <div class="content">该手机号会员已注销，<br />是否激活</div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            id="showEdit"
            @click="showActivatedVip = false"
          >取消</div>
          <div
            class="btn"
            id="restartBut"
            style="margin-left:30px"
            @click="restartMember"
          >激活</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import { Dialog } from 'element-ui';

export default {
  components: {
    [Dialog.name]: Dialog
  },
  data () {
    return {
      showActivatedVip: false, // 激活会员弹窗
      restCards: '',
      allCards: '',
      isShow: false,
      showMail: false,
      detail: {
        name: '',
        mobile: '',
        password: '',
        disc: '',
        has_money: 0,
        birthday: '',
        addr: '',
        remark: '',
        pay_type: 1,
        sex: 1
      },
      mid_detail: {
        name: '',
        mobile: '',
        password: '',
        disc: '',
        has_money: '',
        birthday: '',
        addr: '',
        remark: '',
        pay_type: 1,
        sex: 1
      },
      show_point_gift: false,
      addMemberMoney: '',
      gift_money: '',
      show_delete: false,
      permit_input: true,
      pickerOptions: {
        disabledDate (time) {
          return time.getTime() > Date.now();
        }
      },
      id: '',
      show_change_cardno: '',
      member_adding: false,
      submit_click: false,
      point_setting: {
        score_deduction: {
          enable: false,
          deduction_flg: ''
        }
      },
      showEdit: false,
      input_score: '',
      restartVipId: [],
      numberMargin: 'margin-top: 20px',
      pickUpTitleText: '取件'
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    editScore() {
      if (this.detail.integral === '刷新中...') {
        return;
      }
      this.input_score = this.detail.integral;
      this.showEdit = true;
      demo.actionLog(logList.clickVipDetailChangePointDialogShow);
    },
    saveScore() {
      demo.actionLog({page: 'pc_add_member', action: 'saveVipPoint', description: `会员-会员详情-积分修改点击保存(${this.detail.integral}-->${this.input_score})`});
      let that = this;
      demo.$http.post(this.$rest.pc_editVipPoints, {
        'systemName': $config.systemName,
        'subName': $config.subName,
        'phone': this.sysUid,
        'sysSid': this.sysSid,
        'storeName': this.username,
        'id': this.id,
        'point': Number(this.input_score),
        'oldPoint': this.detail.integral
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          if (res.data.code === '0') {
            that.showEdit = false;
            that.detail.integral = Number(that.input_score);
            that.memberDetail.integral = Number(that.input_score);
          } else {
            demo.msg('warning', res.data.msg);
          }
        });
    },
    styleObj(is_del) {
      return {
        background: is_del === 0 && this.$employeeAuth('edit_vips') ? '' : '#BBB',
        borderColor: is_del === 0 && this.$employeeAuth('edit_vips') ? '' : '#BBB',
        color: is_del === 0 && this.$employeeAuth('edit_vips') ? '' : '#FFF',
        opacity: this.$employeeAuth('edit_vips') ? '' : '0.4'
      };
    },
    lookOnceCardCount() {
      demo.$http.post(this.$rest.pc_getRestCardsAndTimes, {
        'systemName': $config.systemName,
        'phone': this.sysUid,
        'sysSid': this.sysSid,
        'vipId': this.id
      }, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          if (res.data.code === '0') {
            this.allCards = res.data.data.allCards;
            this.restCards = '(' + res.data.data.restCards + '张可用)';
          } else {
            demo.msg('warning', res.data.msg);
          }
        });
    },
    /**
     * 查看次卡
     */
    lookOnceCard() {
      if (this.detail.is_deleted === 1) {
        return;
      }
      this.SET_SHOW({clickFrom: 'vip'});
      this.SET_SHOW({showTimesCardTab: false, showTimesCard: true, vipId: this.detail.id});
    },
    /**
     * 购买次卡
     */
    buyTimesCard() {
      if (Number(this.detail.is_deleted) !== 0) {
        return;
      }
      this.SET_SHOW({showTimesCardTab: true, showTimesCard: true, vipId: this.detail.id});
    },
    /**
     * 寄件
     */
    mail () {
      demo.actionLog(logList.clickVipDetailSending);
      if (Number(this.detail.is_deleted) !== 0) {
        return;
      }
      this.showMail = true;
    },
    dismissMailDialog () {
      this.showMail = false;
    },
    closeMail () {
      this.showMail = false;
    },
    /**
     * 取件
     */
    pickUp () {
      demo.actionLog(logList.clickVipDetailPickUp);
      if (Number(this.detail.is_deleted) !== 0) {
        return;
      }
      this.SET_SHOW({ show_pick_up: true });
    },
    /**
     * 监听弹窗title发生变化
     */
    changeDialogTitle(value) {
      this.pickUpTitleText = value;
    },
    dismissPickUpDialog () {
      this.SET_SHOW({ show_pick_up: false });
      this.pickUpTitleText = '取件';
    },
    closePickUp () {
      console.log('弹窗关闭');
      this.SET_SHOW({ show_pick_up: false });
    },

    setMemberExchange (id, integral, name) {
      demo.actionLog(logList.clickVipDetailExchange);
      if (integral === 0) {
        return demo.msg('warning', '积分为0，无法兑换');
      }
      if (pos.network.isConnected()) {
        this.SET_SHOW({ showMemberExchangeId: id,
          showMemberExchangeIntegral: integral,
          showMemberExchange: true,
          showMemberExchangeName: name });
      } else {
        demo.msg('warning', '本地网络处于离线状态，兑换商品暂时无法使用');
      }
    },
    deleteCardno () {
      this.SET_SHOW({ cardNo: '' });
    },
    cancel () {
      this.SET_SHOW({ showAddMember: 'close' });
    },
    checkDisc () {
      if (isNaN(this.detail.disc) == true || Number(this.detail.disc) < 0.1) {
        this.detail.disc = 10.0;
      } else if (this.detail.disc > 10) {
        this.detail.disc = 10.0;
      } else {
        this.detail.disc = Number(this.detail.disc).toFixed(1);
      }
    },
    // 删除前确认会员信息
    batchDelete () {
      if (!this.$employeeAuth('cancel_vips')) {
        return;
      }
      if (this.id) {
        demo.actionLog({page: 'pc_add_member', description: `${this.detail.is_deleted == 0 ? '注销' : '激活'}弹窗出现，准备${this.detail.is_deleted == 0 ? '注销' : '激活'}会员手机号：${this.detail.mobile}`});
        this.show_delete = true;
      } else {
        demo.msg('warning', '请选择要' + (Number(this.detail.is_deleted) === 0 ? '注销' : '激活') + '的会员！');
      }
    },
    editMember () {
      if (Number(this.detail.is_deleted) !== 0 || !this.$employeeAuth('edit_vips')) {
        return;
      }
      this.SET_SHOW({ showAddMember: 'edit' });
      this.mid_detail = _.cloneDeep(this.detail);
      this.permit_input = !this.permit_input;
      setTimeout(function () {
        $('#member_name_edit').focus();
      }, 0);
    },
    deleteMember () {
      var _this = this;
      var member_data = {
        'systemName': $config.systemName,
        'phone': this.sysUid,
        'sysSid': this.sysSid,
        'revise_user': this.phone,
        'id': _this.id,
        'is_deleted': Number(this.detail.is_deleted) === 0 ? 1 : 0
      };
      console.log(member_data, 'member_data');
      if (pos.network.isConnected()) {
        demo.$http.post(_this.$rest.pc_deleteOrRecoveryVip, member_data, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (res) {
            if (res.data.code === '0') {
              demo.msg('success', '会员信息' + (Number(_this.detail.is_deleted) === 0 ? '注销' : '激活') + '成功');
              demo.actionLog({page: 'pc_add_member', description: `重新${_this.detail.is_deleted == 0 ? '注销' : '激活'}成功，会员手机号：${_this.detail.mobile}`});
            }
            _this.show_delete = false;
            _this.SET_SHOW({ showAddMember: 'close' });
            _this.detail = {
              name: '',
              mobile: '',
              code: '',
              integral: '',
              password: '',
              disc: '',
              has_money: '',
              birthday: '',
              addr: '',
              remark: '',
              pay_type: 1,
              sex: 1
            };
            _this.SET_SHOW({ member_detail: _this.detail });
          });
      } else {
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    restartMember () {
      var _this = this;
      var member_data = {
        'systemName': $config.systemName,
        'phone': this.sysUid,
        'sysSid': this.sysSid,
        'revise_user': this.phone,
        'id': this.restartVipId[0],
        'is_deleted': 0
      };
      console.log(member_data, 'member_data');
      if (pos.network.isConnected()) {
        demo.$http.post(_this.$rest.pc_deleteOrRecoveryVip, member_data, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (res) {
            if (res.data.code === '0') {
              demo.msg('success', '会员信息激活成功');
            }
            _this.showActivatedVip = false;
            _this.SET_SHOW({ showAddMember: 'close' });
          });
      } else {
        demo.msg('warning', '本地网络处于离线状态，会员/云同步功能将无法使用');
      }
    },
    cancelEdit () {
      if (this.showAddMember === 'new') {
        this.SET_SHOW({ showAddMember: 'close' });
      } else {
        this.detail = this.mid_detail;
        this.SET_SHOW({ showAddMember: 'readonly' });
      }
    },
    isShowMember () {
      if (Number(this.detail.is_deleted) !== 0) {
        return;
      }
      if (this.detail.id === null || this.detail.id === '') {
        demo.msg('warning', this.$msg.select_vip);
      } else {
        this.showMemberDetails();
      }
    },
    subMember (flg) {
      if (flg) {
        demo.actionLog(logList.clickAddVipSaveAndRecharge);
      }
      console.log(this.detail);
      const lastValue = this.$vipCardNumberFormat(this.detail.code);
      if (!demo.isNullOrTrimEmpty(this.detail.code) && lastValue !== this.detail.code) {
        demo.msg('warning', '会员卡号不合法，请重新输入（仅允许15位以内的数字和字母）');
        return false;
      }
      var _this = this;
      if (this.submit_click === true) {
        return;
      }
      this.submit_click = true;
      setTimeout(function () {
        _this.submit_click = false;
      }, _this.clickInterval);
      if (_this.sonarIf()) {
        return;
      }
      var detaillist = {
        'systemName': $config.systemName,
        'phone': _this.sysUid,
        'sysSid': _this.sysSid,
        'code': _this.detail.code,
        'integral': _this.detail.integral,
        'name': _this.detail.name,
        'mobile': _this.detail.mobile,
        'password': _this.detail.password,
        'disc': Number(_this.detail.disc).toFixed(1),
        'has_money': _this.detail.has_money,
        'birthday': _this.detail.birthday,
        'addr': _this.detail.addr,
        'remark': _this.detail.remark,
        'payType': _this.detail.pay_type,
        'sex': _this.detail.sex
      };
      console.log(detaillist, 'detaillist');
      if (this.member_adding === true) {
        return;
      }
      this.member_adding = true;
      var url = '';
      if (_this.showAddMember === 'new') {
        url = _this.$rest.pc_addvip;
        detaillist.createUser = _this.phone;
      } else {
        url = _this.$rest.pc_editvip;
        detaillist.id = _this.id;
        detaillist.revise_user = _this.phone;
      }
      demo.$http.post(url, detaillist, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (res) {
          if (_this.showEdit) {
            _this.showEdit = false;
            return;
          }
          _this.sonarSubMember(res, flg);
        })
        .catch(function (error) {
          _this.member_adding = false;
          console.error(error);
        });
    },
    sonarSubMember(res, flg) {
      this.member_adding = false;
      if (res.data.code === '0' && flg) {
        demo.msg('success', this.$msg.save_success);
        this.SET_SHOW({
          member_detail: res.data.data
        });
        this.SET_SHOW({ showAddMember: 'close' });
        this.SET_SHOW({
          showInputRecharge: true
        });
      } else if (res.data.code === '0') {
        demo.msg('success', this.$msg.save_success);
        this.SET_SHOW({ showAddMember: 'close' });
      } else {
        // 该手机号的会员已经存在且未激活
        if (res.data.code === '28') {
          // 再判断是注销的会员再弹激活弹窗
          this.restartVipId = res.data.data;
          this.showActivatedVip = true;
        } else {
          demo.msg('warning', res.data.msg);
        }
      }
    },
    sonarIf() {
      let _this = this;
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
        return true;
      }
      if (!_this.detail.name || _this.detail.name.trim() === '') {
        demo.msg('warning', '请输入姓名');
        return true;
      }
      if (!_this.detail.mobile) {
        demo.msg('warning', '请输入手机号');
        return true;
      }
      if (_this.detail.mobile.length !== 11) {
        demo.msg('warning', '请输入11位手机号');
        return true;
      }
      return false;
    },
    initDetail () {
      this.SET_SHOW({
        member_detail: {
          name: '',
          mobile: '',
          code: '',
          integral: '',
          password: '',
          disc: '10.0',
          has_money: '',
          birthday: '',
          addr: '',
          remark: '',
          pay_type: 1,
          sex: 1
        }
      });
      this.detail = {
        name: '',
        mobile: '',
        code: '',
        integral: '',
        password: '',
        disc: '10.0',
        has_money: '',
        birthday: '',
        addr: '',
        remark: '',
        pay_type: 1,
        sex: 1
      };
    },
    // 充值明细显示
    showMemberDetails () {
      this.SET_SHOW({
        showInputRecharge: true
      });
    },
    getMemberMoney () {
      var that = this;
      var vipdata = {
        'systemName': $config.systemName,
        'phone': that.sysUid
      };
      vipdata.id = that.id ? that.id : that.detail.id;
      demo.$http.post(that.$rest.pc_getVipById, vipdata, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (rs) {
          that.detail.has_money = rs.data.data.hasMoney;
          that.memberDetail.has_money = rs.data.data.hasMoney;
        });
    },
    getMemberIntegral () {
      var that = this;
      var vipdata = {
        'systemName': $config.systemName,
        'phone': that.sysUid,
        'id': that.detail.id
      };
      if (pos.network.isConnected()) {
        demo.$http.post(that.$rest.pc_getVipById, vipdata, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(function (rs) {
            that.detail.integral = rs.data.data.integral;
            that.memberDetail.integral = rs.data.data.integral;
          });
      }
    },
    /**
     * 获取会员设置数据
     */
    getSettings() {
      demo.$http
        .post(
          this.$rest.pc_vipGetSetting,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            if (res.data.data) {
              this.point_setting = res.data.data.pointSetting
                ? demo.t2json(res.data.data.pointSetting)
                : this.point_setting;
              if (demo.t2json(res.data.data.pointSetting).score_deduction.enable === true &&
                demo.t2json(res.data.data.pointSetting).score_deduction.deduction_flg === 'gift' &&
                this.$employeeAuth('exchange_vip_points')) {
                this.show_point_gift = true;
              } else {
                this.show_point_gift = false;
              }
            }
          } else {
            this.show_point_gift = false;
            if (res.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
              demo.msg('warning', res.data.msg);
            }
          }
        });
    },
    getAddNumberMargin() {
      let marginTop = document.body.clientHeight > 630 ? (document.body.clientHeight - 630) / 2 : 0;
      this.numberMargin = 'margin-top:' + marginTop + 'px';
    }
  },
  watch: {
    'detail.code': {
      handler () {
        if (!this.detail.code) {
          this.SET_SHOW({ cardNo: '' });
        }
      }
    },
    showMemberExchange () {
      if (this.showMemberExchange === false && this.isPay !== true) {
        this.detail.integral = '刷新中...';
        this.getMemberIntegral();
      }
    },
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      if (this.showAddMember === 'readonly') {
        return;
      }
      if (this.showAddMember !== 'close') {
        if (this.detail.code !== this.cardNo && this.detail.code && this.cardNo !== '') {
          this.show_change_cardno = true;
        }
        if (this.detail.code !== this.cardNo && !this.detail.code) {
          this.detail.code = this.cardNo;
        }
      }
    },
    showAddMember () {
      this.SET_SHOW({ cardNo: '' });
      if (this.showAddMember === 'new') {
        this.initDetail();
        this.permit_input = true;
        setTimeout(function () {
          $('#member_name').focus();
        }, 0);
      } else if (this.showAddMember === 'readonly') {
        this.permit_input = false;
        this.detail = _.cloneDeep(this.memberDetail);
        this.id = this.memberDetail.id;
        this.lookOnceCardCount();
        console.log('请求');
        this.getSettings();
      } else if (this.showAddMember === 'edit') {
        this.detail = _.cloneDeep(this.memberDetail);
        this.id = this.memberDetail.id;
        setTimeout(function () {
          $('#member_name_edit').focus();
        }, 0);
        this.lookOnceCardCount();
      } else {
        console.log('其他');
      }
    },
    showInputRecharge () {
      // 如果充值页面关闭，肯定处于只读状态，刷新会员储值金额
      if (this.showInputRecharge === false && this.showAddMember !== 'close') {
        this.getMemberMoney(1);
        this.detail.integral = '刷新中...';
        this.getMemberIntegral();
      }
    },
    showTimesCard() {
      if (!this.showTimesCard && this.clickFrom === 'vip') {
        this.lookOnceCardCount();
        this.getMemberMoney(1);
      }
    },
    showEdit() {
      if (this.showEdit) {
        let that = this;
        setTimeout(() => {
          that.$refs.nowUsePoint.select();
        }, 100);
      }
    }
  },
  mounted() {
    let that = this;
    that.getAddNumberMargin();
    window.onresize = () => {
      if (that.addNumMargin) {
        clearTimeout(that.addNumMargin);
      }
      that.addNumMargin = setTimeout(() => {
        that.getAddNumberMargin();
      }, 300);
    };
  },
  beforeDestroy() {
    window.onresize = null;
  },
  computed: mapState({
    showAddMember: state => state.show.showAddMember,
    memberDetail: state => state.show.member_detail,
    sysUid: state => state.show.sys_uid,
    phone: state => state.show.phone,
    sysSid: state => state.show.sys_sid,
    cardNo: state => state.show.cardNo,
    showInputRecharge: state => state.show.showInputRecharge,
    isPay: state => state.show.isPay,
    clickInterval: state => state.show.clickInterval,
    showMemberExchange: state => state.show.showMemberExchange,
    showPickUp: (state) => state.show.show_pick_up,
    showTimesCard: (state) => state.show.showTimesCard,
    ultimate: (state) => state.show.ultimate,
    showTimesCardTab: (state) => state.show.showTimesCardTab,
    username: (state) => state.show.username,
    loginInfo: state => state.show.loginInfo,
    clickFrom: state => state.show.clickFrom
  })
};
</script>
