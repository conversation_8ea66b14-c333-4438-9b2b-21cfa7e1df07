<template>
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left_container">
        <div class='search_input_box'
        :style="inputing_keyword ? 'border-color: #d5aa76' : 'border-color: #e3e6eb;'"
        >
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder='商品名称/条码/首字母/扫码'
            :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
            id='search_input_keyword_deposit'
            v-model='keyword'
            @input="keyword = $goodsNameFormat(keyword)"
          />
          <img
            alt=""
            class='search_input_delete'
            v-show="keyword != ''"
            @click="focusInput('search_input_keyword_deposit')"
            src='../image/pc_clear_input.png'
          />
        </div>
        <div class='search_input_box ml-14'
        :style="second_inputing_keyword ? 'border-color: #d5aa76' : 'border-color: #e3e6eb;'"
        >
          <input
            @focus='second_inputing_keyword = true'
            @blur='second_inputing_keyword = false'
            type='text'
            placeholder='会员姓名/手机号/刷卡'
            :style="secondKeyword ? 'color: #567485;' : 'color: #B1C3CD;'"
            id='second_search_input_keyword_deposit'
            v-model='secondKeyword'
            @input="secondKeyword = $vipNameFormat(secondKeyword)"
          />
          <img
            alt=""
            class='search_input_delete'
            v-show="secondKeyword != ''"
            @click="focusInput('second_search_input_keyword_deposit')"
            src='../image/pc_clear_input.png'
          />
        </div>
        <div class="btn-query" @click="queryData">查询</div>
      </div>
      <div class="btn_export_excel" @click="exportExcel">导出表格</div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        :empty-text="!loading ? '暂无数据' : ' '"
        stripe
      >
        <el-table-column
          show-overflow-tooltip
          prop="vipName"
          label="会员"
          align="left"
          min-width="15%"
        >
        </el-table-column>
        <el-table-column
          prop="vipMobile"
          label="手机号"
          align="center"
          min-width="15%"
        >
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="goodsName"
          label="商品名称"
          align="left"
          min-width="40%"
        >
        </el-table-column>
        <el-table-column
          prop="code"
          label="条码"
          align="left"
          min-width="15%"
        >
        </el-table-column>
        <el-table-column
          prop="count"
          label="剩余可取数量"
          align="center"
          min-width="15%"
        >
        </el-table-column>
      </el-table>
      <div class="table_bottom" style="color:#567485">
        <div>
          总有记录：<span>{{totalRecord}}</span>，总剩余量：<span>{{totalCount}}</span>
        </div>
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="totalRecord"
          @current-change="handleCurrentChange"
          :current-page="pagenum"
          :page-size="limit"
        >
        <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="limit"
            :currentPage.sync="pagenum"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { showToast } from '@/utils/util.js';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    vCjPageSize
  },
  data() {
    return {
      loading: false,
      inputing_keyword: false,
      second_inputing_keyword: false,
      keyword: '',
      secondKeyword: '',
      tableHeight: 0,
      tableData: [],
      counts: {},
      pageKey: 0,
      pagenum: 1,
      limit: 10,
      totalRecord: 0,
      totalCount: 0
    }
  },
  created () {
    this.tableHeight = screen.availHeight - 200;
    // this.limit = Math.floor((this.tableHeight - 50) / 50);
    demo.actionLog(logList.clickJiCunResidue);
  },
  mounted () {
    setTimeout(() => {
      $('#search_input_keyword_deposit').focus();
    }, 0);
    this.queryData();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      if (sid === 'search_input_keyword_deposit') {
        this.keyword = '';
      }
      if (sid === 'second_search_input_keyword_deposit') {
        this.secondKeyword = '';
      }
      $('#' + sid).focus();
    },
    /**
     * 点击页码
     */
    handleCurrentChange (pagenum) {
      this.pagenum = pagenum;
      this.byPageShow(this.pagenum);
    },
    handleSizeChange() {
      this.byPageShow(this.pagenum);
    },
    /**
     * 导出Excel
     */
    exportExcel() {
      demo.actionLog(logList.clickJiCunResidueExportExcel);
      if (!this.matchRes || this.matchRes.length === 0) {
        return demo.msg('warning', '暂无可导出的数据');
      }
      const field_mapping = {
        会员: 'vipName',
        手机号: 'vipMobile',
        商品名称: 'goodsName',
        条码: 'code',
        剩余可取数量: 'count'
      };
      this.$makeExcel(this.matchRes, field_mapping, '寄存剩余' + new Date().format('yyyyMMddhhmmss'));
    },

    /**
     * 查询数据
     */
    async queryData() {
      // 每次查询，左下角汇总数据先重置为0
      this.totalRecord = 0;
      this.totalCount = 0;
      // 页码重置为1
      this.pagenum = 1;
      const res = await this.getVipRecordData();
      console.log('云上寄存剩余数据：', res);
      const local = await this.queryLocalData(res);
      console.log('查出的本地商品数据：', local);
      this.matchRes = this.matchData(res, local).filter(item => item.goodsName);
      console.log('匹配出所有符合条件的数据(含检索条件)：', this.matchRes);
      this.calcTotalValue(this.matchRes);
      this.byPageShow(this.pagenum);
    },

    /**
     * 获取云上数据
     */
    getVipRecordData() {
      return new Promise(resolve => {
        this.loading = true;
        const params = {
          searchStr: this.secondKeyword.trim()
        }
        demo.$http
          .post(this.$rest.pc_depositSurplus, params, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: demo.$store.state.show.token
            },
            maxContentLength: Infinity,
            timeout: 60000
          })
          .then(rs => {
            setTimeout(() => {
              this.loading = false;
            }, this.delayedTime);
            const res = rs.data;
            if (res.code === '0') {
              resolve(res.data);
              return;
            }
            showToast(this, res.msg);
          }).catch(() => {
            setTimeout(() => {
              this.loading = false;
            }, this.delayedTime);
          });
      });
    },
    /**
     * 查出的本地数据
     */
    queryLocalData(array) {
      return new Promise(resolve => {
        const fingerprintArr = [];
        for (let i = 0; i < array.length; i++) {
          fingerprintArr.push(array[i].fingerprint);
        }
        const params = {
          pset: '',
          type: '',
          condition: this.keyword.trim(),
          fingerprint: fingerprintArr,
          fingerprintFlg: false,
          selectDel: true,
          vipGoods: true,
          escape: true,
          limit: 65535,
          offset: 0
        };
        goodService.search(params, res => {
          resolve(demo.t2json(res));
        });
      });
    },

    /**
     * 匹配出最终显示数据
     */
    matchData(res, local) {
      for (let i = 0; i < res.length; i++) {
        for (let j = 0; j < local.length; j++) {
          res[i].count = +res[i].count;
          if (res[i].fingerprint === local[j].fingerprint) {
            res[i].goodsName = local[j].name;
            res[i].code = local[j].code;
            break;
          }
        }
      }
      return res;
    },

    /**
     * 计算左下角汇总数据
     */
    calcTotalValue(matchRes) {
      this.totalRecord = matchRes.length;
      for (let i = 0; i < matchRes.length; i++) {
        this.totalCount += matchRes[i].count;
      }
    },

    /**
     * 本地进行分页展示
     */
    byPageShow(pagenum) {
      this.tableData = this.matchRes.slice((pagenum - 1) * this.limit, pagenum * this.limit);
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    delayedTime: state => state.show.delayedTime,
    sysSid: state => state.show.sys_sid
  })
}
</script>

<style lang='less' scoped>
.ml-14 {
  margin-left: 14px;
}
.btn-query {
  cursor: pointer;
  width: 110px;
  height: 44px;
  line-height: 44px;
  margin-left: 14px;
  font-weight: bold;
  border-radius: 22px;
  text-align: center;
  color: #FFFFFF;
  font-size: 18px;
  background: @themeBackGroundColor;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  font-size: 16px;
  padding-left: 23px;
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-range-editor.el-input__inner {
  border-radius: 50px;
}
/deep/ .el-date-editor .el-range__icon {
  display: none;
}
/deep/ .el-range-editor .el-range-input {
  margin-left: 12px;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-range-separator {
  color: rgba(177, 195, 205, 100);
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .top_left .el-input__inner {
  color: #C0C4CC;
}
.deposit_record_container{
  background: #F5F8FB;
  .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 10px;
    .top_left_container{
      display: flex;
      align-items: center;
      .top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
        .top_left_title{
          color: @themeFontColor;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .btn_export_excel{
      cursor: pointer;
      width: 110px;
      height: 44px;
      line-height: 44px;
      margin-right: 10px;
      font-weight: bold;
      border-radius: 22px;
      text-align: center;
      color: #FFFFFF;
      font-size: 18px;
      background: @themeBackGroundColor;
    }
    .top_right{
      display: flex;
      align-items: center;
      .top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
      .table_bottom{
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        padding-left: 10px;
        padding-right: 13px;
        background: white;
        span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  display: flex;
  align-items: center;
}
.table_name_code_column{
  .p_name {
    height: 22px;
    margin-bottom: 10px;
    line-height: 36px;
    font-size: 16px;
  }
  .p_spec {
    height: 22px;
    margin-bottom: 5px;
    line-height: 22px;
    font-size: 16px;
  }
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    background: #FFFFFF;
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  padding-right: 0;
}
/deep/ .el-table th > .cell {
  padding-left:10px;
  padding-right:0;
}
/deep/ .el-table td {
  padding-left:0;
  color:@themeFontColor;
}
/deep/ .el-table .cell {
  padding-left:10px;
  padding-right:0;
}
</style>
