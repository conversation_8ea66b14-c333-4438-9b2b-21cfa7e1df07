<style lang='less' scoped>
.notice-container {
  width: calc(100% - 8px);
  margin: 30px 0;
  height: calc(100% - 110px);
  overflow-y: auto;
  font-family: 'HarmonyOSSansSC', sans-serif;
}
.offline-container {
  width: 100%;
  height: 100%;
  font-family: 'HarmonyOSSansSC', sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
}
.content-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px auto;
  max-width: 866px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ECECEC;
}
.cover-image {
  width: 200px;
  height: 356px;
  height: auto;
  margin-right: 20px;
  border-radius: 20px;
}
.content-info {
  flex: 1;
  height: 356px;
  position: relative;
  .content-title {
    font-weight: bold;
    font-size: 24px;
    line-height: 28px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    overflow-wrap: break-word;
  }
  .content-description {
    margin-top: 16px;
    width: 350px;
    height: 130px;
    color: #878787;
    overflow-wrap: break-word;
  }
}
.qr-code-section {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .scan-tip {
    background: linear-gradient(to right, #1400FF, #00ACFF);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    border: 1px solid #B0BEF6;
    border-radius: 8px;
    width: 180px;
    height: 42px;
    line-height: 42px;
    text-align: center;
  }
}
.qr-code {
  width: 120px;
  height: 120px;
  position: absolute;
  bottom: 8px;
  left: 8px;
  border-radius: 16px;
}
.scan-code {
  display: flex;
  justify-content: center;
  align-items: center;
}
.qr-code-container {
  position: relative;
  margin-right: 10px;
}
.img-bg {
  width: 136px;
  height: 176px;
}
.off_line_img {
  width: 160px;
  height: 160px;
}
.off-qr-code-section {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.off-box {
  margin-top: -15vh;
}
.off_line_text {
  color: #878787;
  font-size: 16px;
}
.scan-text {
  font-size: 24px;
  font-weight: bold;
  margin: 30px auto 16px;
}
/deep/ .el-image__error {
  border-radius: 16px;
}
.notice-container::-webkit-scrollbar {
  width: 6px;
}
.notice-container::-webkit-scrollbar-thumb {
  background: #dcdfe6;
  border-radius: 3px;
}

</style>
<template>
  <div v-loading.fullscreen.lock="loading">
    <cj-header backText="返回 [Esc]" :enableEsc="true" @leftClick="close"></cj-header>
    <div v-if="noticeArr.length" class="notice-container">
      <div v-for="item in noticeArr" :key="item.id">
        <div class="content-item">
          <img :src="item.coverImagePath" alt="封面图" class="cover-image" />
          <div class="content-info">
            <div class="content-title">
              {{ item.title }}
            </div>
            <div class="content-description">
              {{ item.description }}
            </div>
            <div class="qr-code-section">
              <div class="scan-tip">扫码解锁视频号/抖音→</div>
              <div class="scan-code">
                <div v-if="item.videoQrcodePath" class="qr-code-container">
                  <img src="../../image/wechatQR.png" class="img-bg" />
                  <el-image :src="item.videoQrcodePath" alt="微信二维码" class="qr-code" />
                </div>
                <div v-if="item.douyinQrcodePath" class="qr-code-container">
                  <img src="../../image/douyinQR.png" alt="抖音二维码" class="img-bg" />
                  <el-image :src="item.douyinQrcodePath" alt="微信二维码" class="qr-code" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="offline-container">
      <div class="off-box">
        <template v-if="offline">
          <img class="off_line_img" src="../../image/pc_offline_said.png"/>
          <div class="off_line_text">当前页面无网络，请检查网络连接</div>
        </template>
        <template v-else>
          <img class="off_line_img" src="../../image/pc_search_empty.png"/>
          <div class="off_line_text">暂无作品</div>
        </template>
        <div class="scan-text">不想错过精彩视频？扫码关注我们！</div>
        <div class="off-qr-code-section">
          <div class="scan-code">
            <div class="qr-code-container">
              <img src="../../image/wechatQR.png" class="img-bg" />
              <img src="../../image/pc_offline_wechat.png" alt="微信二维码" class="qr-code" />
            </div>
            <div class="qr-code-container">
              <img src="../../image/douyinQR.png" alt="抖音二维码" class="img-bg" />
              <img src="../../image/pc_offline_douyin.png" alt="抖音二维码" class="qr-code" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import CjHeader from '@/common/components/CjHeader';
export default {
  components: {
    CjHeader
  },
  data () {
    return {
      offline: false,
      loading: false,
      noticeArr: []
    };
  },
  created() {
    this.loadPromotionList();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    loadPromotionList() {
      this.loading = true;
      if (!pos.network.isConnected()) { // 离线
        this.offline = true;
        this.loading = false;
        return;
      }
      const param = {
        pageNum: 1,
        pageSize: 10
      }
      demo.$http.get(this.$rest.promotionList, { params: param })
        .then(res => {
          if (res.data.code === 200 && res.data.data) {
            this.noticeArr = res.data.data.list;
          }
          this.loading = false;
        })
        .catch(error => {
          CefSharp.PostMessage(`品牌推广列表查询Error:${error}`);
          this.loading = false;
        });
    },
    close() {
      this.$emit('close');
      setTimeout(() => {
        this.SET_SHOW({ isRepositoryNotice: false, isHome: true });
      }, 0);
    }
  },
  computed: mapState({
    ultimate: state => state.show.ultimate
  })
};
</script>
