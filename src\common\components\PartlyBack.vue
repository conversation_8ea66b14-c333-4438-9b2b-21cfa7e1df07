<template>
  <div class="partlyBackPage">
    <cj-header title="退货" @leftClick="close"></cj-header>
    <div class="backContent">
      <div class="orderNo">单号：{{ backOrderInfo.code }}</div>
      <div class="listWrap">
        <el-table :data="list" height="100%" header-cell-class-name="tableHeader">
          <el-table-column type="index" label="序号" width="64" align="center"></el-table-column>
          <el-table-column label="品名/条码" min-width="422" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="goodName">{{ scope.row.goodName }}</div>
              <div class="goodCode">{{ scope.row.code }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="remainingQty" label="可退数量" align="center" min-width="124"></el-table-column>
          <el-table-column prop="discPrice" align="center" min-width="124">
            <div slot="header">
              <span>折算单价</span>
              <el-tooltip effect="dark" placement="top">
                <span slot="content">
                  整单优惠折算后的单品价格（因四舍五入，可能存
                  <br />
                  在尾数差异）
                </span>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </el-table-column>
          <el-table-column prop="maxBackMoney" label="可退金额" align="center" min-width="124"></el-table-column>
          <el-table-column label="退货数量" align="center" min-width="192">
            <template slot-scope="scope">
              <cj-input
                v-model="scope.row.backNum"
                type="number"
                height="40"
                fontSize="20"
                inputAlign="center"
                :clearable="false"
                :decimal="scope.row.isWeight ? 3 : 0"
                :minNumber="0"
                :maxNumber="scope.row.remainingQty"
                focus-select
                placeholder=""
                @input="handleChange($event, scope.row, false)"
                @blur="handleBlur(scope.row)"
              >
                <!-- 暂时取消虚拟数字键盘 -->
                <!-- <div slot="leftIcon" class="iconWrap">
                  <el-popover v-model="scope.row.visible" placement="bottom" trigger="click" :offset="10">
                    <cj-number-keyboard
                      v-if="scope.row.visible"
                      :value.sync="scope.row.backNum"
                      @change="handleChange($event, scope.row, false)"
                      @close="scope.row.visible = false"
                    ></cj-number-keyboard>
                    <div class="mask" slot="reference">
                      <div
                        class="leftIcon"
                        :class="{ iconDisabled: Number(scope.row.backNum) === 0 }"
                        @click.stop="handleChange(-1, scope.row, true)"
                      >
                        <i class="el-icon-minus"></i>
                      </div>
                      <div
                        class="rightIcon"
                        :class="{ iconDisabled: Number(scope.row.backNum) === scope.row.maxNum }"
                        @click.stop="handleChange(1, scope.row, true)"
                      >
                        <i class="el-icon-plus"></i>
                      </div>
                    </div>
                  </el-popover>
                </div> -->
                <div
                  slot="leftIcon"
                  class="leftIcon"
                  :class="{ iconDisabled: Number(scope.row.backNum) === 0 }"
                  @click.stop="handleChange(-1, scope.row, true)"
                >
                  <i class="el-icon-minus"></i>
                </div>
                <div
                  slot="rightIcon"
                  class="rightIcon"
                  :class="{ iconDisabled: +scope.row.backNum === +scope.row.remainingQty }"
                  @click.stop="handleChange(1, scope.row, true)"
                >
                  <i class="el-icon-plus"></i>
                </div>
              </cj-input>
            </template>
          </el-table-column>
          <el-table-column align="center" min-width="192" class-name="backCell">
            <div slot="header">
              <span>退货金额</span>
              <el-tooltip effect="dark" placement="top" content="修改后将以单品折扣的形式体现在退货单中">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <template slot-scope="scope">
              <div class="backPriceWrap">
                <div class="backPrice">{{ Number(scope.row.backPrice).toFixed(2) }}</div>
                <div class="editWrap" @click.stop="handleEditPrice(scope.row, scope.$index)">
                  <i class="el-icon-edit"></i>
                </div>
                <div v-if="scope.row.message" class="priceTips">{{ scope.row.message }}</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footerWrap">
        <div class="info">
          共退货
          <span>{{ totalNumber }}</span>
          件，退货总额：
          <span>￥{{ totalMoney }}</span>
        </div>
        <div class="nextBtn" @click.stop="handleNext">下一步(Space)</div>
      </div>
    </div>
    <!-- 退货弹窗 -->
    <el-dialog
      :visible.sync="showBackMoneyDialog"
      append-to-body
      :show-close="false"
      custom-class="backMoneyDialog"
      :close-on-click-modal="false"
      width="680px"
    >
      <BackMoneyDialog
        v-if="showBackMoneyDialog"
        :params="backMoneyParams"
        @refresh="refreshOrder"
        @backDetail="$emit('backDetail', $event)"
        @closeDialog="showBackMoneyDialog = false"
      />
    </el-dialog>
    <!-- 修改退货金额弹窗 -->
    <el-dialog
      :visible.sync="priceEditShow"
      append-to-body
      custom-class="priceEdiDialog"
      title="退货金额"
      width="548px"
      :close-on-click-modal="false"
    >
      <div v-if="priceEditShow" class="keyboard">
        <cj-input
          ref="editPrice"
          v-model="editBackPrice"
          type="price"
          height="60"
          autofocus
          fontSize="32"
          :clearable="false"
          :minNumber="0"
          :maxNumber="editMaxBackPrice"
          focus-select
          placeholder="请输入金额"
        >
          <span slot="rightIcon" class="priceUnit">元</span>
        </cj-input>
        <cj-number-keyboard @change="editPriceChange"></cj-number-keyboard>
        <div class="btnWrap">
          <div class="cencel" @click.stop="priceEditShow = false">取消</div>
          <div class="confirm" @click="editConfirm">确定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import logList from '@/config/logList';
import { SET_SHOW } from '@/store/show';
import { Keyevent } from '@/utils/keyEvent.js';
import CjInput from '@/common/components/CjInput';
import CjHeader from '@/common/components/CjHeader';
import BackMoneyDialog from '@/components/back_money_dialog';
import CjNumberKeyboard from '@/common/components/CjNumberKeyboard';
import { getGenerateOrder } from '@/api/order';
export default {
  name: 'PartlyBack',
  components: {
    CjInput,
    CjHeader,
    BackMoneyDialog,
    CjNumberKeyboard
  },
  props: {
    // 销售单商品列表
    goodList: {
      type: Array,
      default: () => []
    },
    // 销售单详情
    orderInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      keyEvent: null,
      maxBackMoney: 0, // 此次部分退货最大可退金额
      list: [], // 退货商品列表
      showBackMoneyDialog: false, // 是否显示退款弹窗
      priceEditShow: false, // 是否显示编辑退货金额弹窗
      editBackPrice: 0,
      editMaxBackPrice: 0,
      editIndex: 0,
      backMoneyParams: {},
      backOrderInfo: {}
    };
  },
  computed: {
    // 总退货数量
    totalNumber() {
      const total = this.list.reduce((pre, next) => {
        return +next.backNum + pre;
      }, 0);
      return Number(total.toFixed(3));
    },
    // 总退货金额
    totalMoney() {
      const total = this.list.reduce((pre, next) => {
        return +next.backPrice + pre;
      }, 0);
      return total.toFixed(2);
    },
    // 退货商品列表请求参数
    leftGoodsList() {
      const list = this.list.filter(good => good.backNum > 0);
      const goodsList = list.map(good => {
        // 如果商品全部退完需要和最大可退金额计算，如果未退完需要和当前数量最大可退金额计算
        const maxPrice = good.backNum === good.remainingQty ? good.maxBackMoney : good.maxBackPrice;
        return {
          amt: -good.backPrice,
          code: good.code,
          disc: 1,
          goodFingerprint: good.goodFingerprint,
          goodName: good.goodName,
          itemDisc: (good.backPrice / maxPrice).toFixed(2),
          mprice: good.mprice,
          price: good.discPrice,
          purPrice: good.purPrice,
          qty: -good.backNum,
          unitName: good.unitName,
          fingerprint: good.fingerprint
        };
      });
      return goodsList;
    }
  },
  created() {
    this.backOrderInfo = JSON.parse(JSON.stringify(this.orderInfo));
    this.init(this.goodList);
  },
  mounted() {
    this.SET_SHOW({ isPartlyBack: true });
    this.keyEvent = new Keyevent(this.handleKeyEvent);
    this.keyEvent.start();
  },
  beforeDestroy() {
    if (this.keyEvent) {
      this.keyEvent.stop();
      this.keyEvent = null;
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 订单列表初始化
    init(goodList) {
      this.list = goodList.filter(good => {
        good.message = '';
        // 设置退货数量
        this.$set(good, 'backNum', 0);
        // 设置退货金额
        this.$set(good, 'backPrice', 0);
        this.$set(good, 'maxBackPrice', 0);
        // 设置虚拟键盘控制参数
        this.$set(good, 'visible', false);
        // 设置商品是否为称重商品
        this.$set(good, 'isWeight', this.DICT['FORM'].WEIGHING_UNIT_LIST.includes(good.unitName));
        // 处理商品可退数量可能出现精度问题
        good.remainingQty = Number(good.remainingQty.toFixed(3));
        // 商品的折后单价
        const discPrice = good.price * good.itemDisc * good.disc;
        // 页面显示要保留2位小数
        good.discPrice = discPrice.toFixed(2);
        // 商品的最大初始可退金额
        good.orgMaxBackMoney = (discPrice.toFixed(2) * good.qty).toFixed(2);
        // 商品的此次最大可退金额
        good.maxBackMoney = (discPrice.toFixed(2) * good.remainingQty).toFixed(2);
        // 判断此次最大可退金额是否大于初始可退金额-历史已退金额，如果大于需将此次最大可退金额设为初始可退金额-历史已退金额
        if (+good.maxBackMoney > +good.orgMaxBackMoney + good.refundedAmt) {
          good.maxBackMoney = (+good.orgMaxBackMoney + good.refundedAmt).toFixed(2);
          // // 修改完该商品的最大可退金额反向计算出折扣单价
          // good.discPrice = (good.maxBackMoney / good.remainingQty).toFixed(2);
        }
        return good.remainingQty > 0;
      });
      // 当前销售单可退最大金额
      this.maxBackMoney = +this.backOrderInfo.discAmt;
      // 当前销售单可退最大数量
      this.maxBackNumber = this.goodList.reduce((pre, next) => {
        return pre + next.qty;
      }, 0);
      // 通过整单折扣和单品折扣反算出来的可退最大金额
      const totalMoney = this.goodList.reduce((pre, next) => {
        return +next.orgMaxBackMoney + pre;
      }, 0);
      console.warn('totalMoney', totalMoney);
      // 如果反算出来的可退金额和原可退金额有差值，将差值放到可退金额最大的商品上增减
      if (totalMoney !== this.maxBackMoney) {
        // 排序找到初始商品列表中最大可退金额商品
        const sortList = JSON.parse(JSON.stringify(this.goodList)).sort((a, b) => {
          return b.orgMaxBackMoney - a.orgMaxBackMoney;
        });
        const good = this.list.find(good => {
          return good.goodFingerprint === sortList[0].goodFingerprint;
        });
        // 修改该商品的最大可退金额
        good.maxBackMoney = (+good.maxBackMoney + this.maxBackMoney - totalMoney).toFixed(2);
        good.orgMaxBackMoney = (+good.orgMaxBackMoney + this.maxBackMoney - totalMoney).toFixed(2);
        // 修改完该商品的最大可退金额反向计算出折扣单价
        good.discPrice = (good.maxBackMoney / good.remainingQty).toFixed(2);
      }
    },
    // 云同步sales表后重新抽取订单信息
    refreshOrder() {
      const { id, fingerprint } = this.backOrderInfo;
      saleService.detail(id, fingerprint, ({ saleitems }) => {
        this.init(saleitems);
      });
      this.showBackMoneyDialog = false;
      this.$emit('refresh');
    },
    // 关闭页面
    close() {
      this.$emit('close');
      setTimeout(() => {
        this.SET_SHOW({ isPartlyBack: false });
      }, 200);
    },
    /**
     * 退货数量发生变化
     * @param {number,string} value 退货数量变化值
     * @param {object} good 操作退货数量的商品
     * @param {boolean} boolean false直接修改输入框内的值，true通过加减号修改退货数量
     */
    handleChange(value, good, boolean) {
      if (+good.backNum === +good.remainingQty && boolean && value === 1) {
        return;
      }
      good.backNum = boolean ? Number((Number(good.backNum) + value).toFixed(3)) : `${value}`;
      if (+good.backNum === +good.qty) {
        // 如果一次全部退完使用最大可退金额
        good.backPrice = good.maxBackPrice = +good.maxBackMoney;
      } else {
        // 商品的退货金额和当前退货数量下的最大退货金额
        const backMoney = Number((good.discPrice * good.backNum).toFixed(2));
        // 判断计算的退货金额/最大退货金额是否大于修正过的可退金额
        good.backPrice = good.maxBackPrice = backMoney > +good.maxBackMoney ? +good.maxBackMoney : backMoney;
        if (+good.backNum === good.remainingQty && good.refundedQty < 0) {
          // 如果当前非第一次退货数量退完时，退货金额最大值修改初始可退金额-历史已退金额
          const maxBackPrice = (+good.orgMaxBackMoney + good.refundedAmt).toFixed(2);
          good.maxBackPrice = Number(maxBackPrice);
          // 如果当前退货金额小于初始可退金额-历史已退金额显示message提示
          if (+good.backPrice < +good.maxBackPrice) {
            good.message = `剩余${(good.maxBackPrice - good.backPrice).toFixed(2)}未退`;
          } else {
            good.message = '';
          }
        } else {
          good.message = '';
        }
      }
    },
    // 退货数量失焦时数量为空时变为0
    handleBlur(good) {
      good.backNum = good.backNum || 0
    },
    /**
     * 编辑商品退货金额
     * @param {object} good 要编辑的商品对象backPrice退货金额，maxBackPrice当前退货数量下最大退货金额
     * @param {number} index 要编辑退货金额商品在列表中的下标
     */
    handleEditPrice({ backPrice, maxBackPrice }, index) {
      this.editBackPrice = +backPrice;
      this.editMaxBackPrice = +maxBackPrice;
      this.editIndex = index;
      this.priceEditShow = true;
      this.$_actionLog(logList.editRefundMoney);
    },
    // 编辑退货金额确认
    editConfirm() {
      const good = this.list[this.editIndex];
      good.backPrice = Number(this.editBackPrice).toFixed(2);
      if (+good.backPrice < +good.maxBackPrice && +good.backNum === good.remainingQty) {
        good.message = `剩余${(good.maxBackPrice - good.backPrice).toFixed(2)}未退`;
      } else {
        good.message = '';
      }
      this.priceEditShow = false;
    },
    // 虚拟键盘输入
    editPriceChange(value) {
      // 焦点偏移
      let selectOffset;
      // 起始位置
      let selectionStart;
      let selectionEnd;
      const editPrice = this.$refs.editPrice.$el.querySelector('input');
      console.warn('editPrice', this.$refs.editPrice.$el.querySelector('input'));
      if (editPrice) {
        // 判断起始位置，是否全选
        selectionStart = editPrice.selectionStart;
        selectionEnd = editPrice.selectionEnd;
      }
      if (value === '删除') {
        // 有选中部分
        if (selectionStart === selectionEnd) {
          this.editBackPrice = this.editBackPrice.substring(0, selectionStart - 1) +
            '' + this.editBackPrice.substring(selectionEnd, this.editBackPrice.toString().length);
          selectOffset = -1;
        } else {
          // 无选中部分
          this.editBackPrice = this.editBackPrice.substring(0, selectionStart) +
            '' + this.editBackPrice.substring(selectionEnd, this.editBackPrice.toString().length);
          selectOffset = 0;
        }
        setTimeout(() => {
          // 删除时光标处理
          this.setCaretPosition(editPrice, selectionStart + selectOffset);
        }, 0);
      } else {
        const offset = value === '.' ? 3 : 1;
        if (selectionStart === selectionEnd) {
          this.editBackPrice = `${this.editBackPrice}${value}`
          setTimeout(() => {
            this.setCaretPosition(editPrice, selectionStart + offset);
          }, 0)
        } else {
          this.editBackPrice = this.editBackPrice.toString().substring(0, selectionStart) +
            value + this.editBackPrice.toString().substring(selectionEnd, this.editBackPrice.length);
          setTimeout(() => {
            this.setCaretPosition(editPrice, selectionStart + offset);
          }, 0)
        }
      }
    },
    // 删除光标处理
    setCaretPosition(node, pos) {
      // 设置光标位置函数
      if (node.setSelectionRange) {
        node.focus();
        node.setSelectionRange(pos, pos);
      } else if (node.createTextRange) {
        let range = node.createTextRange();
        range.collapse(true);
        range.moveEnd('character', pos);
        range.moveStart('character', pos);
        range.select();
      } else {
        console.log('');
      }
    },
    /**
     * 键盘事件监听
     * @param {object} event type1扫码2键盘，code按键事件对于key
     */
    handleKeyEvent({ type, code }) {
      if (type === 1) {
        return;
      }
      switch (code) {
        case ' ':
          this.handleNext();
          break;
        case 'Escape':
          this.close();
          break;
      }
    },
    // 下一步
    async handleNext() {
      // 检查退货数量是否为0
      const checkNum = this.list.every(good => {
        return +good.backNum === 0;
      });
      if (checkNum) {
        demo.msg('warning', '请选择退货数量');
        return;
      }
      if ([7, 8].includes(this.backOrderInfo.accountId) && +this.totalMoney === 0) {
        demo.msg('error', '退货总额不能为0');
        return;
      }
      // 扫码付单才进行云端采号
      let refundOrderId
      if ([7, 8].includes(this.backOrderInfo.accountId)) {
        const params = {
          deviceCode: demo.$store.state.show.devicecode,
          outTradeNo: this.backOrderInfo.code
        }
        try {
          const { code, data } = await getGenerateOrder(params);
          if (code === 200) {
            refundOrderId = data;
          } else {
            demo.msg('error', '服务器请求异常，请稍后重试');
            return;
          }
        } catch (error) {
          demo.msg('error', '服务器请求异常，请稍后重试');
          return;
        }
      }
      this.backMoneyParams = {
        shouldBackMoney: this.totalMoney,
        realBackMoney: this.totalMoney,
        accountId: this.backOrderInfo.accountId,
        backParams: {
          billAmt: -this.totalMoney,
          changeAmt: 0,
          deviceCode: demo.$store.state.show.devicecode,
          disc: 1,
          discAmt: -this.totalMoney,
          localOperateTime: new Date().format('yyyy-MM-dd hh:mm:ss'),
          outTradeNo: this.backOrderInfo.code,
          oweAmt: -this.totalMoney,
          payAmt: -this.totalMoney,
          refundType: 0, // 退款类型0部分1整单
          saleFingerprint: this.backOrderInfo.fingerprint,
          money: this.totalMoney,
          vipMobile: '',
          vipname: '',
          leftGoodsList: this.leftGoodsList
        }
      };
      if (refundOrderId) {
        this.backMoneyParams.backParams.refundOrderId = refundOrderId;
      }
      this.showBackMoneyDialog = true;
    }
  }
};
</script>
<style lang="less" scoped>
.partlyBackPage {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 62px 12px 12px;
  background-color: rgb(245, 248, 251);
  .backContent {
    color: @themeFontColor;
    height: 100%;
    padding: 11px;
    border-radius: 8px;
    background-color: #ffffff;
    .orderNo {
      height: 48px;
      line-height: 48px;
      font-size: 16px;
      font-weight: bold;
    }
    .listWrap {
      height: calc(100% - 104px);
      /deep/.el-table {
        color: @themeFontColor;
        font-size: 20px;
      }
      /deep/.el-table::before {
        display: none;
      }
      /deep/.tableHeader {
        height: 50px;
        font-size: 16px;
        background-color: #f5f8fb;
      }
      /deep/.el-table__row {
        height: 72px;
      }
      /deep/.el-table td {
        border-bottom: 1px solid #ebeef5;
      }
      /deep/.input:disabled {
        cursor: pointer;
        color: @themeFontColor;
        -webkit-text-fill-color: @themeFontColor;
      }
      /deep/.el-table--enable-row-hover .el-table__body tr:hover > td {
        background-color: #ffffff;
      }
      .el-icon-question {
        color: #adc1cf;
      }
      .goodName {
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .goodCode {
        font-size: 16px;
        font-weight: 500;
        color: @themeFontColorLight;
      }
      .iconWrap {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        .mask {
          width: 100%;
          height: 100%;
        }
      }
      .leftIcon,
      .rightIcon {
        position: absolute;
        width: 40px;
        height: 38px;
        top: 0;
        z-index: 2;
        text-align: center;
        line-height: 38px;
        border-radius: 4px;
        cursor: pointer;
        background-color: #f5f8fb;
        /deep/i {
          font-weight: 900;
        }
      }
      .leftIcon {
        left: 0;
      }
      .rightIcon {
        right: 0;
      }
      .iconDisabled {
        color: #e4e7ed;
        cursor: not-allowed;
      }
      /deep/.backCell {
        .cell {
          overflow: visible;
        }
      }
      .backPriceWrap {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        position: relative;
        .editWrap {
          width: 38px;
          height: 40px;
          font-size: 22px;
          text-align: center;
          line-height: 40px;
          border-radius: 4px;
          margin-left: 10px;
          cursor: pointer;
          background-color: #f5f8fb;
        }
        .priceTips {
          position: absolute;
          font-size: 14px;
          color: #ff0000;
          right: 50px;
          bottom: -10px;
        }
      }
    }
    .footerWrap {
      height: 68px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .info {
        font-size: 24px;
        span {
          color: @warningRed;
        }
      }
      .nextBtn {
        width: 172px;
        height: 48px;
        color: #ffffff;
        text-align: center;
        line-height: 48px;
        font-size: 18px;
        font-weight: 700;
        border-radius: 4px;
        margin-left: 40px;
        background-color: @warningRed;
      }
    }
  }
}
/deep/ .backMoneyDialog {
  /deep/ .el-dialog__header {
    padding: 0;
  }
  /deep/ .el-dialog__body {
    padding: 0;
  }
  /deep/ .el-dialog {
    border-radius: 10px;
  }
}
/deep/ .priceEdiDialog {
  /deep/ .el-dialog__header {
    padding: 24px 24px 16px;
  }
  /deep/ .el-dialog__body {
    padding: 0px 24px 24px;
  }
  /deep/ .el-icon-close {
    font-size: 24px;
  }
  /deep/ .inputBorder {
    border: 1px solid #cacaca;
    margin-bottom: 20px;
  }
  /deep/ .activeClass {
    border-color: @themeBackGroundColor;
  }
}
.keyboard {
  padding: 20px 0 0;
  border-top: 1px solid #e3e6eb;
  .priceUnit {
    font-size: 24px;
    color: #537286;
    font-weight: bold;
  }
  .btnWrap {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    .cencel,
    .confirm {
      width: 200px;
      height: 52px;
      font-size: 18px;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
      line-height: 52px;
      border-radius: 6px;
      cursor: pointer;
      user-select: none;
      background-color: @themeBackGroundColor;
    }
    .cencel {
      margin-right: 20px;
      color: @themeBackGroundColor;
      background-color: #ffffff;
      border: 1px solid @themeBackGroundColor;
    }
  }
}
</style>
