<style lang="less">
.el-checkbox__inner {
  width: 24px;
  height: 24px;
  border: none;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox1.png);
}
.written_off {
  color: #B2C3CD;
}
.el-checkbox__inner::after {
  border: none;
}
.el-checkbox {
  margin-bottom: 0.2rem;
  margin-top: 0.3rem;
}
.pc_emp33 .el-table th > .cell {
  padding-left: 30px!important;
}
.pc_emp33 .el-table .cell {
  padding-left: 30px!important;
}
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.el-table td,
.el-table th.is-leaf {
  border: none;
}
.el-table thead {
  color: @themeFontColor;
}
.el-checkbox__inner.is-focus {
  border-color: @themeBackGroundColor;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  height: 24px;
  top: 0;
  transform: none;
  -webkit-transform: none;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox3.png) !important;
}
.pc_emp {
  height: 100%;
}

.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
.pc_emp1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.pc_emp11 {
  width: 620px;
  height: 395px;
  background: #fff;
  border-radius: 2px;
  margin: 0 auto;
  margin-top: 115px;
}
.pc_emp12 {
  width: 100%;
  height: 71px;
  border-bottom: 1px solid #eee;
  font-size: 24px;
  line-height: 75px;
  text-align: center;
  color: @themeFontColor;
}
.pc_emp13 {
  /*margin-top: 27px;margin-left: 30px;font-weight: bold;color: @themeFontColor;font-size: 16px;line-height: 16px;*/
  margin-top: 47px;
  margin-left: 30px;
  font-weight: bold;
  color: @themeFontColor;
  font-size: 16px;
  line-height: 16px;
}
.pc_emp14 {
  margin-top: 14px;
  margin-left: 30px;
  font-size: 16px;
  line-height: 16px;
}
.pc_emp15 {
  overflow: hidden;
  margin-top: 32px;
}
.pc_emp15 input {
  margin-left: 30px;
  width: 458px;
  float: left;
  font-size: 18px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #dcdae2;
  text-indent: 18px;
  outline: none;
}
.pc_emp15 div {
  width: 90px;
  text-align: center;
  height: 40px;
  line-height: 38px;
  margin-left: 15px;
  border: 1px solid @themeBackGroundColor;
  color: @themeBackGroundColor;
  font-size: 22px;
  float: left;
  border-radius: 4px;
}
.pc_emp16 {
  overflow: hidden;
  margin-top: 48px;
  font-size: 24px;
}
.pc_emp17 {
  /*width: 138px;height: 50px;color: #666;text-align: center;line-height: 48px;border: 1px solid #DCDFE6;margin-left: 141px;float: left;border-radius: 4px;*/
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  margin-left: 110px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
}
.pc_emp18 {
  /*width: 138px;height: 50px;color: #666;text-align: center;line-height: 48px;border: 1px solid @themeBackGroundColor;margin-left: 60px;
    background: @themeBackGroundColor;color: #FFF;float: left;border-radius: 4px;cursor: pointer;*/
  width: 200px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 60px;
  background-image: @linearBackgroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.pc_emp19 {
  width: 126px;
  height: 44px;
  line-height: 44px;
  padding-left: 25px;
  background: #ebeced;
  font-size: 16px;
  color: @themeFontColor;
  border-radius: 5px;
}
.pc_emp19:hover {
  background: #eaf9fc;
  color: @themeBackGroundColor;
}
/* .el-checkbox__label{
  font-size: 16px;
} */
.pc_emp2 {
  float: left;
  margin-left: 10px;
}
.pc_emp21 {
  padding: 0 10px;
  padding-top: 0.5rem;
}
.pc_emp22 {
  position: absolute;
  right: 20px;
}
.pc_emp23 {
  width: 134px;
  height: 38px;
  background-image: @linearBackgroundColor;
  text-align: center;
  line-height: 38px;
  border-radius: 19px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  float: left;
  margin-left: 10px;
  margin-top: 12px;
  cursor: pointer;
}
.pc_emp29 {
  width: 325px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-top: 13px;
  margin-left: 20px;
  background: #fff;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_emp29 input {
  width: 230px;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 16px;
  margin-top: 7px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_emp3 {
  float: left;
  margin-left: 14px;
  margin-top: 8px;
}
.pc_emp32 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_emp33 {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: calc(100% - 120px);
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  background: #f5f8fb;
}
.pc_emp34 {
  width: 100%;
  margin-top: -40px;
  z-index: 2100;
  overflow: hidden;
  background: #fff;
}
.pc_emp35 {
  width: 560px;
  margin: 0 auto;
  margin-top: 160px;
  color: #969696;
  text-align: center;
  font-size: 22px;
}
.pc_emp35 img {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  margin-top: 0px;
}
.pc_emp36 {
  width: 93px;
  position: fixed;
  right: -9px;
  top: 150px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 152px);
  z-index: 2;
}
.pc_emp37 {
  height: 49px;
  background: linear-gradient(180deg, #69d6fe, #16aaff);
  line-height: 49px;
  color: #fff;
}
.pc_emp38 {
  height: 47px;
  line-height: 47px;
  border-radius: 5px;
  color: #fff;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
}
.pc_emp46 {
  position: fixed;
  bottom: 0;
  height: 80px;
  color: @themeFontColor;
  font-weight: bold;
  font-size: 16px;
  width: 100%;
  z-index: 1;
}
.pc_emp47 {
  left: 43px;
  position: absolute;
  top: 27px;
}
.pc_emp48 {
  position: absolute;
  right: 105px;
  top: 24px;
}
.pc_emp49 {
  height: calc(100% - 49px);
  overflow: scroll;
}
.pc_emp49 div {
  border-bottom: 1px solid #fff;
}
.pc_emp5 {
  overflow: hidden;
  position: relative;
  background: #f5f8fb;
  height: 70px;
}
.pc_emp5 .el-input__inner {
  font-size: 16px;color: @themeFontColor;
  border-radius: 22px;
  height: 44px;
}
.pc_emp51 {
  float: left;
  cursor: pointer;
  font-size: 16px;
  color: @themeFontColor;
  margin-left: 25px;
}
.pc_emp51 img {
  float: left;
  margin-top: 19px;
}
.pc_emp51 div {
  float: left;
  line-height: 62px;
  margin-left: 8px;
  font-weight: bold;
}
.pc_emp52 {
  position: relative;
  background: @themeBackGroundColor;
  width: 140px;
  height: 44px;line-height: 44px;margin: 0 auto;border-radius: 24px;color: #FFF;font-size: 16px;font-weight: bold;cursor: pointer;
  float: right;margin-right: 20px;margin-top: 13px;text-align: center;
}
.pc_emp53 {
  font-size: 16px;height: 44px;border-radius: 22;width: 130px;margin-left: 16px;margin-top: 13px;overflow: hidden;
}
#paidIn {
  color: @themeBackGroundColor;
}
.emp29 {
  border-color:  @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div class='pc_emp' v-loading.fullscreen.lock="vipDataLoading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <v-AddEmployee></v-AddEmployee>
    <div class='pc_emp5'>
      <div style='float: left;'>
        <div
          class='pc_emp29'
          :class="inputing_keyword ? 'emp29' : 'isInPutIng1'"
        >
          <img
            class='pc_emp3'
            src='../../image/pc_search.png'
          />
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder='请输入员工姓名/手机号/工号'
            v-model='keyword'
            v-focus-select="'focusSelect'"
            id='employee_keyword'
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            maxlength="30"
          />
          <img
            class='pc_emp32'
            v-show="keyword != ''"
            @click="inputFocus('employee_keyword')"
            src='../../image/pc_clear_input.png'
          />
        </div>
      </div>
      <el-select v-model="value" placeholder="请选择" class="pc_emp53">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      <div class="pc_emp52" :style="!$employeeAuth('create_employee') ? 'opacity: 40%':''" @click="!$employeeAuth('create_employee') ? '' : add_goods()">
        新增收银员
      </div>
    </div>
    <div class="pc_emp33">
      <div style="width: 100%;height: 100%;border: 1px solid #e3e6eb;border-radius: 5px;background: #FFF;">
        <el-table
          ref="multipleTable"
          empty-text=" "
          :data="tableData"
          @cell-click="choose_one_goods"
          tooltip-effect="dark"
          style="float: left;font-size: 16px;margin-top: 5px;color: #567485;width: 100%;"
          :height="tableData == '' ? '55px': table_height"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            prop="employeeName"
            label="姓名"
            align="left"
          ></el-table-column>
          <el-table-column
            label="手机号"
            align="center"
          >
            <template slot-scope="scope">{{ scope.row.employeePhone ? scope.row.employeePhone : '-'}}</template>
          </el-table-column>
          <el-table-column
            prop="employeeNumber"
            label="工号"
            align="center"
          ></el-table-column>
          <el-table-column
            label="职务"
            align="center"
          >
            <template slot-scope="scope">{{ scope.row.avatar ? scope.row.avatar : '-'}}</template>
          </el-table-column>
          <el-table-column label="状态" align="center">
            <template slot-scope="scope ">
              <span :class="[scope.row.status === 0 ? '' : 'written_off']">{{scope.row.status === 0 ? '启用' : '禁用'}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template><span id="paidIn" :style="!$employeeAuth('edit_employee') ? 'opacity: 40%;':'cursor: pointer;'">编辑</span></template>
          </el-table-column>
        </el-table>
        <div
          style="width: 100%;height: 1px;background: #FFF;position: absolute;z-index: 1;"
          :style="'margin-top:' + (table_height + 4) + 'px'"
        ></div>
        <div v-show="tableData == '' && !vipDataLoading" class="pc_emp34">
          <div class="pc_emp35">
            <img src="../../image/pc_no_goods.png" />
            <div v-show="keyword != ''">
              未查询到<span style="font-weight: bold;">“{{keyword}}”</span>相关收银员
            </div>
            <div v-show="keyword == ''">暂无收银员信息</div>
          </div>
        </div>
      </div>
    </div>
    <div class="pc_emp46">
      <!-- <div class="pc_emp47">
        共 <span style="color: #d5aa76;">{{total}}</span> 款商品
      </div> -->

      <div class="pc_emp48">
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="pagenum"
          :page-size="limit"
          :page-count="total"
        >
          <!-- slot -->
          <vCjPageSize @sizeChange="handleSizeChange" :pageSize.sync="limit" :currentPage.sync="pagenum" :pageKey.sync="pageKey"></vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import vCjPageSize from '@/common/components/CjPageSize';

export default {
  components: {
    vCjPageSize
  },
  data() {
    return {
      vipDataLoading: false, // loading
      tableData: [],
      textarea: '',
      show_import: false,
      file_name: '',
      keyword: '',
      inputing_keyword: false,
      total: 0,
      limit: 10,
      pagenum: 1,
      pageKey: 0,
      table_height: 0,
      searching: false,
      pinyin: false,
      print_click: false,
      options: [{
        value: 1,
        label: '全部'
      }, {
        value: 3,
        label: '启用'
      }, {
        value: 2,
        label: '禁用'
      }],
      value: 1,
      can_get_employeeList: false
    };
  },
  created() {
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
  },
  mounted() {
    // this.limit = parseInt((document.body.clientHeight - 230) / 43);
    this.watch_type = false;
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    this.watch_type = true;
    this.getEmployeeList();
    this.inputFocus('employee_keyword');
  },
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
  },
  watch: {
    keyword() {
      this.watch_type = false;
      this.can_get_employeeList = false;
      this.pagenum = 1;
      var that = this;
      setTimeout(function() {
        that.watch_type = true;
        that.can_get_employeeList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.right_list = [];
        that.getEmployeeList(4);
      }, that.delayedTime);
    },
    showAddEmployee() {
      if (this.showAddEmployee === 'close') {
        this.getEmployeeList();
      }
    },
    value() {
      this.pagenum = 1;
      this.getEmployeeList();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    inputFocus(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    add_goods() {
      this.SET_SHOW({ showAddEmployee: 'new' });
    },
    // 监听屏幕高度，使table表格高度能实时变化
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 192;
      }, that.delayedTime);
    },
    // 表格的checkbox，选择以后val会实时响应
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.choose_list = val;
    },
    // 获取收银员列表
    getEmployeeList() {
      if (this.pinyin) {
        return;
      }
      this.vipDataLoading = true;
      var data = {
        'keyword': this.keyword,
        'pageSize': this.limit,
        'currentPage': this.pagenum,
        'employeeStatus': this.value,
        'sysSid': this.sysSid,
        authorityVersion: this.DICT['PERMISSIONS']['AUTHORITYVERSION']
      };
      var that = this;
      demo.$http.post(this.$rest.getEmployeeList, data, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': this.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(function (res) {
          setTimeout(() => {
            that.vipDataLoading = false;
          }, that.delayedTime);
          console.log(res);
          if (res.data.code === 200) {
            if (res.data.data.list.length !== 0 || res.data.data.total === 0) {
              that.tableData = res.data.data.list;
              that.total = res.data.data.total;
            } else if (that.pagenum > 1) {
              that.pagenum--;
              that.getEmployeeList();
            }
          } else {
            // demo.msg('warning', res.data.msg);
            demo.msg('warning', '系统异常，请重新登陆！');
          }
        })
        .catch(function (rs) {
          setTimeout(() => {
            that.vipDataLoading = false;
          }, that.delayedTime);
          demo.msg('warning', '获取列表失败，请稍后尝试');
          console.error(rs);
        });
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      this.getEmployeeList(2);
    },
    handleSizeChange() {
      this.getEmployeeList(2);
    },
    // 选择的单条商品
    choose_one_goods(row) {
      if (this.$employeeAuth('edit_employee')) {
        this.SET_SHOW({
          employeeDetail: _.cloneDeep(row)
        });
        this.SET_SHOW({
          showAddEmployee: 'edit'
        });
      }
    }
  },
  // 从vuex中获取变量
  computed: mapState({
    showAddEmployee: state => state.show.showAddEmployee,
    sysSid: state => state.show.sys_sid,
    delayedTime: state => state.show.delayedTime,
    token: state => state.show.token
  })
};
</script>
