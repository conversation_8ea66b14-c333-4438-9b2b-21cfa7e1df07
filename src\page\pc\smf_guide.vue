<template>
  <div
    class="pc_aso"
    style="height:100vh;"
  >
    <div class="vxImg_container">
      <div>
        <div class="pc_aso1">
          <div>
            <div class="pc_aso4">
              <div class="pc_title_info">
                开通扫码付
              </div>
              <div class="pc_title_info themeFontColor">
                越用越省心
              </div>
            </div>
            <div class="pc_aso5">您可以扫码自助开通，或者 点击屏幕右上角“联系客服”和我们联系了解更多</div>
            <div class="pc_aso3">
              <i class="el-icon-success"></i>
              <div style="margin-left: 25px;">
                <div class="div_f">支持多种收款方式</div>
              </div>
            </div>
            <div class="pc_aso3" style="margin: 20px 0px;">
              <i class="el-icon-success"></i>
              <div style="margin-left: 25px;">
                <div class="div_f">规避逃单/假币风险</div>
              </div>
            </div>
            <div class="pc_aso3">
              <i class="el-icon-success"></i>
              <div style="margin-left: 25px;">
                <div class="div_f">微信&支付宝 D+1 到银行卡</div>
              </div>
            </div>
            <div class="pc_aso6" @click="ignoreClick">点击跳过</div>
          </div>
          <div class="pc_img_container">
            <div class="header">微信或手机浏览器扫码立即自助开通</div>
            <div class="btn">开通仅需5分钟</div>
            <div class="vx_img" ref="qrCodeUrl"></div>
            <div class="pc_aso2">
              <div>
                <img src="../../image/com_wechat.png" />
                <div>微信支付</div>
              </div>
              <div>
                <img src="../../image/com_zfb.png" />
                <div>支付宝支付</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="guide_top">
      <el-popover
        placement="bottom-start"
        width="224"
        trigger="click"
      >
        <div class="qr_container">
          <div id="container" style="font-size: 16px;margin-top:6px;text-align:center;height: 28px;">
            {{$t('components.header.customer_service')}}
          </div>
        </div>
        <div
          slot="reference"
          class="top_btn"
          style="border:none;margin-right:44px;cursor:pointer"
          @click="customerServiceClick"
        >
          <img
            src="@/image/zgzn-pos/icon_customer_service.png"
            style="width:20px;height:20px;margin-right:6px"
            alt=""
          >
          <div>联系客服</div>
        </div>
      </el-popover>
    </div>
    <!-- 退出系统dialog -->
    <div
      v-show="showTipsDialog" style="width: 100%;height: 100%;position: fixed;top: 0;left: 0;
      background: rgba(0,0,0,.5);z-index: 999999;"
    >
      <div class="tips_dialog">
        <div class="title">提示</div>
        <div class="content">确定退出系统？<br/>
          <span v-if="ultimate !== null">系统将自动交接班。</span>
        </div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            id="btnStyle"
            @click="cancelClose"
          >取消</div>
          <div
            class="btn"
            style="background:#BDA169;margin-left:30px"
            @click="confirmLogout"
          >确定</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import base_change_shifts from '@/components/base_change_shifts.vue';
import QRCode from 'qrcodejs2'

export default {
  mixins: [base_change_shifts],
  components: {},
  data () {
    return {
      qrcode: null,
      gologining: false
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    creatQrCode() {
      var qr_data = {
        text: this.$rest.qrcode + '/?key=' + window.btoa(this.sys_uid.substring(0, 3) + '****' +
          this.sys_uid.substring(7, 11) + '&' + $config.subName + '&' + $config.systemName + '&4'),
        width: 184,
        height: 184,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      };
      this.qrcode = new QRCode(this.$refs.qrCodeUrl, qr_data);
    },
    customerServiceClick() { // 联系客服记录log
      CefSharp.PostMessage("扫码付开通引导页点击联系客服");
      demo.actionLog({ page: 'smf_guide', action: 'customerService', description: '扫码付开通引导页点击联系客服' });
    },
    ignoreClick() {
      CefSharp.PostMessage("扫码付开通引导页点击跳过");
      demo.actionLog({ page: 'smf_guide', action: 'ignoreClick', description: '扫码付开通引导页点击跳过' });
      this.SET_SHOW({ autoLogin: false });
    },
    cancelClose() {
      this.SET_SHOW({ showTipsDialog: false });
      external.closeCancel();
    },
    confirmLogout () {
      if (this.gologining === true) {
        return;
      }
      if (this.loginInfo.uid === undefined) {
        external.closeMainForm();
        return;
      }
      this.gologining = true;
      setTimeout(() => {
        this.gologining = false;
      }, 3000);
      this.SET_SHOW({ showTipsDialog: false });
      setTimeout(() => {
        this.getChangeShiftsData('exit');
      }, 100);
    }
  },
  mounted() {
    this.creatQrCode();
  },
  watch: {},
  beforeDestroy() {},
  computed: {
    ...mapState({
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      showTipsDialog: state => state.show.showTipsDialog,
      loginInfo: state => state.show.loginInfo,
      ultimate: state => state.show.ultimate,
      uid: state => state.show.uid,
      token: state => state.show.token
    })
  }
};
</script>

<style lang="less" scoped>
@fontColor:@themeFontColor;
.themeFontColor {
  color: @themeBackGroundColor;
}
.pc_aso {
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  font-family: HarmonyOSSansSC, sans-serif;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
}
.guide_top{
  position: absolute;
  top: 30px;
  right: 0;
  display: flex;
  align-items: center;
}
.qr_container{
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
}
.top_btn{
  width: 100px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @themeBackGroundColor;
  font-weight: bold;
  font-size: 14px
}
.vxImg_container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pc_title_info {
  font-size: 40px;
  font-weight: normal;
  position: relative;
  z-index: 300;
}
.pc_aso1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pc_img_container {
  width: 334px;
  border-radius: 8px;
  margin-left: 100px;
  padding: 32px;
  box-shadow: 0px 4px 20px #ececec;
  .header {
    margin: 0 auto;
    width: 160px;
    color: #000;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    line-height: 22px;
  }
  .btn {
    display: flex;
    width: 126px;
    height: 28px;
    text-align: center;
    padding: 4px 8px;
    border-radius: 14px;
    border: 1px solid #F64C4C;
    color: #F64C4C;
    font-size: 16px;
    line-height: 16px;
    margin: 10px auto;
  }
}
.vx_img {
  display: flex;
  width: 184px;height: 184px;
  margin: 20px auto;
}
.pc_aso2 {
  font-size: 16px;
  width: 184px;
  text-align: center;
  color: #000;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 32px;
    height: 32px;
    margin-bottom: 20px;
  }
}
.pc_aso3 {
  height: 36px;
  display: flex;
  align-items: center;
  .div_f {
    font-size: 18px;
    color: #222;
  }
  .div_s {
    color: #8E8E8E;
    font-size: 16px;
  }
  i {
    color: @themeBackGroundColor;
    font-size: 24px;
  }
}
.pc_aso4 {
  margin-bottom: 40px;
}
.pc_aso5 {
  color: #8E8E8E;
  width: 408px;
  font-size: 16px;
  margin-bottom: 25px;
}
.pc_aso6 {
  margin-top: 40px;
  color: #CACACA;
  font-size: 16px;
  font-weight: normal;
  text-decoration-line: underline;
  cursor: pointer;
}
#btnStyle {
  background: @themeFontColor;
}
.tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  width: 450px;
  height: 264px;
  margin: 215px auto !important;
  background: #FFF;
  border-radius: 6px;
  overflow: hidden;
  .title{
    color: @fontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @fontColor;
    font-size: 24px;
    margin-top: 20px;
    text-align: center;
  }
  .dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 35px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: bold;
      font-size: 20px;
    }
  }
}
</style>
