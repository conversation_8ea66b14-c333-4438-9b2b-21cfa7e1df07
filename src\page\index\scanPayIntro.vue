<template>
  <div class="wrap">
    <img class="bgImg" ref="bgImgRef" src="@/image/home/<USER>">
    <div class="headerWrap">
      <cj-header backText="返回" background-color="transparent" @leftClick="back"></cj-header>
    </div>
    <div v-if="showKefu" class="offlineImg customService">
      <img src="@/image/home/<USER>">
      <div class="kefuImg" ref="kefuSrcUrl"></div>
    </div>
    <div class="offlineImg selfImg">
      <img src="@/image/home/<USER>">
      <div ref="qrCode" class="qrCode" id="qrCode"></div>
    </div>
    <div class="videoWrap selfVideo" :style="videoStyle">
      <video controls controlsList="nodownload" disablePictureInPicture
        poster="../../image/home/<USER>"
        :src="selfUrl" @play.once="handlePlay('self')"></video>
    </div>
    <div class="videoWrap serviceVideo" :style="videoStyle">
      <video controls controlsList="nodownload" disablePictureInPicture
        poster="../../image/home/<USER>"
        :src="serviceUrl" @play.once="handlePlay('service')"></video>
    </div>
    <confirm-dialog
      :visible.sync="showTipsDialog"
      title="提示"
      message="<div>确定退出系统？<br/>系统将自动交接班。</div>"
      @cancel="cancelClose"
      @confirm="confirmLogout"
      :closeOnClickModal="false"
    />
  </div>
</template>
<script>
import QRCode from 'qrcodejs2';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import base_change_shifts from '@/components/base_change_shifts.vue';
import { getWxQrCode } from '@/api/wxQrCode';
import logList from '@/config/logList';
import CjHeader from '@/common/components/CjHeader';
import ConfirmDialog from '@/common/components/ConfirmDialog';
export default {
  mixins: [base_change_shifts],
  components: {
    CjHeader,
    ConfirmDialog
  },
  data() {
    return {
      bgImgHeigh: 0,
      gologining: false,
      qrCode: null,
      selfUrl: '',
      kefuSrc: '',
      serviceUrl: ''
    };
  },
  computed: {
    ...mapState({
      showKefu: state => state.show.showKefu,
      loginInfo: state => state.show.loginInfo,
      showTipsDialog: state => state.show.showTipsDialog,
      sys_uid: (state) => state.show.sys_uid
    }),
    // 是否无网络
    offline() {
      return !pos.network.isConnected()
    },
    videoStyle() {
      return `top:${(this.bgImgHeigh / 2) + (this.bgImgHeigh / 1042) * 60}px`;
    }
  },
  mounted() {
    if (pos.network.isConnected()) {
      this.createQrCode();
      this.getHelpQrCode();
      const domain = $config.ServerUrl.HttpUrl;
      this.checkVideoHead('selfVideo', `${domain}/selfVideo.mp4`, true);
      this.checkVideoHead('serviceVideo', `${domain}/serviceVideo.mp4`, false);
    } else {
      this.cacheVideo('selfVideo', true);
      this.cacheVideo('serviceVideo', false);
    }
    this.resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        this.bgImgHeigh = entry.contentRect.height;
      }
    });
    this.resizeObserver.observe(this.$refs.bgImgRef);
    localStorage.setItem('scanPay', true);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 返回主页
    back() {
      this.$router.back();
    },
    // 生成自主开通二维码
    createQrCode() {
      const text = this.$rest.qrcode +
        '/?key=' +
        window.btoa(
          this.sys_uid.substring(0, 3) +
          '****' + this.sys_uid.substring(7, 11) +
          '&' + $config.subName + '&' +
          $config.systemName + '&4'
        );
      if (!this.qrCode) {
        const qr_data = {
          text: text,
          width: 140,
          height: 140,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        };
        this.qrCode = new QRCode(this.$refs.qrCode, qr_data);
      } else {
        this.qrCode.makeCode(text);
      }
    },
    // 微信客服代开通二维码
    async getHelpQrCode() {
      const param = {
        type: 'service'
      }
      const { weComUrl } = await getWxQrCode(param);
      if (weComUrl) {
        let qr_data = {
          text: weComUrl,
          width: 140,
          height: 140,
          colorDark: '#000000',
          colorLight: '#ffffff',
          correctLevel: QRCode.CorrectLevel.H
        };
        this.kefuSrc = new QRCode(this.$refs.kefuSrcUrl, qr_data);
      }
    },
    getSrcUrl(fileName, url) {
      return $config.Base.MainPageUrl + '/productImg/' + fileName + '.mp4' + '?url=' + url;
    },
    // 视频是否更新检查
    checkVideoHead(videoName, url, boolean) {
      fetch(url, { method: 'HEAD' }).then(res => {
        const last = res.headers.get('Last-Modified');
        const time = last.replaceAll(' ', '').replaceAll(',', '').replaceAll(':', '');
        this.saveVideo(`${videoName}${time}`, url, boolean, videoName)
      })
    },
    // 视频缓存检查
    cacheVideo(videoName, boolean) {
      settingService.get({ key: videoName }, (video) => {
        if (video && video.length) {
          if (boolean) {
            this.selfUrl = this.getSrcUrl(video[0].value);
          } else {
            this.serviceUrl = this.getSrcUrl(video[0].value);
          }
        }
      })
    },
    // 缓存视频资源
    saveVideo(fileName, url, boolean, videoName) {
      if (!pos.network.isConnected()) {
        return;
      }
      if (boolean) {
        this.selfUrl = this.getSrcUrl(fileName, url);
      } else {
        this.serviceUrl = this.getSrcUrl(fileName, url);
      }
      settingService.put({ key: videoName, value: fileName })
    },
    handlePlay(type) {
      this.$_actionLog(logList[type === 'self' ? 'scanPaySelfPlay' : 'scanPayServicePlay']);
    },
    cancelClose() {
      demo.$store.commit('SET_SHOW', { showTipsDialog: false });
      external.closeCancel();
    },
    confirmLogout() {
      if (this.gologining === true) {
        return;
      }
      if (this.loginInfo.uid === undefined) {
        external.closeMainForm();
        return;
      }
      this.gologining = true;
      setTimeout(() => {
        this.gologining = false;
      }, 3000);
      this.SET_SHOW({ showTipsDialog: false });
      this.back();
      setTimeout(() => {
        this.getChangeShiftsData('exit');
      }, 100);
    }
  },
  beforeDestroy() {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
};
</script>
<style lang="less" scoped>
.wrap {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: relative;
  .bgImg {
    width: 100%;
    height: auto;
  }
  .headerWrap {
    position: fixed;
    width: 100%;
    top: 0;
  }
  .offlineImg {
    width: 10%;
    height: auto;
    position: fixed;
    top: 13%;
    right: 5%;
    img {
      width: 100%;
      height: auto;
    }
  }
  .customService {
    .kefuImg {
      position: absolute;
      top: 27%;
      left: 22%;
      width: 56%;
      aspect-ratio: 1;
      /deep/ img {
        width: 100%;
      }
    }
  }
  .selfImg {
    top: 41%;
    .qrCode {
      position: absolute;
      top: 27%;
      left: 22%;
      width: 56%;
      aspect-ratio: 1;
    }
  }
  .videoWrap {
    width: 18%;
    aspect-ratio: 0.55;
    // background-color: red;
    position: absolute;
    // top: 95%;
    left: 28%;
    video {
      width: 100%;
      height: 100%;
    }
  }
  .serviceVideo {
    left: 55%;
  }
}
/deep/ .qrCode img {
  width: 100%;
}
</style>
