<style lang="less">
.pc_mem17 .el-table td,
.pc_mem17 .el-table th {
  padding: 0px;
}
.pc_mem17 .el-table th > .cell {
  padding-left: 0;
  padding-right: 0;
  height: 50px;
  line-height: 50px;
  background: #f5f7fa;
}
.pc_mem17 .el-table td {
  padding-left: 0;
  color: @themeFontColor;
}
.pc_mem17 .el-table .cell {
  padding-left: 0;
  padding-right: 0;
}
.pc_mem17 .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
.el-textarea__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.pc_mem1 {
  position: absolute;
  right: 330px;
}
.pc_mem2 {
  position: absolute;
  right: 180px;
}
.pc_mem3 {
  position: absolute;
  right: 30px;
}
.pc_mem11 {
  position: relative;
  background: @themeBackGroundColor;
  width: 110px;
  height: 44px;
  margin: 0 auto;
  margin-right: 10px;
  border-radius: 22px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
}
.pc_mem12 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-left: 10px;
  background: #fff;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_mem12 input {
  width: 230px;
  height: 28px;
  line-height: 28px;
  margin-left: 23px;
  font-size: 16px;
  margin-top: 8px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_mem13 {
  float: left;
  margin-left: 14px;
  margin-top: 8px;
}
.pc_mem14 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_mem15 {
  overflow: hidden;
  position: relative;
  background: #f5f8fb;
  display: flex;
  align-items: center;
  padding: 6px 0;
}
.pc_mem16 {
  float: left;
  cursor: pointer;
  font-size: 16px;
  color: @themeFontColor;
  margin-left: 25px;
}
.pc_mem16 img {
  float: left;
  margin-top: 19px;
}
.pc_mem16 div {
  float: left;
  line-height: 62px;
  margin-left: 8px;
  font-weight: bold;
}
.pc_mem17 {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: calc(100% - 112px);
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  background: #f5f8fb;
}
.pc_mem18 {
  width: 100px;
  height: 38px;
  border-radius: 4px;
  font-size: 16px;
  color: @themeBackGroundColor;
  line-height: 36px;
  text-align: center;
  cursor: pointer;
}
.pc_mem49 {
  right: 30px;
  position: absolute;
  bottom: 12px;
}
.pc_mem50 {
  left: 30px;
  position: absolute;
  bottom: 16px;
  font-size: 18px;
  padding: 0 10px;
  color: @themeFontColor;
  font-weight: bold;
  background: white;
  span {
    color: @themeBackGroundColor;
    margin-right: 30px;
  }
}
.pc_mem5 {
  width: 60px;
  height: 60px;
  background: #fff;
  border-radius: 50%;
  float: left;
  color: @themeBackGroundColor;
  font-size: 30px;
  font-weight: bold;
  line-height: 60px;
  text-align: center;
  margin-top: 21px;
  margin-left: 84px;
}
.pc_mem51 {
  margin-left: 45px;
  float: left;
}
.pc_mem52 {
  margin-top: 24px;
  font-size: 20px;
  font-weight: bold;
  line-height: 20px;
}
.pc_mem53 {
  line-height: 16px;
  margin-top: 19px;
}

.pc_mem54 {
  width: 100%;
  text-align: center;
  font-size: 18px;
  color: #567486;
  line-height: 18px;
  margin-top: 19px;
}
.pc_mem55 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff6159;
  text-align: center;
  color: #ffffff;
  line-height: 20px;
  font-size: 16px;
  margin-top: 10px;
  margin-right: 8px;
  cursor: pointer;
  display: inline-block;
}
.pc_mem55::after {
  border-bottom-color: rgba(0, 0, 0, 0.8) !important;
}
.pc_mem56 {
  margin-left: 24px;
  margin-top: 60px;
}
.top_center {
  flex: 1;
  display: flex;
  align-items: center;
}

.select_title {
  height: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #557485;
  line-height: 16px;
  margin-left: 60px;
}

.top_center .el-select .el-input__inner {
  border-color: #c0c4cc;
  border-radius: 50px;
  font-size: 16px;
  background: white;
  height: 44px;
}
.top_center .el-input__inner {
  padding: 0 20px;
  color: @themeFontColor;
}
.top_center .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}

.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
  font-weight: 700;
}

.bottom_content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
}
.written_off {
  color: #b2c3cd;
}
.downloadfaildata:hover {
  text-decoration: underline;
}
.el-dialog__body {
  padding: 15px 20px;
  color: @themeFontColor;
  font-size: 14px;
  word-break: break-all;
}
.el-dialog__header {
  padding-top: 20px;
  margin-right: 20px;
  padding-bottom: 10px;
  margin-left: 20px;
  border-bottom: 1px solid #e5e7eb;
}
.pc_member49 {
  padding: 24px;
  /deep/.el-input.el-input--suffix {
    font-size: 16px;
    color: #567485;
    /deep/.el-input__inner {
      font-family: 'DinMedium', sans-serif;
    }
    font-family: 'DinMedium', sans-serif;
  }
  /deep/.el-input__inner {
    font-size: 16px;
    color: #567485;
    font-family: 'DinMedium', sans-serif;
  }
}
.pc_member54 {
  background: #ffffff;
  border: 1px solid #c0c4cc;
  box-sizing: border-box;
  border-radius: 22px;
  cursor: pointer;
  float: left;
  margin-left: 12px;
  height: 44px;
  font-size: 16px;
  line-height: 44px;
  color: #567485;
  padding: 0 12px;
}
.discount_input_padding {
  /deep/.el-input__inner {
    font-family: 'DinMedium' !important;
  }
}
.com_mem58 {
  float: left;
  margin-top: 0px;
  overflow: hidden;
  cursor: pointer;
}
.com_mem59 {
  float: left;
  width: 16px;
  height: 16px;
  background: @themeBackGroundColor;
  border: 2px solid @themeBackGroundColor;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  margin-top: 12px;
}
.com_mem6 {
  width: 6px;
  height: 6px;
  background: #fff;
  margin-top: 3px;
  margin-left: 3px;
  border-radius: 50%;
  overflow: hidden;
}
.com_mem61 {
  float: left;
  margin-left: 10px;
  font-size: 16px;
  color: #567485;
  margin-top: 7px;
}
.downBut {
  float: left;
  margin-left: 190px;
  width: 268px;
  height: 44px;
  border-radius: 6px;
  border: 1px solid @themeBackGroundColor;
  cursor: pointer;
}
.downButTitle {
  margin-left: 8px;
  line-height: 42px;
  color: @themeBackGroundColor;
  float: left;
}
.batchImport {
  float: left;
  margin-left: 190px;
  width: 268px;
  height: 44px;
  border-radius: 6px;
  border: 1px solid @themeBackGroundColor;
  background: @themeBackGroundColor;
  cursor: pointer;
}
.largeInsert {
  float: right;
  margin-right: 10px;
  width: 110px;
  height: 44px;
  border: 1px solid @themeBackGroundColor;
  line-height: 42px;
  text-align: center;
  color: @themeBackGroundColor;
  border-radius: 22px;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
}
.batchUdPayType {
  background: @themeBackGroundColor;
}
.batchUdPayType1 {
  background: #fff;
  border-color: #d2d5d9;
}
#checkBatchDiscount {
  background: @themeBackGroundColor;
}
.mem12 {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
#butColor {
  color: @themeFontColor;
}
</style>
<template>
  <div style="height: 100%" v-loading.fullscreen.lock="vipDataLoading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <!-- 次卡模态框 -->
    <v-VipTimesCard></v-VipTimesCard>
    <v-MemberRecharge></v-MemberRecharge>
    <v-AddMember></v-AddMember>
    <!--弹出结算界面的计算器-->
    <v-FinalPay></v-FinalPay>
    <!-- 会员积分兑换商品 -->
    <v-MemberPE></v-MemberPE>
    <!-- 关注公众号弹框 -->
    <div v-show="showFollowErweima" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 400">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)"></div>
      <div
        style="
          font-size: 16px;
          color: #567485;
          position: relative;
          z-index: 800;
          height: 634px;
          margin: 0 auto;
          margin-top: 30px;
          background: #fff;
          width: 500px;
          overflow: hidden;
          border-radius: 12px;
        "
      >
        <div style="height: 100px; background: linear-gradient(90deg, #d2ba8a 0%, #b49a5a 100%); color: #fff">
          <div class="pc_mem5">{{ erweima_name.substring(0, 1) }}</div>
          <div class="pc_mem51">
            <div class="pc_mem52">{{ erweima_name }}</div>
            <div class="pc_mem53">{{ erweima_mobile.substring(0, 3) + '****' + erweima_mobile.substring(7, 11) }}</div>
          </div>
        </div>
        <div @click="getF5Erweima()" style="width: 335px; overflow: hidden; height: 335px; margin: 0 auto; margin-top: 33px">
          <img alt="" v-show="follow_erweima !== ''" :src="follow_erweima" style="width: 335px; height: 335px" />
          <div v-show="follow_erweima === ''" style="width: 60px; height: 60px; margin: 0 auto; margin-top: 120px">
            <img src="../../image/pc_member_f5.png" alt="" />
          </div>
          <div v-show="follow_erweima === ''" style="width: 100%; text-align: center; margin-top: 26px">点击刷新</div>
        </div>
        <div class="pc_mem54">扫描二维码关注公众号，及时掌握账户变动信息</div>
        <div
          @click="closeFollowErweima()"
          style="
            width: 250px;
            height: 60px;
            background: #ff625a;
            border-radius: 4px;
            line-height: 60px;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            text-align: center;
            margin: 0 auto;
            margin-top: 30px;
          "
        >
          关闭二维码
        </div>
      </div>
    </div>
    <!-- 批量修改 -->
    <div v-if="showBatchSetDiscount" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 400">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)"></div>
      <div
        style="
          position: relative;
          z-index: 800;
          min-height: 275px;
          margin: 0 auto;
          margin-top: 120px;
          background: #fff;
          width: 548px;
          overflow: hidden;
          border-radius: 6px;
          color: #567485;
        "
      >
        <div class="pc_member49">
          <div style="border-bottom: 1px solid #e3e6eb; height: 38px; display: flex; justify-content: space-between">
            <div style="font-weight: bold; font-size: 18px; color: #567485">批量修改折扣</div>
            <i class="el-icon-close" style="font-size: 22px; font-weight: bold; margin-top: 3px; cursor: pointer" @click="closeBatchDiscount"></i>
          </div>
          <div style="margin: 14px 40px 20px">
            <div style="display: inline-block; font-size: 18px; font-weight: bold; line-height: 42px; color: #b2c3cd; width: 100px">价格等级</div>
            <div style="display: inline-block">
              <div class="com_mem58" @click="batchUdPayType = 1, $refs.discountInput.focus()">
                <div class="com_mem59" :class="batchUdPayType === 1 ? 'batchUdPayType' : 'batchUdPayType1'">
                  <div class="com_mem6"></div>
                </div>
                <div class="com_mem61">零售价</div>
              </div>
              <div class="com_mem58" style="margin-left: 20px" @click="batchUdPayType = 2, $refs.discountInput.focus()">
                <div class="com_mem59" :class="batchUdPayType === 2 ? 'batchUdPayType' : 'batchUdPayType1'">
                  <div class="com_mem6"></div>
                </div>
                <div class="com_mem61">会员价</div>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin: 20px 40px">
            <div style="font-size: 18px; font-weight: bold; line-height: 42px; color: #b2c3cd; min-width: 102px">会员折扣</div>
            <div>
              <el-input
                style="min-width: 317px; font-family: 'Microsoft YaHei', sans-serif"
                class="discount_input_padding"
                ref="discountInput"
                v-model.trim="batchUdDiscount"
                v-focus-select="'autoFocus,focusSelect'"
                @input="batchUdDiscount = $memberDiscountLimit(batchUdDiscount)"
                placeholder="请输入折扣"
              >
                <i slot="suffix">
                  <div style="font-family: DIN Alternate, sans-serif; color: #999; font-weight: bold; line-height: 44px; margin-right: 10px">折</div>
                </i>
              </el-input>
            </div>
          </div>
          <div style="margin-top: 20px; display: flex; justify-content: center">
            <div
              id="checkBatchDiscount"
              style="
                border-radius: 6px;
                color: #fff;
                cursor: pointer;
                font-weight: bold;
                font-size: 18px;
                width: 420px;
                height: 52px;
                text-align: center;
                line-height: 50px;
              "
              @click="checkBatchDiscount()"
            >
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 批量导入弹框 -->
    <div v-show="show_large_insert" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 400">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)"></div>
      <div
        id="butColor"
        style="
          font-size: 16px;
          position: relative;
          z-index: 800;
          min-height: 300px;
          margin: 0 auto;
          margin-top: 140px;
          background: #fff;
          width: 600px;
          overflow: hidden;
          border-radius: 6px;
        "
      >
        <div style="width: calc(100% - 40px); margin-left: 20px; border-bottom: 1px solid #e3e6eb; overflow: hidden">
          <div style="text-indent: 10px; float: left; font-weight: bold; line-height: 60px">批量导入会员</div>
          <div
            @click="
              show_large_insert = false;
              keyword = '';
              pagenum = 1;
              getMemberList();
              show_large_insert = false;
            "
            style="float: right; font-size: 25px; line-height: 56px; color: #8197a6; cursor: pointer"
          >
            ×
          </div>
        </div>
        <!-- 下载模板部分 -->
        <div style="overflow: hidden; margin-top: 24px">
          <div id="butColor" style="float: left; margin-left: 30px; line-height: 44px">会员导入模板</div>
          <a href="./excels/vip_template.xlsx" download="会员批量导入模板.xlsx">
            <div class="downBut">
              <div style="width: 17px; height: 20px; float: left; margin-left: 90px; margin-top: 8px">
                <img alt="" src="../../image/zgzn-pos/pc_member_download.png" style="width: 17px; height: 20px" />
              </div>
              <div class="downButTitle">下载模板</div>
            </div>
          </a>
        </div>
        <!-- 上传文件按钮 -->
        <div style="overflow: hidden; margin-top: 30px">
          <div id="butColor" style="float: left; margin-left: 30px; line-height: 44px">导入会员文件</div>
          <div @click="batchClick()" class="batchImport" v-show="!upload_loading">
            <div style="width: 17px; height: 20px; float: left; margin-left: 90px; margin-top: 8px">
              <img alt="" src="../../image/pc_member_upload.png" style="width: 17px; height: 20px" />
            </div>
            <div style="margin-left: 8px; line-height: 42px; color: #fff; float: left">上传文件</div>
          </div>

          <!-- 隐藏上传input -->
          <input
            class="input-file"
            type="file"
            style="display: none"
            @change="batchUpload"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          />
          <!-- 上传中按钮变灰 -->
          <div
            v-show="upload_loading"
            style="float: left; margin-left: 190px; width: 268px; height: 44px; border-radius: 6px; border: 1px solid #b4b8c0; background: #b4b8c0"
          >
            <div style="width: 17px; height: 20px; float: left; margin-left: 90px; margin-top: 8px">
              <img alt="" src="../../image/pc_member_upload.png" style="width: 17px; height: 20px" />
            </div>
            <div style="margin-left: 8px; line-height: 42px; color: #fff; float: left">上传文件</div>
          </div>
          <div class="pc_mem56">
            <el-popover @show="setClass()" popper-class="pc_pay192" placement="top-start" width="411" trigger="hover" content="">
              <div style="text-align: left">
                <div style="font-family: 'Microsoft YaHei'">
                  <span>1.会员条数过多时请分批次导入，每次不超过5000条。</span>
                  <br />
                  <span>2.会员数据复制到导入模板文件时请右键>选择性粘贴>粘贴为数值,避免格式问题引发错误。</span>
                  <br />
                  <span>3.数字列及会员手机号列中请勿包含全角数字、全角小数点或空格。</span>
                  <br />
                </div>
              </div>
              <div slot="reference" style="float: left">
                <div class="pc_mem55">?</div>
                <span
                  style="font-size: 16px;#4C567C;color: #FF6159;
                  text-decoration-line:underline;cursor:pointer;"
                >
                  如何避免导入失败？
                </span>
              </div>
            </el-popover>
            <span style="margin-left: 210px; line-height: 22px; color: #b2c3cd">单次导入不超过5000条，</span>
            <span style="margin-left: 147px; line-height: 22px; color: #b2c3cd">仅支持导入.xls和.xlsx的文件格式</span>
          </div>
        </div>
        <!-- 上传中... -->
        <div v-show="upload_loading" style="width: 268px; margin: 0 auto; margin-top: 190px">
          <div style="width: 20px; height: 20px; margin: 0 auto">
            <img alt="" src="../../image/pc_member_loading.png" style="width: 20px; height: 20px" />
          </div>
          <div style="line-height: 16px; margin-top: 30px; font-weight: bold; text-align: center">正在导入，请稍后...</div>
          <div style="height: 84px"></div>
        </div>
        <!-- 上传完成 -->
        <div
          v-show="upload_complete"
          style="width: calc(100% - 40px); margin-left: 20px; border-bottom: 1px solid #e3e6eb; overflow: hidden; margin-top: 15px"
        >
          <div style="text-indent: 10px; float: left; font-weight: bold; line-height: 60px">导入结果</div>
        </div>
        <div v-show="upload_complete" style="line-height: 16px; margin-left: 30px">
          <div style="margin-top: 23px">1.共{{ upload_data.total }}条数据，成功导入{{ upload_data.correct }}条数据</div>
          <div style="margin-top: 20px">2.失败{{ upload_data.wrong }}条</div>
          <div v-show="Number(upload_data.wrong) > 0" style="margin-top: 20px">
            3.失败原因：具体请
            <span style="color: #d5aa76; cursor: pointer" class="downloadfaildata" @click="downloadfaildata">下载错误数据</span>
          </div>
          <div style="height: 59px"></div>
        </div>
      </div>
    </div>
    <div class="pc_mem15">
      <div class="pc_mem12" :class="inputing_keyword_border ? 'mem12' : 'isInPutIng1'">
        <input
          @focus="inputing_keyword_border = true"
          @blur="inputing_keyword_border = false"
          @compositionstart="inputing_keyword = true"
          @compositionend="
            inputing_keyword = false;
            trimKeyword();
          "
          type="text"
          placeholder="请输入姓名/手机号/刷卡"
          v-focus-select="'autoFocus,focusSelect'"
          v-model="keyword"
          id="member_search"
          @input="keyword = $vipNameFormat(keyword)"
          maxlength="15"
        />
        <img alt="" class="pc_mem14" v-show="keyword != ''" @click="clearKeyword()" src="../../image/pc_clear_input.png" />
      </div>
      <div class="top_center">
        <el-select v-model="birthday_value" placeholder="生日设置" style="width: 130px; margin-left: 10px" @change="refreshData" id="birthdaySetting">
          <el-option v-for="item in birthday_options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="vip_status" placeholder="会员状态" style="width: 130px; margin-left: 10px" @change="refreshData">
          <el-option v-for="item in vip_status_options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <div
          class="pc_member54"
          @click="$employeeAuth('edit_vips') ? batchSetDiscount() : ''"
          :style="$employeeAuth('edit_vips') ? '' : 'opacity: 40%'"
        >
          批量修改折扣
        </div>
        <div
          v-if="vip_status !== '1' && $employeeAuth('cancel_vips')"
          class="pc_member54"
          @click="$employeeAuth('edit_vips') ? batchHandle() : ''"
          :style="$employeeAuth('edit_vips') ? '' : 'opacity: 40%'"
        >
          批量{{ batchMap[vip_status] }}
        </div>
      </div>
      <div @click="largeInsert()" class="largeInsert" :style="$employeeAuth('create_vips') ? '' : 'opacity: 40%'">批量导入</div>
      <div style="display: flex; justify-content: center">
        <div class="pc_mem11" @click="addMember()" :style="$employeeAuth('create_vips') ? '' : 'opacity: 40%'">
          <div style="text-align: center; line-height: 42px; font-weight: bold">新增会员</div>
        </div>
      </div>
    </div>
    <div class="pc_mem17">
      <div style="width: 100%; height: 100%; background: #fff; border: 1px solid #e3e6eb; border-radius: 5px; position: relative">
        <el-table
          :data="tableData"
          :empty-text="!vipDataLoading ? '暂无数据' : ' '"
          style="width: 100%; font-size: 16px; color: #567485"
          stripe
          ref="memberTable"
          @cell-click="chooseOneMember"
          :height="table_height"
          @selection-change="handleSelectionChange"
          @sort-change="sortChange"
        >
          <el-table-column min-width="5%" type="selection" align="center"></el-table-column>
          <el-table-column width="20px"></el-table-column>
          <el-table-column show-overflow-tooltip prop="name" label="姓名" min-width="11.11%"></el-table-column>
          <el-table-column prop="mobile" label="手机号" min-width="11.11%" align="center"></el-table-column>
          <el-table-column prop="birthday" label="生日" min-width="11.11%" align="center"></el-table-column>
          <el-table-column label="折扣" min-width="11.11%" show-overflow-tooltip align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.pay_type === 1 ? '零售价' : '会员价' }}{{ scope.row.disc }}折</span>
            </template>
          </el-table-column>
          <el-table-column prop="has_money" label="储值余额" min-width="11.11%" show-overflow-tooltip align="right" sortable="custom">
            <template slot-scope="scope">{{ $toDecimalFormat(scope.row.has_money, 2, true) }}</template>
          </el-table-column>
          <el-table-column
            prop="integral"
            label="会员积分"
            min-width="11.11%"
            show-overflow-tooltip
            align="right"
            sortable="custom"
          ></el-table-column>
          <el-table-column prop="create_time" label="注册时间" min-width="11.11%" align="center" sortable="custom">
            <template slot-scope="scope">
              <div>{{ scope.row.create_time.substring(0, 10) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="会员状态" align="center" min-width="11.11%">
            <template slot-scope="scope">
              <span :class="[scope.row.is_deleted == 0 ? '' : 'written_off']">{{ scope.row.is_deleted == 0 ? '已激活' : '已注销' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="" v-if="ultimate === true" min-width="11.11%">
            <template slot-scope="scope">
              <div class="pc_mem18" @click.stop="openFollowErweima(scope.row)">关注公众号</div>
            </template>
          </el-table-column>
        </el-table>
        <div class="bottom_content">
          <div class="pc_mem49">
            <el-pagination
              :key="pageKey"
              layout="prev, pager, next, slot"
              :total="total"
              @current-change="handleCurrentChange"
              :current-page="pagenum"
              :page-size="pageSize"
              :page-count="total"
            >
              <!-- slot -->
              <vCjPageSize @sizeChange="handleSizeChange" :pageSize.sync="pageSize" :currentPage.sync="pagenum" :pageKey.sync="pageKey"></vCjPageSize>
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <confirm-dialog
      :visible.sync="visible"
      customClass="testClass"
      :message="`确定${batchMap[vip_status]}选择的会员信息？`"
      @confirm="batchConfirm"
    ></confirm-dialog>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Select } from 'element-ui';
import XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style';
import stringUtils, { StringBuilder } from '../../common/stringUtils';
import FileSaver from 'file-saver';
import logList from '@/config/logList';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import vCjPageSize from '@/common/components/CjPageSize';
import { date } from 'frontend-utils';
export default {
  components: {
    [Select.name]: Select,
    ConfirmDialog,
    vCjPageSize
  },
  data() {
    return {
      vipDataLoading: false, // loading
      upload_loading: false,
      upload_complete: false,
      upload_data: {
        total: 0,
        correct: 0,
        wrong: 0
      },
      show_large_insert: false,
      check_birth: false,
      check_det: true,
      total: 0,
      pageSize: 10,
      pagenum: 1,
      pageKey: 0,
      memberSort: {},
      inputing_keyword: '',
      inputing_keyword_border: false,
      table_height: 0,
      totalCNT: '',
      keyword: '',
      tableData: [],
      alert_recharge: '',
      remark: '',
      birthday_value: '', // 生日选择值(默认：全部)
      birthday_options: [
        {
          value: '1',
          label: '全部'
        },
        {
          value: '2',
          label: '本月'
        },
        {
          value: '3',
          label: '本日'
        }
      ],
      thisMonthBirthday: false, // 本月生日
      thisDayBirthday: false, // 本日生日
      vip_status: '2', // 会员状态选择值(默认：已激活)
      vip_status_options: [
        {
          value: '1',
          label: '全部'
        },
        {
          value: '2',
          label: '已激活'
        },
        {
          value: '3',
          label: '已注销'
        }
      ],
      field_mapping: {
        '姓名（必填）': 'name',
        '手机号（必填）': 'mobile',
        会员卡号: 'code',
        积分: 'integral',
        充值金额: 'money',
        赠送金额: 'giveMoney',
        支付密码: 'password',
        价格等级: 'payType',
        会员折扣: 'disc',
        生日: 'birthday',
        性别: 'sex',
        联系地址: 'addr',
        备注: 'remark',
        错误原因: 'errorMsg'
      },
      tmpDown: '',
      showOpenErweima: false,
      showFollowErweima: false,
      follow_erweima: '',
      erweima_mobile: '',
      erweima_name: '',
      memberSelected: [],
      showBatchSetDiscount: false,
      batchUdPayType: 1,
      batchUdDiscount: '',
      batchMap: {
        2: '注销',
        3: '激活'
      }, // 批量操作按钮文案
      visible: false // 是否显示批量注销/激活弹窗
    };
  },
  created() {
    this.SET_SHOW({ cardNo: '' });
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.getMemberList();
  },
  mounted() {
    // $('#member_search').focus();
    document.getElementById('birthdaySetting').setAttribute('value', '生日设置');
  },
  watch: {
    keyword() {
      var that = this;
      this.pagenum = 1;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.getMemberList();
      }, that.delayedTime);
    },
    cardNo() {
      if (this.cardNo === '') {
        return;
      }
      if (this.showAddMember === 'close') {
        this.keyword = this.cardNo;
        this.SET_SHOW({ cardNo: '' });
        this.pagenum = 1;
        this.getMemberList();
      }
    },
    showAddMember() {
      // 点击关注公众号，关闭明细
      if (this.showFollowErweima === true) {
        return;
      }
      if (this.showAddMember === 'close') {
        this.getMemberList();
      }
    },
    showInputRecharge() {
      if (!this.showInputRecharge && this.showAddMember === 'close') {
        this.pagenum = 1;
        this.getMemberList();
      }
    },
    memberShortCutExit() {
      if (this.memberShortCutExit) {
        this.judgeAllowExit();
        this.SET_SHOW({ memberShortCutExit: false });
      }
    },
    showFollowErweima() {
      // external.evaluateScript('demo.$store.commit("SET_SHOW",{screen2FollowErweima: "' + this.follow_erweima + '"})');
      // external.evaluateScript('demo.$store.commit("SET_SHOW",{screen2ShowFollowErweima: ' + this.showFollowErweima + '})');
      demo.screen2(
        {
          screen2FollowErweima: this.follow_erweima,
          screen2ShowFollowErweima: this.showFollowErweima,
          screen2ErweimaName: this.erweima_name,
          screen2ErweimaMobile: this.erweima_mobile,
          screen2ShowList: this.showFollowErweima ? '' : null
        },
        11
      );
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    createVip() {
      let _this = this;
      _this.$http
        .post(_this.$rest.pc_initVipDb, {})
        .then(function (res) {
          var rd = res.data;
          _this.sonarMember(rd);
        })
        .catch(() => {
          demo.msg('warning', '会员系统创建失败，请检查当前网络环境');
        });
    },
    sonarMember(rs) {
      if (rs.code !== 200) {
        demo.msg('warning', '会员系统创建失败，请检查当前网络环境');
      }
    },
    largeInsert () {
      demo.actionLog(logList.clickVipImportBatch);
      if (!this.$employeeAuth('create_vips')) {
        return;
      }
      this.show_large_insert = true;
      this.upload_loading = false;
      this.upload_complete = false;
    },
    getF5Erweima() {
      var dt = {
        name: this.erweima_name,
        mobile: this.erweima_mobile
      };
      this.openFollowErweima(dt);
    },
    setClass() {
      let setClassEls = document.getElementsByClassName('popper__arrow');
      for (let i = 0; i < setClassEls.length; i++) {
        setClassEls[i].setAttribute('class', 'popper__arrow pc_pay193');
      }
    },
    openFollowErweima(dt) {
      this.erweima_name = dt.name;
      this.erweima_mobile = dt.mobile;
      // external.evaluateScript('demo.$store.commit("SET_SHOW",{screen2ErweimaName: "' + dt.name + '"})');
      // external.evaluateScript('demo.$store.commit("SET_SHOW",{screen2ErweimaMobile: "' + dt.mobile + '"})');
      // demo.screen2({'screen2ErweimaName': dt.name, 'screen2ErweimaMobile': dt.mobile}, 12);
      this.showOpenErweima = true;
      var that = this;
      setTimeout(function () {
        that.SET_SHOW({ showAddMember: 'close' });
      }, 0);
      // 上传
      demo.$http
        .get(this.$rest.pc_createQrCodeUrl + '?phone=' + dt.mobile, {
          headers: {
            'Content-Type': 'application/json'
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(
          res => {
            that.showOpenErweima = false;
            that.showFollowErweima = true;
            console.log(res, 88);
            if (res.data.data && res.data.data.qr_code_url && res.data.data.qr_code_url.indexOf('http') !== -1) {
              that.follow_erweima = res.data.data.qr_code_url;
            } else {
              that.follow_erweima = '';
            }
          },
          function (error) {
            that.showOpenErweima = false;
            that.showFollowErweima = false;
            demo.msg('warning', '公众号二维码获取失败，请稍后再试');
            console.error(error);
          }
        );
    },
    closeFollowErweima() {
      this.follow_erweima = '';
      this.showFollowErweima = false;
    },
    /**
     * 条件筛选，刷新列表
     */
    refreshData() {
      this.pagenum = 1;
      this.getMemberList();
    },
    /**
     * 批量导入
     */
    clearKeyword() {
      this.keyword = '';
      this.getMemberList();
    },
    trimKeyword() {
      this.keyword = this.keyword.trim();
    },
    // 监听屏幕高度，使table表格高度能实时变化
    listenResize() {
      // 浏览器高度$(window).height()
      this.table_height = $(window).height() - 186;
      // this.pageSize = parseInt((this.table_height - 50) / 50);
    },
    // 添加会员
    addMember() {
      if (!this.$employeeAuth('create_vips')) {
        return;
      }
      this.SET_SHOW({
        member_detail: {
          name: '',
          mobile: '',
          password: '',
          disc: '10',
          has_money: '',
          birthday: '',
          addr: '',
          remark: '',
          pay_type: 1
        }
      });

      this.SET_SHOW({ showAddMember: 'new' });
    },
    // 点击“批量下载”
    batchClick() {
      this.upload_complete = false;
      this.upload_loading = false;
      document.querySelector('.input-file').click();
    },
    checkDupby(arry, key) {
      return _.filter(
        arry.map(function (v) {
          return v[key];
        }),
        function (value, index, iteratee) {
          return _.includes(iteratee, value, index + 1) && value;
        }
      );
    },
    batchUpload() {
      this.tmpDown = '';
      if (!event.currentTarget.files.length) {
        return;
      }
      const that = this;

      // 拿取文件对象
      var file = event.currentTarget.files[0];
      var filename = file.name;
      var suffixs = ['xls', 'xlsx'];
      if (suffixs.indexOf(filename.substr(filename.lastIndexOf('.') + 1).toLowerCase()) === -1) {
        demo.msg('warning', that.$msg.support_suffixs.format({ suffixs: suffixs.join('、') }));
        return;
      }

      // 用FileReader来读取
      var reader = new FileReader();
      // 重写FileReader上的readAsBinaryString方法
      FileReader.prototype.readAsBinaryString = function (file) {
        reader.onload = function () {
          // 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          var binary = '';
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          var wb = XLSX.read(binary, {
            type: 'binary',
            cellDates: true
          });
          var outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]);
          if (outdata.length > 5000) {
            demo.msg('warning', '批量导入会员数据大于5000条时请分批次导入,每次不超过5000条');
            return;
          }
          outdata = outdata.map(e => {
            e['手机号（必填）'] = that.$convertFullWidthToHalfWidth(e['手机号（必填）']);
            e['会员卡号'] = that.$convertFullWidthToHalfWidth(e['会员卡号']);
            e['积分'] = that.$convertFullWidthToHalfWidth(e['积分']);
            e['充值金额'] = that.prcFormat(that.$convertFullWidthToHalfWidth(e['充值金额']));
            e['赠送金额'] = that.prcFormat(that.$convertFullWidthToHalfWidth(e['赠送金额']));
            e['支付密码'] = that.$convertFullWidthToHalfWidth(e['支付密码']);
            e['价格等级'] = that.$convertFullWidthToHalfWidth(e['价格等级']);
            e['会员折扣'] = that.$convertFullWidthToHalfWidth(e['会员折扣']);
            e['生日'] = that.$convertFullWidthToHalfWidth(e['生日']);
            if (demo.isExcelDateTimeNumber(e['生日'])) { // 判断是否为excell 时间格式
              e['生日'] = new Date(demo.excelTimeToTimestamp(e['生日'])).format('yyyy-MM-dd');
            } else {
              const regex = /^(19[0-9]{2}|20[0-9]{2})[-\/]?(0?[1-9]|1[0-2])[-\/]?(0?[1-9]|[12][0-9]|3[01])$/; // eslint-disable-line
              if (regex.test(e['生日'])) {
                e['生日'] = e['生日'].toString().replace(regex, (match, year, month, day) => {
                  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                });
              }
            }
            return e;
          });
          console.log('导入会员数据：', outdata);
          var vips = stringUtils.fieldMapping(outdata, that.field_mapping, '错误原因');
          if (vips.length === 0) {
            demo.msg('warning', that.$msg.no_import_data);
            return;
          }

          const columns = _.values(that.field_mapping);
          const columnCount = columns.length;
          const content = new StringBuilder();
          content.append(columns.join()).append('\r\n');
          vips.forEach((i, i1) => {
            columns.forEach((column, i2) => {
              const columnValue = stringUtils.csvFormat(i[column]);
              const separator = i2 === columnCount - 1 ? (i1 === vips.length - 1 ? '' : '\r\n') : ',';
              content.append(columnValue).append(separator);
            });
          });
          const file = stringUtils.createFile('file', content.toString(), 'text/javascript');
          if (file.size > 5 * 1024 * 1024) {
            demo.msg('warning', that.$msg.can_not_exceed_5m);
            return;
          }
          const formData = new FormData();
          formData.append('file', file);

          that.upload_loading = true;
          // 上传
          demo.$http
            .post(that.$rest.vipBatchImport, formData, {
              headers: {
                'Content-Type': 'application/json'
              },
              maxContentLength: Infinity,
              timeout: 60000
            })
            .then(res => {
              console.log(res);
              that.upload_loading = false;
              that.upload_complete = true;
              that.upload_data = res.data.data;
              that.sonarBatchUpload(res);
            })
            .catch(err => {
              that.upload_loading = false;
              console.error(err);
            });
        };
        reader.readAsArrayBuffer(file);
      };
      reader.readAsBinaryString(file);

      // 清空input，使下次选择文件生效
      document.querySelector('.input-file').value = '';
    },
    prcFormat(num, precision = 2) {
      if (this.isNumeric(num)) {
        return Number(num) == Number(num).toFixed(precision) ? this.limitDecimalPlaces(num, precision) : num
      } else {
        return num
      }
    },
    isNumeric(str) {
      return /^\d+(\.\d+)?$/.test(str);
    },
    limitDecimalPlaces(number, precision = 2) {
      let parts = number.toString().split('.');
      if (parts.length > 1 && parts[1].length > precision) {
        return parseFloat(`${parts[0]}.${parts[1].slice(0, precision)}`);
      }
      return number;
    },
    sonarBatchUpload(res) {
      if (res.data.data.wrong > 0) {
        this.downloadMater(res.data.data.data);
      }
    },
    sonarInfo(str) {
      return str || '';
    },
    downloadMater(info) {
      let data = info.map(it => {
        return {
          name: this.sonarInfo(it.name),
          mobile: this.sonarInfo(it.mobile),
          code: this.sonarInfo(it.code),
          integral: this.sonarInfo(it.integral),
          money: this.sonarInfo(it.money),
          giveMoney: this.sonarInfo(it.giveMoney),
          password: this.sonarInfo(it.password),
          payType: this.sonarInfo(it.payType),
          disc: this.sonarInfo(it.disc),
          birthday: this.sonarInfo(it.birthday),
          sex: this.sonarInfo(it.sex),
          addr: this.sonarInfo(it.addr),
          remark: this.sonarInfo(it.remark),
          errorMsg: this.sonarInfo(it.errorMsg)
        };
      });
      console.log('data', data);
      const defaultCellStyle = {
        font: { name: '宋体' }
      };
      const wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
        defaultCellStyle: defaultCellStyle,
        showGridLines: false
      };
      const wb = { SheetNames: ['会员导入模板', '备注'], Sheets: {}, Props: {} };
      wb.Sheets['会员导入模板'] = XLSX.utils.json_to_sheet(stringUtils.fieldMapping2(data, stringUtils.reverseMapping(this.field_mapping)));
      var style1 = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: {
          top: { style: 'thin', color: '000000' },
          bottom: { style: 'thin', color: '000000' },
          left: { style: 'thin', color: '000000' },
          right: { style: 'thin', color: '000000' }
        }
      };
      wb.Sheets['会员导入模板']['A1'].s = style1;
      wb.Sheets['会员导入模板']['B1'].s = style1;

      var style2 = {
        fill: {
          fgColor: { rgb: 'B7DEE8' }
        },
        font: { name: '宋体' },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: {
          top: { style: 'thin', color: '000000' },
          bottom: { style: 'thin', color: '000000' },
          left: { style: 'thin', color: '000000' },
          right: { style: 'thin', color: '000000' }
        }
      };
      var columns = ['C1', 'D1', 'E1', 'F1', 'G1', 'H1', 'I1', 'J1', 'K1', 'L1', 'M1'];
      columns.forEach(column => {
        wb.Sheets['会员导入模板'][column].s = style2;
      });

      var style3 = {
        font: { name: '宋体' },
        border: {
          top: { style: 'thin', color: '000000' },
          bottom: { style: 'thin', color: '000000' },
          left: { style: 'thin', color: '000000' },
          right: { style: 'thin', color: '000000' }
        }
      };
      columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
      for (var i = 2; i <= data.length + 1; i++) {
        columns.forEach(column => {
          wb.Sheets['会员导入模板'][column + i].s = style3;
        });
      }
      this.addSheet(wb)
      // 创建二进制对象写入转换好的字节流
      let tmpDown = new Blob([stringUtils.s2ab(XLSXStyle.write(wb, wopts))], {
        type: 'application/octet-stream'
      });
      console.log(tmpDown);
      this.tmpDown = tmpDown;
      // 保存文件
      //  FileSaver.saveAs(tmpDown, '会员批量导入失败的数据.xlsx');
    },
    // sheet1为导出的错误数据
    // 添加sheet2为备注，把规则添加到导出错误数据的excel里，方便用户根据备注修改错误数据
    addSheet(wb) {
      const data = [
        {A: '字段', B: '默认值', C: '输入内容说明', D: ''},
        {A: '姓名', B: '', C: '最多15位', D: '必填项目'},
        {A: '手机号', B: '', C: '', D: '必填项目'},
        {A: '会员卡号', B: '', C: '最多15位数字、字母', D: '选填项目'},
        {A: '积分', B: '', C: '限整数，0～99999999；例子：150', D: '选填项目'},
        {A: '储值金额', B: '', C: '单位：元，精确到小数点后两位；例子：320.45', D: '选填项目'},
        {A: '赠送金额', B: '', C: '单位：元，精确到小数点后两位；例子：10.50', D: '选填项目'},
        {A: '支付密码', B: '', C: '6位以内纯数字', D: '选填项目'},
        {A: '价格等级', B: 1, C: '1-零售价；2-会员价', D: '选填项目'},
        {A: '会员折扣', B: '', C: '单位：折(0.1～10)，精确到小数点后一位；例子：8.5', D: '选填项目'},
        {A: '生日', B: '', C: '例子：1949/01/01 或 1949-01-01', D: '选填项目'},
        {A: '性别', B: '男', C: '男/女', D: '选填项目'},
        {A: '联系地址', B: '', C: '最多50位', D: '选填项目'},
        {A: '备注', B: '', C: '最多20位', D: '选填项目'}
      ]
      wb.Sheets['备注'] = XLSX.utils.json_to_sheet(data, {skipHeader: true, hidden: [], skipHidden: false});
      wb.Sheets['备注']['!cols'] = [{wch: 12}, {wch: 12}, {wch: 56}, {wch: 12}];
      // styleBlue、styleYellow、styleRed代表蓝色部分样式、黄色部分样式、红色部分样式
      // 这种写法好处是改变其中一个，其余的相同赋该变量值的都会进行改变
      // 这也是缺点，就是单点其中一个做修改，其余的也会影响
      // 红色变量注释了，就是因为其中一个不需要边框，去掉的时候，凡事红色式样相关的边框都消失了，产生了问题
      let styleBlue = {
        fill: {
          fgColor: { rgb: 'B6DDE8' }
        },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colBlue = ['A1', 'A2', 'A4', 'A5', 'A6', 'A7', 'A8', 'A9', 'A10', 'A11', 'A12', 'A13', 'A14', 'B1', 'C1']
      colBlue.forEach(el => {
        wb.Sheets['备注'][el].s = styleBlue;
      });
      let styleYellow = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colYellow = ['A2', 'A3']
      colYellow.forEach(el => {
        wb.Sheets['备注'][el].s = styleYellow;
      });
      // let styleRed = {
      //   font: { color: {rgb: 'FF0000'} }
      // };
      const colRed = ['D2', 'D3']
      colRed.forEach(el => {
        wb.Sheets['备注'][el].s = {
          font: { color: {rgb: 'FF0000'} }
        };
      })
      let borderAll = {
        top: { style: 'thin', color: '000000' },
        bottom: { style: 'thin', color: '000000' },
        left: { style: 'thin', color: '000000' },
        right: { style: 'thin', color: '000000' }
      };
      for (let i = 1; i < 15; i++) {
        this.addBorder('A', i, wb, borderAll)
        this.addBorder('B', i, wb, borderAll)
        this.addBorder('C', i, wb, borderAll)
        this.addBorder('D', i, wb, borderAll)
      }
    },
    addBorder(column, i, wb, borderAll) {
      if (wb.Sheets['备注'][column + i].s === undefined) {
        wb.Sheets['备注'][column + i].s = {border: borderAll}
      } else {
        wb.Sheets['备注'][column + i].s.border = borderAll
      }
    },
    downloadfaildata() {
      if (this.tmpDown != '') {
        const time = date().format('YYYYMMDDHHmmss');
        FileSaver.saveAs(this.tmpDown, `会员批量导入失败的数据${time}.xlsx`);
      }
    },
    handleSelectionChange(val) {
      this.memberSelected = val;
    },
    sortChange(e) {
      this.pagenum = 1;
      this.memberSort = e;
      this.memberSort.order = this.memberSort.order === 'ascending' ? 'asc' : 'desc';
      this.getMemberList();
    },
    batchSetDiscount() {
      // 批量修改折扣
      if (this.memberSelected.length > 0) {
        this.showBatchSetDiscount = true;
        demo.actionLog(logList.clickChangeDiscountBatch);
      } else {
        demo.msg('warning', '请勾选需要修改的会员!');
      }
    },
    // 批量注销或激活
    batchHandle() {
      if (this.memberSelected.length > 0) {
        this.visible = true;
      } else {
        demo.msg('warning', `请勾选需要${this.batchMap[this.vip_status]}的会员!`);
      }
    },
    // 批量注销或激活确认
    batchConfirm() {
      const selectList = this.memberSelected.map(item => item.id);
      demo.$http
        .post(`${this.$rest.pc_deleteOrRecoveryVipList}${this.vip_status === '2' ? 1 : 0}`, selectList, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          timeout: 60000
        })
        .then(({ data: { code, msg } }) => {
          this.visible = false;
          if (+code === 0) {
            this.refreshData();
            demo.msg('success', `会员信息${this.batchMap[this.vip_status]}成功`);
          } else {
            demo.msg('error', msg);
          }
        })
        .catch(() => {
          demo.msg('error', '内部服务器异常，请稍后重试');
        });
    },
    checkBatchDiscount() {
      // 批量修改会员折扣类型及折扣力度
      if (this.batchUdDiscount === '') {
        demo.msg('warning', '请输入会员折扣!');
        return;
      }
      if (Number(this.batchUdDiscount) === 0) {
        demo.msg('warning', '会员折扣不能为0!');
        return;
      }
      let idArr = [];
      this.memberSelected.forEach(item => {
        idArr.push(item.id);
      });
      let data = {
        idList: idArr,
        sysUid: this.sys_uid,
        payType: this.batchUdPayType,
        discount: this.batchUdDiscount
      };
      demo.$http.post(this.$rest.pc_batchUpdateDiscount, data).then(
        res => {
          console.log(res, '批量修改会员');
          if (res.data.code === '0') {
            console.log(data, '批量修改会员data');
            demo.msg('success', '批量修改会员折扣成功!');
            this.closeBatchDiscount();
            this.getMemberList();
          } else {
            demo.msg('warning', res.data.msg);
            this.closeBatchDiscount();
          }
        },
        error => {
          console.error(error);
          demo.msg('warning', '批量修改会员折扣失败!');
          this.closeBatchDiscount();
        }
      );
    },
    closeBatchDiscount() {
      this.batchUdPayType = 1;
      this.batchUdDiscount = '';
      this.showBatchSetDiscount = false;
      this.memberSelected = [];
      this.$refs.memberTable.clearSelection();
    },
    judgeAllowExit() {
      if (!this.showBatchSetDiscount && !this.show_large_insert && this.showAddMember === 'close' && !this.showFollowErweima && !this.showInputRecharge) {
        this.SET_SHOW({ isMember: false, isHome: true });
      }
    },
    // 显示会员列表
    getMemberList() {
      if (this.inputing_keyword) {
        return;
      }
      var _this = this;
      this.thisMonthBirthday = this.birthday_value === '2';
      this.thisDayBirthday = this.birthday_value === '3';
      this.vipDataLoading = true;
      var vipdata = {
        systemName: $config.systemName,
        uid: _this.loginInfo.uid,
        phone: _this.sys_uid,
        sysSid: _this.sys_sid,
        thisMonthBirthday: this.thisMonthBirthday,
        thisDayBirthday: this.thisDayBirthday,
        currentPage: _this.pagenum,
        pageSize: _this.pageSize,
        orderByFields: _this.memberSort.prop || null,
        orderByRule: _this.memberSort.order || null,
        is_deleted: _this.vip_status === '1' ? '' : _this.vip_status === '2' ? '0' : '1'
      };
      if (_this.keyword != null || _this.keyword !== '') {
        vipdata.searchStr = _this.keyword;
      }
      demo.$http
        .post(_this.$rest.pc_vipList, vipdata, {
          headers: {
            'Content-Type': 'application/json'
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(function (rs) {
          setTimeout(() => {
            _this.vipDataLoading = false;
          }, _this.delayedTime);
          console.log('==会员列表', rs.data);
          _this.tableData = rs.data.data.list;
          _this.total = rs.data.data.total;
        })
        .catch(function () {
          setTimeout(() => {
            _this.vipDataLoading = false;
          }, _this.delayedTime);
          _this.createVip();
        });
    },
    // 选择的单条会员
    chooseOneMember(row) {
      // if (this.showOpenErweima == true) {
      //   return;
      // }
      var detail = _.cloneDeep(row);
      this.SET_SHOW({ member_detail: detail });
      this.SET_SHOW({ showAddMember: 'readonly' });
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      this.getMemberList();
    },
    handleSizeChange() {
      this.getMemberList();
    }
  },
  computed: mapState({
    sys_uid: state => state.show.sys_uid,
    sys_sid: state => state.show.sys_sid,
    phone: state => state.show.phone,
    member_detail: state => state.show.member_detail,
    cardNo: state => state.show.cardNo,
    showAddMember: state => state.show.showAddMember,
    showInputRecharge: state => state.show.showInputRecharge,
    employeeAuth: state => state.show.employeeAuth,
    loginInfo: state => state.show.loginInfo,
    ultimate: state => state.show.ultimate,
    delayedTime: state => state.show.delayedTime,
    memberShortCutExit: state => state.show.memberShortCutExit,
    token: state => state.show.token,
    partitionId: state => state.show.partitionId
  })
};
</script>
