import rest from '@/config/rest';

/**
 * 添加广告
 * @param {*} params
 * @returns
 */
export function saveAdd(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.insertOne, params, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 修改广告
 * @param {*} params
 * @returns
 */
export function updateAd(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.advertUpdate, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 删除广告
 * @param {*} params
 * @returns
 */
export function delAd(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.advertDelete, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 获取广告
 * @returns
 */
export function getAds() {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.advertGet, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: demo.$store.state.show.token
        }
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 上传广告图片文件
 */
export function uploadAdFile(file) {
  return new Promise(resolve => {
    const formData = new FormData();
    formData.append('type', 3);
    formData.append('file', file);
    demo.$http
      .post(rest.adFileUpload, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: demo.$store.state.show.token
        }
      })
      .then(res => {
        resolve(rest.upload + '/' + res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
