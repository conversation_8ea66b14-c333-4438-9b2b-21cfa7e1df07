export default {
  // 放置公用状态
  namespaced: true,
  state: {
    isMobiie: false,
    isFirst: true,
    specs: {}
  },
  mutations: {
    'SET_STATUS'(state, store) {
      Object.assign(state, store);
    },
    'SET_SPECS'(state, specs) {
      state.specs = specs;
    }
  },
  actions: {
    'SET_STATUS'({ commit }, store) {
      commit(SET_STATUS, store);
    },
    getSpec({ commit }) {
      return new Promise((resolve) => {
        specsService.getSpecs().then(res => {
          commit('SET_SPECS', res);
          resolve(res);
        });
      });
    }
  }
};
