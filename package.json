{"name": "pos", "version": "1.0.0", "description": "A Vue.js project", "author": "caopeijun <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --fix --ext .js,.vue src", "build": "node build/build.js"}, "dependencies": {"axios": "^0.19.1", "bootstrap": "^4.4.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.12", "echarts": "^4.9.0", "element-china-area-data": "^5.0.2", "element-ui": "2.13.1", "fastclick": "^1.0.6", "file-saver": "^2.0.2", "frontend-utils": "^0.2.16", "hotkeys-js": "^3.10.1", "jquery": "^3.4.1", "js-md5": "^0.7.3", "jsbarcode": "^3.11.4", "lodash": "^4.17.15", "mqtt": "^3.0.0", "qrcodejs2": "^0.0.2", "vue": "2.6.11", "vue-awesome-swiper": "2.6.7", "vue-concise-slider": "3.4.4", "vue-i18n": "8.28.2", "vue-js-modal": "1.3.31", "vue-lazyload": "1.3.3", "vue-router": "3.1.3", "vue-slicksort": "1.1.3", "vue-socket.io": "3.0.10", "vuedraggable": "2.24.3", "vuex": "3.1.2", "xlsx": "^0.16.6", "xlsx-style": "^0.8.13"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-import": "^1.13.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.4.2", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.7.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "glob": "^7.2.0", "gulp": "^3.9.1", "gulp-sftp": "^0.1.5", "html-webpack-plugin": "^2.30.1", "less": "^3.10.3", "less-loader": "^4.1.0", "node-notifier": "^5.1.2", "node-sass": "^4.13.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "sass-resources-loader": "^2.2.4", "script-loader": "^0.7.2", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vant": "2.2.14", "vue-cli-plugin-style-resources-loader": "0.1.5", "vue-concise-slider": "3.4.4", "vue-loader": "13.7.3", "vue-style-loader": "3.1.2", "vue-svg-icon": "1.2.9", "vue-template-compiler": "2.6.11", "webpack": "3.6.0", "webpack-bundle-analyzer": "2.9.0", "webpack-dev-server": "2.9.1", "webpack-merge": "4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}