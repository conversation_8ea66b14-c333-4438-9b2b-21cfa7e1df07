// https://eslint.org/docs/user-guide/configuring

module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint'
  },
  env: {
    browser: true,
  },
  extends: [
    // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
    // consider switching to `plugin:vue/strongly-recommended` or `plugin:vue/recommended` for stricter rules.
    'plugin:vue/essential',
    // https://github.com/standard/standard/blob/master/docs/RULES-en.md
    'standard'
  ],
  // required to lint *.vue files
  plugins: [
    'vue'
  ],
  // add your custom rules here
  rules: {
    'generator-star-spacing': 'error',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-unused-vars': 0,
    // 'no-use-before-define': 0,
    'no-undef': 0,
    'no-tabs': 0, //缩进
    'handle-callback-err': 0,
    'no-redeclare': 0, //var一个变量的时候，检测这个变量是否已存在
    'eqeqeq': 0,
    'camelcase': 0,
    "unicode-bom": 0,
    'no-unused-expressions': 'error',
    'no-mixed-operators': 'error',
    'use-isnan': 'error',
    'brace-style': 'error',
    'no-new-object': 'error',
    // 'no-eval': 'error',
    'no-eval': 0,
    'semi': 0, //检测结尾分号
    'space-before-function-paren': 0, //function后面加空格检测
    'one-var': 'error',
    'no-sequences': 'error',
    'quotes': [0, 'single'],
    'spaced-comment': 'error',
    'no-multiple-empty-lines': 'error',
    'curly': 'error',
    'no-extend-native': 0,
    // 'no-trailing-spaces':0,
    //'padded-blocks':0,

  }
}
