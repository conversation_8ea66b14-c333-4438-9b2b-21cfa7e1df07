import rest from '@/config/rest';
// 退货退款接口
export function orderRefund(backOrder) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.pc_refund, backOrder, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 40000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 获取退款订单号接口
export function getGenerateOrder(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.pc_generate_order, params, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
