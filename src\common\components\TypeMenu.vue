<template>
  <div ref="menuWrap" class="menuWrap">
    <div v-if="showEdit" class="editType" :style="{ top: `${editTop}px` }" @click="editType">编辑分类</div>
    <el-menu
      class="el-menu-vertical-demo"
      @open="handleClick"
      @close="handleClick"
      @select="handleSelect"
      :unique-opened="true"
      background-color="#545c64"
      text-color="#fff"
      font-size="16px"
      ref="elMenu"
      :default-active="`${value}_menu`"
    >
      <el-menu-item v-if="showHot" index="-99_menu" :class="{ type_choose: type === '-99' }">热销</el-menu-item>
      <el-menu-item index="_menu" :class="{ type_choose: type === '' }">全部分类</el-menu-item>
      <el-menu-item v-if="show_weighing_category" index="0_menu" :class="{ type_choose: type === '0' }">称重分类</el-menu-item>
      <div v-for="tp in typeArr" :key="tp.id">
        <el-menu-item :index="tp.id + '_menu'" :id="tp.id + '_menu'"
          :class="{ type_choose: type == tp.id }" v-if="tp.list.length === 0">
          <span class="type_name">{{ tp.name }}</span>
        </el-menu-item>
        <el-submenu
          :id="tp.id + '_menu'"
          :index="tp.id + '_menu'"
          :class="{ type_choose: type == tp.id }"
          v-if="tp.list.length !== 0">
          <template slot="title">
            <div :class="{ parent_menu_active_div: type == tp.id }">
              <div class="type_name">{{ tp.name }}</div>
            </div>
          </template>
          <el-menu-item :style="$t('image.homeImage.menuItem')"
            v-for="item in tp.list" :key="item.id" :index="item.id + '_menu'"
            :class="{ type_choose: type == item.id }" :id="item.id + '_menu'">
            <div class="type_name">{{ item.name }}</div>
          </el-menu-item>
        </el-submenu>
      </div>
    </el-menu>
    <type-mana v-if="typeEditShow"
      ref="typeMana"
      :visible.sync="typeEditShow"
      :type-arr="typeArr"
      @update="getAllCategory"
      @close="typeEditShow = false"
      @deleteType="handleDelete"></type-mana>
  </div>
</template>
<script>
import TypeMana from './TypeMana.vue'
import { eleToView } from '@/utils/util.js';
export default {
  name: 'TypeMenu',
  components: {
    TypeMana
  },
  props: {
    value: {
      type: [Number, String],
      default: ''
    },
    // 是否显示编辑分类
    showEdit: {
      type: Boolean,
      default: true
    },
    // 是否显示热销分类
    showHot: {
      type: Boolean,
      default: false
    },
    // 编辑按钮顶部高度
    editTop: {
      type: Number,
      default: 128
    },
    // 是否为收银台的编辑分类
    isPayPage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      typeArr: [], // 分类数组
      type: '',
      show_weighing_category: true, // 是否显示称重分类
      typeEditShow: false, // 是否显示编辑分类弹窗
      first: true
    };
  },
  watch: {
    value: {
      handler(val) {
        this.type = val;
        // 只有首次加载时才会进行滚动到可视位置
        if (this.first && val !== '') {
          this.first = false;
          this.$nextTick(() => {
            this.scrollToElement(val);
          });
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.update();
  },
  methods: {
    // 刷新分类列表
    update() {
      this.getAllCategory();
      this.pdWeighingCategory();
    },
    // 获取所有分类
    getAllCategory() {
      typeService.search(res => {
        const list = demo.t2json(res);
        if (list.length > 0) {
          this.typeArr = list;
          this.$emit('update:list', list);
          if (this.typeEditShow) {
            this.$refs.typeMana.update(list)
          }
        }
      });
    },
    // 查询是否有称重商品
    pdWeighingCategory() {
      unitService.weight(res => {
        const list = demo.t2json(res);
        this.show_weighing_category = list.length > 0;
      });
    },
    // 滚动到可视范围内
    scrollToElement(val) {
      setTimeout(() => {
        eleToView(`${val}_menu`);
      }, 500);
    },
    // 点击编辑分类
    editType(e) {
      if (this.isPayPage) {
        this.typeEditShow = true;
      } else {
        this.$emit('edit', e);
      };
    },
    // 打开/折叠菜单
    handleClick(key) {
      const type = key.split('_')[0];
      this.$emit('input', type);
      this.$emit('onChange', type);
    },
    // 选择菜单
    handleSelect(key) {
      const type = key.split('_')[0];
      this.$emit('input', type);
      this.$emit('onChange', type);
    },
    // 检查删除的分类是否为当期筛选的分类
    handleDelete(type) {
      const types = type.list.find(pre => {
        return pre.id === this.value;
      })
      if (type.id === Number(this.value) || types) {
        this.$emit('input', '');
        this.$refs.menuWrap.parentNode.scrollTop = 0;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.menuWrap {
  position: relative;
  .editType {
    position: fixed;
    width: 104px;
    height: 50px;
    background-color: @inventoryColor;
    color: @areaColor;
    line-height: 50px;
    font-weight: 700;
    border-radius: 4px 4px 0 0;
  }
  .type_name{
    width: 79px;
    line-height: 1.3;
    white-space: normal;
    max-height:50px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    word-wrap:break-word;
  }
  /deep/.el-submenu {
    min-width: 110px;
  }
  /deep/.el-menu-item {
    background-color: @areaColor !important;
    min-width: 110px !important;
    text-align: left;
    border-bottom: 1px solid #fff;
    padding-left: 10px !important;
  }
  /deep/.el-menu-item.is-active {
    color: #fff !important;
  }
  /deep/.is-active {
    background-color: @areaColor;
  }
  /deep/.el-submenu__title {
    padding-left: 10px !important;
    background-color: @areaColor !important;
    border-bottom: 1px solid #fff;
    text-align: left;
    i {
      color: #fff;
      float: right;
      font-size: 18px;
      right: 5px;
      line-height: 10px;
    }
  }
  /deep/.type_choose {
    background: @linearBackgroundColor !important;
    .el-submenu__title {
      background: @linearBackgroundColor !important;
    }
  }
  /deep/.el-submenu.is-active .el-submenu__title {
    border-bottom: 1px solid #fff !important;
  }
  /deep/.el-submenu.is-opened .el-menu-item {
    background-color: @inventoryColor !important;
    text-align: left !important;
    padding-left: 20px !important;
    padding-right: 0px !important;
    border-bottom: 1px solid #fff !important;
    margin: 0 auto;
    width: 100%;
  }
}
</style>
