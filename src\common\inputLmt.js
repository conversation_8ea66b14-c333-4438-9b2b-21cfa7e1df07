/**
 *  xx
 */
export default {
  install(Vue) {
    // 纯数字,只允许输入纯数字,不限位数
    Vue.prototype.$allNumberLimit = (param) => {
      return param.replace(/[^\d]/g, '');
    };

    Vue.prototype.$countLimit = (param) => {
      // 从0开始计数,01,00格式避免,纯数字,配合maxlength限制位数
      return param.replace(/[^\d]/g, '').replace(/^[0][\d]/g, '0');
    };

    Vue.prototype.$intMaxMinLimit = (param) => {
      // 仅允许输入整数,限制最大值与最小值
      // param = {data: val, max: 最大值限制, min: 最小值限制}
      let data = param.data + '';
      if (Number(param.min) >= 0) {
        data = data.replace(/[^\d]/g, '');
      }
      data = data.replace(/[^-\d]/g, '').replace(/(?<!^)-/g, '').replace('-', '$*$').replace(/-/g, '').replace('$*$', '-')
        .replace(/^[0]{2,}/g, '0').replace(/^[-][0]{2,}/g, '-0').replace(/^[0][1-9]/g, '0').replace(/^[-][0][1-9]/g, '-0')
        .replace(/^\./g, '0.').replace(/^[-]\./g, '-0.');
      if (param.max && Number(data) > Number(param.max)) {
        data = param.max;
      }
      if (param.min && Number(data) < Number(param.min)) {
        data = param.min;
      }
      if (typeof (param.data) === 'number') {
        return isNaN(Number(data)) ? data : Number(data);
      }
      return data;
    };

    Vue.prototype.$discountRateLimit = (param) => {
      // 折扣率,两位小数,最大值100 最小值0 不含%
      let actualMax = 100;
      let actualMin = 0.01;
      param = param.replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0]{2,}/g, '0')
        .replace(/^[0][1-9]/g, '0').replace(/^\./g, '0.');
      if (param.indexOf('.') !== -1) {
        param = param.substring(0, param.indexOf('.') + 3);
      }
      if (param > actualMax) {
        param = actualMax;
      } else if (param > 0 && param < actualMin) {
        param = actualMin;
      }
      return param;
    };

    Vue.prototype.$memberDiscountLimit = (param) => {
      // 会员折扣 最大值10.0,最小值0.1,一位小数
      let actualMax = 10.0;
      let actualMin = 0.1;
      param = param.replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0]{2,}/g, '0')
        .replace(/^[0][1-9]/g, '0').replace(/^\./g, '0.');
      if (param.indexOf('.') !== -1) {
        param = param.substring(0, param.indexOf('.') + 2);
      }
      if (param > actualMax) {
        param = actualMax;
      } else if (param > 0 && param < actualMin) {
        param = actualMin;
      }
      return param;
    };

    Vue.prototype.$barCodeLimit = (param) => {
      return param.replace(/[^\w-]|_/ig, '');
    };

    Vue.prototype.$priceLimit = (param, max) => {
      // 金额默认两位小数,最小值0.00,最大值999999.99
      // let decimals = 2;
      let actualMax = max || 999999.99;
      // param = param.replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      //   .replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
      // if (param.indexOf('.') !== -1) {
      //   param = param.substring(0, param.indexOf('.') + decimals + 1); // 保留几位小数
      // }
      param = param.match(/^\d*(\.?\d{0,2})/g)[0] || '';
      if (param > actualMax) {
        param = actualMax;
      }
      return param;
    };

    Vue.prototype.$pricePurPriceLimit = (param) => {
      // 进货价默认两位小数,最小值0,最大值999999.999999
      // let decimals = 2;
      let actualMax = 999999.999999;
      // param = param.replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      //   .replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
      // if (param.indexOf('.') !== -1) {
      //   param = param.substring(0, param.indexOf('.') + decimals + 1); // 保留几位小数
      // }
      param = param.match(/^\d*(\.?\d{0,6})/g)[0] || '';
      if (param > actualMax) {
        param = actualMax;
      }
      return param;
    };

    Vue.prototype.$payPriceLimit = (param) => {
      // 支付金额默认两位小数,最小值0.00,最大值9999999.99
      let actualMax = 999999.99;
      param = param.match(/^\d*(\.?\d{0,2})/g)[0] || '';
      if (+param > actualMax) {
        param = actualMax;
      }
      if (param === '.') {
        param = '0.';
      }
      return param;
    };

    Vue.prototype.$stockLimit = (param) => {
      // 库存
      // 传参 {data: val, max: 99999.999, min:-99999.999, decimals: 0}
      //  传小数的话允许小数，最大值为3,不传的话默认没有小数
      let data = param.data;
      let decimals = param.decimals ? param.decimals : 0;
      let actualMax = param.max ? param.max : 99999;
      let actualMin = param.min ? param.min : -99999;
      if (actualMin >= 0) {
        data = data.replace(/[^.\d]/g, '');
      }
      data = data.replace(/[^-.\d]/g, '').replace(/(?<!^)-/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        .replace('-', '$*$').replace(/-/g, '').replace('$*$', '-').replace(/^[0]{2,}/g, '0').replace(/^[-][0]{2,}/g, '-0')
        .replace(/^[0][1-9]/g, '0').replace(/^[-][0][1-9]/g, '-0').replace(/^\./g, '0.').replace(/^[-]\./g, '-0.');
      if (data.indexOf('.') !== -1) {
        data = data.substring(0, data.indexOf('.') + decimals + 1); // 保留几位小数
      }
      if (data > actualMax) {
        data = actualMax;
      } else if (data < actualMin && data !== '') {
        data = actualMin;
      } else {
        // do nothing
      }
      return data;
    };
    Vue.prototype.$conversion = (val, bits = 1000) => {
      let num = val;
      if (demo.isNullOrTrimEmpty(val)) {
        num = 0;
      }
      if (!Number.isInteger(num)) {
        num = Math.round((Number(val) + Number.EPSILON) * bits) / bits;
      }
      // console.log(val, num);
      return num;
    };
    Vue.prototype.$totalLimit = param => {
      // 库存
      // 传参 {data: val, max: 99999.999, min:-99999.999, decimals: 0}
      //  传小数的话允许小数，最大值为3,不传的话默认没有小数
      let data = param.data.toString();
      let decimals = !demo.isNullOrTrimEmpty(param.decimals) ? param.decimals : 0;
      let actualMax = !demo.isNullOrTrimEmpty(param.max) ? param.max : 99999;
      let actualMin = !demo.isNullOrTrimEmpty(param.min) ? param.min : -99999;
      // 最小值>=0 或者 首位字符为数字 则过滤非数字字符
      if (actualMin >= 0 || !isNaN(Number(data.toString().charAt(0)))) {
        data = data.replace(/[^.\d]/g, '');
      }
      data = data
        .replace(/[^-.\d]/g, '')
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
        .replace('-', '$*$')
        .replace(/-/g, '')
        .replace('$*$', '-')
        .replace(/^[0]{2,}/g, '0')
        .replace(/^[-][0]{2,}/g, '-0')
        .replace(/^[0][1-9]/g, '0')
        .replace(/^[-][0][1-9]/g, '-0')
        .replace(/^\./g, '0.')
        .replace(/^[-]\./g, '-0.');
      if (data.indexOf('.') !== -1) {
        if (decimals && decimals > 0) {
          data = data.substring(0, data.indexOf('.') + decimals + 1); // 保留几位小数
        } else {
          data = data.substring(0, data.indexOf('.')); // decimals为0时去除小数及小数点
        }
      }
      if (data === '' && param.max === -0.001) {
        return '-';
      }
      // 处理特殊情况：0.、0.0、0.00
      if (data === '0.' || data === '0.0' || data === '0.00' || data === '0' ||
      data === '-0.' || data === '-0.0' || data === '-0.00' || data === '-0') {
        return data;
      }
      if (param.decimals === 0 && param.max === -0.001) {
        if (data === '') {
          return data;
        } else if (data > 0) {
          return -data;
        }
      }
      if (data > actualMax) {
        data = actualMax;
      } else if (data < actualMin && data !== '') {
        data = actualMin;
      } else {
        // do nothing
      }
      return data;
    };
    // 图片裂图处理方法
    Vue.prototype.$imgError = (param) => {
      console.log(param);
      param.currentTarget.src = require('../image/pc_no_cloth_img.png');
      return param;
    };
  }
}
