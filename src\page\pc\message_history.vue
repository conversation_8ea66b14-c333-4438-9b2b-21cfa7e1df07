<style lang='less' scoped>
.shortInfo {
  color: #CFA26B;
  cursor: pointer;
}
.re_deposit_record_container {
  /deep/ .el-select__caret{
    margin-top: 0px;
  }
  /deep/ .el-table .cell {
    padding-left: 25px;
  }
  background: #F5F8FB;
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table td, .el-table th {
    padding: 10px 0;
  }
  /deep/ .el-range-editor.el-input__inner {
    border-radius: 50px;
    height: 44px;
  }
  /deep/ .el-date-editor .el-range__icon {
    display: none;
    height: 44px;
  }
  /deep/ .el-range-editor .el-range-input {
    margin-left: 12px;
    color: @themeFontColor;
  }
  /deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-date-editor .el-range-separator {
    color: @themeFontColor;
    height: 85%;
  }
  /deep/ .el-date-table td.today span {
    color: @themeBackGroundColor !important;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table__header-wrapper {
    height: 50px;
    margin-top: -5px;
    > .el-table__header > .has-gutter > tr > th {
      height: 50px;
      > div {
        height: 100%;
      }
    }
  }
  /deep/ .tab1 .el-table__header-wrapper > .el-table__header > .has-gutter > tr > th {
    padding: 15px 0px 1px 0px;
    > div > .caret-wrapper{
      margin-top: -6px;
      > .sort-caret .ascending {
        top: 7px;
      }
      > .sort-caret .descending {
        bottom: 5px;
      }
    }
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-input__inner {
    border-radius: 22px;
    color: @themeFontColor;
    font-size: 16px;
    height: 44px;
    padding-left: 20px;;
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  .re_top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 20px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
        height: 44px;
      }
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .com_pae16 {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: @themeBackGroundColor;
      text-align: center;
      color: #FFFFFF;
      line-height: 20px;
      font-size: 16px;
      float: right;
      margin-top: 2px;
      margin-left: 12px;
      cursor: pointer;
    }
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding: 0 10px;
      color: @themeFontColor;
      background: white;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 22px;
  background: #FFFFFF;
  margin-left: 10px;
}
.pc_report2 > input {
  width: 235px;
  height: 18px;
  line-height: 18px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 12px;
  border: none;
  color: #B1C3CD;
  background: #FFFFFF;
  float: left;
}
.pc_rep15 {
  float: right;margin-right: 10px;
  width: 110px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 22px;
  font-weight: 700;
  font-size: 18px;
  color: #FFF;line-height: 44px;
  text-align: center;
  cursor: pointer;
}
.el-table .ascending .sort-caret.ascending{
  border-bottom-color: @themeBackGroundColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color: @themeBackGroundColor;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}
.el-date-table td.end-date span {
  background-color: @themeBackGroundColor;
}
.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}
.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}
/deep/ .el-input__icon.el-icon-date {
  display: none;
}
/deep/ .el-input__icon {
  margin-top: 3px;
}
/deep/ .date_picker_container{
  width: 300px;
  height: 46px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 22px;
  display: flex;
  align-items: center;
  float: left;
  margin-left: 10px;
  > .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date .el-input__inner {
    border: none;
    background: #FFFFFF;
    color: #B2C3CD;
    font-size: 16px;
    padding-left: 0px;
    padding-right: 0px;
    text-align: center;
  }
}
/deep/.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 20px;
  .header_title{
    color: @themeFontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 40px;
    color: #8298A6;
    cursor: pointer;
  }
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
.detail {
  margin-top: 16px;
  padding: 0 20px 20px 20px;
  .detail_item {
    color: @themeFontColor;
    font-size: 16px;
    display: inline-block;
    width: 48%;
    margin-bottom: 16px;
  }
}
.focusDate {
  border-color: @themeBackGroundColor;
}
.focusDate1 {
  border-color: #E3E6EB;
}
</style>
<template>
  <div>
    <div class="re_deposit_record_container">
      <div>
        <div class="re_top">
          <div class="re_top_left_container">
            <div v-show="tabIndexShort === 1" style='float: left;'>
              <div
                class='pc_report2'
                :class="inputing_keyword ? 'focusDate' : 'focusDate1'"
              >
                <input
                  @focus='inputing_keyword = true'
                  @blur='inputing_keyword = false'
                  :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
                  type='text'
                  placeholder='请输入接收手机号'
                  v-model='keyword'
                  id='message_search'
                  maxlength="11"
                />
                <img
                  class='pc_report1'
                  alt=""
                  v-show="keyword != ''"
                  @click="focusInput('message_search')"
                  src='../../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div v-show="tabIndexShort === 1" @click="focusDate = true" class="date_picker_container"
            :class="focusDate ? 'focusDate' : 'focusDate1'">
              <el-date-picker
                id="dateFrom"
                v-model="dateMsg[0]"
                type="date"
                placeholder="开始日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #B2C3CD">至</div>
              <el-date-picker
                id="dateTo"
                v-model="dateMsg[1]"
                type="date"
                placeholder="结束日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate = false"
              >
              </el-date-picker>
            </div>
            <div v-show="tabIndexShort === 2" @click="focusDate2 = true" class="date_picker_container"
            :class="focusDate2 ? 'focusDate' : 'focusDate1'">
              <el-date-picker
                id="dateFrom"
                v-model="dateMsg2[0]"
                type="date"
                placeholder="开始日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate2 = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485">至</div>
              <el-date-picker
                id="dateTo"
                v-model="dateMsg2[1]"
                type="date"
                placeholder="结束日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate2 = false"
              >
              </el-date-picker>
            </div>
            <div v-show="tabIndexShort === 3" @click="focusDate2 = true" class="date_picker_container"
            :class="focusDate2 ? 'focusDate' : 'focusDate1'">
              <el-date-picker
                id="dateFrom"
                v-model="dateMsg3[0]"
                type="date"
                placeholder="开始日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate2 = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485">至</div>
              <el-date-picker
                id="dateTo"
                v-model="dateMsg3[1]"
                type="date"
                placeholder="结束日期"
                value-format='yyyy-MM-dd'
                @change="formatDate"
                @blur="focusDate2 = false"
              >
              </el-date-picker>
            </div>
            <div v-show="tabIndexShort === 1" class="re_top_left" style="float: left;">
              <el-select
                v-model="sendStatus"
                placeholder="短信状态"
                style="width:130px;font-size: 16px;height: 44px;color: #567458;"
                id="messageStatus"
              >
                <el-option
                  v-for="item in status_list"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="pc_rep15" @click="exportExcel()" style="margin-right: -10px;" v-if="tabIndexShort !== 3">导出表格</div>
        </div>
        <div class="re_table_container tab1">
          <el-table
            v-if="tabIndexShort === 1"
            ref="multipleTable"
            :height="table_height"
            stripe
            :data="msg_data"
            style="font-size: 16px;color: #567485;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange" >
            <el-table-column
              prop="phoneNum"
              min-width="15%"
              show-overflow-tooltip
              align="left"
              label="发送手机号">
            </el-table-column>
            <el-table-column
              prop="content"
              min-width="28%"
              show-overflow-tooltip
              align="center"
              label="内容">
            </el-table-column>
            <el-table-column
              min-width="15%"
              align="left"
              show-overflow-tooltip
              label="发送时间">
              <template slot-scope="scope">
                {{ scope.row.sendDate }}
              </template>
            </el-table-column>
            <el-table-column
              min-width="15%"
              align="right"
              show-overflow-tooltip
              >
              <template slot="header">
                发送条数
                <el-popover
                  popper-class="pc_fip5"
                  placement="right"
                  width="224"
                  trigger="click"
                  content="每条限制60字，超过另计费">
                  <div class="com_pae16" slot="reference">?</div>
                </el-popover>
              </template>
              <template slot-scope="scope">
                <div style="padding-right: 30px;">
                  {{scope.row.sendCount}}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="sendStatusTip"
              min-width="27%"
              align="center "
              show-overflow-tooltip
              label="短信状态">
            </el-table-column>
          </el-table>
          <el-table
            v-if="tabIndexShort === 2"
            ref="multipleTable"
            :height="table_height"
            stripe
            :data="msg_data"
            style="font-size: 16px;color: #567485;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange" >
            <el-table-column
              prop="createAt"
              min-width="20%"
              show-overflow-tooltip
              align="left"
              label="购买时间 ">
            </el-table-column>
            <el-table-column
              prop="total"
              min-width="15%"
              show-overflow-tooltip
              align="right"
              label="购买条数">
            </el-table-column>
            <el-table-column
              min-width="25%"
              align="right"
              show-overflow-tooltip
              label="金额">
              <template slot-scope="scope">
                {{ Number(scope.row.price).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column
              min-width="15%"
              align="left"
              >
            </el-table-column>
            <el-table-column
              min-width="25%"
              prop="payType"
              align="left"
              show-overflow-tooltip
              label="支付方式">
            </el-table-column>
          </el-table>
          <el-table
            v-if="tabIndexShort === 3"
            ref="multipleTable"
            :height="table_height"
            stripe
            :data="msg_data"
            style="font-size: 16px;color: #567485;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange" >
            <el-table-column
              prop="createAt"
              min-width="15%"
              show-overflow-tooltip
              align="left"
              label="群发时间 ">
            </el-table-column>
            <el-table-column
              prop="sendCount"
              min-width="15%"
              show-overflow-tooltip
              align="left"
              label="发送手机号数量">
            </el-table-column>
            <el-table-column
              min-width="35%"
              align="left"
              show-overflow-tooltip
              label="群发内容">
              <template slot-scope="scope">
                {{scope.row.content}}
              </template>
            </el-table-column>
            <el-table-column
              min-width="15%"
              align="center"
              show-overflow-tooltip
              label="操作">
              <template slot-scope="scope">
                <div v-if="tabIndexShort === 3" class="shortInfo" @click="openDetail(scope.row)">明细</div>
              </template>
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              共<span> {{total || 0}} </span>条记录
            </div>
            <div>
              <el-pagination
                layout="prev, pager, next"
                :total="total"
                @current-change="msgChange"
                :current-page="currentPage"
                :page-size="pageSize"
                :page-count="total"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="showDetail"
      width="622px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">发送明细</div>
        <div
          class="icon_close"
          @click="showDetail = false"
        >×</div>
      </div>
      <div class="detail">
        <div class="detail_item">
          提交时间：{{detail.createAt}}
        </div>
        <div class="detail_item">
          计费条数：{{detail.sendCount}}
        </div>
        <div class="detail_item">
          号码数量：{{detail.smsSum}}
        </div>
        <div class="detail_item">
          失败条数：{{detail.failCount}}
        </div>
        <div class="detail_item">
          成功条数：{{detail.successCount}}
        </div>
        <div class="detail_item">
          当前状态：{{detail.errorMsg}}
        </div>
        <div class="detail_item" style="width: 100%">
          群发内容：
        </div>
        <div class="detail_item" style="width: 100%;background: #F9FBFB;border-radius: 8px;line-height: 44px;">
          {{detail.smsCont}}
        </div>
        <div class="detail_item" style="width: 100%">
          发送对象：
        </div>
        <div class="detail_item" style="width: 100%;background: #F9FBFB;border-radius: 8px;padding: 8px 16px 8px 16px;max-height: 130px;">
          {{detail.phone.replace(/[,]/ig, '，')}}
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import logList from '@/config/logList';
export default {
  data() {
    return {
      detail: {
        createAt: '',
        successCount: 0,
        smsSum: 0,
        failCount: 0,
        errorMsg: '',
        smsCont: '',
        phone: ''
      },
      showDetail: false,
      inputing_keyword: false,
      msgType: '',
      sendStatus: '',
      category_list: [],
      status_list: [
        {
          'code': '0',
          'name': '全部状态'
        },
        {
          'code': '2',
          'name': '发送失败'
        },
        {
          'code': '3',
          'name': '发送成功'
        }
      ],
      keyword: '',
      pageSize: parseInt(($(window).height() - 279) / 45),
      currentPage: 1,
      total: 0,
      goodssort: {},
      table_height: $(window).height() - 279,
      msg_data: [],
      multipleSelection: [],
      dateMsg: '',
      dateMsg2: ['', ''],
      dateMsg3: ['', ''],
      focusDate: false,
      focusDate2: false,
      isExport: false
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading
  },
  methods: {
    ...mapActions([SET_SHOW]),
    openDetail(data) {
      this.showDetail = true;
      let param = {
        msgId: data.msgId,
        reqId: data.reqId
      };
      demo.$http.post(this.$rest.smsListDetails, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(rs => {
        const obj = rs.data;
        if (obj.code === 200 && obj.data !== null) {
          this.detail = obj.data;
        }
      });
    },
    setClass () {
      let setClassEls = document.getElementsByClassName('popper__arrow');
      for (let i = 0; i < setClassEls.length; i++) {
        setClassEls[i].setAttribute('class', 'popper__arrow pc_pay193');
      }
    },
    formatDate() {
      if (this.tabIndexShort === 1) {
        if ((this.dateMsg[0] !== '' && this.dateMsg[0] !== null) &&
          (this.dateMsg[1] !== '' && this.dateMsg[1] !== null) &&
          (new Date(this.dateMsg[0]).getTime() > new Date(this.dateMsg[1]).getTime())) {
          [this.dateMsg[0], this.dateMsg[1]] = [this.dateMsg[1], this.dateMsg[0]]; // 俩元素互换位置
        }
      } else if (this.tabIndexShort === 2) {
        if ((this.dateMsg2[0] !== '' && this.dateMsg2[0] !== null) &&
          (this.dateMsg2[1] !== '' && this.dateMsg2[1] !== null) &&
          (new Date(this.dateMsg2[0]).getTime() > new Date(this.dateMsg2[1]).getTime())) {
          [this.dateMsg2[0], this.dateMsg2[1]] = [this.dateMsg2[1], this.dateMsg2[0]]; // 俩元素互换位置
        }
      } else {
        if ((this.dateMsg3[0] !== '' && this.dateMsg3[0] !== null) &&
          (this.dateMsg3[1] !== '' && this.dateMsg3[1] !== null) &&
          (new Date(this.dateMsg3[0]).getTime() > new Date(this.dateMsg3[1]).getTime())) {
          [this.dateMsg3[0], this.dateMsg3[1]] = [this.dateMsg3[1], this.dateMsg3[0]]; // 俩元素互换位置
        }
      }
      this.searchMsgMain();
    },
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    msgChange(val) {
      this.currentPage = val;
      this.searchShortMsg();
    },
    exportAddExcel(exportList) {
      if (this.tabIndexShort === 1) {
        demo.actionLog(logList.clickSmsSendRecord);
      }
      if (this.tabIndexShort === 2) {
        demo.actionLog(logList.clickSmsBuyRecord);
      }
      this.isExport = false;
      let field_mapping = {
        发送手机号: 'phoneNum',
        内容: 'content',
        发送时间: 'sendDate',
        发送条数: 'sendCount',
        短信状态: 'sendStatusTip'
      };
      if (this.tabIndexShort === 2) {
        field_mapping = {
          购买时间: 'createAt',
          购买条数: 'total',
          金额: 'price',
          支付方式: 'payType'
        };
      }
      if (exportList.length === 0) {
        demo.msg('warning', '暂无记录');
      } else {
        this.$makeExcel(exportList, field_mapping, (this.tabIndexShort === 1 ? '短信发送记录' : '短信购买记录') + new Date().format('yyyyMMddhhmmss'));
      }
    },
    exportExcel() {
      this.isExport = true;
      this.searchMsgMain();
    },
    /**
     * 查询
     */
    searchMsgMain() {
      this.currentPage = 1;
      this.searchShortMsg();
    },
    /**
     * 列表数据
     */
    searchShortMsg() {
      let url = this.getUrl();
      let arr = this.getShortMsgFromTo();
      let from = arr[0];
      let to = arr[1];
      let param = {
        'paidServiceCode': '3',
        'receivePhone': this.keyword,
        'from': from !== null && from.length !== 0 ? from.substring(0, 10) + ' 00:00:00' : '',
        'to': to !== null && to.length !== 0 ? to.substring(0, 10) + ' 23:59:59' : '',
        'msgType': this.msgType === '' ? 0 : this.msgType,
        'sendStatus': this.sendStatus === '' ? 0 : this.sendStatus,
        'pageSize': this.pageSize,
        'currentPage': this.currentPage,
        'phone': this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName
      };
      if (this.tabIndexShort === 3) {
        param = {
          startDate: from !== null && from.length !== 0 ? from.substring(0, 10) + ' 00:00:00' : '',
          endDate: to !== null && to.length !== 0 ? to.substring(0, 10) + ' 23:59:59' : '',
          sysUid: this.sysUid,
          pageSize: this.pageSize,
          currentPage: this.currentPage,
          systemName: $config.systemName,
          subName: $config.subName
        }
      }
      demo.$http.post(url, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(rs => {
        const obj = rs.data;
        this.dataFormat(obj);
      });
    },
    getShortMsgFromTo() {
      let arr = [];
      let from = '';
      let to = '';
      if (this.tabIndexShort === 1) {
        from = this.dateMsg.length === 0 ? null : this.dateMsg[0];
        to = this.dateMsg.length === 0 ? null : this.dateMsg[1];
      } else if (this.tabIndexShort === 2) {
        console.log(this.dateMsg2.length, 'this.dateMsg2.length');
        from = this.dateMsg2.length === 0 ? null : this.dateMsg2[0];
        to = this.dateMsg2.length === 0 ? null : this.dateMsg2[1];
      } else {
        from = this.dateMsg3.length === 0 ? null : this.dateMsg3[0];
        to = this.dateMsg3.length === 0 ? null : this.dateMsg3[1];
      }
      arr.push(from, to);
      return arr;
    },
    getUrl() {
      let url = this.$rest.selectSendRecords;
      if (this.isExport && this.tabIndexShort === 1) {
        url = this.$rest.exportSendRecords;
      } else if (this.isExport && this.tabIndexShort === 2) {
        url = this.$rest.exportItemPurchase;
      } else if (!this.isExport && this.tabIndexShort === 2) {
        url = this.$rest.selectItemPurchase;
      } else if (!this.isExport && this.tabIndexShort === 3) {
        url = this.$rest.smsList;
      } else {
        // todo
      }
      return url;
    },
    dataFormat(obj) {
      if (obj.data !== null) {
        if (this.isExport) {
          let exportList = obj.data;
          this.exportAddExcel(exportList);
        } else {
          this.msg_data = obj.data.list;
          this.total = obj.data.total;
        }
      } else {
        this.msg_data = [];
        if (this.isExport) {
          let exportList = [];
          this.exportAddExcel(exportList);
        }
      }
    },
    // 获取短信类别
    selectSmsType () {
      let that = this;
      const param = {
        'paidServiceCode': '3'
      };
      demo.$http.post(this.$rest.selectSmsType, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      }).then(rs => {
        const obj = rs.data;
        if (obj.code === 200) {
          that.category_list.push({
            'code': '0',
            'name': '全部类型'
          });
          for (let i = 0; i < obj.data.length; i++) {
            that.category_list.push(obj.data[i]);
          }
        } else {
          demo.msg('warning', obj.msg);
          that.category_list = [];
        }
      });
    },
    createDate() {
      let today = new Date();
      let date_to = today.format('yyyy-MM-dd hh:mm:ss');
      let date_from = new Date(today.setDate(today.getDate())).format('yyyy-MM-dd 00:00:00');
      this.dateMsg = [date_from, date_to];
      this.dateMsg3 = [date_from, date_to];
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    tabIndexShort: state => state.show.tabIndexShort,
    delayedTime: state => state.show.delayedTime
  }),
  created() {
    this.createDate();
    this.selectSmsType();
    this.searchMsgMain();
  },
  mounted() {
    setTimeout(() => {
      $('#message_search').focus();
    });
    document.getElementById('messageStatus').setAttribute('value', '短信状态');
  },
  watch: {
    tabIndexShort() {
      if (this.tabIndexShort === 1) {
        setTimeout(() => {
          $('#message_search').focus();
        });
      }
      this.isExport = false;
      this.createDate();
      this.dateMsg2 = ['', ''];
      this.searchMsgMain();
    },
    keyword() {
      let that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.searchMsgMain();
      }, that.delayedTime);
    },
    msgType() {
      this.searchMsgMain();
    },
    sendStatus() {
      this.searchMsgMain();
    }
  }
};
</script>
