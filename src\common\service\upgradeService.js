import rest from '../../config/rest';
import { StringBuilder } from '../stringUtils';
import settingService from './settingService';

const upgradeService = {
  func: function() {
    return [this.updateRefundFingerprint];
  },

  execute: function(from) {
    return new Promise(async (resolve, reject) => {
      const fs = this.func();
      const cnt = fs.length;
      if (isNaN(from)) {
        if (isNaN($setting.upgradeFrom)) {
          await settingService.reloadSetting();
        }
        from = isNaN($setting.upgradeFrom) ? 0 : $setting.upgradeFrom;
      }
      if (from >= cnt) {
        if (+from !== +$setting.upgradeFrom) {
          settingService.put({ key: 'upgradeFrom', value: from });
          $setting.upgradeFrom = from;
        }
        resolve();
        return;
      }
      const f = fs[from];
      f()
        .then(res => {
          from++;
          const msg = `[升级方法-{0}] 执行成功`.format(f.name);
          console.log(msg, res);
          CefSharp.PostMessage(msg + ' ' + res);
          return this.execute(from);
        })
        .catch(err => {
          const msg = `[升级方法-{0}] 执行失败`.format(f.name);
          console.error(msg, err);
          CefSharp.PostMessage(msg + ' ' + err);
          if (+from !== +$setting.upgradeFrom) {
            settingService.put({ key: 'upgradeFrom', value: from });
            $setting.upgradeFrom = from;
          }
          reject(err);
        });
    });
  },

  /**
   * 更新sales.refund_fingerprint
   */
  updateRefundFingerprint: function() {
    return new Promise((resolve, reject) => {
      demo.$http
        .get(rest.getRefundFingerprint)
        .then(res => {
          const data = res.data;
          if (res.status !== 200 || data.status !== 200 || data.code !== 200) {
            reject(data.msg);
            return;
          }

          const list = data.data;
          if (list.length === 0) {
            resolve();
            return;
          }
          const builder = new StringBuilder();
          for (const item of list) {
            builder.append(sqlApi.updateRefundFingerprint.format(item.refundFingerprint, item.syncG));
          }
          return dao.asyncTransaction(builder.toString());
        })
        .then(resolve)
        .catch(reject);
    });
  }
};

window.upgradeService = upgradeService;
export default upgradeService;
