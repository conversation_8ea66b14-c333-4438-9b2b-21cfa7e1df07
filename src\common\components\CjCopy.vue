<template>
  <div class="content">
    <div>
      <slot v-if="$slots.navCopy" name="navCopy"></slot>
      <div v-else class="content-image__copy" :class="alignmentClass">
        <el-popover
          v-if="isOmit"
          :disabled="isNullOrTrimEmpty(name).toString().length < 5"
          popper-class="pc_pay192 popper_self"
          style="overflow: hidden"
          placement="top"
          trigger="hover"
          :content="name">
          <div slot="reference" class="list-right__content pc_pay199">{{ name || '-' }}</div>
        </el-popover>
        <div v-else class="list-right__content">{{ name || '-' }}</div>
        <img v-if="isNullOrTrimEmpty(name)" @click.stop="copyClick(name)" class="copyIcon" width="18" height="18" src="../../image/copy.png" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CjCopy',
  data() {
    return {
      doRegisterClick: false // 防连点
    }
  },
  props: {
    // 内容
    name: {
      type: String,
      default: ''
    },
    // 对齐方式
    center: {
      type: String,
      default: ''
    },
    // 是否需要气泡
    isOmit: {
      type: Boolean,
      default: true
    },
    // 节流
    throttleTime: {
      type: Number,
      default: 3000
    }
  },
  computed: {
    // 计算对齐方式
    alignmentClass() {
      return {
        'content-image__left': this.center === 'left',
        'content-image__center': this.center === 'center',
        'content-image__end': this.center === 'right'
      };
    }
  },
  methods: {
    copyClick(name) {
      // 防连点
      if (this.doRegisterClick) {
        return;
      }
      this.doRegisterClick = true;
      setTimeout(() => {
        this.doRegisterClick = false;
      }, this.throttleTime);
      const _input = document.createElement('input'); // 直接构建input
      _input.value = name; // 设置内容
      document.body.appendChild(_input); // 添加临时实例
      _input.select(); // 选择实例内容
      document.execCommand('Copy'); // 执行复制
      document.body.removeChild(_input); // 删除临时实例
      demo.msg('success', '复制成功，使用Ctrl+V粘贴');
    },
    // 转化传入参数
    isNullOrTrimEmpty(obj) {
      if (obj === null || obj === undefined || obj === '' || obj === '-') {
        return '';
      }
      return obj;
    }
  }
}
</script>
<style lang="less" scoped>
  .content {
    overflow: hidden;
    width: 100%;
  }
  .content-image__end {
    justify-content: flex-end;
  }
  .content-image__left {
    justify-content: flex-start;
  }
  .content-image__center {
    justify-content: center;
  }
  .list-right__content {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .content-image__copy {
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    .copyIcon {
      margin-left: 5px;
      cursor: pointer;
    }
  }
</style>
