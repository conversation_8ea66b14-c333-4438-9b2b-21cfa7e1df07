<template>
  <!-- 会员增长统计 -->
  <div class='vip_consume_collection_container'>
    <div class='vip_consume_collection_container_content'>
      <div class="top">
        <div class="top_left">
          <div
            class="date_picker_container"
            @click="focusDate = true"
            :class="focusDate ? 'focusDate' : 'focusDate1'"
          >
            <el-date-picker
              v-model="fromDate"
              type="date"
              placeholder="开始日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
            >
            </el-date-picker>
            <div style="font-size: 16px;color: #567485">至</div>
            <el-date-picker
              v-model="toDate"
              type="date"
              placeholder="结束日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
            >
            </el-date-picker>
          </div>
          <div style="display:flex;">
            <div v-for="(item,index) in statisticsStyle"
              :key="index"
              @click="statisticsStyleIndex = index">
              <div class="btn_item"
                    :style="statisticsStyleIndex === index ? 'border-color: #d5aa76;' : ''">
                  <div
                    class="btn_item_active"
                    :style="statisticsStyleIndex === index ? '' : 'background: #FFF'"
                  ></div>
              </div>
              <div style="float: left;margin-left: 10px;font-size: 16px;color: #567485;">{{item + '统计'}}</div>
            </div>
          </div>
        </div>
        <div class="top_right">
          <div class="btn_export_excel" @click="getEcharts()">查询</div>
        </div>
      </div>
      <div style="height: calc(100vh - 126px);background: #FFF;
                  width: 100%;
                  border: 1px solid #E3E6EB;">
        <div v-show="show_echarts"
          class="center"
          style="margin-top:20px">
          <div
            ref="chart"
            style="width: 1000px;height:500px;margin: 0 auto;margin-top: 20px;"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import echarts from 'echarts';
import logList from '@/config/logList';
export default {
  data () {
    return {
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      statisticsStyle: ['按月', '按周', '按日'], // 统计方式
      statisticsStyleIndex: 1,
      show_echarts: false
    };
  },
  mounted () {
    let today = new Date();
    this.fromDate = new Date(today.setDate(today.getDate() - 7)).format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    this.getEcharts();
  },
  methods: {
    getEcharts() {
      demo.actionLog(logList.pc_vip_increase);
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        // nothing to do
      }
      var sub_data = {
        'sysSid': this.sysSid,
        'statisticalMethod': this.statisticsStyle[this.statisticsStyleIndex],
        'fromDate': this.fromDate,
        'toDate': this.toDate,
        'phone': this.phone,
        'uid': this.sysUid,
        'systemName': $config.systemName
      };
      var that = this;
      demo.$http.post(this.$rest.pc_getVipGrowthList, sub_data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          if (res.data.code === '0') {
            if (res.data.data.length === 0) {
              that.show_echarts = false;
              demo.msg('warning', '暂无符合条件数据，请重新选择条件');
              return;
            }
            that.show_echarts = true;
            that.makeEcharts(res.data.data.dateGroup, res.data.data.numGroup);
          } else {
            demo.msg('warning', res.data.msg);
            that.show_echarts = false;
          }
        });
    },
    makeEcharts(x, y) {
      var chart = echarts.init(this.$refs.chart);
      var options = {
        xAxis: [
          {
            type: 'category',
            data: x,
            axisLabel: {
              fontSize: 16,
              color: '#567485',
              margin: 15
            },
            axisLine: {
              lineStyle: {
                color: '#E3E6EB'
              }
            },
            axisTick: {
              show: false
            }
          },
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              lineStyle: {
                color: '#E3E6EB'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              fontSize: 16,
              formatter: '{value}个',
              color: '#567485',
              margin: 15
            },
            axisLine: {
              lineStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#E3E6EB'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '新增会员数',
            type: 'bar',
            data: y, // 会员个数
            itemStyle: {
              normal: {
                color: '#D5AA76'
              }
            }
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',

            label: {
              // formatter: '新增会员数：{value}',
              backgroundColor: '#6a7985'
            }
          }
        }
      };
      chart.setOption(options);
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    phone: state => state.show.phone
  })
};
</script>

<style lang='less' scoped>
.focusDate {
  border-color: @themeBackGroundColor;
}
.focusDate1 {
  border-color: #E3E6EB;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  padding-right: 0;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  display: flex;
  align-items: center;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .el-input--suffix .el-input__inner {
  color: #C0C4CC;
  background: #FFFFFF;
}
.vip_consume_collection_container{
  background: #F5F8FB;
  font-size: 16px;
  color: @themeFontColor;
  .vip_consume_collection_container_content{
    overflow: auto;
    background: white;
    height: calc(100vh - 70px);
    margin: 0px 10px 10px;
    border-radius: 4px;
    .top{
      display: flex;
      justify-content: flex-start;
      align-items: center;
      height: 64px;
      background: #F5F8FB;
      .top_left{
        display: flex;
        align-items: center;
        .top_left_title{
          color: @themeFontColor;
          font-size: 16px;
        }
        .btn_item {
          float: left;
          width: 24px;
          height: 24px;
          background: #fff;
          border: 2px solid #D2D5D9;
          border-radius: 50%;
          overflow: hidden;
          position: relative;
          cursor: pointer;
          margin-left: 25px;
        }
        .btn_item_active {
          width: 14px;
          height: 14px;
          background: @themeBackGroundColor;
          margin-top: 3px;
          margin-left: 3px;
          border-radius: 50%;
          overflow: hidden;
        }
      }
      .top_right{
        display: flex;
        align-items: center;
        margin-left: 25px;
        .btn_export_excel{
          width: 100px;
          height: 44px;
          line-height: 44px;
          background: @themeBackGroundColor;
          color: white;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
          cursor: pointer;
          border-radius: 22px;
        }
      }
    }
    .center{
      .center_top{
        display: flex;
        align-items: center;
        padding: 20px 30px;
      }
    }
    .bottom{
      .bottom_top{
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 90px;
        border-top: 1px dashed #E3E6EB;
        margin: 0 20px;
        margin-top: 30px;
        .btn_export_excel{
          width: 130px;
          height: 44px;
          line-height: 44px;
          background: @themeBackGroundColor;
          color: white;
          text-align: center;
          font-size: 16px;
          font-weight: bold;
          border-radius: 50px;
          cursor: pointer;
          margin-right: 10px;
        }
      }
      .collection_statistics{
        height: 102px;
        margin: 0 10px;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
      }
    }
  }
}
</style>
