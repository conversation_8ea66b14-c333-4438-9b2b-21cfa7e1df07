<style lang="less" scoped>
.buy_sof {
  position: absolute;width: 100%;height: 100%;background: rgba(0,0,0,.5);top: 0;left: 0;z-index: 9999;
}
.buy_sof1 {
  width: 450px;height: 342px;background: #FFF;border-radius: 6px;margin: 0 auto;margin-top: 172px;overflow: hidden;
}
.buy_sof2 {
  line-height: 24px;margin-top: 40px;font-weight: bold;color: @themeFontColor;text-align: center;width: 100%;font-size: 24px;
}
#buySof2 {
  color: @themeFontColor;
}
#toTM {
  color: @themeBackGroundColor;
}
#close {
  background: @themeFontColor;
}
</style>
<template>
  <div class="buy_sof">
    <div class="buy_sof1">
      <div class="buy_sof2">升级版本</div>
      <div id="buySof2" style="width: 390px;height: 60px;background: #F5F8FB;border-radius: 6px;overflow: hidden;
      margin-top: 40px;margin-left: 30px;line-height: 18px;font-size: 18px;font-weight: bold;">
        <div style="float: left;margin-left: 20px;margin-top: 22px;">通过客服电话购买：</div>
        <div style="float: right;margin-right: 20px;margin-top: 22px;">{{$t('components.header.telephone')}}</div>
      </div>
      <div id="buySof2" style="width: 390px;height: 60px;background: #F5F8FB;border-radius: 6px;overflow: hidden;
      margin-left: 30px;line-height: 18px;font-size: 18px;font-weight: bold;margin-top: 10px;">
        <div style="float: left;margin-left: 20px;margin-top: 22px;">天猫旗舰店购买：</div>
        <div id="toTM" style="float: right;margin-right: 20px;margin-top: 22px;" @click="toTM()">前往购买
          <span style="font-family: 'Microsoft YaHei',sans-serif;">&nbsp;></span></div>
      </div>
      <div id="close" @click="close()" style="width: 140px;height: 50px;margin-top: 40px;margin-left: 155px;
      color: #FFF;font-weight: bold;font-size: 20px;line-height: 50px;text-align: center;border-radius: 4px;cursor:pointer;">
        取消
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  methods: {
    ...mapActions([SET_SHOW]),
    close() {
      this.SET_SHOW({isBuySoftware: false});
    },
    toTM() {
      window.open(
        'https://zhangguizhinang.tmall.com/search.htm?spm=a220o.1000855.w5002-22406951241.1.1fc63196KdOA5a&search=y'
      );
    }
  }
};
</script>
