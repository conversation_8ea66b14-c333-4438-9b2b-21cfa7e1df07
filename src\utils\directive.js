import Vue from 'vue';
// 输入框自动获取焦点、获取焦点自动全选功能，如果当前dom为非input自动找该dom下的子dom中的第一个input
// 使用方法<input v-focus-select="'autoFocus,focusSelect'" />
export const focusSelect = Vue.directive('focus-select', {
  bind: (el, bind) => {
    const element = findInput(el);
    if (element) {
      if (bind.value.includes('autoFocus')) {
        setTimeout(() => {
          element.focus();
        }, 0)
      }
      if (bind.value.includes('focusSelect')) {
        // 输入框增加获取焦点监听
        element.onfocus = () => {
          element.select()
        }
      }
    }
  }
});

function findInput(el) {
  let element;
  // 判断当前dom是否为input/textarea
  const tagName = el.tagName.toLowerCase();
  if (tagName === 'input' || tagName === 'textarea') {
    element = el;
  } else {
    // 查找子dom中的input/textarea
    const inputs = el.querySelectorAll('input');
    const textareas = el.querySelectorAll('textarea');
    element = inputs[0] || textareas[0];
  }
  return element;
}

export default focusSelect;
