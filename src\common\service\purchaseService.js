import _ from 'lodash';
import logList from "../../config/logList";
import dao from '../dao/dao';
import stringUtils, { StringBuilder } from '../stringUtils';

const purchaseService = {
  /**
   * 进货
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  insert: async function (params, onSuccess, onFail) {
    let itemsLength = params.purchaseItems.length;
    if (itemsLength === 0) {
      onFail('进货明细不能为空');
      return;
    }
    params.uid = demo.$store.state.show.loginInfo.uid;
    params.billAmt = 0;
    params.disc = +params.disc || 1;
    params.inOut = +params.inOut || 1;
    params.remark = params.remark || null;
    if (demo.isNullOrTrimEmpty(params.code)) {
      let code = await orderService.syncGet({ type: 'JHD' });
      params.code = code[0].code;
    }
    dao.transaction(this.insertAndReturnBackCommon(params), onSuccess, onFail);
  },
  /**
   * 退货
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  returnBack: async function (params, onSuccess, onFail) {
    let itemsLength = params.purchaseItems.length;
    if (itemsLength === 0) {
      onFail('退货明细不能为空');
      return;
    }
    params.uid = demo.$store.state.show.loginInfo.uid;
    params.billAmt = 0;
    params.disc = +params.disc || 1;
    params.inOut = +params.inOut || 2;
    params.remark = params.remark || null;
    var itemsGroupBy = _.groupBy(params.purchaseItems, 'supplierFingerprint');
    let codes = await orderService.syncGet({ type: 'JHD', limit: Object.keys(itemsGroupBy).length });
    var sql = '';
    var index = 0;
    _.forEach(itemsGroupBy, (item, supplierFingerprint) => {
      params.code = codes[index].code;
      params.supplierFingerprint = supplierFingerprint;
      params.purchaseItems = item;
      index++;
      sql += this.insertAndReturnBackCommon(params);
    });
    dao.transaction(sql, onSuccess, onFail);
  },
  insertAndReturnBackCommon: function (params) {
    var listLog = [];
    listLog[0] = params.code;
    let obj = _.cloneDeep(logList.purs);
    obj.description = obj.description.format(listLog);
    demo.actionLog(obj);
    let batchSize = 50;
    let goodSql = '';
    let purchaseItemsInsert = sqlApi.purchaseItemsInsert;
    params.billAmt = 0;
    params.fingerprint = commonService.guid();
    _.forEach(params.purchaseItems, (item, index) => {
      item.purFingerprint = params.fingerprint;
      item.fingerprint = commonService.guid();
      item.supplierFingerprint = params.supplierFingerprint;
      if (params.inOut !== 1) {
        item.qty = -Math.abs(item.qty);
      }
      item.disc = +item.disc || 1;
      item.amt = Number(item.price * item.qty).toFixed(2);
      params.billAmt += +item.amt;
      item.curStock = item.qty;
      item.purPrice = item.price;
      item.manufactureDate = demo.isNullOrTrimEmpty(item.manufactureDate) ? null : item.manufactureDate;
      let itemParams = demo.sqlConversion(item);
      purchaseItemsInsert += sqlApi.purchaseItemsInsertValues.format(itemParams);
      let index1 = index + 1;
      if (index1 === params.purchaseItems.length) {
        purchaseItemsInsert += ';';
      } else if (index1 % batchSize === 0) {
        purchaseItemsInsert += ';' + sqlApi.purchaseItemsInsert;
      } else {
        purchaseItemsInsert += ',';
      }
      if (params.isAverage) {
        itemParams.purPrice = itemParams.averagePrice;
      }
      goodSql += sqlApi.purchaseUpdateGoods.format(itemParams);
    });
    params.discAmt = Number(params.inOut) === 1 ? params.finalAmt : (Number(params.billAmt * params.disc).toFixed(2));
    params.payAmt = params.discAmt;
    params.oweAmt = params.discAmt;
    let accountParams = {};
    accountParams.id = params.accountId;
    accountParams.curAmt = -params.discAmt;
    let accountSql = sqlApi.accountsUpdate.format(accountParams);
    return sqlApi.purchasesInsert + sqlApi.purchasesInsertValues.format(demo.sqlConversion(params)) + ';' + purchaseItemsInsert + accountSql + goodSql;
  },
  /**
   * 添加或更新前判断
   * @data {supplier_id:1,account_id:5,purchase_code:''}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  isExists: function (data, onSuccess, onFail) {
    var result = {
      status: 0,
      msg: 'success'
    };
    var msgList = {
      '0': result,
      '1': {
        status: 1,
        msg: '供应商不存在。'
      },
      '2': {
        status: 1,
        msg: '付款方式不存在。'
      },
      '3': {
        status: 1,
        msg: '单号已存在。'
      }
    };
    dao.exec(sqlApi.purchaseExists.format(data),
      function (res) {
        var resultList = demo.t2json(res);
        var val = resultList[0].val;
        result = msgList[val];
        onSuccess(result);
      },
      onFail
    );
  },
  /**
   * 添加或更新
   * @data {*}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (data, onSuccess, onFail) {
    var listLog = [];
    listLog[0] = params.code;
    let obj = _.cloneDeep(logList.purs);
    obj.description = obj.description.format(listLog);
    demo.actionLog(obj);
    CefSharp.PostMessage('purchaseService.put--args: ' + JSON.stringify(data));
    this.isExists(data,
      function (res) {
        if (res.status === 0) {
          dao.exec(getSql(sqlApi.purchaseUpdateGoods, sqlApi.purchasePut, sqlApi.getPurchaseId),
            function (res1) {
              onSuccess(res1);
            },
            onFail
          );
        } else {
          onSuccess(res.msg);
        }
      }, onFail);
    // 拼接sql
    function getSql(sqlProductsUpdate, sqlPut, sqlGetPurchaseId) {
      var puritems = data.puritems;
      var products_update = '';
      var params = {
        uid: demo.$store.state.show.loginInfo.uid,
        code: data.code,
        in_out: data.in_out,
        bill_amt: data.bill_amt,
        disc_amt: data.disc_amt,
        disc: data.disc,
        pay_amt: data.pay_amt,
        owe_amt: data.owe_amt,
        remark: data.remark.replace(/'/g, '‘').replace(/;/g, '；'),
        supplier_id: data.supplier_id,
        account_id: data.account_id,
        fingerprint: md5(data.code),
        values: ''
      };
      /**
       * 1、puritems insert
       * 2、products update
       */
      for (var i = 0; i < puritems.length; i++) {
        var item = {
          code: data.code,
          purFingerprint: params.fingerprint,
          good_id: puritems[i].good_id,
          in_out: puritems[i].in_out,
          purPrice: puritems[i].purPrice,
          disc: puritems[i].disc,
          price: puritems[i].price,
          qty: puritems[i].qty,
          amt: puritems[i].amt,
          remark: puritems[i].remark.replace(/'/g, '‘').replace(/;/g, '；'),
          fingerprint: md5(data.code + puritems[i].good_id)
        };
        params.values = params.values + (params.values !== '' ? ',' : '') + sqlGetPurchaseId.format(item);
        var updateGood = {
          supplier_id: data.supplier_id,
          pur_price: item.price,
          cur_stock: item.qty,
          good_id: item.good_id
        };
        products_update += sqlProductsUpdate.format(updateGood);
      }
      return sqlPut.format(params) + products_update;
    }
  },
  getSearchWheresTotal: function (data) {
    var wheresTotal = '';
    if (!demo.isNullOrTrimEmpty(data.from)) {
      wheresTotal += "and purchases.opt_date>='" + data.from + "' ";
    }
    if (!demo.isNullOrTrimEmpty(data.to)) {
      wheresTotal += "and purchases.opt_date<='" + data.to + "' ";
    }
    if (data.hasOwnProperty('inOut') && +data.inOut !== 0) {
      wheresTotal += 'and purchases.in_out=' + data.inOut + ' ';
    }
    if (data.hasOwnProperty('accountId') && +data.accountId !== 0) {
      wheresTotal += 'and purchases.account_id=' + data.accountId + ' ';
    }
    if (data.hasOwnProperty('supplierFingerprint') && data.supplierFingerprint) {
      wheresTotal += 'and purchases.supplier_fingerprint="' + data.supplierFingerprint + '"';
    }
    if (!demo.isNullOrTrimEmpty(data.keyword)) {
      data.keyword = data.keyword.replace(/'/g, '‘').replaceAll('/', '//').replaceAll("_", "/_").replaceAll('%', '/%');
      wheresTotal += `and (goods.name like '%${data.keyword}%' ESCAPE '/'
      or goods.pinyin like '%${data.keyword}%' ESCAPE '/'
      or goods.code like '%${data.keyword}%' ESCAPE '/'
      or goods_ext_barcode.ext_barcode like '%${data.keyword}%' ESCAPE '/'
      or purchases.remark like '%${data.keyword}%' ESCAPE '/'
      or goods.first_letters like '%${data.keyword}%' ESCAPE '/') `;
    }
    return wheresTotal;
  },
  /**
   * 进货单导出
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  exportExcel: function (data, onSuccess, onFail) {
    const wheresDetails = this.getSearchWheresTotal(data);
    const wheresDesc = ' order by opt_date desc, create_time desc, id desc, oid, items_id asc';
    dao.exec(
      sqlApi.purchase_export_data.format(wheresDetails) + wheresDesc,
      onSuccess,
      onFail
    );
  },
  /*
      0  from: "2019-12-01",    根据日期精确检索    必須
      1  to: "2019-12-31",    根据日期精确检索    必須
      2  strcmp: "面",    根据单据名称模糊检索
      3  company_id: 5,    根据供应商精确检索
      4  page_size: 20,    分页    必須
      5  page_num: 1,    分页    必須
      6  acct_id: 4,    收款账户为“微信”（见表accts）
                              1  现金
                              2  银行存款
                              3  POS收银账户
                              4  微信
                              5  支付宝
      7  tye: 1    限制单据类型为“进货”（1:进货 2:退货）
    */
  // 进货单检索 purchaseService.search('2019-11-01','2019-12-31','米',1,5,1,1,1,function(res){console.log(res);})
  search: function (data, onSuccess, onFail) {
    var wheresTotal = this.getSearchWheresTotal(data);
    const wheresDayTotal = wheresTotal + ' group by purchases.opt_date order by purchases.opt_date desc ';
    const wheresDayDetails = wheresTotal + ' order by purchases.create_at desc, purchases.id desc';
    var purchaseGetTotal, purchaseGetDayTotal, purchaseGetDetails;
    if (demo.isNullOrTrimEmpty(data.keyword)) {
      purchaseGetTotal = sqlApi.purchaseGetTotal;
      purchaseGetDayTotal = sqlApi.purchaseGetDayTotal;
      purchaseGetDetails = sqlApi.purchaseGetDetails;
    } else {
      purchaseGetTotal = sqlApi.purchaseGetTotalWithGoods;
      purchaseGetDayTotal = sqlApi.purchaseGetDayTotalWithGoods;
      purchaseGetDetails = sqlApi.purchaseGetDetailsWithGoods;
    }
    dao.exec(purchaseGetTotal.format(wheresTotal), res => {
      var totalRes = res[0];
      dao.exec(purchaseGetDayTotal.format(wheresDayTotal), dayTotalRes => {
        dao.exec(purchaseGetDetails.format(wheresDayDetails), res2 => {
          var dayDetailsRes = _.groupBy(res2, 'optDate');
          for (var i = 0; i < dayTotalRes.length; i++) {
            var dayTotal = dayTotalRes[i];
            dayTotal.details = dayDetailsRes[dayTotal.optDate];
          }
          totalRes.days = dayTotalRes;
          onSuccess(totalRes);
        }, onFail);
      }, onFail);
    }, onFail);
  },
  /**
   * 进货单明细 purchaseService.detail(1,function(res){console.log(res);})
   * @param {*} id
   * @param {*} onSuccess
   * @param {*} onFail
   */
  detail: function (
    id,
    onSuccess,
    onFail
  ) {
    var data = {
      id: id
    };
    dao.exec(
      sqlApi.purchaseSearchDetail.format(data),
      function (res) {
        var json_res = {};
        json_res.items = specsService.specStringToArray(res);
        if (json_res.items.length > 0) {
          json_res.bill = json_res.items[0];
        }
        onSuccess(json_res);
      },
      onFail);
  },
  /**
   * 进货单批量删除
   * purchaseService.deleteBatch({'id':'1,2,3'}, res=>{console.log(res)}, res=>{console.log(res)})
   * 批量启用: purchaseService.deleteBatch({'id':'1,2,3', 'isDel':0}, onSuccess, onFail)
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  deleteBatch: function (params, onSuccess, onFail) {
    if (params.isDel !== 0) {
      params.isDel = 1;
    }
    dao.transaction(sqlApi.purchasesDeleteBatch.format(params), onSuccess, onFail);
  },

  /**
   * 进货批量导入
   * http://yapi.trechina.cn/project/1152/interface/api/58108
   */
  batchImport: function (params) {
    return new Promise((resolve, reject) => {
      this.batchImportCheck(params)
        .then(existGoods => {
          return this.batchImportGoods(params, existGoods);
        })
        .then(resolve)
        .catch(reject);
    });
  },
  batchImportCheck: function (params) {
    return new Promise((resolve, reject) => {
      let codes;
      this.batchImportPreCheck(params)
        .then(() => {
          return this.batchImportLoopCheck(params);
        })
        .then(res => {
          codes = res.codes;
          return this.batchImportNotWeightQtyCheck(params, res.qtyCodes);
        })
        .then(() => {
          const firstErrorData = _.find(params, i => !stringUtils.isBlank(i.errorMsg));
          if (firstErrorData !== undefined) {
            reject(new Error('checkFail'));
            return;
          }
          return this.batchImportGoodsExistsCheck(params, codes);
        })
        .then(existGoods => {
          const firstErrorData = _.find(params, i => i.goodsExist === 0);
          if (firstErrorData !== undefined) {
            reject(new Error('goodsNotExist'));
            return;
          }
          resolve(existGoods);
        })
        .catch(reject);
    });
  },
  batchImportPreCheck: function (params) {
    return new Promise((resolve, reject) => {
      if (!Array.isArray(params)) {
        reject(new Error('入参错误'));
        return;
      }
      if (params.length === 0) {
        reject(new Error('没有导入数据'));
        return;
      }
      if (params.length > 200) {
        reject(new Error('导入数据超过200条限制'));
        return;
      }
      const columns = ['code', 'name', 'price', 'qty', 'manufactureDate', 'salePrice', 'vipPrice'];
      const firstRowColumns = _.keys(params[0]);
      const bothColumns = columns.filter(i => new Set(firstRowColumns).has(i));
      if (bothColumns.length < columns.length) {
        reject(new Error('导入模板错误'));
        return;
      }

      resolve();
    });
  },
  batchImportLoopCheck: function (params) {
    return new Promise((resolve) => {
      const codeGroup = _.groupBy(_.filter(params, i => !stringUtils.isBlank(i.code)), 'code');
      for (const key in codeGroup) {
        const items = codeGroup[key];
        if (items.length > 1) {
          items.forEach(i => {
            i.errorMsg = '条码重复；';
          });
        }
      }

      const codes = [];
      const qtyCodes = [];
      const errorMsg = new StringBuilder();
      for (const item of params) {
        errorMsg.clear();
        const codeBool = /^[0-9a-zA-Z-]{1,16}$/.test(item.code);
        if (codeBool) {
          codes.push(item.code);
        } else if (stringUtils.isBlank(item.code)) {
          errorMsg.append('条码不能为空；');
        } else {
          errorMsg.append('条码格式错误；');
        }

        if ((item.name + '').length > 60) {
          errorMsg.append('商品名称长度不能超过60；');
        }

        if (stringUtils.isBlank(item.price)) {
          errorMsg.append('进货价不能为空；');
        } else if (isNaN(item.price)) {
          errorMsg.append('进货价不是数字；');
        } else if (item.price < 0) {
          errorMsg.append('进货价不能小于0；');
        } else if (item.price > 999999.999999) {
          errorMsg.append('进货价不能大于999999.999999；');
        } else {
          const priceSplit = (+item.price + '').split('.');
          if (priceSplit.length === 2 && priceSplit[1].length > 6) {
            errorMsg.append('进货价小数位数不能超过6位；');
          }
        }

        if (stringUtils.isBlank(item.qty)) {
          errorMsg.append('进货数量不能为空；');
        } else if (isNaN(item.qty)) {
          errorMsg.append('进货数量不是数字；');
        } else if (item.qty < 0) {
          errorMsg.append('进货数量应大于0；');
        } else if (item.qty > 99999.999) {
          errorMsg.append('进货数量不能大于99999.999；');
        } else {
          const qtySplit = (+item.qty + '').split('.');
          if (qtySplit.length === 2) {
            const qtySplitOneLenght = qtySplit[1].length;
            if (qtySplitOneLenght > 3) {
              errorMsg.append('进货数量小数位数不能超过3位；');
            } else if (codeBool && qtySplitOneLenght > 0) {
              qtyCodes.push(item.code);
            }
          }
        }

        const {salePrice, vipPrice} = item;
        // 零售价
        if (!stringUtils.isBlank(salePrice)) {
          if (isNaN(salePrice)) {
            errorMsg.append('零售价不是数字；');
          } else if (salePrice < 0) {
            errorMsg.append('零售价不能小于0；');
          } else if (salePrice > 999999.99) {
            errorMsg.append('零售价不能大于999999.99；');
          } else {
            const priceSplit = (+salePrice + '').split('.');
            if (priceSplit.length === 2 && priceSplit[1].length > 2) {
              errorMsg.append('零售价小数位数不能超过2位；');
            }
          }
        }

        // 会员价
        if (!stringUtils.isBlank(vipPrice)) {
          if (isNaN(vipPrice)) {
            errorMsg.append('会员价不是数字；');
          } else if (vipPrice < 0) {
            errorMsg.append('会员价不能小于0；');
          } else if (vipPrice > 999999.99) {
            errorMsg.append('会员价不能大于999999.99；');
          } else {
            const priceSplit = (+vipPrice + '').split('.');
            if (priceSplit.length === 2 && priceSplit[1].length > 2) {
              errorMsg.append('会员价小数位数不能超过2位；');
            }
          }
        }

        if (!stringUtils.isBlank(item.manufactureDate) && item.manufactureDate.trim() !== new Date(item.manufactureDate).format('yyyy-MM-dd')) {
          errorMsg.append('生产日期格式错误；');
        }

        if (!errorMsg.isEmpty()) {
          item.errorMsg = stringUtils.isBlank(item.errorMsg) ? errorMsg.toString() : item.errorMsg + errorMsg.toString();
        }
      }

      resolve({ codes, qtyCodes });
    });
  },
  batchImportNotWeightQtyCheck: function (params, qtyCodes) {
    return new Promise((resolve, reject) => {
      const qtyCodesLength = qtyCodes.length;
      if (qtyCodesLength === 0) {
        resolve();
        return;
      }

      const codeSql = new StringBuilder(`select '${qtyCodes[0]}' as code`);
      for (let i = 1; i < qtyCodesLength; i++) {
        codeSql.append(` union all select '${qtyCodes[i]}'`);
      }
      const columnSql = `goods.code, tmp1.ext_barcode, goods.unit_fingerprint`;
      const getUnitsParams = {
        columns: `name, fingerprint`,
        wheres: `where is_deleted = 0 and name in ('${demo.$store.state.show.weightUnits.join('\',\'')}')`,
        orderBys: ``,
        limit: ``
      };
      Promise.all([dao.asyncExec(sqlApi.getUnits.format(getUnitsParams)), dao.asyncExec(sqlApi.goodsIfExists.format(codeSql.toString(), columnSql))])
        .then(res => {
          const weightUnits = res[0];
          const weightUnitFingerprints = _.map(weightUnits, 'fingerprint');
          const notWeightGoods = _.filter(res[1], i => !weightUnitFingerprints.includes(i.unitFingerprint));
          const notWeightCodes = new Set();
          notWeightGoods.forEach(i => {
            if (!stringUtils.isBlank(i.code)) {
              notWeightCodes.add(i.code);
            }
            if (!stringUtils.isBlank(i.extBarcode)) {
              notWeightCodes.add(i.extBarcode);
            }
          });
          params.forEach(i => {
            if (qtyCodes.includes(i.code) && notWeightCodes.has(i.code)) {
              i.errorMsg = stringUtils.isBlank(i.errorMsg) ? '非称重商品进货数量应为整数；' : i.errorMsg + '非称重商品进货数量应为整数；';
            }
          });
          resolve();
        })
        .catch(reject);
    });
  },
  batchImportGoodsExistsCheck: function (params, codes) {
    return new Promise((resolve, reject) => {
      const codesLength = codes.length;
      if (codesLength === 0) {
        resolve();
        return;
      }

      const codeSql = new StringBuilder(`select '${codes[0]}' as code`);
      for (let i = 1; i < codesLength; i++) {
        codeSql.append(` union all select '${codes[i]}'`);
      }
      const columnSql =
        `goods.id, goods.name, goods.code, tmp1.ext_barcode, goods.sale_price, goods.pur_price, goods.vip_price,
        goods.cur_stock, goods.unit_fingerprint, goods.fingerprint`;
      dao.asyncExec(sqlApi.goodsIfExists.format(codeSql.toString(), columnSql))
        .then(existGoods => {
          const allCodes = new Set();
          existGoods.forEach(i => {
            if (!stringUtils.isBlank(i.code)) {
              allCodes.add(i.code);
            }
            if (!stringUtils.isBlank(i.extBarcode)) {
              allCodes.add(i.extBarcode);
            }
          });

          params.forEach(i => {
            if (codes.includes(i.code) && !allCodes.has(i.code)) {
              i.goodsExist = 0;
            }
          });

          resolve(existGoods);
        })
        .catch(reject);
    });
  },
  batchImportGoods: function (params, existGoods) {
    return new Promise((resolve, reject) => {
      if (existGoods === undefined) {
        resolve(0);
        return;
      }

      const getUnitsParams = {
        columns: `name, fingerprint`,
        wheres: `where is_deleted = 0`,
        orderBys: ``,
        limit: ``
      };
      dao.asyncExec(sqlApi.getUnits.format(getUnitsParams))
        .then(units => {
          const unitsMap = {};
          units.forEach(i => {
            unitsMap[i.fingerprint] = i.name;
          });
          existGoods.forEach(i => {
            const unitName = unitsMap[i.unitFingerprint];
            i.unitName = unitName === undefined ? null : unitName;
          });

          const goodsCodeGroup = _.groupBy(_.filter(existGoods, i => !stringUtils.isBlank(i.code)), 'code');
          const goodsExtBarcodeGroup = _.groupBy(_.filter(existGoods, i => !stringUtils.isBlank(i.extBarcode)), 'extBarcode');
          params.forEach(i => {
            const goods = (goodsCodeGroup[i.code] || goodsExtBarcodeGroup[i.code])[0];
            i.id = goods.id;
            i.name = goods.name;
            i.goodsCode = goods.code;
            i.salePrice = i.salePrice || goods.salePrice; // 如果导入的列表中有零售价使用导入的零售价
            i.purPrice = goods.purPrice;
            i.curStock = goods.curStock;
            i.vipPrice = i.vipPrice || goods.vipPrice; // 如果导入的列表中有会员价使用导入的会员价
            i.unitName = goods.unitName;
            i.unitFingerprint = goods.unitFingerprint;
            i.fingerprint = goods.fingerprint;
          });
          const paramsGoodsCodeGroup = _.groupBy(params, 'goodsCode');
          const deleteCodes = [];
          for (const goodsCode in paramsGoodsCodeGroup) {
            const goodsList = paramsGoodsCodeGroup[goodsCode];
            const goodsListLength = goodsList.length;
            if (goodsListLength > 1) {
              goodsList[0]['qty'] = _.sumBy(goodsList, 'qty');
              const deleteGoods = goodsList.splice(1, goodsListLength - 1);
              deleteCodes.push(..._.map(deleteGoods, 'code'));
            }
          }
          _.remove(params, i => deleteCodes.includes(i.code));
          const hasMultyCodes = Object.keys(goodsExtBarcodeGroup).length === 0 ? 0 : 1;
          resolve(hasMultyCodes);
        })
        .catch(reject);
    });
  },
  /**
   * 指定商品最近3次进货历史
   * var params = {"goodFingerprint":"71c6ecac9cc0ec0d52326fc21005d255"};
   * purchaseService.getPurGoodsSupplier(res => {params, console.log(res)}, err => {console.error(err)});
   */
  getPurGoodsSupplier: function (params, onSuccess, onFail) {
    if (demo.isNullOrTrimEmpty(params.goodFingerprint)) {
      onSuccess();
    }
    dao.exec(sqlApi.getPurGoodsSupplier.format(params), onSuccess, onFail);
  }
};
window.purchaseService = purchaseService;
export default purchaseService;
