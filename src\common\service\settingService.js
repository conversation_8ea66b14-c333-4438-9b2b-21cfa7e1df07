import rest from '../../config/rest';
import dao from '../dao/dao';

const settingService = {
  reloadSetting: function() {
    return new Promise((resolve, reject) => {
      dao
        .asyncExec(sqlApi.settingGet.format({ where: '' }))
        .then(res => {
          const s = {};
          for (const i of res) {
            s[i.key] = i.value;
          }
          window.$setting = s;
          resolve(res);
        })
        .catch(reject);
    });
  },
  // 查询信息
  //   data:[
  //     {key:settingService.username},{key:settingService.useruid,value:''}
  // ]
  get: function (data, onSuccess, onFail) {
    var where = '';
    if (data.constructor === String) {
      // TODO
    }
    if (data.constructor === Object) {
      where = where + "where key='" + (data.key.constructor === Object ? data.key.value : data.key) + "'";
      if (data.value !== undefined) {
        where = where + " and value='" + data.value + "'";
      }
    }
    if (data.constructor === Array) {
      where = this.getForWhere(data, where);
    }

    dao.exec(
      sqlApi.settingGet.format({
        where: where
      }),
      onSuccess,
      onFail
    );
  },
  getForWhere(data, where) {
    var newWhere = where;
    for (var item of data) {
      if (newWhere !== '') {
        newWhere = newWhere + ' or ';
      } else {
        newWhere = ' where ';
      }
      var key = item.key.constructor === Object ? item.key.value : item.key;
      newWhere = newWhere + "  key='" + key + "'";
      if (item.value !== undefined) {
        newWhere = newWhere + " and value='" + item.value + "'";
      }
    }
    return newWhere;
  },
  getPutConstructor(data, select) {
    var newSelect = select;
    for (var item of data) {
      if (newSelect !== '') {
        newSelect = newSelect + ' union ';
      }
      var key = item.key.constructor === Object ? item.key.value : item.key;
      var remark = (item.remark ? item.remark : (settingService.key[key] ? settingService.key[key].remark : '') || '');
      newSelect = newSelect + " select '" + key + "','" + item.value + "','" + remark + "'";
    }
    return newSelect;
  },
  /**
   * 添加或更新
   * @data {key:'',value:''}
   * @param {*} onSuccess
   * @param {*} onFail
   */
  put: function (data, onSuccess, onFail) {
    var select = '';
    if (data.constructor === String) {
      // TODO
    }
    if (data.constructor === Object) {
      var key = (data.key.constructor === Object ? data.key.value : data.key);
      var remark = (data.remark ? data.remark : (settingService.key[key] ? settingService.key[key].remark : '') || '');
      select = select + " select '" + key + "','" + data.value + "','" + remark + "'";
    }
    if (data.constructor === Array) {
      select = this.getPutConstructor(data, select);
    }
    this.putSonar(select, onSuccess, onFail);
  },
  putSonar(select, onSuccess, onFail) {
    if (select !== '') {
      dao.asyncExec(sqlApi.settingPut.format({select}))
        .then(() => {
          return dao.asyncExec(sqlApi.settingGet.format({where: ''}));
        })
        .then(res => {
          const setting = {};
          res.forEach(o => {
            setting[o.key] = o.value;
          });
          window.$setting = setting;
          if (onSuccess !== null && onSuccess !== undefined) {
            onSuccess(res);
          }
        })
        .catch(onFail);
    }
  },

  /**
   * 当前时间与最近一次登陆时间，2小时之内，免登录
   * @param {*} onSuccess
   * @param {*} onFail
   */
  ifLogin: function (onSuccess, onFail) {
    dao.exec(sqlApi.settingLogin.format({
      key: settingService.key.userlastime.value
    }), onSuccess, onFail);
  },

  /**
   * 系统当前时间
   * @param {*} onSuccess
   * @param {*} onFail
   */
  getDate: function (onSuccess, onFail) {
    dao.exec(sqlApi.settingCurrentDate, onSuccess, onFail);
  },
  /**
   * 条码秤：条码秤相关表初始化
   */
  initScale: function (onSuccess, onFail) {
    dao.transaction(scaleSqlApi.initScale, onSuccess, onFail);
  },
  /**
   * 离线登陆，判断用户名密码是否正确。1：正确；0：错误
   * @param {*} data
   * @param {*} onSuccess
   * @param {*} onFail
   */
  offlineLogin: function (data, onSuccess, onFail) {
    data.type !== 2
      ? dao.exec(sqlApi.offlineLogin.format(data), onSuccess, onFail)
      : dao.exec(sqlApi.clerkEmployeeCheck.format(data), onSuccess, onFail);
  },
  /**
   * 清空数据
   * var params = [1,2,3,4,5,6];
   * settingService.clears(params, res => {console.log(res)}, err => {console.error(err);});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   */
  clears: function(params, onSuccess, onFail) {
    if (params.length === 0) {
      onFail('未选择清空模块');
      return;
    }

    var params1 = params.map(item => { return +item; }).sort();
    var products = params1.includes(1) ? 1 : 0;
    var pos = params1.includes(2) ? 2 : 0;
    var users = params1.includes(3) ? 3 : 0;
    var shiftHistories = params1.includes(4) ? 4 : 0;
    var vips = params1.includes(5) ? 5 : 0;
    var recharge = params1.includes(6) ? 6 : 0;

    var hisId;
    var successes = [];
    this.clearHistoryAdd(params1).then(res => {
      hisId = res.data.data;
      return this.clearVips(vips || recharge);
    }).then(() => {
      if (vips > 0) {
        successes.push(vips);
      }
      if (recharge > 0) {
        successes.push(recharge);
      }
      return this.clearPos(products || pos);
    }).then(() => {
      if (pos > 0) {
        successes.push(pos);
      }
      return this.clearProducts(products);
    }).then(() => {
      if (products > 0) {
        successes.push(products);
      }
      return this.clearUsers(users || shiftHistories);
    }).then(() => {
      if (users > 0) {
        successes.push(users);
      }
      if (shiftHistories > 0) {
        successes.push(shiftHistories);
      }

      this.afterClears(hisId, params1, successes, onSuccess, onFail);
    })
      .catch(err => {
        console.error('settingService.clears: ' + JSON.stringify(err));
        this.afterClears(hisId, params1, successes, onSuccess, onFail);
      });
  },
  clearHistoryAdd: function(params) {
    return new Promise((resolve, reject) => {
      demo.$http.post(rest.clearHistory, {
        modules: params.join()
      }, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  clearVips: function(params) {
    return new Promise((resolve, reject) => {
      if (params === 0) {
        resolve();
        return;
      }

      demo.$http.delete(rest.clearVips, {
        data: {
          moduleId: params
        }
      }, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  clearPos: function(params) {
    return new Promise((resolve, reject) => {
      if (params === 0) {
        resolve();
        return;
      }
      let param = { tables: 'product_companies' };
      Promise.all([demo.$http.delete(rest.clearPos, {
        timeout: 60000
      }), demo.$http.delete(rest.clearsSpecificTable, {params: param}, { timeout: 60000 })])
        .then(resolve)
        .catch(reject);
    });
  },
  clearProducts: function(params) {
    return new Promise((resolve, reject) => {
      if (params === 0) {
        resolve();
        return;
      }

      demo.$http.delete(rest.clearProducts, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  clearUsers: function(params) {
    return new Promise((resolve, reject) => {
      if (params === 0) {
        resolve();
        return;
      }

      demo.$http.delete(rest.clearUsers, {
        data: {
          moduleId: params
        }
      }, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  afterClears: function(hisId, modules, successes, onSuccess, onFail) {
    if (successes.length === 0) {
      onSuccess();
      return;
    }
    this.clearHistoryUpdate(hisId, modules, successes)
      .then(() => {
        return this.getInitData(successes);
      })
      .then(res => {
        var initData;
        if (!demo.isNullOrTrimEmpty(res)) {
          initData = res.data.data;
        }
        this.clearLocalTables(hisId, successes, initData, onSuccess, onFail);
      })
      .catch(err => {
        console.error('settingService.afterClears: ' + JSON.stringify(err));
        var initData = {
          types: [],
          units: []
        };
        this.clearLocalTables(hisId, successes, initData, () => {
          onSuccess(err);
        }, onFail);
      });
  },
  clearHistoryUpdate: function(id, modules, successes) {
    return new Promise((resolve, reject) => {
      demo.$http.put(rest.clearHistory, {
        id,
        status: modules.length === successes.length ? 1 : 2,
        successes: successes.sort().join()
      }, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  otherClears: function(hisId, successes, onSuccess, onFail) {
    if (successes.length === 0) {
      onSuccess();
      return;
    }
    this.getInitData(successes)
      .then(res => {
        var initData;
        if (!demo.isNullOrTrimEmpty(res)) {
          initData = res.data.data;
        }
        this.clearLocalTables(hisId, successes, initData, onSuccess, onFail);
      })
      .catch(err => {
        console.error('settingService.afterClears: ' + JSON.stringify(err));
        var initData = {
          types: [],
          units: []
        };
        this.clearLocalTables(hisId, successes, initData, () => {
          onSuccess(err);
        }, onFail);
      });
  },
  getInitData: function(successes) {
    return new Promise((resolve, reject) => {
      if (!successes.includes(1)) {
        resolve();
        return;
      }

      demo.$http.get(rest.getInitData, {
        timeout: 60000
      })
        .then(resolve)
        .catch(reject);
    });
  },
  getInfoValueForKey: function(key) {
    let info = demo.t2json($setting.info);
    if (demo.isNullOrTrimEmpty(info)) {
      return '';
    }
    return info[key];
  },
  setInfoValueForKey: function(key, value) {
    let info = demo.t2json($setting.info);
    if (demo.isNullOrTrimEmpty(info)) {
      info = {};
    }
    info[key] = value;
    settingService.put({ key: 'info', value: JSON.stringify(info) });
  },
  clearLocalTableSnapshot(pageName, onSuccess, onFail) {
    dao.exec(sqlApi.existsTable.format({'tblName': 'snapshot'}), res => {
      if (demo.t2json(res)[0].cnt > 0) {
        dao.exec(clearsSql.delSnapshot.format({'pageName': pageName}), onSuccess, onFail);
      }
    }, onFail);
  },
  clearLocalTables: function(hisId, successes, initData, onSuccess, onFail) {
    var bool1 = successes.includes(1);
    var bool2 = successes.includes(2);
    var bool3 = successes.includes(3);
    var bool4 = successes.includes(4);

    var info = demo.t2json($setting.info);
    if (demo.isNullOrTrimEmpty(info)) {
      info = {};
    }
    info.clearId = hisId;
    $setting.info = JSON.stringify(info);
    var sql = clearsSql.replaceClearId.format('info', $setting.info, '') + '\n';
    if (bool1) {
      sql +=
        clearsSql.delGoods + '\n' +
        clearsSql.delGoodsAttributes + '\n' +
        clearsSql.delGoodsExtBarcode + '\n' +
        clearsSql.delGoodsSuppliers + '\n' +
        clearsSql.delImages + '\n' +
        clearsSql.delTypes + '\n' +
        clearsSql.delUnits + '\n' +
        clearsSql.delScale + '\n' +
        clearsSql.delSpecs + '\n';
      initData.types.forEach(item => {
        sql += clearsSql.initTypes.format(demo.sqlConversion(item)) + '\n';
      });
      initData.units.forEach(item => {
        sql += clearsSql.initUnits.format(demo.sqlConversion(item)) + '\n';
      });
      this.clearLocalTableSnapshot('purchase', () => {}, () => {});
    }
    if (bool2) {
      sql +=
        clearsSql.delSuppliers + '\n' +
        clearsSql.delGoodsSuppliers + '\n' +
        clearsSql.delPurchases + '\n' +
        clearsSql.delSales + '\n' +
        clearsSql.delInventories + '\n' +
        clearsSql.delRecordBills + '\n';
      this.clearLocalTableSnapshot('purchase', () => {}, () => {});
      if (!bool1) {
        sql += clearsSql.delCurStock;
      }
    }
    if (bool3) {
      sql += clearsSql.delClerks + '\n';
    }
    if (bool4) {
      sql += clearsSql.delShiftHistories + '\n';
    }

    dao.transaction(sql, () => {
      if (bool1) {
        specsService.getSpecs()
          .then(() => {
            onSuccess();
            external.reloadForm();
          })
          .catch(onFail);
      } else if (bool2 || bool3 || bool4) {
        onSuccess();
        external.reloadForm();
      } else {
        onSuccess();
      }
    }, onFail);
  }
};

Object.defineProperties(settingService, {
  key: {
    get: function () {
      return {
        stock: {
          value: 'stock',
          remark: '无库存设置',
          writable: false,
          enumerable: true,
          configurable: false
        },
        cutsmallmoney: {
          value: 'cutsmallmoney',
          remark: '抹零设置',
          writable: false,
          enumerable: true,
          configurable: false
        },
        username: {
          value: 'username',
          remark: '用户名',
          writable: false,
          enumerable: true,
          configurable: false
        },
        usertoken: {
          value: 'usertoken',
          remark: '用户token',
          writable: false,
          enumerable: true,
          configurable: false
        },
        useruid: {
          value: 'useruid',
          remark: '用户uid',
          writable: false,
          enumerable: true,
          configurable: false
        },
        usersid: {
          value: 'usersid',
          remark: '用户sid',
          writable: false,
          enumerable: true,
          configurable: false
        },
        userpwd: {
          value: 'userpwd',
          remark: '用户密码',
          writable: false,
          enumerable: true,
          configurable: false
        },
        employeenumber: {
          value: 'employeenumber',
          remark: '工号',
          writable: false,
          enumerable: true,
          configurable: false
        },
        ultimate: {
          value: 'ultimate',
          remark: '是否旗舰版权限',
          writable: false,
          enumerable: true,
          configurable: false
        },
        employeenumber_list: {
          value: 'employeenumber_list',
          remark: '工号列表',
          writable: false,
          enumerable: true,
          configurable: false
        },
        employeeremember: {
          value: 'employeeremember',
          remark: '员工是否记住密码',
          writable: false,
          enumerable: true,
          configurable: false
        },
        period: {
          value: 'period',
          remark: '是否是无限期限的',
          writable: false,
          enumerable: true,
          configurable: false
        },
        enddate: {
          value: 'enddate',
          remark: '有效期结束时间，时间戳',
          writable: false,
          enumerable: true,
          configurable: false
        },
        userremember: {
          value: 'userremember',
          remark: '是否记住密码',
          writable: false,
          enumerable: true,
          configurable: false
        },
        userlastime: {
          value: 'userlastime',
          remark: '最近一次登陆时间',
          writable: false,
          enumerable: true,
          configurable: false
        },
        devicecode: {
          value: 'devicecode',
          remark: '单号后缀',
          writable: false,
          enumerable: true,
          configurable: false
        },
        show_ad: {
          value: 'show_ad',
          remark: '是否显示广告',
          writable: false,
          enumerable: true,
          configurable: false
        },
        remark_content: {
          value: 'remark_content',
          remark: '小票备注内容',
          writable: false,
          enumerable: true,
          configurable: false
        },
        font_value: {
          value: 'font_value',
          remark: '小票正文字号设置',
          writable: false,
          enumerable: true,
          configurable: false
        },
        line_height: {
          value: 'line_height',
          remark: '小票行间距',
          writable: false,
          enumerable: true,
          configurable: false
        },
        printMode: {
          value: 'printMode',
          remark: '打印机驱动',
          writable: false,
          enumerable: true,
          configurable: false
        },
        print_cols: {
          value: 'print_cols',
          remark: '小票打印规格',
          writable: false,
          enumerable: true,
          configurable: false
        },
        print_cols_label: {
          value: 'print_cols_label',
          remark: '条码纸规格',
          writable: false,
          enumerable: true,
          configurable: false
        },
        tagPrintCols: {
          value: 'tagPrintCols',
          remark: '吊牌纸规格',
          writable: false,
          enumerable: true,
          configurable: false
        },
        print_small_ticket_afterpay: {
          value: 'print_small_ticket_afterpay',
          remark: '支付完成是否打印小票',
          writable: false,
          enumerable: true,
          configurable: false
        },
        useCardReader: {
          value: 'useCardReader',
          remark: '是否读卡：0-不读，1-读',
          writable: false,
          enumerable: true,
          configurable: false
        },
        show_novice: {
          value: 'show_novice',
          remark: '0不显示引导页1显示引导页',
          writable: false,
          enumerable: true,
          configurable: false
        },
        voice_off: {
          value: 'voice_off',
          remark: '语音播报开关',
          writable: false,
          enumerable: true,
          configurable: false
        },
        set_priceNotice: {
          value: 'set_priceNotice',
          remark: '负利润check',
          writable: false,
          enumerable: true,
          configurable: false
        },
        print_card_small_ticket_afterpay: {
          value: 'print_card_small_ticket_afterpay',
          remark: '次卡支付完成是否打印小票',
          writable: false,
          enumerable: true,
          configurable: false
        },
        smallPrinterCopies: {
          value: 'smallPrinterCopies',
          remark: '小票打印份数',
          writable: false,
          enumerable: true,
          configurable: false
        },
        labelPrinterCopies: {
          value: 'labelPrinterCopies',
          remark: '条码打印份数',
          writable: false,
          enumerable: true,
          configurable: false
        },
        tipPrinterCopies: {
          value: 'tipPrinterCopies',
          remark: '标价签打印份数',
          writable: false,
          enumerable: true,
          configurable: false
        },
        memberInfoArr: {
          value: 'memberInfoArr',
          remark: '打印会员信息',
          writable: false,
          enumerable: true,
          configurable: false
        },
        logAndCode: {
          value: 'logAndCode',
          remark: '店铺logo及二维码',
          writable: false,
          enumerable: true,
          configurable: false
        },
        setLabelDefault: {
          value: 'setLabelDefault',
          remark: '条码默认打印份数开关',
          writable: false,
          enumerable: true,
          configurable: false
        },
        setTipDefault: {
          value: 'setTipDefault',
          remark: '标价签默认打印份数开关',
          writable: false,
          enumerable: true,
          configurable: false
        },
        labelItem: {
          value: 'labelItem',
          remark: '条码需要打印的信息',
          writable: false,
          enumerable: true,
          configurable: false
        },
        model: {
          value: 'model',
          remark: '吊牌打印个性化设置',
          writable: false,
          enumerable: true,
          configurable: false
        },
        printVipPrice: {
          value: 'printVipPrice',
          remark: '打印会员价开关',
          writable: false,
          enumerable: true,
          configurable: false
        },
        eye: {
          value: 'eye',
          remark: '是否隐藏首页销售额利润',
          writable: false,
          enumerable: true,
          configurable: false
        },
        estimatedProfit: {
          value: 'estimatedProfit',
          remark: '首页预估利润默认展示日期',
          writable: false,
          enumerable: true,
          configurable: false
        },
        hasWeigh: {
          value: 'hasWeigh',
          remark: '是否启用电子秤',
          writable: false,
          enumerable: true,
          configurable: false
        },
        weighValue: {
          value: 'weighValue',
          remark: '电子秤端口',
          writable: false,
          enumerable: true,
          configurable: false
        },
        weighTypeValue: {
          value: 'weighTypeValue',
          remark: '电子秤类型',
          writable: false,
          enumerable: true,
          configurable: false
        },
        showKexian: {
          value: 'showKexian',
          remark: '是否启用客显',
          writable: false,
          enumerable: true,
          configurable: false
        },
        kexianValue: {
          value: 'kexianValue',
          remark: '客显端口',
          writable: false,
          enumerable: true,
          configurable: false
        },
        isOnceOpen: {
          value: 'isOnceOpen',
          remark: '是否是第一次打开称重商品',
          writable: false,
          enumerable: true,
          configurable: false
        },
        tagIsTransverse: {
          value: 'tagIsTransverse',
          remark: '打印预览',
          writable: false,
          enumerable: true,
          configurable: false
        },
        codePrintValue: {
          value: 'codePrintValue',
          remark: '打印条码',
          writable: false,
          enumerable: true,
          configurable: false
        },
        shortModels: {
          value: 'shortModels',
          remark: '短信模板',
          writable: false,
          enumerable: true,
          configurable: false
        },
        mailPrintTicket: {
          value: 'mailPrintTicket',
          remark: '寄件打印小票',
          writable: false,
          enumerable: true,
          configurable: false
        },
        pickUpPrintTicket: {
          value: 'pickUpPrintTicket',
          remark: '取件打印小票',
          writable: false,
          enumerable: true,
          configurable: false
        }
      };
    }
  }
});

window.settingService = settingService;
export default settingService;
