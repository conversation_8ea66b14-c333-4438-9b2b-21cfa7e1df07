const shiftSql = {
  // 交接班并登出，插入交接班履历数据
  changeAddShiftHistories:
        `update shifthistories
            set
            sales_amt ='{sales_amt}'
            , pay_amt ='{pay_amt}'
            , vip_charge ='{vip_charge}'
            , cash_amt ='{cash_amt}'
            , remark ='{remark}'
            , revise_by ='{revise_by}'
            , end_date ='{end_date}'
            , is_synced =0
        where fingerprint='{fingerprint}';`,
  // 插入交接班履历数据
  insertShiftHistories: `insert into shifthistories
  (uid, employee_number, name, begin_date, create_by, fingerprint)
  VALUES (
  {uid},case when '{employee_number}'='null' then null else '{employee_number}' end,'{name}',strftime('%Y-%m-%d %H:%M:%S','now','localtime'),'{create_by}','{fingerprint}'
  );`,
  getShiftById:
        `select id, uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge, cash_amt, remark, 
          info1, info2, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
        from shifthistories
        where id={0};`,
  shiftHistoryInsert:
        `insert into shifthistories (
          uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge, cash_amt, remark, 
          info1, info2, is_synced, fingerprint, create_by, revise_by, create_at, revise_at
        ) values (
          {uid}, {employeeNumber}, {name}, {beginDate}, {endDate}, {salesAmt}, {payAmt}, {vipCharge}, {cashAmt}, {remark},
          {info1}, {info2}, 0, {fingerprint}, {createBy}, {reviseBy}, {createAt}, {reviseAt}
        );`,
  shiftHistoryUpdate:
        `update shifthistories set
          uid={uid},
          employee_number={employeeNumber},
          name={name},
          begin_date={beginDate},
          end_date={endDate},
          sales_amt={salesAmt},
          pay_amt={payAmt},
          vip_charge={vipCharge},
          cash_amt={cashAmt},
          remark={remark},
          info1={info1},
          info2={info2},
          is_synced=0,
          revise_by={reviseBy},
          revise_at={reviseAt}
        where id={id};`,
  getShiftOneHistories: `select *  from shifthistories where fingerprint = '{fingerprint}';`,
  shiftHistoryReports:
  `select id, uid, case when employee_number is null or trim(employee_number)='' then '管理员' else name end as name,
      ifnull(employee_number, '') as employee_number,
      ifnull(strftime('%Y-%m-%d %H:%M:%S', begin_date), '') as begin_date,
      ifnull(strftime('%Y-%m-%d %H:%M:%S', end_date), '') as end_date,
      ifnull(sales_amt, '0') as sales_amt, ifnull(pay_amt, '0') as pay_amt, ifnull(vip_charge, '0') as vip_charge, ifnull(cash_amt, '0') as cash_amt, create_by, revise_by, is_synced, fingerprint,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as create_at,
      strftime('%Y-%m-%d %H:%M:%S', revise_at) as revise_at,
      ifnull(remark, '') as remark,
      info1, info2,
      case when info1 is null or (info1 not like '%"isDel":1%' and info1 not like '%"isDel": 1%') then '' else '已删除' end as status
   from shifthistories {wheres}
   order by begin_date desc
   limit {limit} offset {offset};`,
  shiftHistoryReportsCount:
    `select count(1) as totalCount from shifthistories {wheres};`,
  shiftHistoryReportsTotal:
  `select ifnull(sum(sales_amt), '0') as salesAmtTotal, ifnull(sum(pay_amt), '0') as payAmtTotal,
    ifnull(sum(vip_charge), '0') as vipChargeTotal, ifnull(sum(cash_amt), '0') as cashAmtTotal
    from shifthistories {wheres};`,
  selectShiftHistory: `select {select} from shifthistories {wheres};`,
  // 交接班记录删除或恢复
  shiftHistoryReportsDeleteOrReply: `update shifthistories set info1 = '{info1}',revise_at = datetime('now', 'localtime'),revise_by = {uid}, is_synced = 0 where id = {id}`,
  getNextShiftById:
  `with tmp as (
    select uid, begin_date from shifthistories where id = {0}
  )
  select id, uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge, cash_amt, remark, 
    info1, info2, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
  from shifthistories
  where id <> {0}
  and (info1 is null or (info1 not like '%"isDel":1%' and info1 not like '%"isDel": 1%'))
  and uid = (select uid from tmp)
  and begin_date >= (select begin_date from tmp)
  order by begin_date asc
  limit 1;`,
  getByShiftSales: `select account_id,pay_amt,in_out,fingerprint from sales where is_deleted = 0 and  uid = '{uid}' and create_at between '{start}' and '{end}'`,
  getBlendsByShift:
  `select blend.account_id, blend.pay_amt, sa.in_out,blend.fingerprint
  from sale_blend_pays blend
  inner join sales sa
  on blend.sale_fingerprint = sa.fingerprint
  where sale_fingerprint in ({0});`,
  getShiftHistoriesEndDate: `select strftime('%Y-%m-%d %H:%M:%S', end_date) as end_date from shifthistories where uid='{uid} and begin_date > '{start}' order by begin_date limit 1;`
}

export default shiftSql;
