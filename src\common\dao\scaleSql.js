global.scaleSqlApi = {
  initScale:
    `CREATE TABLE if not exists "scale_list" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "scale_brand_code" integer NOT NULL,
      "scale_brand_name" text NOT NULL,
      "scale_type_code" integer NOT NULL,
      "scale_type_name" text NOT NULL,
      "port" integer NOT NULL,
      "hotkey_qty" integer NOT NULL,
      "is_del" integer NOT NULL default  0,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NOT NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00'
    );
    CREATE TABLE if not exists "product_scale" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "good_fingerprint" text NOT NULL,
      "is_sendscale" integer NOT NULL default 1,
      "model" integer NOT NULL,
      "expire_date" integer NOT NULL default 0,
      "tare" real NOT NULL,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NOT NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00',
      CONSTRAINT "uk_product_scale" UNIQUE ("good_fingerprint" ASC)
    );
    CREATE TABLE if not exists "sendscale" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "sendscale_fingerprint" text NOT NULL,
      "sendscale_name" text NOT NULL,
      "scale_brand_code" integer NOT NULL,
      "scale_type_code" integer NOT NULL,
      "scale_ip" text NOT NULL,
      "port" integer NOT NULL,
      "remark" text NULL,
      "is_del" integer NOT NULL default  0,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NOT NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00',
      CONSTRAINT "uk_sendscale" UNIQUE ("sendscale_fingerprint" ASC)
    );
    CREATE TABLE if not exists "sendscale_product" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "sendscale_product_fingerprint" text NOT NULL,
      "sendscale_fingerprint" text NOT NULL,
      "good_fingerprint" text NOT NULL,
      "plu_code" integer NOT NULL,
      "hot_key" integer NULL,
      "is_del" integer NOT NULL default  0,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NOT NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00',
      CONSTRAINT "uk_sendscale_product" UNIQUE ("sendscale_product_fingerprint" ASC)
    );
    CREATE TABLE if not exists "sendscale_history" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "sendscale_history_fingerprint" text not NULL,
      "sendscale_fingerprint" text  NULL,
      "sendscale_name" text NULL,
      "scale_brand_name" text NULL,
      "scale_type_name" text NULL,
      "scale_ip" text NULL,
      "port" integer NULL,
      "remark" text NULL,
      "good_fingerprint" text NULL,
      "type_fingerprint" text NULL,
      "code" text NULL,
      "name" text NULL,
      "sale_price" real null,
      "plu_code" integer NULL,
      "hot_key" integer NULL,
      "is_sendscale" integer NULL,
      "model" integer NULL,
      "expire_date" integer NULL,
      "tare" real NULL,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp default (datetime('now','localtime')),
      "revise_at" timestamp default (datetime('now','localtime')),
      "is_sync" integer NULL default  0,
      "sync_at" timestamp default  '2000-01-01 00:00:00',
      CONSTRAINT "uk_sendscale_history" UNIQUE ("sendscale_history_fingerprint" ASC)
    );
    CREATE TABLE if not exists "sync_product_scale" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "good_fingerprint" text NULL,
      "is_sendscale" integer NULL,
      "model" integer NULL,
      "expire_date" integer NULL,
      "tare" real NULL,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp NULL,
      "revise_at" timestamp NULL,
      "is_sync" integer NULL,
      "sync_at" timestamp NULL
    );
    CREATE TABLE if not exists "sync_sendscale" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "sendscale_fingerprint" text NULL,
      "sendscale_name" text NULL,
      "scale_brand_code" integer NULL,
      "scale_type_code" integer NULL,
      "scale_ip" text NULL,
      "port" integer NULL,
      "remark" text NULL,
      "is_del" integer NULL,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp NULL,
      "revise_at" timestamp NULL,
      "is_sync" integer NULL,
      "sync_at" timestamp NULL
    );
    CREATE TABLE if not exists "sync_sendscale_product" (
      "id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "sendscale_product_fingerprint" text NULL,
      "sendscale_fingerprint" text NULL,
      "good_fingerprint" text NULL,
      "plu_code" integer NULL,
      "hot_key" integer NULL,
      "is_del" integer NULL,
      "create_by" text NULL,
      "revise_by" text NULL,
      "create_at" timestamp NULL,
      "revise_at" timestamp NULL,
      "is_sync" integer NULL,
      "sync_at" timestamp NULL
    );`
};
