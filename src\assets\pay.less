.el-textarea__inner:focus {
  border-color: @themeBackGroundColor;
}
.pc_pay_calculator {
  width: calc(100% - 88px);
  height: 320px;
  margin-top: 17px;
  margin-left: 44px;
  max-width: 580px;
  border-radius: 5px;
}
.pc_pay_calculator table {
  text-align: center;
  color: @themeFontColor;
  width: 100%;
  height: 100%;
  font-size: 48px;
}
.pc_pay_calculator table td {
  border: 1px solid #eef0f3;
  width: 25%;
  cursor: pointer;
}
.pc_pay_calculator_grey {
  background: #f5f8fb;
  color: #bac9d2;
  cursor: default;
}
.pc_pay_calculator_settlement {
  height: 320px;
  width: 655px;
  margin: 0 auto;
  border-radius: 5px;
  border: 1px solid #e3e6eb;
  overflow: hidden;
  margin-top: 22px;
  position: relative;
  background: #fff;
}
.pc_pay_calculator_settlement table {
  text-align: center;
  color: #688392;
  width: 100%;
  height: 100%;
  font-size: 36px;
}
.pc_pay_calculator_settlement table td {
  border: 1px solid #e3e6eb;
  width: 20%;
  cursor: pointer;
}
.pc_pay_calculator_blue {
  color: @themeBackGroundColor;
}
.pc_pay {
  width: 100%;
  height: 100%;
  background: #f5f8fb;
  padding: 10px !important;
}
.pc_pay1 {
  position: relative;
  width: 450px;
  height: calc(100% - 50px);
  background-color: #fff !important;
  float: left;
  border: 1px solid #e5e8ec;
  color: #333;
  border-radius: 5px;
  overflow: hidden;
}
.pc_pay1_bg {
  background: url(../image/pc_pay_gun.png) no-repeat center;
}
.pc_pay11 {
  font-weight: bold;
  overflow: hidden;
  position: fixed;
  height: 52px;
  color: @themeFontColor;
}
.pc_pay11 div {
  float: left;
  margin-top: 34px;
  line-height: 18px;
}
.pc_pay11_1 {
  overflow: hidden;
  font-weight: normal;
  font-size: 16px;
}
.pc_pay11_1 div {
  float: left;
}
.pc_pay12 {
  position: absolute;
  height: 240px;
  width: 100%;
  left: 0;
  bottom: 0;
}
.pc_pay13 {
  width: 136px;
  height: 40px;
  background: #f5f8fb;
  cursor: pointer;
  border-radius: 8px;
}
.pc_pay13 img {
  width: 30px;
  height: 30px;
  margin-top: 5px;
  margin-left: 7px;
  float: left;
}
.pc_pay13 div {
  margin-left: 3px;
  float: left;
  line-height: 40px;
}
.pc_pay13 span {
  color: @text;
}
.pc_pay14 {
  overflow: hidden;
  font-size: 16px;
  position: relative;
  height: 50px;
  padding-top: 10px;
  color: @themeFontColor;
}
.pc_pay14 div {
  float: left;
}
.pc_pay15 {
  border-radius: 18px;
  background: #fdebe9;
  width: 90px;
  height: 36px;
  text-align: center;
  margin-left: 10px;
  line-height: 36px;
  background-size: 100% 100%;
  color: #fd7d51;
  font-weight: bold;
}
.pc_pay16 {
  width: 93px;
  height: 36px;
  margin-left: 10px;
  background: @themeButtonBackGroundColor;
  border-radius: 18px;
}
.pc_pay16 img {
  width: 25px;
  height: 25px;
  margin-top: 6px;
  margin-left: 16px;
  float: left;
}
.pc_pay16 div {
  float: left;
  color: @themeBackGroundColor;
  font-weight: bold;
  line-height: 36px;
  margin-left: 5px;
}
.pc_pay17 {
  width: 100%;
  margin: 0 auto;
  border-bottom: 1px solid #e3e6eb;
  height: 16px;
}
.pc_pay18 {
  overflow: hidden;
  margin-left: 14px;
  margin-top: 18px;
  font-size: 16px;
  color: @themeFontColor;
}
.pc_pay19 {
  height: 40px;
  display: table-cell;
  vertical-align: bottom;
  font-size: 16px;
}
.pc_pay19 span {
  color: #ff6159;
}
.pc_pay2 {
  overflow: hidden;
  margin-top: 0px;
  color: #fff;
}
.pc_pay21 {
  text-align: center;
  width: 152px;
  height: 80px;
  background-image: linear-gradient(180deg, #ffbc90 0%, #ff6658 100%);
  border-radius: 5px;
  margin-left: 15px;
  float: left;
  cursor: pointer;
  position: relative;
  margin-top: 16px;
}
.pc_pay22 {
  font-size: 20px;
  margin-top: 18px;
  line-height: 24px;
  font-weight: bold;
}
.pc_pay23 {
  text-align: center;
  width: 166px;
  margin-left: 10px;
  height: 60px;
  background-image: linear-gradient(180deg, #d8b774 0%, #deb071 100%);
  border-radius: 5px;
  overflow: hidden;
  float: left;
  margin-top: 16px;
  cursor: pointer;
}
.pc_pay24 {
  position: relative;
  width: calc(100% - 460px);
  height: calc(100% - 50px);
  background-color: #fff !important;
  float: left;
  border: 1px solid #e5e8ec;
  color: @themeFontColor;
  margin-left: 10px;
  border-radius: 5px;
  overflow: hidden;
}
.pc_pay25 {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(80, 80, 80, 0.55);
  top: 0;
  left: 0;
  z-index: 200;
  overflow: auto;
}
.pc_pay26 {
  position: fixed;
  right: 10px;
  top: 25px;
  color: #fff;
  cursor: pointer;
}
.pc_pay27 {
  width: 125px;
  height: 125px;
  border: 5px solid #ddd;
  border-radius: 50%;
  margin: 0 auto;
  margin-top: 150px;
}
.pc_pay27 img {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.pc_pay28 {
  width: 400px;
  text-align: center;
  margin: 0 auto;
  margin-top: 25px;
  font-size: 24px;
  color: #eee;
}
.pc_pay29 {
  margin-top: 40px;
  font-weight: 100;
}
.pc_pay30 {
  text-align: center;
  width: calc(100% - 30px);
  margin-left: 15px;
  height: 60px;
  background-image: linear-gradient(180deg, #d8b774 0%, #deb071 100%);
  border-radius: 5px;
  overflow: hidden;
  float: left;
  margin-top: 16px;
  cursor: pointer;
}
.pc_pay3 {
  width: calc(100% - 102px);
  height: 89px;
  position: absolute;
  bottom: 0;
  left: 0;
  padding-left: 15px;
  z-index: 1;
  background: #fff;
  display: flex;
}
.pc_pay31 {
  width: 100%;
  margin-right: 14px;
  float: left;
  cursor: pointer;
}
.pc_pay32 {
  width: 0px;
  height: 0px;
  border: 8px solid #666;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: @themeBackGroundColor;
  border-top-color: transparent;
  margin-left: 48%;
}
.pc_pay33 {
  width: 100%;
  height: 60px;
  border: 1px solid #e3e6eb;
  background: #f5f8fb;
  border-radius: 5px;
  text-align: center;
  overflow: hidden;
}
.pc_pay34 {
  margin-top: 17px;
  font-size: 18px;
  line-height: 25px;
  font-weight: bold;
}
.pc_pay36 {
  background: @themeBackGroundColor;
  border: none;
  height: 60px;
  color: #fff;
}
.pc_pay37 {
  width: 580px;
  height: 60px;
  border-radius: 30px;
  border: 1px solid #e3e6eb;
  background: #f5f8fb;
  line-height: 1;
  float: left;
}
.pc_pay38 {
  width: 40px;
  height: 40px;
  margin-left: 14px;
  margin-top: 8px;
  float: left;
}
.pc_pay39 {
  width: calc(100% - 110px);
  float: left;
  margin-left: 15px;
  border: none;
  background: #f5f8fb;
  height: 100%;
  line-height: 1;
  font-size: 17px;
}
.pc_pay40 {
  width: calc(33% - 7px);
  margin-right: 8px;
  float: left;
  cursor: pointer;
}
.pc_pay4 {
  width: 20px;
  height: 20px;
  margin-left: 5px;
  margin-top: 19px;
  cursor: pointer;
}
.pc_pay41 {
  float: left;
  width: 166px;
}
.pc_pay42 {
  line-height: 20px;
  font-size: 16px;
}
.pc_pay43 {
  font-size: 26px;
  margin-top: 10px;
  font-weight: bold;
}
.pc_pay44 {
  font-size: 26px;
  font-weight: normal;
  color: #13ce66;
}
.pc_pay45 {
  // width: calc(100% - 80px);
  width: 100%;
  padding-left: 12px;
  // padding-right: 12px;
  overflow: hidden;
}
.pc_pay46 {
  width: 212px;
  height: 124px;
  border: 1px solid #e3e6eb;
  background: #f5f8fb;
  border-radius: 4px;
  margin-bottom: 8px;
  margin-right: 8px;
  overflow: hidden;
  float: left;
  position: relative;
}
.pc_pay46:hover {
  background: @themeBackGroundColor;
  cursor: pointer;
  box-shadow: 2px 2px 4px #999;
  color: #fff;
  border-color: @themeBackGroundColor;
}
.pc_pay46 img {
  width: 70px;
  height: 70px;
  float: left;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_pay46 p {
  margin-left: 11px;
  width: 116px;
  margin-top: 4px;
}
.pc_pay47 {
  margin-left: 14px;
  line-height: 16px;
  margin-top: 9px;
}
.pc_pay48 {
  width: 88px;
  position: fixed;
  right: -9px;
  top: 153px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 152px);
  z-index: 1;
}
.pc_pay49 {
  width: 55px;
  height: 54px;
  background: #f5f8fb;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
  line-height: 16px;
  color: @themeFontColor;
  overflow: hidden;
  cursor: pointer;
}
.pc_pay49 p {
  width: 40px;
  letter-spacing: 2px;
  margin-top: 11px;
  margin-left: 9px;
}
.pc_pay5 {
  background: linear-gradient(90deg, #69d6fe, #16aaff);
  color: #fff;
}
.pc_pay51 {
  width: 100%;
  padding-left: 40px;
  padding-right: 40px;
  height: calc(100% - 110px);
  overflow: scroll;
}
.pc_pay52 {
  font-size: 16px;
  font-weight: 700;
  margin-top: 45px;
  line-height: 16px;
  overflow: hidden;
  height: 18px;
}
.pc_pay52 div {
  float: left;
}
.pc_pay52 img {
  float: left;
}
.pc_pay53 {
  background: #fdebe9;
  width: 125px;
  height: 54px;
  margin-top: 18px;
  border-radius: 4px;
  margin-right: 20px;
  float: left;
  cursor: pointer;
  font-size: 20px;
  color: #ff6159;
  text-align: center;
  line-height: 54px;
  font-weight: 700;
  position: relative;
}
.pc_pay53 div {
  width: 24px;
  height: 24px;
  position: absolute;
  top: -12px;
  right: -12px;
  border: 2px solid #f56c6c;
  border-radius: 50%;
  font-size: 22px;
  font-weight: 700;
  z-index: 1;
  background: #f56c6c;
  color: #fff;
  cursor: pointer;
  line-height: 18px;
}
.pc_pay54 {
  float: left;
  font-size: 16px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  width: 100px;
  height: 40px;
  text-align: center;
  line-height: 38px;
  background: #f5f8fb;
  margin-right: 20px;
  cursor: pointer;
  position: relative;
  margin-top: 18px;
}
.pc_pay54 img {
  position: absolute;
  right: -1px;
  bottom: -1px;
}
.pc_pay55 {
  border-color: @themeBackGroundColor;
  color: @themeBackGroundColor;
  background: none;
}
.pc_pay57 {
  background: #fff;
}
.pc_pay58 {
  width: 125px;
  height: 54px;
  border-radius: 4px;
  border: 1px solid #ff6159;
  color: #ff6159;
  margin-top: 18px;
  float: left;
  cursor: pointer;
  position: relative;
  text-align: center;
  line-height: 52px;
  font-size: 18px;
  font-weight: 700;
}
.pc_pay59 {
  background: @themeButtonBackGroundColor;
  width: 125px;
  height: 54px;
  color: @themeBackGroundColor;
  border-radius: 4px;
  margin-top: 18px;
  margin-right: 20px;
  float: left;
  cursor: pointer;
  position: relative;
  text-align: center;
  line-height: 54px;
  font-size: 20px;
  font-weight: 700;
}
.pc_pay59 div {
  width: 24px;
  height: 24px;
  position: absolute;
  top: -12px;
  right: -12px;
  border: 2px solid #f56c6c;
  border-radius: 50%;
  font-size: 22px;
  font-weight: 700;
  z-index: 1;
  background: #f56c6c;
  color: #fff;
  cursor: pointer;
  line-height: 18px;
}
.pc_pay6 {
  background: @themeButtonBackGroundColor;
  width: 90px;
  height: 36px;
  border-radius: 18px;
  margin-left: 10px;
  text-align: center;
  line-height: 36px;
  background-size: 100% 100%;
  color: @themeBackGroundColor;
  font-weight: bold;
}
.pc_pay61 {
  width: 100%;
  margin: 0 auto;
  margin-top: 10px;
  max-width: 570px;
}
.pc_pay61 table {
  border-collapse: inherit;
  border-spacing: 10px;
  width: 100%;
}
.pc_pay61 td {
  border: 1px solid #e3e6eb;
  width: 25%;
  background: #fff;
  height: 70px;
  border-radius: 4px;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  color: @themeFontColor;
}
.pc_pay61 div {
  width: calc(100% - 20px);
  max-width: 570px;
  margin: 0 auto;
  border-radius: 4px;
  height: 70px;
  text-align: center;
  line-height: 70px;
  font-size: 24px;
  color: #fff;
  background: @themeBackGroundColor;
  padding-left: 10px;
  padding-right: 10px;
  font-weight: 70;
  letter-spacing: 8px;
}
.pc_pay62 {
  width: 580px;
  margin: 0 auto;
  margin-top: 150px;
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  z-index: 800;
}
.pc_pay63 {
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #5a7788;
  margin-left: 24px;
  margin-top: 26px;
}
.pc_pay64 {
  width: 540px;
  height: 67px;
  line-height: 67px;
  background: #f5f8fb;
  font-size: 24px;
  position: relative;
  margin-top: 26px;
  margin-left: 20px;
  font-weight: bold;
}
.pc_pay65 {
  width: 894px;
  height: 607px;
  background: #f5f8fb;
  overflow: hidden;
  color: #688392;
  z-index: 10;
  margin: 0 auto;
  margin-top: 57px;
  border-radius: 10px;
  position: relative;
}
.pc_pay66 {
  height: 131px;
  overflow: hidden;
  width: 637px;
  background-color: #ffffff;
  border-radius: 10px;
  border: solid 1px #e3e6eb;
  margin-top: 41px;
  margin-left: 41px;
  display: flex;
  font-weight: bold;
}
.pc_pay67 {
  width: 152px;
  height: 120px;
  background-color: #ffffff;
  border-radius: 8px;
  border: solid 1px #e3e6eb;
  text-align: center;
  line-height: 118px;
  font-size: 30px;
  color: @themeFontColor;
  margin-top: 41px;
  margin-left: 22px;
  cursor: pointer;
}
.pc_pay69 {
  height: 21px;
  line-height: 21px;
  font-size: 20px;
  font-weight: bold;
  color: @themeFontColor;
  margin-top: 36px;
}
.pc_pay7 {
  border: 1px solid #fff;
  background: @themeBackGroundColor;
  border-radius: 50%;
  overflow: hidden;
  width: 30px;
  height: 30px;
  margin-top: 9px;
  margin-left: 30px;
  float: left;
}
.pc_pay7 img {
  width: 16px;
  height: 20px;
  margin-top: 4px;
  margin-left: 6px;
}
.pc_pay71 {
  float: left;
  line-height: 48px;
  margin-left: 15px;
  font-size: 16px;
}
.pc_pay73 {
  width: 100px;
  height: 50px;
  margin-top: 32px;
  margin-left: 480px;
  color: #fff;
  background: @themeBackGroundColor;
  border: 1px solid @themeBackGroundColor;
  border-radius: 5px;
  text-align: center;
  line-height: 48px;
  font-size: 20px;
  float: left;
}
.pc_pay74 {
  margin-top: 52px;
  width: 100%;
  overflow: hidden;
}
.pc_pay75 {
  width: 125px;
  height: 180px;
  float: left;
  margin-left: 98px;
  cursor: pointer;
}
.pc_pay75 img {
  width: 100%;
  height: 100%;
}
.pc_pay76 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 5;
}
.pc_pay77 {
  width: 503px;
  height: 295px;
  background: #fff;
  margin: 0 auto;
  margin-top: 80px;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_pay77_1 {
  width: 520px;
  height: 620px;
  background: #fff;
  margin: 0 auto;
  margin-top: 80px;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_pay78 {
  line-height: 25px;
  font-size: 25px;
  width: 100%;
  color: #688392;
  text-align: center;
  margin-top: 28px;
}
.pc_pay79 {
  width: 350px;
  height: 90px;
  border: 1px solid #ccc;
  line-height: 90px;
  background: #f9f9f9;
  color: @themeFontColor;
  border-radius: 10px;
  margin: 0 auto;
  margin-top: 38px;
  overflow: hidden;
}
.pc_pay79 input {
  width: 230px;
  font-size: 54px;
  margin-left: 40px;
  height: 70px;
  margin-top: 10px;
  text-align: center;
  background: #f9f9f9;
  border: none;
  float: left;
}
.pc_pay79 div {
  font-size: 26px;
  line-height: 90px;
  color: #999;
  width: 70px;
  text-align: right;
  float: left;
}
.pc_pay8 {
  text-align: center;
  width: 100%;
  font-size: 18px;
  color: #c6c6c6;
  line-height: 18px;
  margin-top: 14px;
}
.pc_pay81 {
  overflow: hidden;
  padding: 28px 25px 0px;
  display: flex;
  justify-content: space-between;
}
.pc_pay82 {
  width: 160px;
  height: 51px;
  background: @themeFontColor;
  color: #fff;
  font-size: 20px;
  float: left;
  text-align: center;
  line-height: 51px;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay83 {
  width: 160px;
  height: 51px;
  border: 1px solid @themeBackGroundColor;
  font-size: 20px;
  float: left;
  text-align: center;
  line-height: 51px;
  border-radius: 4px;
  background: @themeBackGroundColor;
  color: #fff;
  cursor: pointer;
}
.pc_pay84 {
  position: absolute;
  width: 110px;
  height: 110px;
  border-radius: 50%;
  background: #ff8484;
  top: -50px;
  left: -50px;
}
/* .pc_pay84:hover{
  background: #FF8484;cursor: pointer;
} */
.pc_pay84 img {
  width: 35px;
  height: 35px;
  margin-top: 56px;
  margin-left: 56px;
}
.pc_pay86 {
  height: calc(100% - 123px);
  overflow: scroll;
}
.pc_pay86 div {
  border-bottom: 1px solid #fff;
}
.pc_pay88 {
  height: 46px;
  line-height: 46px;
  border-radius: 5px;
  color: #fff;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
}
.pc_pay89 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 20px;
}
.pc_pay9 {
  width: 150px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  background: @themeFontColor;
  color: #fff;
  margin-left: 60px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay91 {
  width: 150px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  /* background: linear-gradient(90deg, #f1d3af, #cfa26b); */
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay92 {
  background: #fce2d6;
}
.pc_pay93 {
  background: #fff;
}
.pc_pay94 {
  background-image: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  color: #fff;
  font-size: 30px;
}
.pc_pay95 {
  width: 160px;
  height: 160px;
  margin-top: 40px;
  float: left;
}
.pc_pay96 {
  width: 305px;
  margin-left: 10px;
  float: left;
  margin-top: 40px;
  color: @themeFontColor;
  overflow: hidden;
}
.pc_pay97 {
  overflow: hidden;
  width: 100%;
  margin-top: 25px;
  font-size: 16px;
}
.pc_pay98 {
  float: left;
  width: 95px;
  text-align: right;
  line-height: 40px;
}
.pc_pay98 span {
  color: #ff8484;
  margin-right: 5px;
}
.pc_pay99 {
  float: left;
  width: calc(100% - 135px);
  margin-left: 10px;
}
.pc_pay100 {
  float: left;
  width: 160px;
  height: 160px;
  margin-top: 26px;
  margin-left: 0px;
  border: 1px solid #f6f6f6;
  overflow: hidden;
}
.pc_pay100 img {
  width: 100%;
  height: 100%;
}
.pc_pay101 {
  width: 614px;
  height: 321px;
  border-radius: 10px;
  background: #f5f8fb;
  margin: 0 auto;
  margin-top: 141px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_pay102 {
  width: 517px;
  height: 131px;
  background-color: #ffffff;
  border-radius: 10px;
  border: solid 1px #e3e6eb;
  display: flex;
  color: @themeFontColor;
  margin: 0 auto;
  margin-top: 52px;
  font-weight: bold;
}
.pc_pay103 {
  width: 33%;
  margin-top: 24px;
  text-align: center;
}
.pc_pay104 {
  font-size: 18px;
  line-height: 18px;
}
.pc_pay105 {
  margin-top: 22px;
  font-size: 35px;
}
.pc_pay105 span {
  font-size: 20px;
}
.pc_pay106 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 5;
}
.pc_pay107 {
  width: 470px;
  height: 600px;
  background: #fff;
  margin: 0 auto;
  margin-top: 50px;
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_pay108 {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(80, 80, 80, 0.55);
  top: 0;
  left: 0;
  z-index: 200;
}
.pc_pay109 {
  line-height: 55px;
  height: 55px;
  font-size: 25px;
  width: 100%;
  color: #688392;
  text-align: center;
  border-bottom: 1px solid #dcdfe6;
}
.pc_pay110 {
  overflow: hidden;
  margin: 12px;
}
.pc_pay110 input {
  width: 218px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.pc_pay111 {
  color: #b6bfe7;
  font-size: 18px;
  width: 100px;
  font-weight: bold;
  float: left;
  line-height: 18px;
  margin-top: 12px;
}
.pc_pay112 {
  width: 218px;
  border: none;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.pc_pay113 {
  width: 218px;
  border: none;
  height: 36px;
  line-height: 36px;
  background: #f5f8fb;
  float: right;
  font-weight: bold;
  color: @themeFontColor;
}
.pc_pay114 {
  width: 220px;
  height: 50px;
  background-image: linear-gradient(90deg, #aaaaaa, #a7a2a2);
  text-align: center;
  line-height: 50px;
  color: #fff;
  font-size: 25px;
  font-weight: bold;
  float: left;
  margin-left: 10px;
  margin-top: 12px;
  cursor: pointer;
  border-radius: 15px;
}
.pc_pay115 {
  width: 220px;
  height: 50px;
  background-image: linear-gradient(90deg, #f1d3af, #cfa26b);
  text-align: center;
  line-height: 50px;
  color: #fff;
  font-size: 25px;
  font-weight: bold;
  float: right;
  margin-left: 10px;
  margin-top: 12px;
  cursor: pointer;
  border-radius: 15px;
}
.pc_pay116 {
  width: 786px;
  position: absolute;
  bottom: 35px;
}
.pc_pay117 {
  width: 70px;
  height: 36px;
  background-image: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  border-radius: 5px;
  float: left;
  color: #fff;
  margin-left: 8px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
}
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(../image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.weight-head {
  font-size: 16px;
  cursor: pointer;
  margin-left: 16px;
  color: @themeFontColor;
}
.weight-head img {
  float: left;
  margin-top: 10px;
}
.weight-head div {
  float: left;
  line-height: 50px;
  margin-left: 2px;
}
.pc_pay204 {
  padding-top: 25px;
  padding-left: 25px;
  width: 598px;
  margin: 0 auto;
  overflow: hidden;
}
.pc_pay204 label {
  width: 100px;
  font-size: 18px;
  color: #688392;
}
.pc_pay204 input {
  height: 40px;
  width: 200px;
  border-radius: 4px;
  border: 1px solid #ccc;
  text-indent: 20px;
  font-size: 18px;
}
.pc_pay204 span {
  width: 100px;
  padding-left: 20px;
  color: #688392;
  cursor: pointer;
  font-size: 18px;
}
.pc_pay118 {
  width: 100%;
  margin-top: -40px;
  z-index: 2100;
  overflow: hidden;
  background: #fff;
}
.pc_pay119 {
  width: 560px;
  margin: 0 auto;
  margin-top: 0px;
  color: #969696;
  text-align: center;
  font-size: 22px;
  line-height: 22px;
}
.pc_pay119 img {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  margin-top: 0px;
}
.weight_amount_span {
  width: 200px;
  margin-left: -20px;
  padding-left: 0px;
}
.weight_amount_span > span {
  padding-right: 15px;
  /* margin-left: -10px; */
}
.btn-confirm {
  text-align: center;
  width: calc(100% - 50px);
  margin-left: 25px;
  height: 60px;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 30px;
  cursor: pointer;
  color: #fff;
  font-size: 20px;
  line-height: 60px;
  background: @themeBackGroundColor;
}
.pc_pay120 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}
.pc_pay121 {
  position: relative;
  z-index: 800;
  height: 300px;
  margin: 0 auto;
  margin-top: 240px;
  background: #fff;
  width: 450px;
  overflow: hidden;
  border-radius: 5px;
}
.pc_pay122 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 400;
}
.pc_pay123 {
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: @themeFontColor;
  margin-top: 40px;
}
.pc_pay124 {
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: @themeFontColor;
  margin-top: 25px;
  font-weight: 100;
}
.pc_pay125 {
  position: relative;
  z-index: 800;
  height: 250px;
  margin: 0 auto;
  margin-top: 240px;
  background: #fff;
  width: 450px;
  overflow: hidden;
  border-radius: 5px;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_pay126 {
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: @themeFontColor;
  margin-top: 30px;
}
.pc_pay127 {
  width: 250px;
  height: 40px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 5px;
  line-height: 40px;
  margin-left: 100px;
  margin-top: 30px;
  text-indent: 10px;
  font-size: 16px;
  color: @text;
}
.pc_pay128 {
  margin-left: 40px;
  color: #bac9d2;
}
.pc_pay129 {
  position: absolute;
  height: 67px;
  line-height: 67px;
  top: 0;
  font-size: 24px;
  right: 40px;
  color: @themeFontColor;
}
.pc_pay130 {
  width: 550px;
  margin-left: 22px;
  margin-top: 20px;
  overflow: hidden;
}
.pc_pay130 div {
  border: 1px solid #e3e6eb;
  width: 128px;
  height: 80px;
  line-height: 80px;
  font-size: 28px;
  color: @themeFontColor;
  text-align: center;
  float: left;
  margin-right: 8px;
  font-weight: bold;
  margin-bottom: 8px;
  cursor: pointer;
}
.pc_pay131 {
  position: absolute;
  top: 0;
  left: 0;
}
.pc_pay132 {
  overflow: hidden;
  width: 100%;
  height: 100%;
  font-size: 16px;
}
.pc_pay133 {
  width: 100%;
  height: calc(100% - 340px);
  overflow: scroll;
  margin-top: 10px;
  color: @themeFontColor;
}
.pc_pay134 {
  height: 16px;
  border-bottom: 1px solid #e3e6eb;
  width: 91.5%;
  margin-left: 5%;
}
.pc_pay135 {
  margin-left: 15px;
  border-radius: 3px;
  line-height: 35px;
  text-align: center;
}
.pc_pay136 {
  float: right;
  margin-right: 20px;
  position: relative;
}
.pc_pay137 {
  position: absolute;
  right: -10px;
  top: -16px;
  cursor: pointer;
}
.pc_pay138 {
  position: absolute;
  background: #f56c6c;
  width: 35px;
  height: 22px;
  border-radius: 11px;
  overflow: hidden;
  text-align: center;
  line-height: 22px;
  font-size: 16px;
  color: #fff;
  top: -10px;
  right: -10px;
}
.pc_pay139 {
  position: relative;
  top: -50px;
  left: 80%;
  background-image: linear-gradient(180deg, #f1d3af, #cfa26b);
  width: 150px;
  height: 40px;
  border-radius: 28px;
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}
.pc_pay139 img {
  float: left;
  margin-left: 15px;
  margin-top: 8px;
}
.pc_pay139 div {
  float: left;
  margin-left: 10px;
  margin-top: 4px;
  font-weight: bold;
}
/* .pc_pay140 {
  line-height: 60px;
} */
.pc_pay140 div {
  float: left;
  width: 100px;
  text-align: left;
}
.pc_pay140 img {
  width: 30px;
  float: left;
  margin-top: 10px;
}
.pc_pay141 {
  overflow: hidden;
}
.pc_pay142 {
  float: left;
  margin-left: 20px;
  color: @themeFontColor;
  font-size: 18px;
  width: calc(100% - 180px);
}
.pc_pay143 {
  line-height: 28px;
  font-weight: bold;
  margin-top: 36px;
  font-size: 20px;
  overflow: hidden;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  text-overflow: ellipsis;
}
.pc_pay144 {
  line-height: 18px;
  margin-top: 30px;
  overflow: hidden;
}
.pc_pay145 {
  margin-top: 15px;
  overflow: hidden;
  height: 38px;
}
.pc_pay146 {
  width: 102px;
  float: left;
  line-height: 38px;
}
.pc_pay147 {
  float: left;
  color: #ff773b;
  font-size: 22px;
  line-height: 38px;
  font-weight: bold;
}
.pc_pay147 img {
  width: 20px;
  height: 20px;
  margin-left: 20px;
  cursor: pointer;
  margin-top: -5px;
}
.pc_pay148 {
  float: left;
  color: @themeFontColor;
  font-size: 18px;
  overflow: hidden;
}
.pc_pay148 input {
  float: left;
  width: 88px;
  height: 36px;
  border-radius: 4px;
  border: 1px solid #ccc;
  text-indent: 12px;
}
#pa3 {
  width: 73px;
}
.pc_pay149 {
  float: left;
  margin-left: 10px;
  width: 70px;
  height: 36px;
  text-align: center;
  line-height: 34px;
  background: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  color: #fff;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay149_skew {
  float: left;
  position: relative;
  margin-left: 10px;
  padding-left: 20px;
  text-align: left;
  width: 130px;
  height: 36px;
  line-height: 36px;
  background: linear-gradient(90deg, #d8b774 0%, #deb071 100%);
  color: #fff;
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay149_skew_un {
  width: 100px;
  height: 36px;
  position: absolute;
  padding-left: 6px;
  top: 0;
  left: 74px !important;
  right: 0;
  bottom: 0;
  background: #eeedf0;
  color: #89898a;
  transform: skewX(-45deg);
  span {
    display: block;
    text-align: left;
    transform: skewX(45deg);
  }
}
.pc_pay150 {
  line-height: 20px;
  margin-top: 5px;
  overflow: hidden;
  height: 38px;
}
.pc_pay151 {
  width: 20px;
  text-align: center;
  float: left;
  line-height: 37px;
}
.pc_pay152 {
  float: left;
  color: @themeFontColor;
  font-size: 20px;
  width: 165px;
}
.pc_pay153 {
  width: 165px;
  border: 1px solid #e6e5eb;
  line-height: 32px;
  height: 36px;
  overflow: hidden;
  font-size: 30px;
  font-weight: 100;
  border-radius: 4px;
}
.pc_pay154 {
  width: 36px;
  border-right: 1px solid #e6e5eb;
  background: #eeedf0;
  text-align: center;
  font-weight: bold;
  float: left;
  height: 36px;
  cursor: pointer;
  font-size: 12px;
  line-height: 34px;
  color: #89898a;
}
.pc_pay155 {
  width: calc(100% - 72px);
  height: 36px;
  text-align: center;
  float: left;
  font-size: 18px;
  font-weight: normal;
  line-height: 35px;
}
.pc_pay156 {
  width: 36px;
  border-left: 1px solid #e6e5eb;
  background: #eeedf0;
  text-align: center;
  float: left;
  height: 36px;
  cursor: pointer;
  color: #89898a;
}
.pc_pay157 {
  overflow: hidden;
  width: 100%;
  // min-width: 410px;
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
}
/deep/ .pc_pay157 .el-input__inner {
  height: 48px;
  line-height: 48px;
}
.pc_pay158 {
  height: calc(100% - 202px);
  margin-top: 30px;
  overflow: scroll;
  width: calc(100% - 111px);
}
.pc_goods_empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #969696;
  font-size: 22px;
  img {
    width: 190px;
  }
  .center {
    text-align: center;
  }
}
.pc_pay160 {
  width: 70px;
  height: 70px;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 20px;
  font-weight: bold;
  line-height: 34px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 11px;
  overflow: hidden;
  letter-spacing: 6px;
}
.stock-style {
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.6);
  position: absolute;
  left: 10px;
  top: 60px;
  width: 70px;
  height: 20px;
  text-align: center;
  font-weight: 500;
  font-size: 13px;
}
.pc_pay161 {
  width: 70px;
  height: 70px;
  margin-top: 10px;
  margin-left: 10px;
  font-size: 25px;
  font-weight: bold;
  line-height: 70px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
}
.pc_pay162 {
  float: left;
}
.pc_pay162 p {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  height: 72px;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-weight: 500;
  margin-top: 11px;
  margin-bottom: 0;
  line-height: 24px;
}
.pc_pay163 {
  float: right;
  font-weight: bold;
  font-size: 16px;
  margin-right: 10px;
}
.pc_pay164 {
  width: calc(100% - 20px);
  max-width: 550px;
  margin: 0 auto;
  margin-top: 50px;
  background: #f5f8fb;
  overflow: hidden;
  border-radius: 5px;
  height: 70px;
  line-height: 70px;
  font-size: 24px;
  text-indent: 30px;
  font-weight: bold;
}
.pc_pay165 {
  float: left;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_pay166 {
  overflow: hidden;
  width: 475px;
  margin: 0 auto;
}
.pc_pay167 {
  width: 100%;
  position: relative;
  margin-top: 20px;
  font-size: 24px;
  overflow: hidden;
}
.pc_pay168 {
  float: right;
  margin-right: 40px;
  background: linear-gradient(90deg, #69d6fe, #16aaff);
}
.pc_pay169 {
  width: 100%;
  text-align: center;
  margin-top: 20px;
}
.pc_pay169 span {
  color: #ff8484;
}
.pc_pay170 {
  text-align: center;
  width: 120px;
  height: 60px;
  background-image: linear-gradient(180deg, #ffbc90 0%, #ff6658 100%);
  border-radius: 5px;
  margin-left: 12px;
  float: left;
  cursor: pointer;
  position: relative;
  margin-top: 16px;
  line-height: 60px;
}
.pc_pay171 {
  font-size: 20px;
  font-weight: bold;
  line-height: 80px;
}
.pc_pay172 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: left;
  text-align: right;
  margin-left: 5px;
}
.pc_pay173 {
  float: left;
  width: 80px;
  height: 32px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  line-height: 30px;
  color: @themeBackGroundColor;
  margin-top: 11px;
  margin-left: 9px;
}
.pc_pay174 {
  position: relative;
  z-index: 800;
  margin-top: 60px;
  margin-left: 10px;
  width: 450px;
  height: calc(100% - 190px);
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
}
.pc_pay175 {
  height: calc(100% - 120px);
  overflow: scroll;
  color: #666666;
  font-size: 16px;
}
.pc_pay176 {
  height: 60px;
  background: @themeButtonBackGroundColor;
  font-size: 18px;
  border-radius: 6px;
  overflow: hidden;
}
.pc_pay176 img {
  width: 18px;
  height: 19px;
  float: left;
  margin-top: 20px;
  margin-left: 25px;
}
.pc_pay176 div {
  float: left;
  margin-left: 10px;
  font-size: 14px;
  color: @themeBackGroundColor;
  line-height: 62px;
}
.pc_pay177 {
  width: 105px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: left;
  margin-left: 25px;
}
.pc_pay178 {
  float: left;
  margin-left: 25px;
  width: 104px;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  height: 22px;
}
.pc_pay179 {
  float: left;
  width: 300px;
  text-align: right;
  line-height: 18px;
  overflow: hidden;
  color: @themeBackGroundColor;
  font-weight: bold;
}
.pc_pay180 {
  float: left;
  width: 100px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  margin-left: 100px;
  border: 1px solid @themeBackGroundColor;
  color: @themeBackGroundColor;
  margin-top: 16px;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay180_1 {
  float: left;
  width: 100px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  margin-left: 15px;
  border: 1px solid @themeBackGroundColor;
  color: @themeBackGroundColor;
  margin-top: 16px;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay181 {
  float: left;
  width: 100px;
  height: 40px;
  line-height: 38px;
  text-align: center;
  border: 1px solid @themeBackGroundColor;
  color: #fff;
  margin-top: 16px;
  margin-left: 15px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  cursor: pointer;
}
.pc_pay182 {
  height: 50px;
  border-top: 1px solid #e3e6eb;
  font-size: 16px;
  color: @themeBackGroundColor;
  font-weight: bold;
  position: absolute;
  bottom: 0;
  width: 100%;
}
.pc_pay182 img {
  float: left;
  width: 18px;
  height: 19px;
  margin-top: 15px;
  margin-left: 180px;
}
.pc_pay182 div {
  line-height: 50px;
  margin-left: 10px;
  float: left;
}
.pc_pay183 {
  width: 540px;
  height: 67px;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 67px;
  border-radius: 4px;
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin-left: 20px;
  margin-top: 12px;
}
.pc_pay184 {
  background-image: linear-gradient(90deg, #f1d3af, #cfa26b);
}
.pc_pay185 {
  width: 70px;
  height: 70px;
  font-size: 21px;
  font-weight: bold;
  line-height: 33px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 11px;
  border-radius: 20px;
  overflow: hidden;
  letter-spacing: 6px;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_pay186 {
  width: 70px;
  height: 70px;
  font-size: 21px;
  font-weight: bold;
  line-height: 70px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
  border-radius: 20px;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_pay187 {
  background: @themeFontColor;
}
.pc_pay188 {
  width: calc(100% - 143px);
  margin-left: 13px;
  float: left;
  height: 44px;
  border-radius: 4px;
  border: 1px solid #e5e8ec;
  background: #fff;
  line-height: 1;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
/* .pc_pay188 div {
  float: left;width: 20px;height: 20px;background: #bbb;color: #FFF;border-radius: 50%;
  text-align: center;line-height: 20px;margin-left: 60px;margin-top: 11px;cursor: pointer;
} */
.pc_pay189 {
  width: 786px;
  height: 511px;
  overflow: scroll;
  margin: 0 auto;
  border: 1px solid #e5e8ec;
  margin-top: 18px;
}
/* padding-left: 40px;padding-right: 40px; */
.pc_pay190 {
  width: 100px;
  text-align: left;
  overflow: hidden;
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  text-overflow: ellipsis;
  margin-left: 24px;
}
.pc_pay191 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: @themeBackGroundColor;
  text-align: center;
  color: #ffffff;
  line-height: 20px;
  font-size: 16px;
  float: right;
  margin-top: 10px;
  cursor: pointer;
}
.pc_pay194 {
  width: 100px;
  height: 40px;
  border-radius: 20px;
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
  background: @themeBackGroundColor;
}
.pc_pay194 img {
  float: left;
  margin-top: 8px;
  margin-left: 20px;
}
.pc_pay194 div {
  float: left;
  margin-top: 7px;
  font-size: 16px;
  color: #fff;
  margin-left: 8px;
}
.pc_pay195 {
  width: 746px;
  height: 436px;
  margin: 0 auto;
  border-radius: 6px;
  margin-top: 169px;
  background: #fff;
  position: relative;
  z-index: 800;
  color: @themeFontColor;
  overflow: hidden;
}
.pc_pay196 {
  line-height: 18px;
  font-size: 18px;
  margin-top: 20px;
  margin-left: 20px;
  font-weight: bold;
}
/deep/ .pc_pay195 .el-input__inner {
  height: 44px;
  line-height: 44px;
  color: @themeFontColor;
  font-size: 16px;
}
.pc_pay197 {
  margin-top: 5px;
  overflow: hidden;
  margin-left: 425px;
}
.pc_pay197 div {
  width: 140px;
  height: 50px;
  line-height: 50px;
  border-radius: 4px;
  background: @themeFontColor;
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  float: left;
  cursor: pointer;
}
.pc_pay198 {
  width: 190px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  float: left;
}
.pc_pay199 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
}
.pc_pay200 {
  width: 20px;
  height: 20px;
  margin-left: 60px;
  margin-top: 10px;
  cursor: pointer;
}
.pc_pay201 {
  width: 130px;
  height: 44px;
  border-radius: 2px;
  color: #fff;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 44px;
  font-size: 18px;
  font-weight: bold;
  float: left;
}
.pc_pay202 {
  margin-left: 5px;
  color: #ff6159;
  font-weight: bold;
  font-size: 26px;
}
.pc_pay203 {
  width: 333px;
  height: 60px;
  background: #f5f8fb;
  margin: 0 auto;
  margin-top: 38px;
}
.pc_pay203 input {
  margin-left: 12px;
  margin-top: 11px;
  font-size: 24px;
  font-weight: bold;
  color: @themeFontColor;
  border: none;
  background: #f5f8fb;
}
.pc_pay_selection {
  width: 96px;
  height: 100%;
  float: right;
  color: @themeFontColor;
  display: grid;
  margin-right: -6px;
  font-size: 16px;
  font-weight: bold;
}
.pc_pay_selection1 {
  height: 70px;
  border-bottom: 1px solid #e3e6eb;
  line-height: 66px;
  text-align: center;
  cursor: pointer;
}
.pc_pay_selection2 {
  background: @themeBackGroundColor;
  color: #ffffff;
}
.pc_pay_selection3 {
  height: 100%;
  overflow: auto;
  border-left: 1px solid #e3e6eb;
}
.pc_pay_price {
  display: block;
  float: none !important;
  margin-top: 5px;
  color: #b6bfe7;
  text-decoration: line-through;
}
.pc_weight {
  width: 608px;
  height: 452px;
  border-radius: 6px;
  padding: 24px;
  background: #fff;
  margin: 0 auto;
  top: 18%;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.pc_weight1 {
  font-size: 18px;
  color: #567485;
  font-weight: bold;
}
.pc_weight2 {
  margin-top: 16px;
}
.pc_weight3 {
  width: 176px;
  height: 216px;
  border: 1px solid #e3e6eb;
  background: #f9fbfb;
  display: inline-block;
  font-size: 16px;
  color: #567485;
  text-align: center;
  cursor: pointer;
}
.pc_weight3_img {
  margin: 0 auto;
  width: 120px;
  height: 120px;
  margin-top: 16px;
  margin-bottom: 10px;
}
.pc_weight4 {
  height: 44px;
  text-align: center;
  width: 310px;
  background: @themeBackGroundColor;
  border-radius: 6px;
  color: #ffffff;
  font-size: 16px;
  line-height: 42px;
  margin: 0 auto;
}
.pc_weight5 {
  background: #f5f8fb;
  width: 24px;
  height: 24px;
  border: 2px solid #b1c3cd;
  border-radius: 4px;
  margin: 0 auto;
}
.pc_weight6 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: @themeBackGroundColor;
  text-align: center;
  color: #ffffff;
  line-height: 20px;
  font-size: 16px;
  float: right;
  margin-top: 5px;
  cursor: pointer;
  margin-right: 8px;
}
.pc_weight6::after {
  border-bottom-color: rgba(0, 0, 0, 0.8) !important;
}
.pc_popover_margin {
  margin: 0 5px !important;
}
.pc_pay192_padding {
  padding: 5px !important;
}
.pc_weight7 {
  margin-left: 10px;
  font-size: 16px;
  display: inline-block;
  width: 104px;
  height: 30px;
  background: #f5f7fa;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  line-height: 28px;
}
.pc_weight7 span {
  padding-left: 0;
}
.numberClick {
  cursor: pointer;
}
.pc_pay_clear_div {
  height: 52px;
  width: 100%;
  border-bottom: 1px solid #e3e6eb;
  padding: 0 12px;
  font-weight: 600;
  color: #567485;
}
.pc_pay_clear_div div {
  cursor: pointer;
  margin-top: 14px;
}
.shortCutTag {
  display: inline-block;
  margin-top: 0px !important;
  background-color: @input_backgroundColor;
  border-radius: 4px;
  font-size: 13px;
  height: 30px;
  width: 60px;
  text-align: center;
  line-height: 30px;
  color: @text;
}
.lastOrder_detail_title {
  font-size: 14px;
  color: #567485;
  width: 478px;
  height: 126px;
  border-radius: 6px;
  font-weight: bold;
  background: #f4f4f4;
  margin: 10px 12px;
  // font-family: AlibabaPuHuiTi, sans-serif;
}
.lastOrder_detail_title span {
  font-family: AlibabaPuHuiTi, sans-serif;
  color: #aaa;
}
.lastOrder_detail_data {
  font-size: 14px;
  color: #567485;
  font-weight: 600;
  font-family: AlibabaPuHuiTi, sans-serif;
}
.lastOrder_detail_data span {
  font-weight: 600;
}
.lastOrder_detail_goods {
  width: 482px;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 10px 16px;
  padding-top: 10px;
  border-top: 1px solid #e3e6eb;
  font-family: AlibabaPuHuiTi, sans-serif;
  color: #567485;
  font-weight: 600;
}
.last-order-detail-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  &--refund {
    width: 120px;
    height: 42px;
    border: 1px solid @themeBackGroundColor;;
    border-radius: 8px;
    color: @themeBackGroundColor;;
    text-align: center;
    font-weight: 700;
    line-height: 38px;
    font-size: 16px;
    margin-top: 30px;
    margin-right: 10px;
    cursor: pointer;
  }
  &--print {
    width: 140px;
    height: 42px;
    background: @themeBackGroundColor;;
    border-radius: 8px;
    color: #fff;
    text-align: center;
    font-weight: 700;
    line-height: 38px;
    font-size: 16px;
    margin-top: 30px;
    cursor: pointer;
  }
}
.fullDisabled, .partDisabled {
  color: #e5dcc6;
  border: 1px solid #e5dcc6;
}
.pc_pay205 {
  height: 68px;
  float: left;
  line-height: 68px;
  color: @themeFontColor;
  font-size: 18px;
  font-weight: bold;
}
.pc_pay206 {
  float: right;
  font-size: 45px;
  color: @themeFontColor;
  font-weight: 300;
  cursor: pointer;
}
.pc_pay207 {
  width: 598px;
  height: 84px;
  background: #f5f8fb;
  margin: 0 auto;
  margin-top: 14px;
  border-radius: 6px;
}
.pc_pay208 {
  line-height: 50px;
  height: 54px;
  width: calc(100% - 24px);
  margin-left: 12px;
  font-size: 20px;
  color: @themeFontColor;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pc_pay209 {
  line-height: 16px;
  color: @themeFontColor;
  font-size: 16px;
}
.pc_pay209 span {
  margin-left: 12px;
}
.pc_pay210 {
  overflow: hidden;
  font-size: 16px;
  color: @themeFontColor;
  margin-top: 17px;
  margin-bottom: 15px;
}
.pc_pay211 {
  float: left;
  width: 54px;
  line-height: 31px;
  margin-left: 30px;
}
.pc_pay212 {
  width: 544px;
  overflow: hidden;
  float: left;
}
.pc_pay212 div {
  height: 31px;
  line-height: 29px;
  padding-left: 18px;
  padding-right: 18px;
  min-width: 70px;
  text-align: center;
  color: #b6bfe7;
  border: 1px solid #b6bfe7;
  float: left;
  margin-left: 12px;
  border-radius: 16px;
}
.pc_pay213 {
  padding-left: 15px;
  padding-right: 15px;
  color: @themeFontColor;
  border-radius: 20px;
  float: left;
  font-size: 16px;
  height: 31px;
  line-height: 31px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  background: #f5f5f5;
}
.pc_pay213:hover {
  color: #fff;
  background: #d6b676;
}
.vip_row_active {
  background: @fadeThemeColor;
}
.vip_row_grey {
  background: #FAFBFC;
}
.pc_relivate {
  position: relative;
  height: 70px;
}
.prod_name_repeat {
  font-size: 14px;
  color: #FF6159;
  position: absolute;
  top: 45px;
  left: 106px;
}
.pc_search_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 111px);
  margin-top: 20px;
  padding-left: 10px;
}
.pc_scales_container {
  background: #F5F8FB;
  border: 1px solid #E1E1E1;
  max-width: 262px;
  min-width: 200px;
  height: 56px;
  border-radius: 8px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-grow: 1;
  margin-right: 20px;
}
.weight_led_number {
  color: @themeFontColor;
  font-weight: bold;
  font-size: 16px;
  min-width: 111px;
  .led {
    font-family: UnidreamLED, sans-serif;
    font-size: 36px;
    margin-right: 3px;
    font-weight: normal;
  }
}
.scales_connect_status {
  display: flex;
  align-items: center;
  min-width: 89px;
  color: @themeFontColor;
  font-size: 16px;
}
.success_point {
  background: #47B881;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 3px;
}
.error_point {
  background: rgb(255, 97, 89);
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 3px;
}
.error_span {
  color: rgb(255, 97, 89);
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 10px;
}
@media only screen and (max-width: 1280px) {
  .pc_pay46 {
    width: 190px;
  }
  .pc_pay46 p {
    margin-left: 7px;
    width: 98px;
  }
  .pc_search_container {
    width: 100%;
    padding-right: 10px;
  }
}
.pc_pay174_top {
  position: absolute;top: 0;width: 100%;height: 70px;background: #fff;border-bottom: 1px solid #E5E5E5;
}