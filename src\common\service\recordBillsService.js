import dao from '../dao/dao';

const recordBillsService = {
  /**
   * 查询挂单数量
   * @param {*} data {}
   * @param {*} onSuccess 成功回调
   * @param {*} onFail 失败回调
   */
  getCount: function (onSuccess, onFail) {
    dao.exec(sqlApi.record_bills_getCount, onSuccess, onFail);
  },

  /**
   * 查询
   * @param {*} data {'id':1}
   * @param {*} onSuccess 成功回调
   * @param {*} onFail 失败回调
   */
  select: function (data, onSuccess, onFail) {
    data.column = '';
    if (typeof (data) === 'object' && data.hasOwnProperty('id')) {
      dao.exec(sqlApi.getRecordBillsById.format(data), onSuccess, onFail);
    } else {
      let tpl = '';
      if (!demo.isNullOrTrimEmpty(data.keyword)) {
        data.column = `,substr(info,LENGTH(info) - INSTR(REVERSE(info), '":"kramer"')+2,INSTR(SUBSTR(info, LENGTH(info) - INSTR(REVERSE(info), '":"kramer"')+2), '"')-1) as remark,
        substr(info, INSTR(info, '"member_mobile":"') + LENGTH('"member_mobile":"'),INSTR(substr(info, INSTR(info, '"member_mobile":"') + LENGTH('"member_mobile":"')), '"') - 1) AS memberMobile_value`;
        tpl += `and (remark like '%${data.keyword}%' or memberMobile_value like '%${data.keyword}%')`;
      }
      if (!demo.isNullOrTrimEmpty(data.limit) && !demo.isNullOrTrimEmpty(data.offset)) {
        tpl += `limit ${data.limit} offset ${data.offset}`;
      }
      data.page = tpl;
      dao.exec(sqlApi.record_bills_select.format(data), onSuccess, onFail);
    }
  },

  /**
   * 新增
   * @param {*} data {'info': '{\'aaa\':\'aaa\'}', 'uid':123}
   * @param {*} onSuccess 成功回调
   * @param {*} onFail 失败回调
   */
  insert: function (data, onSuccess, onFail) {
    data.fingerprint = md5(commonService.guid());
    dao.exec(sqlApi.record_bills_insert.format(demo.sqlConversion(data)), onSuccess, onFail);
  },

  /**
   * 修改
   * @param {*} data {"id":1, "info": "{\"aaa\":\"aaa\"}"}
   * @param {*} onSuccess 成功回调
   * @param {*} onFail 失败回调
   */
  update: function (data, onSuccess, onFail) {
    dao.exec(sqlApi.record_bills_update.format(demo.sqlConversion(data)), onSuccess, onFail);
  },

  /**
   * 删除
   * @param {*} data {}:truncate；{"id":1}:delete
   * @param {*} onSuccess 成功回调
   * @param {*} onFail 失败回调
   */
  delete: function (data, onSuccess, onFail) {
    if (data.id === undefined) {
      // truncate
      dao.transaction(sqlApi.record_bills_truncate, onSuccess, onFail);
    } else {
      // delete
      dao.exec(sqlApi.record_bills_delete.format(data), onSuccess, onFail);
    }
  },

  /**
   * 更新 record_bills.info.left_goods_list.[id, cur_stock]
   * recordBillsService.updateRecordBillsGoodIdAndStock(1, res => {console.log(res)});
   * @param {*} id 挂单id
   * @param {*} onSuccess
   * @param {*} onFail
   */
  updateRecordBillsGoodIdAndStock: function(id, onSuccess, onFail) {
    var params = {'id': id};
    dao.exec(sqlApi.getRecordBillsById.format(params), res => {
      var recordBills = demo.t2json(res);
      if (recordBills.length === 0) {
        onSuccess();
      } else {
        var bill = recordBills[0];
        var info = demo.t2json(bill.info);
        var goodList = info.left_goods_list;
        var array = _.map(goodList, 'fingerprint');
        array = [...new Set(array)];

        if (array.length === 0) {
          onSuccess();
        } else {
          params = '\'' + array.join('\',\'') + '\'';
          dao.exec(sqlApi.getGoodsByFingerprints.format(params), res1 => {
            var goods = demo.t2json(res1);
            var goodIds = {};
            var goodStocks = {};
            goods.forEach(good => {
              goodIds[good.fingerprint] = good.id;
              goodStocks[good.fingerprint] = good.curStock;
            });

            goodList.forEach(good => {
              if (!demo.isNullOrTrimEmpty(goodIds[good.fingerprint])) {
                good.id = goodIds[good.fingerprint];
              }
              if (!demo.isNullOrTrimEmpty(goodStocks[good.fingerprint])) {
                good.curStock = goodStocks[good.fingerprint];
              }
            });

            info.left_goods_list = goodList;
            bill.info = '\'' + JSON.stringify(info).replace(/'/g, '‘').replace(/;/g, '；') + '\'';
            params = {'id': id};
            var sql = sqlApi.updateRecordBillsInfo.format(bill) + sqlApi.getRecordBillsById.format(params);
            dao.exec(sql, onSuccess, onFail);
          }, onFail);
        }
      }
    }, onFail);
  }
};

window.recordBillsService = recordBillsService;
export default recordBillsService;
