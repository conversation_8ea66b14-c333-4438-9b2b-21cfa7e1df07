<style lang="less">
#app {
  height: 100%;
  width: 100%;
  cursor: default;
  // 禁用屏幕缩放
  // content-zooming:none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.el-table__empty-text {
  font-size: 16px;
}
input::-webkit-input-placeholder {
  color: #B2C3CD;
}
.el-popover {
  min-width: 104px;
  padding: 0;
}
.el-popper {
  margin-top: 4px!important;
}
.pc_pay192 {
  color: rgba(255, 255, 255, 0.9)!important;
  background: rgba(0, 0, 0, 0.8)!important;
  margin-top: 12px!important;
  border: 0px;
}
.popper_self {
  background: rgba(0, 0, 0, 1)!important;
  padding: 7px;
  min-width: 1px;
  margin-bottom: 0px;
}
.el-popper[x-placement^=top] .popper__arrow {
  // bottom: 0px;
  border-top-color: rgba(0, 0, 0, 1);
}
.el-popper[x-placement^=top] .popper__arrow::after {
  border-top-color: rgba(0, 0, 0, 1);
}
.el-popper .popper__arrow {
  border-width: 0px;
}
.popper__arrow::after {
    // top: 0px!important;
}
.pc_pay193::after {
    border-bottom-color:  rgba(0, 0, 0, 0.8)!important;
}
.el-tooltip__popper[x-placement^=top] .popper__arrow::after {
  border-top-color: rgba(0, 0, 0, 0);
}
.el-tooltip__popper[x-placement^=top] {
  margin-bottom: 0;
}
.search_input_box {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 22px;
  background: #FFFFFF;
}
.search_input_box > input {
  width: 235px;
  height: 18px;
  line-height: 18px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 12px;
  border: none;
  color: #B1C3CD;
  background: #FFFFFF;
  float: left;
}
.search_input_delete {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.el-table td,
.el-table th {
  padding: 10px 0;
}
.el-checkbox__inner {
  width: 24px;
  height: 24px;
  border: none;
  background-image: url(./image/zgzn-pos/pc_goods_checkbox1.png);
}
.el-checkbox__inner::after {
  border: none;
}
.el-checkbox {
  margin-bottom: 0.2rem;
  margin-top: 0.3rem;
}
.el-table th > .cell {
  padding-left: 14px;
}
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(./image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.el-table td,
.el-table th.is-leaf {
  border: none;
}
.el-table thead {
  color: @themeFontColor;
}
.el-select-dropdown__wrap {
  max-height: 295px;
}
.el-picker-panel__shortcut {
  outline: none !important;
}
.el-picker-panel__shortcut:hover {
  color: @themeBackGroundColor !important;
}
.pay_setting .el-input__inner{
  color: @themeFontColor;
}
.pay_setting > .el-dialog__wrapper > .el-dialog {
  margin-top: 5vh !important;
}
.el-checkbox__inner.is-focus {
  border-color: @themeBackGroundColor;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  height: 24px;
  top: 0;
  transform: none;
  -webkit-transform: none;
  background-image: url(./image/zgzn-pos/pc_goods_checkbox3.png) !important;
}
.el-input-number__decrease:hover, .el-input-number__increase:hover {
  color: @themeFontColor;
}
.el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled),
 .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
  border-color: @themeBackGroundColor;
}
.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
.el-date-table td.today span {
  color: @themeBackGroundColor;
}
.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}
.el-date-table td.end-date span, .el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}
.el-popup-parent--hidden {
  padding-right: 0 !important;
}
.el-date-table td.current:not(.disabled) span {
  color: #FFF;
  background-color: @themeBackGroundColor !important;
}
.el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
.el-cascader .el-input .el-input__inner:focus, .el-cascader .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
.el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
  color: @themeBackGroundColor;
}
.pc_fip5 {
  padding: 8px 14px !important;background: rgba(0,0,0,.8) !important;
  color: rgba(255, 255, 255, 0.9) !important;margin-top: 14px !important;line-height: 24px;
}
.pc_fip5 .popper__arrow::after {
  top: -21px !important;border-right-color: rgba(0,0,0,.8) !important;left: 0px !important;
}
.pc_jinjian_code_img .popper__arrow::after {
  border-top-color: #fff !important;
}
.datePicker_month {
  /deep/.el-date-picker__header  {
    display: none;
  }
  /deep/.el-month-table {
    .cell {
      font-size: 15px;
    }
    td .cell:hover {
      color: #CFA26B;
    }
    a {
      text-decoration: none;
    }
    .current{
      a {
        color: #CFA26B;
      }
    }
    td.current:not(.disabled) .cell {
      color: #FFF;
    }
    td.current:not(.disabled) div {
      background: #CFA26B;
      border-radius: 6px;
    }
    td.today .cell{
      color: #CFA26B;
      font-weight: normal;
    }
  }
}
.datePicker_day {
  /deep/.el-date-picker__header {
    display: none;
  }
}
.pop_padding {
  padding: 5px !important;
}
@font-face {
  font-family: 'DinMedium';
  // src: url('../st//font/DinMedium.ttf');
  src: url('./font/DinMedium.ttf');
}
@font-face {
  font-family: 'SourceHanSansCN';
  src: url('./font/SourceHanSansCN.ttf');
}
@font-face {
  font-family: 'HarmonyOSSansSC';
  src: url('./font/HarmonyOSSansSC.ttf');
}
.el-input__inner {
  height: 44px;line-height: 44px;font-size: 16px;color: @themeFontColor;
}
.el-icon-circle-close:before {
  content: "\e79d";
}
.el-input .el-input__clear {
  font-size: 19px;
}
.el-input__clear {
  width: 35px;
}
.el-input__icon {
  line-height: 44px;
}
.tr_gray {
  background: #fafafa;
}
.tr_white {
  background: #fff;
}
.tr_disabled {
  background: #F2F2F2 !important;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: @themeBackGroundColor;
}
.el-table th>.cell.highlight {
  color: @themeBackGroundColor;
}
.pc_spec_8 {
  .el-table--border:after,
  .el-table--group:after,
  .el-table:before {
    background-color: #E5E8EC;
  }
  .el-table__header-wrapper .is-leaf {
    border-right: 1px solid #E5E8EC;
  }
  .el-table--border,
  .el-table--group {
    border-color: #E5E8EC;
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 1px solid #E5E8EC;
  }
  .el-table--border th,
  .el-table--border th.gutter:last-of-type {
    border-bottom: 1px solid #E5E8EC
  }
  .el-table--border td,
  .el-table--border th {
    border-right: 1px solid #E5E8EC;
  }
}
.el-cascader .el-input .el-input__inner:focus, .el-cascader
.el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
.el-cascader-node.in-active-path, .el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: @themeBackGroundColor;
}
@font-face {
  font-family: 'UnidreamLED';
  src: url('./font/UnidreamLED.ttf');
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: @themeBackGroundColor !important;
  background: @themeBackGroundColor !important;
}
.el-radio__input {
  margin-top: 9px;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: 0px;
}
.el-slider__button {
  border: 2px solid @themeBackGroundColor !important;
}
.el-slider__bar {
  background-color: @themeBackGroundColor !important;
}
.pc_fixed_dropdown {
  /deep/.el-scrollbar {
    height: 295px !important;
  }
  /deep/.el-select-dropdown__list {
    padding: 0px;
  }
}
.date-pick-panel {
  .el-date-picker__header {
    span:nth-child(3) { /*第三个标签是span的标签，把它隐藏*/
      display: none;
    }
    button:nth-child(1) {
      display: none;
    }
    button:nth-child(5) {
      display: none;
    }
  }
}
.short-cut-popover {
  max-height: 70vh;
  border-radius: 8px;
  overflow: auto;
}
.short-cut-popover::-webkit-scrollbar{
  width: 0;
}
.el-loading-spinner .path {
  stroke: @themeBackGroundColor !important;
}
.el-loading-text {
  color: #FFFFFF !important;
}
.homeTimeSelect {
  .el-select-dropdown__item {
    width: 80px;
    margin: 0 auto;
    border-radius: 4px;
    padding-left: 8px;
  }
  .selected {
    background-color: #F5F0E3;
  }
  .popper__arrow {
    display: none;
  }
}
</style>
<template>
  <div id="app">
    <router-view />
  </div>
</template>
<script>
export default {
  name: 'App'
};
</script>
