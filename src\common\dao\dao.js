const dao = {
  inTransaction: 0,
  allTransaction: [],

  asyncExec: function(sql) {
    return new Promise((resolve, reject) => {
      this.exec(sql, resolve, reject);
    });
  },

  exec: function (sql, onSuccess, onFail) {
    this.allTransaction.push({ type: 'exec', sql, onSuccess, onFail });
    if (this.inTransaction === 0) {
      this.inTransaction = 1;
      this.loopTransaction();
    }
  },

  asyncTransaction: function(sql) {
    return new Promise((resolve, reject) => {
      this.transaction(sql, resolve, reject);
    });
  },

  transaction: function (sql, onSuccess, onFail) {
    this.allTransaction.push({ type: 'transaction', sql, onSuccess, onFail });
    if (this.inTransaction === 0) {
      this.inTransaction = 1;
      this.loopTransaction();
    }
  },

  asyncImport: function(params) {
    return new Promise((resolve, reject) => {
      this.import(params, resolve, reject);
    });
  },

  import: function(params, onSuccess, onFail) {
    this.allTransaction.push({ type: 'import', params, onSuccess, onFail });
    if (this.inTransaction === 0) {
      this.inTransaction = 1;
      this.loopTransaction();
    }
  },

  loopTransaction: function () {
    if (this.allTransaction.length === 0) {
      this.inTransaction = 0;
      return;
    }
    const msg = `loopTransaction = {0}`.format(JSON.stringify(this.allTransaction));
    console.log(msg);
    CefSharp.PostMessage(msg);
    const loop = this.allTransaction.shift();
    if (loop.type === 'exec') {
      console.log(loop.sql);
      this.doExec(loop.sql, loop.onSuccess, loop.onFail);
    } else if (loop.type === 'transaction') {
      console.log(loop.sql);
      this.doTransaction(loop.sql, loop.onSuccess, loop.onFail);
    } else {
      console.log(JSON.stringify(loop.params));
      this.doImport(loop.params, loop.onSuccess, loop.onFail);
    }
  },

  doExec: function (sql, onSuccess, onFail) {
    pos.db.query(sql, res => {
      setTimeout(() => onSuccess(demo.tableToHump(demo.t2json(res))), 0);
      this.loopTransaction();
    }, err => {
      setTimeout(() => onFail(err), 0);
      this.loopTransaction();
    });
  },

  doTransaction: function (sql, onSuccess, onFail) {
    pos.db.executeSqlBatch(sql, res => {
      setTimeout(() => onSuccess(res), 0);
      this.loopTransaction();
    }, err => {
      setTimeout(() => onFail(err), 0);
      this.loopTransaction();
    });
  },

  doImport: function (params, onSuccess, onFail) {
    sqlitePlugin.executeImport(params.directoryName, params.fileName, params.tables, res => {
      setTimeout(() => onSuccess(res), 0);
      this.loopTransaction();
    }, err => {
      setTimeout(() => onFail(err), 0);
      this.loopTransaction();
    });
  }
};

window.dao = dao;
export default dao;
