<template>
  <div>
    <div class="change_shifts_container">
      <div style="margin:0 auto;margin-top:18px;flex:1">
        <div
          class="title"
          v-if="changeNumber === ''"
        >管理员</div>
        <div
          class="title"
          v-else
        >收银员：{{changeName}}&emsp;&emsp;&emsp;&emsp;工号：{{changeNumber}}</div>
        <div class="time">
          <span>班次：</span>
          <span class="time_date">{{getDateTime('date', loginTime)}}</span>
          <span class="time_t">{{getDateTime('time', loginTime)}}</span>
          <span class="time_t">-</span>
          <span class="time_date">{{getDateTime('date', endDate)}}</span>
          <span class="time_t">{{getDateTime('time', endDate)}}</span>
        </div>
        <div class="item_container">
          <div
            class="item"
            v-for="(item, index) in detail_arr"
            :key="index"
          >
            <div class="prefix">{{item.prefix}}</div>
            <div class="name">{{item.title}}</div>
            <div class="desc">{{item.total}}</div>
          </div>
        </div>
        <div
          @click="clickItem()"
          class="clickItem"
        >查看详情
          <img
            src="@/image/zgzn-pos/icon_arrow_right.png"
            style="width:24px;height:24px;margin-bottom: 1px"
            alt=""
          >
        </div>

        <div class="change_shifts_bottom">
          <div
            class="btn printBtn"
            @click="printChange"
          >打印</div>
          <div>
            <div
              class="btn"
              id="reportBut"
              @click="openShowTable()"
            >商品销售报表</div>
            <div
              class="btn"
              :class="isRepair ? 'logoutBut': 'forClass'"
              @click="changeShiftsLogout"
            >{{changeFrom === 'home' && isRepair ? '交接班并登出' : '交接班补班'}}</div>
          </div>
        </div>
      </div>
    </div>
    <!--报表详情-->
    <el-dialog
      :visible.sync="showDetailDialog"
      :show-close='false'
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      width="586px"
      top='68px'
    >
      <div class="dialog_header">
        <div class="header_title">交接班详情</div>
        <div
          class="icon_close"
          @click="closeShowDetail()"
        >×</div>
      </div>
      <div class="dialog_content">
        <div class="content">
          <div
            class="dialog_detail_item main-item"
            v-for="(item,index) in detail_arr"
            :key="index"
            v-show="item.title && index < 4"
          >
            <div class="prefix-item">
              <div class="prefix item-prefix">{{item.prefix}}</div>
              <div class="itemTitle">{{item.title}}</div>
              <div class="itemTotal">{{item.total}}</div>
            </div>
            <div v-if="item.desc && item.desc.length > 0" class="item-inner">
              <div
                class="inner_item"
                v-for="(innerItem,innerIndex) in getDesc(item.desc)"
                :key="innerIndex"
              >
                <div v-if="index === 0 || index === 3  || index === 2" class="item-width">
                  <div class="itemTitle item-right">{{innerItem.title}}</div>
                  <div class="itemTotal item-right">{{innerItem.total}}</div>
                  <div
                    class="dialog_detail_item"
                    v-for="(innerItem1,innerIndex1) in getDesc(innerItem.desc)"
                    :key="innerIndex1"
                  >
                    <div class="itemTitle dialog-title">{{innerItem1.title}}</div>
                    <div class="itemTotal total-title">
                      {{Number(innerItem1.money).toFixed(2)+'元'+'（'+innerItem1.count+'笔）'}}
                    </div>
                  </div>
                </div>
                <div v-if="index === 1" class="item-width">
                  <div class="itemTitle item-right">{{innerItem.payType}}</div>
                  <div class="itemTotal item-right">{{Number(innerItem.money).toFixed(2)+'元'+'（'+innerItem.count+'笔）'}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="btn_container"
          style="padding-bottom: 20px"
        >
          <div
            class="btn"
            id="cancelBtn"
            @click="closeShowDetail()"
          >取消</div>
          <div
            class="btn"
            id="confirmBtn"
            @click="printChange()"
          >打印</div>
        </div>
      </div>
    </el-dialog>
    <!--商品销售报表table-->
    <el-dialog
      :visible.sync="showTableDialog"
      :show-close='false'
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      width="680px"
      top='32px'
    >
      <div class="dialog_header">
        <div class="header_title">商品销售报表</div>
        <div
          class="icon_close"
          @click="closeShowTable()"
        >×</div>
      </div>
      <div class="table_container">
        <el-table
          :data="table_arr.slice((curren_page-1)*limit,curren_page*limit)"
          stripe
          style="width: 100%"
          :height="445"
        >
          <el-table-column
            show-overflow-tooltip
            prop="name"
            label="商品名称"
            width="240"
          >
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="category"
            label="商品分类"
            align="center"
            width="210"
          >
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="count"
            label="数量"
            align="center"
          >
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="money"
            label="小计"
            align="center"
          >
          </el-table-column>
        </el-table>
        <div class="total_container">
          <div>总数：<span>{{countTotal}}</span>&nbsp;&nbsp;总计：<span>￥{{moneyTotal}}</span></div>
          <el-pagination
            layout="prev, pager, next"
            :total="this.table_arr.length"
            @current-change="handleCurrentChange"
            :current-page="curren_page"
            :page-size="limit"
          ></el-pagination>
        </div>
      </div>
      <div class="btn_container">
        <div
          class="btn"
          id="cancelBtn"
          @click="closeShowTable()"
        >取消</div>
        <div
          class="btn"
          id="confirmBtn"
          @click="printSales()"
        >打印</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--交接班登出dialog-->
    <el-dialog
      :visible.sync="showTipsDialogChangeShifts"
      :show-close='false'
      :close-on-click-modal='false'
      :close-on-press-escape='false'
      width="450px"
      top='214px'
    >
      <div class="tips_dialog">
        <div class="title">提示</div>
        <div class="content">{{msg1}}<br />{{msg2}}</div>
        <div class="dialog_btn_container">
          <div
            class="btn"
            style="background:#567485"
            @click="showTipsDialogChangeShifts = false"
          >取消</div>
          <div
            class="btn"
            id="confirm"
            @click="logout"
          >{{changeFrom === 'home' ? '登出' : '确定'}}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import logList from '@/config/logList';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog, Table, TableColumn, Pagination } from 'element-ui';
export default {
  components: {
    [Dialog.name]: Dialog,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination
  },
  data () {
    return {
      isNet: 0,
      countTotal: 0,
      moneyTotal: 0,
      showTipsDialogChangeShifts: false,
      detail_arr: [
        {
          prefix: '销',
          title: '总销售',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '会',
          title: '会员充值',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '现',
          title: '应收现金',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '支',
          title: '支付统计',
          desc: [],
          total: '（0.00元 0笔）'
        }
      ],
      printData: {
        start: '',
        end: '',
        user: '',
        title: '',
        countTotal: '',
        moneyTotal: ''
      },
      table_arr: [],
      curren_page: 1,
      limit: 8,
      userInfo: this.$store.state.show.loginInfo,
      gologining: false,
      money_arr: [],
      msg1: '交接班后将返回登录页，',
      msg2: '您确定要交接班并登出吗？',
      printting: false,
      salePrintting: false
    };
  },
  created () {
    if (this.changeFrom === 'home') {
      this.msg1 = '交接班后将返回登录页，';
      this.msg2 = '您确定要交接班并登出吗？';
    } else {
      this.msg1 = '补班后数据将不可更改，';
      this.msg2 = ' 确定要补班吗？';
    }
    console.log('===登录信息', this.userInfo);
    if ($setting.username) {
      this.userInfo.phone = $setting.username;
    }
    this.setPrintData();
    if (this.isHasEndDate) {
      let remark = this.changeShiftRemark.split('&@')[0];
      if (remark !== '') {
        this.detail_arr = demo.t2json(remark);
      }
    } else {
      if (this.isNewChange) {
        this.getChangeShiftsData();
      } else {
        this.getNextById();
      }
    }
    demo.actionLog(logList.changeShiftsView); // 交接班页面显示log
    this.getSalesTable();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getNextById() { // 新增交接班时交接班结束时间
      const params = {
        shiftHistoryId: this.changeId
      }
      shiftHistoryService.getNextById(params).then(res => {
        if (res) {
          this.SET_SHOW({endDate: res});
        }
        this.getChangeShiftsData();
      }).catch(() => {
        this.getChangeShiftsData();
      });
    },
    getDateTime(type, time) {
      const dateTime = time.replace(/-/g, '.');
      if (type === 'time') {
        return dateTime.substring(11);
      }
      return dateTime.substring(0, 11);
    },
    getDesc(arr) {
      if (arr && arr.length > 0) {
        return arr.filter(d => d.count);
      } else {
        return [];
      }
    },
    getChangeShiftsData() { // 获取指定时间段内的详细数据
      let params = {
        start: this.loginTime,
        end: this.endDate.replace(/\./g, '-'),
        uid: this.uid
      }
      shiftHistoryService.statistics(params).then(res => {
        this.detail_arr = res;
        console.log(res, '--------------------res00');
      }).catch(err => {
        console.log('warning', err);
      });
    },
    openShowTable() {
      this.SET_SHOW({ showTableDialog: true });
    },
    closeShowTable() {
      this.SET_SHOW({ showTableDialog: false });
    },
    closeShowDetail() {
      this.SET_SHOW({ showDetailDialog: false });
    },
    goChangeShifts() {
      this.SET_SHOW({ isChangeShifts: false });
      this.SET_SHOW({ isChangeShiftsRecord: true });
      this.SET_SHOW({ changeNumber: this.$store.state.show.loginInfo.employeeNumber });
    },
    /**
     * 获取本地销售数据报表数据
     */
    getSalesTable () {
      let sub_data = {
        'uid': this.uid,
        'start': this.loginTime,
        'end': this.endDate.replace(/\./g, '-')
      };
      // 查本销售数据报表数据
      changeShiftsService.changeProductSalesReport(sub_data, res => {
        const that = this;
        specsService.specStringToArray(res).forEach(function (item) {
          that.table_arr.push({
            ...item,
            money: Number(item.money).toFixed(2)
          });
        });
        that.countTotal = Number(_.sum(that.table_arr.map(g => +g.count)).toFixed(3));
        that.moneyTotal = _.sum(that.table_arr.map((g) => Number(g.money))).toFixed(2);
      });
    },
    /**
     * 打印数据封装
     */
    setPrintData () {
      this.printData.start = this.loginTime;
      this.printData.end = this.endDate.replace(/\./g, '-');
      this.printData.user = this.changeNumber ? this.changeNumber : '管理员';
      this.printData.title = '交接班报表';
    },
    /**
     * 点击项
     */
    clickItem () {
      this.SET_SHOW({ showDetailDialog: true });
    },
    /**
     * 点击页码
     */
    handleCurrentChange (currenPage) {
      this.curren_page = currenPage;
    },
    /**
     * 登出
     */
    logout () {
      if (this.gologining === true) {
        return;
      }
      if (this.changeFrom === 'home') {
        this.gologining = true;
        setTimeout(() => {
          this.gologining = false;
        }, 3000);
      }
      this.showTipsDialogChangeShifts = false;
      this.updateLocalChangeShiftsRecord();
    },
    /**
     * 更新本地交接班记录
     */
    updateLocalChangeShiftsRecord () {
      let param = {
        action: 1,
        shiftHistoryId: this.changeId,
        uid: this.uid,
        end: this.endDate.replace(/\./g, '-'),
        start: this.loginTime.replace(/\./g, '-')
      };
      if (this.changeFrom === 'home') {
        param.action = 2;
      } else {
        if (!this.isNewChange) {
          param.action = 3;
        }
      }
      console.log('交接班插入本地参数', param);
      // 插入交接班记录到本地表,插入成功后云同步退出到登录页
      shiftHistoryService.save(param).then(() => {
        settingService.put({ key: settingService.key.usertoken, value: '' }); // 清空本地表usertoken
        this.syncLogout();
      }).catch(() => {
        settingService.put({ key: settingService.key.usertoken, value: '' }); // 清空本地表usertoken
        this.syncLogout();
      });
    },
    /**
     * 进入页面云同步
     */
    sync () {
      // 如果正在执行云同步，则不继续执行
      if (this.isSyncing) {
        return;
      }
      this.SET_SHOW({loadingMsg: '正在云同步，请稍后……', isSyncingLogin: true});
      syncService.clearDataInfo(() => {
        this.SET_SHOW({isSyncingLogin: false});
        this.setDefaultData();
      }, () => {
        this.SET_SHOW({isSyncingLogin: false});
        this.setDefaultData();
      });
    },
    /**
     * 退出云同步
     */
    syncLogout () {
      // 如果正在执行云同步，则不继续执行
      if (+this.isSyncing > 0) {
        return;
      }
      if (!pos.network.isConnected() || this.$store.state.show.network === false) {
        this.SET_SHOW({load_message: '正在退出，请稍后……'});
      } else {
        this.SET_SHOW({load_message: '正在云同步，请稍后……'});
      }
      this.SET_SHOW({isSyncingLogin: true});
      syncService.clearDataInfo(() => {
        if (this.changeFrom === 'home') {
          // this.SET_SHOW({isSyncingLogin: false});
          this.toExit();
        } else {
          this.SET_SHOW({ isSyncingLogin: false });
          this.SET_SHOW({ isChangeShifts: false });
          this.SET_SHOW({ isChangeShiftsRecord: true });
        }
      }, () => {
        this.SET_SHOW({isSyncingLogin: false});
        this.toExit();
      });
    },
    getKexian(v, t) {
      external.customerdisplay({
        displaydata: v,
        displaytype: t,
        port: this.kexianValue,
        baudrate: '2400',
        databits: '8',
        parity: '0',
        stopBits: '1'
      });
    },
    toExit () {
      if (this.showKexian) {
        this.getKexian('', 0);
      }
      if (this.showTipsDialog) {
        external.closeMainForm();
        return;
      }
      this.$log.info('reload');
      external.reloadForm();
    },
    /**
     * 打印交接班数据
     */
    printChange () {
      var that = this;
      if (that.printting) {
        return;
      }
      that.printting = true;
      setTimeout(() => {
        that.printting = false;
      }, 3000);
      that.printData.title = '交接班报表';
      if (that.settingSmallPrinter !== undefined && that.settingSmallPrinter !== null && that.settingSmallPrinter.trim() !== '') {
        pos.printer.printChangeShiftsTimes(that.detail_arr, that.printData, 'change');
      } else {
        demo.msg('warning', that.$msg.not_setting_small_printer);
      }
      demo.actionLog(logList.changeShiftsDetailPrint);
    },
    /**
     * 打印销售报表
     */
    printSales () {
      var that = this;
      if (that.salePrintting) {
        return;
      }
      that.salePrintting = true;
      setTimeout(() => {
        that.salePrintting = false;
      }, 3000);
      that.printData.countTotal = that.countTotal;
      that.printData.moneyTotal = that.moneyTotal;
      that.printData.title = '商品销售报表';
      if (that.settingSmallPrinter !== undefined && that.settingSmallPrinter !== null && that.settingSmallPrinter.trim() !== '') {
        pos.printer.printChangeShiftsTimes(that.table_arr, that.printData, 'sales');
      } else {
        demo.msg('warning', that.$msg.not_setting_small_printer);
      }
      demo.actionLog(logList.changeShiftsReportPrint);
    },
    // 点击交接班并登出
    changeShiftsLogout () {
      if (this.isRepair) {
        this.showTipsDialogChangeShifts = true;
        demo.actionLog(logList.changeShiftsLogout);
      }
    }
  },
  beforeDestroy () {
    this.SET_SHOW({ isHasEndDate: false });
    this.closeShowDetail();
    this.closeShowTable();
  },
  computed: mapState({
    showTipsDialog: state => state.show.showTipsDialog,
    isSyncing: state => state.show.isSyncing,
    isHome: state => state.show.isHome,
    isGoods: state => state.show.isGoods,
    isLogin: state => state.show.isLogin,
    isHeader: state => state.show.isHeader,
    fingerprint: state => state.show.fingerprint,
    settingSmallPrinter: state => state.show.setting_small_printer,
    loginTime: state => state.show.loginTime,
    endDate: state => state.show.endDate,
    changeFrom: state => state.show.changeFrom,
    uid: state => state.show.changeUid,
    changeFingerprint: state => state.show.changeFingerprint,
    isRepair: state => state.show.isRepair,
    changeNumber: state => state.show.changeNumber,
    changeName: state => state.show.changeName,
    delReLogin: state => state.show.delReLogin,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    kexianValue: state => state.show.kexianValue,
    showKexian: state => state.show.showKexian,
    isHasEndDate: state => state.show.isHasEndDate,
    changeId: state => state.show.changeId,
    isNewChange: state => state.show.isNewChange,
    changeShiftRemark: state => state.show.changeShiftRemark,
    showDetailDialog: state => state.show.showDetailDialog,
    showTableDialog: state => state.show.showTableDialog
  })
};
</script>

<style lang='less' scoped>
#font {
  color: @fontColor;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-table th {
  font-size: 16px;
}
@fontColor:@themeFontColor;
.change_shifts_container{
  width: 100%;
  height: 100%;
  background: url(../../image/zgzn-pos/pc_login_bg.png) no-repeat;
  position: relative;
  top: 0;
  left: 0;
  overflow: hidden;
  background-size: cover;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title{
    font-size: 20px;
    color: @fontColor;
    font-weight: bold;
    text-align: center;
    padding-bottom: 8px;
    border-bottom: 1px solid #E3E6EB;
  }
  .time{
    font-size: 16px;
    color: rgba(177, 195, 205, 100);
    text-align: center;
    &_date {
      color: @fontColor;
      font-weight: 400;
      font-size: 24px;
    }
    &_t {
      font-weight: 700;
      color: @fontColor;
      font-size: 24px;
    }
  }
  .item_container{
    margin-top: 30px;
    .item{
      min-width: 540px;
      height: 60px;
      display: flex;
      align-items: center;
      padding-top: 20px;
      padding-bottom: 20px;
      padding-left: 20px;
      padding-right: 10px;
      background-color: rgba(249, 251, 251, 100);
      border: 1px solid rgba(227, 230, 235, 100);
      margin-top: 10px;
      .prefix{
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        border-radius: 2px;
        background-color: @themeBackGroundColor;
        color: white;
        font-size: 18px;
        font-weight: 700;
      }
      .name{
        color: @fontColor;
        font-size: 18px;
        font-weight: 700;
        margin-left: 20px;
      }
      .desc{
        text-align: right;
        flex: 1;
        color: @fontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .change_shifts_bottom{
    margin-top: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .cb_text{
      color: @fontColor;
      font-size: 18px;
      font-weight: bold;
      margin-left: 10px;
      cursor: pointer;
    }
    .btn{
      width: 170px;
      height: 54px;
      line-height: 42px;
      text-align: center;
      border-radius: 4px;
      font-size: 20px;
      font-weight: 700;
      cursor: pointer;
    }
    .printBtn {
      border: 1px solid @themeBackGroundColor;
      color:@themeBackGroundColor;
      width: 120px
    }
    #reportBut {
      border: 1px solid @themeBackGroundColor;
      color:@themeBackGroundColor;
    }
    .logoutBut {
      background:@themeBackGroundColor;
      color:white;
      margin-left:20px;
    }
    .forClass {
      background:#F9FBFB;
      color:@text;
      margin-left:20px;
    }
  }
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 20px;
  .header_title{
    color: @fontColor;
    font-size: 18px;
    font-weight: bold;
    height: 60px;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.item-width {
  width: 100%;
}
.item-right {
  margin-right: 40px;
}
.total-title {
  margin-left: 34px;
  margin-right: 40px;
  font-weight: 400;
}
.dialog-title {
  margin-left: 34px;
  margin-right: 40px;
}
.itemTitle {
  font-size:16px;
  color: #567485;
  font-weight: 400;
  padding:5px 0;
  display: inline-block;
}
.itemTotal {
  font-size:16px;
  color: #567485;
  font-weight: 700;
  padding:5px 0;
  float: right;
}
.main-item {
    background: #F9FBFB;
    width: 93%;
    padding: 12px 24px;
    margin-bottom: 16px;
    border: 1px solid #E3E6EB;
  }
.dialog_content {
  .content {
    margin-top:17px;
    margin-left:34px;
    overflow: auto;
    height:350px;
    .dialog_detail_item {
      .item-inner {
        .inner_item {
          display: flex;
          align-items: center;
          margin-left: 34px;
        }
      }
      .prefix-item {
        border-bottom: 1px solid #ECECEC;
        padding-bottom: 10px;
        .prefix {
          width: 30px;
          height: 30px;
          text-align: center;
          line-height: 30px;
          border-radius: 2px;
          background-color: #BDA169;
          color: white;
          font-size: 18px;
          font-weight: bold;
        }
        .item-prefix {
          display: inline-block;
          margin-right: 10px;
        }
      }
  }
  }
}
.line_hidden{
  visibility: hidden;
}
.confirm_btn {
  margin-top: 26px;
  width: 120px;
  height: 44px;
  background: @themeBackGroundColor;
  color: white;
  border-radius: 4px;
  text-align: center;
  line-height: 44px;
  margin-right: 20px;
  margin-bottom: 20px;
  cursor: pointer;
}
.cancel_btn {
  margin-top: 26px;
  width: 120px;
  height: 44px;
  color: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  line-height: 44px;
  margin-right: 20px;
  margin-bottom: 20px;
  cursor: pointer;
  border:1px solid @themeBackGroundColor;
}
.table_container{
  border: 1px solid #E3E6EB;
  border-radius: 2px;
  margin: 20px;
  .total_container{
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    height: 50px;
    padding: 0 10px;
    span{
      color: @themeBackGroundColor;
    }
  }
}
.btn_container{
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  .btn{
    width: 120px;
    height: 44px;
    text-align: center;
    line-height: 34px;
    margin-right: 20px;
    border-radius: 4px;
    font-size: 16px;
  }
  #cancelBtn{
    border:1px solid @themeBackGroundColor;
    color:@themeBackGroundColor;
  }
  #confirmBtn{
    background:@themeBackGroundColor;
    color:white;
  }
}
/deep/ .el-table--enable-row-transition .el-table__body td {
  height: 50px;
  font-size: 16px;
}
.tips_dialog{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
  .title{
    color: @fontColor;
    font-size: 24px;
    text-align: center;
    margin-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @fontColor;
    font-size: 24px;
    margin-top: 28px;
    text-align: center;
  }
  .dialog_btn_container{
    display: flex;
    justify-content: center;
    margin-top: 50px;
    padding-bottom: 30px;
    .btn{
      width: 140px;
      height: 50px;
      line-height: 38px;
      color: white;
      font-weight: 700;
      font-size: 20px;
    }
    #confirm {
      background:@themeBackGroundColor;
      margin-left:30px
    }
  }
}
.clickItem {
  font-size: 16px;
  color:@themeBackGroundColor;
  text-align: center;
  margin-top: 30px;
  cursor: pointer
}
</style>
