// 页面
@themeBackGroundColor: #b4995a;
@themeFontColor: #567485;
@themeFontColorLight: #8FA8BA;
@themeBackGroundColorLight: rgba(189, 161, 105, 0.5);
//按钮字体颜色
@butFontColor: #ffffff;
//页面头部按钮背景色
@themeButtonBackGroundColor: #f4efe5;
@linearBackgroundColor: linear-gradient(0deg, #b4995a, #b4995a);
//分类列表背景色
@areaColor: #567485;
//分类列表子菜单背景色
@inventoryColor: #d2e4ee;
//分类列表文字颜色
@areaFontColor: #567485;
// login 发送验证码
@verification: #cccccc;
//pc_add_inventory_list 库存盘点/进货界面头部区域背景色
@unStockBackGround: #fff1f0;
@stockBackGround: #f8f4ec;
// home
@fontWeight: normal;
@fontStyle: normal;
//首页销售数据列表_文字颜色
@homeColor: #567485;
//首页左侧区域背景色
@left_backgroundColor: #243642;
//首页左侧按钮背景色
@leftBut_backgroundColor: #394a55;
//首页左侧按钮文字颜色
@leftBut_fontColor: #d1b889;

//header
@versionIcon: linear-gradient(180deg, #3a4a55 0%, #243642 100%);
//home 版本升级
@verIcon1_backgroundColor: linear-gradient(270deg, #d9a784 0%, #efdeca 100%),
  linear-gradient(90deg, #8fa3c2 0%, #63749a 100%), #cfa980;
@verIcon1_fontColor: #282a43;
@verIcon2_backgroundColor: linear-gradient(90deg, #3b416b 0%, #24253b 100%);
@verIcon2_fontColor: #ffcebf;
//pc_add_edit_supplier
@input_backgroundColor: #f5f8fb;
//change_shifts 交接班详情
@text: #b2c3cd;
// 控件获取焦点时边框颜色
@themeFocusBorderColor: #bda169;
@fadeThemeColor: #F4EFE5;
@warningRed: #F64C4C;
@warningBgColor:#FEF2F2;
