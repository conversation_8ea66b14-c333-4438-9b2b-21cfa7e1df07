<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
export default {
  data () {
    return {
      detail_arr: [
        {
          prefix: '销',
          title: '总销售',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '会',
          title: '会员充值',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '现',
          title: '应收现金',
          desc: [],
          total: '（0.00元 0笔）'
        }, {
          prefix: '支',
          title: '支付统计',
          desc: [],
          total: '（0.00元 0笔）'
        }
      ],
      userInfo: _.cloneDeep(this.$store.state.show.loginInfo),
      loginTime: '',
      endDate: '',
      table_arr: [],
      money_arr: [],
      type: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 获取本地交接班记录
     */
    getChangeShiftsData (type) {
      this.type = type;
      if ($setting.username) {
        this.userInfo.phone = $setting.username;
      }
      if ($setting.userlastime) {
        this.loginTime = $setting.userlastime;
      }
      this.updateLocalChangeShiftsRecord();
    },
    /**
     * 更新本地交接班记录
     */
    updateLocalChangeShiftsRecord () {
      const param = {
        action: 2,
        shiftHistoryId: this.changeId,
        uid: this.userInfo.uid,
        end: new Date().format('yyyy-MM-dd hh:mm:ss'),
        start: this.loginTime.replace(/\./g, '-')
      };
      console.log('交接班插入本地参数', param);
      // 插入交接班记录到本地表,插入成功后云同步退出到登录页
      shiftHistoryService.save(param).then(() => {
        this.continueExit();
      }).catch(() => {
        this.continueExit();
      });
    },
    /**
     * 退出时云同步
     */
    continueExit () {
      // 如果正在执行云同步，则不继续执行
      if (+this.isSyncing > 0) {
        return;
      }
      if (!pos.network.isConnected() || this.$store.state.show.network === false) {
        this.SET_SHOW({load_message: '正在退出，请稍后……'});
      } else {
        this.SET_SHOW({load_message: '正在云同步，请稍后……'});
      }
      this.SET_SHOW({isSyncingLogin: true, pcLockScreen: false});
      this.$log.info('logout');
      syncService.clearDataInfo(() => {
        this.toExit();
      }, () => {
        this.toExit();
      });
    },
    /**
     * 云同步
     */
    beforeSync (type) {
      // 如果正在执行云同步，则不继续执行
      if (this.isSyncing) {
        return;
      }
      this.$log.info('logout');
      syncService.clearDataInfo(
        () => {
          this.getChangeShiftsData(type);
        },
        () => {
          this.getChangeShiftsData(type);
        }
      );
    },
    /**
     * 退出云同步
     */
    syncLogout () {
      // 如果正在执行云同步，则不继续执行
      if (this.isSyncing) {
        return;
      }
      this.SET_SHOW({isSyncingLogin: true});
      this.$log.info('logout');
      syncService.clearDataInfo(
        () => {
          this.toExit();
        },
        () => {
          this.toExit();
        }
      );
    },
    toExit () {
      setTimeout(() => {
        let that = this;
        settingService.put({ key: settingService.key.usertoken, value: '' }); // 清空本地表usertoken
        if (that.type === 'login') {
          that.$log.info('reload');
          demo.actionLog(logList.base_change_login, () => external.reloadForm());
        }
        if (that.type === 'exit') {
          demo.actionLog(logList.base_change_shifts, () => external.closeMainForm());
        }
      }, 500);
    },
    // 退出时上传actionLogs
    actionLogsGoodsStock(onSuccess) {
      let that = this;

      var params = {
        'systemName': $config.systemName,
        'phone': that.sys_uid,
        'sysSid': that.sys_sid,
        'deviceType': 'pc',
        'mac': pos.network.getMac(),
        'action': 'exit',
        'info': {}
      };
      commonService.actionLogsGoodsStock(res => {
        params.info = res;

        demo.$http.post(that.$rest.actionLogsGoodsStock, params, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': that.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
          .then(onSuccess)
          .catch(res1 => {
            console.error(res1);
          });
      });
    },
    getDetailArr () {
      return this.detail_arr;
    }
  },
  computed: mapState({
    isSyncing: state => state.show.isSyncing,
    isSyncingLogin: state => state.show.isSyncingLogin,
    isHome: state => state.show.isHome,
    isLogin: state => state.show.isLogin,
    fingerprint: state => state.show.fingerprint,
    isHeader: state => state.show.isHeader,
    changeId: state => state.show.changeId,
    kexianValue: state => state.show.kexianValue,
    showKexian: state => state.show.showKexian
  })
};
</script>
