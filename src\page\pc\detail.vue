<style lang="less" scoped>
#fontColor {
  color: @themeFontColor;
}
.media {
  border-top: 1px solid #dee2e6;
  padding-top: 5px;
  padding-bottom: 5px;
}
.footer {
  position: relative;
  bottom: 0;
  left: 28%;
  width: 100%;
}
/deep/ .el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  background-color: #fff;
}
/deep/ .el-input__inner {
  height: 44px;
  line-height: 44px;
}
.pc_det1 {
  color: #fff;
  background: linear-gradient(90deg, #f1d3af, #cfa26b);
}
.pc_det11 {
  width: 325px;
  height: 38px;
  border: 1px solid #bbb;
  border-radius: 19px;
  margin: 0 auto;
  margin-top: 20px;
  display: none;
}
.pc_det12 {
  width: 24px;
  height: 24px;
  margin-top: 6px;
  margin-left: 10px;
  float: left;
}
.pc_det13 {
  width: 250px;
  height: 28px;
  line-height: 28px;
  margin-left: 3px;
  font-size: 14px;
  margin-top: 4px;
  border: none;
  color: #666;
  float: left;
}
.pc_det14 {
  width: 16px;
  height: 16px;
  margin-top: 10px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_det15 {
  float: left;
  overflow: hidden;
  // width: 50%;
  min-height: 45px;
}
.pc_det15 div {
  float: left;
  border-radius: 14px;
  height: 28px;
  padding-left: 15px;
  padding-right: 15px;
  line-height: 28px;
  cursor: pointer;
  margin-top: 10px;
}
.pc_det16 {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  width: 100%;
  min-height: 64px;
  background-color: #f5f8fb;
  font-weight: normal;
  font-size: 16px;
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 22px;
    font-size: 16px;
    color: @themeFontColor;
  }
  /deep/ .el-input__icon.el-range__icon.el-icon-date {
    visibility: hidden;
  }
  /deep/ .el-range-input {
    font-size: 16px;
    color: @themeFontColor;
  }
}
.pc_det17 {
  float: left;
}
.pc_det17 div {
  float: left;
  border-radius: 14px;
  height: 28px;
  padding-left: 15px;
  padding-right: 15px;
  line-height: 28px;
  cursor: pointer;
  margin-top: 10px;
}
.pc_det18 {
  overflow: hidden;
  width: 100%;
}
.pc_det19 {
  color: @themeBackGroundColor;
  font-weight: bold;
  overflow: hidden;
  padding-left: 15px;
  padding-right: 15px;
  line-height: 50px;
  font-size: 15px;
}
.pc_det19 div {
  float: left;
}
.pc_det2 {
  margin-top: 5px;
  overflow: hidden;
  font-size: 15px;
}
.pc_det2 div {
  float: left;
}
.pc_det21 {
  margin-top: 10px;
  font-size: 15px;
  .det-bottom {
    display: flex;
    justify-content: space-between;
    .bottom__title {
      color: @warningRed;
      border-radius: 5px;
      font-size: 14px !important;
      padding: 0 8px;
      border: 1px solid @warningRed;
    }
    .bottom__index {
      color: @butFontColor;
      border-radius: 5px;
      font-size: 14px !important;
      padding: 0 8px;
      border: 1px solid @butFontColor;
    }
  }
}
.pc_det22 {
  padding-left: 15px;
  padding-right: 15px;
  overflow: hidden;
  padding-bottom: 5px;
  cursor: pointer;
}
.pc_det222 {
  background: @themeBackGroundColor;
  color: #fff;
}
.pc_det23 {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100%;
  font-size: 16px;
  border-top: 1px dashed #e5e5e5;
  height: 190px;
}
.pc_det24 {
  overflow: hidden;
  line-height: 18px;
  margin-top: 25px;
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.pc_det24 div {
  float: left;
  width: 45%;
  text-indent: 40px;
}
.pc_det25 {
  width: 90px;
  height: 36px;
  color: @themeBackGroundColor;
  background-color: @themeButtonBackGroundColor;
  border-radius: 18px;
  float: right;
  margin-left: 20px;
  cursor: pointer;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
}
.partly-back {
  color: #e5dcc6;
  background: #f4efe5;
}
.pc_det26 {
  float: left;
  color: @themeBackGroundColor;
  font-size: 18px;
  font-weight: 700;
  margin-left: 20px;
  margin-top: 15px;
  line-height: 18px;
}
.pc_det27 {
  overflow: hidden;
  line-height: 18px;
  margin-top: 24px;
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.pc_det27 div {
  float: left;
  width: 33%;
  text-indent: 18px;
}
.pc_det28 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.pc_det29 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
  font-weight: normal;
}
.pc_det3 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-weight: normal;
}
.pc_det31 {
  width: 360px;
  height: 62px;
  background: @linearBackgroundColor;
  color: #fff;
  line-height: 62px;
  font-size: 16px;
  text-indent: 20px;
  position: absolute;
  left: 0;
  bottom: 0;
}
.pc_det32 {
  cursor: pointer;
  width: 100px;
  height: 44px;
  line-height: 44px;
  border-radius: 22px;
  text-align: center;
  font-weight: 700;
  color: #ffffff;
  font-size: 18px;
  background: @themeBackGroundColor;
  float: left;
  margin-left: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.pc_det33 {
  float: right;
  margin-right: 10px;
  width: 110px;
  min-width: 110px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 22px;
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  line-height: 44px;
  text-align: center;
  margin-top: 10px;
  cursor: pointer;
}
.pc_det34 {
  width: 330px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-top: 10px;
  margin-left: 10px;
  background: #fff;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_det34 input {
  width: 280px;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 16px;
  margin-top: 8px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_det34_short {
  width: 260px;
  input {
    width: 210px;
  }
}
.pc_det_31 {
  width: 250px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-top: 10px;
  margin-left: 10px;
  background: #fff;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_det_31 input {
  width: 200px;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 16px;
  margin-top: 8px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_det35 {
  width: 18px;
  height: 18px;
  margin-top: 12px;
  margin-left: 4px;
  float: left;
  cursor: pointer;
}
.pc_btn2 {
  cursor: pointer;
  width: 130px;
  height: 44px;
  margin-left: 50px;
  line-height: 40px;
  border-radius: 22px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  background: @themeBackGroundColor;
  float: left;
  border: 0px;
  outline: none;
}
.pc_btn2:active {
  cursor: pointer;
  width: 130px;
  height: 44px;
  margin-left: 50px;
  line-height: 40px;
  border-radius: 22px;
  text-align: center;
  color: #fff;
  font-size: 18px;
  background: @themeBackGroundColor;
  float: left;
  border: 0px;
  opacity: 0.5;
  outline: none;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner {
    border: none;
    height: 42px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
}
.date_picker_container {
  // width: 300px;
  // height: 45px;
  width: 428px;
  height: 44px;
  background: #ffffff;
  border: 1px solid #d7d7d7;
  border-radius: 20px;
  margin-left: 10px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  float: left;
  /deep/ .el-input__prefix {
    display: none;
  }
}
.cj-date-picker-container {
  float: left;
  margin-top: 10px;
  margin-left: 10px;
}
.employee_select {
  margin-left: 10px;
  width: 140px;
  height: 44px;
  float: left;
  margin-top: 10px
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
  background: #ffffff;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 20px;
  height: 44px;
  color: @themeFontColor;
  font-size: 16px;
  padding-right: 0;
}
/deep/ .el-icon-date:before {
  content: '';
}
.isInPutIng {
  border-color: @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
.isInPutIng2 {
  border-color: #bbb;
}
#paidIn {
  color: @themeBackGroundColor;
}
.order {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  overflow: hidden;
  font-weight: bold;
  font-size: 16px;
  color: @homeColor;
  .order-title {
    position: relative;
    .title-code {
      .title-code__returns {
        padding: 0 10px;
        display: inline-block;
        border: 1px solid @warningRed;
        color: @warningRed;
        font-size: 14px;
        margin-left: 10px;
        border-radius: 5px;
      }
    }
  }
  .order-search {
    .order-search__show {
      margin-left: 15px;
      cursor: pointer;
      color: @themeBackGroundColor;
    }
  }
  .order-position {
    position: absolute;
    width: 400px;
    right: 10px;
    top: 120px;
    border-radius: 10px;
    z-index: 5;
    padding: 15px;
    background: @input_backgroundColor;
    .position-title {
      position: relative;
      display: flex;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      align-items: center;
      .position-title__close {
        cursor: pointer;
        color: #8298a6;
        margin-top: -5px;
        font-size: 30px;
      }
    }
    .position-title::before {
      content: '';
      display: block;
      position: absolute;
      top: -30px;
      left: 97%;
      transform: translateX(-50%);
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #f5f8fb;
    }
    .position {
      margin-top: 10px;
      padding: 10px;
      border-radius: 5px;
      background: @butFontColor;
      max-height: 220px;
      overflow: auto;
      .position-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;
        .position-list__top {
          color: @areaFontColor;
          font-size: 14px;
          font-weight: 400;
          .time {
            font-weight: 700;
          }
          .day {
            font-size: 12px;
          }
        }
        .position-list__bottom {
          cursor: pointer;
          color: @themeBackGroundColor;
        }
      }
    }
  }
}

/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
/deep/ .el-dialog {
  border-radius: 10px;
}
.sale-detail-wrap {
  width: 100%;
  height: calc(100% - 49px);
  font-size: 16px;
  position: relative;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .sale-content-wrap {
    height: calc(100% - 64px);
    border: 1px solid #dee2e6;
    background-color: #fff;
    border-radius: 4px;
    .sale-list-wrap {
      height: 100%;
      width: 360px;
      float: left;
      border-right: 1px solid #dee2e6;
      overflow-y: hidden;
      .sale-list {
        height: calc(100% - 62px);
        #leftList_id {
          height: 100%;
          overflow: scroll;
        }
      }
    }
    .sale-table-wrap {
      width: calc(100% - 360px);
      float: left;
      height: 100%;
      position: relative;
      .table-wrap {
        width: calc(100% - 10px);
        margin: 0 auto;
        overflow: hidden;
        height: calc(100% - 300px);
      }
    }
  }
  .mid-content {
    height: calc(100% - 118px);
  }
  .small-content {
    height: calc(100% - 172px);
  }
}
</style>
<template>
  <div
    id="fontColor"
    @click.stop="showReturnsList = false;"
    style="width: 100%;height: 100%;font-weight: bold;"
    v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)"
  >
    <!--弹出结算界面的计算器-->
    <v-FinalPay></v-FinalPay>
    <div v-show="delete_order" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 400">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)" @click="delete_order = false"></div>
      <div
        style="
          position: relative;
          z-index: 800;
          height: 250px;
          margin: 0 auto;
          margin-top: 240px;
          background: #fff;
          width: 450px;
          overflow: hidden;
          border-radius: 5px;
        "
      >
        <div style="width: 100%; text-align: center; font-size: 25px; margin-top: 40px; font-weight: normal">提示</div>
        <div style="width: 100%; text-align: center; font-size: 25px; margin-top: 25px; font-weight: 100">删除后不可恢复，是否删除单据？</div>
        <div class="pc_det28">
          <div class="pc_det29" @click="delete_order = false">取消</div>
          <div
            class="pc_det3"
            @click="
              continue_delete_order();
              delete_order = false;
            "
          >
            删除
          </div>
        </div>
      </div>
    </div>
    <div v-show="delete_goods" style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; z-index: 400">
      <div style="position: fixed; width: 100%; height: 100%; top: 0; left: 0; background: rgba(0, 0, 0, 0.5)" @click="delete_goods = false"></div>
      <div
        style="
          position: relative;
          z-index: 800;
          height: 250px;
          margin: 0 auto;
          margin-top: 240px;
          background: #fff;
          width: 450px;
          overflow: hidden;
          border-radius: 5px;
        "
      >
        <div style="width: 100%; text-align: center; font-size: 25px; margin-top: 40px; font-weight: normal">温馨提示</div>
        <div style="width: 100%; text-align: center; font-size: 25px; margin-top: 25px; font-weight: 100">是否确定退货？</div>
        <div class="pc_det28">
          <div class="pc_det29" @click="delete_goods = false">取消</div>
          <div
            class="pc_det3"
            @click="
              continue_delete_goods();
              delete_goods = false;
            "
          >
            确定
          </div>
        </div>
      </div>
    </div>
    <div style="width: 100%; height: 100%; padding: 12px; background-color: #f5f8fb">
      <div class="sale-detail-wrap">
        <!-- line1 -->
        <div>
          <div class="pc_det16" ref="searchHeaderCon">
            <div style="float: left;overflow: hidden;" :style="screenWidth < 1300 ? 'width: 860px;' : ''">
              <div style='float: left;'>
                <div
                  class='pc_det34'
                  :class="[
                    {'pc_det34_short':  pc_detail_tab === 1},
                    inputing_keyword ? 'isInPutIng' : 'isInPutIng1'
                  ]"
                >
                  <input
                    @focus='inputing_keyword = true'
                    @blur='inputing_keyword = false'
                    type='text'
                    :placeholder="'商品名称/条码/首字母/扫码' + (pc_detail_tab === 1 ? '' : '/备注')"
                    v-model='keyword'
                    v-focus-select="'focusSelect'"
                    ref="goods_keyword"
                    style="margin-left: 15px"
                    @compositionstart="pinyin = true"
                    @compositionend="pinyin = false"
                    @input="keyword = $goodsNameFormat(keyword)"
                  />
                  <img alt="" class="pc_det35" v-show="keyword !== ''" @click="inputFocus()" src="../../image/pc_clear_input.png" />
                </div>
              </div>
              <div v-if="pc_detail_tab === 1" style="float: left;">
                <div class="pc_det_31" :class="inputing_keyword2 ? 'isInPutIng' : 'isInPutIng1'">
                  <input
                    @focus='inputing_keyword2 = true'
                    @blur='inputing_keyword2 = false'
                    type='text'
                    placeholder='会员姓名/手机号/备注/单号'
                    v-model='vipKeyword'
                    v-focus-select="'focusSelect'"
                    ref='vip_keyword'
                    @input="vipKeyword = $vipNameFormat(vipKeyword, {maxLen: 20})"
                    style="margin-left: 15px"
                  />
                  <img alt="" class="pc_det35" v-show="vipKeyword !== ''" @click="inputFocusVip()" src="../../image/pc_clear_input.png" />
                </div>
              </div>
              <div class="cj-date-picker-container">
                <vCjDatePicker
                  v-if="pc_detail_tab === 1"
                  type="datetime"
                  :width="428"
                  :start-date.sync="date_picker.date_from"
                  :end-date.sync="date_picker.date_to"
                  date-format="yyyy-MM-dd HH:mm:ss"
                  @pickerChange="() => {}"/>
                <vCjDatePicker
                  v-if="pc_detail_tab === 2"
                  type="date"
                  :start-date.sync="date_picker2.date_from"
                  :end-date.sync="date_picker2.date_to"
                  date-format="yyyy-MM-dd"
                  @pickerChange="() => {}"/>
              </div>
              <el-select
                v-model="accountId"
                placeholder="全部支付方式"
                style="margin-left: 10px; width: 160px; height: 44px; margin-top: 10px; float: left"
              >
                <el-option v-for="item in accountlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-select v-model="inOut" placeholder="全部单据" style="margin-left: 10px; width: 130px; height: 44px; float: left; margin-top: 10px">
                <el-option v-for="item in inOutList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-select v-if="pc_detail_tab === 1" v-model="employeeVal" placeholder="全部收银员" class="employee_select">
                <el-option v-for="item in employeeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <div v-show="pc_detail_tab === 2" style="margin-top: 10px; float: left">
                <el-select v-model="companyId" placeholder="全部供应商" style="margin-left: 10px; width: 160px; height: 44px">
                  <el-option v-for="item in makerlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div class="pc_det32" @click="get_leftList(true, true)">查询</div>
            </div>
            <div class="pc_det33" @click="getExcel()">导出表格</div>
          </div>
        </div>
        <div class="sale-content-wrap" :class="[saleContentClass]" :key="searchDivbr">
          <div class="sale-list-wrap">
            <div class="pc_det11" :class="inputing_keyword ? 'isInPutIng' : 'isInPutIng2'">
              <img alt="" class="pc_det12" src="../../image/pc_search.png" />
              <input
                class="pc_det13"
                @focus="inputing_keyword = true"
                @blur="inputing_keyword = false"
                type="text"
                placeholder="输入单据号/供应商名称/商品名称查询"
                v-model="keyword"
              />
              <img alt="" class="pc_det14" v-show="keyword != ''" @click="keyword = ''" src="../../image/pc_clear_input.png" />
            </div>
            <div class="sale-list">
              <div
                id="leftList_id"
                ref="leftList_id"
                :infinite-scroll-disabled="pc_detail_tab === 2 || waitingResponse"
                infinite-scroll-immediate="false"
                v-infinite-scroll="infiniteScrollLoad"
                infinite-scroll-delay="50"
              >
                <div class="pc_det18" v-for="(lef, index1) in leftList_total" :key="lef.optDate">
                  <div class="pc_det19">
                    <div style="float: left">{{ lef.optDate }}</div>
                    <div style="float: right; right: 0" v-if="!$employeeAuth('purchase_price') || pc_detail_tab !== 2">
                      {{ $toDecimalFormat(lef.dayTotalMoney, 2, true) }}
                    </div>
                  </div>
                  <div
                    v-for="(le, index2) in lef.details"
                    :key="le.id"
                    class="pc_det22"
                    @click="
                      goodsDetail(le);
                      change_color = index1 + '_' + index2;
                    "
                    :class="{ pc_det222: change_color == index1 + '_' + index2 }"
                  >
                    <div class="pc_det2">
                      <div v-show="pc_detail_tab === 1 && le.accountId !== 1" style="width: 120px">
                        {{ le.accountName }}
                        <span v-if="le.inOut === 1">收款</span>
                        <span v-if="le.inOut === 2">退款</span>
                      </div>
                      <div v-show="pc_detail_tab === 1 && le.accountId === 1 && le.inOut === 1" style="width: 120px">现金收款</div>
                      <div v-show="pc_detail_tab === 1 && le.accountId === 1 && le.inOut === 2" style="width: 120px">现金退款</div>
                      <div v-show="pc_detail_tab === 2 && le.inOut === 1" style="width: 180px">
                        进货
                        <span style="margin-left: 20px; color: #e3d7bd">{{ timeFormat(le.createAt) }}</span>
                      </div>
                      <div v-show="pc_detail_tab === 2 && le.inOut === 2" style="width: 180px">
                        退货
                        <span style="margin-left: 20px; color: #e3d7bd">{{ timeFormat(le.createAt) }}</span>
                      </div>
                      <div v-show="le.vipid != null && le.vipid != '' && le.vipid != 0" style="overflow: hidden">
                        <img
                          alt=""
                          v-show="change_color == index1 + '_' + index2"
                          src="../../image/pc_detail_member.png"
                          style="width: 18px; margin-top: 3px; float: left"
                        />
                        <img
                          alt=""
                          v-show="change_color != index1 + '_' + index2"
                          src="../../image/pc_detail_member_grey.png"
                          style="width: 18px; margin-top: 3px; float: left"
                        />
                        <div style="float: left; margin-left: 8px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 80px">
                          {{ le.vipname }}
                        </div>
                      </div>
                      <div style="float: right; margin-right: 0;" v-show="!$employeeAuth('purchase_price') || pc_detail_tab !== 2">
                        {{ Number(le.payAmt).toFixed(2) }}
                      </div>
                    </div>
                    <div class="pc_det21">
                      <div v-if="pc_detail_tab === 1" class="det-bottom">
                        <div v-if="pc_detail_tab === 1">{{ format_time(le.createAt) }}</div>
                        <div
                          v-if="pc_detail_tab === 1 && le.inOut === 1 && le.refundStatus && !le.refundStatus.includes('No Refund')"
                          :class="change_color == index1 + '_' + index2 ? 'bottom__index' : 'bottom__title'"
                        >
                          {{ returnShow(le.refundStatus) }}
                        </div>
                      </div>
                      <div v-if="pc_detail_tab === 2">{{ le.code }}</div>
                    </div>
                  </div>
                </div>
                <div style="height: 15px"></div>
              </div>
            </div>
            <!--合计按钮-->
            <div class="pc_det31">
              <div style="float: left">共{{ isNaN(total_oreder) ? '0' : total_oreder }}单</div>
              <div style="float: right" v-show="!$employeeAuth('purchase_price') || pc_detail_tab !== 2">
                <span>合计:</span>
                <span style="float: right; margin-right: 20px">¥ {{ isNaN(total_all_money) ? '0.00' : Number(total_all_money).toFixed(2) }}</span>
              </div>
            </div>
          </div>
          <!--右边部分-->
          <div v-show="right_msg != ''" class="sale-table-wrap">
            <div style="height: 105px; overflow: hidden">
              <div style="overflow: hidden; height: 50px">
                <div class="pc_det26">
                  <span v-show="pc_detail_tab === 1 && right_msg.inOut === 1">销售单详情</span>
                  <span v-show="right_msg.inOut !== 1">退货单详情</span>
                  <span v-show="pc_detail_tab === 2 && right_msg.inOut === 1">进货单详情</span>
                </div>
                <div style="float: right;margin-right:20px;margin-top: 10px;">
                  <div
                    class="pc_det25"
                    v-show="pc_detail_tab === 1"
                    @click="print_list()"
                  >打印</div>
                  <div class="pc_det25"
                    v-show="pc_detail_tab === 2 && $employeeAuth('delete_purchase_return_purchase')"
                    @click="deletePurchaseOrder">删除</div>
                  <!-- 设计说3.9.0先把收银台退货改成退货 -->
                  <div
                    class="pc_det25"
                    :class="(right_msg.refundStatus &&
                      right_msg.refundStatus.includes('Full Refund')) ||
                      right_msg.accountId === 99 ||
                      !$employeeAuth('return_sales') ||
                      isNullOrTrimEmpty(right_msg.vipname) ? 'partly-back' : ''"
                    v-show="pc_detail_tab === 1 && right_msg.inOut === 1"
                    @click.stop="handlePartlyBack()"
                  >
                    部分退货
                  </div>
                  <div
                    class="pc_det25"
                    :class="(right_msg.refundStatus && !right_msg.refundStatus.includes('No Refund')) ||
                      !$employeeAuth('return_sales') ? 'partly-back' : ''"
                    v-show="pc_detail_tab === 1 && right_msg.inOut === 1"
                    @click="orderRefund()"
                  >
                    整单退货
                  </div>
                </div>
              </div>
              <div class="order">
                <div class="order-title">
                  <div class="title-code">
                    单号：{{ right_msg.code }}
                    <span
                      v-if="pc_detail_tab === 1
                      && right_msg.inOut === 1
                      && right_msg.refundStatus
                      && !right_msg.refundStatus.includes('No Refund')"
                      class="title-code__returns"
                    >
                      {{ returnShow(right_msg.refundStatus) }}
                    </span>
                  </div>
                  <div class="title-time">时间：{{ dateFormat(right_msg.createAt) }}</div>
                </div>
                <div class="order-search" v-if="pc_detail_tab === 1 && right_msg.refundStatus">
                  <div v-if="right_msg.refundStatus && !right_msg.refundStatus.includes('No Refund') && right_msg.inOut === 1">
                    关联退货单
                    <span v-if="returnsList.length === 1 && right_msg.inOut === 1">：{{ returnsList[0].code }}</span>
                    <span class="order-search__show" @click.stop="showReturns(false, returnsList[0])">查看</span>
                  </div>
                  <div v-if="right_msg.inOut === 2 && returnsList.length > 0">
                    原销售单：{{ returnsList[0] ? returnsList[0].code : '-' }}
                    <span class="order-search__show" @click.stop="showReturns(false, returnsList[0])">查看</span>
                  </div>
                </div>
                <div v-if="showReturnsList" class="order-position">
                  <div class="position-title">
                    <div>关联退货单</div>
                    <div class="position-title__close" @click="showReturnsClose">×</div>
                  </div>
                  <div class="position">
                    <div class="position-list" v-for="(item, index) in returnsList" :key="index">
                      <div class="position-list__top">
                        <div class="time">{{ timeFormat(item.createAt) }}</div>
                        <div class="day">{{ new Date(item.createAt).format('yyyy-MM-dd') }}</div>
                      </div>
                      <div class="position-list__top">{{ item.code }}</div>
                      <div class="position-list__bottom" @click="showReturns(true, returnsList[index])">查看</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="table-wrap">
              <el-table
                v-show="pc_detail_tab === 1"
                id="detailTab1"
                ref="multipleTable"
                empty-text=" "
                :data="right_list"
                height="100%"
                tooltip-effect="dark"
                style="float: left; width: 100%; font-size: 16px; font-weight: normal"
              >
                <el-table-column label="" type="index" width="40" align="center"></el-table-column>
                <el-table-column label="名称">
                  <template slot-scope="scope">
                    <cj-copy :name="scope.row.goodName"></cj-copy>
                  </template>
                </el-table-column>
                <el-table-column label="条码" width="157" align="right">
                  <template slot-scope="scope">
                    <cj-copy :name="scope.row.code" :center="'right'"></cj-copy>
                  </template>
                </el-table-column>
                <el-table-column prop="qty" label="数量" width="95" align="right"></el-table-column>
                <el-table-column label="单价" width="110" align="right">
                  <template slot-scope="scope">¥ {{ Number(scope.row.price).toFixed(2) }}</template>
                </el-table-column>
                <el-table-column label="折扣" width="70" align="right">
                  <template slot-scope="scope">{{ (Number(scope.row.itemDisc) * 100).toFixed(0) }}%</template>
                </el-table-column>
                <el-table-column label="小计" width="120" align="right">
                  <template slot-scope="scope">¥ {{ Number(scope.row.amt).toFixed(2) }}</template>
                </el-table-column>
              </el-table>
              <el-table
                v-show="pc_detail_tab === 2"
                id="detailTab2"
                ref="multipleTable"
                empty-text=" "
                :data="right_list"
                height="100%"
                tooltip-effect="dark"
                style="float: left; width: 100%; font-size: 15px; font-weight: normal"
              >
                <el-table-column label="序号" type="index" show-overflow-tooltip width="56" align="center"></el-table-column>
                <el-table-column label="名称" show-overflow-tooltip>
                  <template slot-scope="scope">{{ scope.row.goodName }}</template>
                </el-table-column>
                <el-table-column label="条码" width="150" align="left">
                  <template slot-scope="scope">
                    {{ scope.row.goodCode }}
                  </template>
                </el-table-column>
                <el-table-column label="进货价" width="100" align="left" v-if="!$employeeAuth('purchase_price')">
                  <template slot-scope="scope">¥ {{ scope.row.price }}</template>
                </el-table-column>
                <el-table-column label="单位" width="56" align="center">
                  <template slot-scope="scope">{{ scope.row.unitName ? scope.row.unitName : '-' }}</template>
                </el-table-column>
                <el-table-column label="数量" width="95" align="right">
                  <template slot-scope="scope">{{ $toDecimalFormat(scope.row.qty, 3) }}</template>
                </el-table-column>
                <el-table-column label="金额" width="100" align="right" v-if="!$employeeAuth('purchase_price')">
                  <template slot-scope="scope">¥ {{ Number(scope.row.amt).toFixed(2) }}</template>
                </el-table-column>
              </el-table>
            </div>
            <div class="pc_det23" :key="searchDivbr" v-show="pc_detail_tab === 1">
              <div class="pc_det27">
                <div>收银员：{{ right_msg.uid_no }}</div>
                <div v-show="right_msg.vipname !== ''" style="width: 66%">会员：{{ right_msg.vipname }}</div>
              </div>
              <div class="pc_det27">
                <div v-show="right_msg.inOut === 1">优惠：¥ {{ (Number(right_msg.billAmt) - Number(right_msg.discAmt)).toFixed(2) }}</div>
                <div v-show="right_msg.inOut === 1">应收：¥ {{ Number(right_msg.discAmt).toFixed(2) }}</div>
                <div v-show="right_msg.inOut === 2">应退：¥ {{ Math.abs(Number(right_msg.discAmt)).toFixed(2) }}</div>
                <div v-show="!$employeeAuth('purchase_price')">
                  单笔利润：¥
                  {{ Number(right_msg.discAmt - right_msg.sumPurPrice).toFixed(2) }}
                </div>
              </div>
              <div class="pc_det27">
                <template v-if="combinedItem.length === 2">
                  <el-tooltip
                    effect="dark"
                    placement="top"
                    :content="combinedSettlementMethod">
                    <div class="ellipsis">结算方式：
                      <span>
                        {{combinedSettlementMethod}}
                      </span>
                    </div>
                  </el-tooltip>
                </template>
                <template v-else>
                  <div class="ellipsis">结算方式：
                    <span>
                      {{((right_msg.accountId === 7 || right_msg.accountId === 8) &&
                        right_msg.inOut === 2) ?
                        getAccountName(right_msg.accountName) :
                        right_msg.accountName}}
                    </span>
                  </div>
                </template>
                <div
                  v-show="right_msg.inOut === 1"
                >实收：¥ {{(Number(right_msg.discAmt) + Number(right_msg.changeAmt)).toFixed(2)}}</div>
                <div
                  v-show="right_msg.inOut === 2"
                >实退：¥ {{Math.abs(Number(right_msg.discAmt)).toFixed(2)}}</div>
                <div
                  v-show="right_msg.inOut === 1"
                >找零：¥ {{Number(right_msg.changeAmt).toFixed(2)}}</div>
              </div>
              <div class="pc_det27">
                <div style="width: calc(100% - 20px)">
                  备注：
                  <span>{{ right_msg.remark || '无' }}</span>
                </div>
              </div>
            </div>
            <div class="pc_det23" v-show="pc_detail_tab === 2">
              <div class="pc_det24">
                <template v-if="right_msg.supplierName">
                  <el-popover
                    popper-class="pc_pay192 popper_self"
                    placement="top"
                    trigger="hover"
                    :close-delay="10"
                    :content="`${right_msg.supplierName}`"
                    width="400"
                  >
                    <div slot="reference" class="ellipsis">供应商：{{ right_msg.supplierName || '-' }}</div>
                  </el-popover>
                </template>
                <template v-else>
                  <div>供应商：{{ right_msg.supplierName || '-' }}</div>
                </template>
                <div>操作人：{{ right_msg.uid_no }}</div>
              </div>
              <div class="pc_det24">
                <div>账户：{{ right_msg.accountName }}</div>
                <div>
                  合计金额：
                  <span v-if="!$employeeAuth('purchase_price')">¥ {{ Number(right_msg.billAmt).toFixed(2) }}</span>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="pc_det24">
                <div>
                  折扣率：{{
                    right_msg.billAmt == 0 || Number(right_msg.payAmt) === Number(right_msg.billAmt)
                      ? '100'
                      : (Number(right_msg.disc) * 100).toFixed(2)
                  }}%
                </div>
                <div style="font-size: 18px">
                  本单实付：
                  <span v-if="!$employeeAuth('purchase_price')" id="paidIn" style="font-weight: bold">
                    ¥ {{ Number(right_msg.payAmt).toFixed(2) }}
                  </span>
                  <span v-else>-</span>
                </div>
              </div>
              <div class="pc_det24">
                <div style="width: calc(100% - 20px)">
                  <template v-if="right_msg.remark">
                    <el-popover
                      popper-class="pc_pay192 popper_self"
                      placement="top"
                      trigger="hover"
                      :close-delay="10"
                      :content="`${right_msg.remark}`"
                      width="500"
                    >
                      <div slot="reference" class="ellipsis" style="width: 100%">备注：{{ right_msg.remark }}</div>
                    </el-popover>
                  </template>
                  <template v-else>
                    备注：
                    <span>无</span>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <cj-mask :visible.sync="patylyBackShow">
      <partly-back
        v-if="patylyBackShow"
        :good-list="right_list"
        :order-info="right_msg"
        @backDetail="backDetail"
        @refresh="get_leftList(true)"
        @close="patylyBackShow = false"
      ></partly-back>
    </cj-mask>
    <return-title
      :visible.sync="visible"
      :orderTime="orderTime"
      :supplierShow="supplierShow"
      :right_msg="rightDetail"
      :combinedItem="combinedItem"
      :loading="returnLoading"
      @closeLoading="closeLoading"
      :right_list="orderRightlist"
    ></return-title>
    <!-- 退货弹窗 -->
    <el-dialog v-if="showBackMoneyDialog"
      :visible.sync="showBackMoneyDialog"
      :show-close="false"
      :close-on-click-modal="false" width="680px">
      <BackMoneyDialog
        :params="backMoneyParams"
        @backDetail="backDetail"
        @refresh="backDetail(true)"
        @closeDialog="showBackMoneyDialog = false" />
    </el-dialog>
    <confirm-dialog
      :visible.sync="zeroMoneyTipsShow"
      confirm-text="继续退货"
      :message="`销售单中有小计为
        <span style='color: #FF0000'>0元</span>商品，<br/>
        是否继续退货？`"
      @confirm="zeroMoneyTipsShow = false, patylyBackShow = true"
      @cancel="zeroMoneyTipsShow = false"
      :closeOnClickModal="false"
    />
    <confirm-dialog
      :visible="showBackCompleteDialog"
      :closeOnClickModal="false"
      confirm-text="返回收银台"
      cancel-text="留在这里"
      message="退货已完成，<br/>是否返回收银台?"
      :btnFontSize="18"
      @confirm="backCompleteConfirm(true)"
      @cancel="backCompleteConfirm(false)"
    ></confirm-dialog>
    <confirm-dialog
      :visible.sync="offlineRefundShow"
      title="提示"
      :message="`
        因版本升级，旧版本销售单仅支持<br/>
        收银台退货（退货不关联原单，金<br/>
        额不原路退回）<br/>
      `"
      confirm-text="收银台退货"
      @confirm="continue_delete_goods"
      :closeOnClickModal="false"
    ></confirm-dialog>
    <confirm-dialog
      :visible.sync="vipOldRefundShow"
      title="提示"
      :message="`
        因版本升级，旧版本销售单仅支持<br/>
        整单退货（退货金额不原路退回）<br/>
      `"
      confirm-text="整单退货"
      @confirm="vipReturnGoods"
      :closeOnClickModal="false"
    ></confirm-dialog>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import CjMask from '@/common/components/CjMask';
import PartlyBack from '@/common/components/PartlyBack';
import ReturnTitle from '@/common/components/ReturnTitle';
import BackMoneyDialog from '@/components/back_money_dialog.vue';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import { thousandSeparation } from 'frontend-utils';
import { getGenerateOrder } from '@/api/order';
import refundParams from '@/mixins/refundParams';
import CjCopy from '@/common/components/CjCopy';
import vCjDatePicker from '@/common/components/CjDatePicker';
export default {
  mixins: [refundParams],
  components: {
    CjMask,
    PartlyBack,
    ReturnTitle,
    BackMoneyDialog,
    ConfirmDialog,
    CjCopy,
    vCjDatePicker
  },
  data() {
    return {
      returnLoading: false, // 详情loading
      orderRightlist: [], // 退货详情数据
      orderTime: {}, // 进退货单号
      showBackMoneyDialog: false,
      supplierShow: false, // 进退款
      rightDetail: {}, // 退款详情数据
      backMoneyParams: {
        shouldBackMoney: '8.00',
        realBackMoney: '6.00'
      },
      visible: false, // 退货详情控制弹窗
      showReturnsList: false, // 退货单list是否显示
      returnsList: [], // 退货单详情list
      loading: false, // loading
      focusDate: false,
      choose_date: 2,
      change_color: '',
      // 进退货
      inOut: 0,
      // 付款方式
      accountId: '',
      // 供应商选择id
      companyId: '',
      // 供应商列表
      makerlist: [],
      // detail1时收款账户list
      accountlist1: [
        {
          value: 0,
          label: '全部支付方式'
        },
        {
          value: 1,
          label: '现金'
        },
        {
          value: 3,
          label: 'POS收银'
        },
        {
          value: 4,
          label: '微信'
        },
        {
          value: 5,
          label: '支付宝'
        },
        {
          value: 6,
          label: '会员卡'
        },
        {
          value: 7,
          label: '微信(扫码付)'
        },
        {
          value: 8,
          label: '支付宝(扫码付)'
        },
        {
          value: 99,
          label: '组合支付'
        }
      ],
      // detail2时收款账户list
      accountlist2: [
        {
          value: '',
          label: '全部支付方式'
        },
        {
          value: 1,
          label: '现金'
        },
        {
          value: 4,
          label: '微信'
        },
        {
          value: 5,
          label: '支付宝'
        }
      ],
      // detail1单据类型list
      inOutList1: [
        {
          value: 0,
          label: '全部单据'
        },
        {
          value: 1,
          label: '收款'
        },
        {
          value: 2,
          label: '退款'
        },
        {
          value: 3,
          label: '收款（已退）'
        }
      ],
      employeeVal: 0,
      // 员工list
      employeeList: [
        {
          value: 0,
          label: '全部收银员'
        }
      ],
      // detail2单据类型list
      inOutList2: [
        {
          value: '',
          label: '全部单据'
        },
        {
          value: 1,
          label: '进货'
        },
        {
          value: 2,
          label: '退货'
        }
      ],
      inOutList: [],
      date_picker: '',
      date_picker2: '',
      inputing_keyword: false,
      inputing_keyword2: false,
      waitingResponse: false,
      keyword: '',
      vipKeyword: '',
      limit: 100,
      pageNum: 1,
      // 左侧列表
      leftList: [],
      leftList_total: [],
      // 右侧详细信息（单号等）
      right_msg: [],
      // 右侧详情列表
      right_list: [],
      total_all_money: 0,
      total_oreder: 0,
      // start_date: '2000-01-01',
      // end_date: '2047-12-30',
      delete_order: false,
      delete_goods: false,
      // 允许监听进行请求加载
      allow_loading: false,
      allow_list: false,
      accountlist: [],
      print_click: false,
      searchDivbr: false,
      combinedItem: [], // 组合支付详细数据
      pinyin: false,
      patylyBackShow: false, // 是否显示部分退货窗口
      zeroMoneyTipsShow: false, // 是否显示订单中有0元商品提示弹窗
      offlineRefundShow: false, // 既存扫码付订单无法原路退回提示弹窗
      vipOldRefundShow: false, // 既存会员扫码订单提示弹窗
      saleContentClass: 'large-content'
    };
  },
  created() {
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.SET_SHOW({ isPrintSetting: false });
  },
  mounted() {
    this.getAllEmployees();
    this.$refs.goods_keyword.focus();
    this.SET_SHOW({
      isLogo: true
    });
    this.SET_SHOW({
      isHeader: true
    });
    if (this.pc_detail_tab === 1) {
      demo.actionLog(logList.clickSaleDetail);
      this.accountId = 0;
      this.inOut = 0;
      this.accountlist = this.accountlist1;
      this.inOutList = this.inOutList1;
    } else if (this.pc_detail_tab === 2) {
      demo.actionLog(logList.clickPurchaseDetail);
      this.accountId = '';
      this.companyId = '';
      this.inOut = '';
      this.accountlist = this.accountlist2;
      this.inOutList = this.inOutList2;
    } else {
      console.log('其他');
    }
    this.allow_list = false;
    let today = new Date();
    let date_to = today.format('yyyy-MM-dd 23:59:59');
    let date_from = new Date().format('yyyy-MM-dd 00:00:00');
    this.date_picker = {
      date_from: date_from,
      date_to: date_to
    };
    this.date_picker2 = {
      date_from: new Date().format('yyyy-MM-dd'),
      date_to: new Date().format('yyyy-MM-dd')
    };
    // 加载供应商列表
    this.getMakerListData();
    this.loading = true;
    syncService.clearDataInfo(() => {
      this.get_leftList(true);
    }, error => {
      this.loading = false;
      this.get_leftList(true);
    }, 'pos')
    const that = this;
    // 起始加载时，监听事件生效，多请求两次
    setTimeout(function () {
      that.allow_loading = true;
    }, 100);
  },
  watch: {
    detailBackPay() {
      if (this.detailBackPay) {
        var today = new Date();
        var date_to = today.format('yyyy-MM-dd hh:mm:ss');
        var date_from = new Date().format('yyyy-MM-dd 00:00:00');
        this.date_picker = {
          date_from: date_from,
          date_to: date_to
        };
        this.date_picker2 = {
          date_from: new Date().format('yyyy-MM-dd'),
          date_to: new Date().format('yyyy-MM-dd')
        };
        this.get_leftList(true);
        this.SET_SHOW({ detailBackPay: false });
      }
    },
    pc_detail_tab() {
      this.right_list = [];
      this.right_msg = [];
      this.leftList = [];
      this.leftList_total = [];
      this.total_all_money = 0;
      this.total_oreder = 0;
      if (this.pc_detail_tab === 1) {
        this.allow_loading = false;
        this.accountId = 0;
        this.inOut = '';
      } else if (this.pc_detail_tab === 2) {
        this.allow_loading = false;
        this.accountId = '';
        this.companyId = '';
        this.inOut = '';
      } else {
        console.log('其他');
      }
      this.get_leftList();
      var that = this;
      setTimeout(function () {
        that.allow_loading = true;
      }, 500);
    },
    leftList() {
      this.right_msg = [];
      this.right_list = [];
      if (this.leftList.length != 0) {
        this.change_color = '0_0';
        this.right_msg = this.leftList[0];
        this.goodsDetail(this.right_msg);
      }
      // setTimeout(function() {
      //   document.getElementById('leftList_id').scrollTop = 0;
      // }, 0);
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 获取所有员工数据
     */
    getAllEmployees() {
      clerkService.getAllClerks(res => {
        this.employeeList = [...this.employeeList, ...demo.t2json(res)];
      });
    },
    // 处理文字
    getAccountName(e) {
      if (e === '微信(扫码付)') {
        return '原路退回（微信-扫码付）';
      } else if (e === '支付宝(扫码付)') {
        return '原路退回（支付宝-扫码付）';
      }
      return e;
    },
    // 处理特殊情况
    isNullOrTrimEmpty(obj) {
      if (obj === null || obj === undefined || obj === '') {
        return false;
      }
      return true;
    },
    // 详情返回回调
    closeLoading() {
      this.returnLoading = false;
    },
    // 显示退货信息
    returnShow(e) {
      let name = '';
      if (e === 'Partial Refund') {
        name = '部分退货';
      } else if (e === 'Full Refund') {
        name = '整单退货';
      }
      return name;
    },
    // 显示部分退货窗口
    handlePartlyBack() {
      if (!this.$employeeAuth('return_sales')) {
        demo.msg('error', this.$msg.not_refund_permissions);
        return;
      }
      if (this.right_msg.refundStatus && this.right_msg.refundStatus.includes('Full Refund')) {
        demo.msg('error', '请勿重复退单');
        return;
      }
      if (!demo.isNullOrTrimEmpty(this.right_msg.vipname) || this.right_msg.accountId === 99) {
        // 组合支付
        if (this.right_msg.accountId === 99) {
          demo.actionLog(logList.clickPurchaseCombinationPayReturns);
        }
        // 有会员
        if (!demo.isNullOrTrimEmpty(this.right_msg.vipname)) {
          demo.actionLog(logList.clickPurchaseVipPartialReturns);
        }
        demo.msg('warning', '暂不支持有组合支付或会员的订单部分退货');
        return;
      }
      // 既存的扫码付单据如果info1中没有payInfo，提示线下退款
      if ([7, 8].includes(this.right_msg.accountId) && !this.right_msg.info1.includes('payInfo')) {
        this.offlineRefundShow = true;
        return;
      }
      // 查询订单中是否有0元商品
      const hasZero = this.right_list.find(good => {
        return +good.amt === 0;
      })
      if (hasZero) {
        this.zeroMoneyTipsShow = true;
        return;
      }
      this.patylyBackShow = true;
    },
    // 退货退款成功并刷新列表
    backDetail(bool) {
      this.patylyBackShow = false;
      this.showBackMoneyDialog = false;
      this.SET_SHOW({ isPartlyBack: false, showBackCompleteDialog: this.fromDetail === 0 });
      if (bool) {
        this.get_leftList(true);
      }
    },
    // 查看退货单
    showReturns(item, value) {
      console.log('查看退货单');
      if (this.returnsList.length > 1) {
        this.showReturnsList = true;
      } else {
        this.showReturnsList = false;
      }
      this.orderTime = value;
      this.getGoodsDetail(value);
      if (item || this.returnsList.length === 1) {
        this.visible = true;
        this.returnLoading = true;
      }
    },
    // 获取销售单
    getGoodsDetail(json) {
      saleService.detail(json.id, json.fingerprint, res => {
        console.log(res, 444444);
        clerkService.getEmployeeNumberByUid(res.sales, ['uid'], r => {
          console.log(r, '销售明细', json);
          this.rightDetail = demo.t2json(r)[0];
          this.rightDetail.vipname = json.vipname;
          this.rightDetail.vipid = json.vipid;
          this.rightDetail.vipmobile = json.vipmobile;
          this.rightDetail.info1 = json.info1;
          this.rightDetail.info2 = json.info2;
        });
        this.orderRightlist = res.saleitems;
        if (res.zuhe !== undefined) {
          // 组合支付详细数据
          this.combinedItem = res.zuhe;
        } else {
          this.combinedItem = [];
        }
      });
    },
    // 退货单弹窗关闭
    showReturnsClose() {
      this.showReturnsList = false;
    },
    deletePurchaseOrder() {
      this.delete_order = true;
      demo.actionLog(logList.clickPurchaseDetailDelete);
    },
    /**
     * 整单退货
     */
    async orderRefund() {
      if (!this.$employeeAuth('return_sales')) {
        demo.msg('error', this.$msg.not_refund_permissions);
        return;
      }
      if (this.right_msg.refundStatus && !this.right_msg.refundStatus.includes('No Refund')) {
        demo.msg('error', '请勿重复退单');
        return;
      }
      // 既存的扫码付单据如果info1中没有payInfo，提示线下退款
      if (
        [7, 8].includes(this.right_msg.accountId) &&
        !this.right_msg.info1.includes('payInfo')
      ) {
        // 非会员单提示线下退款
        if (!this.right_msg.info1.includes('vipInfo')) {
          this.offlineRefundShow = true;
          return;
        }
        // 既存无payInfo信息会员单使用原来的退款逻辑
        this.vipOldRefundShow = true;
        return;
      }
      // 扫码付单才进行云端采号
      if ([7, 8].includes(this.right_msg.accountId)) {
        const params = {
          deviceCode: demo.$store.state.show.devicecode,
          outTradeNo: this.right_msg.code
        }
        try {
          const { code, data } = await getGenerateOrder(params);
          if (code === 200) {
            this.backMoneyParams = this.getRefundParams(this.right_list, this.right_msg, this.combinedItem, data);
            this.showBackMoneyDialog = true;
            this.reportFormLog(_.cloneDeep(this.backMoneyParams.backParams), '销售明细预整单退货数据');
          } else {
            demo.msg('error', '服务器请求异常，请稍后重试');
          }
        } catch (error) {
          demo.msg('error', '服务器请求异常，请稍后重试');
        }
      } else {
        this.backMoneyParams = this.getRefundParams(this.right_list, this.right_msg, this.combinedItem);
        this.showBackMoneyDialog = true;
        this.reportFormLog(_.cloneDeep(this.backMoneyParams.backParams), '销售明细预整单退货数据');
      }
    },
    // 既存无payInfo信息会员单使用原来的退款逻辑
    vipReturnGoods() {
      this.vipOldRefundShow = false;
      const cancel_vipData = {
        phone: this.sys_uid,
        systemName: $config.systemName,
        uid: this.loginInfo.uid,
        sysSid: this.sys_sid,
        vipId: this.right_msg.vipid,
        vipname: this.right_msg.vipname,
        outTradeNo: this.right_msg.code,
        type: 3,
        originId: 4,
        left_goods_list: this.right_list,
        acctId: this.right_msg.acctId,
        localOperateTime: this.right_msg.createAt,
        storeName: this.username,
        money: Number(this.right_msg.discAmt).toFixed(2),
        checkPoint: 1
      };
      this.SET_SHOW({
        finalPayParam: {
          from: 'detail_member',
          cancel_vipData: cancel_vipData,
          accountId: this.right_msg.accountId,
          buy_back: 2,
          pc_return_goods: true,
          showFinalPrice: Number(this.right_msg.discAmt).toFixed(2),
          showMember: true,
          acctsId: 8,
          ifautoCash: false,
          ifpay: false,
          member_mobile: this.right_msg.vipmobile,
          member_id: this.right_msg.vipid,
          member_name: this.right_msg.vipname,
          left_goods_list: this.right_list,
          totalPrice: this.right_msg.billAmt,
          rightKeyword: '',
          preDisc: this.right_msg.discAmt,
          refundFingerprint: this.right_msg.fingerprint
        },
        settlement: true
      });
      this.reportFormLog(_.cloneDeep(this.finalPayParam), '销售明细预整单退货数据');
      const returnGoodList = [];
      this.right_list.forEach(good => {
        returnGoodList.push({
          name: good.goodName,
          sale_price: good.price,
          salePrice: good.price,
          number: good.qty,
          amt: good.amt
        })
      })
      const subData = {
        'screen2ShowList': returnGoodList,
        'screen2ReducePrice': Number(this.right_msg.billAmt) - Number(this.right_msg.discAmt),
        'screen2ShowFinalPrice': Number(this.right_msg.discAmt).toFixed(2),
        'screen2ShowMember': false,
        screen2ReturnGoods: true
      };
      demo.screen2(subData, 24);
    },
    exportSalesExcel(exportList) {
      var field_mapping = {
        单号: 'code',
        日期: 'createAt',
        单据类型: 'inOut',
        支付方式: 'accountName',
        收银员: 'employeeNumber',
        会员名: 'vipname',
        商品名称: 'goodName',
        条码: 'goodsCode',
        分类: 'category',
        数量: 'qty',
        单价: 'price',
        折扣: 'itemDisc',
        小计: 'amt',
        应收金额: 'discAmt',
        优惠: 'prefAmt',
        实收金额: 'actualReceiveAmt',
        找零金额: 'changeAmt',
        备注: 'remark'
      };
      if (!this.$employeeAuth('purchase_price')) {
        field_mapping['单笔利润'] = 'profits';
      }
      field_mapping['退货状态'] = 'isRefund';
      field_mapping['关联单号'] = 'associated';
      if (exportList.length === 0) {
        demo.msg('warning', '销售明细商品列表不能为空');
      } else {
        exportList.forEach(item => {
          item.goodsCode = item.goodsCode || '';
          item.category = item.category || '-';
          item.actualReceiveAmt = item.inOut === '退款' ? item.discAmt : item.discAmt + item.changeAmt;
          item.changeAmt = item.inOut === '退款' ? 0 : item.changeAmt;
        });
        this.$makeExcel(exportList, field_mapping, '销售明细' + new Date().format('yyyyMMddhhmmss'));
      }
      this.loading = false;
    },
    exportAddExcel(exportList) {
      var field_mapping = {};
      if (!this.$employeeAuth('purchase_price')) {
        field_mapping = {
          单号: 'code',
          日期: 'createTime',
          单据类型: 'inOutName',
          供应商: 'supplierName',
          操作人: 'employeeNumber',
          商品名称: 'itemName',
          商品条码: 'itemCode',
          进货价: 'purPrice',
          单位: 'unitName',
          数量: 'qty',
          金额: 'price',
          合计金额: 'billAmt',
          '折扣率(%)': 'disc',
          本单实付: 'payAmt',
          账户: 'accountName',
          备注: 'remark'
        };
      } else {
        field_mapping = {
          单号: 'code',
          日期: 'createTime',
          单据类型: 'inOutName',
          供应商: 'supplierName',
          操作人: 'employeeNumber',
          商品名称: 'itemName',
          商品条码: 'itemCode',
          单位: 'unitName',
          数量: 'qty',
          '折扣率(%)': 'disc',
          账户: 'account_name',
          备注: 'remark'
        };
      }
      if (exportList.length === 0) {
        demo.msg('warning', '进货明细商品列表不能为空');
      } else {
        exportList.forEach(item => {
          item.itemCode = item.itemCode || '-';
          item.supplierName = item.supplierName || '-';
        });
        this.$makeExcel(exportList, field_mapping, '进货明细' + new Date().format('yyyyMMddhhmmss'));
      }
      this.loading = false;
    },
    inputFocus() {
      this.keyword = '';
      this.$refs.goods_keyword.focus();
    },
    inputFocusVip() {
      this.vipKeyword = '';
      this.$refs.vip_keyword.focus();
    },
    formatNumber(n) {
      var num = n;
      if (Number(n).toString().indexOf('.') !== -1) {
        if (Number(n).toString().split('.')[1].length > 2) {
          num = Number(n).toFixed(3);
        } else {
          num = Number(n).toFixed(2);
        }
      } else {
        num = Number(n).toFixed(2);
      }
      return num;
    },
    openSetting() {
      this.SET_SHOW({ isPrintSetting: true });
    },
    print_list() {
      var that = this;
      let list = [];
      let payWay1 = '';
      let payWay2 = '';
      let combinedFinalPay = [];
      if (this.print_click === true) {
        return;
      }
      demo.actionLog(logList.clickSaleDetailPrint);
      this.print_click = true;
      setTimeout(function () {
        that.print_click = false;
      }, that.clickInterval);
      try {
        if (this.setting_small_printer === undefined || this.setting_small_printer === null || this.setting_small_printer.trim() === '') {
          this.openSetting();
          return;
        }
        console.log(this.right_msg, 'this.right_msg');
        let date = new Date(Date.parse(this.right_msg.createAt));
        if (this.combinedItem.length === 2) {
          payWay1 = this.combinedItem[0].accountName === '会员卡' ? '会员支付' : this.combinedItem[0].accountName;
          payWay2 = this.combinedItem[1].accountName === '会员卡' ? '会员支付' : this.combinedItem[1].accountName;
          combinedFinalPay.push(Number(this.combinedItem[0].payAmt).toFixed(2));
          combinedFinalPay.push(Number(this.combinedItem[1].payAmt).toFixed(2));
        }
        if (that.right_msg.inOut === 2) {
          list = _.cloneDeep(this.right_list).map(item => {
            return {
              ...item,
              number: Math.abs(item.qty),
              amt: Math.abs(item.amt)
            }
          })
        } else {
          list = _.cloneDeep(this.right_list);
        }
        var print_data = {
          printername: that.setting_small_printer,
          combinedFinalPay: combinedFinalPay,
          payWay1: payWay1,
          payWay2: payWay2,
          storename: that.username + '（补）',
          operater: this.right_msg.uid_no,
          accts: this.right_msg.accountName === '会员卡' ? '会员支付' : this.right_msg.accountName,
          pay_amt: (Number(this.right_msg.payAmt) + Number(this.right_msg.changeAmt)).toFixed(2),
          change_amt: Number(this.right_msg.changeAmt).toFixed(2),
          createAt: date.format('yyyy-MM-dd hh:mm:ss'),
          remark: 'Tel:' + that.phone,
          goods: list,
          printNum: demo.$store.state.show.smallPrinterCopies
        };
        this.reportFormLog(_.cloneDeep(print_data), '销售明细打印');
        let vipInfo = {};
        if (this.right_msg.info1 && JSON.parse(this.right_msg.info1).vipInfo && this.right_msg.vipid) {
          vipInfo = JSON.parse(this.right_msg.info1).vipInfo || {};
          vipInfo.member_name = '会员名';
          vipInfo.member_value = this.right_msg.vipname;
          vipInfo.member_money_name = '余额';
          vipInfo.member_mobile_name = '会员手机号';
          vipInfo.member_mobile_value =
            this.right_msg.vipmobile.toString().substring(0, 3) + '****' + this.right_msg.vipmobile.toString().substring(7, 11);
          vipInfo.member_point_name = '积分';
        }
        console.log(print_data, '带你打印');
        pos.printer.printPOSTimes(Object.assign(print_data, vipInfo),
          Object.assign({'from': 'sales_detail', 'buy_back': that.right_msg.inOut === 2 ? 2 : 1, 'orderno': that.right_msg.code}));
      } catch (e) {
        demo.msg('warning', '打印设备异常，请检查小票打印机！');
      }
    },
    /**
     * 退货
     */
    continue_delete_goods() {
      this.offlineRefundShow = false;
      for (var i = 0; i < this.right_list.length; i++) {
        this.right_list[i].disc = (Number(this.right_list[i].itemDisc) * 100).toFixed(0);
        this.right_list[i].isScales = true;
      }
      // 记录销售明细退货数据log
      let logObj = {};
      logObj['returnGoods'] = _.cloneDeep(this.right_list);
      Object.assign(logObj, this.right_msg);
      demo.screen2({ screen2ReturnGoods: true }, 10);
      this.SET_SHOW({
        isDetail: false,
        pc_return_goods: true,
        isPay: true,
        return_goods: this.right_list,
        returnGoodsMsg: this.right_msg
      });
    },
    continue_delete_order() {
      var that = this;
      if (this.right_msg.inOut !== 1) {
        var m = '退货单';
      } else {
        var m = '进货单';
      }
      purchaseService.deleteBatch({ id: this.right_msg.id }, function () {
        demo.msg('success', '删除' + m + '详情成功');
        that.get_leftList();
      });
    },
    getDayNumByYearMonth(year, month) {
      switch (Number(month)) {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
          return 31;
        case 4:
        case 6:
        case 9:
        case 11:
          return 30;
        case 2:
          return this.isLeapYear(year) ? 29 : 28;
      }
    },
    // 是否是闰年
    isLeapYear(year) {
      if (year / 4 == 0 && year / 100 != 0) {
        return true;
      } else if (year / 400 == 0) {
        return true;
      } else {
        return false;
      }
    },
    format_time(time) {
      return time.split(' ')[1];
    },
    listenResize() {
      // 浏览器高度$(window).height()
      if (this.clientSize) {
        clearTimeout(this.clientSize);
      }
      this.clientSize = setTimeout(() => {
        // console.log(that.$refs.searchHeaderCon.offsetHeight, 'searchHeaderCon');
        if (this.$refs.searchHeaderCon.offsetHeight > 170) {
          this.saleContentClass = 'small-content';
        } else if (this.$refs.searchHeaderCon.offsetHeight > 64) {
          this.saleContentClass = 'mid-content';
        } else {
          this.searchDivbr = false;
          this.saleContentClass = 'large-content';
        }
      }, this.delayedTime);
    },
    goodsDetail(json) {
      var that = this;
      if (this.pc_detail_tab === 1) {
        // 销售明细的单条商品详情
        saleService.detail(json.id, json.fingerprint, function (res) {
          console.log(res, 3333333);
          clerkService.getEmployeeNumberByUid(res.sales, ['uid'], r => {
            console.log(r, '销售明细', json);
            that.right_msg = demo.t2json(r)[0];
            that.right_msg.vipname = json.vipname;
            that.right_msg.refundStatus = json.refundStatus || '';
            that.right_msg.vipid = json.vipid;
            that.right_msg.vipmobile = json.vipmobile;
            that.right_msg.info1 = json.info1;
            that.right_msg.info2 = json.info2;
          });
          that.right_list = res.saleitems;
          if (res.zuhe !== undefined) {
            // 组合支付详细数据
            that.combinedItem = res.zuhe;
          } else {
            that.combinedItem = [];
          }
        });
        if (json.inOut === 1) {
          // 销售单，查找退货关联
          saleService.getSaleByRefundFingerprint(
            json.fingerprint,
            res => {
              this.supplierShow = true;
              this.returnsList = _.cloneDeep(res);
            },
            err => {
              console.log(err);
            }
          );
        } else {
          // 退款单，查找进货关联
          saleService.getSaleByFingerprint(
            json.refundFingerprint,
            res => {
              this.supplierShow = false;
              this.returnsList = _.cloneDeep(res);
            },
            err => {
              console.log(err);
            }
          );
        }
      } else if (this.pc_detail_tab === 2) {
        // 进货明细的单条商品详情
        purchaseService.detail(json.id, function (res) {
          // that.right_msg = res.bill;
          clerkService.getEmployeeNumberByUid([res.bill], ['uid'], r => {
            console.log(r, '进货明细');
            that.right_msg = demo.t2json(r)[0];
          });
          that.right_list = res.items;
        });
      } else {
        console.log('其他');
      }
    },
    getMakerListData() {
      // 获取供应商列表数据
      supplierService.getDetailSupplierDropDownList(res => {
        this.makerlist = [
          {
            value: '',
            label: '全部供应商'
          }
        ];
        let makers = demo.t2json(res);
        console.log('获取供应商列表', res);
        for (let mk of makers) {
          this.makerlist.push({
            value: mk.fingerprint,
            label: mk.name
          });
        }
      });
    },
    sonarGetLeftList(res, flag) {
      console.log('sonarGetLeftList res', res);
      // 这个接口返回空，为res = {}
      if (JSON.stringify(res) == '{}' || JSON.stringify(res) == '[]') {
        this.leftList = [];
        this.total_all_money = 0;
        this.total_oreder = 0;
        this.waitingResponse = false;
      } else {
        if (flag) {
          var mid_list = res.days.length === 0 ? [] : res.days[0].details;
          this.leftList = mid_list;
          this.leftList_total = res.days;
          this.total_oreder = res.salesCount;
          this.total_all_money = res.totalMoney;
          document.getElementById('leftList_id').scrollTop = 0;
          this.waitingResponse = false;
        } else {
          this.infiniteHandler(res);
        }
      }
      if (!flag && res.days && res.days.length === 0) {
        this.pageNum--;
        demo.msg('warning', '暂无更多数据');
      }
      if (this.leftList.length === 0) {
        demo.msg('warning', '暂无数据');
      }
    },
    infiniteHandler(res) {
      if (res.days.length !== 0) {
        let preScrollHeight = document.getElementById('leftList_id').scrollTop;
        let leftList_total_new = this.leftList_total.concat(res.days);
        this.uniqueTotal(leftList_total_new);
        this.$nextTick(() => {
          this.waitingResponse = false;
          document.getElementById('leftList_id').scrollTop = preScrollHeight;
        });
      } else {
        this.waitingResponse = false;
      }
    },
    uniqueTotal(leftList_total_new) {
      console.log('leftList_total_new++', leftList_total_new);
      this.leftList_total = leftList_total_new.reduce((accumulator, curObj) => {
        if (!(accumulator instanceof Array)) {
          accumulator = [].concat(accumulator);
          if (curObj.optDate === accumulator[0].opDate) {
            accumulator[0].details = accumulator[0].details.concat(curObj.details);
          } else {
            accumulator = accumulator.concat(curObj || []);
          }
        } else {
          let tmp = _.cloneDeep(accumulator).pop();
          if (tmp.optDate !== curObj.optDate) {
            accumulator = accumulator.concat(curObj);
          } else {
            tmp = tmp.details.concat(curObj.details);
            accumulator[accumulator.length - 1].details = tmp;
          }
        }
        return accumulator;
      });
    },
    setPageNum(flag) {
      if (flag) {
        this.pageNum = 1;
      }
    },
    infiniteScrollLoad() {
      if (this.waitingResponse || document.getElementById('leftList_id').scrollTop === 0) {
        return;
      }
      this.pageNum++;
      this.get_leftList();
    },
    /**
     * 销售明细/进货明细报表查询
     * @params {Boolean} flag 非滚动加载查询
     * @params {Boolean} logFlg 是否记录行为log
     */
    get_leftList(flag, logFlg) {
      this.allow_list = true;
      var that = this;
      if (this.pc_detail_tab === 1) {
        if (this.waitingResponse) {
          return;
        }
        this.waitingResponse = true;
        // 销售明细左侧列表
        this.setPageNum(flag);
        this.dateReverse('date_picker');
        this.loading = true;
        var data = {
          keyword: this.keyword,
          from: demo.toDataFormat(this.date_picker.date_from, 'yyyy-MM-dd hh:mm:ss'),
          to: demo.toDataFormat(this.date_picker.date_to, 'yyyy-MM-dd hh:mm:ss'),
          inOut: this.inOut,
          accountId: Number(this.accountId),
          condition: this.keyword,
          isSearch: flag ? 1 : 0,
          pageSize: this.limit,
          offset: (this.pageNum - 1) * this.limit,
          vip: this.vipKeyword,
          employee: this.employeeVal
        };
        console.log(data, '请求销售明细参数');
        saleService.search(
          data,
          res => {
            setTimeout(() => {
              this.loading = false;
            }, this.delayedTime);
            this.sonarGetLeftList(res, flag);
          },
          err => {
            this.waitingResponse = false;
            this.pageNum--;
            console.error(err);
            setTimeout(() => {
              this.loading = false;
            }, this.delayedTime);
          }
        );
      } else if (this.pc_detail_tab === 2) {
        // 进货明细
        this.dateReverse('date_picker2');
        that.loading = true;
        var data = {
          keyword: this.keyword,
          from: demo.toDataFormat(this.date_picker2.date_from, 'yyyy-MM-dd'),
          to: demo.toDataFormat(this.date_picker2.date_to, 'yyyy-MM-dd'),
          search_data: '',
          supplierFingerprint: this.companyId,
          accountId: this.accountId,
          inOut: this.inOut,
          page_size: 700000,
          page_num: '1'
        };
        purchaseService.search(data, function(res) {
          setTimeout(() => {
            that.loading = false;
          }, that.delayedTime);
          console.log(res, 111);
          var rd = demo.t2json(JSON.stringify(res));
          var mid_list = rd.days[0] === undefined ? [] : rd.days[0].details;
          that.leftList = mid_list;
          that.leftList_total = rd.days;
          console.log(that.leftList_total, 'that.leftList_total');
          that.total_all_money = rd.totalMoney;
          that.total_oreder = rd.pursCount;
          if (res.days.length === 0) {
            demo.msg('warning', '暂无数据');
          }
        },
        err => {
          console.log(err);
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        }
        );
      } else if (this.pc_detail_tab === 3) {
        // 盘点明细
        setTimeout(() => {
          thousandSeparation.loading = false;
        }, that.delayedTime);
      } else {
        console.log('Unexpected value for detail');
        setTimeout(() => {
          that.loading = false;
        }, that.delayedTime);
      }
    },
    backCompleteConfirm(boolean) {
      this.SET_SHOW({ showBackCompleteDialog: false });
      if (boolean) {
        this.SET_SHOW({ isDetail: false, isPay: true, pc_detail_tab: 1, fromDetail: 0 });
      }
    },
    reportFormLog(sub_data, description) {
      // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({ page: 'detail', action: 'reportFormLog', description });
      }
    },
    dateFormat(dateStr) {
      return dateStr ? dateStr.replace('T', ' ').split('.')[0] : dateStr;
    },
    timeFormat(dateStr) {
      return new Date(dateStr).format('hh:mm:ss');
    },
    dateReverse(prop) {
      if (new Date(this[prop].date_from).getTime() > new Date(this[prop].date_to).getTime() && this[prop].date_to) {
        let cp = _.cloneDeep(this[prop]);
        this[prop].date_from = cp.date_to;
        this[prop].date_to = cp.date_from;
      }
    },
    getExcel() {
      var that = this;
      this.loading = true;
      if (this.pc_detail_tab === 1) {
        // 销售明细左侧列表
        this.dateReverse('date_picker');
        var data = {
          keyword: this.keyword,
          from: demo.toDataFormat(this.date_picker.date_from, 'yyyy-MM-dd hh:mm:ss'),
          to: demo.toDataFormat(this.date_picker.date_to, 'yyyy-MM-dd hh:mm:ss'),
          inOut: this.inOut,
          accountId: Number(this.accountId),
          condition: this.keyword,
          limit: 700000,
          offset: 0,
          vip: this.vipKeyword,
          employee: this.employeeVal
        };
        console.log(data, '请求销售明细EXCEL导出参数+++');
        this.reportFormLog(_.cloneDeep(data), '销售明细导出表格');
        saleService.doSalesExcelExport(
          data,
          function (res) {
            var rd = demo.t2json(res).map(item => {
              const { saleCodes, refundCodes } = item;
              return {
                ...item,
                amt: isNaN(item.amt) ? item.amt : Number(item.amt),
                itemDisc: isNaN(item.itemDisc) ? item.itemDisc : Number(item.itemDisc),
                price: isNaN(item.price) ? item.price : Number(item.price),
                profits: isNaN(item.profits) ? item.profits : Number(item.profits),
                qty: isNaN(item.qty) ? item.qty : Number(item.qty),
                associated: saleCodes ? `原单号：${saleCodes}` : refundCodes ? `退货单：${refundCodes}` : ''
              };
            });

            if (rd.length > 0) {
              that.exportSalesExcel(rd);
            } else {
              that.loading = false;
              demo.msg('warning', '暂无符合条件数据，请重新选择条件');
            }
          },
          err => {
            that.loading = false;
            demo.msg('error', err);
          }
        );
      } else if (this.pc_detail_tab === 2) {
        // 进货明细
        this.dateReverse('date_picker2');
        var data = {
          keyword: this.keyword,
          from: demo.toDataFormat(this.date_picker2.date_from, 'yyyy-MM-dd'),
          to: demo.toDataFormat(this.date_picker2.date_to, 'yyyy-MM-dd'),
          search_data: '',
          supplierFingerprint: this.companyId,
          accountId: this.accountId,
          inOut: this.inOut,
          page_size: 700000,
          page_num: '1'
        };
        console.log('进货明细data+++', data);
        this.reportFormLog(_.cloneDeep(data), '进货明细导出表格');
        purchaseService.exportExcel(data, function (res) {
          var rd = demo.t2json(res).map(item => {
            return {
              ...item,
              'disc': item.disc === '0' ? '-' : Number(item.disc.replace(/%/g, '')),
              'price': isNaN(item.price) ? item.price : Number(item.price),
              'purPrice': item.purPrice === null ? '-' : Number(item.purPrice),
              'qty': isNaN(item.qty) ? item.qty : Number(item.qty)
            }
          });
          console.log(rd, '进货明细数据');
          if (rd.length > 0) {
            that.exportAddExcel(rd);
          } else {
            that.loading = false;
            demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          }
        });
      } else {
        this.loading = false;
        console.log('Unexpected value for detail');
      }
    }
  },
  computed: {
    combinedSettlementMethod() {
      return `${this.combinedItem[0].accountName}
        ¥${+this.combinedItem[0].acctId == 1 ? Number(this.right_msg.changeAmt + this.combinedItem[0].payAmt).toFixed(2) : Number(this.combinedItem[0].payAmt).toFixed(2)}
        ${this.combinedItem[1].accountName}
        ¥${+this.combinedItem[1].acctId == 1 ? Number(this.right_msg.changeAmt + this.combinedItem[1].payAmt).toFixed(2) : Number(this.combinedItem[1].payAmt).toFixed(2)}`;
    },
    ...mapState({
      isHome: state => state.show.isHome,
      detailBackPay: state => state.show.detailBackPay,
      isDetail: state => state.show.isDetail,
      pc_detail_tab: state => state.show.pc_detail_tab,
      finalPayParam: state => state.show.finalPayParam,
      showBackCompleteDialog: state => state.show.showBackCompleteDialog,
      fromDetail: state => state.show.fromDetail,
      settlement: state => state.show.settlement,
      loginInfo: state => state.show.loginInfo,
      setting_small_printer: state => state.show.setting_small_printer,
      username: state => state.show.username,
      phone: state => state.show.phone,
      screenWidth: state => state.show.screenWidth,
      delayedTime: state => state.show.delayedTime,
      clickInterval: state => state.show.clickInterval
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
  }
};
</script>
