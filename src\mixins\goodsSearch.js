const goodsSearch = {
  methods: {
    // 右侧商品列表滚动加载
    scrollFn() {
      if (this.stop_scrollFn || this.type === '-99') {
        return;
      }
      const listWrap = document.getElementById('stock_right_list_id');
      const clientH = listWrap.clientHeight;
      const scrollT = listWrap.scrollTop;
      const wholeH = listWrap.scrollHeight;
      if (clientH + scrollT + 50 >= wholeH) {
        this.stop_scrollFn = true;
        setTimeout(() => {
          this.stop_scrollFn = false;
        }, 1000);
        this.can_getProdList = true;
        this.getProdList();
      }
    },
    // 根据分类调用不同方法
    handleGoodList() {
      if (this.type === '-99') {
        this.getHotList();
      } else {
        this.getProdList();
      }
    },
    // 获取商品列表
    getProdList() {
      if (this.type === '-99' && this.keyword) {
        this.type = '';
      }
      if (this.can_getProdList === false || this.type === '-99') {
        return;
      }
      this.can_getProdList = false;
      setTimeout(() => {
        this.can_getProdList = true;
      }, 0);
      this.loading = true;
      const params = {
        pset: '',
        type: this.type,
        condition: this.keyword,
        limit: this.limit,
        selectDel: false,
        offset: (this.pagenum - 1) * this.limit,
        isGroup: false
      };
      goodService.search(params, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        const json = demo.t2json(res);
        if (this.pagenum === 1) {
          this.right_list = [];
        }
        this.right_list = this.right_list.concat(json);
        console.log(this.right_list, '进货页面右侧数据');
        this.watch_type = true;
        this.pagenum++;
        this.stop_scrollFn = false;
      }, () => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      });
    },
    // 获取热销商品列表
    getHotList() {
      this.keyword = '';
      goodService.hot(res => {
        const list = demo.t2json(res);
        this.right_list = list;
      });
    }
  }
};
export default goodsSearch;
