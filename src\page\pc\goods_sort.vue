<style lang="less" scoped>
/deep/.el-icon-arrow-down:before {
  content: "\e78f";
}
.pc_type_div {
  width: 110px;
  position: fixed;
  right: 6px;
  top: 136px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 206px);
  z-index: 2;
  overflow-y: auto;
  overflow-x: hidden;
  /deep/.el-menu-item {
    background-color: @areaColor !important;
    min-width: 110px !important;
    text-align: left;
    border-bottom: 1px solid #fff;
    padding-left: 10px !important;
  }
  /deep/.el-menu-item.is-active {
    color: #fff !important;
    background: @linearBackgroundColor !important;
  }
  /deep/.is-active {
    background-color: @areaColor;
  }
  /deep/.type_choose {
    background: @linearBackgroundColor !important;
  }
  /deep/.el-submenu__title {
    padding-left: 10px !important;
    background-color: @areaColor !important;
    border-bottom: 1px solid #fff;
    text-align: left;
    i {
      color: #fff;
      float: right;
      font-size: 18px;
      right: 5px;
      line-height: 10px;
    }
  }
  /deep/.el-submenu.is-active .el-submenu__title {
     border-bottom: 1px solid #fff !important;
  }
  /deep/.el-submenu.is-opened .el-menu-item {
    background-color:  @inventoryColor !important;
    text-align: left !important;
    padding-left: 20px !important;
    padding-right: 0px !important;
    border-bottom: 1px solid #fff !important;
    margin: 0 auto;
    width: 100%;
  }
}
.rightMain {
  width: 100%;
  display: inline-block;
  padding: 6px;
  .top {
    height: 40px;
    color: @themeBackGroundColor;
    font-size: 14px;
    background: #FFFAF0;
    line-height: 40px;
    padding-left: 12px;
    border-radius: 4px;
  }
}
.pc_sort1 {
  width: 74px;
  height: 74px;
  font-size: 21px;
  font-weight: bold;
  line-height: 37px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 10px;
  border-radius: 2px;
  overflow: hidden;
  letter-spacing: 10px;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_sort2 {
  width: 70px;
  height: 70px;
  font-size: 21px;
  font-weight: bold;
  line-height: 70px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_sort3 {
  float: left;
}
.pc_sort3 p {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  height: 42px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 500;
  margin-top: 11px;
  margin-bottom: 0;
  line-height: 20px;
}
.namePriceEllipsis {
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.pc_sort4 {
  float: left;
  font-weight: bold;
  font-size: 13px;
  margin-left: 11px;
  line-height: 18px;
}

.pc_sort {
  width: 100%;
  color: @themeFontColor;
  background: #fff;
  border-radius: 8px;
  padding: 20px 120px 20px 20px;
}
.pc_sort5 {
  height: calc(100vh - 131px);
  overflow: scroll;
}
.pc_sort6 {
  width: 212px;
  height: 124px;
  border: 1px solid #e3e6eb;
  background: #f5f8fb;
  border-radius: 4px;
  margin-bottom: 8px;
  margin-right: 8px;
  overflow: hidden;
  float: left;
  position: relative;
}
.pc_sort6:hover {
  background: @themeBackGroundColor;
  cursor: pointer;
  box-shadow: 2px 2px 4px #999;
  color: #fff;
  border-color: @themeBackGroundColor;
}
.pc_sort6 img {
  width: 70px;
  height: 70px;
  float: left;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_sort6 p {
  margin-left: 11px;
  width: 116px;
  margin-top: 4px;
}
.pc_sort7 {
  width: 70px;
  height: 70px;
  font-size: 21px;
  font-weight: bold;
  line-height: 33px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-indent: 11px;
  border-radius: 20px;
  overflow: hidden;
  letter-spacing: 6px;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_sort8 {
  width: 70px;
  height: 70px;
  font-size: 21px;
  font-weight: bold;
  line-height: 70px;
  float: left;
  background: @themeBackGroundColor;
  color: #fff;
  text-align: center;
  border-radius: 20px;
  margin-top: 10px;
  margin-left: 10px;
}
.pc_sort9 {
  float: left;
}
.pc_sort9 p {
  white-space: normal;
  word-wrap: break-word;
  word-break: break-all;
  height: 72px;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-weight: 500;
  margin-top: 11px;
  margin-bottom: 0;
  line-height: 24px;
}
.pc_sort10 {
  margin-left: 14px;
  line-height: 16px;
  margin-top: 9px;
  div {
    float: right;
    font-weight: bold;
    font-size: 16px;
    margin-right: 10px;
  }
}
.tag {
  position: absolute;
  top: 0px;
  left: 0px;
  background: #ff4d4f;
  border-radius: 8px 0px;
  width: 32px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: rgb(255, 255, 255);
  z-index: 2;
}
.meal-weight {
  position: relative;
  top: -20px;
  text-align: center;
  margin-left: 12px;
  width: 74px;
  font-size: 14px;
  color: #FFFFFF;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
}
.stock-style {
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.6);
  position: absolute;
  left: 10px;
  top: 60px;
  width: 70px;
  height: 20px;
  text-align: center;
  font-weight: 500;
  font-size: 13px;
}
.bold {
  font-weight: bold;
}
.parent_menu_active_div {
  background: @linearBackgroundColor !important;
  width: 110px;
  margin-left: -10px;
  padding-left: 10px;
  height: 56px;
  border-bottom: 1px solid #fff;
}
.type_name{
  width: 79px;
  line-height: 1.3;
  white-space: normal;
  max-height:50px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  word-wrap:break-word;
}
.drag_item {
  opacity: 1 !important;
  background-color: #F5F0E3;
  position: relative;
}
// .drag_item::before {
//   color: #fff;
//   background: #868c92;
//   padding: 1px 5px;
//   content: '按住';
//   position: absolute;
//   right: 30px;
//   bottom: 40px;
// }
</style>
<template>
  <div style="background: #F2F5F7;">
    <div class="rightMain">
      <div v-if="showMsg" class="top">
        <span class="bold">拖动商品</span>调整顺序
        <span @click="showMsg = false" style="cursor: pointer;float: right;margin-right: 20px;">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.99999 7.05732L11.3 3.75732L12.2427 4.69999L8.94266 7.99999L12.2427
              11.3L11.3 12.2427L7.99999 8.94266L4.69999 12.2427L3.75732 11.3L7.05732
              7.99999L3.75732 4.69999L4.69999 3.75732L7.99999 7.05732Z" fill="black"/>
          </svg>
        </span>
      </div>
      <!-- 商品父div -->
      <div class="pc_sort" :style="showMsg ? 'height: calc(100vh - 110px)' : 'height: calc(100vh - 70px)'">
        <div class="pc_sort5"
          v-if="showSort"
          v-infinite-scroll="loadMore"
          :infinite-scroll-disabled="loading"
          :infinite-scroll-immediate="true">
          <draggable
            filter=".unmover"
            dragClass="drag_item"
            element="div"
            v-model="goodsList"
            :delay="0"
            :disabled="dragDisable"
            @update="moveListEnd"
            forceFallback="true"
            animation="300">
            <transition-group>
              <!--每一个商品div-->
              <div v-for="(rg,index) in goodsList"
                :key="'good' + index"
                :style="screenWidth < 1025 ? 'width: 213px;' : ''"
                class="pc_sort6">
                <div style="overflow: hidden;">
                  <!--商品图片-->
                  <img v-lazy="$getSrcUrl(rg.image)" v-if="rg.image != '' && rg.image != null" :src="$getSrcUrl(rg.image)" />
                  <img v-show="(rg.image == '' || rg.image == null) && rg.pinyin == ''" src="../../image/pc_no_cloth_img.png"/>
                  <div v-show="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length > 2" class="pc_sort7">
                      <span >{{rg.pinyin.length > 2 ? rg.pinyin.substring(0,2) : ''}}</span><br>
                      <span style="margin-left: 10px;">{{rg.pinyin.substring(2,(rg.pinyin.length > 4 ? 4 : rg.pinyin.length))}}</span>
                  </div>
                  <div v-show="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length <= 2 && rg.pinyin.length > 0" class="pc_sort8">
                    {{rg.pinyin}}
                  </div>
                  <div v-if="set_StockShow" class="stock-style"
                    :style="(rg.image == '' || rg.image == null) && rg.pinyin !== '' && rg.pinyin.length > 0 ? 'border-radius: 0px 0px 20px 20px;' : 'border-radius: 0px;'">
                    {{(rg.curStock + '').length > 5 ? '' : '库存:'}}
                    {{rg.curStock || 0}}
                  </div>
                  <div class="pc_sort9">
                    <!--商品名字-->
                    <p :title="rg.name">{{rg.name}}</p>
                  </div>
                </div>
                <div class="pc_sort10">
                  <div class="pc_pay163">¥&nbsp;{{Number(rg.salePrice).toFixed(2)}}</div>
                </div>
              </div>
            </transition-group>
          </draggable>
        </div>
      </div>
    </div>
    <div style="width: 101px;height: calc(100% - 50px);position: relative;float: right;overflow-y: scroll;">
      <div class="pc_type_div">
        <el-menu
          class="el-menu-vertical-demo"
          @open="handleOpen"
          @close="handleClose"
          @select="handleSelect"
          :unique-opened=true
          background-color="#545c64"
          text-color="#fff"
          font-size="16px"
          ref="elMenu"
          active-text-color="#ffd04b">
          <div>
            <el-menu-item index="_menu" :class="type === '' ? 'type_choose' : ''">全部分类</el-menu-item>
          </div>
          <div v-if="show_weighing_category">
            <el-menu-item index="0_menu" :class="type === '0' ? 'type_choose' : ''">称重分类</el-menu-item>
          </div>
          <div v-for="tp in typeArr" :key="tp.id">
            <el-menu-item :index="tp.id + '_menu'" :class="type == tp.id ? 'type_choose' : ''" v-if="tp.list.length === 0">
              <span class="type_name">{{tp.name}}</span>
            </el-menu-item>
            <el-submenu :index="tp.id + '_submenu'"
              :class="tp.id === type ? 'type_choose' : ''"
              v-if="tp.list.length !== 0">
              <template slot="title">
                <div :class="type == tp.id ? 'parent_menu_active_div' : ''">
                  <span class="type_name">{{tp.name}}</span>
                </div>
              </template>
              <el-menu-item :style="$t('image.homeImage.menuItem')"
                v-for="item in tp.list" :key="item.id" :index="item.id + '_sub'">
                <span class="type_name">{{item.name}}</span>
              </el-menu-item>
            </el-submenu>
          </div>
        </el-menu>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data() {
    return {
      show_weighing_category: false,
      type: '',
      typeArr: [],
      goodsList: [],
      pageNum: 0,
      limit: 100,
      set_StockShow: false,
      showMsg: true,
      showSort: false,
      dragDisable: false,
      loading: true
    }
  },
  created() {
    this.getAllCategory();
    this.pdWeighingCategory();
  },
  mounted() {
    this.addListenevent();
    this.initSort();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    initSort() { // 商品初始化排序
      let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      this.set_StockShow = dataInfo.set_StockShow ? demo.t2json(dataInfo.set_StockShow) : false;

      var m_list = _.cloneDeep(this.storeList);
      var mid = demo.t2json(m_list[0].settings || {});
      if (!mid.isNotOnceOpenSortPage) {
        // 第一次进入排序页面调用排序初始化方法补齐sort表数据
        goodService.sortInit(() => {
          CefSharp.PostMessage('首次进入排序页面调用sortInit补齐goods_attribute数据');
          mid.isNotOnceOpenSortPage = 1;
          m_list[0].settings = JSON.stringify(mid);
          this.SET_SHOW({storeList: m_list});
          storeInfoService.updateSettings({'id': 1, 'settings': JSON.stringify(mid)}, () => {}, () => {});
          this.initSuccess();
        });
      } else {
        this.initSuccess();
      }
    },
    initSuccess() {
      goodService.initGoodsSort(() => {
        this.loading = false;
        this.showSort = true;
      }, () => {
        demo.msg('error', '系统出错了，请稍后重试');
        this.SET_SHOW({ isGoodsSort: false, isPay: true });
      })
    },
    moveListEnd(e) {
      /**
       * 拖动完成后更新商品的sort值
       * 假设被拖动商品为商品C
       * params.fingerprint : 商品C的fingerprint
       * params.sort：商品C要更新成的sort 往前拖的话就是拖动完成后商品C后面商品的sort值
       */
      let isDragBackwards = e.newIndex > e.oldIndex;
      let params = {
        fingerprint: this.goodsList[e.newIndex].fingerprint,
        sort: isDragBackwards ? this.goodsList[e.newIndex - 1].sort : this.goodsList[e.newIndex + 1].sort,
        sortRule: isDragBackwards ? 'sort - 1' : 'sort + 1',
        beginSort: isDragBackwards ? this.goodsList[e.newIndex].sort : this.goodsList[e.newIndex + 1].sort,
        endSort: isDragBackwards ? this.goodsList[e.newIndex - 1].sort : this.goodsList[e.newIndex].sort
      }
      this.dragDisable = true;
      goodService.updateGoodSort(params, res => {
        let minIndex = isDragBackwards ? e.oldIndex : e.newIndex;
        let maxIndex = isDragBackwards ? e.newIndex : e.oldIndex;
        console.log(`minIndex:${minIndex}, maxIndex:${maxIndex}`);
        for (let i = minIndex; i <= maxIndex; i++) {
          this.goodsList[i].sort = res.find(newItem => newItem.fingerprint === this.goodsList[i].fingerprint).sort;
        }
        this.dragDisable = false;
      }, () => {
        // todo
        this.dragDisable = false;
      });
    },
    // 获取所有分类
    getAllCategory() {
      typeService.search(res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          this.typeArr = json;
        }
      });
    },
    loadMore() { // 滚动加载商品
      this.loading = true;
      this.pageNum += 1;
      this.getProdList();
    },
    // 获取产品列表
    getProdList() {
      console.log(`getProdList执行1次+ page:${this.pageNum}`);
      let data = {
        pset: '',
        isGroup: false,
        type: this.type,
        condition: '',
        selectDel: false,
        getDel: false,
        limit: this.limit,
        offset: (this.pageNum - 1) * this.limit,
        orderBy: [
          {
            column: 'ga.sort',
            order: 'asc'
          },
          {
            column: 'a.create_at',
            order: 'desc'
          }
        ]
      };
      let list = _.cloneDeep(this.goodsList);
      goodService.search(data, res => {
        res = _.sortBy(res, o => { return o.sort; });
        if (res.length === 0) {
          return;
        }
        this.goodsList = list.concat(res);
        this.loading = false;
        console.log(`concat time:${new Date().getTime()}, page:${this.pageNum}`);
      }, () => {
        // error
      });
    },
    handleOpen(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleClose(key) {
      this.$refs.elMenu.activeIndex = key;
      this.type = key.split('_')[0];
    },
    handleSelect(key) {
      this.type = key.split('_')[0];
    },
    addListenevent() {
      document.body.addEventListener('keyup', this.keyup, false);
    },
    cancelListenevent() {
      document.body.removeEventListener('keyup', this.keyup, false);
    },
    pdWeighingCategory() {
      unitService.weight(res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          this.show_weighing_category = true;
        } else {
          this.show_weighing_category = false;
        }
      });
    },
    keyup(e) {
      var code = e.which;
      if (code === 27) {
        this.SET_SHOW({ isGoodsSort: false, isPay: true });
      }
    }
  },
  watch: {
    type() {
      if (this.type === '') {
        this.$refs.elMenu.activeIndex = null;
      }
      console.log(`type change time:${new Date().getTime()}, page:${this.pageNum}`);
      this.goodsList = [];
      this.showSort = false;
      setTimeout(() => {
        this.pageNum = 0;
        this.showSort = true;
        this.loading = false;
      });
    },
    pageNum() {
      console.log('pageNum watch:', this.pageNum);
    },
    loading() {
      console.log('this.loading:', this.loading);
    }
  },
  beforeDestroy() {
    this.cancelListenevent();
  },
  computed: mapState({
    storeList: state => state.show.storeList,
    screenWidth: state => state.show.screenWidth
  })
}
</script>
