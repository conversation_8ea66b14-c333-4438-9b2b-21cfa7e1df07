<style lang="less" scoped>
.siz_set1 {
  width: calc(100% - 268px);margin: 10px auto;height: calc(100% - 70px);
  background: #FFF;overflow: hidden;border: 1px solid #E3E6EB;
}
.siz_set11 {
  width: calc(100% - 260px);margin-left: 12px;float: left;
}
.siz_set12 {
  width: 120px;height: 40px;line-height: 40px;font-weight: bold;text-align: center;float: left;margin-right: 16px;
  margin-top: 15px;background: @themeButtonBackGroundColor;color: @themeBackGroundColor;border-radius: 18px;cursor: pointer;
}
.siz_set13 {
  overflow: hidden;margin-top: 2px;
}
.siz_set14 {
  width: 130px;text-indent: 3px;float: left;font-weight: bold;
  color: @themeFontColor;line-height: 20px;margin-top: 28px;
}
.siz_set15 {
  width: calc(100% - 130px);float: left;
}
.siz_set16 {
  float: left;height: 32px;line-height: 32px;padding-left: 12px;padding-right: 12px;background: #F2F2F2;
  margin-right: 10px;border-radius: 16px;margin-top: 10px;text-align: center;cursor: pointer;
}
.siz_set17 {
  float: left;height: 32px;line-height: 32px;background: @themeBackGroundColor;color: #FFF;
  border-radius: 16px;margin-top: 10px;width: 60px;text-align: center;cursor: pointer;
}
.siz_set18 {
  width: 100%;height: 1px;background: #E5E8EC;margin-top: 20px;
}
.siz_set19 {
  position: relative;float: left;width: 242px;border-right: 1px solid #E3E6EB;height: 100%;
  border-radius: 8px 0 0 8px;background: #fff;
}
.siz_set2 {
  min-height: 54px;overflow: hidden;text-overflow: ellipsis;font-size: 16px;cursor: pointer;
}
.siz_set21 {
  min-height: 54px;overflow: hidden;text-overflow: ellipsis;font-size: 16px;cursor: pointer;
  background: linear-gradient(90deg, #D0B889 0%, #B59A5B 100%);color: #FFF;
}
.siz_set22 {
  float: right;
}
.siz_set22 img {
  float: left;margin-right: 12px;cursor: pointer;
}
/deep/ .el-table th, .el-table tr {
  background-color: #F5F7FA;
}
.siz_set23 {
  display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;
  float: left;margin-left: 17px;width: 160px;word-wrap:break-word;
}
.siz_set24 {
  width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;
}
.siz_set25 {
  width: 450px;height: 245px;background: #FFF;margin: 0 auto;margin-top: 300px;
}
.siz_set26 {
  line-height: 20px;width: 100%;color: @themeBackGroundColor;overflow: hidden;
}
.siz_set27 {
  margin-left: 25px;font-size: 20px;margin-top: 22px;font-weight: bold;color: #DEB071;
}
.siz_set28 {
  float: right;font-size: 25px;margin-right: 28px;margin-top: 18px;color: #8197A6;cursor: pointer;
}
.siz_set29 {
  width: calc(100% - 64px);margin-top: 27px;margin-left: 32px;
}
.siz_set3 {
  line-height: 14px;font-size: 14px;margin-top: 10px;margin-left: 32px;overflow: hidden;
}
.siz_set31 {
  float: left;width: 14px;height: 14px;text-align: center;line-height: 14px;color: #FFF;
  background: #F9AE1B;border-radius: 50%;font-size: 12px;text-indent: 2px;
}
.siz_set32 {
  float: left;margin-left: 5px;color: #F9AE1B;
}
.siz_set33 {
  overflow: hidden;margin-top: 27px;
}
.siz_set34 {
  width: 112px;height: 47px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: @themeBackGroundColor;
  line-height: 45px;text-align: center;font-size: 16px;float: left;margin-left: 170px;
}
.siz_set35 {
  width: 112px;height: 47px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: #FFF;
  line-height: 45px;text-align: center;font-size: 16px;background: @themeBackGroundColor;float: left;margin-left: 20px;
}
.siz_set36 {
  width: 100%;height: 100%;position: fixed;top: 0;left: 0;background: rgba(0,0,0,.5);z-index: 200;
}
.siz_set37 {
  width: 450px;height: 335px;background: #FFF;margin: 0 auto;margin-top: 220px;border-radius: 6px;
}
.siz_set38 {
  line-height: 20px;width: 100%;color: @themeBackGroundColor;overflow: hidden;
}
.siz_set39 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;font-weight: bold;margin-top: 40px;
}
.siz_set4 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;margin-top: 50px;
}
.siz_set41 {
  text-align: center;line-height: 24px;font-size: 24px;color: @themeFontColor;margin-top: 18px;
}
.siz_set42 {
  text-align: center;line-height: 18px;font-size: 18px;color: #F4725F;margin-top: 18px;
}
.siz_set43 {
  overflow: hidden;margin-top: 55px;
}
.siz_set44 {
  width: 140px;height: 50px;border: 1px solid @themeFontColor;border-radius: 4px;color: #FFF;line-height: 45px;cursor: pointer;
  text-align: center;font-size: 20px;background: @themeFontColor;float: left;margin-left: 70px;font-weight: bold;
}
.siz_set45 {
  width: 140px;height: 50px;border: 1px solid @themeBackGroundColor;border-radius: 4px;color: #FFF;line-height: 45px;cursor: pointer;
  text-align: center;font-size: 20px;background: @themeBackGroundColor;float: left;margin-left: 30px;font-weight: bold;
}
.siz_set46 {
  height: calc(100% - 200px);overflow-y: scroll;
}
.siz_set47 {
  position: fixed;bottom: 94px;width: 224px;left: 22px;height: 48px;
  border-radius: 4px;background: #F5F7FA;color: @themeFontColor;cursor: pointer;
}
.siz_set48 {
  float: left;margin-left: 23px;margin-top: 10px;
}
.siz_set49 {
  position: fixed;bottom: 36px;width: 174px;left: 46px;
  border-radius: 24px;background: @themeBackGroundColor;color: #fff;cursor: pointer;height: 52px;
}
.siz_set5 {
  float: left;margin-left: 10px;
}
.siz_set51 {
  float: left;font-size: 16px;width: 18px;height: 18px;margin-top: 26px;color: #FFF;
  background: #FF6159;line-height: 16px;text-align: center;border-radius: 50%;
}
.siz_set52 {
  float: left;font-size: 16px;color: #FF6159;margin-left: 7px;margin-top: 27px;line-height: 16px;
}
.siz_set53 {
  float: left;width: 100%;font-size: 16px;color: @themeFontColor;
}
/deep/.el-input__inner {
  height: 36px;
  border-radius: 6px;
}
.el-row {
  height: 100%;
}
.el-col {
  height: 100%;
}
.el-checkbox {
  display: block;
  margin-bottom: 20px;
}
/deep/.el-checkbox__label {
  font-size: 16px;
  color: @themeFontColor;
}
/deep/.el-alert__title {
  font-size: 14px;
}
.scales_set1 {
  width: 100%;height: 100%;background: #F5F8FB;font-size: 16px;
  color: @themeFontColor;overflow: hidden;padding: 12px;letter-spacing: 1.2px;
}
.scales_set2 {
  position: relative;height: calc(100% - 60px);color: @themeFontColor;
  border-radius: 8px;background: #fff;margin-bottom: 15px;
}
.scales_set3 {
  width: 80%;margin:0 auto;height: 100%;
}
.scales_set4 {
  background:#fff;padding: 10px;cursor: pointer;
  border: 1px solid #ececec;margin-bottom: 12px;border-radius: 8px;
  .scales_set5 {
    font-size: 18px;font-weight: 600;font-style: normal;
  }
  .scales_set7 {
    color: #8C8C8C;letter-spacing: 0.8px;
  }
}
.scales_set6 {
  background:@themeBackGroundColor;padding: 10px;
  border: 1px solid #ececec;margin-bottom: 12px;border-radius: 8px;cursor: pointer;
  .scales_set5 {
    font-size: 18px;font-weight: 600;font-style: normal;color: #fff !important;
  }
  .scales_set7 {
    letter-spacing: 0.8px;color: #fff
  }
}
.scales_set8 {
  height: calc(100% - 180px);overflow-y: auto;padding-right:5px;
}
.scales_set9 {
  display: flex; justify-content: center;margin: 20px auto 0;
  border-radius: 24px;background: @themeBackGroundColor;color: #fff;cursor: pointer;height: 52px;padding: 0 10px;
}
.scales_set10 {
  line-height: 52px;font-size: 18px;letter-spacing: 0px;
}
.scales_set11 {
  height: calc(100% - 180px);padding-right:5px;
  span {
    color: #8C8C8C;
    font-size: 14px;
  }
  div {
    margin-bottom: 6px;
  }
}
.scales_set12 {
  width: 94%;margin:0 auto;height: 100%;letter-spacing: 0.5px;
}
.scales_set13 {
  width: 80px;height: 40px;border-radius: 4px;background: #DEB071;color: #fff;
  cursor: pointer;text-align: center;line-height: 40px;margin-left: 6px;
}
.scales_set14 {
  width: 80px;height: 40px;border-radius: 4px;color: #DEB071;border: 1px solid #DEB071;
  cursor: pointer;text-align: center;line-height: 40px;margin-left: 12px;
}
.scales_set15 {
  display: flex;justify-content: space-between;
}
.scales_title_div {
  padding: 5px;
  background: #F5F8FB;
  border-radius: 8px;
  margin-bottom: 10px;
  .scales_span {
    font-size: 14px;line-height: 4px;
    span {
      color: #FF4D4F;
    }
  }
}
.scales_set16 {
  height: calc(100% - 145px);padding-right:5px;color: @themeFontColor;
}
.scales_set17 {
  border-right: 1px solid #E3E6EB;position: relative;font-size: 16px;
}
.scales_set18 {
  position: absolute;top: 10px;
}
.scales_set19 {
  display: flex;justify-content: flex-end;margin-bottom: 20px;
}
.scales_table_class {
  border: 1px solid #E3E6EB;
  border-radius: 4px;
  font-size: 16px;
  overflow-x: hidden;
}
.scales_set_mod {
  position: relative;
  z-index: 200;
}
.scales_set_del {
  width: 24px;
  height: 24px;
  background: #ff4d4f;
  border-radius: 0 8px 0 24px;
  color: #fff;
  text-align: right;
  font-size: 26px;
  line-height: 14px;
  position: absolute;
  top: 1px;
  right: 1px;
  cursor: pointer;
  z-index: 400;
}
.scales_set20 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.scales_set21 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  background: #BFBFBF;
  cursor: pointer;
}
.scales_set22 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background: linear-gradient(90deg, #D8B774 0%, #DEB071 100%);
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.scales_set23 {
  display: flex;justify-content: space-between;margin-bottom: 20px;
}
.scales_set24 {
  display: flex;justify-content: flex-start;margin: 10px 30px 25px;font-size: 16px;
}
</style>
<template>
  <div>
    <!-- 删除条码秤 -->
    <div
      v-show='showDelScale'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='showDelScale = false'
      ></div>
      <div style='position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;color:#567485;'>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;line-height: 22px;'>删除后无法恢复,</div>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: normal;line-height: 40px;padding: 0 45px;'>是否继续？</div>
        <div class='scales_set20'>
          <div
            class='scales_set21'
            @click='showDelScale = false'
          >取消</div>
          <div
            class='scales_set22'
            @click='delScale();showDelScale = false'
          >删除</div>
        </div>
      </div>
    </div>
    <!-- 修改未保存确认弹窗 -->
    <div
      v-show='saveScaleConfirm'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='closeSaveConfirm()'
      ></div>
      <div style='position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;color:#567485;'>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;line-height: 22px;'>当前有未保存的数据,</div>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: normal;line-height: 40px;padding: 0 45px;'>是否继续？</div>
        <div class='scales_set20'>
          <div
            class='scales_set21'
            @click='closeSaveConfirm()'
          >取消</div>
          <div
            class='scales_set22'
            @click='scalesChoose()'
          >确定</div>
        </div>
      </div>
    </div>
    <!-- PLU编码设置弹窗 -->
    <div
      v-show='showEdit'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='showEdit = false;'
      ></div>
      <div style='position: relative;z-index: 800;height: 400px;margin: 0 auto;margin-top: 200px;background: #FFF;
        width: 450px;overflow: hidden;color:#567485;'>
        <div class="scales_set23">
          <div class="siz_set27">
            PLU编码设置
          </div>
          <div @click="showEdit = false" class="siz_set28">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="scales_set24">
          <div style="width: 100px;color: #8C8C8C;">商品名</div>
          <div>{{editCod.editName}}</div>
        </div>
        <div class="scales_set24">
          <div style="width: 100px;color: #8C8C8C;">PLU编码</div>
          <div>
            <el-input v-model.trim="editCod.editPul" placeholder="请输入PLU编码" @input="editPulInput()"></el-input>
          </div>
        </div>
        <div class="scales_set24">
          <div style="width: 100px;color: #8C8C8C;">热键</div>
          <div>
            {{editCod.editHotkey}}
          </div>
        </div>
        <div style="width: 90%">
          <el-alert
            :title="'本机型' + hotkeyNumber +'（含）以下的PLU编码默认设置同数值的热键，大于' + hotkeyNumber +'的PLU编码无法设置热键。'"
            type="warning"
            :closable="false"
            style="margin-left:20px;padding-top: 5px;height: 60px;font-size: 14px;"
            show-icon>
          </el-alert>
        </div>
        <div class="scales_set19" style="margin-right: 20px;margin-top: 20px;">
          <div class="scales_set14" style="width: 112px;height: 47px;line-height: 47px;font-size: 16px;" @click="showEdit = false;">取消</div>
          <div class="scales_set13" style="width: 112px;height: 47px;line-height: 47px;font-size: 16px;margin-left: 30px;" @click="editPluSubmit()">确定</div>
        </div>
      </div>
    </div>
    <div class="scales_set1">
    <el-row :gutter="20">
      <el-col :span="4">
        <div class="scales_set2">
          <div class="scales_set3">
            <div style="padding:14px 0px;border-bottom: 1px solid #ececec;margin-bottom: 15px;">
              <span style="font-size:18px;font-weight: bold;">条码秤</span>
            </div>
            <div class="scales_set8" ref="scales_list_div" id="scales_set8">
              <div v-for="(sc, index) in scalesList" :key="sc.id" class="scales_set_mod">
                <div :class="scalesSelectIndex === index ? 'scales_set6' : 'scales_set4'"
                  @click="scalesChoose(index)">
                  <span class="scales_set5" :style="sc.sendscaleName === '' ? 'color: #8C8C8C' : ''">
                    {{sc.sendscaleName === '' ? '请输入秤名称' : sc.sendscaleName}}
                  </span><br />
                  <span class="scales_set7">{{sc.scaleIp === '' ? '请输入秤IP' : sc.scaleIp}}</span>
                </div>
                <div v-show="scalesSelectIndex === index" class="scales_set_del"
                  @click="scalesSelectIndex === index ? showDelScaleConfirm(sc) : ''">×</div>
              </div>
            </div>
            <div @click="addScale()" class="scales_set9">
              <i class="el-icon-plus" style="color: #fff;font-size:22px;line-height:52px;"></i>
              <div class="scales_set10">
                新增条码秤
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="scales_set2" v-if="scalesList.length !== 0">
          <div class="scales_set3">
            <div style="padding:14px 0px;border-bottom: 1px solid #ececec;margin-bottom: 8px;">
              <span style="font-size:18px;font-weight: bold;">条码秤设置</span>
            </div>
            <div class="scales_set11">
              <div>
                <span style="color: red;">*</span><span>名称</span>
                <el-input v-model.trim="scalesDetail.sendscaleName" style="margin-top: 5px;"
                  maxlength="10" @input="scalesSetChange()" placeholder="请输入条码秤名称"></el-input>
              </div>
              <div>
                <span style="color: red;">*</span><span>品牌</span>
                <el-select v-model="scalesDetail.scaleBrandCode" @change="scalesBrandChange()" placeholder="请选择条码秤品牌">
                  <el-option v-for="sb in scalesBrandList" :label="sb.name" :value="sb.code" :key="sb.code"></el-option>
                </el-select>
              </div>
              <div>
                <span style="color: red;">*</span><span>型号</span>
                <el-select v-model="scalesDetail.scaleTypeCode"
                  @change="scalesSetChange()"
                  :placeholder="scalesDetail.scaleBrandCode === '' ? '请先选择条码秤品牌' : '请选择条码秤型号'"
                  :disabled="scalesDetail.scaleBrandCode === ''">
                  <el-option v-for="st in scalesTypeList" :label="st.scaleTypeName" :value="st.scaleTypeCode" :key="st.scaleTypeCode"
                    v-show="st.scaleBrandCode === scalesDetail.scaleBrandCode">
                  </el-option>
                </el-select>
              </div>
              <div>
                <span style="color: red;">*</span><span>秤IP</span>
                <el-input v-model.trim="scalesDetail.scaleIp" @input="scalesSetChange()"
                  style="margin-top: 5px;" placeholder="请输入条码秤IP"></el-input>
              </div>
              <div>
                <span style="color: red;">*</span><span>端口</span>
                <el-input v-model.trim="scalesDetail.port" @input="scalesPortInput()"
                  style="margin-top: 5px;" placeholder="请输入条码秤端口"></el-input>
              </div>
              <div>
                <span>备注</span>
                <el-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 3, maxRows: 3}"
                  v-model.trim="scalesDetail.remark"
                  @input="scalesSetChange()"
                  maxlength="20"
                  style="margin-top: 5px;height: 98px;"
                  placeholder="请输入条码秤备注">
                </el-input>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="scales_set2"  v-if="scalesList.length !== 0">
          <div class="scales_set12">
            <div class="scales_set15" ref="scales_set15" style="border-bottom: 1px solid #ececec;margin-bottom: 15px;">
              <div style="padding:14px 0px;">
                <span style="font-size:20px;font-weight: bold;">传秤商品</span><br/>
                <div class="scales_title_div">
                  <span class="scales_span">
                    1.请确保收银机和条码秤<span>已入网</span>并在<span>同一网段</span>内;
                  </span>
                  <br/>
                  <span class="scales_span">
                    2.自动生成 (PLU编号和热键)、保存、传秤等操作，<span>请先选择商品。</span>
                  </span>
                  <br/>
                  <span class="scales_span">
                    3.商品信息<span>发生修改</span>时（比如改价），请<span>再次传秤</span>以保持信息一致。请先选择商品。
                  </span>
                  <br/>
                  <span class="scales_span">
                    4.<span>暂不支持总价超过 999.99 元的传秤商品条码</span>，请仔细核对商品金额，避免造成损失。
                  </span>
                </div>
              </div>
              <div class="scales_set15" style="align-items: center;">
                <div class="scales_set13" @click="beforeSaveCheck(true)" v-loading.fullscreen.lock="fullscreenLoading">传秤</div>
                <div class="scales_set14" @click="beforeSaveCheck()">保存</div>
              </div>
            </div>
            <div class="scales_set16">
              <el-col :span="6" class="scales_set17">
                <div>
                  <el-checkbox v-model="typeGoodsCheckAll" :indeterminate="indeterminate" @change="handleCheckAllChange">全选</el-checkbox>
                </div>
                <div style="overflow-y: auto;height: calc(100% - 75px);">
                  <el-checkbox-group v-model="scalesDetail.typeCheckList">
                    <el-checkbox v-for="tg in typeGoodsOptions" :label="tg.typeFingerprint" :key="tg.typeFingerprint" :title="tg.name">{{tg.name}}</el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-col>
              <el-col :span="18" style="padding-right: 0px;">
                <div class="scales_set19" ref="scales_set19" style="justify-content:space-between">
                  <div style="color: #FF483C;font-weight: 400;font-size: 16px;line-height: 30px;">*传秤商品，售价不能为“0”。</div>
                  <div style="display: flex;justify-content:flex-end">
                    <div class="scales_set14" style="width: 220px;height: 30px;line-height: 30px;" @click="automaticPlu()">自动生成 (PLU编号和热键)</div>
                    <div class="scales_set14" style="height: 30px;line-height: 30px;" @click="exportExcel()">导出表格</div>
                  </div>
                </div>
                <div>
                  <el-table
                    :data="tableData"
                    :height="table_height"
                    ref="multipleTable"
                    stripe
                    @selection-change="handleSelectionChange"
                    class="scales_table_class">
                    <el-table-column type="selection" width="56px"></el-table-column>
                    <el-table-column label="商品名" show-overflow-tooltip prop="name" align="left"></el-table-column>
                    <el-table-column label="售价" prop="salePrice" width="120px" align="center"></el-table-column>
                    <el-table-column label="PLU编号" width="100px" align="center">
                      <template slot-scope="scope">
                        <div :key="scope.$index + '&' + scope.row.pluCode" style="cursor:pointer;"
                          @click="showEditPlu(scope.row, scope.$index)">
                          {{scope.row.pluCode}}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="热键" width="100px" align="center">
                      <template slot-scope="scope">
                        <div :key="scope.$index + '&' + scope.row.hotKey" style="cursor:pointer;"
                          @click="showEditPlu(scope.row, scope.$index)">{{scope.row.hotKey ? scope.row.hotKey : ''}}
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-col>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
export default {
  data() {
    return {
      scalesList: [],
      scalesSelectIndex: 0,
      scalesDetail: {
        sendscaleFingerprint: '',
        sendscaleName: '',
        scaleBrandName: '',
        scaleBrandCode: '',
        scaleTypeName: '',
        scaleTypeCode: '',
        scaleIp: '',
        port: '',
        remark: '',
        typeCheckList: [],
        scalesItems: []
      },
      scalesBrandList: [],
      scalesTypeList: {},
      scalesInfo: [], // 秤信息列表
      typeGoodsOptions: [], // 传秤商品checkbox list
      typeGoodsCheckAll: false,
      indeterminate: false,
      tableData: [],
      table_height: document.body.clientHeight - 305,
      choose_list: [],
      hotkeyNumber: '', // 条码秤指定型号的最大热键数
      initNo: 1,
      showDelScale: false, // 删除秤确认弹窗
      delFingerprint: '', // 要删除的秤信息
      showEdit: false,
      beforeChangeScales: 0,
      oldDetailItems: [],
      oldDetailItemMap: {},
      submit_click: false,
      isChanged: false,
      fullscreenLoading: false,
      editCod: {
        editIndex: 0,
        editName: '',
        editPul: '',
        editHotkey: ''
      },
      preEditPul: ''
    };
  },
  created() {
    this.loadScalesOptions(); // 加载全商品及秤信息
    this.listenResize();
    window.addEventListener('resize', this.listenResize); // 窗口大小变化监听
  },
  mounted() {
  },
  watch: {
    'scalesDetail.typeCheckList'() {
      this.tableData = [];
      this.typeGoodsOptions.forEach(typeGood => {
        if (this.scalesDetail.typeCheckList.indexOf(typeGood.typeFingerprint) !== -1 && typeGood.items) {
          let tmp = _.cloneDeep(typeGood.items);
          this.tableData = this.tableData.concat(tmp);
        }
      });
      this.indeterminate = this.scalesDetail.typeCheckList.length < this.typeGoodsOptions.length && this.scalesDetail.typeCheckList.length !== 0;
      this.typeGoodsCheckAll = this.scalesDetail.typeCheckList.length === this.typeGoodsOptions.length && this.scalesDetail.typeCheckList.length !== 0;
      console.log(this.tableData, 'this.tableData+');
      this.tableToggleSelect();
    },
    'scalesDetail.scaleTypeCode'() {
      // hotkeyNumber
      this.scaleTypeChange();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    loadScalesOptions() {
      // 秤品牌及型号下拉数据
      goodService.selectScaleList(
        scales => {
          console.log(scales, 'scale list');
          this.scalesTypeList = scales;
          this.scalesInfo = scales;
          let obj = {};
          this.scalesBrandList = this.scalesInfo.reduce((init, cur) => {
            if (!obj[cur.scaleBrandCode]) {
              obj[cur.scaleBrandCode] = cur.scaleBrandCode;
              init.push({code: cur.scaleBrandCode, name: cur.scaleBrandName});
            }
            return init;
          }, []);
          console.log(this.scalesBrandList, 'scalesBrandList+');
          goodService.selectAllGoodsList(
            res => {
              this.typeGoodsOptions = res;
              console.log(this.typeGoodsOptions, '传秤商品分类及所属商品');
              this.getSavedScales();
            });
        });
    },
    getSavedScales() {
      goodService.findScaleitem(res => {
        this.scalesList = res;
        console.log(res, '获取已保存秤的信息');
        if (this.scalesList.length > 0) {
          this.scalesChoose(0);
        }
      });
    },
    scaleTypeChange() {
      this.scalesInfo.forEach(info => {
        if (info.scaleTypeCode === this.scalesDetail.scaleTypeCode) {
          if (this.scalesDetail.port === '') {
            this.scalesDetail.port = info.port;
          }
          this.hotkeyNumber = Number(info.hotkeyQty);
        }
      });
    },
    scalesChoose(index) {
      let changeFlg = false;
      // if (this.isChanged) {
      //   changeFlg = true;
      // } else {
      //   changeFlg = this.checkChooseChange();
      // }
      if (changeFlg && !!index) {
        this.beforeChangeScales = index;
        this.SET_SHOW({saveScaleConfirm: true});
        return;
      }
      if (index === undefined) {
        index = this.beforeChangeScales;
      }
      this.scalesSelectIndex = index;
      this.typeGoodsOptions.forEach(typeGood => {
        typeGood.items.forEach(good => {
          delete good.hotKey;
          delete good.pluCode;
        });
      });
      let item = _.cloneDeep(this.scalesList[index]);
      this.beforeChangeScales = index;
      this.scalesDetail = item;
      // this.scaleTypeChange();
      console.log(this.scalesDetail, 'this.scalesDetail+');
      for (let si of item.scalesItems) {
        if (si.hotKey === 0) {
          si.hotKey = null;
        }
      }
      this.choose_list = item.scalesItems;
      this.oldDetailItems = item.scalesItems ? _.cloneDeep(item.scalesItems) : [];
      console.log(this.oldDetailItems, 'this.oldDetailItems+');
      let tempObj = {};
      this.oldDetailItems.forEach(item => {
        tempObj[item.fingerprint] = item;
      });
      this.oldDetailItemMap = tempObj;
      this.SET_SHOW({saveScaleConfirm: false});
      this.tableToggleSelect();
    },
    checkChooseChange() {
      if (this.oldDetailItems.length === 0 && this.choose_list.length === 0) {
        return false;
      }
      if (this.oldDetailItems.length !== 0) {
        if (this.oldDetailItems.length !== this.choose_list.length) {
          return true;
        } else {
          let flg = false;
          for (let i = 0; i < this.oldDetailItems.length; i++) {
            if (this.oldDetailItems[i].goodFingerprint !== this.choose_list[i].goodFingerprint ||
              this.oldDetailItems[i].hotKey !== this.choose_list[i].hotKey ||
              this.oldDetailItems[i].pluCode !== this.choose_list[i].pluCode) {
              flg = true;
              break;
            }
          }
          return flg;
        }
      }
      return false;
    },
    handleCheckAllChange() { // 传秤分类全选
      this.scalesDetail.typeCheckList = [];
      this.indeterminate = false;
      if (this.typeGoodsCheckAll) {
        this.typeGoodsOptions.forEach(item => {
          this.scalesDetail.typeCheckList.push(item.typeFingerprint);
        });
      }
    },
    handleSelectionChange(val) {
      this.tableData.forEach(td => {
        td.isChecked = false;
      });
      val.forEach(item => {
        Object.assign(item, {isChecked: true});
      });
      this.choose_list = val;
      this.indeterminate = this.scalesDetail.typeCheckList.length < this.typeGoodsOptions.length && this.scalesDetail.typeCheckList.length !== 0;
      this.typeGoodsCheckAll = this.scalesDetail.typeCheckList.length === this.typeGoodsOptions.length && this.scalesDetail.typeCheckList.length !== 0;
      console.log(val, 'handleSelectionChange val');
    },
    listenResize() {
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        let conHeight = this.$refs['scales_set15'].offsetHeight;
        let titleHeight = this.$refs['scales_set19'].offsetHeight;
        console.log(conHeight, 'fff');
        this.table_height = $(window).height() - conHeight - titleHeight - 124;
      }, this.delayedTime);
    },
    tableToggleSelect() { // 表格触发行选中
      let checkedFigList = [];
      this.choose_list.forEach(item => {
        checkedFigList.push(item.fingerprint);
      });
      let tmpList = _.cloneDeep(this.choose_list);
      this.$nextTick(() => {
        for (let td of this.tableData) {
          let index = checkedFigList.indexOf(td.fingerprint);
          if (index != -1) {
            td.isChecked = true;
            Object.assign(td, tmpList[index]);
            this.$refs.multipleTable.toggleRowSelection(td, true);
          } else if (this.oldDetailItemMap[td.fingerprint]) { // 上次保存的plu商品修改
            Object.assign(td, this.oldDetailItemMap[td.fingerprint]);
          } else {
            console.log('');
          }
        }
      });
    },
    automaticPlu() {
      if (!this.hotkeyNumber) {
        demo.msg('warning', '请先设置条码秤型号');
        return;
      }
      this.initNo = 1;
      let pluArr = [];
      let hotkeyArr = [];
      for (let i = 0; i < this.tableData.length; i++) {
        if (!this.tableData[i].isChecked) { // 清空未勾选商品的PLU编号与热键
          let tmp = this.tableData[i];
          tmp.pluCode = '';
          tmp.hotKey = null;
          this.$set(this.tableData, i, tmp);
        }
        var tPluCode = this.getDefaultValue(this.tableData[i].pluCode, '');
        var tHotKey = this.getDefaultValue(this.tableData[i].hotKey, '');
        pluArr.push(tPluCode);
        hotkeyArr.push(tHotKey);
      }
      for (let j = 0; j < this.tableData.length; j++) {
        if (this.tableData[j].isChecked && !this.tableData[j].pluCode) {
          let tmp1 = this.tableData[j];
          var tmp = this.tableData[j];
          console.log(tmp, tmp1);
          this.getNo(pluArr, hotkeyArr);
          if (hotkeyArr.indexOf(this.initNo) !== -1) {
            this.clearExitHotkey(hotkeyArr.indexOf(this.initNo));
          }
          tmp.pluCode = this.initNo;
          pluArr.push(this.initNo);
          if (this.initNo > this.hotkeyNumber) {
            tmp.hotKey = null;
          } else {
            tmp.hotKey = this.initNo;
            hotkeyArr.push(this.initNo);
          }
          this.$set(this.tableData, j, tmp);
        }
      }
    },
    getDefaultValue(v, defaultValue) {
      return v || defaultValue;
    },
    checkIp(ip) {
      let ipv4Reg = /^((2[0-4][0-9])|(25[0-5])|(1[0-9]{0,2})|([1-9][0-9])|([1-9]))\.(((2[0-4][0-9])|(25[0-5])|(1[0-9]{0,2})|([1-9][0-9])|([0-9]))\.){2}((2[0-4][0-9])|(25[0-5])|(1[0-9]{0,2})|([1-9][0-9])|([1-9]))$/;
      let ipv6Reg = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/gm;
      return !(ipv4Reg.test(ip) || ipv6Reg.test(ip));
    },
    getNo(pluArr, hotkeyArr) {
      if (pluArr.indexOf(this.initNo) !== -1) {
        this.initNo++;
        this.getNo(pluArr, hotkeyArr);
      }
    },
    addScale() { // 加秤
      this.scalesList.unshift({
        sendscaleName: '',
        scaleBrandName: '',
        scaleBrandCode: '',
        scaleTypeName: '',
        scaleTypeCode: '',
        scaleIp: '',
        port: '',
        remark: '',
        typeCheckList: [],
        scalesItems: []
      });
      this.$nextTick(() => {
        // this.$refs.scales_list_div.scrollTop = this.$refs.scales_list_div.scrollHeight;
        this.$refs.scales_list_div.scrollTop = 0;
      });
      this.scalesChoose(0);
    },
    showDelScaleConfirm(sc) {
      this.showDelScale = true;
      this.delFingerprint = sc.sendscaleFingerprint;
    },
    delScale() { // 删秤
      let param = {
        sendscaleFingerprint: this.delFingerprint
      }
      goodService.deleteScale(param, () => {
        goodService.findScaleitem(res => { // 获取保存的秤列表
          this.scalesList = res;
          console.log(res, '删除后重搜索秤列表!');
          if (res.length > 0) {
            this.scalesChoose(0);
          }
        });
      });
      console.log(param);
    },
    clearExitHotkey(index) { // 覆盖已被占用的热键
      let tmp = this.tableData[index];
      tmp.hotKey = null;
      this.$set(this.tableData, index, tmp);
    },
    scalesBrandChange() {
      this.isChanged = true;
      this.scalesDetail.scaleTypeCode = '';
    },
    exportExcel() { // 导出表格
      let exportList = _.cloneDeep(this.choose_list);
      if (exportList.length === 0) {
        demo.msg('warning', '请选择传秤商品!');
      }
      demo.actionLog(logList.clickScaleManageExportExcel);
      let field_mapping = {
        PLU编号: 'pluCode',
        热键: 'hotKey',
        商品名称: 'name',
        商品分类: 'typeName',
        商品条码: 'code',
        商品售价: 'salePrice'
      };
      exportList.forEach(item => {
        if (!item.hotKey) {
          item.hotKey = '';
        }
      });
      this.$makeExcel(exportList, field_mapping, '传秤商品详情' + new Date().format('yyyyMMddhhmmss'));
    },
    beforeSaveCheck(type) {
      if (this.scalesDetail.sendscaleName === '') {
        demo.msg('warning', '条码秤名称不能为空!');
        return;
      }
      if (!this.scalesDetail.scaleBrandCode) {
        demo.msg('warning', '请选择条码秤品牌!');
        return;
      }
      if (!this.scalesDetail.scaleTypeCode) {
        demo.msg('warning', '请选择条码秤型号!');
        return;
      }
      if (!this.scalesDetail.scaleIp) {
        demo.msg('warning', '条码秤IP不能为空!');
        return;
      }
      var checkScaleIp = this.checkIp(this.scalesDetail.scaleIp);
      if (checkScaleIp) {
        demo.msg('warning', '条码秤IP不合法!');
        return;
      }
      if (!this.scalesDetail.port) {
        demo.msg('warning', '条码秤端口号不能为空!');
        return;
      }
      if (this.choose_list.length === 0) {
        demo.msg('warning', '请选择需要传秤的商品!');
        return;
      }
      if (this.choose_list.some(choose => !choose.salePrice)) {
        demo.msg('warning', '商品售价不能为零!');
        return;
      }
      this.saveScaleCheck(type);
    },
    saveScaleCheck(type) { // 保存当前秤修改
      let pluArr = [];
      let hotkeyArr = [];
      let flg = false;
      for (let cl of this.choose_list) {
        if (!cl.pluCode) {
          demo.msg('warning', cl.name + '的PLU编号不能为空!');
          flg = true;
          break;
        } else if (pluArr.indexOf(cl.pluCode) !== -1) {
          demo.msg('warning', cl.name + '的PLU编号' + cl.pluCode + '已被占用,请修改!');
          flg = true;
          break;
        } else if (hotkeyArr.indexOf(cl.hotKey) !== -1 && !(cl.hotKey === null || cl.hotKey === '')) {
          demo.msg('warning', cl.name + '的热键' + cl.hotKey + '已被占用,请修改!');
          flg = true;
          break;
        } else {
          pluArr.push(cl.pluCode);
          hotkeyArr.push(cl.hotKey);
        }
      }
      if (flg) {
        return;
      }
      this.saveScale(type);
    },
    saveScale(type) {
      let param = _.cloneDeep(this.scalesDetail);
      // param.scalesBrandId = param.scaleBrandCode;
      // param.scalesTypeId = param.scaleTypeCode;
      param.scalesItems = this.choose_list;
      if (this.submit_click) { // 防连点
        return;
      }
      this.submit_click = true;
      setTimeout(() => {
        this.submit_click = false;
      }, this.clickInterval);
      if (type) { // 传秤全屏遮罩
        this.fullscreenLoading = true;
        setTimeout(() => {
          this.fullscreenLoading = false;
        }, 20000);
      }
      console.log(param, '条码秤保存参数+');
      goodService.scaleSave(param, res => {
        console.log(res, '条码秤保存成功回调');
        // 保存
        this.isChanged = false;
        demo.msg('success', '条码秤保存成功!');
        if (type) { // 保存并传秤
          this.sendScales(_.cloneDeep(this.choose_list), param);
        }
        goodService.findScaleitem(res => {
          this.scalesList = res;
          this.scalesSelectIndex = 0;
          this.scalesChoose(this.scalesSelectIndex);
        });
        this.closeSaveConfirm();
      }, err => {
        console.log(err);
        demo.msg('warning', '条码秤信息保存失败!');
        this.closeSaveConfirm();
      });
    },
    closeSaveConfirm() { // 关闭保存确认框
      this.SET_SHOW({ saveScaleConfirm: false });
    },
    showEditPlu(row, index) { // 编辑商品PLU数据加载
      if (!row.isChecked) {
        demo.msg('warning', '请先勾选此商品!');
        return;
      }
      this.showEdit = true;
      this.editCod = {
        editIndex: index,
        editName: row.name,
        editPul: row.pluCode,
        editHotkey: row.hotKey
      }
      this.preEditPul = row.pluCode;
    },
    editPulInput() { // PLU编码修改监听
      let plu = Number(this.$intMaxMinLimit({data: this.editCod.editPul, max: 999, min: 0}));
      this.editCod.editPul = plu === 0 ? '' : plu;
      if (Number(this.hotkeyNumber) >= this.editCod.editPul) {
        this.editCod.editHotkey = this.editCod.editPul;
      } else {
        this.editCod.editHotkey = '';
      }
    },
    editPluSubmit() { // PLU编码修改保存
      console.log(this.editCod);
      let flg = this.checkPulExit(this.editCod.editPul);
      if (flg && this.preEditPul !== this.editCod.editPul) {
        demo.msg('warning', '已存在商品PLU编号为' + this.editCod.editPul);
      } else {
        let tmp = this.tableData[this.editCod.editIndex];
        tmp.pluCode = this.editCod.editPul;
        tmp.hotKey = this.editCod.editHotkey;
        this.$set(this.tableData, this.editCod.editIndex, tmp);
        this.showEdit = false;
      }
    },
    scalesPortInput() {
      let port = Number(this.$intMaxMinLimit({data: this.scalesDetail.port, max: 65535, min: 0}));
      this.scalesDetail.port = port === 0 ? '' : port;
      this.scalesSetChange();
    },
    scalesSetChange() { // 条码秤设置变更
      this.isChanged = true;
    },
    sendScales(data, param) { // 调用c#传秤
      console.log(this.choose_list, '');
      let tmp = this.choose_list.map(item => {
        return {
          GoodsName: item.name,
          GoodsNum: Number(item.pluCode),
          GoodsCode: item.code,
          UnitPrice: item.salePrice,
          TotalPrice: 0.0,
          WeighingWay: item.weighModelVal,
          StoreCode: "20",
          Tare: item.tare,
          Validity: item.expireDate
        }
      });
      let sortObjArr = function(a, b) { // 升序排列对象数组
        return a.GoodsNum - b.GoodsNum;
      }
      let hotkeyData = tmp.sort(sortObjArr);
      let hotkeyArr = [];
      for (let i = 0; i < this.hotkeyNumber; i++) {
        // hotkeyArr.push(hotkeyData.length > i ? hotkeyData[i].GoodsNum : 0);
        hotkeyArr.push(0);
      }
      for (let j = 0; j < hotkeyData.length; j++) {
        // hotkeyArr.push(hotkeyData.length > i ? hotkeyData[i].GoodsNum : 0);
        if (hotkeyArr.length > hotkeyData[j].GoodsNum - 1) {
          hotkeyArr[hotkeyData[j].GoodsNum - 1] = hotkeyData[j].GoodsNum;
        }
      }
      console.log(hotkeyData, 'hotkeyData');
      console.log(hotkeyArr, 'hotkeyArr');
      console.log(tmp, '传秤数据');
      let ip = this.scalesDetail.scaleIp;
      let port = this.scalesDetail.port;
      console.log(ip + ' ' + typeof (ip), 'ip');
      console.log(port + ' ' + typeof (port), 'port');
      zgDHTMFBalance.sendGoods(ip, port, tmp, () => {
        demo.msg('success', '传秤成功!');
        console.log('传秤成功+');
        this.fullscreenLoading = false;
        goodService.insertSendscaleHistory(param, () => {}, () => {});
        console.log('sendHotKey 传参' + ip + ' ' + port + ' ' + hotkeyArr);
        setTimeout(() => {
          zgDHTMFBalance.sendHotKey(ip, port, hotkeyArr, () => {
            demo.msg('success', '设置热键成功!');
            this.fullscreenLoading = false;
          }, () => {
            demo.msg('warning', '热键设置失败,请检查网络及条码秤状态!');
            this.fullscreenLoading = false;
          });
        }, 2000);
      }, () => {
        demo.msg('warning', '传秤失败,请检查网络及条码秤状态!');
        console.log('传秤失败+');
        this.fullscreenLoading = false;
      });
    },
    checkPulExit(plu) {
      let flg = false;
      for (let tb of this.tableData) {
        if (tb.pluCode === plu) {
          flg = true;
          break;
        }
      }
      return flg;
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.listenResize);
    this.SET_SHOW({ saveScaleConfirm: false, scannerChangeStart: false, curScaleChanged: false });
  },
  computed: {
    ...mapState({
      showSizeColor: state => state.show.showSizeColor,
      delayedTime: state => state.show.delayedTime,
      clickInterval: state => state.show.clickInterval,
      saveScaleConfirm: state => state.show.saveScaleConfirm,
      scannerChangeStart: state => state.show.scannerChangeStart,
      curScaleChanged: state => state.show.curScaleChanged
    })
  }
};
</script>
