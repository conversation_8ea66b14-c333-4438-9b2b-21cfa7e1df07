<style lang="less">
.com_pmp1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
  color: @themeFontColor;
}
.com_pmp11 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 120;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}
.com_pmp12 {
  float: left;margin-left: 20px;line-height: 32px;
  span {
    color: @themeBackGroundColor;
  }
}
.com_pmp13 {
  float: right;margin-right: 20px;
}
.com_pmp14 {
  position: relative;width: 700px;background: #FFF;border-radius: 6px;
  margin: 0 auto;margin-top: 15px;overflow: hidden;z-index: 300;font-size: 16px;
}
.com_pmp15 {
  line-height: 60px;width: calc(100% - 40px);margin-left: 20px;border-bottom: 1px solid #E3E6EB;overflow: hidden;
}
.com_pmp16 {
  float: left;font-weight: 700;text-indent: 10px;
}
.com_pmp17 {
  float: right;font-size: 30px;line-height: 56px;
}
.com_pmp18 {
  border: 1px solid #E5E8EC;margin-left: 20px;width: calc(100% - 40px);margin-top: 15px;
}
.com_pmp19 {
  overflow: hidden;margin-top: 10px;margin-bottom: 10px;
}
.com_pmp2{
  width: 80px;
  height: 43px;
  text-align: center;
  line-height: 40px;
  background:@themeBackGroundColor;
  color: #FFF;
  border-radius: 4px;
  cursor: pointer;
}
.com_pmp21 {
  width: 440px;
  background: #FFFFFF;
  border-radius: 6px;
  margin: 0 auto;margin-top: 220px;
  position: relative;
  z-index: 400;
  font-size: 16px;
}

.com_pmp22 {
  font-size: 18px;
  color: #B2C3CD;
  line-height: 24px;
  margin-top: 20px;
  margin-left: 28px;
  margin-right: 20px;
}
.com_exchange_print {
  font-size: 16px;
  font-weight: bold;
  color: #B2C3CD;
  line-height: 24px;
  margin-top: 20px;
  margin-left: 28px;
  margin-right: 20px;
  width: 220px;
  display: flex;
  align-items: center;
}
.com_pmp221 {
  display: block;
  width: 388px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.com_pmp23 {
  width: 120px;height: 44px;line-height: 42px;margin-top: 30px;color: @themeBackGroundColor;
  font-size: 16px;text-align: center;background: #FFF;border-radius: 4px;cursor: pointer;
  border: 1px solid @themeBackGroundColor;float: right;margin-right: 20px;
}
.com_pmp24 {
  width: 120px;height: 44px;line-height: 42px;margin-top: 30px;color: #FFF;font-size: 16px;text-align: center;
  background: linear-gradient(90deg, #D2BA8A 0%, #B49A5A 100%);border-radius: 4px;cursor: pointer;float: right;margin-right: 16px;
}
</style>
<template>
  <!--会员积分兑换礼品弹出框-->
  <div v-if="showMemberExchange" class="com_pmp1">
    <!-- 设置兑换礼品弹出框 -->
    <div v-show="show_exchange_goods" class="pc_pay122">
      <div class="com_pmp11" style="z-index: 350;"></div>
      <div class="com_pmp21">
        <div class="com_pmp15">
          <div class="com_pmp16">兑换数量</div>
          <div class="com_pmp17" @click="show_exchange_goods = false;">×</div>
        </div>
        <div class="com_pmp22">{{row.name}}</div>
        <div style="color: #567485;margin-top: 28px;margin-left: 28px;">
          兑换个数
          <el-input-number
            v-model="num"
            style="margin-left: 60px;width: 262px;"
            :min="1"
            :max="99999"
            placeholder="请输入兑换个数"
            @blur="setNum()"
            v-input-int-max-min="{max: 99999,min: 0}"
          >
          </el-input-number>
        </div>
        <div class="com_exchange_print" @click="setPointExchangePermit">
          <img v-show="!permitPointExchangePrint" src="../image/pc_goods_checkbox1.png" />
          <img v-show="permitPointExchangePrint" src="../image/pc_goods_checkbox2.png" />
          <div style="margin-left: 6px;">兑换完成打印小票</div>
        </div>
        <div style="overflow: hidden;padding-bottom: 30px;">
          <div @click="submit_exchanged_goods()" class="com_pmp24">确定</div>
          <div @click="show_exchange_goods = false" class="com_pmp23">取消</div>
        </div>
      </div>
    </div>
    <div class="com_pmp11"></div>
    <div style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;">
      <div class="com_pmp14">
        <div class="com_pmp15">
          <div class="com_pmp16">兑换礼品（该会员拥有 <span style="color: #ff6159;">{{total_integral}}</span> 个积分）</div>
          <div class="com_pmp17" @click="close_member_exchange()">×</div>
        </div>
        <el-input
          placeholder="商品名称/条码/首字母"
          v-model="keyword"
          clearable
          style="font-size:16px;width: 660px;margin-left: 20px;margin-top: 15px;border-radius: 22px;"
          @compositionstart='pinyin = true'
          @compositionend='pinyin = false'
          @input="keyword = keyword.replace(/[$']/g, '')"
        >
        </el-input>
        <div class="com_pmp18">
          <el-table
            :data="goods_list"
            stripe
            style="width: 100%;height: 500px;font-size: 16px;">
            <el-table-column
              prop="name"
              width="300"
              label="商品名称" :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column
              prop="point"
              label="所需积分">
            </el-table-column>
            <el-table-column
              label="兑换数量">
              <template slot-scope="scope">
                {{(Number(scope.row.toplimit) - Number(scope.row.exchanged)).toFixed(0)}}
              </template>
            </el-table-column>
            <el-table-column
              label="操作">
              <template slot-scope="scope">
                <div v-show="(Number(scope.row.toplimit) - Number(scope.row.exchanged)).toFixed(0) !== '0'" class="com_pmp2" @click="exchanged_goods(scope.$index)">兑换</div>
                <div v-show="(Number(scope.row.toplimit) - Number(scope.row.exchanged)).toFixed(0) === '0'" class="com_pmp2" style="background: #E6E9EF;">兑换</div>
              </template>
            </el-table-column>
          </el-table>
          <div class="com_pmp19">
            <div class="com_pmp12">
              共 <span>{{total}}</span> 款商品
            </div>
            <div class="com_pmp13">
              <el-pagination
                layout="prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
                :current-page.sync="pagenum"
                :page-size="limit"
                :page-count="total"
              ></el-pagination>
            </div>
          </div>
        </div>
        <div style="height: 40px;"></div>
      </div>
    </div>

  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
// import { INVENTORY_DEAL_TYPE } from '@/utils/inventoryDealType';

export default {
  data() {
    return {
      goods_list: [],
      total: 0,
      limit: 7,
      pagenum: 1,
      total_integral: '',
      orderno: '',
      goods_fingerprint_old_list: [],
      row: {},
      from: 'point_exchange_print',
      show_exchange_goods: false,
      num: 1,
      exchanging: false,
      setSystem: true,
      index: '',
      keyword: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    searchGoods () {
      this.pagenum = 1;
      this.get_goods_list();
    },
    setNum() {
      if (this.num === '' || Number(this.num) === 0 || this.num === undefined) {
        this.num = 1;
      }
    },
    close_member_exchange() {
      this.SET_SHOW({showMemberExchange: false});
    },
    submit_exchanged_goods() {
      if (this.exchanging === true) {
        return;
      }
      // 如果输入数字，少于1或者大于最大值，或者不是正整数
      if (Number(this.num) < 1 || Number(this.num) > Number((Number(this.row.toplimit) - Number(this.row.exchanged)).toFixed(0)) || Number(this.num) * 10 % 2 !== 0) {
        demo.msg('warning', '超出兑换上限，最多兑换' + Number(Number(this.row.toplimit) - Number(this.row.exchanged)) + '个');
        return;
      }
      if (!this.setSystem && (this.row.curStock - this.num < 0)) {
        demo.msg('warning', '现有库存不足，仍要兑换请前往【设置】-【系统设置】，开启【销售设置】');
        return;
      }

      this.exchanging = true;

      var data = {
        'systemName': $config.systemName,
        'subName': $config.subName,
        'storeName': this.username,
        'point': Number(this.num) * Number(this.row.point),
        'phone': this.sys_uid,
        'sysSid': this.sys_sid,
        'vipId': this.showMemberExchangeId,
        'fingerprint': this.row.fingerprint,
        'exchange': this.num,
        'createUser': this.sys_uid,
        'productName': this.row.name,
        'productCode': this.row.code
        // "": Number(this.num) * Number(this.row.point)
      };
      var that = this;
      demo.$http.post(this.$rest.pc_exchangeProduct, data, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      })
        .then(function (res) {
          that.exchanging = false;
          that.show_exchange_goods = false;
          if (res.data.code === '0') {
            demo.msg('success', '兑换完成');
            that.change_cur_stock();
            that.total_integral = Number(that.total_integral) - (Number(that.num) * Number(that.row.point));
            that.pointChargePrint(data);
            that.goods_list = [];
            that.get_goods_list();
          } else {
            demo.msg('warning', '兑换失败，' + res.data.msg);
          }
        });
    },
    change_cur_stock() {
      let newAutoPacking;
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        newAutoPacking = 1;
      } else {
        newAutoPacking = JSON.parse(this.storeList[0].settings).autoPacking === '1' ? 1 : 0;
      }
      var params = {
        autoPacking: newAutoPacking,
        remark: this.showMemberExchangeName,
        items: [{
          goods_id: this.row.id,
          fingerprint: this.row.fingerprint,
          cur_stock: this.row.curStock,
          pick_up_num: -this.num,
          purPrice: this.row.purPrice
        }]
      };
      inventoryService.updateStockAfterExchangeGood(params, res => {
        console.log(res);
      }, err => {
        console.error(err);
      });
    },
    pointChargePrint(data) { // 积分兑换商品打印小票
      if (this.permitPointExchangePrint) {
        if (this.setting_small_printer === undefined || this.setting_small_printer === null ||
          this.setting_small_printer.trim() === '') {
          setTimeout(() => {
            demo.msg('warning', this.$msg.not_setting_small_printer);
          }, 3000);
          return;
        }
        let orderCreate = new Date().format('yyyy-MM-dd hh:mm:ss');
        let printParam = _.cloneDeep(data);
        printParam.createAt = orderCreate;
        printParam.operater = this.loginInfo.employeeNumber ? this.loginInfo.employeeNumber : '管理员';
        printParam.storename = data.storeName;
        printParam.printNum = 1;
        let memberNewData = {};
        memberNewData.member_name = '会员名';
        memberNewData.member_value = this.member_detail.name;
        memberNewData.member_money_name = '余额';
        memberNewData.member_money_value = this.member_detail.has_money;
        memberNewData.member_mobile_name = '会员手机号';
        memberNewData.member_mobile_value = this.member_detail.mobile;
        memberNewData.member_point_name = '积分';
        memberNewData.member_point_value = Number(this.total_integral);
        printParam.memberNewData = memberNewData;
        pos.printer.printPOSTimes(printParam, this);
      }

      // 积分兑换打印
    },
    setPointExchangePermit() { // 保存积分兑换是否打印小票
      this.SET_SHOW({permitPointExchangePrint: !this.permitPointExchangePrint});
      let store = _.cloneDeep(this.storeList);
      let dataInfo = store[0].settings ? demo.t2json(store[0].settings) : {};
      dataInfo.permitPointExchangePrint = this.permitPointExchangePrint;
      store[0].settings = JSON.stringify(dataInfo);
      this.SET_SHOW({storeList: store});
      storeInfoService.updateSettings({'id': 1, 'settings': store[0].settings}, () => {
        // do nothing
      }, () => {
        // do nothing
      });
    },
    exchanged_goods(index) {
      this.index = index;
      this.row = this.goods_list[index];
      this.num = 1;
      this.show_exchange_goods = true;
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      let data = {
        pset: '',
        type: '',
        condition: this.keyword,
        limit: this.limit,
        fingerprint: this.goods_fingerprint_list,
        fingerprintFlg: false,
        vipGoods: true,
        offset: Number((this.pagenum - 1) * this.limit)
      };
      goodService.search(data, res => {
        console.log(res, 555);
        this.goods_list = demo.t2json(res);
        this.goods_list = this.goods_list.map(goods => {
          let select_goods = this.goods_fingerprint_old_list.find(fingerprint_old => fingerprint_old.fingerprint === goods.fingerprint);
          goods.point = select_goods.point;
          goods.toplimit = select_goods.toplimit;
          goods.exchanged = select_goods.exchanged;
          return goods;
        });
        console.log(this.goods_list, 'goods_list');
      });
      goodService.searchCnt(data, res => { this.total = Number(demo.t2json(res)[0].cnt); });
    },
    getSetSystem () {
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        if ($setting.stock) {
          this.setSystem = $setting.stock !== '0';
        }
        this.SET_SHOW({ permitPointExchangePrint: false });
      } else {
        if (demo.t2json(this.storeList[0].settings).setSystem === undefined) {
          if ($setting.stock) {
            this.setSystem = $setting.stock !== '0';
          }
        } else {
          this.setSystem = demo.t2json(this.storeList[0].settings).setSystem === '1';
        }
        if (demo.t2json(this.storeList[0].settings).permitPointExchangePrint) {
          let isExchangePrint = demo.t2json(demo.t2json(this.storeList[0].settings).permitPointExchangePrint);
          this.SET_SHOW({permitPointExchangePrint: isExchangePrint});
        } else {
          this.SET_SHOW({permitPointExchangePrint: false});
        }
      }
    },
    get_goods_list() {
      demo.$http.post(this.$rest.pc_getProducts,
        {phone: this.sys_uid,
          sysSid: this.sys_sid,
          systemName: $config.systemName
        }, {
          headers: {
            'Content-Type': 'application/json'
          }
        }).then(res => {
        if (res.data.code === '0') {
          console.log(res.data, "res.data");
          if (res.data.data) {
            let fingerprintsStr = res.data.data.fingerprints;
            if (fingerprintsStr === null) {
              this.goods_fingerprint_list = [];
            } else {
              this.goods_fingerprint_list = fingerprintsStr.replace(/'/g, '').split(',');
            }
            this.goods_fingerprint_old_list = res.data.data.products;
          }
          this.handleCurrentChange(1);
        } else {
          demo.msg('error', '获取云端数据失败，请重试');
        }
      });
    }
  },
  watch: {
    keyword() {
      console.log(this.keyword_timer, 'this.keyword_timer');
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        this.searchGoods();
      }, this.delayedTime);
    },
    showMemberExchange() {
      if (this.showMemberExchange === true) {
        this.keyword = '';
        this.total_integral = this.showMemberExchangeIntegral;
        this.get_goods_list();
      }
    }
  },
  computed: mapState({
    showMemberExchange: state => state.show.showMemberExchange,
    sys_sid: state => state.show.sys_sid,
    sys_uid: state => state.show.sys_uid,
    storeList: state => state.show.storeList,
    member_detail: state => state.show.member_detail,
    loginInfo: state => state.show.loginInfo,
    showMemberExchangeId: state => state.show.showMemberExchangeId,
    showMemberExchangeName: state => state.show.showMemberExchangeName,
    showMemberExchangeIntegral: state => state.show.showMemberExchangeIntegral,
    permitPointExchangePrint: state => state.show.permitPointExchangePrint,
    setting_small_printer: state => state.show.setting_small_printer,
    username: state => state.show.username,
    delayedTime: state => state.show.delayedTime
  }),
  created() {
    this.getSetSystem();
  }
};
</script>
