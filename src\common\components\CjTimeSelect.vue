<template>
  <div class="warp">
    <template v-if="type === 'select'">
      <el-select v-model="selectTime"
        popper-class="homeTimeSelect"
        placeholder="请选择"
        @change="handleChange">
        <el-option
          v-for="item in showList"
          :key="item.name"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </template>
    <template v-else-if="type === 'tabs'">
      <div class="tabWrap">
        <div class="content cjFlexCenter">
          <div v-for="item in showList" :key="item.name" class="tabItem"
            :class="{activeTab: item.name === selectTime}"
            @click="handleChange(item.name)">{{ item.name }}</div>
        </div>
        <div class="activeBg" :style="activeStyle"></div>
      </div>
    </template>
  </div>
</template>
<script>
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
export default {
  name: 'CjTimeSelect',
  props: {
    // 组件样式
    type: {
      type: String,
      default: 'select'
    },
    // 开始时间
    from: {
      type: String,
      default: ''
    },
    // 结束时间
    to: {
      type: String,
      default: ''
    },
    // 自定义列表
    list: {
      type: Array,
      default: () => []
    },
    // 组件首次加载时是否直接按照默认值触发一次选择事件
    createRun: {
      type: Boolean,
      default: true
    },
    // 默认选择的日期
    defaultTime: {
      type: String,
      default: '今日'
    },
    // 时间格式化规则
    format: {
      type: String,
      default: 'YYYY-MM-DD'
    }
  },
  data() {
    return {
      selectTime: '今日',
      // name显示的名称和具体选择的key值，type为时间类型，可选值day 日，week周，month月，year年，
      // hour小时，minute分钟，secound秒，millisecond毫秒
      // handle为操作方式，以当前时间增加/减少，可选add增加，subtract减少，空为不操作
      // value具体增加/减少的值，和handle配合
      // 例:上周为handle: subtract, value: 1, type: week 下月为handle: add, value: 1, type: month
      timeList: [
        {
          name: '今日',
          type: 'day'
        },
        {
          name: '昨日',
          type: 'day',
          handle: 'subtract',
          value: 1
        },
        {
          name: '本周',
          type: 'week'
        },
        {
          name: '上周',
          type: 'week',
          handle: 'subtract',
          value: 1
        },
        {
          name: '本月',
          type: 'month'
        },
        {
          name: '上月',
          type: 'month',
          handle: 'subtract',
          value: 1
        },
        {
          name: '本年',
          type: 'year'
        }
      ]
    }
  },
  computed: {
    showList() {
      if (this.list.length) {
        return this.list;
      } else {
        return this.timeList;
      }
    },
    activeStyle() {
      const index = this.showList.findIndex(item => item.name === this.selectTime);
      return `left: ${index * 80}px`
    }
  },
  mounted() {
    if (this.defaultTime) {
      this.selectTime = this.defaultTime;
    }
    if (this.createRun) {
      this.handleChange(this.selectTime);
    }
  },
  methods: {
    // 刷新
    refresh(boolean) {
      if (boolean && this.defaultTime) {
        this.selectTime = this.defaultTime;
      }
      this.handleChange(this.selectTime);
    },
    // 时间选择
    handleChange(value) {
      const item = this.showList.find(item => item.name === value);
      if (this.type === 'tabs') {
        this.selectTime = value;
      }
      let from, to;
      if (item.handle) {
        const now = dayjs();
        const target = now[item.handle](item.value, item.type);
        from = target.startOf(item.type).format(this.format);
        to = target.endOf(item.type).format(this.format);
      } else {
        const now = dayjs();
        from = now.startOf(item.type).format(this.format);
        to = now.endOf(item.type).format(this.format);
      }
      this.$emit('input', value);
      this.$emit('update:from', from);
      this.$emit('update:to', to);
      this.$emit('handleChange', { from, to });
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-input__inner {
  height: 32px;
  background-color: #F6F8F9;
  border-color: #B2C3CD;
}
/deep/.el-input__icon {
  line-height: 32px !important;
}
/deep/.el-select-dropdown__item.selected {
  color: #b4995a;
  background-color: #F5F0E3;
  width: 80px;
  margin: 0 auto;
  border-radius: 4px;
}
.warp {
  width: 100%;
  height: 100%;
  .tabWrap {
    position: relative;
    background-color: #F6F8F9;
    .content {
      padding: 2px;
      border-right: 6px;
      position: relative;
      z-index: 1;
      .tabItem {
        width: 68px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        font-size: 14px;
      }
      .tabItem:not(:last-child) {
        margin-right: 12px;
      }
      .activeTab {
        color: #FFFFFF;
        transition: all 0.3s ease-in-out;
      }
    }
    .activeBg {
      position: absolute;
      top: 2px;
      width: 68px;
      height: 28px;
      border-radius: 6px;
      transition: all 0.3s ease-in-out;
      background-color: @themeBackGroundColor;
    }
  }
}
</style>
