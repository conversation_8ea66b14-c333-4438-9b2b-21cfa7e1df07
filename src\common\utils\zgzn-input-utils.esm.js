import{isEmpty}from"frontend-utils";function ownKeys(t,e){var r,u=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),u.push.apply(u,r)),u}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _toPrimitive(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==typeof e?e:String(e)}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var emojiReg=/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi,specialReg=/[^a-zA-Z0-9\u4E00-\u9FA5\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29\ud840-\ud868\udc00-\udfff\ud86a-\ud86c\udc00-\udc1d]/g,chineseReg=/[\u4E00-\u9FA5\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29\ud840-\ud868\udc00-\udfff\ud86a-\ud86c\udc00-\udc1d]/g,fullToHalf=function(e){return e.replace(/[\uFF00-\uFFE4\uFFE6-\uFFEF]/g,function(e){return String.fromCharCode(e.charCodeAt(0)-65248)})},halfToFull=function(e){return e.replace(/[\x21-\x7E]/g,function(e){return String.fromCharCode(e.charCodeAt(0)+65248)})},floatNumberFormat=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=0,t=_objectSpread2(_objectSpread2({},{maxNumber:null,minNumber:null,halfFull:"half",decimal:-1,isZeroPad:!1,roundType:"",symbol:"",default:0}),t);if("string"!=typeof e&&(isNaN(e)||null===e))return t.default;r=e,isNaN(Number(r))||(n=(r=Number(r)).toExponential().match(/\d(?:\.(\d*))?e([+-]\d+)/),r=r.toFixed(Math.max(0,(n[1]||"").length-n[2])));var u=String(r),n=0===(u=fullToHalf(u)).indexOf("-")||"-"===t.symbol,r=(u=u.replace(/^([^.]*\.)|\.+/g,"$1").replace(/[^\d.]/g,""),-1<t.decimal&&(r=Math.pow(10,t.decimal),t.roundType&&u.split("").reverse().indexOf(".")>=t.decimal?u=String(Math[t.roundType](u*r)/r):u.includes(".")&&(u=u.slice(0,u.indexOf(".")+t.decimal+(0<t.decimal?1:0)))),u.split("."));if(u="".concat(""!==r[0]?Number(r[0]):r[0]).concat(void 0!==r[1]?".".concat(r[1]):""),""!==(u=""!==(u="".concat(n&&"+"!==t.symbol?"-":"").concat(u))&&!isNaN(t.maxNumber)&&!isEmpty(t.maxNumber)&&Number(u)>Number(t.maxNumber)?String(t.maxNumber):u)&&!isNaN(t.minNumber)&&!isEmpty(t.minNumber)&&Number(u)<Number(t.minNumber)&&(u=String(t.minNumber)),console.log(u),t.isZeroPad&&t.decimal){var r=u.toString().split("."),i=0;1<r.length?r[1].length<t.decimal&&(i=t.decimal-r[1].length):(u="".concat(u,"."),i=t.decimal);for(var o=0;o<i;o++)u=u.toString()+"0"}return console.log(u),u="full"===t.halfFull?halfToFull(u):u},intNumberFormat=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=_objectSpread2(_objectSpread2({},{maxNumber:null,minNumber:null,decimal:0,halfFull:"half",symbol:"",default:0}),t);return floatNumberFormat(e,t)},stringFormat=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=(console.log(e,t),_objectSpread2(_objectSpread2({},{maxLen:0,specialFilter:!1,emojiFilter:!1,chineseFilter:!1,chineseDouble:!1,halfFull:"half",default:""}),t));if(parseInt(r.maxLen)&&r.maxLen<0&&(r.maxLen=0),r.maxLen<0&&(r.maxLen=0),"string"!=typeof e&&"number"!=typeof e)return r.default;var u=e.toString();if((r.emojiFilter||r.specialFilter)&&(u=u.replace(emojiReg,"")),r.specialFilter&&(u=u.replace(specialReg,"")),r.chineseFilter&&(u=u.replace(chineseReg,"")),"half"===r.halfFull?u=fullToHalf(u):"full"===r.halfFull&&(u=halfToFull(u)),!isNaN(r.maxLen)&&0<Number(r.maxLen))if(r.chineseDouble){for(var n=u.split(""),i=0,o=/[\u4E00-\u9FA5]/,l=/[\uFF01-\uFFEF]+/,a=0;a<n.length;a++)if((i+=o.test(n[a])||l.test(n[a])?2:1)>Number(r.maxLen)){u=u.slice(0,a);break}}else u=u.slice(0,r.maxLen);return u},moneyFormat=function(e){var t,r,u,n,i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=_objectSpread2(_objectSpread2({},{splitLen:3,symbol:",",sign:"",signPosition:"start",default:""}),i);return isNaN(e)||!Number.isInteger(i.splitLen)||i.splitLen<=0?i.default:(t=(r=fullToHalf(String(e))).includes("."),n=(r=r.split("."))[0],r=r[1]||"",u=new RegExp("(\\d)(?=(\\d{".concat(i.splitLen,"})+(?!\\d))"),"g"),n=n.replace(u,"$1".concat(i.symbol)),(t?"".concat("start"===i.signPosition?i.sign:"").concat(n,".").concat(r):"".concat("start"===i.signPosition?i.sign:"").concat(n)).concat("end"===i.signPosition?i.sign:""))};export{floatNumberFormat,intNumberFormat,moneyFormat,stringFormat};
