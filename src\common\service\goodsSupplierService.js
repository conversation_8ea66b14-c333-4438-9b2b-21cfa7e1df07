import _ from 'lodash';
import dao from '../dao/dao';

const goodSupplierService = {
  /**
   * 批量设置商品供应商（多个商品同一个供应商）
   * var param = {"fingerprintList":["4c03d5d0af9488b029aa217d948e09f3","996fe1ed5b271139dcd2326197bfb7f2"],"supplierFingerprint":"8e333d1a3e586c6f54a9a408cfc0020e"};
   * goodSupplierService.batchUpdateGoodsSupplier(param, res => {console.log(res)}, err => {console.error(err)});
   */
  batchUpdateGoodsSupplier(data, onSuccess, onFail) {
    data.uid = $userinfo.uid;
    let fingerprintList = data.fingerprintList;
    let supplierFingerprint = data.supplierFingerprint;
    let sql = '';
    if (fingerprintList.length > 0) {
      if (demo.isNullOrTrimEmpty(supplierFingerprint)) {
        // 删除关联
        sql += `update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime') 
        where fingerprint in('${fingerprintList.join("','")}')`;
      } else {
        _.forEach(fingerprintList, fingerprint => {
          sql += `insert into goods_suppliers (supplier_fingerprint, fingerprint) values ('${supplierFingerprint}','${fingerprint}')
            on CONFLICT(fingerprint) do
            update set supplier_fingerprint='${supplierFingerprint}', is_del=0, is_synced=0, revise_at=datetime('now','localtime');`;
        });
      }
      if (!demo.isNullOrTrimEmpty(sql)) {
        dao.exec(sql, onSuccess, onFail);
      }
    }
  },
  /**
   * 批量设置商品的供应商（每个商品设置不同的供应商）
   * var param = [{"fingerprint":"1f33a849506a89b2a7463cd7116eaf83","supplierFingerprint":"8e333d1a3e586c6f54a9a408cfc0020e"},{"fingerprint":"6b7c6544bec557e64e2f6315dafb5390","supplierFingerprint":"8e333d1a3e586c6f54a9a408cfc0020e"},{"fingerprint":"45b8ca814fc69f2118f1761cbb456922","supplierFingerprint":"8e333d1a3e586c6f54a9a408cfc0020e"}];
   * goodSupplierService.batchSetGoodsSuppliers(param, res => {console.log(res)}, err => {console.error(err)});
   */
  batchSetGoodsSupplier(data, onSuccess, onFail) {
    data.uid = $userinfo.uid;
    let sql = '';
    if (data.length > 0) {
      _.forEach(data, item => {
        if (demo.isNullOrTrimEmpty(item.supplierFingerprint)) {
          // 删除关联
          sql += `update goods_suppliers set is_del=1, is_synced=0, revise_at=datetime('now','localtime')
          where fingerprint='${item.fingerprint}';`;
        } else {
          sql += `insert into goods_suppliers (supplier_fingerprint, fingerprint) values ('${item.supplierFingerprint}','${item.fingerprint}')
          on CONFLICT(fingerprint) do
          update set supplier_fingerprint='${item.supplierFingerprint}', is_del=0, is_synced=0, revise_at=datetime('now','localtime');`;
        }
      });
    }
    if (!demo.isNullOrTrimEmpty(sql)) {
      dao.exec(sql, onSuccess, onFail);
    }
  }
};
window.goodSupplierService = goodSupplierService;
export default goodSupplierService;
