<template>
  <div class="report_forms_container">
    <div class="pc_applet_code" v-show="showAppletCode">
      <div class="container">
        <div class="close" @click="showAppletCode = false">×</div>
        <div class="img_container">
          <img alt="" :src="appletCodeBase64"/>
        </div>
        扫一扫，小程序看数据
      </div>
    </div>
    <div class="item_container">
      <div
        class="item"
        v-for="(item,index) in arr"
        :key="index"
        @click="clickItem(index)"
        v-show="index !== 5 || ultimate"
        :style="ultimate === null && index !== 2 ? 'opacity: 40%' : ''"
      >
        <img :src="item.img">
        <div style="margin-left:18px;float: left;width: 200px;margin-top: 26px;">
          <div class="title">{{item.title}}</div>
          <div class="desc">{{item.desc}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
export default {
  data () {
    return {
      showAppletCode: false,
      arr: [
        {
          img: require('../../image/zgzn-pos/shangpinxiaoshoutongji.png'),
          title: '商品销售统计',
          desc: '查看商品的销售情况、销售数量和销售额'
        },
        {
          img: require('../../image/zgzn-pos/huiyuanjiaoyimingxi.png'),
          title: '会员报表',
          desc: '查看会员充值、消费和积分使用，分析会员价值'
        },
        {
          img: require('../../image/zgzn-pos/xiaoshoumingxi.png'),
          title: '销售明细',
          desc: '查看收款和退款的详细销售记录和支付方式'
        },
        {
          img: require('../../image/zgzn-pos/jinhuomingxi.png'),
          title: '进货明细',
          desc: '查看进货和退货的详细记录和支付方式'
        },
        {
          img: require('../../image/zgzn-pos/cikabaobiao.png'),
          title: '次卡报表',
          desc: '查看次卡统计、销售明细和使用明细'
        },
        {
          img: require('../../image/zgzn-pos/jicunmingxi.png'),
          title: '寄存数据',
          desc: '查看会员寄存物品的数量、库存及明细'
        },
        {
          img: require('../../image/zgzn-pos/jiaojiebanmingxi.png'),
          title: '交接班记录',
          desc: '查看交接班的业绩汇总及详情'
        },
        {
          img: require('../../image/zgzn-pos/kucuntongji.png'),
          title: '库存报表',
          desc: '查询商品库存，查看库存统计、变动明细'
        }
      ],
      appletCodeBase64: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    clickItem (index) {
      if (this.ultimate === null && index !== 2) {
        this.SET_SHOW({isBuySoftware: true});
        return;
      }
      switch (index) {
        case 0:
          this.SET_SHOW({ isReport: true });
          this.SET_SHOW({ isReportForms: false });
          this.SET_SHOW({ pc_detail_tab: 4 });
          this.SET_SHOW({ fromReport: 1 });
          break;
        case 1:
          this.SET_SHOW({ isReport: true });
          this.SET_SHOW({ pc_detail_tab: 3 });
          this.SET_SHOW({ fromReport: 1 });
          this.SET_SHOW({ isReportForms: false });
          break;
        case 2:
          this.SET_SHOW({ isDetail: true });
          this.SET_SHOW({ isReportForms: false });
          this.SET_SHOW({ pc_detail_tab: 1 });
          this.SET_SHOW({ fromDetail: 1 });
          break;
        case 3:
          this.SET_SHOW({ isDetail: true });
          this.SET_SHOW({ isReportForms: false });
          this.SET_SHOW({ pc_detail_tab: 2 });
          this.SET_SHOW({ fromDetail: 1 });
          break;
        case 4:
          this.SET_SHOW({ isOnceCard: true });
          this.SET_SHOW({ isReportForms: false });
          this.SET_SHOW({ pc_detail_tab: 5 });
          break;
        case 5:
          if (!this.ultimate) {
            break;
          }
          this.SET_SHOW({ isStockStatistics: true });
          this.SET_SHOW({ isReportForms: false });
          break;
        case 6:
          this.SET_SHOW({ isReportForms: false });
          this.SET_SHOW({ isChangeShiftsRecord: true });
          break;
        case 7:
          this.SET_SHOW({ isStockRecord: true });
          this.SET_SHOW({ isReportForms: false });
          break;
        default:
          break;
      }
    },
    getAppletCode() { // 获取小程序码,登录后首次进入时通过接口获取一次，其余情况加载缓存的小程序码
      if (this.zgcmAppletBase64) {
        this.appletCodeBase64 = 'data:image/jpeg;base64,' + this.zgcmAppletBase64;
        this.showAppletCode = true;
      } else {
        if (pos.network.isConnected()) {
          let param = {
            systemName: $config.systemName,
            subName: $config.subName,
            sysUid: this.sysUid,
            sysSid: this.sysSid
          }
          demo.$http
            .post($config.Base.OtherOptions.zgcmUrl + this.$rest.zgcmGetAppletCode, param)
            .then(res => {
              if (res.data.code === 0 && res.data.data) {
                this.appletCodeBase64 = 'data:image/jpeg;base64,' + res.data.data;
                this.showAppletCode = true;
                this.SET_SHOW({zgcmAppletBase64: res.data.data});
                settingService.setInfoValueForKey('zgcm_applet_base64', res.data.data);
              } else {
                this.appletCodeBase64 = '';
              }
            })
            .catch(() => {
              // do nothing
            });
        } else if ($setting.info && demo.t2json($setting.info).zgcm_applet_base64) {
          this.appletCodeBase64 = 'data:image/jpeg;base64,' + demo.t2json($setting.info).zgcm_applet_base64;
          this.showAppletCode = true;
        }
      }
    }
  },
  created() {
    this.getAppletCode();
  },
  computed: mapState({
    isReportForms: state => state.show.isReportForms,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    ultimate: state => state.show.ultimate,
    zgcmAppletBase64: state => state.show.zgcmAppletBase64,
    isStockStatistics: state => state.show.isStockStatistics
  })
};
</script>

<style lang='less' scoped>
.report_forms_container{
  display: flex;
  justify-content: center;
  background: #F5F8FB;
  .item_container{
    padding-top: 20px;
    width: 990px;
    .item{
        width: 300px;
        height: 125px;
        background: white;
        margin-left: 15px;
        margin-right: 15px;
        cursor: pointer;
        float: left;
        overflow: hidden;
        margin-top: 30px;
        border-radius: 4px;
        img{
          width: 62px;
          height: 62px;
          margin-left: 20px;
          float: left;
          margin-top: 34px;
        }
        .title{
          color: @themeFontColor;
          font-size: 16px;
          font-weight: 700;
          line-height: 16px;
        }
        .desc{
          margin-top: 10px;
          font-size: 16px;
          color: @text;
          padding-right: 20px;
        }
      }
  }
}
.pc_applet_code {
  background: @themeButtonBackGroundColor;
  border-radius: 12px 0px 0px 12px;
  width: 160px;
  color: @themeBackGroundColor;
  font-weight: 500;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  right: 0px;
  position: absolute;
  z-index: 100;
  margin-top: 100px;
  .container {
    display: inline-block;
    vertical-align: middle;
    margin-top: 12px;
    padding: 0;
    position: relative;
  }
  .close {
    cursor: pointer;
    position: absolute;
    font-size: 24px;
    left: 138px;
    top: -5px;
  }
  .img_container {
    width: 150px;
    height: 150px;
    margin: 15px 5px 0;
    background-color: #fff;
    border-radius: 75px;
  }
  img {
    height: 132px;
    width: 132px;
    border-radius: 60px;
    margin-top: 9px;
  }
}
@media only screen and (max-width: 1280px) {
  .pc_applet_code {
    margin-top: 340px;
  }
}
</style>
