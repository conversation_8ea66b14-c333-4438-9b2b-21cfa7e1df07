<style lang="less" scoped>
.pc_set43 {
  height: 100%;
}
.pc_set43 .el-select {
  width: 100px;
}
.pc_set22 {
  margin-top: 30px;
  font-size: 16px;
  position: relative;
  line-height: 20px;
}
.pc_set23 {
  position: absolute;
  right: 0;
  top: 0;
}
.pc_set4 {
  float: left;
  width: 24px;
  height: 24px;
  background: #fff;
  border: 2px solid #D2D5D9;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  margin-top: 10px;
  cursor: pointer;
}
.pc_set39 {
  margin-top: 4px;
  overflow: hidden;
}
.pc_set41 {
  width: 14px;
  height: 14px;
  background: @themeBackGroundColor;
  margin-top: 3px;
  margin-left: 3px;
  border-radius: 50%;
  overflow: hidden;
}
.pc_set42 {
  float: left;
  margin-left: 20px;
  font-size: 16px;
  color: @themeFontColor;
  margin-top: 10px;
  margin-right: 10px;
  .edit{
    color: @themeBackGroundColor;
    margin-left: 10px;
    cursor: pointer;
  }
}
.pc_set44_col_manger {
  margin-top: 20px;
  overflow: hidden;
}
.pc_save {
  width: 120px;
  height: 44px;
  line-height: 44px;
  margin-top: 30px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background: linear-gradient(90deg, #d2ba8a 0%, #b49a5a 100%);
  border-radius: 4px;
  cursor: pointer;
}
.margin-left {
  margin-left: 40px;
}
.pc_setvip11 {
  width: 24px;
  height: 24px;
  float: left;
  margin-top: 7px;
  margin-right: 15px;
  background-image: url(../../../../image/zgzn-pos/pc_goods_checkbox1.png);
  cursor: pointer;
}
.pc_setvip13 {
  width: 24px;
  height: 24px;
  float: left;
  margin-top: 7px;
  margin-right: 15px;
  background-image: url(../../../../image/zgzn-pos/pc_goods_checkbox1.png);
}
.pc_setvip12 {
  width: 24px;
  height: 24px;
  float: left;
  margin-top: 7px;
  margin-right: 15px;
  background-image: url(../../../../image/zgzn-pos/pc_goods_checkbox2.png);
  cursor: pointer;
}
.pc_setvip14 {
  width: 24px;
  height: 24px;
  float: left;
  margin-top: 7px;
  margin-right: 15px;
  background-image: url(../../../../image/pc_goods_checkbox4.png);
}
.pc_setvip15 {
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border-radius: 4px;
  font-size: 16px;
  overflow: hidden;
  float: left;
}
.pc_setvip16 {
  width: 60px;
  height: 42px;
  background: @themeBackGroundColor;
  border-radius: 0px 4px 4px 0px;
  text-align: center;
  line-height: 42px;
  float: right;
  color: #FFF;
  cursor: pointer;
}
.pc_setvip17 {
  width: 120px;
  height: 44px;
  background: @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  line-height: 44px;
  font-size: 16px;color: #FFF;
  float: right;
  cursor: pointer;
}
.pc_setvip18 {
  border: 1px solid #E5E8EC;
  border-right: none;
  overflow: hidden;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  width: 240px;
  float: left;
  /deep/.el-input__inner {
    height: 40px;
  }
}
.pc_setvip18 input {
  float: left;width: 190px;margin-left: 20px;line-height: 40px;border: none;
}
.pc_setvip18 div {
  float: left;
  width: 17px;
  height: 17px;
  line-height: 14px;
  background: #BBB;
  border-radius: 50%;
  overflow: hidden;
  text-align: center;
  color: #FFF;
  margin-left: 2px;
  margin-top: 13px;
  cursor: pointer;
}
.pc_setvip19 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
}
.pc_setvip2 {
  right: 100px;
  position: absolute;
  bottom: 12px;
}
.pc_setvip21 {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;
}
.pc_setvip22 {
  position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);
  display: flex;align-items: center;
}
.pc_setvip23 {
  position: relative;
  z-index: 800;
  padding-bottom: 26px;
  margin: 0 auto;
  background: #FFF;
  width: 613px;
  overflow: hidden;
  border-radius: 5px;
  font-size: 16px;
  color: #B2C4CD;
}
.pc_setvip24 {
  line-height: 60px;
  width: calc(100% - 40px);
  margin-left: 20px;
  border-bottom: 1px solid #e3e6eb;
  overflow: hidden;
  color: @themeFontColor;
}
.pc_setvip25 {
  float: left;
  font-weight: bold;
  text-indent: 10px;
}
.pc_setvip26 {
  float: right;
  font-size: 30px;
  line-height: 56px;
  color: @themeFontColor;
  cursor: pointer;
}
.pc_setvip27 {
  width: 120px;
  height: 44px;
  margin-left: 20px;
  line-height: 42px;
  margin-top: 30px;
  color: @themeBackGroundColor;
  font-size: 16px;
  text-align: center;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid @themeBackGroundColor;
  float: right;
  margin-right: 20px;
}
.pc_setvip28 {
  width: 120px;
  height: 44px;
  margin-left: 20px;
  line-height: 42px;
  margin-top: 30px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background: @themeBackGroundColor;
  border-radius: 4px;
  cursor: pointer;
  float: right;
  margin-right: 43px;
}
/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ input[type="number"] {
  -moz-appearance: textfield;
}
/deep/ .el-table::before {
    height: 0px;
}
.pagination_container {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;
  border-top: 1px solid rgb(227, 230, 235);
}
/deep/ .el-table--enable-row-transition .el-table__body td {
  height: 50px;
}
/deep/ .el-table th {
  height: 50px;
}
.divider_line{
  height: 1px;
  background: #E4E7ED;
  margin-top: 20px;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
@fontColor:@themeFontColor;
@themeColor:#BDA169;
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 20px;
  .header_title{
    color: @fontColor;
    font-size: 18px;
    font-weight: 700;
    height: 60px;
    line-height: 60px;
  }
}
.dialog_content{
  font-size: 16px;
  margin-top: 20px;
  margin-left: 30px;
  display: flex;
  align-items: center;
}
.dialog_btn_container{
  display: flex;
  justify-content: flex-end;
  margin-top: 50px;
  margin-right: 20px;
  .btn{
    width: 120px;
    height: 44px;
    text-align: center;
    line-height: 34px;
    font-size: 16px;
    border-radius: 4px;
    cursor: pointer;
  }
}
.item{
  display: flex;
  align-items: center;
}
.btn_add{
  width: 120px;
  height: 44px;
  line-height: 44px;
  background: @themeBackGroundColor;
  color: white;
  border-radius: 4px;
  text-align: center;
  margin-right: 10px;
  cursor: pointer;
}
.form_container{
  background: #F9FBFB;
  margin: 0 30px;
  margin-top: 24px;
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 1px;
  font-size: 16px;
}
/deep/ .el-form-item__content {
  font-size: 16px;
}
.points_rate_table{
  margin-top: 30px;
  border: 1px solid #E4E7ED;
  margin-right: 12px;
  margin-bottom: 120px;
  /deep/.el-table__body-wrapper.is-scrolling-none {
    min-height: 40px;
  }
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.msgFontStyle {
  font-size: 14px;
  font-weight: 400;
  color: #FF5555;
  height: 18px;
  line-height: 17px;
}
.dialog_btn_delete{
  display: flex;
  justify-content: center;
  margin-top: 50px;
  padding-bottom: 30px;
  .btn{
    width: 140px;
    height: 50px;
    line-height: 38px;
    color: white;
    font-weight: bold;
    font-size: 20px;
  }
}
.deleteDialog {
  .title{
    color: @fontColor;
    font-size: 24px;
    text-align: center;
    padding-top: 40px;
    font-weight: bold;
  }
  .content{
    color: @fontColor;
    font-size: 24px;
    margin-top: 28px;
    text-align: center;
  }
}
.pc_short {
  width: 40%;
  height: 100px;
  background: #FFF7E6;
  border-radius: 4px;
  display: inline-block;
  margin-right: 20px;
  .pc_short1 {
    height: 100px;
    border-radius: 0 0 0 100%;
    width: 20%;
    float: right;
    background: #FFF1D4;
  }
  .pc_short2 {
    height: 100px;
    width: 30%;
    float: right;
    border-radius: 4px;
    background: #FFF1D4;
    .pc_short3 {
      width: 72%;
      height: 36px;
      background: @themeBackGroundColor;
      border-radius: 4px;
      margin-top: 30px;
      text-align: center;
      font-weight: 700;
      font-size: 16px;
      line-height: 35px;
      color: #FFFFFF;
      cursor: pointer;
    }
    .pc_short7 {
      font-size: 16px;
      line-height: 21px;
      cursor: pointer;
      color: @themeBackGroundColor;
      margin-top: 38px;
    }
  }
  .pc_short6 {
    width: 50%;
    height: 100px;
    float: left;
    .pc_short4 {
      margin-left: 14%;
      font-size: 14px;
      line-height: 18px;
      color: @themeBackGroundColor;
      margin-top: 16px;
    }
    .pc_short5 {
      margin-left: 14%;
      font-weight: 700;
      margin-top: 12px;
      font-size: 32px;
      line-height: 37px;
      color: @themeBackGroundColor;
    }
  }
}
.pc_short9 {
  margin-top: 2%;
  margin-bottom: 20px;
  font-weight: 700;
  font-size: 18px;
  line-height: 24px;
  color: @themeFontColor;
}
.pc_short10 {
  height: 54px;
  background: #F5F8FB;
  border: 1px solid #E3E6EB;
  box-sizing: border-box;
  border-radius: 4px;
  font-size: 14px;
  line-height: 52px;
  color: @text;
  padding-left: 10px;
  width: 81%;
  margin-top: 12px;
  margin-bottom: 10px;
}
.pc_short11 {
  font-size: 14px;
  line-height: 18px;
  color: #B2C3CD;
  margin-bottom: 20px;
}
.msg_item{
  display: flex;
  align-items: center;
  margin-bottom: 33px;
}
.notice_title{
  width: 173px;
  height: 17px;
  font-size: 16px;
  font-weight: 400;
  color: @themeFontColor;
  line-height: 17px;
}
.notice_remark{
  flex:1;
  margin-left:62px;
  font-size: 16px;
  font-weight: 400;
  color: #B2C3CD;
}
.member_disc_div {
  /deep/.el-input__inner {
    background: #fff;
    border-radius: 4px 0px 0px 4px;
  }
}
.input_suffix_div {
  border-radius: 0px 4px 4px 0px;
  display: inline-block;
  border: 1px solid #E3E6EB;
  background: #FAFAFA;
  width: 38px;
  height: 44px;
  line-height: 42px;
  text-align: center;
  margin-left: -5px;
}
.yes {
  border-color: @themeBackGroundColor;
}
#settingMemberGift {
  color: @themeBackGroundColor;
  cursor: pointer;
}
.notSettingMember {
  color: @themeBackGroundColor;
  cursor: pointer;
  margin-left: 25px;
}
.scopeBut {
  color: @themeBackGroundColor;
}
.setMemberGoods {
  line-height: 16px;
  margin-top: 25px;
  width: 170px;
  font-size: 16px;
  color: @themeBackGroundColor;
  cursor: pointer;
}
#cancelBut{
  border:1px solid @themeBackGroundColor;
  color:@themeBackGroundColor;
}
#saveBut {
  background:@themeBackGroundColor;
  margin-left:30px;
  color:white;
}
#confirmBut {
  background:@themeBackGroundColor;
  margin-left:30px;
}
.delete {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  float: left;
  border-radius: 4px;
  background: @themeBackGroundColor;
  color: #fff;
  cursor: pointer;
}
.scopeDel {
  color: @themeBackGroundColor;
  cursor: pointer;
}
#field {
  color: @themeFontColor;
}
/deep/ .el-tabs__header.is-top {
  border-bottom: none;
  margin: 0px;
}
#cancelBackground {
  background: @themeFontColor;
}
</style>
<template>
  <div class="pc_setvip">
    <vPcSMSContract v-if="isShowSMSContract" :isLogin="false" :fromType="2" @closeSMS="closeSMS" @sureSMS="sureSMS"></vPcSMSContract>
    <!-- 新增次卡 -->
    <div
      v-show="show_ckcard"
      class="pc_setvip21"
    >
      <div class="pc_setvip22">
        <div class="pc_setvip23">
          <div class="pc_setvip24">
            <div class="pc_setvip25">
              新增次卡
            </div>
            <div
              class="pc_setvip26"
              @click="show_ckcard = false"
            >×</div>
          </div>
          <div style="margin-top: 30px;margin-left: 50px;">
            <span style="color:red">*</span>
            <span id="field" style="font-weight: bold;">次卡名称</span>
            <el-input
              v-model="ck_name"
              maxlength=30
              style="width: 430px;margin-left: 20px;background: #F5F8FB;"
              clearable
            >
            </el-input>
          </div>
          <div style="margin-top: 30px;margin-left: 50px;">
            <span style="color:red">*</span>
            <span id="field" style="font-weight: bold;">销售价格</span>
            <el-input
              @input="ck_price = $priceLimit(ck_price)"
              v-model="ck_price"
              style="width: 430px;margin-left: 20px;"
              @blur="checkInput2('ck_price', 0, 999999.99)"
              clearable
            >
            </el-input>
          </div>
          <div style="margin-top: 25px;margin-left: 50px;overflow: hidden;min-height: 42px;;">
            <div style="float: left;">
              <div id="field" style="font-weight: bold;float: left;margin-top: 8px;">限制使用次数</div>
              <el-switch
                v-model="limitFlg"
                style="margin-left: 30px;float: left;margin-top: 10px;"
                :active-color="$t('image.homeImage.color')"
                inactive-color="#DCDFE6"
              ></el-switch>
            </div>
            <div
              style="float: left;margin-left: 68px;"
              v-show="limitFlg"
            >
              <span style="color:red">*</span>
              <span id="field" style="font-weight: bold;">可使用次数</span>
              <el-input
                @input="ck_count = $intMaxMinLimit({data: ck_count, max: 99999, min: 0})"
                v-model="ck_count"
                style="width: 180px;margin-left: 20px;"
                @blur="checkInput('ck_count', 'number', 1, 99999)"
                clearable
              >
              </el-input>
            </div>
          </div>
          <div style="margin-top: 25px;margin-left: 50px;min-height: 42px;">
            <div style="float: left;">
              <div id="field" style="font-weight: bold;float: left;margin-top: 8px;">有效时间</div>
              <el-select
                v-model="ck_type"
                placeholder="请选择"
                style="width: 430px;margin-left: 36px;"
              >
                <el-option
                  v-for="item in ckValidList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div
            v-if="ck_type === 'limitless'"
            style="margin-top: 25px;margin-left: 50px;overflow: hidden;min-height: 44px;"
          >
          </div>
          <div
            v-if="ck_type === 'days'"
            style="margin-top: 25px;margin-left: 50px;overflow: hidden;min-height: 42px;"
          >
            <div style="float: left;">
              <span style="color:red;float: left;margin-top: 8px;margin-right: 2px;">*</span>
              <div id="field" style="font-weight: bold;float: left;margin-top: 8px;;">有效天数</div>
              <el-input
                @input="ck_day_range = $intMaxMinLimit({data: ck_day_range, max: 99999, min: 0})"
                v-model="ck_day_range"
                style="width: 400px;margin-left: 27px;"
                @blur="checkInput('ck_day_range', 'number', 1, 99999)"
                clearable
              >
              </el-input>
              <span id="field" style="margin-left: 8px;">天</span>
            </div>
          </div>
          <div
            v-if="ck_type === 'period'"
            style="margin-top: 25px;margin-left: 50px;overflow: hidden;min-height: 42px;"
          >
            <div style="float: left;">
              <span style="color:red;float: left;margin-top: 8px;margin-right: 2px;">*</span>
              <div style="font-weight: bold;float: left;margin-top: 8px;">使用日期</div>
              <el-date-picker
                style="width: 430px;margin-left: 27px;"
                v-model="ck_period_range"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                :picker-options="pickerOptions"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </div>
          </div>
          <div style="overflow: hidden;">
            <div
              :style="{'cursor': save_ckSaveBtn_disabled ? 'pointer' : 'not-allowed'}"
              @click="save_ckSaveBtn_disabled ? ckSave() : false"
              class="pc_setvip28"
            >保存</div>
            <div
              @click="show_ckcard = false"
              class="pc_setvip27"
            >取消</div>
          </div>
        </div>
      </div>

    </div>
    <!-- 次卡确认删除 -->
    <div
      v-show='show_ck_confirm_delete'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;'
        @click='show_ck_confirm_delete = false'
      ></div>
      <div style='position: relative;z-index: 800;height: 285px;;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;'>
        <div style='width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: bold;'>提示</div>
        <div style='width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 400;padding-left: 23px;'> 该次卡仍有顾客未使用完，</div>
        <div style='width: 100%;text-align: center;font-size: 25px;color: #567485;font-weight: 400;'> 删除将导致无法正常使用</div>
        <div style='overflow: hidden;
          margin-top: 30px;
          font-size: 24px;'>
          <div
            id="cancelBackground"
            style='width: 138px;
              height: 50px;
              text-align: center;
              line-height: 48px;
              margin-left: 72px;
              color: #fff;
              float: left;
              border-radius: 4px;
              cursor: pointer;'
            @click='show_ck_confirm_delete = false'
          >取消</div>
          <div
            class="delete"
            @click='ckDelete(true)'
          >删除</div>
        </div>
      </div>
    </div>
    <!-- 次卡删除 -->
    <div
      v-show='show_ck_delete'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;'
        @click='show_ck_delete = false'
      ></div>
      <div style='position: relative;z-index: 800;height: 248px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;'>
        <div style='width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;'>提示</div>
        <div style='width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;'>删除后不可恢复，是否删除次卡？</div>
        <div style='overflow: hidden;
  margin-top: 30px;
  font-size: 24px;'>
          <div
            id="cancelBackground"
            style='width: 138px;
              height: 50px;
              text-align: center;
              line-height: 48px;
              margin-left: 72px;
              color: #fff;
              float: left;
              border-radius: 4px;
              cursor: pointer;'
            @click='show_ck_delete = false'
          >取消</div>
          <div
            class="delete"
            @click='ckDelete(false)'
          >删除</div>
        </div>
      </div>
    </div>
    <el-tabs
      :value="memberIndex"
      type="card"
      style="height: calc(100%);"
    >
      <div
        label="积分设置"
        v-show="memberIndex === 'first'"
        style="height: calc(100%); overflow:auto"
      >
        <!--1.7.0新版UI-->
        <div class="pc_set22" style="margin-top:0">
          <div style="line-height: 17px;font-size: 16px;font-weight: 700;">积分抵扣</div>
          <div class="pc_set39 pc_set44_col_manger">
            <div @click="setScoreDeduction(false, 'nothing')">
              <div
                class="pc_set4"
                style="margin-top: 16px;"
                :class="point_setting.score_deduction.deduction_flg === 'nothing' ? 'yes' : ''"
              >
                <div
                  class="pc_set41"
                  :style="point_setting.score_deduction.deduction_flg === 'nothing' ? '' : 'background: #FFF'"
                ></div>
              </div>
              <div
                class="pc_set42"
                style="margin-top: 18px;"
              >无</div>
            </div>
          </div>
          <div class="pc_set39" style="margin-top: 0">
            <div @click="setScoreDeduction(true, 'exchange')">
              <div
                class="pc_set4"
                style="margin-top: 16px;"
                :class="point_setting.score_deduction.deduction_flg === 'exchange' ? 'yes' : ''"
              >
                <div
                  class="pc_set41"
                  :style="point_setting.score_deduction.deduction_flg === 'exchange' ? '' : 'background: #FFF'"
                ></div>
              </div>
              <div
                class="pc_set42"
                style="margin-top: 18px;"
              >抵扣现金</div>
            </div>
            <div
              class="pc_set42"
              style="margin-top: 18px;margin-left:40px"
              v-if="point_setting.score_deduction.deduction_flg === 'exchange'"
            >消耗{{point_setting.score_deduction.consume_score}}积分，抵扣{{Number(point_setting.score_deduction.deduction_money).toFixed(2)}}元
              <span
                class="edit"
                @click="edit('deduction')"
              >编辑</span>
            </div>
          </div>
          <div class="pc_set39">
            <div @click="setScoreDeduction(true, 'gift')">
              <div>
                <div class="pc_set4"
                   :class="point_setting.score_deduction.deduction_flg === 'gift' ? 'yes' : ''">
                  <div
                    class="pc_set41"
                    :style="point_setting.score_deduction.deduction_flg === 'gift' ? '' : 'background: #FFF'"
                  ></div>
                </div>
                <div class="pc_set42">兑换礼品</div>
              </div>
            </div>
            <span
              class="pc_set42 margin-left"
              id="settingMemberGift"
              v-if="point_setting.score_deduction.deduction_flg === 'gift'"
              @click="setMemberGoods('setting_member_gift')"
            >设置兑换礼品→</span>
          </div>
          <div class='divider_line'></div>
        </div>
        <div class="pc_set22">
          <div style="line-height: 17px;font-size: 16px;font-weight: 700;">积分获取</div>
          <div class="item">
            <div style="margin-top:30px">消费得积分</div>
            <el-switch
              v-model="point_setting.score_get.consume_score_flg"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              style="margin-top:30px;margin-left:30px"
            ></el-switch>
            <div
              class="pc_set42"
              style="margin-top: 30px;margin-left:50px"
              v-if="point_setting.score_get.consume_score_flg"
            >每消费{{Number(point_setting.score_get.consume_money).toFixed(2)}}元，可得{{Number(point_setting.score_get.consume_get_score)}}积分
              <span
                class="edit"
                @click="edit('consume')"
              >编辑</span>
              <span class="notSettingMember"
                @click="setMemberGoods('setting_member_point')"
              >设置不积分商品→</span>
            </div>
          </div>
          <div class="item">
            <div style="margin-top:30px">充值得积分</div>
            <el-switch
              v-model="point_setting.score_get.credit_score_flg"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              style="margin-top:30px;margin-left:30px"
            ></el-switch>
            <div
              class="pc_set42"
              style="margin-top: 30px;margin-left:50px"
              v-if="point_setting.score_get.credit_score_flg"
            >每充值{{Number(point_setting.score_get.credit_money).toFixed(2)}}元，可得{{Number(point_setting.score_get.credit_get_score)}}积分
              <span
                class="edit"
                @click="edit('credit')"
              >编辑</span>
            </div>
          </div>
          <div class="item">
            <div style="margin-top:30px">注册得积分</div>
            <el-switch
              v-model="point_setting.score_get.register_score_flg"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              style="margin-top:30px;margin-left:30px"
            ></el-switch>
            <div
              class="pc_set42"
              style="margin-top: 30px;margin-left:50px"
              v-if="point_setting.score_get.register_score_flg"
            >注册会员可得{{Number(point_setting.score_get.register_get_score)}}积分
              <span
                class="edit"
                @click="edit('register')"
              >编辑</span>
            </div>
          </div>
          <!-- <div class="item">
            <div style="margin-top:30px">积分清空</div>
            <el-switch
              v-model="point_setting.score_clear.enable"
              active-color="#D5AA76"
              inactive-color="#DCDFE6"
              style="margin-top:30px;margin-left:46px"
            ></el-switch>
            <div
              class="pc_set42"
              style="margin-top: 30px;margin-left:50px"
              v-if="point_setting.score_clear.enable"
            >每年{{new Date(point_setting.score_clear.execute_date).getMonth() + 1}}月
            {{new Date(point_setting.score_clear.execute_date).getDate()}}日凌晨清空去年以及之前会员积分
            <span
              class="edit"
              @click="edit('clear')"
            >编辑</span>
            </div>
          </div>
          <div v-if="point_setting.score_clear.enable"
            style="margin-top:15px;margin-left:190px;color:#B2C3CD;">
            （积分清空如需通知会员请前往微信消息设置/短信消息设置中设置）
          </div> -->
          <div class='divider_line'></div>
        </div>
        <div class="pc_set22">
          <div style="line-height: 17px;font-size: 16px;font-weight: 700">会员生日积分设置</div>
          <div class="item">
            <div style="margin-top:30px">会员生日</div>
            <el-switch
              v-model="point_setting.score_vip_birthday.enable"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              style="margin-top:30px;margin-left:45px"
            ></el-switch>
            <div
              class="pc_set42"
              style="margin-top: 30px;margin-left:50px"
              v-if="point_setting.score_vip_birthday.enable"
            >会员生日{{Number(point_setting.score_vip_birthday.times)}}倍积分
              <span v-if="ultimate">，提前{{Number(point_setting.score_vip_birthday.days)}}天通知会员</span>
              <span
                class="edit"
                @click="edit('birthday')"
              >编辑</span>
            </div>
          </div>
          <div class='divider_line'></div>
        </div>
        <div class="pc_set22">
          <div style="display:flex;justify-content:space-between;align-items:center;margin-top:-12px">
            <div style="line-height: 17px;font-size: 16px;font-weight: 700;">
              积分倍率设置
              <span style="font-weight: 400;color: #B2C3CD;">
                （若积分倍率冲突，则选择最高的积分倍数）
              </span>
            </div>
            <div
              class="btn_add"
              @click="addPointsRate"
            >添加</div>
          </div>
        </div>
        <!--积分倍率表格-->
        <div class="points_rate_table" :style="tableHeight">
          <el-table
            :data="pointsTableData.slice((curren_page-1)*limits,curren_page*limits)"
            height="100%"
          >
            <el-table-column
              prop="name"
              label="类型"
              align="center"
              min-width="27%"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="times"
              label="积分倍数"
              align="center"
              min-width="15%"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              prop="date"
              label="适用时间"
              align="center"
              min-width="33%"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              min-width="25%"
            >
              <template slot-scope="scope">
                <div class="scopeBut" >
                  <span style="cursor: pointer" @click="handleEdit(scope.$index, scope.row)">编辑</span>
                  <span style="display:inline-block;margin:0 10px">|</span>
                  <span style="cursor: pointer" @click="deleteConfirm(scope.$index)">删除</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        <div
            class="pagination_container"
            v-if="pointsTableData.length > limits"
          >
            <el-pagination
              layout="prev, pager, next"
              :total="pointsTableData.length"
              @current-change="handleCurrentChangeOne"
              :current-page="curren_page"
              :page-size="limits"
            ></el-pagination>
          </div>
        </div>
      </div>
      <div
        label="会员日设置"
        v-show="memberIndex === 'second'"
      >
        <div class="pc_set22" style="margin-top: 0px">
          <div style="position: relative;height: 40px;width: 360px;">
            <div style="line-height: 17px;font-size: 16px;font-weight: 700;">会员日设置</div>
            <el-switch
              class="pc_set23"
              v-model="vipday_setting.enable"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
          </div>
          <div>
            <div style="overflow: hidden;">
              <div class="pc_set39">
                <div
                  :style="{'pointer-events': vipday_setting.enable ? 'auto' : 'none'}"
                  @click="vipday_setting.vipday_flg = 'week'"
                >
                  <div
                    class="pc_set4"
                    :class="vipday_setting.vipday_flg == 'week' && vipday_setting.enable ? 'yes' : ''"
                  >
                    <div
                      class="pc_set41"
                      :style="vipday_setting.vipday_flg == 'week' ?
                    (vipday_setting.enable ? '' : 'background: #D2D5D9') : 'background: #FFF'"
                    ></div>
                  </div>
                  <div class="pc_set42" style="margin-left: 5px;margin-right: 35px;">每周</div>
                </div>
                <div>
                  <el-select
                    :disabled="!vipday_setting.enable || vipday_setting.vipday_flg !== 'week'"
                    v-model="vipday_setting.week"
                    style="width: 200px;"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="wk in week_list"
                      :key="wk.value"
                      :label="wk.label"
                      :value="wk.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div class="pc_set39" style="margin-top: 20px;">
                <div
                  :style="{'pointer-events': vipday_setting.enable ? 'auto' : 'none'}"
                  @click="vipday_setting.vipday_flg = 'month'"
                >
                  <div
                    class="pc_set4"
                    :class="vipday_setting.vipday_flg == 'month' && vipday_setting.enable ? 'yes' : ''"
                  >
                    <div
                      class="pc_set41"
                      :style="vipday_setting.vipday_flg == 'month' ?
                    (vipday_setting.enable ? '' : 'background: #D2D5D9') : 'background: #FFF'"
                    ></div>
                  </div>
                  <div class="pc_set42"  style="margin-left: 5px;margin-right: 35px;">每月</div>
                </div>
                <div>
                  <el-select
                    :disabled="!vipday_setting.enable || vipday_setting.vipday_flg !== 'month'"
                    v-model="vipday_setting.days"
                    style="width: 200px;"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="mt in month_list"
                      :key="mt.value"
                      :label="mt.label"
                      :value="mt.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
            </div>
            <div
              v-show="vipday_setting.enable"
              class="setMemberGoods"
              @click="setMemberGoods('setting_member_day')"
            >设置会员日活动商品 →</div>
            <div style="margin-top: 30px;" class="member_disc_div">
              参加会员日活动的商品为商品管理中零售价的
              <el-input
                @input="vipday_setting.discount = $memberDiscountLimit(vipday_setting.discount)"
                :disabled="!vipday_setting.enable"
                v-model="vipday_setting.discount"
                style="width: 80px;margin-left: 10px"
                @blur="checkInput('vipday_setting.discount', 'decimal', 0.1, 10.0, 1, 10.0)"
              ></el-input>
              <div class="input_suffix_div" style="">折</div>
            </div>
            <div style="line-height: 16px;margin-top: 25px;font-size: 16px;color: #B2C4CE;">参加会员日活动的商品折扣不与其他优惠叠加</div>
          </div>
          <div style="height: 10px;"></div>
        </div>
      </div>
      <div
        label="次卡设置"
        v-show="memberIndex === 'third'"
      >
        <div
          class="pc_set22"
          style="width: 100%;margin-top: 0px"
        >
          <div style="overflow: hidden;">
            <div class="pc_setvip15">
              <div class="pc_setvip18">
                <input
                  placeholder="输入次卡名称"
                  v-model="ck_keyword"
                  class="el-input__inner"
                  style="width: 100%;margin-left: 0px;"
                  maxlength="30"
                />
                <div
                  v-show="ck_keyword != ''"
                  style="position: fixed;margin-left: 210px;padding-left: 1.3px;"
                  @click="ck_keyword = ''"
                >×</div>
              </div>
              <div
                class="pc_setvip16"
                @click="ckSearch()"
              >查询</div>
            </div>
            <div
              class="pc_setvip17"
              @click="ckAdd()"
            >新增次卡</div>
          </div>
          <div style="
            width: 100%;
            background: #fff;
            border: 1px solid #e3e6eb;
            border-radius: 5px;
            position: relative;
            margin-top: 17px;
          ">
            <el-table
              :data="tableData"
              :header-cell-style="tableHeaderColor"
              :cell-style="tableRowStyle"
              style="width: 100%; font-size: 16px; color: #5a7788"
              stripe
              :height="tableData == '' ? '155px' : table_height"
            >
              <el-table-column
                prop="name"
                label="次卡名称"
                min-width="25%"
                show-overflow-tooltip
              > </el-table-column>
              <el-table-column
                prop="restTimes"
                label="可用次数"
                align="right"
                min-width="15%"
                show-overflow-tooltip
              > </el-table-column>
              <el-table-column
                prop="price"
                label="销售价格"
                align="right"
                min-width="15%"
                show-overflow-tooltip
              >
                <template slot-scope="scope">{{$toDecimalFormat(scope.row.price, 2, true)}}</template>
              </el-table-column>
              <el-table-column
                prop="period"
                label="有效时间"
                align="center"
                min-width="35%"
                show-overflow-tooltip
              >
              </el-table-column>
              <el-table-column
                label="操作"
                width="80"
                align="center"
                min-width="10%"
              >
                <template slot-scope="scope">
                  <span
                    class="scopeDel"
                    @click="ck_del_id=scope.row.id;show_ck_delete = true"
                  >删除</span>
                </template>
              </el-table-column>
            </el-table>
            <!--分页器-->
            <div
              class="pagination_container"
              v-if="tableAllData.length > limit"
            >
              <el-pagination
                layout="prev, pager, next"
                :total="total"
                @current-change="handleCurrentChange"
                :current-page.sync="pagenum"
                :page-size="limit"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
      <div
        label="短信消息设置"
        v-show="memberIndex === 'four'"
      >
        <div class="pc_short" :style="screenRelWidth < 1366 ? 'width: 46.5%' : ''">
          <div class="pc_short6">
            <div class="pc_short4">当前剩余可用条数</div>
            <div class="pc_short5">{{residueCount}}</div>
          </div>
          <div class="pc_short2">
            <div class="pc_short3" :style="screenRelWidth < 1366 ? 'width: 96%' : ''" @click="gotoBuyMsg">前往充值</div>
          </div>
          <div class="pc_short1"></div>
        </div>
        <div class="pc_short" :style="screenRelWidth < 1366 ? 'width: 46.5%' : ''">
          <div class="pc_short6">
            <div class="pc_short4">已使用条数</div>
            <div class="pc_short5">{{usedCount}}</div>
          </div>
          <div class="pc_short2">
            <div class="pc_short7" @click="gotoHistory" :style="screenRelWidth < 1366 ? 'margin-left: 6px' : ''"
              style="font-family: Microsoft YaHei, sans-serif;">
              购买/发送记录>
            </div>
          </div>
          <div class="pc_short1"></div>
        </div>
        <div class="pc_short9">模板展示</div>
        <!-- 只有旗舰版才开放最后一条数据：发送寄取件短信功能，其他版本不显示此功能 -->
        <div
          class="msg_item"
          style="display: block;margin-bottom: 0;"
          v-for="(item,index) in noticeArr"
          :key="index"
          v-show="index > 4 && index < (ultimate ? noticeArr.length : noticeArr.length - 1)"
        >
          <div class="notice_title" style="display: inline-block">{{item.notice_title}}</div>
          <el-switch
            v-model="item.flag"
            :active-color="$t('image.homeImage.color')"
            @change="upLoadPushMsg"
          >
          </el-switch>
          <div v-show="item.notice_remark !== ''" class="pc_short10" :style="screenRelWidth < 1366 ? 'width: 96%' : ''">{{item.notice_remark}}</div>
        </div>
      </div>
      <div
        label="微信消息设置"
        v-show="memberIndex === 'five'"
      >
        <div
          class="msg_item"
          v-for="(item,index) in noticeArr"
          :key="index"
          v-show="index < 5"
        >
          <div class="notice_title">{{item.notice_title}}</div>
          <el-switch
            v-model="item.flag"
            :active-color="$t('image.homeImage.color')"
            @change="upLoadPushMsg"
          >
          </el-switch>
          <div class="notice_remark">{{item.notice_remark}}</div>
        </div>
      </div>
    </el-tabs>
    <!--抵扣现金弹窗-->
    <el-dialog
      :visible.sync="showExchange"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">抵扣现金</div>
        </div>
        <div class="dialog_content">
          <div>每消耗</div>
            <div style="width:120px;margin:0 10px">
              <el-input
                @input="pointInfo.point = $intMaxMinLimit({data: pointInfo.point, max: 99999, min: 0})"
                @blur="checkInput3('pointInfo.point', 1, 99999, 0, 10)"
                v-model="pointInfo.point"/>
            </div>
            <div>积分，抵扣</div>
            <div style="width:120px;margin:0 10px">
              <el-input
                @input="pointInfo.money = $intMaxMinLimit({data: pointInfo.money, max: 99999, min: 0})"
                @blur="checkInput3('pointInfo.money', 1, 99999)"
                v-model="pointInfo.money"/>
            </div>
            <div>元</div>
        </div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
          id="cancelBut"
          @click=" showExchange = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('deduction') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--消费得积分弹窗-->
    <el-dialog
      :visible.sync="showConsumeScore"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">消费得积分</div>
        </div>
        <div class="dialog_content">
          <div>每消费</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.money = $priceLimit(pointInfo.money)"
              @blur="checkInput3('pointInfo.money', 1, 999999.99, 2, 10)"
              v-model="pointInfo.money"/>
          </div>
          <div>元，得</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.point = $intMaxMinLimit({data: pointInfo.point, max: 99999, min: 0})"
              @blur="checkInput3('pointInfo.point', 1, 99999)"
              v-model="pointInfo.point"/>
          </div>
          <div>积分</div>
        </div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
          id="cancelBut"
          @click="showConsumeScore = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('consume') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--充值得积分弹窗-->
    <el-dialog
      :visible.sync="showCreditScore"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">充值得积分</div>
        </div>
        <div class="dialog_content">
          <div>每充值</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.money = $priceLimit(pointInfo.money)"
              @blur="checkInput3('pointInfo.money', 1, 99999.99, 2, 10)"
              v-model="pointInfo.money"/>
          </div>
          <div>元，得</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.point = $intMaxMinLimit({data: pointInfo.point, max: 99999, min: 0})"
              @blur="checkInput3('pointInfo.point', 1, 99999)"
              v-model="pointInfo.point"/>
          </div>
          <div>积分</div>
        </div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
          id="cancelBut"
          @click="showCreditScore = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('credit') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--注册得积分弹窗-->
    <el-dialog
      :visible.sync="showRegisterScore"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">注册得积分</div>
        </div>
        <div class="dialog_content">
          <div>注册会员可得</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.point = $intMaxMinLimit({data: pointInfo.point, max: 99999, min: 0})"
              @blur="checkInput3('pointInfo.point',1, 99999, 0, 100)"
              v-model="pointInfo.point"/>
          </div>
          <div>积分</div>
        </div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
          id="cancelBut"
          @click="showRegisterScore = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('register') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--积分清空日期设置弹窗-->
    <!-- <el-dialog
      :visible.sync="showClearScore"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">积分清空</div>
        </div>
        <div class="dialog_content">
          <div>每年</div>
          <div class="score_clear_div">
            <el-date-picker popper-class="datePicker_month" v-model="pointInfo.date" type="month"
              :editable="false" placeholder="月" format="MM" value-format="MM-dd">
            </el-date-picker>
            <span>月</span>
            <el-date-picker popper-class="datePicker_day" v-model="pointInfo.date" type="date"
              :editable="false" placeholder="日" format="dd" value-format="MM-dd">
            </el-date-picker>
            <span>日</span>
          </div>
          <div>清空去年以及之前会员积分</div>
        </div>
        <div style="padding: 5px 30px;color: #888;font-size: 15px;margin-top: 15px;">
          例：若当前年为2021年，设置日期为6月1日时，2021年6月1日凌晨会清除2020年12月31日之前会员获取的积分，
          2021年1月1日至2021年6月1日获取的会员积分保留。
        </div>
      </div> -->
      <!--按钮区-->
      <!-- <div class="dialog_btn_container">
        <div
          class="btn"
          style="border:1px solid @themeBackGroundColor;color:@themeBackGroundColor"
          @click="showClearScore = false;"
        >取消</div>
        <div
          class="btn"
          style="background:@themeBackGroundColor;margin-left:30px;color:white"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('clear') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog> -->
    <!--会员生日弹窗-->
    <el-dialog
      :visible.sync="showBirthday"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="600px"
    >
      <div>
        <div class="dialog_header">
          <div class="header_title">会员生日多倍积分</div>
        </div>
        <div class="dialog_content">
          <div>会员生日</div>
          <div style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.money = $intMaxMinLimit({data: pointInfo.money, max: 99999, min: 0})"
              @blur="checkInput3('pointInfo.money', 1, 99999)"
              v-model="pointInfo.money"/>
          </div>
          <div>倍积分</div>
          <div v-if="ultimate">，提前</div>
          <div v-if="ultimate" style="width:120px;margin:0 10px">
            <el-input
              @input="pointInfo.point = $intMaxMinLimit({data: pointInfo.point, max: 90, min: 0})"
              @blur="checkInput3('pointInfo.point', 1, 99999)"
              v-model="pointInfo.point"/>
          </div>
          <div v-if="ultimate" >天通知会员</div>
        </div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
         id="cancelBut"
          @click="showBirthday = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerSave('birthday') : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--积分倍率弹窗-->
    <el-dialog
      :visible.sync="showPointsRate"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="700px"
    >
      <div class="dialog_header">
        <div class="header_title">多倍积分设置</div>
      </div>
      <!--radio选择区-->
      <div style="display:flex;align-items:center;padding:0 30px">
        <div
          style="display:flex;align-items:center"
          v-for="(item,index) in pointsRateArr"
          :key="index"
          @click="pointsRateIndex = index"
        >
          <div
            class="pc_set4"
            style="margin-top: 16px"
            :class="pointsRateIndex === item.index ? 'yes' : ''"
          >
            <div
              class="pc_set41"
              :style="pointsRateIndex === item.index ? '' : 'background: #FFF'"
            ></div>
          </div>
          <div
            class="pc_set42"
            style="margin-top: 18px;margin-right: 37px"
          >{{item.name}}</div>
        </div>
      </div>
      <!--表单区-->
      <div class="form_container" id="second">
        <el-form
          :model="editData"
        >
          <el-form-item v-if="pointsRateIndex === 0">
            <div style="display:flex;align-items:center" >
              <div><span style="color:red;margin-left:5px">*</span>积分倍数</div>
              <el-input
                @input="editData.holiday_times = $intMaxMinLimit({data: editData.holiday_times, max: 99999, min: 0})"
                id="holiday_times"
                @blur="checkInput3('editData.holiday_times', 1, 99999)"
                @focus="selectText('holiday_times')"
                style="flex:1;margin-left:20px"
                v-model="editData.holiday_times"
                />
            </div>
           <div class="msgFontStyle" style="margin-left: 95px;" :style="checkFlags.holiday_times ? '':'visibility:hidden'">*请输入积分倍数</div>
            <div style="display:flex;align-items:center">
              <div><span style="color:red;margin-left:5px;">*</span>适用日期</div>
              <el-date-picker
                v-model="dateValue"
                type="daterange"
                range-separator="-"
                @blur="deleteDateCheck()"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="flex:1;margin-left:20px;"
                :style="checkFlags.dateValue ? 'border-color: red':''"
              >
              </el-date-picker>
            </div>
          <div class="msgFontStyle" style="margin-left: 95px;" :style="checkFlags.dateValue ? '':'visibility:hidden'">*请输入适用日期</div>
          </el-form-item>
          <el-form-item v-if="pointsRateIndex === 1">
            <div @click="editData.type = 'week'" style="align-items: center">
              <div
                class="pc_set4"
                style="margin-top:9px"
                :class="editData.type === 'week' ? 'yes' : ''"
              >
                <div
                  class="pc_set41"
                  :style="editData.type === 'week' ? '' : 'background: #FFF'"
                ></div>
              </div>
            </div>
            <div class="item">
              <div style="margin-left:20px;width:60px">每周</div>
              <el-select
                placeholder="请选择"
                style="width:130px"
                :disabled="editData.type !== 'week'"
                v-model="week_list_value"
              >
                <el-option
                  v-for="item in week_list"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <div style="margin-left:10px;visibility:hidden">号</div>
              <div>，消费获得</div>
              <el-input
                @input="editData.week_times = $intMaxMinLimit({data: editData.week_times, max: 99999, min: 0})"
                id="week_times"
                :disabled="editData.type !== 'week'"
                @blur="checkInput3('editData.week_times',1, 99999)"
                @focus="selectText('week_times')"
                style="width:130px;margin:0 10px" v-model="editData.week_times"/>
              <div>倍积分</div>
            </div>
            <div class="msgFontStyle" style="float:right; margin-right: 147px" :style="checkFlags.week_times ? '':'visibility:hidden'">*请输入积分倍数</div>
            <div @click="editData.type = 'month'" style="align-items: center">
              <div
                class="pc_set4"
                style="margin-top:29px"
                :class="editData.type === 'month' ? 'yes' : ''"
              >
                <div
                  class="pc_set41"
                  :style="editData.type === 'month' ? '' : 'background: #FFF'"
                ></div>
              </div>
            </div>
            <div
              class="item"
              style="margin-top:20px"
            >
              <div style="margin-left:20px;width:60px">每月</div>
              <el-input
                style="width:130px"
                @input="editData.month = $intMaxMinLimit({data: editData.month, max: 31, min: 0})"
                id="month"
                :disabled="editData.type !== 'month'"
                @blur="checkInput3('editData.month',1, 31)"
                @focus="selectText('month')"
                v-model="editData.month"></el-input>
              <div style="margin-left:10px">号</div>
              <div>，消费获得</div>
              <el-input
                @input="editData.month_times = $intMaxMinLimit({data: editData.month_times, max: 99999, min: 0})"
                id="month_times"
                :disabled="editData.type !== 'month'"
                @blur="checkInput3('editData.month_times',1, 99999)"
                @focus="selectText('month_times')"
                style="width:130px;margin:0 10px" v-model="editData.month_times"></el-input>
              <div>倍积分</div>
            </div>
            <div @click="editData.type = 'day'">
               <div
                class="pc_set4"
                style="margin-top:28px"
                :style="editData.type === 'day' ? 'yes' : ''"
              >
                <div
                  class="pc_set41"
                  :style="editData.type === 'day' ? '' : 'background: #FFF'"
                ></div>
              </div>
            <div class="msgFontStyle" style="float:left;margin-left: 79px" :style="checkFlags.month ? '':'visibility:hidden'">*请输入日期</div>
            <div class="msgFontStyle" style="float:right; margin-right: 147px" :style="checkFlags.month_times ? '':'visibility:hidden'">*请输入积分倍数</div>
            </div>
            <div
              class="item"
              style="margin-top:20px"
            >
              <div style="margin-left:20px;width:60px">每月逢</div>
              <el-input
                style="width:130px;"
                @input="editData.day_pick = $intMaxMinLimit({data: editData.day_pick, max: 9, min: 0})"
                id="day_pick"
                :disabled="editData.type !== 'day'"
                @blur="checkInput3('editData.day_pick',1, 9)"
                @focus="selectText('day_pick')"
                v-model="editData.day_pick"></el-input>
              <div style="margin-left:10px;visibility:hidden">号</div>
              <div>，消费获得</div>
              <el-input
                @input="editData.day_pick_times = $intMaxMinLimit({data: editData.day_pick_times, max: 99999, min: 0})"
                id="day_pick_times"
                class="checkClass"
                :disabled="editData.type !== 'day'"
                @blur="checkInput3('editData.day_pick_times',1, 99999)"
                @focus="selectText('day_pick_times')"
                style="width:130px;margin:0 10px" v-model="editData.day_pick_times"></el-input>
              <div>倍积分</div>
            </div>
            <div class="msgFontStyle" style="float:left;margin-left: 102px" :style="checkFlags.day_pick ? '':'visibility:hidden'">*请输入日期</div>
            <div class="msgFontStyle" style="float:right; margin-right: 147px" :style="checkFlags.day_pick_times ? '':'visibility:hidden'">*请输入积分倍数</div>
          </el-form-item>
        </el-form>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_container">
        <div
          class="btn"
          id="cancelBut"
          @click="cancelClose"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          :style="{'cursor': save_btn_disabled ? 'pointer' : 'not-allowed'}"
          @click="save_btn_disabled ? innerDataSave() : false"
        >保存</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
    <!--删除提示弹窗-->
    <el-dialog
      :visible.sync="showDelete"
      :show-close='false'
      :close-on-click-modal='false'
      :append-to-body='true'
      width="400px"
    >
      <div class="deleteDialog">
        <div class="title">提示</div>
        <div class="content">确定删除这条数据？</div>
      </div>
      <!--按钮区-->
      <div class="dialog_btn_delete">
        <div
          class="btn"
          id="cancelBackground"
          @click="showDelete = false;"
        >取消</div>
        <div
          class="btn"
          id="saveBut"
          @click="deleteSure"
        >确定</div>
      </div>
      <div style="height:20px"></div>
    </el-dialog>
  </div>
</template>

<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { showToast } from '@/utils/util.js';
import { Dialog, Input, Form, DatePicker, Select, Table, TableColumn } from 'element-ui';
import vPcSMSContract from '@/components/pc_sms_contract.vue';
import logList from '@/config/logList';
export default {
  components: {
    [Dialog.name]: Dialog,
    [Input.name]: Input,
    [Form.name]: Form,
    [DatePicker.name]: DatePicker,
    [Select.name]: Select,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    vPcSMSContract
  },
  data () {
    return {
      pointInfo: {
        point: 10,
        money: 1
        // date: new Date('06-01').format('MM-dd')
      },
      tableMarginBottom: '',
      showAdvance: demo.$rest.version.indexOf('旗舰版') !== -1,
      checkFlags: {
        holiday_times: false,
        week_times: false,
        month: false,
        month_times: false,
        day_pick: false,
        day_pick_times: false,
        dateValue: false
      },
      addFlag: false,
      showExchange: false, // 显示抵扣现金弹窗
      showConsumeScore: false, // 显示消费得积分弹窗
      showCreditScore: false, // 显示充值得积分弹窗
      showRegisterScore: false, // 显示注册得积分弹窗
      showBirthday: false, // 显示会员生日弹窗
      // showClearScore: false, // 显示积分清空设置弹窗
      showPointsRate: false, // 积分倍率设置弹窗
      showDelete: false, // 删除提示弹窗
      dateValue: '',
      pointsRateArr: [
        {
          name: '特殊节日多倍积分',
          index: 0
        },
        {
          name: '指定周期多倍积分',
          index: 1
        }
      ],
      pointsRateIndex: 0,
      pointsTableData: [],
      editData: '',
      editIndex: 0,
      // 全局变量
      member_tab_select: 'first', // 显示哪个大tab
      save_btn_disabled: false, // 是否可以点击保存
      save_ckSaveBtn_disabled: true,
      week_list: [
        {
          value: 1,
          label: '星期一'
        },
        {
          value: 2,
          label: '星期二'
        },
        {
          value: 3,
          label: '星期三'
        },
        {
          value: 4,
          label: '星期四'
        },
        {
          value: 5,
          label: '星期五'
        },
        {
          value: 6,
          label: '星期六'
        },
        {
          value: 0,
          label: '星期日'
        }
      ],
      week_list_value: 1,
      month_list: [...Array(31).keys()].map((e) => {
        return { value: e + 1, label: e + 1 + '日' };
      }),
      ckValidList: [{value: 'limitless', label: '不限制'}, {value: 'days', label: '限制使用天数'}, {value: 'period', label: '使用固定日期'}],
      // 积分设置
      point_setting: {
        score_get: {
          enable: false,
          consume_score_flg: false,
          consume_money: 10,
          consume_get_score: 1,
          credit_score_flg: false,
          credit_money: 10,
          credit_get_score: 1,
          register_score_flg: false,
          register_get_score: 100
        },
        score_deduction: {
          enable: false,
          deduction_flg: '',
          consume_score: 10,
          deduction_money: 1
        },
        score_vip_birthday: {
          enable: false,
          times: 2,
          days_flg: false,
          days: 3
        },
        multiple_score: [],
        score_clear: {
          enable: false,
          days: 365
          // execute_date: new Date('06-01').format('MM-dd'),
          // clear_date: new Date('12-31').format('MM-dd'),
          // notice_before_days: 5
        }
      },
      // 次卡
      ck_del_id: null,
      ck_keyword: '',
      table_height: 0,
      tableHeight: {
        height: '152px'
      },
      show_ckcard: false,
      show_ck_delete: false,
      show_ck_confirm_delete: false,
      ck_name: '',
      ck_price: '',
      limitFlg: true,
      ck_count: '',
      ck_type: 'limitless',
      ck_period_range: '',
      ck_day_range: '',
      tableData: [],
      tableAllData: [],
      // 通知设置
      vipday_setting: {
        enable: false,
        vipday_flg: '',
        week: 1,
        days: 1,
        discount: 9.0
      },
      // 分页器
      total: 0,
      pagenum: 1,
      limit: 0,
      curren_page: 1,
      limits: 10,
      screenRelWidth: document.body.clientWidth,
      residueCount: 0, // 短信可用条数
      usedCount: 0, // 短信已用条数
      noticeArr: [
        { key: 'balance_notice', notice_title: '账户变动通知', notice_remark: '账户余额及积分变动时，推送消息通知', flag: false },
        { key: 'integral_change_notice', notice_title: '积分兑换通知', notice_remark: '积分兑换产生积分变动时，推送消息通知', flag: false },
        { key: 'vip_day_notice', notice_title: '会员日活动通知', notice_remark: '会员日活动时，向会员推送活动通知', flag: false },
        { key: 'vip_card_notice', notice_title: '次卡消息通知', notice_remark: '会员办理次卡或消费次卡时，推送消息通知', flag: false },
        { key: 'access_product_notice', notice_title: '会员寄取通知', notice_remark: '会员寄件或取件时，推送消息通知', flag: false },
        // { key: 'wx_point_clear_notice', notice_title: '会员积分清空', notice_remark: '积分清空提前5天向会员发送信息提醒', flag: false },
        { key: 'short_balance_notice', notice_title: '账户变动短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12消费57元，余120元，余298积分', flag: false },
        { key: 'short_integral_change_notice', notice_title: '积分兑换短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12进行了积分兑换，共使用99积分', flag: false },
        { key: 'short_vip_card_notice', notice_title: '次卡消息短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12购买了(摇摇车)次卡，共1张', flag: false },
        { key: 'short_access_product_notice', notice_title: '会员寄取短信通知', notice_remark: '例：您在兴旺店铺于3-31 12:23:12寄存商品[啤酒]，寄存数量[12]，剩余数量[12]', flag: false }
        // { key: 'sms_point_clear_notice', notice_title: '积分清空短信通知', notice_remark: '【大云数智】您在【店铺名称】去年所获积分于【月/日 时间】清空', flag: false }
      ],
      isShowSMSContract: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < (Date.now() - 1000 * 60 * 60 * 24);
        }
      }
    };
  },
  created () {
    this.screenRelWidth = document.body.clientWidth;
    demo.$http
      .post(
        this.$rest.pc_vipGetSetting,
        {
          phone: this.sysUid,
          sysSid: this.sysSid,
          systemName: $config.systemName
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
      .then((res) => {
        if (res.data.code === '0') {
          if (res.data.data) {
            this.point_setting = res.data.data.pointSetting
              ? demo.t2json(res.data.data.pointSetting)
              : this.point_setting;
            // this.isOldScoreClear();
            this.tableDataFormat(this.point_setting);
            this.vipday_setting = res.data.data.vipdaySetting
              ? demo.t2json(res.data.data.vipdaySetting)
              : this.vipday_setting;
          }
        } else {
          if (res.data.msg.indexOf('该用户没有被授权访问资源') === -1) {
            demo.msg('warning', res.data.msg);
          }
        }
        setTimeout(() => {
          this.save_btn_disabled = true;
        }, 1500);
      }).catch(error => {
        console.log(error);
        setTimeout(() => {
          this.save_btn_disabled = true;
        }, 1500);
      });
    this.ckSearch();
  },
  mounted () {
    this.table_height = $(window).height() - 320;
    this.limit = Math.floor((this.table_height - 50) / 50);
    this.getNoticeFlag();
    this.getMessageCount();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    sureSMS() {
      this.isShowSMSContract = false;
    },
    closeSMS() {
      this.isShowSMSContract = false;
      this.SET_SHOW({ isMemberSetting: false, isMember: true });
    },
    getMessageCount () {
      const param = {
        'paidServiceCode': '3',
        'phone': this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName
      };
      demo.$http.post(this.$rest.selectSmsCount, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(rs => {
          const obj = rs.data;
          if (obj.code === 200 && obj.data !== null) {
            this.residueCount = obj.data.residueCount;
            this.usedCount = obj.data.usedCount;
          }
        });
    },
    gotoBuyMsg () {
      this.SET_SHOW({ isMemberSetting: false });
      this.SET_SHOW({ msgFrom: 1 });
      this.SET_SHOW({ isShortMessage: true });
    },
    gotoHistory () {
      this.SET_SHOW({ isMemberSetting: false });
      this.SET_SHOW({ msgFrom: 1 });
      this.SET_SHOW({ tabIndexShort: 1 });
      this.SET_SHOW({ isMessageHistory: true });
    },
    /**
     * 提交推送通知flag
     */
    upLoadPushMsg () {
      const notifySetting = Object.assign(...this.noticeArr.map(notice => {
        let r = {};
        r[notice.key] = notice.flag;
        return r;
      }));
      const param = {
        'systemName': $config.systemName,
        'phone': this.sysUid,
        'sysSid': this.sysSid,
        'notifySetting': notifySetting,
        'createUser': this.sysUid
      };
      demo.$http.post(this.$rest.pc_settingVip, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(rs => {
          const obj = rs.data;
          if (obj.code !== '0') {
            showToast(this, obj.msg);
          }
        });
    },
    /**
     * 获取推送消息的flag
     */
    getNoticeFlag () {
      const param = {
        'systemName': $config.systemName,
        'phone': this.sysUid,
        'sysSid': this.sysSid
      };
      demo.$http.post(this.$rest.pc_vipGetSetting, param, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': demo.$store.state.show.token
        },
        maxContentLength: Infinity,
        timeout: 60000
      })
        .then(rs => {
          const obj = rs.data;
          console.log('==获取通知flag', obj);
          if (obj.code === '0') {
            if (obj.data && obj.data.notifySetting) {
              const notifySetting = demo.t2json(obj.data.notifySetting);
              this.noticeArr.map(e => {
                e.flag = notifySetting[e.key];
              });
            }
            return;
          }
          showToast(this, obj.msg);
        });
    },
    /**
     * 添加积分倍率
     */
    addPointsRate () {
      this.addFlag = true;
      this.pointsRateIndex = 0;
      const editDataEmpty = {
        name: '',
        times: '',
        date: '',
        start_date: '',
        end_date: '',
        enable: false,
        week_times: '',
        month_times: '',
        day_pick_times: '',
        week: '',
        month: '',
        day_pick: '',
        type: 'week',
        holiday_times: ''
      };
      this.editData = editDataEmpty;
      this.week_list_value = 1;
      this.dateValue = '';
      this.showPointsRate = true;
    },
    /**
     * 点击分页
     */
    handleCurrentChange (currentPage) {
      this.tableData = this.tableAllData.slice(this.limit * (currentPage - 1), this.limit * currentPage);
    },
    tableHeaderColor ({ column }) {
      if (column.property === 'name') {
        return 'background: #F9FBFB; padding-left: 15px;';
      }
      return 'background: #F9FBFB;';
    },
    tableRowStyle ({ column }) {
      if (column.property === 'name') {
        return 'padding-left: 18px;';
      }
    },
    changeCb (str) {
      switch (str) {
        case 'consume_score_flg':
        case 'credit_score_flg':
        case 'register_score_flg':
          if (!this.point_setting.score_get.enable) {
            return;
          }
          this.point_setting.score_get[str] = !this.point_setting.score_get[str];
          break;
        case 'days_flg':
          if (!this.point_setting.score_vip_birthday.enable) {
            return;
          }
          this.point_setting.score_vip_birthday.days_flg = !this.point_setting
            .score_vip_birthday.days_flg;
          break;
      }
    },
    setMemberGoods (n) {
      this.SET_SHOW({ showMemberGoods: true });
      this.SET_SHOW({ pcSmgFrom: n });
    },
    deleteDateCheck () {
      if (this.dateValue !== '' || this.dateValue !== null) {
        this.checkFlags.dateValue = false;
      }
    },
    checkInput3 (v, min, max, n, moren) {
      let keyS = v.split('.');
      let that = this[keyS[0]][keyS[1]];
      let flg = false;
      if (isNaN(that) || !that) {
        that = !moren ? min : moren;
        flg = true;
      } else if (Number(that) < min) {
        that = min;
        flg = true;
      } else if (Number(that) > max) {
        that = max;
        flg = true;
      } else if (+Number(that).toFixed(n) !== that) {
        that = +Number(that).toFixed(n);
        flg = true;
      } else {
        // todo
      }
      if (flg) {
        this[keyS[1]] = false;
        this.checkFlags[keyS[1]] = false;
        if (keyS[0] !== 'pointInfo') {
          document.getElementById(keyS[1]).removeAttribute('style');
        }
        this[keyS[0]][keyS[1]] = that;
      }
    },
    checkInput2 (v, min, max) {
      let that = this[v];
      if (isNaN(that) == true || Number(that) < min) {
        this[v] = '';
      } else if (Number(that) > max) {
        this[v] = max;
      } else {
        this[v] = +(+that).toFixed(2) + '';
      }
    },
    checkInput (v, t, min, max, n, moren) {
      let keyS = v.split('.');
      let that = null;
      switch (keyS.length) {
        case 1:
          that = this[keyS[0]];
          break;
        case 2:
          that = this[keyS[0]][keyS[1]];
          break;
        case 3:
          that = this[keyS[0]][keyS[1]][keyS[2]];
          break;
        case 4:
          that = this[keyS[0]][keyS[1]][keyS[2]][keyS[3]];
          break;
      }
      if (t === 'number') {
        this.firstCheckNumber(t, that, min, max, moren, n, keyS);
      } else {
        this.firstCheckDecimal(t, that, min, max, moren, n, keyS);
      }
    },
    firstCheckNumber(t, that, min, max, moren, n, keyS) {
      let _that = that;
      let flg = false;
      if (isNaN(that) || !that) {
        _that = !moren ? min : moren;
        flg = true;
      } else if (Number(that) < min) {
        _that = min;
        flg = true;
      } else if (Number(that) > max) {
        _that = max;
        flg = true;
      } else if (+Number(that).toFixed(0) !== that) {
        _that = +Number(that).toFixed(0);
        flg = true;
      } else {
        // todo
      }
      this.secondCheck(flg, keyS, _that);
    },
    firstCheckDecimal(t, that, min, max, moren, n, keyS) {
      let _that = that;
      let flg = false;
      if (isNaN(that) || !that) {
        _that = !moren ? min : moren;
        flg = true;
      } else if (Number(that) < min) {
        _that = min;
        flg = true;
      } else if (Number(that) > max) {
        _that = max;
        flg = true;
      } else if (+Number(that).toFixed(n) !== that) {
        _that = +Number(that).toFixed(n);
        flg = true;
      } else {
        // todo
      }
      this.secondCheck(flg, keyS, _that);
    },
    secondCheck(flg, keyS, that) {
      if (flg) {
        switch (keyS.length) {
          case 1:
            this[keyS[0]] = that;
            break;
          case 2:
            this[keyS[0]][keyS[1]] = that;
            break;
          case 3:
            this[keyS[0]][keyS[1]][keyS[2]] = that;
            break;
          case 4:
            this[keyS[0]][keyS[1]][keyS[2]][keyS[3]] = that;
            break;
        }
      }
    },
    pointSettingSave (flag) {
      this.save_btn_disabled = false;
      if (this.point_setting.score_get.consume_score_flg || this.point_setting.score_get.credit_score_flg || this.point_setting.score_get.register_score_flg) {
        this.point_setting.score_get.enable = true;
      }
      demo.$http
        .post(
          this.$rest.pc_settingVip,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            createUser: this.loginInfo.uid,
            vipdaySetting: JSON.stringify(this.vipday_setting),
            pointSetting: JSON.stringify(this.point_setting),
            notifySetting: null
          },
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: this.token
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            if (flag === 0) {
              demo.msg('success', '保存成功');
              this.closeDialog();
            }
            if (flag === 2) {
              demo.msg('success', '保存成功');
              this.tableDataFormat(this.point_setting);
              this.closeDialog();
            }
          } else {
            demo.msg('warning', res.data.msg);
          }
          setTimeout(() => {
            this.save_btn_disabled = true;
          }, 1500);
        }).catch(error => {
          console.log(error);
          setTimeout(() => {
            this.save_btn_disabled = true;
          }, 1500);
        });
    },
    ckSearch () {
      demo.$http
        .post(
          this.$rest.pc_getTimesCard,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            name: this.ck_keyword,
            isShowOverdue: true
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            this.tableAllData = this.tableData = res.data.data.map(e => {
              if (!e.restTimes) {
                e.restTimes = '-';
              }
              return e;
            });
            this.total = this.tableAllData.length;
            this.pagenum = 1;
            this.handleCurrentChange(1); // 获取第一页数据
          } else {
            demo.msg('warning', res.data.msg);
          }
        }).catch(error => {
          console.log(error);
        });
    },
    ckAdd () {
      this.ck_name = '';
      this.ck_count = '';
      this.ck_price = '';
      this.ck_type = 'limitless';
      this.ck_period_range = '';
      this.limitFlg = true;
      this.ck_day_range = '';
      this.show_ckcard = true;
      this.save_ckSaveBtn_disabled = true;
      demo.actionLog(logList.clickAddTimesCard);
    },
    ckSave () {
      let rules = {};
      let range = null;
      if (!this.ckSaveFirst()) {
        return;
      }
      range = this.ckSaveSecond() === null;
      if (this.ck_type !== 'limitless' && (range = this.ckSaveSecond()) === null) {
        return;
      }
      rules = {
        type: this.ck_type,
        count_limit: this.limitFlg ? {
          enable: this.limitFlg,
          count: this.ck_count
        } : {
          enable: this.limitFlg
        },
        range: range
      };
      this.save_ckSaveBtn_disabled = false;
      demo.$http
        .post(
          this.$rest.pc_addTimesCard,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            name: this.ck_name,
            price: this.ck_price,
            rules: JSON.stringify(rules),
            createUser: this.sysUid
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            demo.msg('success', '保存成功');
            this.ckSearch();
            this.show_ckcard = false;
          } else {
            demo.msg('warning', res.data.msg);
          }
          setTimeout(() => {
            this.save_ckSaveBtn_disabled = true;
          }, 1500);
        }).catch(error => {
          console.log(error);
          setTimeout(() => {
            this.save_ckSaveBtn_disabled = true;
          }, 1500);
        });
    },
    ckSaveFirst() {
      if (!this.ck_name) {
        demo.msg('warning', '必须输入次卡名称');
        return false;
      }
      if (!this.ck_price) {
        demo.msg('warning', '必须输入销售价格');
        return false;
      }
      if (this.limitFlg && !this.ck_count) {
        demo.msg('warning', '必须输入可使用次数');
        return false;
      }
      return true;
    },
    ckSaveSecond() {
      let range = null;
      if (this.ck_type) {
        if (this.ck_type === 'days') {
          range = this.ckSaveSecondOne();
        }
        if (this.ck_type === 'period') {
          range = this.ckSaveSecondTwo();
        }
      } else {
        demo.msg('warning', '必须选择有效时间');
      }
      return range;
    },
    ckSaveSecondOne() {
      let range = null;
      if (this.ck_day_range) {
        range = [this.ck_day_range];
      } else {
        demo.msg('warning', '必须输入有效天数');
      }
      return range;
    },
    ckSaveSecondTwo() {
      let range = null;
      if (this.ck_period_range) {
        range = this.ck_period_range;
      } else {
        demo.msg('warning', '必须选择使用日期');
      }
      return range;
    },
    ckDelete (flg) {
      demo.$http
        .post(
          this.$rest.pc_delTimesCard,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName,
            ids: [this.ck_del_id],
            delFlg: flg,
            createUser: this.sysUid
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          if (res.data.code === '0') {
            if (res.data.data) {
              this.show_ck_delete = false;
              this.show_ck_confirm_delete = true;
            } else {
              demo.msg('success', '删除成功');
              this.show_ck_confirm_delete = false;
              this.show_ck_delete = false;
              this.ckSearch();
            }
          } else {
            demo.msg('warning', res.data.msg);
          }
        }).catch(error => {
          console.log(error);
        });
    },
    closeDialog() {
      this.showExchange = false;
      this.showConsumeScore = false;
      this.showCreditScore = false;
      this.showRegisterScore = false;
      // this.showClearScore = false;
      this.showPointsRate = false;
      this.showBirthday = false;
      this.showDelete = false;
    },
    edit(key) {
      const that = this;
      switch (key) {
        case 'deduction':
          that.pointInfo.point = that.point_setting.score_deduction.consume_score;
          that.pointInfo.money = that.point_setting.score_deduction.deduction_money;
          that.showExchange = true;
          break;
        case 'consume':
          that.pointInfo.point = that.point_setting.score_get.consume_get_score;
          that.pointInfo.money = that.point_setting.score_get.consume_money;
          that.showConsumeScore = true;
          break;
        case 'credit':
          that.pointInfo.point = that.point_setting.score_get.credit_get_score;
          that.pointInfo.money = that.point_setting.score_get.credit_money;
          that.showCreditScore = true;
          break;
        case 'register':
          that.pointInfo.point = that.point_setting.score_get.register_get_score;
          that.showRegisterScore = true;
          break;
        // case 'clear':
        //   that.pointInfo.date = that.point_setting.score_clear.execute_date;
        //   that.showClearScore = true;
        //   break;
        case 'birthday':
          that.pointInfo.money = that.point_setting.score_vip_birthday.times;
          that.pointInfo.point = that.point_setting.score_vip_birthday.days;
          that.showBirthday = true;
          break;
      }
    },
    innerSave(key) {
      const that = this;
      switch (key) {
        case 'deduction':
          that.point_setting.score_deduction.consume_score = that.pointInfo.point;
          that.point_setting.score_deduction.deduction_money = that.pointInfo.money;
          break;
        case 'consume':
          that.point_setting.score_get.consume_get_score = that.pointInfo.point;
          that.point_setting.score_get.consume_money = that.pointInfo.money;
          break;
        case 'credit':
          that.point_setting.score_get.credit_get_score = that.pointInfo.point;
          that.point_setting.score_get.credit_money = that.pointInfo.money;
          break;
        case 'register':
          that.point_setting.score_get.register_get_score = that.pointInfo.point;
          break;
        // case 'clear':
        //   that.point_setting.score_clear.execute_date = that.pointInfo.date;
        //   break;
        case 'birthday':
          that.point_setting.score_vip_birthday.times = that.pointInfo.money;
          that.point_setting.score_vip_birthday.days = that.pointInfo.point;
          break;
      }
      this.pointSettingSave(0);
    },
    tableDataFormat() {
      let that = this;
      that.pointsTableData = [];
      that.point_setting.multiple_score.forEach(function (val, index) {
        that.pointsTableData.push({
          holiday_times: val.score_holiday_setting.times,
          start_date: val.score_holiday_setting.start_date,
          end_date: val.score_holiday_setting.end_date,
          holiday_enable: val.score_holiday_setting.enable,
          week_times: val.score_times_setting.week_times,
          month_times: val.score_times_setting.month_times,
          day_pick_times: val.score_times_setting.day_pick_times,
          week: val.score_times_setting.week,
          month: val.score_times_setting.month,
          day_pick: val.score_times_setting.day_pick,
          times_enable: val.score_times_setting.enable,
          type: val.score_times_setting.type
        });
        if (val.score_holiday_setting.enable) {
          that.pointsTableData[index].name = '特殊节日多倍积分';
          that.pointsTableData[index].times = val.score_holiday_setting.times;
          that.pointsTableData[index].date = val.score_holiday_setting.start_date + ' 至 ' + val.score_holiday_setting.end_date;
          if (val.score_holiday_setting.start_date === '' && val.score_holiday_setting.end_date === '') {
            that.pointsTableData[index].date = '-';
          }
        } else {
          that.pointsTableData[index].name = '指定周期多倍积分';
          that.pointsTableData[index].times = that.scoreTimesDataFormat(val).split('@')[1];
          that.pointsTableData[index].date = that.scoreTimesDataFormat(val).split('@')[0];
        }
      });
      if (that.pointsTableData.length > 2 && that.pointsTableData.length < 11) {
        that.tableHeight = {
          height: (51 * that.pointsTableData.length + 50) + 'px'
        };
      }
    },
    scoreTimesDataFormat(val) {
      let data = '';
      switch (val.score_times_setting.type) {
        case 'week':
          data = '每周' + this.weekFormat(val.score_times_setting.week) + '@' + val.score_times_setting.week_times;
          break;
        case 'month':
          data = '每月' + val.score_times_setting.month + '号' + '@' + val.score_times_setting.month_times;
          break;
        case 'day':
          data = '每月逢' + val.score_times_setting.day_pick + '@' + val.score_times_setting.day_pick_times;
          break;
        default :
          break;
      }
      return data;
    },
    weekFormat(val) {
      let chnNumChar = ['日', '一', '二', '三', '四', '五', '六'];
      let data = '';
      chnNumChar.forEach(function (value, index) {
        if (val === index) {
          data = value;
        }
      });
      return data;
    },
    handleEdit(index, row) {
      this.editData = _.cloneDeep(row);
      this.editIndex = index;
      if (this.editData.start_date === '' || this.editData.end_date === '') {
        this.dateValue = '';
      } else {
        this.dateValue = [
          this.editData.start_date,
          this.editData.end_date
        ];
      }
      if (this.editData.name === '指定周期多倍积分') {
        this.editData.times = this.editData.holiday_times;
      }
      this.week_list_value = this.editData.week;
      this.editData.name === '指定周期多倍积分' ? this.pointsRateIndex = 1 : this.pointsRateIndex = 0;
      this.addFlag = false;
      this.showPointsRate = true;
    },
    innerDataSave() {
      if (!this.checkEmpty()) {
        return;
      }
      let that = this;
      if (that.addFlag) {
        that.point_setting.multiple_score.push({
          score_holiday_setting: {
            start_date: that.dateValue === '' || that.dateValue === null ? '' : that.dateValue[0].format('yyyy-MM-dd'),
            end_date: that.dateValue === '' || that.dateValue === null ? '' : that.dateValue[1].format('yyyy-MM-dd'),
            enable: that.pointsRateIndex === 0,
            times: that.editData.holiday_times
          },
          score_times_setting: {
            week_times: that.editData.week_times,
            month_times: that.editData.month_times,
            day_pick_times: that.editData.day_pick_times,
            week: that.week_list_value,
            month: that.editData.month,
            day_pick: that.editData.day_pick,
            enable: that.pointsRateIndex === 1,
            type: that.editData.type
          }
        });
      } else {
        that.point_setting.multiple_score[this.editIndex] = {
          score_holiday_setting: {
            start_date: that.dateValue === '' || that.dateValue === null ? '' : that.dateValue[0].format('yyyy-MM-dd'),
            end_date: that.dateValue === '' || that.dateValue === null ? '' : that.dateValue[1].format('yyyy-MM-dd'),
            enable: that.pointsRateIndex === 0,
            times: that.editData.holiday_times
          },
          score_times_setting: {
            week_times: that.editData.week_times,
            month_times: that.editData.month_times,
            day_pick_times: that.editData.day_pick_times,
            week: that.week_list_value,
            month: that.editData.month,
            day_pick: that.editData.day_pick,
            enable: that.pointsRateIndex === 1,
            type: that.editData.type
          }
        };
      }
      that.pointSettingSave(2);
    },
    deleteConfirm(index) {
      this.showDelete = true;
      this.editIndex = index;
    },
    deleteSure() {
      this.point_setting.multiple_score.splice(this.editIndex, 1);
      this.pointsTableData.splice(this.editIndex, 1);
      if (this.pointsTableData.slice((this.curren_page - 1) * this.limits, this.curren_page * this.limits).length === 0 && this.curren_page > 1) {
        this.curren_page--;
      }
      if (this.pointsTableData.length > 2 && this.pointsTableData.length < 11) {
        this.tableHeight = {
          height: (51 * this.pointsTableData.length + 50) + 'px'
        };
      } else if (this.pointsTableData.length <= 2) {
        this.tableHeight = {
          height: '152px'
        };
      } else {
        // todo nothing
      }
      demo.msg('success', '删除成功');
      this.showDelete = false;
    },
    checkEmpty() {
      let flag = true;
      if (this.pointsRateIndex === 0 && this.editData.holiday_times === '') {
        this.checkFlags.holiday_times = true;
        this.setRed('holiday_times');
        flag = false;
      }
      if (this.pointsRateIndex === 0 && (this.dateValue === '' || this.dateValue === null)) {
        this.checkFlags.dateValue = true;
        flag = false;
      }
      if (this.pointsRateIndex === 1) {
        switch (this.editData.type) {
          case 'week':
            if (this.editData.week_times === '') {
              this.setRed('week_times');
              flag = false;
            }
            break;
          case 'month':
            flag = this.setMonthRed();
            break;
          case 'day':
            flag = this.setDayRed();
            break;
        }
      }
      return flag;
    },
    setMonthRed() {
      let flag = true;
      if (this.editData.month === '') {
        this.setRed('month');
        flag = false;
      }
      if (this.editData.month_times === '') {
        this.setRed('month_times');
        flag = false;
      }
      return flag;
    },
    setDayRed() {
      let flag = true;
      if (this.editData.day_pick === '') {
        this.setRed('day_pick');
        flag = false;
      }
      if (this.editData.day_pick_times === '') {
        this.setRed('day_pick_times');
        flag = false;
      }
      return flag;
    },
    setRed(id) {
      this.checkFlags[id] = true;
      document.getElementById(id).setAttribute('style', 'border-color: red');
    },
    removeRed() {
      setTimeout(function () {
        let tag = document.getElementById('second').getElementsByTagName('input');
        for (let i = 0; i < tag.length; i++) {
          tag[i].removeAttribute('style');
        }
      }, 0);
      for (let item in this.checkFlags) {
        this.checkFlags[item] = false;
      }
    },
    cancelClose() {
      this.removeRed();
      this.showPointsRate = false;
    },
    setScoreDeduction(status, value) {
      this.point_setting.score_deduction.enable = status;
      this.point_setting.score_deduction.deduction_flg = value;
    },
    /**
     * 积分设置点击页码
     */
    handleCurrentChangeOne (currenPage) {
      this.curren_page = currenPage;
    }
    // 积分清空旧版设置
    // isOldScoreClear() {
    //   if (this.point_setting.score_clear.days) {
    //     let new_score_clear = {
    //       enable: false,
    //       execute_date: new Date('06-01').format('MM-dd'),
    //       clear_date: new Date('12-31').format('MM-dd'),
    //       notice_before_days: 5
    //     };
    //     this.point_setting.score_clear = new_score_clear;
    //   }
    // }
  },
  computed: mapState({
    token: (state) => state.show.token,
    sysUid: (state) => state.show.sys_uid,
    loginInfo: (state) => state.show.loginInfo,
    ultimate: (state) => state.show.ultimate,
    sysSid: (state) => state.show.sys_sid,
    memberIndex: (state) => state.show.memberIndex
  }),
  beforeDestroy() {
    this.pointSettingSave(1);
  },
  watch: {
    pointsRateIndex() {
      this.removeRed();
    },
    'editData.type'() {
      this.removeRed();
    },
    memberIndex() {
      if (this.memberIndex === 'four') {
        this.isShowSMSContract = true;
        this.getMessageCount();
      }
    }
  }
};
</script>
