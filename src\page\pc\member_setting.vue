<style lang='less' scoped>
.pc_set .el-tabs__header {
  display: none;
}
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
.el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
.el-tabs__content {
  height: 100%;
}
.pc_set {
  width: 100%;
  height: 100%;
  background: #f5f8fb;
  color: @themeFontColor;
  padding: 12px !important;
  position: fixed;
  z-index: 200;
  top: 50px;
}
.pc_set .el-input__inner {
  background-color: #f5f8fb;
}
.pc_set1 {
  float: left;
  width: 180px;
  height: 100%;
  border-right: 1px solid #e7eaef;
  font-weight: bold;
}
.pc_set11 {
  height: 20px;
  width: 100%;
}
.pc_set12 {
  width: 100%;
  height: 57px;
  cursor: pointer;
}
.pc_member_set14 {
  line-height: 57px;
  font-size: 16px;
  float: left;
  margin-left: 40px;
}
.pc_set18 {
  float: left;
  width: calc(100% - 180px);
  padding-top: 40px;
  padding-left: 40px;
  padding-right: 10px;
  background: #fff;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}
.pc_set_video {
  background: @themeButtonBackGroundColor;
  border-radius: 10px 0px 0px 10px;
  width: 140px;
  color: #CFA26B;
  font-weight: 500;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  right: 0px;
  position: absolute;
  z-index: 100;
  margin-top: 66px;
}
.pc_set_cat {
  background: #FF0036;
  border-radius: 10px 0px 0px 10px;
  width: 140px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  right: 0px;
  position: absolute;
  z-index: 100;
  margin-top: 298px;
}
.pc_set_cat_img {
  width: 140px;
  text-align: center;
  right: 0px;
  position: absolute;
  z-index: 100;
  margin-top: 265px;
}
.pc_set34 {
  color: #fff;
  background: @themeBackGroundColor;
}
#set_hy {
  font-size: 16px;
}
.pc_set43 {
  height: 100%;
}
.pc_set43 .el-select {
  width: 100px;
}
.print_remark_content{
  display: flex;
}
.img_remark{
  width: 301px;
  height: 446px;
}
.input_remark{
  width:100%;
  max-width: 340px;
  height:124px;
  background:rgba(245,248,251,1);
  border:1px solid rgba(227,230,235,1);
  border-radius:5px;
  font-size: 15px;
  resize: none;
  padding: 9px;
}
.print_font_setting{
  display: flex;
  align-items: center;
  margin-top: 30px;
}
.font_text{
  width: 98px;
  height: 24px;
  font-size: 16px;
  font-weight: 400;
  color: #557485;
  line-height: 24px;
}
.notice_title{
  width: 173px;
  height: 17px;
  font-size: 16px;
  font-weight: 400;
  color: @themeFontColor;
  line-height: 17px;
}
.notice_remark{
  flex:1;
  margin-left:62px;
  font-size: 15px;
  font-weight: 400;
  color: #B1C3CD;
}
</style>
<template>
  <div class="pc_set">
    <v-SettingMG></v-SettingMG>
    <div style="width: 100%;background: #FFF;height: calc(100% - 49px);border: 1px solid #e3e6eb;border-radius: 5px;overflow: hidden;">
      <!--左侧部分-->
      <div class="pc_set1">
        <!--顶部白色空白-->
        <div class="pc_set11"></div>
        <!--配置部分-->
        <div
          class="pc_set12"
          @click="goPageTag(1, 'first')"
          :class="selectRow === 1 ? 'pc_set34' : ''"
        >
          <div class="pc_member_set14">积分设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(2, 'second')"
          :class="selectRow === 2 ? 'pc_set34' : ''"
        >
          <div class="pc_member_set14">会员日设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(3, 'third')"
          :class="selectRow === 3 ? 'pc_set34' : ''"
        >
          <div class="pc_member_set14">次卡设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(11, 'four')"
          :class="selectRow === 11 ? 'pc_set34' : ''"
        >
          <div class="pc_member_set14">短信消息设置</div>
        </div>
        <div
          v-show="this.ultimate"
          class="pc_set12"
          @click="goPageTag(10, 'five')"
          :class="selectRow === 10 ? 'pc_set34' : ''"
        >
          <div class="pc_member_set14">微信消息设置</div>
        </div>
      </div>
      <!--右侧部分-->
      <div class="pc_set18">
        <div class="pc_set_video" v-show="showVideo">
          <div style="margin-left: 5px;display: inline-block;margin-right: 5px;vertical-align: middle;margin-top: 10px;">
            <div style="cursor: pointer;text-align: right;font-size: 24px;margin-top: -15px;line-height: 30px;" @click="closeVideo">×</div>
            <div>
              <img style="height: 100px;width: 100px;" alt="" src="../../image/pc_video.png"/>
            </div>
            扫一扫查看视频教程
          </div>
        </div>
        <!-- <div class="pc_set_cat_img" v-show="showCatImg">
          <img style="height: 33px; width: 144px;" alt="" src="../../image/cat.png" />
        </div>
        <div class="pc_set_cat" v-show="showCatImg">
          <div style="margin-left: 5px;display: inline-block;margin-right: 5px;vertical-align: middle;margin-top: 10px;">
            <div style="cursor: pointer;text-align: right;font-size: 24px;margin-top: -15px;line-height: 30px;width: 127px" @click="showCatImg = false">×</div>
            <div>
              <img style="height: 100px;width: 100px;" alt="" src="../../image/peijian.png"/>
            </div>
            扫一扫购买配件
          </div>
        </div> -->
        <v-SettingVip
          class="pc_set43"
        ></v-SettingVip>
      </div>
    </div>
  </div>
</template>
<script>

// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';

export default {
  data () {
    return {
      showVideo: true,
      showCatImg: false,
      select_row: 1
    };
  },
  mounted() {
    this.showCatImg = this.isAuto;
  },
  methods: {
    ...mapActions([SET_SHOW]),
    closeVideo() {
      this.showVideo = false;
    },
    goPageTag(index, tap) {
      if (index === 2) {
        demo.actionLog(logList.clickVipDaySetting);
      }
      if (index === 11) {
        demo.actionLog(logList.clickSmsMessageSetting);
      }
      if (index === 10) {
        demo.actionLog(logList.clickWechatMessageSetting);
      }
      this.SET_SHOW({ memberIndex: tap });
      this.SET_SHOW({ selectRow: index });
    }
  },
  computed: mapState({
    selectRow: state => state.show.selectRow,
    ultimate: state => state.show.ultimate,
    sysUid: state => state.show.sys_uid,
    isAuto: state => state.show.isAuto
  })
};
</script>
