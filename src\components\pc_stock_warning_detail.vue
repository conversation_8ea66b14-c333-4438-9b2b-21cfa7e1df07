<template>
  <!-- 库存预警 -->
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="re_deposit_record_container">
      <div>
        <div class="re_top">
          <div class="re_top_left_container">
            <div style='float: left;'>
              <div
                class='pc_report2'
                :style="inputing_keyword ? 'border-color: #BDA16A' : ''"
              >
                <input
                  @focus='inputing_keyword = true'
                  @blur='inputing_keyword = false'
                  type='text'
                  placeholder='商品名称/条码/首字母/扫码'
                  v-model='keywordGoods'
                  id='goods_keyword_report'
                  @compositionstart='pinyin = true'
                  @compositionend='pinyin = false'
                  @input="keywordGoods = $goodsNameFormat(keywordGoods)"
                  @keydown.enter="inputSelectHandler('goods_keyword_report')"
                />
                <img
                  class='pc_report1'
                  v-show="keywordGoods != ''"
                  @click="focusInput('goods_keyword_report')"
                  src='../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div class="re_top_left">
              <vCjSelect @searchChange="searchChange"></vCjSelect>
            </div>
            <div class="re_top_left">
              <el-cascader
                v-model="detailValue"
                :options="category_list"
                :props="{ checkStrictly: true }"
                placeholder="分类选择">
              </el-cascader>
            </div>
            <div class="re_top_left">
              <el-select
                v-model="statusVal"
                placeholder="全部状态"
                style="width:130px;font-size: 16px;"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="re_top_right">
            <button
              class="pc_btn1"
              @click="exportExcel">导出表格</button>
          </div>
        </div>
        <div class="re_table_container">
          <el-table
            ref="multipleTable"
            :height="goods_height"
            stripe
            :empty-text="!loading ? '暂无数据' : ' '"
            :data="goods_data"
            style="font-size: 16px;margin-top: 5px;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
            id="stockRecordTab">
            <el-table-column
              prop="name"
              min-width="22%"
              :show-overflow-tooltip="true"
              label="商品名称">
            </el-table-column>
            <el-table-column
              min-width="18%"
              align="center"
              label="条码">
              <template slot-scope="scope">{{ scope.row.code ? scope.row.code : '-'}}</template>
            </el-table-column>
            <el-table-column
              prop="typeName"
              min-width="12%"
              align="center"
              label="商品分类">
            </el-table-column>
            <el-table-column
              label="供应商"
              :show-overflow-tooltip="true"
              min-width="7%"
              align="center">
              <template slot-scope="scope">
                <div class="supplier-curson" @click="openSupplier(scope.row.supplierName, scope.row)">
                   <span :class="scope.row.supplierName ? 'supplier-title' : ''">{{ scope.row.supplierName ? scope.row.supplierName : '-'}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!(employeeAuth.indexOf('purchase_price') !== -1)"
              min-width="12%"
              align="center"
              label="进价">
              <template slot-scope="scope">{{ $toDecimalFormat(scope.row.purPrice, 6) }}</template>
            </el-table-column>
            <el-table-column
              min-width="12%"
              align="center"
              prop="cur_stock"
              label="库存"
              sortable
              :sort-method="(a, b) => {return a.curStock - b.curStock}">
              <template slot-scope="scope">{{ $toDecimalFormat(scope.row.curStock, 3) }}</template>
            </el-table-column>
            <el-table-column
              min-width="14%"
              align="center"
              label="库存状态">
              <template slot-scope="scope">
                <div style="width: 75px;margin: 0 auto;text-align: center;overflow: hidden;clear: both;">
                  <span style="font-size: 70px;float: left;margin-left: 10px;width: 30px;"
                    :style="'color:' + (scope.row.status === '过剩'
                      ? '#FF6159'
                      : (scope.row.status === '不足' ? '#F9AE1B' : '#67C23A')
                      )">·</span>
                  <span style="float: left;">{{ scope.row.status }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="maxStock"
              min-width="16%"
              align="center"
              label="库存上限/下限">
              <template slot-scope="scope">
                {{scope.row.maxStock + '/' + scope.row.minStock}}
              </template>
            </el-table-column>
            <el-table-column
              min-width="10%"
              align="center"
              label="单位">
              <template slot-scope="scope">{{ scope.row.unitName ? scope.row.unitName : '-'}}</template>
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              记录条数：<span>{{towIntNumber(goods_total)}}</span>
            </div>
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="goods_total"
                @current-change="goodsChange"
                :current-page="goods_pagenum"
                :page-size="goods_pageSize"
                :page-count="goods_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="handleSizeChange"
                  :pageSize.sync="goods_pageSize"
                  :currentPage.sync="goods_pagenum"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <supplier-information :visible.sync="visible" :detailTitle="detailTitle"></supplier-information>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import vCjSelect from '@/common/components/CjSelect';
import SupplierInformation from '@/common/components/SupplierInformation';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  data() {
    return {
      loading: false,
      pinyin: false,
      detailTitle: {}, // 供应商详情
      supplierObject: {
        all: false,
        notSupplier: false,
        supplierList: []
      }, // 供应商传参
      inputing_keyword: false,
      category_list: [],
      detailValue: [''],
      keywordGoods: '',
      goodssort: {},
      goods_total: 0,
      visible: false, // 编辑供应商弹窗
      pageKey: 0,
      goods_pagenum: 1,
      goods_height: $(window).height() - 240,
      goods_pageSize: 10,
      goods_data: [],
      multipleSelection: [],
      dateGoodStocks: '',
      loadingInstance: null,
      export_data: [],
      statusVal: 1,
      statusList: [{id: 3, name: '全部'}, {id: 1, name: '库存不足'}, {id: 2, name: '库存过剩'}]
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading,
    vCjSelect,
    SupplierInformation,
    vCjPageSize
  },
  created() {
    this.getAllCategory();
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    const status = localStorage.getItem('stockStatusChange');
    this.statusVal = +status || 1;
    this.searchEarlyWarningMain();
  },
  mounted() {
    this.listenResize();
    $('#goods_keyword_report').focus();
  },
  watch: {
    keywordGoods() {
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        this.searchEarlyWarningMain();
      }, this.delayedTime);
    },
    dateGoodStocks() {
      if (this.dateGoodStocks) {
        this.searchEarlyWarningMain();
      }
    },
    detailValue() {
      this.searchEarlyWarningMain();
    },
    statusVal() {
      this.searchEarlyWarningMain();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 供应商回调
    searchChange(e) {
      console.log(e, 'eeeeeeeeeeeeeeeeeeeeeee123');
      this.supplierObject = _.cloneDeep(e);
      this.searchEarlyWarningMain();
    },
    // openSupplier供应商选择
    openSupplier(e, value) {
      console.log(e, '111111111111111111-------我是大');
      if (e) {
        console.log('111111111111111111-------我是大111');
        this.detailTitle = _.cloneDeep(value);
        this.visible = true;
      }
    },
    focusInput(sid) {
      this.keywordGoods = '';
      $('#' + sid).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    headerStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    showLoading() {
      this.loadingInstance = Loading.service({
        lock: true
      });
    },
    hideLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    sortChange(e) {
      this.goods_pagenum = 1;
      this.goodssort = _.cloneDeep(e);
      this.goodsStock('search');
    },
    goodsChange(val) {
      this.goods_pagenum = val;
      this.goodsStock('search');
    },
    handleSizeChange() {
      this.goodsStock('search');
    },
    /**
     * 商品库存查询
     */
    searchEarlyWarningMain() {
      if (this.pinyin) {
        return;
      }
      this.goods_pagenum = 1;
      this.goodsStock('search');
    },
    /**
     * 商品库存查询
     */
    goodsStock(str) {
      this.loading = true;
      var params = {
        type: str, // search、excel
        pageSize: this.goods_pageSize,
        page: this.goods_pagenum,
        search: this.keywordGoods.replace(/'/g, "'").replace(/;/g, '；'),
        typeId: this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        status: this.statusVal, // 1:库存不足；2:库存过剩
        orderBy: this.goodssort.prop || null,
        sort: this.goodssort.order || null,
        all: this.supplierObject.all,
        notSupplier: this.supplierObject.notSupplier,
        supplierList: this.supplierObject.supplierList
      };
      console.log(params, 'goodsStock params+');
      // this.reportFormLog(_.cloneDeep(params), '库存预警查询');
      goodService.goodsStockWarningReports(_.cloneDeep(params), res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        console.log('调用一次');
        this.goods_data = _.cloneDeep(res.data);
        console.log(res.data, 'res.data');
        this.goods_total = Number(res.count);
        // this.$nextTick(() => {
        //   this.$refs.multipleTable.doLayout();
        // });
      }).catch(() => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      });
      // let sub_data = {
      //   'limit': this.goods_pageSize,
      //   'offset': Number((this.goods_pagenum - 1) * this.goods_pageSize),
      //   'condition': '',
      //   'type': this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
      //   'order': this.goodssort.order
      // };
      // goodService.stockGoodsReports(sub_data, res => {
      //   this.goods_total = Number(demo.t2json(res).count);
      //   this.goods_data = demo.t2json(res).datas.map(item => {
      //     return {
      //       ...item,
      //       'salePrice': Number(item.salePrice).toFixed(2),
      //       'purPrice': Number(item.purPrice).toFixed(2),
      //       'curStock': this.setMaxDecimal(item.curStock, 3)
      //     };
      //   });
      // });
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : '0.00';
    },
    towIntNumber(val) {
      return val ? Number(val) : 0;
    },
    // 获取所有类别
    getAllCategory() {
      this.category_list = [{label: '全部分类', value: ''}, {label: '称重分类', value: '0'}];
      typeService.search(res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          json.forEach(item => {
            let obj = {
              label: item.name,
              value: item.fingerprint
            }
            if (item.list.length !== 0) {
              let arr = [];
              item.list.forEach(subItem => {
                let subObj = {
                  label: subItem.name,
                  value: subItem.fingerprint
                }
                arr.push(subObj);
              });
              obj['children'] = arr;
            }
            this.category_list = this.category_list.concat(obj);
          });
        }
      });
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_overdue_detail', action: 'reportFormLog', description});
      }
    },
    exportExcel() {
      var that = this;
      let sub_data = {
        'search': this.keywordGoods.replace(/'/g, "'").replace(/;/g, '；'),
        'typeId': this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        'orderBy': this.goodssort.prop, // 库存：cur_stock
        'status': this.statusVal, // 0:全部状态；1:库存不足；2:库存过剩
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      this.reportFormLog(_.cloneDeep(sub_data), '库存预警导出表格');
      goodService.goodsStockWarningReports(sub_data, res => {
        this.export_data = res.data.map(item => {
          return {
            ...item,
            'curStock': that.formatFloat(Number(item.curStock), 3)
          };
        });
        if (this.export_data.length === 0) {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        } else {
          var field_mapping = {};
          if (!this.$employeeAuth('purchase_price')) {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              分类: 'typeName',
              供应商: 'supplierName',
              进价: 'purPrice',
              库存: 'curStock',
              库存状态: 'status',
              库存上限: 'maxStock',
              库存下限: 'minStock',
              单位: 'unitName'
            };
          } else {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              分类: 'typeName',
              供应商: 'supplierName',
              库存: 'curStock',
              库存状态: 'status',
              库存上限: 'maxStock',
              库存下限: 'minStock',
              单位: 'unitName'
            };
          }
          console.log(this.export_data);
          this.export_data.forEach(item => {
            item.code = item.code || '';
            item.unitName = item.unitName || '';
            item.supplierName = item.supplierName || '-';
          });
          this.$makeExcel(this.export_data, field_mapping, '商品库存预警' + new Date().format('yyyyMMddhhmmss'));
          this.export_data = [];
        }
      });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      this.table_height = $(window).height() - 460;
    },
    formatFloat (f, digit) {
      if (isNaN(f)) {
        return '';
      }
      var m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    employeeAuth: state => state.show.employeeAuth,
    delayedTime: state => state.show.delayedTime
  }),
  beforeDestroy() {
    localStorage.removeItem('stockStatusChange');
  },
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
    this.hideLoading();
  }
};
</script>

<style lang='less' scoped>
.supplier-curson {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.inputing_border_color1 {
  border-color: #BDA16A;
}
.inputing_border_color2 {
  border-color: #e3e6eb;
}
.re_deposit_record_container {
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 50px;
    height: 44px;
    line-height: 44px;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeFocusBorderColor;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    height: 50px;
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeFocusBorderColor;
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-input__inner {
    border-radius: 50px;
    color: @themeFontColor;
    font-size: 16px;
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  background: @input_backgroundColor;
  .re_top{
    height: 64px;
    background: @input_backgroundColor;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
      }
    }
    .re_top_right{
      display: flex;
      align-items: center;
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: @input_backgroundColor;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      padding: 0 12px;
      background: white;
      color: @themeFontColor;
      span{
        color: @themeFocusBorderColor;
      }
    }
  }
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
}
.pc_report2 input {
  width: 235px;
  height: 25px;
  line-height: 28px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 9px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.el-table .ascending .sort-caret.ascending{
  border-bottom-color: #BDA16A;
}
.el-table .descending .sort-caret.descending{
  border-top-color: #BDA16A;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.el-input__inner:focus {
  border-color: #BDA16A;
}

.el-date-table td.end-date span {
  background-color: #BDA16A;
}

.pc_btn1 {
  cursor: pointer;font-weight: bold;
  width: 110px;height: 44px;margin-left: 50px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeFocusBorderColor;float: left;border: 0px;outline: none;
}

.pc_btn1:active {
  cursor: pointer;font-weight: bold;
  width: 110px;height: 44px;margin-left: 50px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeFocusBorderColor;float: left;border: 0px;opacity: 0.5;outline: none;
}
.supplier-title {
  color:#537286;
  border-bottom: 1px solid #537286;
  cursor: pointer;
}
</style>
