<template>
<!-- 商品 报表 供应商下拉框 -->
  <div class="content">
    <el-select
      v-model="supplier"
      placeholder="供应商筛选"
      multiple
      collapse-tags
      @change="changeSelect"
      popper-class="pc_fixed_dropdown"
      @visible-change="clearDrop($event)"
      >
      <div class="select_search_input">
        <el-input
          ref="dropDownInput"
          v-model="selSearchValue"
          size="small"
          placeholder="搜索供应商"
          prefix-icon="el-icon-search"
          @input="dropDownSearch"
          clearable></el-input>
      </div>
      <div slot="empty" class="select_search_input">
        <el-input
          ref="dropDownInput_empty"
          v-model="selSearchValue"
          size="small"
          placeholder="搜索供应商"
          prefix-icon="el-icon-search"
          @input="dropDownSearch"
          clearable></el-input>
          <div class="input-title">
            无搜索内容
          </div>
      </div>
      <el-option v-if="makerlistShow.length === makerlist.length" label="全选" value="全选">
        <el-checkbox :indeterminate="isIndeterminate" @click.prevent.native v-model="isSelectAll">全选</el-checkbox>
      </el-option>
      <el-option
        v-for="mk in makerlistShow"
        :key="mk.value"
        :label="mk.label"
        :value="mk.value">
        <span class='label-name-box'>
          <el-checkbox @click.prevent.native v-model="mk.checked">{{mk.label}}</el-checkbox>
        </span>
      </el-option>
   </el-select>
  </div>
</template>
<script>
export default {
  name: 'CjSelect',
  data() {
    return {
      supplier: [], // 供应商下拉框值
      isIndeterminate: false,
      selSearchValue: '', // 供应商选择检索框内容
      makerlistShow: [], // 供应商显示列表
      isSelectAll: false, // 下拉款全选控制
      makerlist: [] // 供应商默认列表,
    }
  },
  methods: {
    changeSelect(val) {
      if (val.includes("全选")) {
        // 说明已经全选了，所以全不选
        if (val.length > this.makerlistShow.length) {
          this.supplier = [];
        } else {
          this.supplier = this.makerlistShow.map(item => item.value);
        }
      }
      // 获取未选择属性
      const notSupplier = this.supplier.includes('isSelect');
      // 重新组合数组
      const supplierList = this.supplier.filter(item => item !== 'isSelect');
      // 选中的数组和原数组相同位true
      const all = this.supplier.length === this.makerlistShow.length && this.makerlistShow.length === this.makerlist.length;
      const supplierObject = {
        all: all,
        notSupplier: notSupplier,
        supplierList: supplierList
      }
      console.log(supplierObject, 'supplierObject');
      this.$emit("searchChange", supplierObject);
    },
    // 获取供应商列表
    getMakerListData () {
      // 获取供应商列表数据
      supplierService.getSupplierDropDownList(res => {
        this.makerlist = [
          {
            value: 'isSelect',
            label: '（未设置供应商）',
            checked: false
          }
        ];
        let makers = demo.t2json(res);
        console.log(makers, 'makerlist');
        for (let mk of makers) {
          this.makerlist.push({
            value: mk.fingerprint,
            label: mk.name,
            checked: false
          });
        }
      });
    },
    // 供应商查询
    dropDownSearch() {
      this.makerlistShow = this.makerlist.filter(this.filterSearch);
      this.$nextTick(() => {
        if (this.makerlistShow.length === 0) {
          this.$refs.dropDownInput_empty.focus();
        } else {
          this.$refs.dropDownInput.focus();
        }
      });
    },
    clearDrop(e) {
      if (e) {
        this.selSearchValue = '';
        this.makerlistShow = _.cloneDeep(this.makerlist);
      }
    },
    filterSearch(item) {
      return item.label.includes(this.selSearchValue);
    },
    /**
     * 监听刷新供应商数据
     */
    watchRefreshListData () {
      this.$event.$on('refreshSuppliers', val => {
        if (val) {
          this.getMakerListData();
        }
      })
    }
  },
  mounted() {
    // 加载供应商列表
    this.getMakerListData();
    this.watchRefreshListData();
  },
  destroyed() {
    this.$event.$off('refreshSuppliers');
  },
  watch: {
    // 监听（全选，全不选以及checkbox是否勾选）
    supplier: {
      handler(arr) {
        this.makerlistShow.forEach(item => {
          item.checked = arr.includes(item.value);
        })
        this.makerlist.forEach(e => {
          e.checked = arr.includes(e.value);
        })
        if (arr.length > 0) {
          this.isIndeterminate = true;
        } else {
          this.isIndeterminate = false;
        }
        if (arr.length === this.makerlistShow.length) {
          this.isSelectAll = true;
          this.isIndeterminate = false;
        } else {
          this.isSelectAll = false;
        }
        // 强制更新（checkbox回显)
        this.$forceUpdate();
      }
    }
  }
}
</script>
<style scoped lang="less">
  .content {
    width: 100%;
  }
  .select_search_input {
    width: 100%;
    padding: 0 20px;
    margin: 10px auto;
  }
  /deep/.el-select__tags {
    display: -webkit-box !important;
  }
  /deep/.el-checkbox {
    display: flex;
    align-items: center;
  }
  .input-title {
    text-align:center;
    margin: 20px auto;
    font-size: 14px;
    color: @homeColor;
    height: 213px;
    line-height: 213px;
  }

  .label-name-box {
    color:@themeBackGroundColor !important;
    font-weight:bold !important;
  }
  /deep/.el-select .el-select__tags .el-tag.el-tag--info {
    background-color: #F0F2F5;
    color: @homeColor;
    max-width: 120px;
    overflow: hidden;
    display: flex;
    align-items: center;
}
/deep/.el-select .el-select__tags .el-tag.el-tag--info .el-tag__close{
  background-color: #c0c4cc;
  color: #f0f2f5;
}
/deep/ .el-select__tags-text {
  max-width:80px;overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space:  normal;
  word-break:break-all;
  -webkit-line-clamp: 1;
}
</style>
