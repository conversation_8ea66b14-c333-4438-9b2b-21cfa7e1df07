<style lang='less' scoped>
/deep/.el-input__inner:hover {
  border-color: @themeBackGroundColor !important;
}
/deep/.el-input__inner {
  font-size: 16px!important;
  color: #567485!important;
  height: 32px!important;
  border-radius: 22px!important;
}
.pc_imp {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 30;
  color: @themeFontColor;
  display: flex;
  align-items: center;
  .pc_imp1 {
    padding: 24px 24px 0;
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 1280px;
    height: 690px;
    border-radius: 10px;
    overflow: hidden;
    /deep/.dialog_header{
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #E3E6EB;
      height: 44px;
      padding-bottom: 20px;
      margin-bottom: 20px;
      margin-left: 0px;
      margin-right: 0px;
      .header_title{
        color: @themeFontColor;
        font-size: 18px;
        font-weight: bold;
        line-height: 60px;
      }
      .icon_close{
        font-size: 30px;
        color: #8298A6;
        cursor: pointer;
      }
    }
    .pc_imp1_1 {
      display: inline-block;
      margin-bottom: 12px;
      width: 100%;
      /deep/ .date_picker_container{
        .el-input__icon {
          line-height: 33px;
        }
        margin-right: 20px;
        height: 44px;
        background: #FFFFFF;
        border: 1px solid #E3E6EB;
        border-radius: 22px;
        margin-left: 10px;
        display: flex;
        align-items: center;
        float: left;
        > .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date .el-input__inner {
          border: none;
          background: #FFFFFF;
          color: @themeFontColor;
          font-size: 16px;
          padding-left: 0px;
          padding-right: 0px;
          text-align: center;
        }
        .el-input__suffix {
          top: 2px;
        }
        .el-input__inner {
          border-radius: 22px;
          background: #FFFFFF;
        }
        .el-input__inner {
          text-align: center;
          border: 0px;
        }
      }
      .date_picker_margin {
        margin-left: 34px;
      }
      .pc_imp1_1_1 {
        float: left;
        color: @themeFontColor;
        font-size: 16px;
        margin-top: 11px;
        font-weight: bold;
      }
      .label_flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
        i {
          font-size: 18px;
          margin-left: 6px;
        }
      }
      .pc_imp1_button {
        width: 140px;
        height: 44px;
        background: @themeBackGroundColor;
        border-radius: 22px;
        font-weight: bold;
        text-align: center;
        color: #FFFFFF;
        font-size: 18px;
        line-height: 42px;
        float: right;
        cursor: pointer;
      }
    }
    .pc_imp1_2 {
      /deep/.cell {
        padding: 0;
      }
      /deep/.el-table th, .el-table tr {
        background: #F5F7FA;
      }
    }
    .pc_imp1_3 {
      .pc_imp1_3_1 {
        color: @themeFontColor;
        font-size: 16px;
        display: inline-block;
        margin-top: 5px;
      }
      .pc_imp1_3_2 {
        margin-top: 5px;
        color: @themeFontColor;
        float: right;
      }
    }
    .pc_imp1_4 {
      width: 314px;
      margin: 10px auto 0;
      .pc_imp1_4_1 {
        color: @themeBackGroundColor;
        line-height: 42px;
        width: 148px;
        height: 44px;
        border-radius: 22px;
        font-size: 18px;
        text-align: center;
        font-weight: bold;
        border: 1px solid @themeBackGroundColor;
        display: inline-block;
        cursor: pointer;
      }
      .pc_imp1_4_2 {
        color: #FFFFFF;
        line-height: 42px;
        width: 148px;
        height: 44px;
        border-radius: 22px;
        font-size: 18px;
        text-align: center;
        font-weight: bold;
        background: @themeBackGroundColor;
        display: inline-block;
        margin-left: 14px;
        cursor: pointer;
      }
    }
  }
}
.pc_imp_tip {
  float: right;
  right: -8px;
  min-width: 36px;
  height: 22px;
  font-size: 16px;
  color: #FFFFFF;
  position: relative;
  background: #F16A6E;
  border-radius: 22px;
  line-height: 22px;
  top: -53px;
  text-align: center;
}
.vip-status-select {
  width: 130px;
  height: 44px;
  margin-left: 10px;
  /deep/.el-select .el-input__inner {
    border-color: #c0c4cc;
    border-radius: 50px;
    font-size: 16px;
    background: white;
    height: 44px;
  }
  /deep/.el-input--suffix {
    height: 44px;
  }
  /deep/.el-input__inner {
    padding: 0 20px;
    color: @themeFontColor;
    height: 44px !important;
  }
  /deep/.el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }

  /deep/.el-select-dropdown__item.selected {
    color: @themeBackGroundColor;
    font-weight: 700;
  }
}

</style>
<template>
  <div v-if="isImportVip" >
    <div class="pc_imp">
      <div class="pc_imp1">
        <div class="dialog_header">
          <div class="header_title">会员筛选</div>
          <div
            class="icon_close"
            @click="closeImport"
          >×</div>
        </div>
        <div class="pc_imp1_1">
          <div :style="isFloat ? 'float: left;margin-bottom: 12px;' : ''">
            <div class="pc_imp1_1_1">注册日期</div>
            <div @click="focusDate = true" class="date_picker_container date_picker_margin"
              :style="'width:' + dateWidth + ';' + (focusDate ? 'border-color: #b4995a' : 'border-color: #E3E6EB')"
              >
              <el-date-picker
                v-model="date[0]"
                type="date"
                placeholder="开始日期"
                :picker-options="pickerOptions"
                value-format='yyyy-MM-dd'
                @blur="focusDate = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485;margin-right: 20px;">至</div>
              <el-date-picker
                v-model="date[1]"
                type="date"
                placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format='yyyy-MM-dd'
                @blur="focusDate = false"
              >
              </el-date-picker>
            </div>
          </div>
          <!-- 新版生日筛选 -->
          <div :style="isFloat ? 'float: left;' : ''">
            <div class="pc_imp1_1_1" style="width: 78px;text-align: right;">生日</div>
            <div @click="focusBirthday = true" class="date_picker_container"
              :style="'width:' + dateWidth + ';' + (focusBirthday ? 'border-color: #b4995a' : 'border-color: #E3E6EB')"
              >
              <el-date-picker
                class="date-range-pick"
                popper-class="date-pick-panel"
                v-model="birthdayDate[0]"
                type="date"
                placeholder="开始日期"
                value-format='MM-dd'
                format="MM-dd"
                clearable
                @blur="focusBirthday = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485;margin-right: 20px;">至</div>
              <el-date-picker
                class="date-range-pick"
                popper-class="date-pick-panel"
                v-model="birthdayDate[1]"
                type="date"
                placeholder="结束日期"
                value-format='MM-dd'
                format="MM-dd"
                clearable
                @blur="focusBirthday = false"
              >
              </el-date-picker>
            </div>
          </div>
          <div :style="isFloat ? 'float: left;' : ''">
            <div class="pc_imp1_1_1" style="width: 78px;text-align: right;">状态</div>
            <el-select class="vip-status-select" v-model="vipStatus">
              <el-option v-for="item in vipStatusList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="pc_imp1_1">
          <div :style="isFloat ? 'float: left;margin-bottom: 12px;' : ''">
            <div class="pc_imp1_1_1 label_flex">
              消费时间
              <el-tooltip
                effect="dark"
                placement="top"
                content="查询某个日期区间内有消费记录的会员名单">
                <i class="el-icon-question" style="float: left;"></i>
              </el-tooltip>
            </div>
            <div @click="focusCon = true" class="date_picker_container"
              :style="'width:' + dateWidth + ';' + (focusCon ? 'border-color: #b4995a' : 'border-color: #E3E6EB')"
              >
              <el-date-picker
                v-model="conDate[0]"
                type="date"
                placeholder="开始日期"
                :picker-options="pickerOptions"
                value-format='yyyy-MM-dd'
                @blur="focusCon = false"
              >
              </el-date-picker>
              <div style="font-size: 16px;color: #567485;margin-right: 20px;">至</div>
              <el-date-picker
                v-model="conDate[1]"
                type="date"
                placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format='yyyy-MM-dd'
                @blur="focusCon = false"
              >
              </el-date-picker>
            </div>
          </div>
          <div :style="isFloat ? 'float: left;' : ''">
            <div class="pc_imp1_1_1" style="width: 80px;text-align: right;">储值余额</div>
            <div @click="focusMoney = true" class="date_picker_container"
              :style="'width:' + dateWidth + ';' + (focusMoney ? 'border-color: #b4995a' : 'border-color: #E3E6EB')"
              >
                <el-input
                  clearable
                  v-model="money1"
                  placeholder="请输入金额"
                  @blur="focusMoney = false"
                  @input="money1 = money1.match(/^\d*(\.?\d{0,2})/g)[0] || ''"
                  maxlength="9"
                />
              <div style="font-size: 16px;color: #567485;margin-right: 20px;">至</div>
                <el-input
                  clearable
                  v-model="money2"
                  placeholder="请输入金额"
                  @blur="focusMoney = false"
                  @input="money2 = money2.match(/^\d*(\.?\d{0,2})/g)[0] || ''"
                  maxlength="9"
                />
            </div>
          </div>
          <div class="pc_imp1_button" @click="pagenum = 1;search()" :style=" isLowWidth ? 'margin-right: 20px;' : ''">查询</div>
        </div>
        <div class="pc_imp1_2">
          <el-table
            ref="multipleTable"
            empty-text="暂无数据"
            :data="tableData"
            stripe
            border
            :row-key="getRowKeys"
            tooltip-effect="dark"
            style="float: left;width: 100%;font-size: 16px;color: #567485;"
            @selection-change="handleSelectionChange"
            :height="tableHeight"
          >
            <el-table-column type="selection" min-width="7%" align="center" :reserve-selection="true"></el-table-column>
            <el-table-column label="姓名" prop="name" align="center" show-overflow-tooltip min-width="11%"></el-table-column>
            <el-table-column label="手机号" prop="mobile" align="center" show-overflow-tooltip min-width="11%"></el-table-column>
            <el-table-column label="生日" prop="birthday" align="center" min-width="10%"></el-table-column>
            <el-table-column label="折扣" align="center" show-overflow-tooltip min-width="10%">
              <template slot-scope="scope">
                {{scope.row.payType + scope.row.disc + '折'}}
              </template>
            </el-table-column>
            <el-table-column label="储值余额" prop="hasMoney" align="center" show-overflow-tooltip min-width="11%"></el-table-column>
            <el-table-column label="会员积分" prop="integral" align="center" show-overflow-tooltip min-width="10%"></el-table-column>
            <el-table-column label="注册时间" prop="createTime" align="center" show-overflow-tooltip min-width="10%"></el-table-column>
            <el-table-column label="消费日期" prop="tradeTime" align="center" show-overflow-tooltip min-width="10%">
              <template slot-scope="scope">
                {{scope.row.tradeTime || '-'}}
              </template>
            </el-table-column>
            <el-table-column label="会员状态" prop="isDeleted" align="center" show-overflow-tooltip min-width="10%"></el-table-column>
          </el-table>
        </div>
        <div class="pc_imp1_3">
          <div class="pc_imp1_3_1">
            共 <span style="color: #d5aa76;">{{total}}</span> 条数据
          </div>
          <div
            class="pc_imp1_3_2"
          >
            <el-pagination
              layout="sizes, prev, pager, next"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pagenum"
              :page-sizes="[6, 10, 50, 100, 200, 300, 400, 500]"
              :page-size="limit"
              :page-count="total"
            ></el-pagination>
          </div>
        </div>
        <div class="pc_imp1_4">
          <div class="pc_imp1_4_1" @click="closeImport">取消</div>
          <div class="pc_imp1_4_2" @click="importVip">导入</div>
          <div v-if="chooseLists.length > 0" class="pc_imp_tip">{{chooseLists.length}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog, Table, TableColumn, Pagination } from 'element-ui';
import logList from '@/config/logList';
export default {
  components: {
    [Dialog.name]: Dialog,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination
  },
  data () {
    return {
      isLowWidth: false,
      date: ['', ''],
      birthday: ['', ''],
      birthdayDate: ['', ''],
      conDate: ['', ''],
      focusDate: false,
      focusBirthday: false,
      focusMoney: false,
      focusCon: false,
      money1: '',
      money2: '',
      chooseLists: [],
      pagenum: 1,
      limit: 6,
      total: 0,
      tableData: [],
      dateWidth: '320px;',
      tableHeight: '372px',
      isFloat: false,
      vipStatus: 0,
      vipStatusList: [{value: 0, label: '已激活'}, {value: 1, label: '已注销'}],
      closeImportFlag: false,
      searchFlag: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      }
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    getRowKeys(val) {
      return val.mobile;
    },
    importVip() {
      demo.actionLog(logList.clickSmsVipImportImport);
      if (this.closeImportFlag) {
        return;
      }
      this.closeImportFlag = true;
      setTimeout(() => {
        this.closeImportFlag = false;
      }, 1000);
      this.SET_SHOW({ chooseLists: this.chooseLists });
      this.closeImport();
    },
    closeImport() {
      this.SET_SHOW({ isImportVip: false });
    },
    handleSelectionChange(val) {
      this.chooseLists = val;
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      this.search();
    },
    handleSizeChange(val) {
      this.limit = val;
      this.search();
    },
    checkParam() {
      if (!demo.isNullOrTrimEmpty(this.date[1]) && new Date(this.date[0]).getTime() > new Date(this.date[1]).getTime()) {
        [this.date[0], this.date[1]] = [this.date[1], this.date[0]]; // 俩元素互换位置
      }
      if (new Date(this.birthday[0]).getTime() > new Date(this.birthday[1]).getTime()) {
        [this.birthday[0], this.birthday[1]] = [this.birthday[1], this.birthday[0]]; // 俩元素互换位置
      }
      if (!demo.isNullOrTrimEmpty(this.conDate[1]) && new Date(this.conDate[0]).getTime() > new Date(this.conDate[1]).getTime()) {
        [this.conDate[0], this.conDate[1]] = [this.conDate[1], this.conDate[0]]; // 俩元素互换位置
      }
      if (this.money1 !== '' && this.money2 !== '') {
        if (this.money1 * 1 > this.money2 * 1) {
          [this.money1, this.money2] = [this.money2, this.money1]; // 俩元素互换位置
        }
      }
    },
    search() {
      if (this.searchFlag) {
        return;
      }
      this.searchFlag = true;
      setTimeout(() => {
        this.searchFlag = false;
      }, 200);
      this.checkParam();
      // 处理“最近消费”日期为空/大小判断问题
      let consumeStartDate = this.conDate[0];
      let consumeEndDate = this.conDate[1];
      if (consumeStartDate && !consumeEndDate) {
        consumeEndDate = new Date().toISOString().slice(0, 10);
      }
      if (!consumeStartDate && consumeEndDate) {
        consumeStartDate = '2020-01-01'; // 系统诞生于2020年，理论上不存在早于这个日期的消费记录
      }
      if (new Date(consumeStartDate).getTime() > new Date(consumeEndDate).getTime()) {
        let middle = consumeStartDate;
        consumeStartDate = consumeEndDate;
        consumeEndDate = middle;
      }
      let param = {
        createStartTime: this.date[0] && this.date[0].length > 0 ? this.date[0] + ' 00:00:00' : '',
        createEndTime: this.date[1] && this.date[1].length > 0 ? this.date[1] + ' 23:59:59' : '',
        startBirthday: this.birthday[0],
        endBirthday: this.birthday[1],
        startDate: this.birthdayDate[0],
        endDate: this.birthdayDate[1],
        isDel: this.vipStatus,
        startHasMoney: this.money1,
        endHasMoney: this.money2,
        startTradeTime: consumeStartDate ? consumeStartDate + ' 00:00:00' : '',
        endTradeTime: consumeEndDate ? consumeEndDate + ' 23:59:59' : '',
        sysUid: this.sysUid,
        systemName: 'zhangguizhinang',
        pageSize: this.limit,
        currentPage: this.pagenum
      }
      demo.$http.post(this.$rest.getVipsDetail, param, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 60000
      }).then(res => {
        const obj = res.data;
        if (obj.code === '0') {
          this.tableData = obj.data.list;
          this.total = obj.data.total;
        } else {
          demo.msg('warning', obj.msg);
        }
      });
    },
    setWidth() {
      if (document.documentElement.clientWidth < 1300) {
        this.dateWidth = '300px;';
        if (document.documentElement.clientWidth < 1240) {
          this.isLowWidth = false;
          this.isFloat = true;
          this.dateWidth = '320px;';
          this.tableHeight = '267px';
          this.limit = 4;
        }
      } else {
        this.dateWidth = '320px;';
        this.isFloat = false;
      }
    }
  },
  created() {
    if (document.documentElement.clientWidth < 1500) {
      this.isLowWidth = true;
      this.setWidth();
    } else {
      this.isLowWidth = false;
      this.isFloat = false;
      this.dateWidth = '320px;';
    }
  },
  mounted() {
    if (this.isImportVip) {
      this.search();
    }
  },
  computed: mapState({
    isImportVip: state => state.show.isImportVip,
    sysUid: state => state.show.sys_uid
  })
};
</script>
