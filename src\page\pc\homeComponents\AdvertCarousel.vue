<template>
  <div class="warp">
    <el-carousel v-if="noticeList.length > 0"
      arrow='always'
      height="100%"
      :interval="interVal"
      :autoplay="autoPlay"
      class="pc_hom63"
      @change="carouselChange">
      <el-carousel-item v-for="(item, index) in noticeList"
        :key="'notice' + index" class="carouselItem">
        <el-image @click="goDetail(item)" :src="item.img"
          class="pc_hom54">
          <div slot="error" class="image-slot">
            <el-image @click="goDetail(item)" :src="item.remoteImg"></el-image>
          </div>
        </el-image>
      </el-carousel-item>
    </el-carousel>
    <!-- 底部广告弹窗 -->
    <el-dialog :visible.sync="showBottomAd" width="650px" top="15vh"
      :close-on-click-modal="true">
      <div class="imgWrap">
        <img v-lazy="bottomAdUrl" class="adImg" :src="bottomAdUrl" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  name: 'AdvertCarousel',
  props: {
    screenWidth: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      autoPlay: true,
      interVal: 5000, // 走马灯切换时间
      noticeCountTemp: {},
      noticeList: [],
      bottomAdUrl: '',
      showBottomAd: false // 是否显示弹窗广告
    }
  },
  computed: {
    ...mapState({
      isSyncingLogin: state => state.show.isSyncingLogin
    })
  },
  watch: {
    isSyncingLogin() {
      if (this.isSyncingLogin) {
        this.saveAdvertRecord();
      }
    }
  },
  mounted() {
    this.getAdvertisements();
  },
  beforeDestroy() {
    this.saveAdvertRecord();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 获取广告列表
    getAdvertisements() {
      external.getPoster(res => {
        this.noticeList = demo.t2json(res);
        console.warn('this.noticeList', this.noticeList)
        this.interVal = this.noticeList.length ? (this.noticeList[0].changeTime || 20) * 1000 : 20000;
        if (this.noticeList.length) {
          this.carouselChange(0);
        }
        this.autoPlay = false;
        setTimeout(() => {
          this.autoPlay = true;
        });
      });
    },
    // 轮播图广告记录
    carouselChange(newIndex) {
      // 缓存轮播计数，销毁时统一上传 同一个广告集计数据跨天的化需要分开传,key = id_optDate
      let optDate = new Date().format('yyyy-MM-dd');
      let item = this.noticeList[newIndex];
      let key = item.id + '_' + optDate;
      if (this.noticeCountTemp[key]) {
        this.noticeCountTemp[key].counts++;
      } else {
        this.noticeCountTemp[key] = {
          deviceId: $config.deviceId,
          adId: item.id,
          optDate: optDate,
          counts: 1,
          type: 2,
          adEvent: 'carousel'
        };
      }
      this.interVal = (item.changeTime || 20) * 1000;
      this.autoPlay = false;
      setTimeout(() => {
        this.autoPlay = true;
      });
    },
    // 轮播图片点击方法
    goDetail(item) {
      if (item.url !== '') {
        this.reportFormLog(_.cloneDeep({}), '短信通知查看详情');
        const func = item.url.split('_')[0];
        const param = item.url.split('_')[1];
        this[func](param, item, func);
      }
    },
    reportFormLog(sub_data, description) {
      // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({ page: 'home', action: 'reportFormLog', description });
      }
    },
    // 广告弹窗弹出
    showQRCode(name, item, func) {
      this.bottomAdUrl = $config.Base.MainPageUrl + '/advert/cache/4/' + name;
      this.showBottomAd = true;
      this.advertInfoAdd(item, func, 1);
    },
    // 短信弹窗弹出
    goShortMessage(name, item, func) {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，请联网后查看');
        return;
      }
      this.SET_SHOW({
        isHome: false,
        msgFrom: 0,
        isShortMessage: true
      });
      this.advertInfoAdd(item, func, 1);
    },
    // 广告记录方法
    advertInfoAdd(item, func, type) {
      if (!pos.network.isConnected()) {
        return;
      }
      const param = {
        deviceId: $config.deviceId,
        adId: item.id,
        type: type,
        adEvent: func
      };
      demo.$http
        .post(this.$rest.advertInfoAdd, param, {
          headers: {
            'Content-Type': 'application/json'
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(res => {
          if (res.data.code === 200) {
            CefSharp.PostMessage('广告点击log插入成功');
          }
        })
        .catch(err => {
          console.log(err, 'err');
        });
    },
    saveAdvertRecord() {
      let param = [];
      Object.keys(this.noticeCountTemp).forEach(key => {
        param.push(this.noticeCountTemp[key]);
      });
      if (!pos.network.isConnected() || !param.length) {
        return;
      }
      demo.$http
        .post(this.$rest.advertInfoAddNew, param)
        .then(res => {
          if (res.data.code === 200) {
            CefSharp.PostMessage('广告轮播次数log插入成功');
            this.noticeCountTemp = {};
          }
        })
        .catch(err => {
          console.log(err, 'err');
          CefSharp.PostMessage('广告轮播次数log插入err');
        });
    }
  }
}
</script>
<style lang="less" scoped>
.warp {
  width: 100%;
  height: 100%;
}
.pc_hom63 {
  height: 100%;
  .carouselItem {
    width: 100%;
    height: 100%;
  }
  .pc_hom54 {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
/deep/ .el-dialog__headerbtn {
  top: 3px;
  right: 3px;
}
/deep/ .el-dialog {
  padding: 20px;
}
.imgWrap {
  width: 610px;
  max-height: 610px;
  overflow-y: auto;
}
.adImg {
  width: 100%;
}
</style>
