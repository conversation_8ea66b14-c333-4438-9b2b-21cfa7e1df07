<style lang='less' scoped>
.com_qkd1 {
  width: 300px;height: 140px;position: fixed;z-index: 2;
}
.com_qkd9 {
  height: 37px;overflow: hidden;cursor: pointer;width: 184px;
  img {
    width: 18px;height: 18px;margin-top: 10px;margin-left: 10px;float: left;
  }
  div {
    float: left;margin-left: 10px;line-height: 14px;font-size: 14px;margin-top: 13px;color: #957159;font-weight: bold;
  }
}
.com_qkd11 {
  opacity: 0;
  transition: all 2s;
}
.com_qkd12 {
  opacity: 1;
  transition: all 2s;
}
.opacityChangeTrue {
  animation: opacityChangeTrue 2s 1;
}
.opacityChangeFalse {
  animation: opacityChangeFalse 2s 1;
}
@keyframes opacityChangeTrue {
  from {opacity: 0}
  to {opacity: 1}
}
@keyframes opacityChangeFalse {
  from {opacity: 1}
  to {opacity: 0}
}
</style>
<template>
  <div class="com_qkd1"
    :style="'top:' + top + 'px;left:' + left + 'px'"
    @mousedown="startPosition($event);startMove($event)" @touchstart="startMove($event)" >
    <div style="position: relative;float: left;width: 160px;">
      <img v-show="hello_img" @click="hello_img = false" src="../image/QKD/hello.png"
        style="width: 153px;height: 69px;margin-top: 20px;">
      <img v-show="code_img" @click="code_img = false" src="../image/QKD/code.png" style="width: 154px;height: 118px;">
    </div>
    <div style="position: relative;float: right;width: 140px;">
      <div v-show="close_icon" style="position: absolute;top: 5px;right: 5px;width: 24px;height: 24px;
        border: 2px solid #999;border-radius: 50%;line-height: 14px;text-align: center;font-size: 25px;
        color: #999;z-index: 10;cursor: pointer;" @click="endImg()">
        ×
      </div>
      <img id="start_img" v-show="start_img" :src="start_src"
        style="width: 140px;height: 140px;position: absolute;top: 0;left: 0;">
      <img id="stand_stretch_img" v-show="stand_stretch_img" src="../image/QKD/stand_stretch.gif"
        style="width: 140px;height: 140px;position: absolute;top: 0;left: 0;z-index: 1;">
      <img id="sleep_img" v-show="sleep_img" src="../image/QKD/sleep.gif"
        style="width: 140px;height: 140px;position: absolute;top: 0;left: 0;">
      <img id="end_img" v-show="end_img" src="../image/QKD/end.gif"
        style="width: 140px;height: 140px;position: absolute;top: 0;left: 0;">
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data () {
    return {
      start_img: true,
      stand_stretch_img: false,
      stretch_img: false,
      sleep_img: false,
      end_img: false,
      moving: false,
      top: window.innerHeight - 140,
      left: window.innerWidth - 300,
      sleep1: null,
      sleep2: null,
      sleep3: null,
      sleep4: null,
      position1: {x: 0, y: 0},
      position2: {x: 0, y: 0},
      showEWM: true,
      code_img: false,
      hello_img: false,
      close_icon: true,
      start_src: require('../image/QKD/start.gif'),
      stand_stretch_src: '',
      sleep_src: '',
      end_src: ''
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    startPosition(e) {
      this.position1 = {x: e.clientX, y: e.clientY};
    },
    startMove(e) {
      this.moving = true;
      var that = this;
      document.onmousemove = function(e) {
        that.moveDiv(e);
      };
      document.onmouseup = function(e) {
        that.endMove(e);
        that.position2 = {x: e.clientX, y: e.clientY};
        if (JSON.stringify(that.position1) === JSON.stringify(that.position2)) {
          that.toShowEWM();
        }
      };
      document.ontouchmove = function(e) {
        that.moveDiv(e);
      };
      document.ontouchend = function(e) {
        that.endMove(e);
      };
    },
    moveDiv(e) {
      if (!this.moving) {
        return;
      }
      var min_left = window.innerWidth < 1200 ? 100 : 210;
      var moveX = this.left + e.movementX;
      this.left = moveX < min_left
        ? min_left
        : (moveX > (window.innerWidth - 300)
          ? (window.innerWidth - 300) : moveX);
      var moveY = this.top + e.movementY;
      this.top = moveY < 0
        ? 0
        : (moveY > (window.innerHeight - 140)
          ? (window.innerHeight - 140) : moveY);
      // console.log(e, 666);
    },
    endMove() {
      this.moving = false;
    },
    sleep() {
      if (this.sleep1) {
        clearTimeout(this.sleep1);
        this.sleep1 = null;
      }
      if (this.sleep2) {
        clearTimeout(this.sleep2);
        this.sleep2 = null;
      }
      this.sleep1 = setTimeout(() => {
        if (this.sleep_img) {
          return;
        }
        this.sleep_img = true;
        document.getElementById('stand_stretch_img').className = 'opacityChangeFalse';
        document.getElementById('sleep_img').className = 'opacityChangeTrue';
        this.sleep2 = setTimeout(() => {
          this.stand_stretch_img = false;
          document.getElementById('stand_stretch_img').className = '';
          document.getElementById('sleep_img').className = '';
        }, 1600);
      }, 30000);
    },
    cancelSleep() {
      if (this.sleep1) {
        clearTimeout(this.sleep1);
        this.sleep1 = null;
      }
      if (this.sleep2) {
        clearTimeout(this.sleep2);
        this.sleep2 = null;
      }
      if (this.sleep_img) {
        if (this.sleep3) {
          clearTimeout(this.sleep3);
          this.sleep3 = null;
        }
        this.stand_stretch_img = true;
        document.getElementById('sleep_img').className = 'opacityChangeFalse';
        document.getElementById('stand_stretch_img').className = 'opacityChangeTrue';
        this.sleep4 = setTimeout(() => {
          this.sleep_img = false;
        }, 1500);
        this.sleep3 = setTimeout(() => {
          document.getElementById('sleep_img').className = '';
          document.getElementById('stand_stretch_img').className = '';
        }, 1600);
      }
      this.sleep();
    },
    endImg() {
      if (this.startImg) {
        clearTimeout(this.startImg);
        this.startImg = null;
      }
      if (this.sleep1) {
        clearTimeout(this.sleep1);
        this.sleep1 = null;
      }
      if (this.sleep2) {
        clearTimeout(this.sleep2);
        this.sleep2 = null;
      }
      if (this.sleep3) {
        clearTimeout(this.sleep3);
        this.sleep3 = null;
      }
      if (this.sleep4) {
        clearTimeout(this.sleep4);
        this.sleep4 = null;
      }
      this.start_img = false;
      this.stand_stretch_img = false;
      this.sleep_img = false;
      this.end_img = true;
      this.code_img = false;
      this.hello_img = false;
      this.close_icon = false;
      setTimeout(() => {
        this.end_img = false;
        this.SET_SHOW({isQKD: false});
      }, 4230);
    },
    toShowEWM() {
      this.hello_img = false;
      this.code_img = true;
    },
    clearImg() {
      this.start_src = '';
      this.stand_stretch_src = '';
      this.sleep_src = '';
    }
  },
  mounted() {
    this.startImg = setTimeout(() => {
      this.start_img = false;
      this.stand_stretch_img = true;
    }, 2000);

    this.helloImgInterval = setInterval(() => {
      if (this.code_img || this.end_img) {
        return;
      }
      this.hello_img = true;
      this.helloImgTimeout = setTimeout(() => {
        this.hello_img = false;
      }, 2000);
    }, 30000);

    this.sleep();
    var that = this;
    window.onmousemove = function() {
      if (that.end_img || !that.close_icon) {
        return;
      }
      that.cancelSleep();
    }
  },
  beforeDestroy() {
    if (this.helloImgInterval) {
      clearInterval(this.helloImgInterval);
      this.helloImgInterval = null;
    }
    if (this.helloImgTimeout) {
      clearTimeout(this.helloImgTimeout);
      this.helloImgTimeout = null;
    }
    if (this.startImg) {
      clearTimeout(this.startImg);
      this.startImg = null;
    }
    if (this.sleep1) {
      clearTimeout(this.sleep1);
      this.sleep1 = null;
    }
    if (this.sleep2) {
      clearTimeout(this.sleep2);
      this.sleep2 = null;
    }
    if (this.sleep3) {
      clearTimeout(this.sleep3);
      this.sleep3 = null;
    }
    if (this.sleep4) {
      clearTimeout(this.sleep4);
      this.sleep4 = null;
    }
    document.onmousemove = null;
    window.onmousemove = null;
  },
  watch: {
    stand_stretch_img() {
      this.clearImg();
      if (this.stand_stretch_img) {
        this.stand_stretch_src = require('../image/QKD/stand_stretch.gif');
      } else {
        setTimeout(() => {
          this.stand_stretch_src = '';
        }, 0);
      }
    },
    sleep_img() {
      this.clearImg();
      if (this.sleep_img) {
        this.sleep_src = require('../image/QKD/sleep.gif');
      } else {
        setTimeout(() => {
          this.sleep_src = '';
        }, 0);
      }
    },
    end_img() {
      this.clearImg();
      this.end_src = require('../image/QKD/end.gif');
    }
  }
};
</script>
