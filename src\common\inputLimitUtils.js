/**********************************************************************************
 * 根据业务场景 统一定义输入域过滤函数
 * 1 goodsNameFormat 商品名称过滤
 *    作用域：商品->首页                         搜索
 *               ->新增                         商品名称
 *               ->商品拆包                      搜索
 *               ->商品拆包->新增关系->选择商品   搜索
 *               ->库存盘点                      搜索
 *           报表->商品销售统计                   搜索
 *               ->销售明细                      搜索
 *               ->进货明细                      搜索
 *               ->寄存->寄存统计                 搜索
 *               ->库存->库存查询                 搜索
 *               ->库存->变动明细                 搜索
 *               ->库存->盘点明细                 搜索
 *               ->库存->库存预警                 搜索
 *               ->库存->过期预警                 搜索
 *           进退货->首页                         搜索
 *                ->供应商                        搜索
 *                ->供应商->新增                  供应商名称
 *           会员->详情->购买次卡                  搜索
 *           会员->详情->寄件                      搜索
 *           会员->详情->兑换                      搜索
 *           会员设置->次卡设置->首页              搜索
 *           会员设置->次卡设置->新增              次卡名称
 * 2 vipNameFormat 会员名称过滤
 *    作用域：会员->首页                         搜索
 *               ->新增                         会员名称
 *               ->编辑                         会员名称
 *           报表->会员->会员交易明细             搜索
 *               ->会员->会员充值统计             搜索
 *               ->会员->积分变动明细             搜索
 *               ->次卡->次卡销售明细             搜索
 *               ->次卡->次卡使用明细             搜索
 *               ->寄存->寄存明细                 搜索
 *           员工->首页                         搜索
 *               ->新增                         员工名称
 *               ->编辑                         员工名称
 * 3 toDecimalFormat 处理金额、数量四舍五入
 *    作用域： 作用于整个项目的金额、数量等需要四舍五入的场景
 * ******************************************************************************/
import { stringFormat, floatNumberFormat } from './utils/zgzn-input-utils.esm.js';

/**
 * 商品名称输入格式化
 * @param {string} inputValue 要格式化的字符串
 * @returns 格式化后的字符串
 */
export const goodsNameFormat = inputValue => {
  const defaultOption = {
    maxLen: 60, // 字符串最大长度 默认0（无限制） <0 默认为0
    specialFilter: false, // 是否过滤特殊字符
    emojiFilter: true, // 是否过滤emoji字符
    chineseFilter: false, // 是否过滤中文
    chineseDouble: false, // 汉字/全角字符是否按照双倍长度计算
    halfFull: '', // 半角全角控制，默认半角half，可选全角full，不做控制''
    default: '' // 默认值，输入值不符合要求时返回
  };
  return stringFormat(inputValue, defaultOption).replace(/[$';:]/g, '');
};
/**
 * 会员名称输入格式化
 * @param {string} inputValue 要格式化的字符串
 * @returns 格式化后的字符串
 */
export const vipNameFormat = (inputValue, option = {}) => {
  const defaultOption = {
    maxLen: 15, // 字符串最大长度 默认0（无限制） <0 默认为0
    specialFilter: false, // 是否过滤特殊字符
    emojiFilter: true, // 是否过滤emoji字符
    chineseFilter: false, // 是否过滤中文
    chineseDouble: false, // 汉字/全角字符是否按照双倍长度计算
    halfFull: 'half', // 半角全角控制，默认半角half，可选全角full，不做控制''
    default: '' // 默认值，输入值不符合要求时返回
  };
  return stringFormat(inputValue, Object.assign(defaultOption, option)).replace(/[$';:]/g, '');
};

/**
 * 会员卡号输入格式化
 * @param {string} inputValue 要格式化的字符串
 * @returns 格式化后的字符串
 */
export const vipCardNumberFormat = inputValue => {
  const defaultOption = {
    maxLen: 15, // 字符串最大长度 默认0（无限制） <0 默认为0
    specialFilter: true, // 是否过滤特殊字符
    emojiFilter: true, // 是否过滤emoji字符
    chineseFilter: true, // 是否过滤中文
    chineseDouble: false, // 汉字/全角字符是否按照双倍长度计算
    halfFull: 'half', // 半角全角控制，默认半角half，可选全角full，不做控制''
    default: '' // 默认值，输入值不符合要求时返回
  };
  return stringFormat(inputValue, defaultOption).replace(/[$';:]/g, '');
};

/**
 * 处理金额、数量四舍五入
 * @param {Number,string} inputValue 要格式化的数字
 * @param {Number} decimal 要精确的小数位，默认两位
 * @param {Boolean} isZeroPad 是否零填充 true: 按照要求的小数位补零 false: 返回实际值 默认false
 * @returns 格式化后的字符串
 */
export const toDecimalFormat = (inputValue, decimal = 2, isZeroPad = false) => {
  const defaultOption = {
    decimal,
    roundType: 'round',
    isZeroPad
  };
  // console.log(floatNumberFormat(inputValue, defaultOption));
  const value = floatNumberFormat(inputValue, defaultOption);
  if (isZeroPad) {
    return Number(value).toFixed(decimal);
  } else {
    return Number(value);
  }
};

export const typeNameFormat = inputValue => {
  const defaultOption = {
    maxLen: 20, // 字符串最大长度 默认0（无限制） <0 默认为0
    specialFilter: false, // 是否过滤特殊字符
    emojiFilter: true, // 是否过滤emoji字符
    chineseFilter: false, // 是否过滤中文
    chineseDouble: true, // 汉字/全角字符是否按照双倍长度计算
    halfFull: '', // 半角全角控制，默认半角half，可选全角full，不做控制''
    default: '' // 默认值，输入值不符合要求时返回
  };
  return stringFormat(inputValue, defaultOption);
};
