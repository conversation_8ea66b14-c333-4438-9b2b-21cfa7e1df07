import Vue from 'vue';
import Icon from 'vue-svg-icon/Icon.vue';
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import App from './App';
import router from './router';
// 公共样式
import './less/common.less';
// loading
import './less/loading.less';
import '@/assets/iconfont/iconfont.css'; // font-class icon
import store from './store/'; // 加载vuex
import components from './components/'; // 加载公共组件
import 'bootstrap/dist/css/bootstrap.css';
import VModal from 'vue-js-modal';
import md5 from 'js-md5';
import axios from 'axios';
import 'vant/lib/icon/local.css';
import VueLazyload from 'vue-lazyload';
// import VueSocketIO from 'vue-socket.io';
// import SocketIO from 'socket.io-client';
import { Toast, NoticeBar } from 'vant';
import rest from './config/rest';
import message from './config/message';
import Log from './log/logs';
// POS
import './common/';
import scanner from './common/scanner';
import makeExcel from './common/makeExcel';
import inputLimits from './common/inputLimits';
import lmt from './common/inputLmt';
import _ from 'lodash';
import { regionData } from 'element-china-area-data';
import VueI18n from 'vue-i18n';
import { newMd5 } from './utils/newMd5';
import { goodsNameFormat, vipNameFormat, toDecimalFormat, vipCardNumberFormat } from '@/common/inputLimitUtils.js';
import Big from 'big.js';
import focusSelect from '@/utils/directive.js';
// 引入数据字典
import '@/config/dict/index';
Object.keys(inputLimits).forEach(item => {
  Vue.directive(item, inputLimits[item]);
});
Vue.use(focusSelect);
Vue.use(lmt);
Vue.use(scanner);
Vue.use(makeExcel);
Vue.use(regionData);
Vue.use(VueI18n);
const i18n = new VueI18n({
  locale: 'zgzn-pos-zh',
  messages: {
    'bdccai-pos-zh': require('./assets/lang/bdccai-pos-zh.js'),
    'zgzn-pos-zh': require('./assets/lang/zgzn-pos-zh.js')
  }
});
// Vue.use(new VueSocketIO({
//   connection: SocketIO(rest.webSocketUrl, {autoConnect: false}),
//   store
// }));
global.md5 = md5;
global.newMd5 = newMd5;
Vue.prototype.$rest = rest;
// 拦截
axios.defaults.timeout = 10000;
axios.interceptors.request.use(
  config => {
    var ver = demo.$rest.version.replace(/[^\d.]/g, '');
    const token = demo.$store.state.show.token || $config.token;
    config.headers = {
      'Content-Type': 'application/json',
      Authorization: token,
      version: '1.7',
      'n-d-version': '{"pos-api": "' + ver + '", "vip-api": "' + ver + '"}'
    };
    if (config.method === 'post') {
      config.url += (config.url.indexOf('?') === -1 ? '?ver=' : '&ver=') + ver;
    }
    let loginInfo = demo.$store.state.show.loginInfo;
    if (!demo.isNullOrTrimEmpty(loginInfo) && +loginInfo.uid > 0) {
      config.headers.uid = +loginInfo.uid;
    }
    return config;
  },
  err => {
    console.error(err);
  }
);
axios.interceptors.response.use(
  response => {
    const tokenErrorCodes = [41000, 41001, 40101, 41003];
    const code = +response.data.code;
    if (tokenErrorCodes.indexOf(code) !== -1) {
      demo.$store.commit('SET_SHOW', {tokenReLogin: true});
      setTimeout(() => {
        demo.$message.closeAll();
      });
    }
    return response;
  },
  error => {
    if (!pos.network.isConnected()) {
      demo.msg('error', '本地网络处于离线状态，会员/云同步功能将无法使用');
      return error;
    }
    if (error && error.stack.toLowerCase().indexOf('network') > -1) {
      CefSharp.PostMessage(`network error:${error.stack}`);
      return { data: { msg: '无法连接远端服务器,处于离线状态', code: '10001' } };
    }
    if (error && error.stack.indexOf('timeout') > -1) {
      CefSharp.PostMessage(`timeout error:${error.stack}`);
      return { data: { msg: '服务器链接超时', code: '10002' } };
    }
    return Promise.reject(error);
  }
);
Vue.prototype.$msg = message;
Vue.prototype.$http = axios;
Vue.prototype.$store = store;
// 输入域组件方法
Vue.prototype.$goodsNameFormat = goodsNameFormat;
Vue.prototype.$vipNameFormat = vipNameFormat;
Vue.prototype.$vipCardNumberFormat = vipCardNumberFormat;
// 数字格式化方法
Vue.prototype.$toDecimalFormat = toDecimalFormat;
Vue.use(ElementUI);
Vue.use(Toast);
Vue.use(NoticeBar);
Vue.use(VueLazyload);
Vue.use(Log);
Vue.component('icon', Icon);
Vue.config.productionTip = true;
Object.keys(components).forEach(key => {
  var name = key.replace(/(\w)/, v => v.toUpperCase()); // 首字母大写
  Vue.component(`v${name}`, components[key]);
});
Vue.use(VModal);
Vue.prototype.$event = new Vue();
// 全角转半角
Vue.prototype.$convertFullWidthToHalfWidth = str => {
  if (/[０-９Ａ-Ｚａ-ｚ]/.test(str)) {
    str = str.replace(/[０-９Ａ-Ｚａ-ｚ]/g, function(fullWidthChar) {
      return String.fromCharCode(fullWidthChar.charCodeAt(0) - 65248);
    });
  }
  if (/．/.test(str)) {
    str = str.replaceAll(/．/g, '.');
  }
  if (/／/.test(str)) {
    str = str.replaceAll(/／/g, '-');
  }
  if (/－/.test(str)) {
    str = str.replaceAll(/－/g, '-');
  }
  return str;
};
global.demo = new Vue({
  el: '#app',
  store,
  i18n,
  router,
  components: {
    App
  },
  template: '<App/>'
});

demo.t2json = function(res) {
  if (typeof res === 'string') {
    if (res.trim() === '') {
      return void 0;
    } else {
      return JSON.parse(res);
    }
  } else {
    return res;
  }
};
CefSharp.PostMessage(`当前系统环境:${process.env.NODE_ENV}`);
demo.updateStoreSettings = (key, value) => {
  return new Promise(resolve => {
    let m_list = _.cloneDeep(demo.$store.state.show.storeList);
    var settings = JSON.parse(m_list[0].settings);
    settings[key] = value;
    m_list[0].settings = JSON.stringify(settings);
    demo.$store.commit('SET_SHOW', { storeList: m_list });
    storeInfoService.updateSettings({ id: 1, settings: JSON.stringify(settings) }, () => {
      resolve();
    });
  });
};
demo.msg = function(type, msg, n) {
  var show_msg = '';
  show_msg = msg === undefined ? '出现异常，请稍后再试' : msg;
  demo.$message.closeAll();
  return demo.$message({
    showClose: true,
    dangerouslyUseHTMLString: true,
    message: '<span style="font-size: 16px;">' + show_msg + '</span>',
    duration: n === undefined ? '3000' : n,
    type: type
  });
};
demo.isExcelDateTimeNumber = (value) => {
  if (typeof value !== 'number' || value <= 0) {
    return false;
  }
  // 假设 Excel 中的日期时间数字的范围是 1 到 73049
  if (value < 1 || value > 73049) {
    return false;
  }
  // 尝试将数字转换为日期时间格式，如果转换失败则不是 Excel 中的日期时间数字
  return !isNaN(new Date((value - 1) * 24 * 60 * 60 * 1000));
}
Vue.prototype.$employeeAuth = function(name) {
  // demo.employeeAuth = function (name) {
  if (demo.$store.state.show.employeeAuth.indexOf(name) !== -1) {
    return true;
  } else {
    return false;
  }
};
var Screen2data = {};
function initScreen2() {
  Screen2data = {
    screen2ShowFollowErweima: '',
    screen2ErweimaName: '',
    screen2ErweimaMobile: '',
    screen2FollowErweima: '',
    screen2ShowList: '',
    // 优惠金额
    screen2ReducePrice: '',
    // 总金额
    screen2TotalPrice: '',
    // 显示实收金额
    screen2ReceiveMoney: '',
    // 主屏是否为退货状态
    screen2ReturnGoods: false,
    // 应付金额（可能被去0）
    screen2ShowFinalPrice: '',
    screen2ShowMember: false,
    screen2MemberMoney: 0,
    screen2MemberPayType: '',
    screen2MemberName: '',
    screen2MemberPoint: '',
    screen2MemberPhone: '',
    show_advert: false
  };
}
// 数组判等function  arr.equals()
// Warn if overriding existing method
if (Array.prototype.equals) {
  console.warn(
    'Overriding existing Array.prototype.equals. Possible causes: New API defines the method,' +
      "there's a framework conflict or you've got double inclusions in your code."
  );
}
Array.prototype.equals = function(array) {
  if (!array) {
    return false;
  }
  if (this.length != array.length) {
    return false;
  }
  for (var i = 0, l = this.length; i < l; i++) {
    if (this[i] instanceof Array && array[i] instanceof Array) {
      if (!this[i].equals(array[i])) {
        return false;
      }
    } else if (this[i] != array[i]) {
      return false;
    }
  }
  return true;
};
/**
 * 重写 Number.prototype.toFixed() 方法
 * 解决四舍五入问题
 * @param s 四舍五入位数
 */
Number.prototype.toFixed = function (s) {
  const num = isNaN(this) ? 0 : this
  const value = new Big(num).mul(1000000000).round().div(1000000000).toFixed(s)
  return value
}
Object.defineProperty(Array.prototype, 'equals', { enumerable: false });
window.$scanerObj = { Result: '' };
// Object.defineProperty(window, '$scanerCodes', {
//   get() {
//     console.log('get', this._$scanerCodes);
//     return this._$scanerCodes;
//   },
//   set(value) {
//     console.log('$scanerCodes set');
//     demo.$store.commit('SET_SHOW', { scanerCode: value });
//   }
// });
function scanCodeProxy(obj, prop) {
  // 自定义监听$scanerObj,闭包避免重复获取栈溢出
  let val = obj[prop];
  Object.defineProperty(obj, prop, {
    get: function() {
      return val;
    },
    set: function(value) {
      demo.$store.commit('SET_SHOW', { scanerObj: value });
    }
  });
}
scanCodeProxy(window, '$scanerObj');
document.body.onfocus = function() {
  demo.$store.commit('SET_SHOW', { zgznActive: true });
  CefSharp.PostMessage(`软件进入活跃状态，正常监听扫码枪扫码动作`);
};
document.body.onblur = function() {
  demo.$store.commit('SET_SHOW', { zgznActive: false });
  CefSharp.PostMessage(
    `软件进入失焦状态，扫码枪扫码功能暂时禁用，当前页面停留在收银台?${demo.$store.state.show.isPay}`
  );
};
initScreen2();
var aim = '';
demo.screen2 = function(newData) {
  $.extend(Screen2data, newData);
  clearTimeout(aim);
  aim = setTimeout(function() {
    external.reloadScreen2(Screen2data);
  }, 10);
};
Vue.prototype.$getSrcUrl = function(image) {
  if (image && image.indexOf($config.Base.MainPageUrl) < 0) {
    var filename = image.substr(image.lastIndexOf('/'));
    image = $config.Base.MainPageUrl + '/productImg' + filename + '?url=' + image;
  }
  return image;
};
demo.weightConvert = function(num) {
  var ele = document.getElementById('iw');
  if (num === '' || isNaN(num) || Number(num) < 0) {
    ele.value = '';
    demo.$store.commit('SET_SHOW', { weighStatus: `·${Number(num) < 0 ? '已' : '未'}连接` });
    demo.$store.commit('SET_SHOW', { weighSet: 0 });
    return;
  }
  demo.$store.commit('SET_SHOW', { weighStatus: '·已连接' });
  var val = parseInt(num) / 1000;
  demo.$store.commit('SET_SHOW', { weighSet: val });
  ele.value = val;
  ele.dispatchEvent(new Event('input'));
};
demo.weightConvertSetting = function(num) {
  if (num === '' || isNaN(num) || Number(num) < 0) {
    demo.$store.commit('SET_SHOW', { weighStatus: `·${Number(num) < 0 ? '已' : '未'}连接` });
    demo.$store.commit('SET_SHOW', { weighSet: 0 });
    return;
  }
  demo.$store.commit('SET_SHOW', { weighStatus: '·已连接' });
  var val = parseInt(num) / 1000;
  demo.$store.commit('SET_SHOW', { weighSet: val });
};
demo.checkWeight = function(num) {
  console.log(num, 'checkWeight');
  if (num === '' || isNaN(num)) {
    console.log('电子秤检测:', false);
    return;
  }
  console.log('电子秤检测:', true);
  demo.$store.commit('SET_SHOW', { hasWeigh: true });
  demo.$store.commit('SET_SHOW', { weighValue: 'COM2' });
  demo.$store.commit('SET_SHOW', { weighTypeValue: 'HongHaiACS' });
  demo.$store.commit('SET_SHOW', { isOnceOpen: false });
  demo.$store.commit('SET_SHOW', { isConnectWeight: true });
};
// 窗口大小变化监听
let resizeTimer;
function listenScreenResize() {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  resizeTimer = setTimeout(() => {
    let screenWidth = document.body.clientWidth;
    demo.$store.commit('SET_SHOW', { screenWidth: screenWidth });
    console.log('当前窗体宽度:' + demo.$store.state.show.screenWidth);
  }, 400);
}
window.addEventListener('resize', listenScreenResize); // 窗口大小变化监听

demo.sqlConversion = obj => {
  var newObj = {};
  Object.keys(obj).forEach(key => {
    var val = obj[key];
    if (val === null) {
      newObj[key] = null;
      return;
    }

    if (typeof val === 'string') {
      newObj[key] = `'${val.replace(/'/g, "''").replace(/;/g, '；').replace(/\$/g, '$'.repeat(4))}'`;
    } else if (typeof val === 'object') {
      newObj[key] = `'${JSON.stringify(val)}'`;
    } else {
      newObj[key] = val;
    }
  });

  return newObj;
};

demo.isNullOrTrimEmpty = obj => {
  return obj === null || obj === undefined || (obj + '').trim() === '';
};
demo.isNullOrUndefined = obj => {
  return obj === null || obj === undefined;
};
demo.toDataFormat = (obj, format) => {
  return obj === null ? '' : obj.format(format);
};

demo.actionLog = ({ page = '', action = '', description = '' }, callback) => {
  let info = {};
  let maxUploadRows = action === 'logout' ? 100 : 10;
  if (action === 'login') {
    info = {
      mac: pos.network.getMac(),
      version: external.getVersion()
    };
    if (typeof returnCitySN != 'undefined') {
      Object.assign(info, returnCitySN || {});
    }
  }
  actionLog.post(
    { maxUploadRows, page, action, description, info },
    () => {
      action === 'logout' && callback();
    },
    () => {
      if (action === 'logout') {
        demo.$log.info('logerr', '记录log出错');
        callback();
      }
    }
  );
};

/**
 *  避免JS计算误差、限制最大小数位数并去除末尾多余0
 *  传入处理的数据及最大保留的小数位数，返回处理过的数据
 * @param {number, digit}
 * @returns {number}
 */
Vue.prototype.setMaxDecimal = (number, digit) => {
  return Number(Number(number).toFixed(digit));
};

Vue.prototype.selectText = id => {
  document.getElementById(id).focus();
  document.getElementById(id).select();
};

/**
 * 下划线转换驼峰
 * @param {*} str
 * @returns
 */
demo.toHump = str => {
  return str.replace(/_(\w)/g, (_all, letter) => {
    return letter.toUpperCase();
  });
};

demo.rowToHump = (row, columns) => {
  _.forEach(_.keys(columns), item => {
    row[columns[item]] = row[item];
    delete row[item];
  });
};

demo.conversion = (val, bits = 1000) => {
  let num = val;
  if (!val) {
    num = 0;
  }
  if (!Number.isInteger(num)) {
    num = Math.round((Number(val) + Number.EPSILON) * bits) / bits;
  }
  // console.log(val, num);
  return num;
}

demo.excelTimeToTimestamp = (excelTime) => {
  const second = 25569;
  const dayTimestamp = 24 * 60 * 60 * 1000;
  return (+excelTime - second) * dayTimestamp;
}

demo.tableToHump = table => {
  if (table.length > 0) {
    var firstRow = table[0];
    var columns = {};
    _.forEach(_.keys(firstRow), item => {
      if (item.includes('_')) {
        columns[item] = demo.toHump(item);
      }
    });
    _.forEach(table, item => {
      demo.rowToHump(item, columns);
    });
  }

  return table;
};
/**
 * 驼峰转换下划线
 * @param {*} str
 * @returns
 */
demo.toLine = str => {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
};

document.oncontextmenu = () => false;
demo.$store.commit('SET_SHOW', { screenWidth: document.documentElement.clientWidth });
demo.$store.commit('SET_SHOW', { screenHeight: document.documentElement.clientHeight });
window.onresize = function() {
  demo.$store.commit('SET_SHOW', { screenWidth: document.documentElement.clientWidth });
  demo.$store.commit('SET_SHOW', { screenHeight: document.documentElement.clientHeight });
};

// 埋点
Vue.prototype.$_actionLog = (log, desc) => {
  const description = desc ? `${log.description}-${desc}` : log.description
  demo.actionLog({...log, description});
};

/**
 * 输入框获取焦点时，自动选中全部值
 * @param {*} e
 */
Vue.prototype.$inputFocusSelectValue = e => {
  e.currentTarget.select();
};
