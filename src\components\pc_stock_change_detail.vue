<template>
  <!-- 库存变动明细 -->
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <!-- 商品选择部分 -->
    <div v-if="show_goods" class="psc_det11">
      <div class="psc_det12">
        <div class="psc_det13">
          <div style="float: left;">选择商品</div>
          <div class="psc_det14" @click="show_goods = false">×</div>
        </div>
        <div class="psc_det15">
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder="商品名称/条码/首字母/扫码"
            v-model='keyword'
            id='goods_keyword'
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="keyword = $goodsNameFormat(keyword)"
            @keydown.enter="inputSelectHandler('goods_keyword')"
          />
          <img alt="" src="../image/pc_clear_input.png" class="deleteIcon" v-if="showClearIcon" @click="keyword = ''">
        </div>
        <div class="psc_det16">
          <div class="psc_det17">
            <div class="psc_det18" style="font-weight: bold;width: 206px">商品名称</div>
            <div class="psc_det18" style="width:205px;font-weight: bold;">条码</div>
            <div class="psc_det18" style="width: 171px;font-weight: bold;text-align: right;margin-left: 0px;">库存</div>
          </div>
          <div :class="goods_list.length > 0 ? 'psc_det_content' : 'flex'">
            <div class="psc_det171"
              v-for="(go, index) in goods_list" :key="index"
              @click="tempGoodsIndex = index;tempGoodsName = go.name;tempGoodsId = go.fingerprint"
              style="cursor:pointer"
              :style="tempGoodsIndex === index ? 'background: #F8F4EC;' : (index % 2 === 0 ? 'background: #FFF;' : 'background: #FAFBFC;')">
              <div class="psc_det18" style="height: 50px;width: 206px">{{go.name}}</div>
              <div class="psc_det18" style="width: 205px;height: 50px;">{{go.code}}</div>
              <div class="psc_det18" style="width: 171px;height: 50px;text-align: right;margin-left: 0px;">{{setMaxDecimal(go.curStock, 3)}}</div>
            </div>
            <div v-if="goods_list.length === 0">
              <div class="empty">暂无数据</div>
            </div>
          </div>
          <!--分页器-->
          <div class="page-content">
            <el-pagination
              :key="goodsPageKey"
              layout="prev, pager, next, slot"
              :total="total"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-size="size"
            >
              <!-- slot -->
              <vCjPageSize
                @sizeChange="goodsSizeChange"
                :pageSize.sync="size"
                :currentPage.sync="currentPage"
                :pageKey.sync="goodsPageKey">
              </vCjPageSize>
            </el-pagination>
          </div>
        </div>
        <div style="overflow: hidden;">
          <div class="psc_det19" id="butBackground" style="color: #FFF;"  @click="getPDList('选择')">选择</div>
          <div class="psc_det19" id="butColor" @click="show_goods = false">取消</div>
        </div>
        <div style="height: 20px;"></div>
      </div>
    </div>
    <!-- 详情弹窗部分 -->
    <div v-if="show_detail" class="psc_det11">
      <div class="psc_det12" style="width: 940px;font-size: 16px;">
        <div class="psc_det13" style="width: 900px;">
          <div style="float: left;font-size: 18px;">{{title}}</div>
          <div class="psc_det14" @click="show_detail = false">×</div>
        </div>
        <div style="width: 900px;height: 20px;margin-left: 20px;border-bottom: 1px solid #E3E6EB;"></div>
        <!--销售单/退款单详情-->
        <div v-show="tableName === 'sales'">
          <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-top: 30px;">
            <div class="psc_det18" style="width: 50px;font-weight: bold;margin-left: 0;text-align: center;">序号</div>
            <div class="psc_det18" style="width: 388px;font-weight: bold;text-align: left;margin-left: 12px;">名称</div>
            <div class="psc_det18" style="width: 68px;font-weight: bold;text-align: left;margin-left: 0;">数量</div>
            <div class="psc_det18" style="width: 87px;font-weight: bold;text-align: right;margin-left: 0;">单价</div>
            <div class="psc_det18" style="width: 85px;font-weight: bold;text-align: center;margin-left: 65px;">折扣</div>
            <div class="psc_det18" style="width: 95px;font-weight: bold;text-align: right;margin-left: 0;">小计</div>
          </div>
          <div style="height: 290px;overflow: scroll;margin-top: 30px;">
            <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-bottom: 30px;" v-for="(si, index) in detail_list" :key="index">
              <div class="psc_det18" style="width: 50px;margin-left: 0;height: 16px;text-align: center;">{{index + 1}}</div>
              <div class="psc_det18" style="width: 388px;height: 16px;text-align: left;margin-left: 12px;">{{si.name}}</div>
              <!-- <div class="psc_det18" style="width: 68px;height: 16px;text-align: left;margin-left: 0;">{{si.qty}}</div> -->

              <el-popover
                :disabled="(Number(si.qty)+'').length<8"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(si.qty) + ''">
                <div slot="reference" class="psc_det18" style="width: 68px;height: 16px;text-align: left;margin-left: 0;">{{si.qty}}</div>
              </el-popover>
              <div class="psc_det18" style="width: 87px;height: 16px;text-align: right;margin-left: 0;">¥ {{Number(si.price).toFixed(2)}}</div>
              <div class="psc_det18" style="width: 85px;height: 16px;text-align: center;margin-left: 65px;">{{si.disc}}</div>
              <el-popover
                :disabled="(Number(si.amt).toFixed(2) + '').length<10"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(si.amt).toFixed(2) + ''">
                <div slot="reference" class="psc_det18" style="width: 95px;height: 16px;text-align: right;margin-left: 0;">¥ {{Number(si.amt).toFixed(2)}}</div>
              </el-popover>
            </div>
          </div>
          <div style="height: 10px;margin-left: 20px;width: 900px;border-bottom: 1px dashed #DCDCDC;"></div>
          <div class="psc_det21">
            <div>收银员：{{detail_info.employeeNumber}}</div>
            <div>会员：{{detail_info.vipname ? detail_info.vipname : '-'}}</div>
          </div>
          <div class="psc_det21">
            <div>优惠金额：¥ {{Number(detail_info.discountAmount).toFixed(2)}}</div>
            <div>应{{title.includes('退款') ? '退' : '收'}}金额：¥ {{ `${$toDecimalFormat(Math.abs(detail_info.payAmt), 2, true)}` }}</div>
          </div>
          <div class="psc_det21">
            <div>结算方式：{{detail_info.acctName}}</div>
            <div>实{{title.includes('退款') ? '退' : '收'}}金额：¥ {{Number(title.includes('退款') ? -(detail_info.payAmt) : (Number(detail_info.payAmt) + Number(detail_info.changeAmt))).toFixed(2)}}</div>
            <div v-if="!title.includes('退款')">找零金额：¥ {{Number(detail_info.changeAmt).toFixed(2)}}</div>
          </div>
          <div class="psc_det21">
            <div>说明：{{detail_info.remark === '' ? '-' : detail_info.remark}}</div>
          </div>
        </div>
        <!--进货单/退货单详情-->
        <div v-show="tableName === 'purchases'">
          <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-top: 30px;">
            <div class="psc_det18" style="width: 50px;font-weight: bold;margin-left: 0;text-align: center;">序号</div>
            <div class="psc_det18" style="width: 270px;font-weight: bold;text-align: left;margin-left: 12px;">名称</div>
            <div class="psc_det18" style="width: 90px;font-weight: bold;text-align: right;margin-left: 0;"
              v-if="!$employeeAuth('purchase_price')">进货价</div>
            <div class="psc_det18" style="width: 165px;font-weight: bold;text-align: left;margin-left: 55px;">商品条码</div>
            <div class="psc_det18" style="width: 60px;font-weight: bold;text-align: left;margin-left: 0px;">单位</div>
            <div class="psc_det18" style="width: 70px;font-weight: bold;text-align: right;margin-left: 0;">数量</div>
            <div class="psc_det18" style="width: 95px;font-weight: bold;text-align: right;margin-left: 0;"
              v-if="!$employeeAuth('purchase_price')">金额</div>
          </div>
          <div style="height: 290px;overflow: scroll;margin-top: 30px;">
            <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-bottom: 30px;" v-for="(si, index) in detail_list" :key="index">
              <div class="psc_det18" style="width: 50px;height: 16px;margin-left: 0;text-align: center;">{{index + 1}}</div>
              <div class="psc_det18" style="width: 270px;height: 16px;text-align: left;margin-left: 12px;">{{si.name}}</div>
              <el-popover
                v-if="!$employeeAuth('purchase_price')"
                :disabled="Number(si.price).toFixed(2).length<9"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(si.price).toFixed(2) + ''">
                <div slot="reference" class="psc_det18" style="width: 90px;height: 16px;text-align: right;margin-left: 0;">¥ {{$toDecimalFormat(si.price, 6)}}</div>
              </el-popover>
              <div class="psc_det18" style="width: 165px;height: 16px;text-align: left;margin-left: 55px;">{{si.code}}</div>
              <div class="psc_det18" style="width: 60px;height: 16px;text-align: left;margin-left: 0px;">{{si.unitsName}}</div>
              <el-popover
                :disabled="(Number(si.qty)+'').length<8"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(si.qty) + ''">
                <div slot="reference" class="psc_det18" style="width: 70px;height: 16px;text-align: right;margin-left: 0;">{{si.qty}}</div>
              </el-popover>
              <el-popover
                :disabled="Number(si.amt).toFixed(2).length<10"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                v-if="!$employeeAuth('purchase_price')"
                :content="Number(si.amt).toFixed(2) + ''">
                <div slot="reference" class="psc_det18" style="width: 95px;height: 16px;text-align: right;margin-left: 0;">¥ {{Number(si.amt).toFixed(2)}}</div>
              </el-popover>
            </div>
          </div>
          <div style="height: 10px;margin-left: 20px;width: 900px;border-bottom: 1px dashed #DCDCDC;"></div>
          <div class="psc_det21">
            <div>收银员：{{detail_info.employeeNumber}}</div>
            <div :title="detail_info.supplierName">供应商：{{detail_info.supplierName}}</div>
          </div>
          <div class="psc_det21">
            <div>账户：{{detail_info.acctName}}</div>
            <div>合计金额：
              <span v-if="!$employeeAuth('purchase_price')">¥ {{Number(detail_info.billAmt).toFixed(2)}}</span>
              <span v-else>-</span>
            </div>
          </div>
          <div class="psc_det21">
            <div>折扣率：{{Number(detail_info.disc).toFixed(2)}}%</div>
            <div>本单实付：
              <span v-if="!$employeeAuth('purchase_price')">¥ {{Number(detail_info.payAmt).toFixed(2)}}</span>
              <span v-else>-</span></div>
          </div>
          <div class="psc_det21">
            <div>说明：{{detail_info.remark === '' ? '-' : detail_info.remark}}</div>
          </div>
        </div>
        <!--盘点单详情-->
        <div v-show="tableName === 'inventories'">
          <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-top: 30px;">
            <div class="psc_det18" style="width: 50px;font-weight: bold;margin-left: 0;text-align: center;">序号</div>
            <div class="psc_det18" style="width: 270px;font-weight: bold;text-align: left;margin-left: 12px;">名称</div>
            <div class="psc_det18" style="width: 90px;font-weight: bold;text-align: left;margin-left: 0;" v-if="!$employeeAuth('purchase_price')">进货价</div>
            <div class="psc_det18" style="width: 112px;font-weight: bold;text-align: left;margin-left: 55px;">盘前库存</div>
             <div class="psc_det18" style="width: 70px;font-weight: bold;text-align: left;margin-left: 0;">盘后库存</div>
            <div class="psc_det18" style="width: 90px;font-weight: bold;text-align: left;margin-left: 55px;">盈亏数</div>
            <div class="psc_det18" style="width: 112px;font-weight: bold;text-align: left;margin-left: 0;" v-if="!$employeeAuth('purchase_price')">盈亏金额</div>
          </div>
          <div style="height: 290px;overflow: scroll;margin-top: 30px;">
            <div style="line-height: 16px;overflow: hidden;margin-left: 20px;margin-bottom: 30px;" v-for="(si, index) in detail_list" :key="index">
              <div class="psc_det18" style="width: 50px;height: 16px;margin-left: 0;text-align: center;">{{index + 1}}</div>
              <div class="psc_det18" style="width: 270px;height: 16px;text-align: left;margin-left: 12px;">{{si.name}}</div>
              <div class="psc_det18" style="width: 90px;height: 16px;text-align: left;margin-left: 0;"
                v-if="!$employeeAuth('purchase_price')">¥ {{$toDecimalFormat(si.price, 6)}}</div>
              <div class="psc_det18" style="width: 112px;height: 16px;text-align: left;margin-left: 55px;">{{si.accountQty}}</div>
               <el-popover
                :disabled="(si.actualQty+'').length<8"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="si.actualQty + ''">
                <div slot="reference" class="psc_det18" style="width: 70px;height: 16px;text-align: left;margin-left: 0;">{{si.actualQty}}</div>
              </el-popover>
              <el-popover
                :disabled="(si.diffQty+'').length<10"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="si.diffQty + ''">
                <div slot="reference" class="psc_det18" style="width: 95px;height: 16px;text-align: left;margin-left: 55px;">{{si.diffQty}}</div>
              </el-popover>
              <el-popover
                v-if="!$employeeAuth('purchase_price')"
                :disabled="(Number(si.diffAmt).toFixed(2)+'').length<10"
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="hover"
                :content="Number(si.diffAmt).toFixed(2) + ''">
                <div slot="reference" class="psc_det18" style="width: 95px;height: 16px;text-align: left;margin-left: 0;">¥ {{Number(si.diffAmt).toFixed(2)}}</div>
              </el-popover>
            </div>
          </div>
          <div style="height: 10px;margin-left: 20px;width: 900px;border-bottom: 1px dashed #DCDCDC;"></div>
          <div class="psc_det21">
            <div>收银员：{{detail_info.employeeNumber}}</div>
          </div>
          <div class="psc_det21">
            <div>盘前库存总数：{{detail_info.accountQty}}</div>
            <div>盘后库存总数：{{detail_info.actualQty}}</div>
            <div>盈亏总数：{{detail_info.diffQty}}</div>
          </div>
          <div class="psc_det21">
            <div>商品总盈亏金额：
              <span v-if="!$employeeAuth('purchase_price')">¥ {{Number(detail_info.diffAmt).toFixed(2)}}</span>
              <span v-else>-</span>
            </div>
          </div>
          <div class="psc_det21">
            <div style="width: 100%;padding-right: 20px;">
              说明：{{detail_info.remark === '' ? '-' : detail_info.remark}}
            </div>
          </div>
        </div>
        <div style="height: 30px;"></div>
      </div>
    </div>
    <div class="top">
      <div class="top_left_container">
        <div class="psc_det1" @click="showGoodsList()" :style="alertKeyword === '商品名称/条码/首字母/扫码' ? 'color: #B1C3CD' : 'color: #567485;'">
          <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 254px;margin-left: 23px;">{{alertKeyword}}</div>
          <i v-if="alertKeyword !== '商品名称/条码/首字母/扫码'" class="el-icon-error" @click.stop="chooseClear"></i>
        </div>
        <div
          class="date_picker_container"
          :class="focusDate ? 'focusDate' : 'focusDate1'"
        >
          <el-date-picker
            v-model="fromDate"
            type="date"
            placeholder="开始日期"
            style="height:44px"
            @focus="setfocusDate()"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
          <div style="font-size: 16px;color: #567485">至</div>
          <el-date-picker
            v-model="toDate"
            type="date"
            placeholder="结束日期"
            style="height:44px"
            @focus="setfocusDate()"
            @blur="focusDate = false"
            value-format='yyyy-MM-dd'
          >
          </el-date-picker>
        </div>
        <div class="top_left">
          <el-select
            v-model="type"
            placeholder="请选择"
            style="margin-left:10px;width:130px"
          >
            <el-option
              v-for="tp in type_list"
              :key="tp.value"
              :label="tp.label"
              :value="tp.value"
            >
            </el-option>
          </el-select>
        </div>
        <div @click="getPDList('查询')" class="psc_det">查询</div>
      </div>
      <div class="top_right">
        <div class="btn_export_excel" @click="getPDList('导出表格')">导出表格</div>
      </div>
    </div>
    <div class="table_container">
      <el-table :data="tableData" :empty-text="!loading ? '暂无数据' : ' '" :height="tableHeight" stripe @sort-change="sortChange">
        <el-table-column v-if="!singleGoods" prop="name" sortable="custom" show-overflow-tooltip label="商品名称" align="left" min-width="19%"></el-table-column>
        <el-table-column prop="createAt" label="操作时间" :sortable="singleGoods ? false : 'custom'" align="left" min-width="19%"></el-table-column>
        <el-table-column label="变动类型" align="left" min-width="12%">
          <template slot-scope="scope">
            <div style="text-indent: 4px;">{{scope.row.changeType}}</div>
          </template>
        </el-table-column>
        <el-table-column label="库存变动" align="right" min-width="12%">
          <template slot-scope="scope">
            <div style="text-indent: 15px;">{{scope.row.changeType === '初始库存' ? scope.row.qty : (Number(scope.row.qty) > 0 ? '+' + scope.row.qty : scope.row.qty)}}</div>
          </template>
        </el-table-column>
        <el-table-column label="说明" align="center" :min-width="singleGoods ? '57%' : '38%'">
          <template slot-scope="scope">
            {{scope.row.remark}}
            <span @click="toShowDetail(scope.row)" style="margin-left: 18px;color: #d5aa76;cursor:pointer;
              font-family: Microsoft YaHei, sans-serif;" v-show="scope.row.remark.indexOf('销售单号') > -1
               || scope.row.remark.indexOf('退款单号') > -1 || scope.row.remark.indexOf('进货单号') > -1
               || scope.row.remark.indexOf('盘点单号') > -1 || scope.row.remark.indexOf('退货单号') > -1">
              详情>>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="psc_det22">
        <template v-if="singleGoods">
          <div class="psc_det2">
            <div style="width: 40%;">商品信息：<span>{{totalInfo.name || ''}}</span></div>
            <div>商品销售：<span>{{`${Number(totalInfo.salesCount) > 0 ? '+' : ''}`}}{{totalInfo.salesCount}}</span></div>
            <div>进货/退货：<span>{{`${Number(totalInfo.pursAndBackCount) > 0 ? '+' : ''}${$toDecimalFormat(totalInfo.pursAndBackCount, 3)}`}}</span></div>
            <div>兑换礼品：<span>{{totalInfo.exchangeCount}}</span></div>
          </div>
          <div class="psc_det2">
            <div style="width: 40%;">现有库存：<span>{{$toDecimalFormat(totalInfo.curStock, 3, true)}}</span></div>
            <div>会员存取：<span>{{`${Number(totalInfo.accessCount) > 0 ? '+' : ''}`}}{{totalInfo.accessCount}}</span></div>
            <div>客户退款：<span style="margin-left:7px">{{`${Number(totalInfo.salesBackCount) > 0 ? '+' : ''}`}}{{totalInfo.salesBackCount}}</span></div>
            <div>库存盘点：<span>{{`${Number(totalInfo.invsCount) > 0 ? '+' : ''}`}}{{totalInfo.invsCount}}</span></div>
          </div>
        </template>
        <template v-else>
          <div class="psc_table_bottom">
            <div>共<span>{{pageTotal}}</span>条记录</div>
            <el-pagination
              :key="pageKey"
              layout="prev, pager, next, slot"
              :total="pageTotal"
              @current-change="pageCountHandler"
              :current-page="pageNum"
              :page-size="pageSize"
            >
              <!-- slot -->
              <vCjPageSize
                @sizeChange="handleSizeChange"
                :pageSize.sync="pageSize"
                :currentPage.sync="pageNum"
                :pageKey.sync="pageKey">
              </vCjPageSize>
            </el-pagination>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Pagination } from 'element-ui';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';
export default {
  components: {
    [Pagination.name]: Pagination,
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      singleGoods: false,
      tableHeight: document.body.clientHeight - 180,
      pageTotal: 0,
      goodsPageKey: 0,
      pageKey: 0,
      pageNum: 1,
      pageSize: 10,
      pageSort: {},
      alertKeyword: '商品名称/条码/首字母/扫码',
      keyword: '',
      inputing_keyword: false,
      pinyin: false,
      can_getGoodsList: true,
      goods_list: [],
      addmoney: 0,
      givemoney: 0,
      zfList: [
        {
          value: '0',
          label: '全部'
        }, {
          value: '1',
          label: '线下支付'
        },
        {
          value: '2',
          label: '扫码付'
        }
      ],
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      focusDate: false,
      type_list: [
        {value: 0, label: '全部变动'},
        {value: 1, label: '商品销售'},
        {value: 2, label: '客户退款'},
        {value: 3, label: '进货'},
        {value: 4, label: '退货'},
        {value: 5, label: '库存盘点'},
        {value: 6, label: '会员存取'},
        {value: 7, label: '兑换礼品'},
        {value: 8, label: '拆包'},
        {value: 10, label: '库存修改'}
      ],
      type: 0,
      tableData: [],
      show_goods: false,
      show_detail: false,
      title: '',
      detail_list: [],
      detail_info: {},
      totalInfo: {},
      currentPage: 1,
      size: 10,
      total: 0,
      tempGoodsIndex: '', // 选中商品的临时下标
      tempGoodsName: '', // 选中商品的临时名称
      tempGoodsId: '', // 选中的临时商品id
      goodsId: '', // 商品id
      showClearIcon: false // 控制输入框的删除图标显示与否
    };
  },
  created () {
    this.fromDate = new Date().format('yyyy-MM-dd');
    this.toDate = new Date().format('yyyy-MM-dd');
    demo.actionLog(logList.clickStockChangeDetail);
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
  },
  watch: {
    alertKeyword() {
      if (this.alertKeyword === '') {
        this.alertKeyword = '商品名称/条码/首字母/扫码';
      }
    },
    singleGoods() {
      this.tableHeight = document.body.clientHeight - (this.singleGoods ? 226 : 180);
    },
    keyword() {
      this.showClearIcon = this.keyword.length > 0;
      if (this.show_goods === false) {
        return;
      }
      this.can_getGoodsList = false;
      var that = this;
      setTimeout(function() {
        that.can_getGoodsList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.goods_list = [];
        that.currentPage = 1;
        that.getGoodsList();
      }, that.delayedTime);
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 点击页码
     */
    handleCurrentChange (currentPage) {
      this.currentPage = currentPage;
      this.getGoodsList();
    },
    goodsSizeChange() {
      this.getGoodsList();
    },
    pageCountHandler(num) {
      this.pageNum = num;
      this.getPDList('查询');
    },
    handleSizeChange() {
      this.getPDList('查询');
    },
    sortChange(e) {
      this.pageNum = 1;
      this.pageSort = e;
      this.getPDList('查询');
    },
    listenResize() {
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        this.tableHeight = document.body.clientHeight - (this.singleGoods ? 226 : 180);
        // this.pageSize = parseInt((this.tableHeight - 54) / 50);
        // 查询所有商品时，窗口尺寸发生变化后计算当前页数
        // if (!this.singleGoods) {
        //   let lastPageNum = Math.ceil(this.pageTotal / this.pageSize);
        //   if (lastPageNum < this.pageNum) {
        //     this.pageNum = lastPageNum;
        //   }
        // }
        this.getPDList('查询');
      }, 0);
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    chooseClear() {
      this.tableData = [];
      this.totalInfo = {};
      this.pageTotal = 0;
      this.pageNum = 1;
      this.tempGoodsIndex = '';
      this.tempGoodsName = '';
      this.tempGoodsId = '';
      this.goodsId = '';
      this.alertKeyword = '';
      this.singleGoods = false;
    },
    setfocusDate() {
      var that = this;
      setTimeout(function() {
        that.focusDate = true;
      }, 200);
    },
    toShowDetail(row) {
      demo.actionLog(logList.clickStockChangeCheckDetail);
      this.show_detail = true;
      this.title = row.remark;
      this.tableName = row.tableName;
      var params = {
        'tableName': row.tableName,
        'id': row.id
      };
      var that = this;
      inventoryService.getInfoById(params, function(res) {
        console.log(res, 1110000);
        if (that.tableName === 'sales') {
          that.detail_list = res.sale_items;
          that.detail_info = res.sales;
        } else if (that.tableName === 'purchases') {
          that.detail_list = res.pur_items;
          that.detail_info = res.pur;
        } else if (that.tableName === 'inventories') {
          that.detail_list = res.inv_items;
          that.detail_info = res.inv;
        } else {
          console.log(that.tableName);
        }
      });
    },
    /**
     * 获取本地商品列表
     */
    getGoodsList() {
      if (this.pinyin) {
        return;
      }
      goodService.goodsSelectByName({'name': this.keyword, 'pageSize': this.size, 'page': this.currentPage}, res => {
        console.log(res.goods, 'res.goodsres.goods');
        this.goods_list = res.goods;
        this.total = Number(res.count);
      });
    },
    getPDList(str) {
      if (str === '选择') {
        this.tableData = [];
        this.totalInfo = {};
        this.pageTotal = 0;
        if (this.tempGoodsIndex === '') {
          demo.msg('warning', '请选择一条商品');
          return;
        }
        this.goodsId = this.tempGoodsId;
        this.alertKeyword = this.tempGoodsName;
        this.singleGoods = true;
        this.show_goods = false;
        return;
      }
      if (str === '查询') {
        if (this.fromDate === null) {
          demo.msg('warning', '请选择开始日期');
          return;
        } else if (this.toDate === null) {
          demo.msg('warning', '请选择结束日期');
          return;
        } else if (this.fromDate > this.toDate) {
          var mid_date = this.toDate;
          this.toDate = this.fromDate;
          this.fromDate = mid_date;
        } else {
          // nothing to do
        }
      }
      this.loading = true;
      var sub_data = {
        'goodId': this.goodsId,
        'dateFrom': this.fromDate,
        'dateTo': this.toDate,
        'type': this.type,
        ...(this.singleGoods ? {} : {
          'pageCount': str === '导出表格' ? 100000 : this.pageSize,
          'pageSize': str === '导出表格' ? 1 : this.pageNum,
          'order': !this.pageSort.order ? null : (this.pageSort.order === 'ascending' ? 'asc' : 'desc'),
          'column': this.pageSort.prop || null
        })
      };
      this.parseResult(sub_data, str);
    },
    /**
     * sonar对应问题
     */
    parseResult(sub_data, str) {
      var that = this;
      // this.reportFormLog(_.cloneDeep(sub_data), str === '查询' ? '变动明细查询' : '变动明细导出表格');
      console.log(sub_data, 'inventoryService.stockChangeReports params:');
      inventoryService.stockChangeReports(sub_data, function(res) {
        setTimeout(() => {
          that.loading = false;
        }, that.delayedTime);
        console.log('res.datas', res.datas);
        if (str === '查询') {
          if (that.singleGoods) { // 查询单个商品
            that.tableData = res.datas;
            that.totalInfo = res.totalInfo;
          } else { // 未指定查询商品
            that.tableData = res.datas.data || [];
            that.totalInfo = {};
            that.pageTotal = res.datas.count || 0;
          }
          console.log(res);
        } else if (str === '导出表格') {
          demo.actionLog(logList.clickStockChangeDetailExportExcel);
          let dataArr = that.singleGoods ? res.datas : res.datas.data;
          if (dataArr.length > 0) {
            var field_mapping = {
              ...(that.singleGoods ? {} : { 商品名称: 'name' }),
              操作时间: 'createAt',
              变动类型: 'changeType',
              库存变动: 'qty',
              说明: 'remark'
            };
            that.$makeExcel(dataArr, field_mapping, '库存变动明细' + new Date().format('yyyyMMddhhmmss'));
          } else {
            demo.msg('warning', '暂无符合条件数据，请重新选择条件');
          }
        } else {
          // nothing to do
        }
      }, function() {
        setTimeout(() => {
          that.loading = false;
        }, that.delayedTime);
      });
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_change_detail', action: 'reportFormLog', description});
      }
    },
    showGoodsList() {
      this.keyword = '';
      this.goods_list = [];
      this.currentPage = 1;
      this.getGoodsList();
      this.tempGoodsIndex = '';
      this.show_goods = true;
      setTimeout(() => {
        $('#goods_keyword').focus();
      }, 100);
    },
    formatFloat (f, digit) {
      if (isNaN(f)) {
        return '';
      }
      var m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.listenResize);
  },
  computed: {
    ...mapState({
      sysUid: state => state.show.sys_uid,
      sysSid: state => state.show.sys_sid,
      phone: state => state.show.phone,
      delayedTime: state => state.show.delayedTime,
      loginInfo: state => state.show.loginInfo
    })
  }
};
</script>

<style lang='less' scoped>
#butBackground {
  background: @themeBackGroundColor;
}
#butColor {
  color: @themeBackGroundColor;
}
.focusDate {
  border-color: @themeBackGroundColor;
}
.focusDate1 {
  border-color: #e3e6eb;
}
.psc_det {
  width: 100px;height: 44px;background: @themeBackGroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: 700;border-radius: 22px;margin-left: 10px;cursor: pointer;
}
.psc_det1 {
  font-size: 16px;width: 300px;height: 44px;border-radius: 22px;color: @themeFontColor;line-height: 44px;
  border: 1px solid #E3E6EB;background: #FFF;cursor: pointer;position: relative;
  i {
    position: absolute;
    font-size: 20px;
    top: 12px;
    right: 10px;
    color: #c8c9cc;
  }
}
.psc_det11{
 background: rgba(0,0,0,.5);width: 100%;height: 100%;position:absolute;top: -50px;left: 0;z-index: 100;
}
.psc_det12{
  width: 700px;margin: 0 auto;margin-top: 40px;background: #FFF;border-radius: 6px;color: @themeFontColor;overflow: hidden;
}
.psc_det13{
  line-height: 18px;margin-top: 20px;margin-left: 20px;font-weight: bold;font-size: 18px;overflow: hidden;width: 660px;
}
.psc_det14{
  float: right;font-size: 35px;font-weight: normal;color: #8197A6;margin-top: -2px;cursor: pointer;
}
.psc_det15{
  width: 660px;margin-left: 20px;margin-top: 20px;height: 44px;border: 1px solid #E3E6EB;border-radius: 4px;
  position: relative;
}
.psc_det15 input {
  width: 600px;font-size: 16px;margin-top: 8px;margin-left: 18px;border: none;
}
.deleteIcon {
  width:18px;height:18px;position:absolute;right:20px;top: 12px;cursor: pointer;
}
.psc_det16{
  margin: 0 auto;margin-top: 15px;width: 660px;height: 456px;border: 1px solid #E5E8EC;font-size: 16px;position: relative;
}
.psc_det17{
  height: 50px;background: #FAFBFC;line-height: 50px;overflow:hidden;
}
.psc_det_content {
  height: 352px;
  overflow-y: auto;
}
.flex {
  height: 352px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  .empty {
    width: 100%;
    text-align: center;
    color: #B2C3CD;
    font-size: 16px;
    font-weight: normal;
    line-height: 42px;
  }
}
.page-content {
  margin: 10px 0;
  float: right;
}
.psc_det171{
  height: 50px;background: #FAFBFC;line-height: 50px;overflow:hidden;
}
.psc_det18{
  width: 278px;margin-left: 20px;float: left;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;
}
.psc_det19 {
  width: 120px;height: 44px;border: 1px solid @themeBackGroundColor;border-radius: 4px;float: right;
  text-align: center;line-height: 42px;margin-right: 20px;margin-top: 20px;font-size: 16px;cursor: pointer;
}
.psc_det2 {
  height: 40px;overflow: hidden;padding-left: 20px;padding-top: 22px;
}
.psc_det2 div {
  width: 20%;float: left;font-size: 16px;color: @themeFontColor;line-height: 16px;
}
.psc_det2 div span {
  color: @themeBackGroundColor;
}
.psc_table_bottom {
  height: 54px;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  color: @themeFontColor;
  span {
    color: @themeBackGroundColor;
  }
}
.psc_det21 {
  line-height: 16px;margin-top: 25px;overflow: hidden;font-weight: bold;margin-left: 30px;
}
.psc_det21 div {
  float: left;width: 295px;text-overflow: ellipsis;white-space: nowrap;overflow: hidden;
}

.psc_det22 {
  width: 100%;border-top: 1px solid #E3E6EB;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  font-size: 16px;
  padding-right: 0;
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  margin-left: 10px;
  display: flex;
  align-items: center;
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .el-input--suffix .el-input__inner {
  color: @themeFontColor;background: #FFF;
}
.deposit_record_container{
  position: relative;
  background: #F5F8FB;
  .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .top_left_container{
      display: flex;
      align-items: center;
      .top_left{
        display: flex;
        align-items: center;
      }
    }
    .top_right{
      display: flex;
      align-items: center;
      .btn_export_excel{
        width: 110px;
        height: 44px;
        line-height: 44px;
        background: @themeBackGroundColor;
        color: white;
        text-align: center;
        font-size: 18px;
        font-weight: 700;
        border-radius: 22px;
        cursor: pointer;
      }
    }
  }
  .table_container{
    height: calc(100% - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #FFF;
  }
}

</style>
