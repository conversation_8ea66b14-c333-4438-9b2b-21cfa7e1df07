<template>
  <div class="deposit_record_container" v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="top">
      <div class="top_left_container">
        <div class='search_input_box'
        :style="inputing_keyword ? 'border-color: #d5aa76' : 'border-color: #e3e6eb;'"
        >
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder='请输入姓名/手机号/刷卡'
            :style="keyword ? 'color: #567485;' : 'color: #B1C3CD;'"
            id='search_input_keyword_deposit'
            v-model='keyword'
            @input="keyword = $vipNameFormat(keyword)"
          />
          <img
            alt=""
            class='search_input_delete'
            v-show="keyword != ''"
            @click="focusInput('search_input_keyword_deposit')"
            src='../image/pc_clear_input.png'
          />
        </div>
        <div class="top_left">
          <div
            class="date_picker_container"
            :style="focusDate ? 'border-color: #d5aa76' : 'border-color: #E3E6EB'"
            @click="focusDate = true"
          >
            <el-date-picker
              v-model="fromDate"
              type="date"
              placeholder="开始日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
              @change="getRecordDataByDate()"
            >
            </el-date-picker>
            <div style="font-size: 16px;color: #567485">至</div>
            <el-date-picker
              v-model="toDate"
              type="date"
              placeholder="结束日期"
              style="height:44px"
              @blur="focusDate = false"
              value-format='yyyy-MM-dd'
              @change="getRecordDataByDate()"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="top_left">
          <el-select
            v-model="deposit_value"
            placeholder="存取类型"
            style="width:130px;height:44px"
            @change="pagenum = 1;getRecordData()"
          >
            <el-option
              v-for="item in deposit_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="top_left">
          <el-select
            v-model="options_value"
            placeholder="收银员"
            style="width:130px;height:44px"
            @change="pagenum = 1;getRecordData()"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="btn_export_excel" @click="exportExcel">导出表格</div>
    </div>
    <div class="table_container">
      <el-table
        :data="tableData"
        :height="tableHeight"
        :empty-text="!loading ? '暂无数据' : ' '"
        stripe
      >
        <el-table-column
          show-overflow-tooltip
          prop="vipName"
          label="会员"
          align="left"
          min-width="11.8%"
        >
        </el-table-column>
        <el-table-column
          prop="vipMobile"
          label="手机号"
          align="center"
          min-width="11.8%"
        >
        </el-table-column>
        <el-table-column
          prop="inOutName"
          label="寄/取"
          align="center"
          min-width="11.8%"
        >
          <template slot-scope="scope">
            <div>{{scope.row.inOutName}}件</div>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="goodsName"
          label="商品名称"
          min-width="22%"
          align="left"
        >
        </el-table-column>
        <el-table-column
          prop="count"
          label="数量"
          align="center"
          min-width="11.8%"
        >
        </el-table-column>
        <el-table-column
          prop="createUser"
          label="收银员"
          align="left"
          min-width="10.8%"
        >
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="时间"
          align="center"
          min-width="20%"
        >
        </el-table-column>
      </el-table>
      <div class="table_bottom" style="color:#567485">
        <div>
          总有记录：<span>{{counts.totalCnt}}</span>，总寄件数：<span>{{counts.inSum}}</span>，总取件数：<span>{{counts.outSum}}</span>
        </div>
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="pagenum"
          :page-size="limit"
        >
        <!-- slot -->
          <vCjPageSize
            @sizeChange="handleSizeChange"
            :pageSize.sync="limit"
            :currentPage.sync="pagenum"
            :pageKey.sync="pageKey">
          </vCjPageSize>
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Input, Select, DatePicker, Table, TableColumn, Pagination } from 'element-ui';
import { showToast } from '@/utils/util.js';
import logList from '@/config/logList';
import vCjPageSize from '@/common/components/CjPageSize';

export default {
  components: {
    [Input.name]: Input,
    [Select.name]: Select,
    [DatePicker.name]: DatePicker,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Pagination.name]: Pagination,
    vCjPageSize
  },
  data () {
    return {
      loading: false,
      inputing_keyword: false,
      keyword: '',
      deposit_type: [
        {
          value: '0',
          label: '全部'
        }, {
          value: '1',
          label: '寄件'
        },
        {
          value: '2',
          label: '取件'
        }
      ],
      deposit_value: '', // 寄取类型
      options: [{
        value: '0',
        label: '全部'
      }],
      options_value: '', // 收银员uid
      users: [], // 管理员+店员
      focusDate: false,
      tableHeight: 0,
      tableData: [],
      counts: {},
      pageKey: 0,
      pagenum: 1,
      limit: 10,
      total: 0,
      timer: null,
      fingerprintArr: [],
      fromDate: '', // 开始日期
      toDate: '' // 结束日期
    };
  },
  created () {
    this.tableHeight = screen.availHeight - 200;
    // this.limit = Math.floor((this.tableHeight - 50) / 50);
    this.SET_SHOW({ cardNo: '' });
    demo.actionLog(logList.clickJiCunDetail);
  },
  mounted () {
    let today = new Date();
    this.toDate = today.format('yyyy-MM-dd');
    this.fromDate = today.format('yyyy-MM-dd');
    this.getShopUsersData();
    setTimeout(() => {
      $('#search_input_keyword_deposit').focus();
    }, 0);
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    /**
     * 点击页码
     */
    handleCurrentChange (pagenum) {
      this.pagenum = pagenum;
      this.getRecordData();
    },
    handleSizeChange() {
      this.getRecordData();
    },
    getRecordDataByDate () {
      this.pagenum = 1;
      let fromCheck = this.fromDate !== null && this.fromDate !== '';
      let toCheck = this.toDate !== null && this.toDate !== '';
      if (fromCheck && toCheck && this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      }
      if (fromCheck && toCheck) {
        this.getRecordData();
      }
      if (!fromCheck && !toCheck) {
        this.getRecordData();
      }
    },
    /**
     * 获取列表数据
     */
    getRecordData (exportExcel) {
      if (exportExcel) {
        demo.actionLog(logList.clickJiCunDetailExportExcel);
      }
      this.loading = true;
      const param = this.getRecordDataParam(exportExcel);
      console.log('==寄存明细参数', param);
      demo.$http
        .post(this.$rest.getAccessHistoryReports, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
          const res = rs.data;
          if (exportExcel && res.data.accessHistories.length === 0) {
            demo.msg('warning', '暂无可导出的数据');
            return;
          }
          // console.log('==寄存明细', res);
          if (res.code === '0') {
            this.counts = res.data.counts;
            this.total = this.counts.totalCnt;
            if (res.data.accessHistories.length === 0 && !exportExcel) {
              this.tableData = [];
              return;
            }
            // 根据fingerprint数组去查询对应的商品
            this.fingerprintArr = [];
            this.sonarRecord(res);
            this.getGoodsByFingerprint(res.data.accessHistories, exportExcel);
            return;
          }
          showToast(this, res.msg);
        }).catch(() => {
          setTimeout(() => {
            this.loading = false;
          }, this.delayedTime);
        });
    },
    getRecordDataParam(exportExcel) {
      return {
        systemName: $config.systemName,
        phone: this.sysUid,
        sysSid: this.sysSid,
        searchStr: this.keyword,
        inOut: this.deposit_value === '0' || this.deposit_value === '' ? null : Number(this.deposit_value),
        createUser: this.options_value === '0' || this.options_value === '' ? null : this.options_value,
        dateFrom: this.fromDate,
        dateTo: this.toDate,
        currentPage: exportExcel ? 1 : this.pagenum,
        pageSize: exportExcel ? 65535 : this.limit
      };
    },
    sonarRecord(res) {
      for (let item of res.data.accessHistories) {
        this.fingerprintArr.push(item.fingerprint);
      }
    },
    judgeEmployeeNumber(employeeNumber) {
      if (employeeNumber) {
        return employeeNumber;
      }
      var employeeName = '管理员';
      return employeeName;
    },
    searchNmuber(arr, exportExcel) {
      let that = this;
      for (let i = 0; i < arr.length; i++) {
        for (let n of this.users) {
          if (+arr[i].createUser === +n.uid) {
            arr[i].createUser = that.judgeEmployeeNumber(n.employeeNumber);
            break;
          }
        }
        this.excelSonar(exportExcel, arr, i);
      }
    },
    /**
     * 根据fingerprint查询对应的商品
     */
    getGoodsByFingerprint (arr, exportExcel) {
      return new Promise(() => {
        let param = {
          pset: '',
          type: '',
          fingerprint: this.fingerprintArr,
          fingerprintFlg: false,
          selectDel: true,
          vipGoods: true,
          limit: 65535,
          offset: 0
        };
        console.log('查询参数===》', param);
        goodService.search(param, res => {
          let searchGoodsList = demo.t2json(res);
          for (let arrItem of arr) {
            for (let searchItem of searchGoodsList) {
              if (arrItem.fingerprint === searchItem.fingerprint) {
                arrItem.specs = searchItem.specs;
                arrItem.goodsName = searchItem.name;
                break;
              }
            }
          }
          // 根据uid再去查询工号
          this.searchNmuber(arr, exportExcel);
        });
      });
    },
    excelSonar(exportExcel, arr, i) {
      if (i === arr.length - 1) {
        // 导出Excel
        if (exportExcel) {
          this.resolveSonar(arr);
          return;
        }
        this.tableData = arr;
        console.log('===整合后的列表数据', this.tableData);
      }
    },
    /**
     * 降低复杂度
     */
    resolveSonar(arr) {
      if (arr && arr.length > 0) {
        let field_mapping = {
          会员: 'vipName',
          手机号: 'vipMobile',
          '寄/取': 'inOutName',
          商品名称: 'goodsName',
          数量: 'count',
          收银员: 'createUser',
          时间: 'createTime'
        };
        this.$makeExcel(arr, field_mapping, '寄存明细' + new Date().format('yyyyMMddhhmmss'));
        return;
      }
      demo.msg('warning', '暂无可导出的数据');
    },
    /**
     * 获取管理员+店员数据
     */
    getShopUsersData () {
      clerkService.selectAll(res => {
        this.users = demo.t2json(res);
        for (let user of this.users) {
          this.options.push({
            value: user.uid + '',
            label: user.employeeNumber ? user.employeeNumber : '管理员'
          });
        }
        this.getRecordData();
      });
    },
    /**
     * 导出Excel
     */
    exportExcel() {
      this.getRecordData(true);
    }
  },
  watch: {
    /**
     * 监听输入框内容变化
     */
    keyword () {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.pagenum = 1;
        this.getRecordData();
      }, 500);
    },
    cardNo () {
      if (this.cardNo === '') {
        return;
      }
      this.keyword = this.cardNo;
      this.SET_SHOW({ cardNo: '' });
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    delayedTime: state => state.show.delayedTime,
    cardNo: (state) => state.show.cardNo
  })
};
</script>

<style lang='less' scoped>
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  font-size: 16px;
  padding-left: 23px;
}
/deep/ .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
/deep/ .el-range-editor.el-input__inner {
  border-radius: 50px;
}
/deep/ .el-date-editor .el-range__icon {
  display: none;
}
/deep/ .el-range-editor .el-range-input {
  margin-left: 12px;
}
/deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}
/deep/ .el-date-editor .el-range-separator {
  color: rgba(177, 195, 205, 100);
}
/deep/ .el-date-table td.today span {
  color: @themeBackGroundColor !important;
}
/deep/ .el-table__row > td {
  border: none;
}
/deep/ .el-table::before {
  height: 0px;
}
/deep/ .el-table th, .el-table tr {
  height: 50px;
  font-size: 16px;
  background: #F5F7FA;
}
/deep/ .el-table__row > td {
  height: 50px;
  font-size: 16px;
}
/deep/ .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
/deep/ .el-table__footer-wrapper {
  font-weight: bold;
  font-size: 16px;
}
/deep/ .el-table th > .cell {
  padding-left: 30px;
}
/deep/ .el-table__row > td {
  padding-left: 20px;
}
/deep/ .el-table td, .el-table th.is-leaf {
  padding-left: 20px;
}
/deep/ .el-input__inner {
  border-radius: 50px;
}
/deep/ .top_left .el-input__inner {
  color: #C0C4CC;
}
.deposit_record_container{
  background: #F5F8FB;
  .top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 10px;
    .top_left_container{
      display: flex;
      align-items: center;
      .top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
        .top_left_title{
          color: @themeFontColor;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .btn_export_excel{
      cursor: pointer;
      width: 110px;
      height: 44px;
      line-height: 44px;
      margin-right: 10px;
      font-weight: bold;
      border-radius: 22px;
      text-align: center;
      color: #FFFFFF;
      font-size: 18px;
      background: @themeBackGroundColor;
    }
    .top_right{
      display: flex;
      align-items: center;
      .top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
      .table_bottom{
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 16px;
        padding-left: 10px;
        padding-right: 13px;
        background: white;
        span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.date_picker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 50px;
  display: flex;
  align-items: center;
}
.table_name_code_column{
  .p_name {
    height: 22px;
    margin-bottom: 10px;
    line-height: 36px;
    font-size: 16px;
  }
  .p_spec {
    height: 22px;
    margin-bottom: 5px;
    line-height: 22px;
    font-size: 16px;
  }
}
/deep/ .date_picker_container .el-input--suffix {
  .el-input__inner{
    background: #FFFFFF;
    border:none;
    height: 42px;
    margin-top: 1px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
  .el-input__suffix {
    top: 2px;
  }
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none;
}
/deep/ .el-input--prefix .el-input__inner {
  padding-left: 0;
}
/deep/ .el-input--suffix .el-input__inner {
  border-radius: 50px;
  height: 44px;
  padding-right: 0;
}
/deep/ .el-table th > .cell {
  padding-left:10px;
  padding-right:0;
}
/deep/ .el-table td {
  padding-left:0;
  color:@themeFontColor;
}
/deep/ .el-table .cell {
  padding-left:10px;
  padding-right:0;
}
</style>
