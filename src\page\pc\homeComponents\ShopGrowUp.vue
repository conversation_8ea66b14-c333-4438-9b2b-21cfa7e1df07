<template>
  <div class="shopGrowUpWrap">
    <div class="header cjFlexBetween">
      <div class="title">商家成长</div>
      <div class="userTag">{{ loginInfo.employeeNumber || '管理员' }}</div>
    </div>
    <div class="infoItem cjFlexBetween">
      <div>本次登录</div>
      <div class="logTime">{{ loginTime }}</div>
    </div>
    <div class="infoItem cjFlexBetween">
      <div>本月累计销售</div>
      <div>
        <span class="saleDay">{{ saleDay }}</span>
        天
      </div>
    </div>
    <div class="infoItem cjFlexBetween">
      <div>短信剩余</div>
      <div>
        <span class="smsNumber">{{ offline? '-' : smsNumber }}</span>
        条
      </div>
      <div class="buySmsBtn" :class="{offlineSms: offline}"
        @click.stop="goShortMessage">去充值</div>
    </div>
    <div class="scanStatus cjFlexBetween">
      <el-tooltip popper-class="scanTips" placement="left" :visible-arrow="false">
        <div slot="content">
          <div v-for="item in scanTips" :key="item" class="tipsItem cjFlexRowCenter">
            <i class="el-icon-success"></i>
            <div>{{ item }}</div>
          </div>
        </div>
        <i class="cj-icon cj-icon-question" />
      </el-tooltip>
      <div v-if="offline"
        class="statusBtn offline cjFlexBetween">
        <img class="offlineIcon" src="../../../image/pc_offline.png">
        <div>无网络</div>
      </div>
      <div v-else-if="aid"
        class="statusBtn success cjFlexBetween">
        <i class="el-icon-circle-check"></i>
        <div>已开通</div>
      </div>
      <div v-else class="statusBtn fail cjFlexBetween"
        @click.stop="goScanPayIntro">
        <div>了解更多</div>
        <i class="el-icon-right"></i>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { getaid } from '@/api/pay';
import { getSelectSmsCount } from '@/api/sms';
export default {
  name: 'ShopGrowUp',
  data() {
    return {
      loginTime: '',
      saleDay: 0,
      smsNumber: 0,
      scanTips: [
        '语音播报，防逃单防抹零',
        '急速扫码付款，自动生成报表',
        '支持信用卡花呗，到账银行卡'
      ],
      show: true
    };
  },
  computed: {
    ...mapState({
      aid: state => state.show.aid,
      sysUid: (state) => state.show.sys_uid,
      loginInfo: state => state.show.loginInfo
    }),
    // 是否无网络
    offline() {
      return !pos.network.isConnected()
    }
  },
  mounted() {
    this.getSaleDays();
    setTimeout(() => {
      this.loginTime = $setting.userlastime;
    }, 0)
    if (pos.network.isConnected) {
      this.getSelectSmsCount();
      if (!this.aid) {
        this.getaid();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 查询当月销售天数
    async getSaleDays() {
      const res = await saleService.periodTimeSaleCount();
      this.saleDay = res[0].cnt || 0;
    },
    // 查询扫码付开通状态
    async getaid() {
      const params = {
        sysUid: this.sysUid,
        aid: 'pay'
      }
      const { code, data } = await getaid(params);
      if (code === '0' && data) {
        this.SET_SHOW({
          aid: data.aid,
          app_secret: data.appSecret,
          sysId: data.sysId
        })
      }
    },
    // 获取剩余短信条数
    async getSelectSmsCount() {
      const params = {
        paidServiceCode: '3',
        phone: this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName
      };
      const {code, data} = await getSelectSmsCount(params);
      if (code === 200) {
        this.smsNumber = data.residueCount || 0;
      }
    },
    // 短信弹窗弹出
    goShortMessage() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，请联网后查看');
        return;
      }
      this.SET_SHOW({
        isHome: false,
        msgFrom: 0,
        isShortMessage: true
      });
    },
    // 跳转扫码付介绍页面
    goScanPayIntro() {
      this.$router.push('scanPayIntro');
    }
  }
};
</script>
<style lang="less">
.scanTips {
  background: rgba(255, 240, 240, 1) !important;
  .tipsItem {
    background-color: rgba(255, 255, 255, 0.9);
    color: @warningRed;
    height: 23px;
    width: 203px;
    padding-left: 9px;
    border-radius: 4px;
    font-weight: bold;
    .el-icon-success {
      margin-right: 5px;
    }
  }
  .tipsItem:not(:first-child) {
    margin-top: 4px;
  }
}
</style>
<style lang="less" scoped>
.shopGrowUpWrap {
  height: 248px;
  border-radius: 12px;
  padding: 12.5px 12px 0;
  background-color: #ffffff;
  .header {
    height: 23px;
    line-height: 23px;
    .title {
      font-size: 20px;
      color: @homeColor;
      font-weight: bold;
    }
    .userTag {
      font-size: 14px;
      padding: 0 8px;
      border-radius: 6px;
      color: #648cbc;
      font-weight: bold;
      background-color: #f6f8f9;
    }
  }
  .infoItem {
    margin-top: 8px;
    padding: 0 8px;
    border-radius: 6px;
    background-color: #f6f8f9;
    font-size: 13px;
    height: 40px;
    .logTime {
      font-size: 12px;
      font-weight: bold;
      color: #648cbc;
    }
    .saleDay,
    .smsNumber {
      font-size: 24px;
      font-weight: bold;
      color: #ff7d00;
    }
    .buySmsBtn {
      border-radius: 50px;
      padding: 2px 8px;
      border: 1px solid @themeBackGroundColor;
      color: @themeBackGroundColor;
      font-size: 12px;
      cursor: pointer;
    }
    .offlineSms {
      border-color: @themeBackGroundColorLight;
      color: @themeBackGroundColorLight;
    }
  }
  .scanStatus {
    height: 40px;
    margin-top: 16px;
    background: url('../../../image/home/<USER>') no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
    padding: 0 8px 0 85px;
    .cj-icon-question {
      color: #ffffff;
    }
    .statusBtn {
      width: 72px;
      height: 24px;
      padding: 0 8px;
      background-color: #FFEBEE;
      border-radius: 50px;
      font-size: 12px;
      color: @warningRed;
    }
    .success {
      color: #47B881;
    }
    .fail {
      width: 84px;
      cursor: pointer;
    }
    .offline {
      color: #B2C3CD;
      .offlineIcon {
        width: 16px;
      }
    }
  }
}
</style>
