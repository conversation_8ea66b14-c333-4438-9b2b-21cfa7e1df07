<style lang="less" scoped>
/deep/.print {
  overflow: auto;
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
      -webkit-appearance: none;
  }
  input[type="number"]{
      -moz-appearance: textfield;
  }
  /deep/.el-input__inner {
    background: #f5f8fb;
    color: @themeFontColor;
    font-size: 16px;
  }
  /deep/.el-input.is-disabled .el-input__inner {
    color: #C0C4CC;
  }
  /deep/.el-select {
    width: 100%;
  }
  /deep/.el-textarea__inner {
    width: 498px;
    background: #F5F8FB;
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    height: 70px;
    font-size: 16px;
    line-height: 23px;
    color: @themeFontColor;
  }
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  color: @themeFontColor;
  .print_info {
    background: #fff;
    margin: 0 auto;
    position: relative;
    width: 602px;
    margin-top: 35px;
    border-radius: 10px;
    overflow: hidden;
    .print_main {
      height: 484px;
      overflow: auto;
      margin-top: 19px;
      width: 592px;
      padding: 0 62px 0 30px;
    }
    .pc_set_ads {
      height: 80%;
      width: 494px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: flex-start;
      align-content: flex-start;
    }
    .pc_set_ad:hover,
    .pc_set_ad:focus,
    .pc_set_ad:active {
      -webkit-transform: scale(1.2);
      transform: scale(1.2);
      -webkit-transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
      transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
    }
    .pc_set_ad {
      width: 18%;
      height: 19%;
      margin: 1%;
      font-size: 0.625vmin;
      white-space: nowrap;
      margin-left: 10px;
    }
    .pc_adv_duration {
      display: flex;
      justify-content: space-between;
      align-items: center;
      /deep/ .el-input-group__append, .el-input-group__prepend {
        color: #567485;
        background-color: #f5f8fb
      }
    }
    .pc_set_time_tips {
      color: @themeBackGroundColor;
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 4px;
    }
    .image_upload {
      float: left;
      padding-right: 12px;
      /deep/ .el-upload--picture-card{
        padding-top: 18px;
        border: 1px solid #B2C3CD;
        width: 160px;
        height: 100px;
        border-radius: 0;
        line-height: 20px;
      }
      /deep/.el-upload--picture-card:hover {
        color: #BDA169;
      }
    }
    .image_pre {
      float: left;
      margin-right: 12px;
      margin-top: 12px;
      cursor: pointer;
      width: 160px;
      height: 100px;
      position: relative;
    }
    .dialog_header_peijian{
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #E3E6EB;
      margin: 0 30px;
      .header_title{
        color: @themeFontColor;
        font-size: 18px;
        font-weight: bold;
        line-height: 60px;
      }
      .icon_close{
        font-size: 40px;
        color: #8298A6;
        cursor: pointer;
      }
    }
    .setting_font{
      width: 126px;
      display: inline-block;
      font-size: 16px;
      font-weight: bold;
      color: @themeFontColor;
      line-height: 54px;
    }
    .explain_font{
      display: inline-block;
      margin-left: 30px;
      font-size: 16px;
      color: @text;
      line-height: 54px;
    }
    .pc_set35 {
      float: left;
      padding: 12px 34px;
      font-size: 16px;
      border: 1px solid #e3e6eb;
      border-radius: 5px;
      line-height: 16px;
      background: #f5f8fb;
      margin-right: 20px;
      cursor: pointer;
      position: relative;
      margin-top: 0px;
    }
    .pc_set36 {
      border-color: @themeBackGroundColor;
      color: @themeBackGroundColor;
      background: #fff;
    }
    .settingHeight {
      color: @themeBackGroundColor;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      display: inline-block;
      cursor: pointer;
    }
    .aline{
      border-bottom: 1px solid #E3E6EB;
      margin-bottom: 20px;
    }
    .setting_print_font{
      width: 146px;
      display: inline-block;
      font-size: 16px;
      font-weight: 400;
      color: @themeFontColor;
      line-height: 55px;
      #remark {
        color: @text;
      }
      .com_pae16 {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: @themeBackGroundColor;
        text-align: center;
        color: #FFFFFF;
        line-height: 20px;
        font-size: 16px;
        margin-top: 10px;
        margin-left: 12px;
        cursor: pointer;
        display: inline-block;
      }
    }
    .setting_radio_main {
      display: block;
      float: right;
      width: 293px;
    }
    .setting_input{
      display: block;
      height: 40px;
      float: right;
      width: 348px;
      margin-top: 7px;
    }
    .setting_radio{
      width: 20px;
      height: 20px;
      background: #fff;
      border-radius: 50%;
      overflow: hidden;
      display: inline-block;
      position: relative;
      cursor: pointer;
      margin-bottom: -4px;
    }
    .setting_radio_inner{
      width: 12px;
      height: 12px;
      background: @themeBackGroundColor;
      margin-top: 2px;
      margin-left: 2px;
      border-radius: 50%;
      overflow: hidden;
      position: relative;
    }
    .setting_print_font2{
      margin-left: 14px;
      display: inline-block;
      font-size: 16px;
      font-weight: 400;
      color: @themeFontColor;
      line-height: 55px;
    }
    .setting_print_font_spacing{
      width: 80px;
      height: 55px;
      text-align: right;
      display: inline-block;
      font-size: 16px;
      font-weight: 400;
      color: @themeFontColor;
      line-height: 55px;
    }
    .printTestClass {
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      line-height: 30px;
      border-radius: 4px;
      cursor: pointer;
      color: @butFontColor;
      background: @themeBackGroundColor;
      width: 88px;
      height: 32px;
      border: 1px solid @themeBackGroundColor;
      display: inline-block;
    }
    .btn_container{
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      .btn{
        width: 120px;
        height: 44px;
        text-align: center;
        line-height: 34px;
        margin-right: 20px;
        border-radius: 4px;
        font-size: 18px;
        font-weight: 400;
      }
    }
    .reload_ad_btn {
      background-color: @themeBackGroundColor;
      border: 0px;
      border-radius: 4px;
      width: 90px;
      height: 26px;
      font-size: 16px;
      display: inline-block;
      color: white;
      text-align: center;
      line-height: 25px;
      cursor: pointer;
      font-weight: normal;
    }
  }
}
.yes {
  border-color: @themeBackGroundColor;
  border: 2px solid @themeBackGroundColor;
}
.no {
  border-color: #D2D5D9;
  border: 2px solid #D2D5D9;
}
#cancelBtn {
  border:1px solid @themeBackGroundColor;
  color:@themeBackGroundColor;
}
#saveBtn {
  background:@themeBackGroundColor;
  color:white;
  margin-right: 30px;
}
.moneyboxCheck {
  float: left;
  width: 80px;
  height: 42px;
  line-height: 40px;
  border: 1px solid @themeBackGroundColor;
  background: @themeBackGroundColor;
  color: #FFF;
  text-align: center;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;

}
/deep/.el-loading-spinner .path {
  stroke: @themeBackGroundColor !important;
}
.add-img {
  width: 160px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px solid #B2C3CD;
  cursor: pointer;
  margin-top: 12px;
}

.el-icon-plus {
  font-size: 24px;
}

.temp-1-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  background: gray;
  opacity: .6;
}

.temp-1-add-text {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  line-height: 28px;
  color: white;
  font-size: 14px;
  text-align: center;
  z-index: 9;
}

.maxline-1 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
}

.temp-2-mask {
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 70px;
  background: white;
  opacity: .7;
  border-radius: 50% 50%;
}

.temp-2-add-text {
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 70px;
  line-height: 70px;
  color: black;
  font-size: 14px;
  text-align: center;
  z-index: 9;
}
</style>
<style>
  @import "../ad/ad.css";
</style>
<template>
  <div v-show="isPrintSetting">
    <div class="print">
      <div class="print_info" v-loading="waitReloadAd">
          <div class="dialog_header_peijian">
          <div class="header_title">配件设置</div>
          <div
            class="icon_close"
            @click="closePaySetting"
          >×</div>
        </div>
        <div class="print_main" style="padding: 0 30px 0 30px;">
          <div v-show="!showAdvertIn">
            <div class="setting_font">启用副屏广告</div>
            <el-switch
              v-model="set_show_ad"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              @change="setShowAdChange"
            ></el-switch>
            <span style="margin-left: 10px" v-show="set_show_ad" class="explain_font">
              电脑端、手机端编辑完成后请点击 <div class="reload_ad_btn" @click="changeWaitReloadAd(true);openAd(true);;adSearch()">刷新广告</div>
            </span>
            <div v-show="set_show_ad">
              <div class="pc_adv_duration">
                <div class="setting_print_font">图片切换时间(秒)：</div>
                <el-input
                  v-model.number="adDuration"
                  style="width: 130px;"
                  @input="adDuration = $intMaxMinLimit({data: adDuration, max: 999, min: 0})"
                  @change="durationChange()">
                  <!-- <template slot="append">
                    秒
                  </template> -->
                </el-input>
              </div>
              <div class="pc_set_time_tips">
                  图片尺寸建议1920*1080，图片大小不超过3M
              </div>
              <div v-if="adsList.length < 10" class="image_upload">
                <div @click="adEdit()" class="add-img">
                  <i class="el-icon-plus"></i>
                  <div class="el-upload__text">上传广告<br />（{{ adsList.length }}/10）</div>
                </div>
              </div>
              <div
                class="image_pre"
                v-for="item in adsList"
                :key="item.id">
                <div @click="adEdit(item)">
                  <el-image
                    style="width: 160px; height: 100px"
                    :src="item.img"
                    :key="item.id"/>
                  <div v-if="item.templateId === 1" class="temp-1-mask"></div>
                  <div v-if="item.templateId === 1" class="temp-1-add-text maxline-1">{{item.comments}}</div>
                  <div v-if="item.templateId === 2" class="temp-2-mask"></div>
                  <div v-if="item.templateId === 2" class="temp-2-add-text maxline-1">{{item.comments}}</div>
                </div>
              </div>
            </div>
          </div>
          <div style="clear: both;">
            <div class="setting_font">启用钱箱</div>
            <el-switch
              v-model="has_moneybox"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
          </div>
          <div>
            <div
              v-show="has_moneybox"
              style="overflow: hidden;"
            >
              <div
                class="pc_set35"
                :class="moneybox_type === 1 ? 'pc_set36' : ''"
                @click="moneybox_type = 1"
              >
                小票打印机集成
                <img
                  alt=""
                  v-show="moneybox_type === 1"
                  src="../image/zgzn-pos/pc_pay_duigou.png"
                  style="position: absolute;right: 0;bottom: 0;"
                />
              </div>
              <!-- <div
                class="pc_set35"
                :class="moneybox_type === 2 ? 'pc_set36' : ''"
                @click="moneybox_type = 2"
              >
                选择端口
                <img
                  alt=""
                  v-show="moneybox_type === 2"
                  src="../image/zgzn-pos/pc_pay_duigou.png"
                  style="position: absolute;right: 0;bottom: 0;"
                />
              </div> -->
              <div
                @click="moneyboxCheck"
                class="moneyboxCheck"
              >检测</div>
              <br />
            </div>
            <div
              v-show="has_moneybox && moneybox_type === 2 ? true : false"
              style="margin-top: 15px;width: 182px;"
            >
              <el-select
                v-model="moneybox_value"
                placeholder="请选择"
              >
                <el-option
                  v-for="mb in moneybox"
                  :key="mb.label"
                  :label="mb.label"
                  :value="mb.label"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div>
            <div class="setting_font">启用读卡设备</div>
            <el-switch
              v-model="setEquip"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
          </div>
          <div class="setting_font" style="width: 402px;">小票打印设置</div>
          <div class="printTestClass" @click="printTest('small')">打印测试</div>
          <img :src="logAndCode[0].url" hidden>
          <img :src="logAndCode[1].url" hidden>
          <div class="aline"></div>
          <div>
            <div>
              <div class="setting_print_font">选择打印机</div>
              <div class="setting_input">
                <el-select
                  v-model="smallPrinterValue"
                  placeholder=""
                >
                  <el-option
                    v-for="pr in receiptPrinterList"
                    :key="pr.label"
                    :label="pr.label"
                    :value="pr.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div>
              <div class="setting_print_font">打印机驱动</div>
              <div class="setting_input" style="width: 160px">
                <el-select v-model="printMode">
                  <el-option
                    v-for="item in printDrives"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div>
              <div class="setting_print_font">默认打印份数</div>
              <div class="setting_input" style="width: 160px;">
                <el-input
                  v-model="smallPrinterCopies"
                  @blur="setNum('smallPrinterCopies')"
                  @input="smallPrinterCopies = $intMaxMinLimit({data: smallPrinterCopies, max: 99999, min: 0})"
                  placeholder="最大输入999"
                ></el-input>
              </div>
            </div>
            <div>
              <div class="setting_print_font">小票规格</div>
              <div style="display: inline-block;float: right;width: 243px;">
                <div style="float: left;">
                  <div
                    @click="printCols = '58'"
                    class="setting_radio"
                    :class="printCols === '58' ? 'yes' : 'no'"
                  >
                    <div v-show="printCols === '58'" class="setting_radio_inner"></div>
                  </div>
                  <div @click="printCols = '58'" class="setting_print_font2" style="cursor: pointer;">58mm</div>
                </div>
                <div style="float: right;">
                  <div
                    @click="printCols = '80'"
                    class="setting_radio"
                    style="margin-left: 75px;"
                    :class="printCols === '80' ? 'yes' : 'no'"
                  >
                    <div v-show="printCols === '80'" class="setting_radio_inner"></div>
                  </div>
                  <div @click="printCols = '80'" class="setting_print_font2" style="cursor: pointer;">80mm</div>
                </div>
              </div>
            </div>
            <!-- <div>
              <div class="setting_print_font">正文字号</div>
              <div class="setting_input" style="width: 160px">
                <el-select
                  v-model="font_value"
                >
                  <el-option
                    v-for="item in font_size_options"
                    :key="item.font_value"
                    :label="item.label"
                    :value="item.font_value"
                  >
                  </el-option>
                </el-select>
              </div>
            </div> -->
            <div>
              <div class="setting_print_font">行间距</div>
              <div class="setting_input" style="width: 160px">
                <el-select
                  v-model="line_height"
                >
                  <el-option
                    v-for="item in line_height_options"
                    :key="item.line_height"
                    :label="item.label"
                    :value="item.line_height"
                  >
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="setting_print_font">备注内容
              <span id="remark" style="font-family: Microsoft YaHei, sans-serif;margin-left: 10px;">
                ({{remark_content.length}}/100)
              </span>
            </div>
            <div>
              <div style="display: inline-block;">
                <el-input
                  type="textarea"
                  resize="none"
                  style="width: 528px;"
                  :rows="2"
                  placeholder="最多可输入100字..."
                  maxlength="100"
                  v-model="remark_content"
                />
              </div>
            </div>
            <div v-show="$employeeAuth('use_settings')"
              class="settingHeight" style="margin-top: 12px;" @click="gotoSetting('tab0')">个性化设置
              <span style="font-family: Microsoft YaHei, sans-serif;">></span>
            </div>
          </div>
          <div class="setting_font" style="width: 402px">条码打印设置</div>
          <div class="printTestClass" @click="printTest('label')">打印测试</div>
          <div class="aline"></div>
          <div>
            <div>
              <div class="setting_print_font">选择打印机</div>
              <div class="setting_input">
                <el-select
                  v-model="labelPrinterValue"
                  placeholder=""
                >
                  <el-option
                    v-for="pr in printerList"
                    :key="pr.label"
                    :label="pr.label"
                    :value="pr.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div>
              <div class="setting_print_font">打印份数</div>
              <div class="setting_input">
                <div class="setting_radio_main" style="display: inline-block;float: right;width: 348px;">
                  <div style="float: left;">
                    <div
                      @click="setLabelDefault = false"
                      class="setting_radio"
                      :class="!setLabelDefault ? 'yes' : 'no'"
                    >
                      <div v-show="!setLabelDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setLabelDefault = false" class="setting_print_font2"
                    style="cursor: pointer;">每次打印前询问</div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="setting_print_font">&nbsp;</div>
              <div class="setting_input">
                <div class="setting_radio_main" style="display: inline-block;float: right;width: 348px;">
                  <div style="float: left;">
                    <div
                      @click="setLabelDefault = true"
                      class="setting_radio"
                      :class="setLabelDefault ? 'yes' : 'no'"
                    >
                      <div v-show="setLabelDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setLabelDefault = true" class="setting_print_font2"
                    style="cursor: pointer;">默认打印份数</div>
                  </div>
                  <div style="float: right;">
                    <div class="setting_input" style="width: 160px;">
                      <el-input
                        :disabled="!setLabelDefault"
                        @blur="setNum('labelPrinterCopies')"
                        v-model="labelPrinterCopies"
                        @input="labelPrinterCopies = $intMaxMinLimit({data: labelPrinterCopies, max: 999, min: 0})"
                        placeholder="最大输入999"
                      ></el-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="setting_print_font">条码纸规格&nbsp;(宽*高)</div>
              <div class="setting_radio_main" style="display: inline-block;float: right;width: 348px;">
                <div style="display: flex;justify-content:space-between;">
                  <div>
                    <div
                      @click="print_cols_label = '64'"
                      class="setting_radio"
                      :class="print_cols_label === '64' ? 'yes' : 'no'"
                    >
                      <div v-show="print_cols_label === '64'" class="setting_radio_inner"></div>
                    </div>
                    <div @click="print_cols_label = '64'" style="cursor: pointer;margin-left: 3px;"
                      class="setting_print_font2">60*40mm</div>
                  </div>
                  <div>
                    <div
                      @click="print_cols_label = '60'"
                      class="setting_radio"
                      :class="print_cols_label === '60' ? 'yes' : 'no'"
                    >
                      <div v-show="print_cols_label === '60'" class="setting_radio_inner"></div>
                    </div>
                    <div @click="print_cols_label = '60'" style="cursor: pointer;"
                    class="setting_print_font2">40*60mm</div>
                  </div>
                  <div>
                    <div
                      @click="print_cols_label = '40'"
                      class="setting_radio"
                      :class="print_cols_label === '40' ? 'yes' : 'no'"
                    >
                      <div v-show="print_cols_label === '40'" class="setting_radio_inner"></div>
                    </div>
                    <div @click="print_cols_label = '40'" style="cursor: pointer;margin-left: 3px;"
                      class="setting_print_font2">40*30mm</div>
                  </div>
                </div>
              </div>
            </div>
            <div
              :key="item.key + 'pay'"
              v-show="index > 2"
              v-for="(item, index) in labelItem"
              >
              <div class="setting_print_font">{{item.title}}
                <el-popover
                  popper-class="pc_fip5"
                  placement="right"
                  width="320"
                  trigger="click"
                  :content="'在商品列表打印时输入' + item.title + '；此打印不支持40*30mm条码纸规格。'">
                  <div class="com_pae16" slot="reference">?</div>
                </el-popover>
              </div>
              <div class="setting_input" style="padding-top: 10px;">
                <el-switch
                  :disabled="print_cols_label === '40' ? true : false"
                  v-model="item.flag"
                  :active-color="$t('image.homeImage.color')"
                  inactive-color="#DCDFE6"
                  style="float: right;"
                ></el-switch>
              </div>
            </div>
            <div v-show="$employeeAuth('use_settings')"
              class="settingHeight" @click="gotoSetting('tab1')">个性化设置
              <span style="font-family: Microsoft YaHei, sans-serif;">></span>
            </div>
          </div>
          <!-- <div class="setting_font" style="width: 402px">吊牌打印设置</div>
          <div class="printTestClass" @click="printTest('tag')">打印测试</div>
          <div class="aline"></div>
          <div>
            <div>
              <div class="setting_print_font">选择打印机</div>
              <div class="setting_input">
                <el-select
                  v-model="tagPrinterValue"
                  placeholder=""
                >
                  <el-option
                    v-for="pr in printerList"
                    :key="pr.label"
                    :label="pr.label"
                    :value="pr.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div>
              <div class="setting_print_font">打印预览</div>
              <div class="setting_radio_main" style="display: inline-block;float: right;width: 293px;">
                <div style="float: left;">
                  <div
                    @click="tagIsTransverse = true"
                    class="setting_radio"
                    :style="tagIsTransverse ? 'border-color: #d5aa76;' : 'border-color: #D2D5D9;'"
                  >
                    <div v-show="tagIsTransverse" class="setting_radio_inner"></div>
                  </div>
                  <div @click="tagIsTransverse = true" style="cursor: pointer;" class="setting_print_font2">横向</div>
                </div>
                <div style="float: right;">
                  <div
                    @click="tagIsTransverse = false"
                    class="setting_radio"
                    :style="!tagIsTransverse ? 'border-color: #d5aa76;' : 'border-color: #D2D5D9;'"
                  >
                    <div v-show="!tagIsTransverse" class="setting_radio_inner"></div>
                  </div>
                  <div @click="tagIsTransverse = false" style="cursor: pointer;" class="setting_print_font2">竖向</div>
                </div>
              </div>
              <div class="setting_print_font">条码纸规格&nbsp;(宽*高)</div>
              <div class="setting_radio_main" style="display: inline-block;float: right;width: 293px;">
                <div style="float: left;">
                  <div
                    @click="tagPrintCols = '60'"
                    class="setting_radio"
                    :style="tagPrintCols === '60' ? 'border-color: #d5aa76;' : 'border-color: #D2D5D9;'"
                  >
                    <div v-show="tagPrintCols === '60'" class="setting_radio_inner"></div>
                  </div>
                  <div @click="tagPrintCols = '60'" style="cursor: pointer;" class="setting_print_font2">40*60mm</div>
                </div>
                <div style="float: right;">
                  <div
                    @click="tagPrintCols = '40'"
                    class="setting_radio"
                    :style="tagPrintCols === '40' ? 'border-color: #d5aa76;' : 'border-color: #D2D5D9;'"
                  >
                    <div v-show="tagPrintCols === '40'" class="setting_radio_inner"></div>
                  </div>
                  <div @click="tagPrintCols = '40'" style="cursor: pointer;" class="setting_print_font2">40*30mm</div>
                </div>
              </div>
            </div>
            <div v-show="$employeeAuth('use_settings')"
              class="settingHeight" @click="gotoSetting('tab11')">个性化设置
              <span style="font-family: Microsoft YaHei, sans-serif;">></span>
            </div>
          </div> -->
          <div class="setting_font" style="width: 402px;">标价签打印设置</div>
          <div class="printTestClass" @click="printTest('tip')">打印测试</div>
          <div class="aline"></div>
          <div>
            <div>
              <div class="setting_print_font">选择打印机</div>
              <div class="setting_input">
                <el-select
                  v-model="tipPrinterValue"
                  placeholder=""
                >
                  <el-option
                    v-for="pr in printerList"
                    :key="pr.label"
                    :label="pr.label"
                    :value="pr.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div>
              <div class="setting_print_font">打印份数</div>
              <div class="setting_input">
                <div class="setting_radio_main" style="display: inline-block;float: right;width: 348px;">
                  <div style="float: left;">
                    <div
                      @click="setTipDefault = false"
                      class="setting_radio"
                      :class="!setTipDefault ? 'yes' : 'no'"
                    >
                      <div v-show="!setTipDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setTipDefault = false" class="setting_print_font2"
                    style="cursor: pointer;">每次打印前询问</div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="setting_print_font">&nbsp;</div>
              <div class="setting_input">
                <div class="setting_radio_main" style="display: inline-block;float: right;width: 348px;">
                  <div style="float: left;">
                    <div
                      @click="setTipDefault = true"
                      class="setting_radio"
                      :class="setTipDefault ? 'yes' : 'no'"
                    >
                      <div v-show="setTipDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setTipDefault = true" class="setting_print_font2" style="cursor: pointer;">默认打印份数</div>
                  </div>
                  <div style="float: right;">
                    <div class="setting_input" style="width: 160px;">
                      <el-input
                        :disabled="!setTipDefault"
                        @blur="setNum('tipPrinterCopies')"
                        v-model="tipPrinterCopies"
                        @input="tipPrinterCopies = $intMaxMinLimit({data: tipPrinterCopies, max: 999, min: 0})"
                        placeholder="最大输入999"
                      ></el-input>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-show="$employeeAuth('use_settings')"
              class="settingHeight" @click="gotoSetting('tab2')">个性化设置
              <span style="font-family: Microsoft YaHei, sans-serif;">></span>
            </div>
          </div>
        </div>
        <div
          class="btn_container"
          style="padding-bottom: 20px;margin-top: 5px"
        >
          <div
            class="btn"
            id="cancelBtn"
            @click="closePaySetting"
          >取消</div>
          <div
            class="btn"
            id="saveBtn"
            @click="saveSetting('header')"
          >保存</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data () {
    return {
      nowIndex: 0,
      findPrinter: '',
      voiceOff: false,
      set_priceNotice: true,
      set_price: 0,
      showAdvertIn: false,
      adDuration: 15, // 图片广告切换时间
      loadAdReady: false,
      has_moneybox: false, // 钱箱设置
      hasWeighOff: false, // 电子秤设置
      hasBarCodeScales: false, // 条码秤开关
      set_StockShow: false,
      soundVolume: 1, // 音量强度
      scanCodeSound: true, // 扫码提示音
      defaultAcctsId: '', // 收银台默认支付方式, '':未选择  1: 现金支付  7: 线下支付  6: 扫码支付
      defaultAcctsOptions: [
        {value: 1, label: '现金支付'},
        {value: 7, label: '线下支付'},
        {value: 6, label: '扫码支付'}
      ],
      soundListen: false,
      soundTimeout: null, // 播报测试
      showKexian: false, // 客显设置
      moneybox_type: 1,
      moneybox_value: '',
      moneybox: [
        { label: 'COM1' },
        { label: 'COM2' },
        { label: 'COM3' },
        { label: 'COM4' },
        { label: 'COM5' },
        { label: 'COM6' }
      ],
      weighValue: '',
      weighShowInPay: true,
      weighTypeValue: '',
      kexianValue: '',
      kexianPriceType: 'price',
      uploadData: {},
      uploadDataAdv: {
        templateId: 1,
        comments: '',
        location: 3,
        startDate: new Date().format('yyyy-MM-dd hh:mm:ss'),
        endDate: '2099-12-31 23:59:59',
        duration: 15,
        type: 3,
        index: 1,
        file: ''
      },
      setEquip: false, // 读卡设备
      showMicroShop: false, // 显示微店设置
      smallPrinterValue: '',
      labelPrinterValue: '',
      tipPrinterValue: '',
      tagPrinterValue: '',
      printerList: [{ label: '请选择默认打印机', value: '' }],
      receiptPrinterList: [{ label: '请选择默认打印机', value: '' }],
      labelItemVerticals: [
        { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', titleKey: '品牌' },
        { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', titleKey: '品名' },
        { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', titleKey: '货号' },
        { key: 'season', title: '季节', flag: true, value: '春秋', label: '', titleKey: '季节' },
        { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', titleKey: '成分' },
        { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', titleKey: '等级' },
        { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', titleKey: '自定义' },
        { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', titleKey: '售价' },
        { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', titleKey: '建议零售价' },
        { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', titleKey: '打印条码' },
        { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', titleKey: '打印货号' }
      ],
      fontSizes: [
        { key: 12, title: '大' },
        { key: 10, title: '中' },
        { key: 8, title: '小' }
      ],
      codeList: [{ label: '条码', value: 'code' }, { label: '货号', value: 'mainCode' }],
      codePrintValue: 'code',
      printCols: '58',
      print_cols_label: '60',
      tagPrintCols: '60',
      tagIsTransverse: true,
      font_value: `"仿宋, 12pt, style=Bold"`,
      line_height: '60',
      sale_price_fontsize: '12',
      vip_price_fontsize: '16',
      sale_price_fontsizes: [],
      font_size_options: [{
        font_value: '',
        label: '默认大小'
      }, {
        font_value: `"仿宋, 22pt, style=Bold"`,
        label: '大'
      }, {
        font_value: `"仿宋, 12pt, style=Bold"`,
        label: '中'
      }, {
        font_value: `"仿宋, 6pt, style=Bold"`,
        label: '小'
      }],
      printDrives: [{
        value: 1,
        label: '指令打印'
      }, {
        value: 2,
        label: '驱动打印'
      }],
      printMode: 1,
      line_height_options: [],
      remark_content: '谢谢惠顾，欢迎下次光临！',
      smallPrinterCopies: 1, // 小票默认打印份数
      labelPrinterCopies: 1, // 条码默认打印份数
      tipPrinterCopies: 1, // 标价签默认打印份数
      memberInfoArrs: [
        { key: 'memberName', title: '会员名', flag: true },
        { key: 'memberPhone', title: '会员手机号', flag: true },
        { key: 'memberMoney', title: '会员卡余额', flag: true },
        { key: 'memberPoints', title: '会员积分', flag: true }
      ],
      logAndCode: [
        { key: 'logo', title: '打印店铺logo', flag: false, url: 'https://www.zgpos.com/local/logo/logo.png', hasImg: true, remark: '（建议图片尺寸300*100像素）' },
        { key: 'code', title: '打印商家二维码', flag: false, url: 'https://www.zgpos.com/local/logo/qrcode.png', hasImg: true, remark: '（建议图片尺寸400*400像素）' }
      ],
      printDate: new Date().format('yyyy-MM-dd hh:mm:ss'),
      setLabelDefault: false,
      setTipDefault: false,
      labelItem: [
        { key: 'commodityName', title: '商品名称', flag: true },
        { key: 'commodityPrice', title: '商品售价', flag: true },
        { key: 'commodityLabel', title: '商品条码', flag: true },
        { key: 'commodityDate', title: '生产日期', flag: false },
        { key: 'commodityEndDate', title: '保质期', flag: false }
      ],
      tagModels: [
        { key: 'model1',
          name: '模板1',
          flag: true,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        },
        { key: 'model2',
          name: '模板2',
          flag: false,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        },
        { key: 'model3',
          name: '模板3',
          flag: false,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        },
        { key: 'model4',
          name: '模板4',
          flag: false,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        },
        { key: 'model5',
          name: '模板5',
          flag: false,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        },
        { key: 'model6',
          name: '模板6',
          flag: false,
          subModel: [
            { key: 'brand', title: '品牌', flag: true, value: this.$t('components.header.header_user'), label: '', size: 10, titleKey: '品牌' },
            { key: 'name', title: '品名', flag: true, value: '冰爽棉麻', label: '', size: 10, titleKey: '品名' },
            { key: 'majorCode', title: '货号', flag: true, value: '2200850000', label: '', size: 10, titleKey: '货号' },
            { key: 'season', title: '季节', flag: true, value: '春秋', label: '', size: 10, titleKey: '季节' },
            { key: 'Ingredients', title: '成分', flag: true, value: '65%棉35%氨纶', label: '', size: 10, titleKey: '成分' },
            { key: 'grade', title: '等级', flag: true, value: '一等品', label: '', size: 10, titleKey: '等级' },
            { key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10, titleKey: '自定义' },
            { key: 'price', title: '售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '售价' },
            { key: 'initPrice', title: '建议零售价', flag: true, value: '¥118.00', label: '', size: 10, titleKey: '建议零售价' },
            { key: 'code', title: '打印条码', flag: true, value: '2200850000016', label: '条形码+数字', size: 10, titleKey: '打印条码' },
            { key: 'printMajorCode', title: '打印货号', flag: true, value: '2200850000', label: '条形码+数字', size: 10, titleKey: '打印货号' }
          ]
        }
      ],
      printList: [],
      isPrint: false,
      printVipPrice: true
    };
  },
  methods: {
    ...mapActions([SET_SHOW]),
    saveDefault() {
      let data = [
        { key: 'hasWeigh', value: this.hasWeigh, remark: '是否启用电子秤' },
        { key: 'weighValue', value: 'COM2', remark: '电子秤端口' },
        { key: 'weighTypeValue', value: 'HongHaiACS', remark: '电子秤类型' },
        { key: 'isOnceOpen', value: this.isOnceOpen, remark: '是否是第一次打开称重商品' },
        { key: 'posprint', value: this.findPrinter, remark: '小票' }
      ];
      settingService.put(data, () => {
        this.SET_SHOW({ isFirst: false });
        this.hasWeighOff = true;
        this.weighValue = 'COM2';
        this.weighTypeValue = 'HongHaiACS';
      });
      this.SET_SHOW({ setting_small_printer: this.findPrinter });
    },
    setNum(type) {
      if (this[type] === '' || Number(this[type]) === 0 || this[type] === undefined) {
        this[type] = 1;
      }
    },
    openAd(delay) {
      external.reloadAd(delay ? false : this.set_show_ad);
      this.SET_SHOW({ reloadAdBeforeDestroy: false });
      if (delay) {
        setTimeout(() => {
          external.reloadAd(this.set_show_ad);
          this.SET_SHOW({ reloadAdBeforeDestroy: false });
          this.changeWaitReloadAd(false);
        }, 3000);
      }
    },
    changeWaitReloadAd(boolean) {
      this.SET_SHOW({ waitReloadAd: boolean });
    },
    setShowAdChange(val) {
      this.SET_SHOW({ set_show_ad: val });
    },
    durationChange() {
      // 修改每个广告的持续时间
      if (!this.adDuration && this.adDuration !== 0) {
        this.adDuration = 15;
      } else if (this.adDuration < 5) { // 广告轮播时间可设置下限调整为5秒
        this.adDuration = 5;
      }
      this.SET_SHOW({ reloadAdBeforeDestroy: true });
      demo.$http.put(this.$rest.updateDuration + `?duration=${Number(this.adDuration)}&locations=3`)
        .then(res => {
          console.log(res);
        })
        .catch(err => {
          console.log(err);
        })
    },
    getHeader() {
      return {
        'Authorization': demo.$store.state.show.token !== '' ? demo.$store.state.show.token : $config.token
      }
    },
    beforeAvatarUploadAdv(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 3;

      if (!isJPG && !isPNG) {
        demo.msg('warning', '上传图片只能是 JPG/PNG 格式!');
      } else if (!isLt2M) {
        demo.msg('warning', '上传图片大小不能超过 3MB!');
      } else {
        console.log('其他');
      }
      this.uploadDataAdv.duration = this.adDuration;
      return (isJPG && isLt2M) || (isPNG && isLt2M);
    },
    getAdvFile(file) {
      this.uploadDataAdv.file = file;
      if (this.adsList.length === 0) {
        this.loadAdReady = true;
      }
      this.SET_SHOW({ reloadAdBeforeDestroy: true });
      this.adSearch();
    },
    adEdit (item) {
      this.SET_SHOW({
        ad_info: _.cloneDeep(item)
      });
      this.SET_SHOW({
        show_edit_ad: true
      });
    },
    closePaySetting() {
      this.SET_SHOW({ isPrintSetting: false });
    },
    // 检测 钱箱配置
    moneyboxCheck () {
      let _this = this;
      // 打印机集成钱箱，一体机
      if (Number(_this.moneybox_type) === 1) {
        if (
          !_this.smallPrinterValue ||
          _this.smallPrinterValue.trim() === ''
        ) {
          demo.msg('warning', _this.$msg.select_printer);
          return;
        }
        external.openCashBox(
          _this.smallPrinterValue,
          () => {
            demo.msg('success', _this.$msg.moneybox_check_success_other_small_printer);
          },
          () => {
            demo.msg('warning', _this.$msg.cash_box_in_error);
          }
        );
      } else {
        if (!_this.moneybox_value || _this.moneybox_value.trim() === '') {
          demo.msg('warning', _this.$msg.select_port);
          return;
        }
        external.openCashBox_separate(
          _this.moneybox_value,
          2400,
          8,
          () => {
            demo.msg('success', _this.$msg.moneybox_check_success_other_port);
          },
          () => {
            demo.msg('warning', _this.$msg.cash_box_in_error);
          }
        );
      }
    },
    gotoSetting(tap) {
      this.SET_SHOW({ settingIndex: tap });
      this.SET_SHOW({ selectRow: 3 });
      if (this.payListLength > 0) {
        this.SET_SHOW({ showPayPrint: true });
        return;
      }
      this.SET_SHOW({ settingFrom: 'pay' });
      this.SET_SHOW({ showPaySetting: false });
      this.SET_SHOW({ isGoods: false });
      this.SET_SHOW({ isPay: false });
      this.SET_SHOW({ isSetting: true });
    },
    printTest(type) {
      if (this.isPrint) {
        return;
      }
      this.isPrint = true;
      setTimeout(() => {
        this.isPrint = false;
      }, 3000);
      if (type === 'small') {
        this.smallPrint();
      } else if (type === 'label') {
        this.labelPrint();
      } else if (type === 'tip') {
        this.tipPrint();
      } else {
        this.labelPrintErect();
      }
    },
    smallPrint() {
      const isVip = this.memberInfoArrs.some(item => item.flag === true);
      let printData = {
        remark: this.remark_content,
        printername: this.smallPrinterValue,
        storename: this.usernamePrint,
        accts: '会员支付',
        pay_amt: '1.00',
        change_amt: '0.00',
        createAt: this.printDate,
        member_name: '会员名',
        member_value: '李大宝',
        member_money_name: '余额',
        member_money_value: '120.20',
        member_mobile_value: '123****4567',
        member_point_name: '积分',
        member_point_value: '20',
        line_height: this.line_height,
        gfont: this.font_value,
        final_integral_money: '1.00',
        goods: [{
          name: '农夫山泉天然水550ml',
          disc: '100',
          salePrice: '2.00',
          number: '1',
          amt: 2,
          amtReally: 2,
          mprice: '2.00'
        }],
        operater: '1001'
      };
      if (isVip) {
        printData['member_mobile_name'] = '会员手机号';
      }
      let obj = {
        from: 'pay',
        buy_back: 1,
        orderno: 'XSD202012000099'
      };
      if (this.smallPrinterValue === '') {
        demo.msg('warning', '请选择打印机');
        return;
      }
      printData.printNum = demo.$store.state.show.smallPrinterCopies;
      pos.printer.printPOS(printData, obj, true, true);
    },
    labelPrint() {
      if (this.labelPrinterValue === '') {
        demo.msg('warning', '请选择打印机');
        return;
      }
      let emptyObj = {Title: ' ', Text: ' ', Size: 6};
      let printData = {
        printname: this.labelPrinterValue,
        Width: this.getLabPrintWidth(),
        Height: this.getLabPrintHeight(),
        Landscape: this.getIsLandscape(),
        Offset: 0.01,
        DefaultSize: 10,
        others: []
      };
      this.setPrintDataOthers(printData, emptyObj);
      console.log(printData, 'printData');
      external.printLabelAndBarcodeInMM(printData);
    },
    setPrintDataOthers(printData, emptyObj) {
      if (this.labelItem[0].flag) {
        printData.others.push({Title: '农夫山泉天然水550ml', Text: '', Size: 10});
      }
      if (this.labelItem[1].flag) {
        printData.others.push({Title: '售价：', Text: '¥2.00', Size: 10});
      }
      printData.others.push(emptyObj);
      if (this.labelItem[2].flag) {
        printData.others.push({
          barcode: '6921168509256',
          barcode_Width: this.print_cols_label === '40' ? 40 : 60,
          barcode_Height: this.print_cols_label === '40' ? 40 : 60
        });
      }
      let dateStr = '';
      dateStr += this.labelItem[3].flag && this.print_cols_label !== '40' ? '生产日期:2021/04/02 '
        : '                    ';
      dateStr += this.labelItem[4].flag && this.print_cols_label !== '40' ? '保质期:10天' : '';
      printData.others.push({
        Title: '',
        Text: dateStr,
        Size: 8
      });
      if (this.print_cols_label === '64') {
        printData.others.unshift(emptyObj);
      }
    },
    getLabPrintWidth() {
      return this.print_cols_label !== '60' ? this.labelPrintMap[this.print_cols_label].width
        : this.labelPrintMap[this.print_cols_label].height;
    },
    getLabPrintHeight() {
      return this.print_cols_label !== '60' ? this.labelPrintMap[this.print_cols_label].height
        : this.labelPrintMap[this.print_cols_label].width;
    },
    getIsLandscape() {
      return this.print_cols_label === '60';
    },
    labelPrintErect() {
      if (this.tagPrinterValue === '') {
        demo.msg('warning', '请选择打印机');
        return;
      }
      let data = [];
      this.tagModels[this.nowIndex].subModel.forEach(item => {
        if (item.flag) {
          if (item.key === 'custom') {
            data.push({Title: item.label + '：', Text: item.value, Size: item.size});
            return;
          }
          if (item.key === 'printMajorCode' || item.key === 'code') {
            data.push({barcode: item.value, barcode_Width: 170, barcode_Height: 40});
            return;
          }
          data.push({Title: item.title + '：', Text: item.value, Size: item.size});
        }
      });
      let printData = {
        printname: this.tagPrinterValue,
        Width: this.getPrintDataWidth(),
        Height: this.getPrintDataHeight(),
        Landscape: !this.tagIsTransverse,
        Offset: 0.02,
        DefaultSize: 10,
        others: data
      }
      console.log(printData, 'printData');
      external.printLabel_new(printData);
    },
    getPrintDataWidth() {
      return this.tagPrintCols === '60' ? (this.tagIsTransverse ? 40 : 60) : (this.tagIsTransverse ? 40 : 30);
    },
    getPrintDataHeight() {
      return this.tagPrintCols === '60' ? (this.tagIsTransverse ? 60 : 40) : (this.tagIsTransverse ? 30 : 40);
    },
    tipPrint() {
      console.log(this.tipPrinterValue, 'this.tipPrinterValue');
      if (this.tipPrinterValue === '') {
        demo.msg('warning', '请选择打印机');
        return;
      }
      let printData = [{
        code: '6921168509256',
        name: '农夫山泉天然水550ml',
        salePrice: '2.00',
        sale_price: "2.00",
        unit_name: '瓶',
        vipPrice: this.printVipPrice ? '2.00' : '',
        vip_price: this.printVipPrice ? '2.00' : '',
        store_name: this.usernamePrint,
        sale_name: '售价',
        vip_name: this.printVipPrice ? '会员价' : '',
        sale_price_fontsize: this.sale_price_fontsize,
        vip_price_fontsize: this.vip_price_fontsize
      }];
      console.log(printData, '------------printData');
      external.printtip(
        this.tipPrinterValue,
        JSON.stringify(printData)
      );
    },
    saveSetting (from, onSuccess) {
      let midSetEquip = _.cloneDeep(this.useCardReader === 1);
      this.SET_SHOW({ isPrintSetting: false });
      this.SET_SHOW({ setting_hasmoneybox: this.has_moneybox + '' });
      this.SET_SHOW({ useCardReader: this.setEquip ? 1 : 0 });
      this.SET_SHOW({
        setting_moneybox: this.moneybox_type === 1 ? '' : this.moneybox_value
      });
      this.SET_SHOW({ setting_small_printer: this.smallPrinterValue });
      this.SET_SHOW({ setting_label_printer: this.labelPrinterValue });
      this.SET_SHOW({ setting_tip_printer: this.tipPrinterValue });
      this.SET_SHOW({ setting_tag_printer: this.tagPrinterValue });
      this.SET_SHOW({ print_cols: this.printCols });
      this.SET_SHOW({ print_cols_label: this.print_cols_label });
      this.SET_SHOW({ tagPrintCols: this.tagPrintCols });
      this.SET_SHOW({ font_value: this.font_value });
      this.SET_SHOW({ line_height: this.line_height });
      this.SET_SHOW({ sale_price_fontsize: this.sale_price_fontsize });
      this.SET_SHOW({ vip_price_fontsize: this.vip_price_fontsize });
      this.SET_SHOW({ remark_content: this.remark_content });
      this.SET_SHOW({ smallPrinterCopies: this.smallPrinterCopies });
      this.SET_SHOW({ labelPrinterCopies: this.labelPrinterCopies });
      this.SET_SHOW({ tipPrinterCopies: this.tipPrinterCopies });
      this.SET_SHOW({ memberInfoArr: this.memberInfoArrs });
      this.SET_SHOW({ printVipPrice: this.printVipPrice });
      this.SET_SHOW({ labelItem: this.labelItem });
      this.SET_SHOW({ tagModels: this.tagModels });
      this.SET_SHOW({ logAndCode: this.logAndCode });
      this.SET_SHOW({ setLabelDefault: this.setLabelDefault });
      this.SET_SHOW({ setTipDefault: this.setTipDefault });
      this.SET_SHOW({ tagIsTransverse: this.tagIsTransverse });
      this.SET_SHOW({ showKexian: this.showKexian });
      this.SET_SHOW({ weighValue: this.weighValue });
      this.SET_SHOW({ weighShowInPay: this.weighShowInPay });
      this.SET_SHOW({ kexianValue: this.kexianValue });
      this.SET_SHOW({ weighTypeValue: this.weighTypeValue });
      this.SET_SHOW({ hasWeigh: this.hasWeighOff });
      this.SET_SHOW({ soundVolume: this.soundVolume });
      this.SET_SHOW({ scanCodeSound: this.scanCodeSound });
      // this.SET_SHOW({ defaultSettlementKey: this.defaultSettlementKey });
      this.SET_SHOW({ clearZero: this.set_price });
      this.SET_SHOW({ printMode: this.printMode });
      this.SET_SHOW({ codePrintValue: this.codePrintValue });
      // 设置读卡设备开关按钮
      if (midSetEquip !== this.setEquip) {
        external.setVipCardReader(this.useCardReader, () => {
          console.log('设置读卡器');
        });
      }
      let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      dataInfo.hasBarCodeScales = this.hasBarCodeScales;
      dataInfo.set_StockShow = this.set_StockShow;
      dataInfo.sound_volume = this.soundVolume;
      dataInfo.scan_code_sound = this.scanCodeSound;
      // dataInfo.default_settlement_key = this.defaultSettlementKey;
      dataInfo.sale_price_fontsize = this.sale_price_fontsize;
      dataInfo.vip_price_fontsize = this.vip_price_fontsize;
      dataInfo.weigh_show_in_pay = this.weighShowInPay;
      // 提交
      var data = [
        { key: 'cutsmallmoney', value: this.set_price, remark: '抹零设置' },
        { key: 'voice_off', value: this.voiceOff ? '0' : '1', remark: '语音播报开关' },
        { key: 'set_priceNotice', value: this.set_priceNotice ? '0' : '1', remark: '负利润check' },
        { key: 'labelprint', value: this.labelPrinterValue, remark: '条码' },
        { key: 'tagPrint', value: this.tagPrinterValue, remark: '吊牌' },
        { key: 'tipprint', value: this.tipPrinterValue, remark: '标价签' },
        { key: 'posprint', value: this.smallPrinterValue, remark: '小票' },
        { key: 'font_value', value: this.font_value, remark: '小票正文字号设置' },
        { key: 'line_height', value: this.line_height, remark: '小票行间距' },
        { key: 'print_cols', value: this.printCols, remark: '小票打印规格' },
        { key: 'print_cols_label', value: this.print_cols_label, remark: '条码纸规格' },
        { key: 'tagPrintCols', value: this.tagPrintCols, remark: '吊牌纸规格' },
        {
          key: 'moneybox',
          value: this.moneybox_type === 1 ? '' : this.moneybox_value,
          remark: '钱箱'
        },
        { key: 'has_moneybox', value: this.has_moneybox, remark: '是否有钱箱' },
        {
          key: 'show_ad',
          value: this.set_show_ad ? '1' : '0',
          remark: '是否显示广告'
        },
        { key: 'hasWeigh', value: this.hasWeighOff, remark: '是否启用电子秤' },
        { key: 'info', value: JSON.stringify(dataInfo), remark: '是否启用条码秤、是否设置群发短信等' },
        { key: 'remark_content', value: this.remark_content.replace(/'/g, '‘').replace(/;/g, '；'), remark: '小票备注内容' },
        { key: 'logAndCode', value: JSON.stringify(this.logAndCode), remark: '打印logo及二维码开关' },
        { key: 'memberInfoArr', value: JSON.stringify(this.memberInfoArrs), remark: '打印会员信息' },
        { key: 'labelItem', value: JSON.stringify(this.labelItem), remark: '条码需要打印的信息' },
        { key: 'tagModels', value: JSON.stringify(this.tagModels), remark: '吊牌打印个性化设置' },
        { key: 'smallPrinterCopies', value: this.smallPrinterCopies, remark: '小票打印张数' },
        { key: 'labelPrinterCopies', value: this.labelPrinterCopies, remark: '条码打印张数' },
        { key: 'tipPrinterCopies', value: this.tipPrinterCopies, remark: '标价签打印张数' },
        { key: 'setLabelDefault', value: this.setLabelDefault, remark: '条码默认打印份数开关' },
        { key: 'setTipDefault', value: this.setTipDefault, remark: '标价签默认打印份数开关' },
        { key: 'printVipPrice', value: JSON.stringify(this.printVipPrice), remark: '打印会员价开关' },
        { key: 'tagIsTransverse', value: this.tagIsTransverse ? '0' : '1', remark: '打印预览' },
        { key: 'weighValue', value: this.weighValue, remark: '电子秤端口' },
        { key: 'weighTypeValue', value: this.weighTypeValue, remark: '电子秤类型' },
        { key: 'showKexian', value: this.showKexian, remark: '是否启用客显' },
        { key: 'kexianValue', value: this.kexianValue, remark: '客显端口' },
        { key: 'printMode', value: this.printMode, remark: '打印机驱动' },
        { key: 'codePrintValue', value: this.codePrintValue, remark: '打印条码' },
        { key: 'stock', value: this.setSystem ? 1 : 0, remark: '是否无库存销售' }
      ];
      settingService.put(data, () => {
        if (from !== 'setting') {
          demo.msg('success', this.$msg.save_success);
        }
        if (onSuccess) {
          onSuccess();
        }
      });
    },
    paySetting (n) {
      // 初始化打印机
      this.getSetting();
      // 小票打印机
      this.getPrinter('small');
      // 条码打印机
      this.getPrinter('label');
      // 价签打印机
      this.getPrinter('tip');
      // 吊牌打印机
      this.getPrinter('tag');
      // 广告
      if (n === 1 && this.set_show_ad) {
        this.adSearch();
      }
      // 钱箱
      this.getMoneybox();
      // 其他设置项
      this.getOtherSets();
      // 设置默认值
      setTimeout(() => {
        this.setDefault();
      }, 700);
    },
    checkWeightConnect() {
      try {
        var data = {
          exec: 'open',
          port: 'COM2',
          baudrate: '9600',
          databits: '8',
          parity: '0',
          stopBits: '1',
          script: 'demo.checkWeight("{0}");',
          scaleName: 'HongHaiACS'
        };
        if (this.hasWeigh) {
          external.execScale(data);
        }
      } catch (e) {
        console(e);
      }
    },
    closeScale() {
      try {
        var data = {};
        external.execScale(data);
        this.inputWeight = '';
      } catch (e) {
        console.log(e);
      }
    },
    setDefault() {
      this.findPrinter = _.find(this.printList, (o) => { return o === 'EP-200 Printer' || o === 'POS58 Printer'; });
      let findCom2 = _.find(this.weighList, (o) => { return o === 'COM2'; });
      if (this.findPrinter !== undefined && findCom2 !== undefined && this.isOnceOpen && this.smallPrinterValue === '') {
        this.checkWeightConnect();
      }
    },
    getMoneybox() {
      this.has_moneybox = this.settingHasmoneybox === 'true';
      this.SET_SHOW({ setting_hasmoneybox: this.has_moneybox + '' });
      this.moneybox_value = this.settingMoneybox;
      let checkValue = this.moneybox_value !== null && this.moneybox_value.trim() !== '';
      if (checkValue) {
        this.moneybox_type = 2;
      }
      this.SET_SHOW({
        setting_moneybox: this.moneybox_type === 1 ? '' : this.moneybox_value
      });
    },
    getSetting () {
      this.printList = external.listPrinters().split(',');
      const that = this;
      zgPrinter.getUsbPrinterList().then(printerItem => {
        that.printerList = [{ label: '请选择默认打印机', value: '' }];
        that.receiptPrinterList = [{ label: '请选择默认打印机', value: '' }];
        if (printerItem && printerItem.length > 0) {
          printerItem.forEach(item => {
            that.receiptPrinterList.push({ label: item.Key, value: item.Value });
          });
        }
        that.printList.forEach(item => {
          that.receiptPrinterList.push({ label: item, value: item });
          that.printerList.push({ label: item, value: item });
        });
        // 当前选择的打印机在列表中是否存在
        let currentPrintValue = that.receiptPrinterList.findIndex((v) => v.value === that.smallPrinterValue);
        if (currentPrintValue === -1) {
          that.smallPrinterValue = that.receiptPrinterList[0].label;
        }
      });
    },
    // 广告
    adSearch () {
      let _this = this;
      if (pos.network.isConnected()) {
        demo.$http.post(_this.$rest.advertGet, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          timeout: 60000
        })
          .then(res => {
            if (res.data.code === 200) {
              let adLists = res.data.data.map(temp => {
                temp.html = temp.html.replace(
                  '{picture_path}',
                  temp.img
                );
                temp.html = temp.html.replace('{comments}', temp.comments);
                return temp;
              });
              _this.SET_SHOW({ adsList: adLists });
              _this.adDuration = Number(adLists[0] && adLists[0].duration) || 15;
              if (_this.loadAdReady && adLists.length) {
                _this.loadAdReady = false;
                _this.changeWaitReloadAd(true);
                _this.openAd(true);
              }
            } else {
              demo.msg('warning', res.data.msg);
            }
          });
      } else {
        demo.msg('warning', '网络已断开，请恢复网络后重试');
      }
    },
    // 打印机
    getPrinter(print) {
      let key1 = print + 'PrinterValue';
      let key2 = print + 'SettingPrinter';
      if (this.printList.includes(this[key2])) {
        this[key1] = this[key2];
        if (key1 === 'smallPrinterValue') {
          this.SET_SHOW({ setting_small_printer: this[key2] });
        } else if (key1 === 'labelPrinterValue') {
          this.SET_SHOW({ setting_label_printer: this[key2] });
        } else if (key1 === 'tipPrinterValue') {
          this.SET_SHOW({ setting_tip_printer: this[key2] });
        } else {
          this.SET_SHOW({ setting_tag_printer: this[key2] });
        }
      }
    },
    getOtherSets() {
      if ($setting.show_ad) {
        this.SET_SHOW({ set_show_ad: $setting.show_ad !== '0' })
      }
      if ($setting.useCardReader) {
        this.setEquip = Number($setting.useCardReader) == 1 && this.ultimate !== null;
        this.SET_SHOW({ useCardReader: this.setEquip ? 1 : 0 });
      }
      if ($setting.print_cols) {
        this.printCols = $setting.print_cols;
        this.SET_SHOW({ print_cols: this.printCols });
      }
      if ($setting.print_cols_label) {
        this.print_cols_label = $setting.print_cols_label;
        this.SET_SHOW({ print_cols_label: this.print_cols_label });
      }
      if ($setting.font_value) {
        this.setFontValue();
      }
      if ($setting.line_height) {
        this.line_height = $setting.line_height;
        this.SET_SHOW({ line_height: this.line_height });
      }
      if ($setting.remark_content || $setting.remark_content === '') {
        this.remark_content = $setting.remark_content;
        this.SET_SHOW({ remark_content: this.remark_content });
      }
      if ($setting.memberInfoArr && demo.t2json($setting.memberInfoArr).length > 0) {
        if (this.ultimate) {
          this.memberInfoArrs = demo.t2json($setting.memberInfoArr);
        }
        this.SET_SHOW({ memberInfoArr: this.memberInfoArrs });
      }
      this.getOtherSetsSonar();
    },
    setFontValue() {
      let fontValue = $setting.font_value;
      if (fontValue === '1' || fontValue === '2' || fontValue === '3') {
        fontValue = '';
      }
      this.SET_SHOW({ font_value: fontValue });
      this.font_value = fontValue;
    },
    setSalePriceFontsize(dataInfo) {
      if (dataInfo.sale_price_fontsize) {
        this.sale_price_fontsize = dataInfo.sale_price_fontsize;
        this.SET_SHOW({ sale_price_fontsize: this.sale_price_fontsize });
      }
      if (dataInfo.vip_price_fontsize) {
        this.vip_price_fontsize = dataInfo.vip_price_fontsize;
        this.SET_SHOW({ vip_price_fontsize: this.vip_price_fontsize });
      }
    },
    getOtherSetsSonar() {
      if ($setting.weighValue) {
        this.weighValue = $setting.weighValue;
        this.SET_SHOW({ weighValue: this.weighValue });
      }
      if ($setting.weighTypeValue) {
        this.weighTypeValue = $setting.weighTypeValue;
        this.SET_SHOW({ weighTypeValue: this.weighTypeValue });
      }
      if ($setting.smallPrinterCopies) {
        this.smallPrinterCopies = demo.t2json($setting.smallPrinterCopies);
        this.SET_SHOW({ smallPrinterCopies: this.smallPrinterCopies });
      }
      if ($setting.labelPrinterCopies) {
        this.labelPrinterCopies = demo.t2json($setting.labelPrinterCopies);
        this.SET_SHOW({ labelPrinterCopies: this.labelPrinterCopies });
      }
      if ($setting.tipPrinterCopies) {
        this.tipPrinterCopies = demo.t2json($setting.tipPrinterCopies);
        this.SET_SHOW({ tipPrinterCopies: this.tipPrinterCopies });
      }
      if ($setting.logAndCode && demo.t2json($setting.logAndCode).length > 0) {
        if (this.ultimate) {
          this.logAndCode = demo.t2json($setting.logAndCode);
        }
        this.SET_SHOW({ logAndCode: this.logAndCode });
      }
      if ($setting.setLabelDefault) {
        this.setLabelDefault = demo.t2json($setting.setLabelDefault);
        this.SET_SHOW({ setLabelDefault: this.setLabelDefault });
      }
      if ($setting.setTipDefault) {
        this.setTipDefault = demo.t2json($setting.setTipDefault);
        this.SET_SHOW({ setTipDefault: this.setTipDefault });
      }
      this.getSetsSubSonar();
    },
    getSetsSubSonar() {
      if ($setting.labelItem) {
        this.labelItem = demo.t2json($setting.labelItem);
        if (!this.ultimate) {
          for (let i = 0; i < 3; i++) {
            this.labelItem[i].flag = true;
          }
        }
        this.SET_SHOW({ labelItem: this.labelItem });
      }
      if ($setting.showKexian) {
        this.showKexian = demo.t2json($setting.showKexian);
        this.SET_SHOW({ showKexian: this.showKexian });
      }
      if ($setting.kexianValue) {
        this.kexianValue = $setting.kexianValue;
        this.SET_SHOW({ kexianValue: this.kexianValue });
      }
      if ($setting.cutsmallmoney) {
        this.set_price = isNaN($setting.cutsmallmoney) ? 0 : Number($setting.cutsmallmoney);
      }
      if ($setting.voice_off) {
        this.voiceOff = $setting.voice_off === '0';
        this.SET_SHOW({ voiceOff: this.voiceOff });
      }
      if ($setting.set_priceNotice) {
        this.set_priceNotice = $setting.set_priceNotice === '0';
      }
      this.getSetsThreeSonar();
    },
    getSetsThreeSonar() {
      if ($setting.print_small_ticket_afterpay) {
        this.SET_SHOW({ permit_print: JSON.parse($setting.print_small_ticket_afterpay) });
      }
      if ($setting.print_card_small_ticket_afterpay) {
        this.SET_SHOW({ permit_card_print: JSON.parse($setting.print_card_small_ticket_afterpay) });
      }
      if ($setting.estimatedProfit) {
        this.SET_SHOW({ estimatedProfit: $setting.estimatedProfit });
      }
      // 对应版本没有引导页的时候注释掉
      if (this.isFirstLogin) {
        this.SET_SHOW({ showNovice: true });
      } else if ($config.IndustryUpdated !== undefined) {
        this.SET_SHOW({ showNovice: $config.IndustryUpdated });
      } else {
        this.SET_SHOW({ showNovice: $setting.show_novice === '1' });
      }
      if ($setting.eye) {
        this.SET_SHOW({ eyeClose: demo.t2json($setting.eye) });
      }
      if ($setting.isOnceOpen) {
        this.SET_SHOW({ isOnceOpen: demo.t2json($setting.isOnceOpen) });
      }
      if ($setting.printVipPrice) {
        this.printVipPriceSetting()
      }
      if ($setting.hasWeigh) {
        this.hasWeighOff = demo.t2json($setting.hasWeigh);
        this.SET_SHOW({ hasWeigh: this.hasWeighOff });
      }
      if ($setting.info) {
        let dataInfo = demo.t2json($setting.info);
        this.hasBarCodeScales = dataInfo.hasBarCodeScales ? demo.t2json(dataInfo.hasBarCodeScales) : false;
        this.set_StockShow = dataInfo.set_StockShow ? dataInfo.set_StockShow : false;
        this.getSoundVolume(dataInfo);
        this.getScanCodeSound(dataInfo);
        this.getWeighShowInPay(dataInfo);
        // this.getDefaultSettlementKey(dataInfo);
        this.setSalePriceFontsize(dataInfo);
      }
      setTimeout(() => {
        this.soundListen = true;
      }, 0);
      this.getSetsFourSonar();
    },
    printVipPriceSetting() {
      if (this.ultimate) {
        this.printVipPrice = demo.t2json($setting.printVipPrice);
      }
      this.SET_SHOW({ printVipPrice: this.printVipPrice });
    },
    getSetsFourSonar() {
      if ($setting.posprint) {
        this.smallPrinterValue = $setting.posprint;
        this.SET_SHOW({ setting_small_printer: $setting.posprint });
      }
      if ($setting.labelprint) {
        this.labelPrinterValue = $setting.labelprint;
        this.SET_SHOW({ setting_label_printer: $setting.labelprint });
      }
      if ($setting.tipprint) {
        this.tipPrinterValue = $setting.tipprint;
        this.SET_SHOW({ setting_tip_printer: $setting.tipprint });
      }
      if ($setting.printMode) {
        let printMode = parseInt($setting.printMode)
        this.printMode = printMode === 0 ? 1 : printMode;
        this.SET_SHOW({ printMode: this.printMode });
      }
      if ($setting.codePrintValue) {
        this.codePrintValue = $setting.codePrintValue;
        this.SET_SHOW({ codePrintValue: $setting.codePrintValue });
      }
      this.getSetsFiveSonar();
    },
    getSetsFiveSonar() {
      if ($setting.tagIsTransverse) {
        this.tagIsTransverse = $setting.tagIsTransverse === '0';
        this.SET_SHOW({ tagIsTransverse: $setting.tagIsTransverse === '0' });
      }
      if ($setting.tagModels) {
        this.tagModels = demo.t2json($setting.tagModels);
        this.SET_SHOW({ tagModels: this.tagModels });
      }
      if ($setting.tagPrint) {
        this.tagPrinterValue = $setting.tagPrint;
        this.SET_SHOW({ setting_tag_printer: $setting.tagPrint });
      }
      if ($setting.tagPrintCols) {
        this.tagPrintCols = $setting.tagPrintCols;
        this.SET_SHOW({ tagPrintCols: this.tagPrintCols });
      }
    },
    getSoundVolume(dataInfo) {
      if (dataInfo.sound_volume) {
        this.soundVolume = demo.t2json(dataInfo.sound_volume);
        this.SET_SHOW({ soundVolume: this.soundVolume });
      }
    },
    getScanCodeSound(dataInfo) {
      if (dataInfo.scan_code_sound !== undefined) {
        this.scanCodeSound = demo.t2json(dataInfo.scan_code_sound);
        this.SET_SHOW({ scanCodeSound: this.scanCodeSound });
      }
    },
    getWeighShowInPay(dataInfo) {
      if (dataInfo.weigh_show_in_pay !== undefined) {
        this.weighShowInPay = demo.t2json(dataInfo.weigh_show_in_pay);
        this.SET_SHOW({ weighShowInPay: this.weighShowInPay });
      }
    },
    getDefaultSettlementKey(dataInfo) {
      if (dataInfo.default_settlement_key) {
        this.defaultSettlementKey = dataInfo.default_settlement_key;
      } else { // 默认enter
        this.defaultSettlementKey = 'Space';
      }
      this.SET_SHOW({ defaultSettlementKey: this.defaultSettlementKey });
    }
  },
  created() {
    if (this.isSetting) {
      if (this.isConnectWeight && this.isFirst) {
        this.saveDefault();
      }
    }
    if (this.ultimate) {
      this.logAndCode.forEach(item => {
        item.flag = false;
      });
      this.SET_SHOW({ logAndCode: this.logAndCode });
    }
    // 初始化行间距的值
    [1, 5, 10, 20, 40, 60, 70, 80].map(e => this.line_height_options.push({
      line_height: e.toString(),
      label: e.toString()
    }));
    // 初始字号范围的值
    [7, 8, 9, 10, 11, 12, 15, 16, 18, 20, 22, 24, 26, 28, 30].map(e => this.sale_price_fontsizes.push({
      fontsize: e.toString(),
      label: e.toString()
    }));
    // 免费版设置读卡设备开关按钮
    if (this.ultimate === null) {
      external.setVipCardReader(0);
    }
  },
  mounted() {
    if (localStorage.getItem('scanPay')) {
      $config.IndustryUpdated = false;
    }
    this.paySetting(1);
  },
  beforeDestroy() {
    if (this.reloadAdBeforeDestroy) {
      this.openAd();
    }
  },
  watch: {
    set_show_ad() {
      if (this.set_show_ad) {
        this.changeWaitReloadAd(true);
        this.adSearch();
      }
      this.openAd(this.set_show_ad);
    },
    line_height() {
      this.SET_SHOW({line_height: this.line_height});
    },
    font_value() {
      this.SET_SHOW({font_value: this.font_value});
    },
    sale_price_fontsize() {
      this.SET_SHOW({sale_price_fontsize: this.sale_price_fontsize});
    },
    vip_price_fontsize() {
      this.SET_SHOW({vip_price_fontsize: this.vip_price_fontsize});
    },
    isConnectWeight() {
      if (this.isConnectWeight) {
        this.closeScale();
      }
    },
    printCols() {
      this.SET_SHOW({print_cols: this.printCols});
    },
    printMode() {
      this.SET_SHOW({printMode: this.printMode});
    },
    setEquip() {
      if (this.setEquip === false) {
        return;
      }
      if (this.ultimate === null) {
        this.SET_SHOW({isBuySoftware: true});
        this.setEquip = false;
      }
    }
  },
  computed: mapState({
    usernamePrint: state => state.show.username,
    adsList: state => state.show.adsList,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    isFirstLogin: state => state.show.isFirstLogin,
    isPrintSetting: state => state.show.isPrintSetting,
    settingMoneybox: state => state.show.setting_moneybox,
    settingHasmoneybox: state => state.show.setting_hasmoneybox,
    useCardReader: state => state.show.useCardReader,
    smallSettingPrinter: state => state.show.setting_small_printer,
    labelSettingPrinter: state => state.show.setting_label_printer,
    tipSettingPrinter: state => state.show.setting_tip_printer,
    tagSettingPrinter: state => state.show.setting_tag_printer,
    labelPrintMap: state => state.show.labelPrintMap,
    ultimate: (state) => state.show.ultimate,
    payListLength: state => state.show.payListLength,
    set_show_ad: state => state.show.set_show_ad,
    reloadAdBeforeDestroy: state => state.show.reloadAdBeforeDestroy,
    isOnceOpen: state => state.show.isOnceOpen,
    hasWeigh: state => state.show.hasWeigh,
    weighList: state => state.show.weighList,
    specs: state => state.info.specs,
    isConnectWeight: state => state.show.isConnectWeight,
    storeList: state => state.show.storeList,
    waitReloadAd: state => state.show.waitReloadAd,
    isSetting: state => state.show.isSetting,
    isFirst: state => state.show.isFirst
  })
};
</script>
