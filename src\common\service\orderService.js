import dao from '../dao/dao';

const orderService = {
  syncGet: function (params) {
    return new Promise((resolve, reject) => {
      this.get(params, resolve, reject);
    });
  },

  /**
   * orderService.get({'type':'XSD'}, res => {console.log(res);}, err => {console.error(err);});
   * @param {*} params
   * @param {*} onSuccess
   * @param {*} onFail
   * @returns
   */
  get: function (params, onSuccess, onFail) {
    params.deviceCode = demo.$store.state.show.devicecode;
    if (params.type === 'VIP') {
      const no = [{code: new Date().format('yyMMddhhmmssS') + '.' + params.deviceCode}];
      onSuccess(JSON.stringify(no));
      return;
    }

    params.table = this.getTable(params.type);
    params.limit = +params.limit || 1;
    if (params.type === 'XSD') {
      dao.exec(sqlApi.getSalesNo.format(params), res => {
        const currentDay = new Date().format('yyMMdd');
        // const salesNo =
        const salesNo = res[0]['salesNo'];
        const { deviceCode } = params;
        const code = this.generateNewSalesOrderNo(currentDay, salesNo, deviceCode);
        const no = [{ code }];
        onSuccess(no);
      }, () => {
        const no = [{code: `XSD${new Date().format('yyMMddhhmmssS')}.${params.deviceCode}`}];
        onSuccess(no);
      });
    } else if (params.type === 'PDD') {
      dao.exec(sqlApi.orderGet.format(params), onSuccess, onFail);
    } else {
      dao.exec(sqlApi.getOrderNo.format(params), onSuccess, onFail);
    }
  },
  generateNewSalesOrderNo(currentDay, salesNo, deviceCode) {
    const currentSalesNo = `XSD${currentDay}${('00000' + salesNo).slice(-5)}.${deviceCode}`;
    const localSalesNo = localStorage.getItem('saleNo');
    if (currentSalesNo === localSalesNo) {
      const newSaleNo = `XSD${currentDay}${('00000' + (Number(salesNo) + 1)).slice(-5)}.${deviceCode}`;
      return newSaleNo;
    }
    return currentSalesNo;
  },
  getTable: function (type) {
    if (type === 'PDD') {
      return 'inventories';
    }
    if (type === 'JHD') {
      return 'purchases';
    }
    if (type === 'XSD') {
      return 'sales';
    }
  }

};

window.orderService = orderService;
export default orderService;
