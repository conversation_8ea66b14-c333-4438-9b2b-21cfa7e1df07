<style lang='less' scoped>
.com_psm18 .el-input__inner {
  width: 95%;
}
.com_psm1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 200;
  color: @themeFontColor;
}
.row_style {
  padding-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: #557485;
  line-height: 24px;
}
.com_psm11 {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 120;
  top: 0;
  left: 0;
}
.com_psm12 {
  float: left;
  margin-left: 20px;
  line-height: 32px;
  span {
    color: @themeBackGroundColor;
  }
}
.com_psm13 {
  float: right;
  margin-right: 20px;
}
.com_psm14_1 {
  position: relative;
  width: 900px;
  background: #fff;
  border-radius: 6px;
  margin: 0 auto;
  margin-top: 10px;
  overflow: hidden;
  z-index: 300;
  font-size: 16px;
}
.com_psm14_2 {
  position: relative;
  width: 700px;
  background: #fff;
  border-radius: 6px;
  margin: 0 auto;
  margin-top: 10px;
  overflow: hidden;
  z-index: 300;
  font-size: 16px;
}
.com_psm15 {
  line-height: 60px;
  width: calc(100% - 40px);
  margin-left: 20px;
  height: 60px;
  border-bottom: 1px solid #e3e6eb;
  overflow: hidden;
}
.com_psm16 {
  float: left;
  font-weight: 700;
  text-indent: 10px;
  font-size: 18px;
}
.com_psm17 {
  float: right;
  font-size: 30px;
  line-height: 56px;
  cursor: pointer;
}
.com_psm18 {
  border: 1px solid #e5e8ec;
  margin-left: 20px;
  width: calc(100% - 40px);
  margin-top: 10px;
  /deep/ .el-table__row {
    height: 43px;
  }
}
.com_psm18 .el-table th > .cell {
  padding-left: 10px;
}
.com_psm19 {
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 10px;
}
.com_psm2 {
  width: 120px;
  height: 40px;
  margin-left: 20px;
  line-height: 38px;
  margin-top: 18px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background: linear-gradient(90deg, #d2ba8a 0%, #b49a5a 100%);
  border-radius: 4px;
  cursor: pointer;
}
.com_psm21 {
  width: 120px;
  height: 44px;
  margin-left: 20px;
  line-height: 42px;
  margin-top: 18px;
  color: @themeBackGroundColor;
  font-size: 16px;
  text-align: center;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid @themeBackGroundColor;
  float: right;
  margin-right: 20px;
}
.com_psm22 {
  width: 120px;
  height: 44px;
  margin-left: 20px;
  line-height: 42px;
  margin-top: 18px;
  color: #fff;
  font-size: 16px;
  text-align: center;
  background: @themeBackGroundColor;
  border-radius: 4px;
  cursor: pointer;
  float: right;
  margin-right: 16px;
}
.margin_r45 {
  margin-right: 45px;
}
.box{
  display: flex;
  padding: 0px 20px;
  .box-item {
    width: 130px;
    height: 40px;
    line-height: 38px;
    margin-top: 10px;
    color: #fff;
    font-size: 16px;
    text-align: center;
    background: @themeBackGroundColor;
    border-radius: 4px;
    cursor: pointer;
    display: block;
}
 .box-field{
  flex:1;
  padding:8px 0px 0px 25px;
 }
}

/deep/ input::-webkit-outer-spin-button,
/deep/ input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
/deep/ input[type="number"] {
  -moz-appearance: textfield;
}
.addButton {
  color: @themeBackGroundColor;
  cursor:pointer;
}
.cancel {
  width: 108px;
  height: 43px;
  text-align: center;
  line-height: 40px;
  background: #FFFFFF;
  color: @themeBackGroundColor;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  border-width: 2px;
  border-style: solid;
  border-color: @themeBackGroundColor;
  cursor: pointer;
}
.save {
  width: 108px;
  height: 43px;
  text-align: center;
  line-height: 40px;
  margin-right: 30px;
  background:@themeBackGroundColor;
  color: #FFF;
  float: right;
  border-radius: 4px;
  cursor: pointer;
}
.warning-color {
  color: #FF0000;
}
/deep/.el-loading-spinner .path {
  stroke: @themeBackGroundColor;
}
</style>
<template>
  <!--设置会员积分商品弹出框-->
  <div v-show="showMemberGoods" class="com_psm1">
    <div class="com_psm11"></div>
    <div :class="pcSmgFrom === 'setting_member_gift' ? 'com_psm14_1' : 'com_psm14_2'">
      <div class="com_psm15">
        <div class="com_psm16" v-show="pcSmgFrom == 'setting_member_point'">
          <span v-show="!show_add_member_goods">不可积分商品列表</span>
          <span v-show="show_add_member_goods">添加不可积分商品</span>
        </div>
        <div class="com_psm16" v-show="pcSmgFrom == 'setting_member_gift'">设置兑换礼品</div>
        <div class="com_psm16" v-show="pcSmgFrom == 'setting_member_day'">设置会员日活动商品</div>
        <div
          class="com_psm17"
          @click="show_add_member_goods ? closeAddMemberGoods() : closeMemberGoods()"
        >×</div>
      </div>
      <el-input
        v-show="show_add_member_goods"
        id="addGoods"
        ref="addGoods"
        placeholder="请输入商品名称/条码/首字母/扫码"
        style="width: calc(100% - 40px);margin-left: 20px;margin-top: 10px;"
        v-model="keyword"
        prefix-icon="el-icon-search"
        maxlength="60"
        clearable
        @compositionstart='pinyin = true'
        @compositionend='pinyin = false'
        @input="keyword = keyword.replace(/[$']/g, '')"
        @keydown.native.enter="inputSelectHandler('addGoods')"
      ></el-input>
      <div  v-show="!show_add_member_goods" class="box">
      <div class="box-item" @click="showAdd()">
        <span v-show="pcSmgFrom === 'setting_member_point'">添加不积分商品</span>
        <span v-show="pcSmgFrom === 'setting_member_gift'">添加兑换礼品</span>
        <span v-show="pcSmgFrom === 'setting_member_day'">添加活动商品</span>
      </div>
      <div class="box-field">
        <el-input
        v-model="condition"
        id="selectGoods"
        ref="selectGoods"
        placeholder="请输入商品名称/条码/首字母/扫码"
        maxlength="60"
        @input="debouncedInput"
        prefix-icon="el-icon-search"
        @keydown.native.enter="inputSelectHandler('selectGoods')"
        >
        </el-input>
      </div>
     </div>
      <div class="com_psm18">
        <el-table
          v-loading="dataLoading"
          :data="goods_list"
          :empty-text="!dataLoading ? '暂无数据' : ' '"
          stripe
          style="width: 100%;height: 432px;font-size: 16px;overflow: auto"
        >
          <el-table-column prop="name" show-overflow-tooltip :key="1" width="314" label="商品名称"></el-table-column>
          <el-table-column
            prop="code"
            show-overflow-tooltip
            v-if="!(pcSmgFrom === 'setting_member_gift' && !show_add_member_goods)"
            :key="3"
            width="157"
            label="条码"
          ></el-table-column>
          <el-table-column
            prop="salePrice"
            show-overflow-tooltip
            v-if="!(pcSmgFrom === 'setting_member_gift' && !show_add_member_goods)"
            :key="4"
            width="94"
            label="售价"
          >
            <template slot-scope="scope">{{$toDecimalFormat(scope.row.salePrice, 2, true)}}</template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            v-if="pcSmgFrom === 'setting_member_gift'"
            :key="5"
            label="所需积分"
          >
            <template slot-scope="scope">
              <span v-if="show_add_member_goods">{{scope.row.point | nullToLine}}</span>
              <span v-else>
                <el-input
                  :disabled="del_goods_list.has(scope.row.fingerprint)"
                  v-model="scope.row.point"
                  @input="scope.row.point = $intMaxMinLimit({data: scope.row.point, max: 99999, min: 0});inputChanger(scope.$index)"
                  @blur="blurChanger(scope.row.point, scope.$index, 'point')"
                ></el-input>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            v-if="pcSmgFrom === 'setting_member_gift'"
            :key="6"
            label="兑换上限"
          >
            <template slot-scope="scope">
              <span v-if="show_add_member_goods">{{scope.row.toplimit | nullToLine}}</span>
              <span v-else>
                <el-input
                  :disabled="del_goods_list.has(scope.row.fingerprint)"
                  v-model="scope.row.toplimit"
                  @input="scope.row.toplimit = $intMaxMinLimit({data: scope.row.toplimit, max: 99999, min: 0});inputChanger(scope.$index)"
                  @blur="blurChanger(scope.row.toplimit, scope.$index, 'toplimit')"
                ></el-input>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            v-if="pcSmgFrom === 'setting_member_gift' && !show_add_member_goods"
            :key="7"
            prop="exchanged"
            label="已兑换"
          ></el-table-column>
          <el-table-column show-overflow-tooltip :key="8" label="操作">
            <template slot-scope="scope">
              <span v-show="pcSmgFrom === 'setting_member_gift'">
                <span v-show="show_add_member_goods">
                  <span
                    v-show="add_goods_list.has(scope.row.fingerprint)"
                    style="color: #E5E9EF;"
                  >添加</span>
                  <span
                    v-show="!add_goods_list.has(scope.row.fingerprint)"
                    class="addButton"
                    @click="showAddGoods(scope.row, scope.$index)"
                  >添加</span>
                </span>
                <span v-show="!show_add_member_goods">
                  <span v-show="!del_goods_list.has(scope.row.fingerprint)" style="color: #D5AA76;">
                    <span
                      @click="resetOneGoods(scope.row.fingerprint, scope.$index)"
                      style="cursor:pointer;"
                    >重置</span> |
                    <span
                      style="cursor:pointer;"
                      class="warning-color"
                      @click="deleteOneGoods(scope.row.fingerprint, scope.$index)"
                    >删除</span>
                  </span>
                  <span v-show="del_goods_list.has(scope.row.fingerprint)" class="addButton" style="color: #c0c4cc;">
                    <span>重置</span> |
                    <span class="warning-color" style="color: #c0c4cc;">删除</span>
                  </span>
                </span>
              </span>
              <span v-show="pcSmgFrom !== 'setting_member_gift'">
                <span v-show="show_add_member_goods">
                  <span
                    v-show="add_goods_list.has(scope.row.fingerprint)"
                    class="addButton warning-color"
                    @click="deleteTemporaryOneGoods(scope.row.fingerprint, scope.$index)"
                  >删除</span>
                  <span
                    v-show="!add_goods_list.has(scope.row.fingerprint)"
                    class="addButton"
                    @click="addOneGoods(scope.row.fingerprint, scope.$index)"
                  >添加</span>
                </span>
                <span v-show="!show_add_member_goods">
                  <span
                    v-show=" del_goods_list.has(scope.row.fingerprint)"
                    class="addButton"
                    @click="returnDeleteOneGoods(scope.row.fingerprint, scope.$index)"
                  >添加</span>
                  <span
                    v-show=" !del_goods_list.has(scope.row.fingerprint)"
                    class="addButton warning-color"
                    @click="deleteOneGoods(scope.row.fingerprint, scope.$index)"
                  >删除</span>
                </span>
              </span>
            </template>
          </el-table-column>
        </el-table>
        <div class="com_psm19">
          <div class="com_psm12">
            共
            <span >{{total}}</span> 款商品
          </div>
          <div class="com_psm13">
            <el-pagination
              layout="prev, pager, next"
              :total="total"
              @current-change="handleCurrentChange"
              :current-page.sync="pagenum"
              :page-size="limit"
              :page-count="total"
            ></el-pagination>
          </div>
        </div>
      </div>
      <div style="overflow: hidden;">
        <div
          v-show="show_add_member_goods"
          @click="closeAddSaveGoods()"
          class="com_psm22"
        >保存</div>
        <div
          v-show="!show_add_member_goods"
          @click="closeSaveGoods()"
          class="com_psm22"
        >确定</div>
        <div
          @click="show_add_member_goods ? closeAddMemberGoods() : closeMemberGoods()"
          class="com_psm21"
        >取消</div>
      </div>
      <div style="height: 18px;"></div>
    </div>
    <!-- 设置兑换礼品弹出框 -->
    <div
      v-show="showMember_add_goods"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
        @click="closeAddGoods()"
      ></div>
      <div
        style="position: relative;z-index: 800;margin: 0 auto;margin-top: 240px;background: #FFF;width: 430px;overflow: hidden;border-radius: 5px;"
      >
        <div
          style="width: 90%;
    text-align: left;
    font-size: 19px;
    font-weight: bold;
    color: #557485;
    margin: 16px 0px 0px 16px;"
        >
          设置兑换礼品
          <span
            style="float: right;
                font-size: xx-large;
                margin-top: -14px;
                cursor:pointer;
                margin-right: -12px;"
            @click="closeAddGoods()"
          >×</span>
        </div>
        <hr />
        <div
          style="
    text-align: left;
    font-size: 18px;
    color: #567485;
    margin-left: 25px;"
        >
          <div
            style="font-size: 16px;
    font-weight: bold;
    color: #B1C3CD;
    line-height: 24px;"
          >{{add_goods.name }}</div>
          <div class="row_style">
            所需积分
            <el-input
              v-model="add_goods.point"
              style="margin-left: 20px; width: 67%;"
              @input="add_goods.point = $intMaxMinLimit({data: add_goods.point, max: 99999, min: 0})"
              @change="checkInput('point')"
            ></el-input>
          </div>
          <div class="row_style">
            兑换上限
            <el-input
              style="margin-left: 20px; width: 67%;"
              v-model="add_goods.toplimit"
              @input="add_goods.toplimit = $intMaxMinLimit({data: add_goods.toplimit, max: 99999, min: 0})"
              @change="checkInput('toplimit')"
            ></el-input>
          </div>
        </div>
        <div style="overflow: hidden;margin-top: 15px;font-size: 24px;margin-bottom: 30px">
          <div
            class="com_psm22 margin_r45"
            @click="saveAddGoods()"
          >确定</div>
          <div
            class="com_psm21 margin_r45"
            @click="closeAddGoods()"
          >取消</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data() {
    return {
      show_add_member_goods: false,
      keyword: '',
      goods_list: [],
      row: 0,
      goods_fingerprint_list: [],
      goods_fingerprint_old_list: [],
      edit_exchange_goods_list: new Map(),
      add_exchange_goods_list: new Map(),
      del_goods_list: new Set(),
      add_goods_list: new Set(),
      total: 0,
      limit: 9,
      pagenum: 1,
      showMember_add_goods: false,
      add_goods: [],
      pinyin: false,
      antiShakingTimer: null,
      dataLoading: false,
      condition: ''
    };
  },
  filters: {
    nullToLine: val => !val ? '-' : val
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 输入框检查
     */
    checkInput(type) {
      if (
        isNaN(this.add_goods[type]) === true ||
        Number(this.add_goods[type]) < 1
      ) {
        this.add_goods[type] = '1';
      } else if (Number(this.add_goods[type]) > 99999) {
        this.add_goods[type] = '99999';
      } else if (
        Number(this.add_goods[type]).toFixed(0) !== this.add_goods[type]
      ) {
        this.add_goods[type] = Number(this.add_goods[type]).toFixed(0);
      } else {
        // todo
      }
    },
    debouncedInput: _.debounce(function(event) {
      this.searchActivityGoods(event);
    }, 600),
    searchActivityGoods() {
      this.condition = this.condition.replace(/[$']/g, '');
      this.handleCurrentChange(1);
    },
    inputChanger(n) {
      this.$set(this.goods_list, n, this.goods_list[n]);
    },
    blurChanger(v, n, p) {
      let val = v;
      if (isNaN(val) == true) {
        val = '1';
      }
      if (p === 'toplimit' && +val < +this.goods_list[n]['exchanged']) {
        val = this.goods_list[n]['exchanged'];
      }
      if (+val < 1) {
        val = '1';
      } else if (+val > 99999) {
        val = '99999';
      } else if (Number(val).toFixed(0) !== val) {
        val = Number(val).toFixed(0);
      } else {
        // todo
      }
      this.goods_list[n][p] = val;
      this.edit_exchange_goods_list.set(
        this.goods_list[n].fingerprint,
        this.goods_list[n]
      );
      this.$set(this.goods_list, n, this.goods_list[n]);
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    /**
     * 切换成添加状态的模态框
     */
    showAdd() {
      this.keyword = '';
      this.goods_list = [];
      this.show_add_member_goods = true;
      this.handleCurrentChange(1);
    },
    /**
     * 设置兑换礼品弹出添加
     */
    showAddGoods(goods, n) {
      this.row = n;
      this.add_goods = demo.t2json(JSON.stringify(goods));
      this.showMember_add_goods = true;
    },
    /**
     * 设置兑换礼品添加窗口保存
     */
    saveAddGoods() {
      this.add_goods_list.add(this.add_goods.fingerprint);
      this.checkInput('point');
      this.checkInput('toplimit');
      this.add_exchange_goods_list.set(
        this.add_goods.fingerprint,
        this.add_goods
      );
      this.$set(this.goods_list, this.row, this.add_goods);
      this.closeAddGoods();
    },
    /**
     * 设置兑换礼品添加窗口取消
     */
    closeAddGoods() {
      this.add_goods = [];
      this.showMember_add_goods = false;
    },
    getPcSmgFrom() {
      if (this.pcSmgFrom === 'setting_member_point') {
        return this.$rest.pc_vipDelGoods;
      }
      return this.$rest.pc_delProductVipDay;
    },
    judgeRespStatus(res) {
      if (res.data.code === '0') {
        demo.msg('success', '保存成功');
        this.closeMemberGoods();
      } else {
        demo.msg('warning', res.data.msg);
      }
    },
    /**
     * 删除模态框点击保存
     */
    closeSaveGoods() {
      let products = [];
      this.del_goods_list.forEach((e) => {
        products.push({ fingerprint: e });
      });
      if (this.pcSmgFrom !== 'setting_member_gift') {
        if (products.length > 0) {
          demo.$http
            .post(
              this.getPcSmgFrom(),
              {
                products: products,
                phone: this.sysUid,
                sysSid: this.sysSid,
                systemName: $config.systemName
              },
              {
                headers: {
                  'Content-Type': 'application/json'
                }
              }
            )
            .then((res) => {
              this.judgeRespStatus(res);
            }).catch(error => {
              console.log(error);
            });
        } else {
          this.closeMemberGoods();
        }
      } else {
        let edit_goods = [];
        this.edit_exchange_goods_list.forEach((value) => {
          edit_goods.push({
            fingerprint: value.fingerprint,
            point: value.point,
            toplimit: value.toplimit,
            exchanged: value.exchanged
          });
        });
        if (edit_goods.length > 0) {
          demo.$http
            .post(
              this.$rest.pc_editProduct,
              {
                products: edit_goods,
                phone: this.sysUid,
                sysSid: this.sysSid,
                systemName: $config.systemName,
                reviseUser: this.sysUid
              },
              {
                headers: {
                  'Content-Type': 'application/json'
                }
              }
            )
            .then((res) => {
              if (res.data.code === '0') {
                this.delGoodsDelProducts(products, true);
              } else {
                demo.msg('warning', res.data.msg);
              }
            }).catch(error => {
              console.log(error);
            });
        } else {
          this.delGoodsDelProducts(products, false);
        }
      }
    },
    /**
     * 删除删除兑换商品
     */
    delGoodsDelProducts(goods, successFlg) {
      if (goods.length > 0) {
        demo.$http
          .post(
            this.$rest.pc_delProducts,
            {
              products: goods,
              phone: this.sysUid,
              sysSid: this.sysSid,
              systemName: $config.systemName
            },
            {
              headers: {
                'Content-Type': 'application/json'
              }
            }
          )
          .then((res) => {
            if (res.data.code === '0') {
              demo.msg('success', '保存成功');
              this.closeMemberGoods();
            } else {
              demo.msg('warning', res.data.msg);
            }
          }).catch(error => {
            console.log(error);
          });
      } else {
        if (successFlg) {
          demo.msg('success', '保存成功');
        }
        this.closeMemberGoods();
      }
    },
    /**
     * 添加模态框点击保存
     */
    closeAddSaveGoods() {
      let products = [];
      let apiUrl = '';
      switch (this.pcSmgFrom) {
        case 'setting_member_gift':
          this.add_exchange_goods_list.forEach((e) => {
            products.push({
              fingerprint: e.fingerprint,
              point: e.point,
              toplimit: e.toplimit,
              exchanged: e.exchanged
            });
          });
          apiUrl = this.$rest.pc_addProducts;
          break;
        case 'setting_member_point':
          apiUrl = this.$rest.pc_vipSetGoods;
          this.add_goods_list.forEach((e) => {
            products.push({ fingerprint: e });
          });
          break;
        case 'setting_member_day':
          apiUrl = this.$rest.pc_addProductVipDay;
          this.add_goods_list.forEach((e) => {
            products.push({ fingerprint: e });
          });
          break;
        default:
          break;
      }
      if (products.length > 0) {
        demo.$http
          .post(
            apiUrl,
            {
              products: products,
              phone: this.sysUid,
              sysSid: this.sysSid,
              systemName: $config.systemName,
              createUser: this.loginInfo.uid
            },
            {
              headers: {
                'Content-Type': 'application/json'
              }
            }
          )
          .then((res) => {
            if (res.data.code === '0') {
              demo.msg('success', '保存成功');
              this.closeAddMemberGoods();
            } else {
              demo.msg('warning', res.data.msg);
            }
          }).catch(error => {
            console.log(error);
          });
      } else {
        this.closeAddMemberGoods();
      }
    },
    /**
     * 最终关闭回调
     */
    closeMemberGoods() {
      this.show_add_member_goods = false;
      this.keyword = '';
      this.goods_list = [];
      this.goods_fingerprint_list = [];
      this.del_goods_list = new Set();
      this.edit_exchange_goods_list = new Map();
      this.add_exchange_goods_list = new Map();
      this.add_goods_list = new Set();
      this.total = 0;
      this.limit = 9;
      this.pagenum = 1;
      this.SET_SHOW({ showMemberGoods: false });
    },
    /**
     * 添加模态框关闭回调
     */
    closeAddMemberGoods() {
      this.goods_list = [];
      this.add_goods_list = new Set();
      this.show_add_member_goods = false;
      this.add_exchange_goods_list = new Map();
      this.getGoods();
    },
    /**
     * 删除模态框删除按钮
     */
    deleteOneGoods(event, n) {
      this.del_goods_list.add(event);
      this.$set(this.goods_list, n, this.goods_list[n]);
    },
    /**
     * 删除模态框添加按钮
     */
    returnDeleteOneGoods(event, n) {
      var _this = this;
      _this.del_goods_list.forEach(function(item) {
        if (item === event) {
          _this.del_goods_list.delete(item);
        }
      });
      this.$set(_this.goods_list, n, _this.goods_list[n]);
    },
    /**
     * 添加模态框添加按钮
     */
    addOneGoods(event, n) {
      this.add_goods_list.add(event);
      this.$set(this.goods_list, n, this.goods_list[n]);
    },
    /**
     * 添加模态框删除按钮
     */
    deleteTemporaryOneGoods(event, n) {
      var _this = this;
      _this.add_goods_list.forEach(function(item) {
        if (item === event) {
          _this.add_goods_list.delete(item);
        }
      });
      _this.$set(_this.goods_list, n, _this.goods_list[n]);
    },
    /**
     * 删除模态框设置兑换礼品画面重置按钮
     */
    resetOneGoods(event, n) {
      this.goods_list[n].toplimit = 1;
      // this.goods_list[n].point = 0;
      this.goods_list[n].exchanged = 0;
      this.edit_exchange_goods_list.set(event, this.goods_list[n]);
      this.$set(this.goods_list, n, this.goods_list[n]);
    },
    judgeDatafingerp(res) {
      if (res.data.data.fingerprints) {
        var newfinger = res.data.data.fingerprints.replace(/'/g, '').split(',');
        return newfinger;
      }
      return [];
    },
    judgeOldfingerp(res) {
      if (this.edit_exchange_goods_list.size > 0) {
        var newProducts = res.data.data.products;
        newProducts.map((products) => {
          this.edit_exchange_goods_list.forEach((value, keye) => {
            if (keye === products.fingerprint) {
              products.point = value.point;
              products.toplimit = value.toplimit;
              products.exchanged = value.exchanged;
              return false;
            }
          });
          return products;
        });
        return newProducts;
      }
      return res.data.data.products;
    },
    /**
     * 初始化获取远程已删除商品数据
     */
    getGoods() {
      this.dataLoading = true;
      let apiUrl = '';
      switch (this.pcSmgFrom) {
        case 'setting_member_gift':
          apiUrl = this.$rest.pc_getProducts;
          break;
        case 'setting_member_point':
          apiUrl = this.$rest.pc_vipGetGoods;
          break;
        case 'setting_member_day':
          apiUrl = this.$rest.pc_getProductVipDay;
          break;
        default:
          break;
      }
      demo.$http
        .post(
          apiUrl,
          {
            phone: this.sysUid,
            sysSid: this.sysSid,
            systemName: $config.systemName
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        .then((res) => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
          console.log(res, 'ress');
          if (res.data.code === '0') {
            if (res.data.data) {
              if (this.pcSmgFrom === 'setting_member_gift') {
                this.goods_fingerprint_list = this.judgeDatafingerp(res);
                this.goods_fingerprint_old_list = this.judgeOldfingerp(res);
              } else {
                this.goods_fingerprint_list = res.data.data.map(
                  (e) => e.fingerprint
                );
              }
            }
            this.handleCurrentChange(1);
          } else {
            demo.msg('warning', '获取云端数据失败，请重试');
            setTimeout(() => {
              this.dataLoading = false;
            }, this.delayedTime);
          }
        }).catch(error => {
          console.log(error);
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
        });
    },
    /**
     * 初始化获取本地商品master数据
     */
    handleCurrentChange(val) {
      if (
        this.pcSmgFrom === 'setting_member_gift' &&
        !this.show_add_member_goods
      ) {
        this.limit = 6;
      } else {
        this.limit = 9;
      }
      this.pagenum = val;
      this.dataLoading = true;
      let data = {
        pset: '',
        type: '',
        condition: this.show_add_member_goods ? this.keyword : this.condition,
        limit: this.limit,
        fingerprint: this.goods_fingerprint_list,
        fingerprintFlg: this.show_add_member_goods,
        vipGoods: true,
        offset: Number((this.pagenum - 1) * this.limit)
      };
      console.log(data, 'data');
      goodService.search(data, (res) => {
        setTimeout(() => {
          this.dataLoading = false;
        }, this.delayedTime);
        console.log(demo.t2json(res), 'demo.t2json(res)');
        this.goods_list = demo.t2json(res);
        if (this.pcSmgFrom === 'setting_member_gift') {
          if (this.show_add_member_goods) {
            this.goods_list = this.goods_list.map((goods) => {
              if (this.add_exchange_goods_list.has(goods.fingerprint)) {
                let select_goods = this.add_exchange_goods_list.get(
                  goods.fingerprint
                );
                goods.point = select_goods.point;
                goods.toplimit = select_goods.toplimit;
              }
              return goods;
            });
          } else {
            console.log(this.goods_fingerprint_old_list, 'this.goods_fingerprint_old_list');
            this.goods_list = this.goods_list.map((goods) => {
              let select_goods = this.goods_fingerprint_old_list.find(
                (fingerprint_old) =>
                  fingerprint_old.fingerprint === goods.fingerprint
              );
              goods.point = this.getGoodsPoint(goods, select_goods);
              goods.toplimit = this.getGoodsTopLimit(goods, select_goods);
              goods.exchanged = this.getGoodsExchanged(goods, select_goods);
              return goods;
            });
          }
        }
      }, () => {
        setTimeout(() => {
          this.dataLoading = false;
        }, this.delayedTime);
      });
      goodService.searchCnt(data, (res) => {
        this.total = Number(demo.t2json(res)[0].cnt);
      });
    },
    getGoodsPoint(goods, select_goods) {
      return goods.point ? goods.point : select_goods.point;
    },
    getGoodsTopLimit(goods, select_goods) {
      return goods.toplimit ? goods.toplimit : select_goods.toplimit;
    },
    getGoodsExchanged(goods, select_goods) {
      return goods.exchanged ? goods.exchanged : select_goods.exchanged;
    }
  },
  watch: {
    keyword() {
      if (this.antiShakingTimer) {
        clearTimeout(this.antiShakingTimer);
      }
      this.antiShakingTimer = setTimeout(() => {
        this.handleCurrentChange(1);
      }, 300);
    },
    show_add_member_goods(falg) {
      this.$nextTick((_) => {
        falg ? this.$refs.addGoods.focus() : this.$refs.selectGoods.focus();
      })
    },
    showMemberGoods() {
      if (this.showMemberGoods) {
        this.condition = '';
        this.$nextTick((_) => {
          this.$refs.selectGoods.focus();
        })
        this.getGoods();
      } else {
        this.del_goods_list = new Set();
        this.add_goods_list = new Set();
        this.goods_fingerprint_list = [];
        this.goods_list = [];
      }
    }
  },
  computed: mapState({
    showMemberGoods: (state) => state.show.showMemberGoods,
    pcSmgFrom: (state) => state.show.pcSmgFrom,
    token: (state) => state.show.token,
    sysUid: (state) => state.show.sys_uid,
    loginInfo: (state) => state.show.loginInfo,
    delayedTime: state => state.show.delayedTime,
    sysSid: (state) => state.show.sys_sid
  })
};
</script>
