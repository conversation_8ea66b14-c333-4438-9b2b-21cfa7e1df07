
const addListener = function (el, type, fn) {
  el.addEventListener(type, fn, false);
};

/**
 * 限制输入长度 适用于直接写maxlength不好用的 取到内层input做限制
 */
let inputLength = {
  bind: function (oldel, binding) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    addListener(el, 'input', () => {
      let oldVal = el.value;
      if (binding.value !== undefined) {
        el.value = el.value.substring(0, binding.value);
      }
      if (oldVal !== el.value) {
        console.log(oldVal + '  ' + el.value, 'oldVal + el.value');
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 * 纯数字 只允许输入纯数字 不限位数
 */
let inputAllnum = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    addListener(el, 'input', () => {
      let oldVal = el.value;
      el.value = (el.value).replace(/[^\d]/g, '');
      if (oldVal !== el.value) { // 变更过值后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 * 计数 0开始 01 00这种不允许 纯数字 配合maxlength限制位数
 */
let inputCount = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    addListener(el, 'input', () => {
      let oldVal = el.value;
      el.value = (el.value).replace(/[^\d]/g, '').replace(/^[0][\d]/g, '0');
      if (oldVal !== el.value) { // 变更过值后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 *  整数  最大值 最小值
 *  {max : 999, min : 0 }
 */
let inputIntMaxMin = {
  bind: function (oldel, binding) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    addListener(el, 'input', () => {
      let oldVal = el.value;
      if (el.value > binding.value.max) {
        el.value = binding.value.max;
      } else if (el.value < binding.value.min) {
        el.value = binding.value.min;
      } else {
        // do something
        if (binding.value.min >= 0) {
          el.value = (el.value).replace(/[^\d]/g, '');
        }
        el.value = (el.value).replace(/[^-\d]/g, '').replace(/(?<!^)-/g, '').replace('-', '$*$').replace(/-/g, '').replace('$*$', '-')
          .replace(/^[0]{2,}/g, '0').replace(/^[-][0]{2,}/g, '-0').replace(/^[0][1-9]/g, '0').replace(/^[-][0][1-9]/g, '-0').replace(/^\./g, '0.').replace(/^[-]\./g, '-0.');
      }
      if (oldVal !== el.value) { // 变更过值后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 * 折扣率  纯数字整数 最大值100 最小值0 不含%
 */
let inputDiscountRate = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let actualMax = 100;
    addListener(el, 'input', () => {
      let oldVal = el.value;
      if (el.value > actualMax) {
        el.value = actualMax;
      }
      el.value = (el.value).replace(/[^\d]/g, '').replace(/^[0]{2,}/g, '0').replace(/^[0][1-9]/g, '0');

      if (oldVal !== el.value) { // 变更过值后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 * 会员折扣 最大值10.0,最小值0.1,一位小数
 */
let inputMemberDiscount = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let actualMax = 10.0;
    let actualMin = 0.1;
    addListener(el, 'input', () => {
      let oldVal = el.value;
      if (el.value > actualMax) {
        el.value = actualMax;
      } else if (el.value > 0 && el.value < actualMin) {
        el.value = actualMin;
      } else {
        el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0]{2,}/g, '0')
          .replace(/^[0][1-9]/g, '0').replace(/^\./g, '0.');
        if ((el.value).indexOf('.') !== -1) {
          el.value = el.value.substring(0, (el.value).indexOf('.') + 2);
        }
      }

      if (oldVal !== el.value) { // 变更过值后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 * 手机号 不传参默认11位 也可手动指定位数
 */
let inputPhone = {
  bind: function (oldel, binding) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let phoneLength = binding.value === undefined ? 11 : binding.value;
    addListener(el, 'input', () => {
      let oldVal = el.value;
      if ((el.value).length > phoneLength) {
        el.value = el.value.substring(0, phoneLength);
      }
      el.value = el.value.replace(/[^\d]/g, '');
      if (oldVal !== el.value) { // 变更过值或变更过长度后再次触发
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

let inputBarcode = { // 传参 {hasChar: 'true', length: '13'}
  bind: function (oldel, binding) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let phoneLength = binding.value === undefined ? 11 : binding.value;
    console.log(phoneLength, 'phoneLength+');
    addListener(el, 'input', () => {
      if ((el.value).length > phoneLength) {
        el.value = el.value.substring(0, phoneLength);
      }
      el.value = el.value.replace(/[^\d]/g, '');
    });
  }
};

/**
 *  金额默认两位小数,最小值0.00,最大值99999.99
 */
let inputPrice = {
  bind: function (oldel, binding, vnode) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let decimals = 2;
    let actualMax = 999999.99;
    // let actualMin = 0.00;
    addListener(el, 'input', () => { // el.value string
      let oldVal = el.value;
      if (el.value > actualMax) {
        el.value = actualMax;
      }
      el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
      if ((el.value).indexOf('.') !== -1) {
        el.value = el.value.substring(0, (el.value).indexOf('.') + decimals + 1); // 保留几位小数
      }
      if (oldVal !== el.value) {
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 *  进货默认两位小数,最小值0.00,最大值99999.999999
 */
let inputPurPrice = {
  bind: function (oldel, binding, vnode) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let decimals = 6;
    let actualMax = 999999.999999;
    // let actualMin = 0.00;
    addListener(el, 'input', () => { // el.value string
      let oldVal = el.value;
      if (el.value > actualMax) {
        el.value = actualMax;
      }
      el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
      if ((el.value).indexOf('.') !== -1) {
        el.value = el.value.substring(0, (el.value).indexOf('.') + decimals + 1); // 保留几位小数
      }
      if (oldVal !== el.value) {
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 *  支付金额默认两位小数,最小值0.00,最大值9999999.99
 */
let inputPayPrice = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let decimals = 2;
    let actualMax = 9999999.99;
    // let actualMin = 0.00;
    addListener(el, 'input', () => { // el.value string
      let oldVal = el.value;
      if (el.value > actualMax) {
        el.value = actualMax;
      }
      el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
      if ((el.value).indexOf('.') !== -1) {
        el.value = el.value.substring(0, (el.value).indexOf('.') + decimals + 1); // 保留几位小数
      }
      if (oldVal !== el.value) {
        el.dispatchEvent(new Event('input'));
      }
    });
  }
};

/**
 *  金额默认两位小数,最小值0.00,最大值999999.99
 */

let inputMemberRecharge = {
  bind: function (oldel) {
    let el;
    if (oldel.tagName.toLowerCase() !== 'input') {
      el = oldel.getElementsByTagName('input')[0];
    } else {
      el = oldel;
    }
    let actualMax = 999999.99;
    addListener(el, 'input', (e) => { // el.value string
      let oldVal = e.target.value;
      if (e.target.value > actualMax) {
        e.target.value = actualMax;
      }
      e.target.value = (e.target.value).match(/^\d*(\.?\d{0,2})/g)[0] || '';
      if (oldVal !== e.target.value) {
        el.dispatchEvent(new Event('input'));
      }
      return e.target.value;
    });
  }
};

/**
 *  自定义折扣/自定义减单 根据value判断
 *  折扣: 0.1 - 10.0 固定一位小数
 *  减单: 0.01 - 99999.99 固定两位小数
 */
// let inputDiscountOrPrice = {
//   bind: function (el, binding) {
//     if (el.tagName.toLowerCase() !== 'input') {
//       el = el.getElementsByTagName('input')[0]
//     }
//     // let actualMin = 0.00;
//     addListener(el, 'input', () => { // el.value string
//       let oldVal = el.value;
//       if (binding.value === 'discount') { // 自定义折扣
//         let actualMax = 10.0;
//         let actualMin = 0.1;
//         if (el.value > actualMax) {
//           el.value = actualMax;
//         } else if (el.value > 0 && el.value < actualMin) {
//           el.value = actualMin;
//         } else {
//           el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0]{2,}/g, '0')
//             .replace(/^[0][1-9]/g, '0').replace(/^\./g, '0.');
//           if ((el.value).indexOf('.') !== -1) {
//             el.value = el.value.substring(0, (el.value).indexOf('.') + 2);
//           }
//         }
//       } else { // 自定义减价
//         let decimals = 2;
//         let actualMax = 99999.99;
//         if (el.value > actualMax) {
//           el.value = actualMax;
//         }
//         el.value = (el.value).replace(/[^.\d]/g, '').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^[0][\d]/g, '0').replace(/^\./g, '0.');
//         if ((el.value).indexOf('.') !== -1) {
//           el.value = el.value.substring(0, (el.value).indexOf('.') + decimals + 1); // 保留几位小数
//         }
//       }

//       if (oldVal !== el.value) {
//         el.dispatchEvent(new Event('input'));
//       }
//     });
//   }
// }

export default {
  inputLength,
  inputAllnum,
  inputCount,
  inputIntMaxMin,
  inputPhone,
  inputBarcode,
  inputDiscountRate,
  inputMemberDiscount,
  inputMemberRecharge,
  inputPrice,
  inputPurPrice,
  inputPayPrice
};
