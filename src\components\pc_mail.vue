<template>
  <div>
    <el-input
      placeholder="请输入商品名称/条码/首字母"
      v-model="keyword"
      clearable
      style="font-size:16px"
      @compositionstart='pinyin = true'
      @compositionend='pinyin = false'
      @input="keyword = keyword.replace(/[$']/g, '')"
    >
      <el-button
        slot="append"
        @click="searchGoods"
        style="font-size:16px"
      >搜索</el-button>
    </el-input>
    <div class="table_container table_new_ui">
      <el-table
        :data="tableData"
        stripe
        v-loading="dataLoading"
        :empty-text="!dataLoading ? '暂无数据' : ' '"
        style="width: 100%"
        :header-cell-style="headerStyle"
        :cell-style="headerStyle"
        :height="451"
        class="tableBox_mail"
      >
        <el-table-column
          prop="name"
          label="商品名称"
          width="330"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="code"
          width="150"
          label="条码"
          show-overflow-tooltip
          align="center"
        >
        </el-table-column>
        <el-table-column label="售价" width="90px" show-overflow-tooltip align="right">
          <template slot-scope="scope">
            <div style="padding-right: 15px">
              {{ Number(scope.row.salePrice).toFixed(2) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div
              style="color: #d5aa76;cursor:pointer"
              @click="clickMail(scope.row)"
            >寄件</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom">
        <div style="font-size:16px">
          共<span style="color:#d5aa76;font-size:16px"> {{total}} </span>款商品
        </div>
        <div></div>
        <el-pagination
          layout="prev, pager, next"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page="pagenum"
          :page-size="limit"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      title="寄件数量"
      :visible.sync="showMail_num"
      append-to-body
      @open='initMailNum'
      @close='closeMailNum'
      width="440px"
      destroy-on-close
      :close-on-click-modal='false'
    >
      <div class="mail_dialog">
        <div class="goods_name">{{goods.name}}</div>
        <div style="display:flex;margin-top:18px;align-items:center;justify-content:space-between">
          <div style="margin-right:30px;margin-left:20px;font-weight: bold;font-size:16px;color: #567485">数量</div>
          <el-input-number
            ref="mailInput"
            v-model="mail_num"
            v-focus-select="'focusSelect'"
            :min="1"
            :max="99999"
            placeholder="请输入寄存数量"
            @focus="$inputFocusSelectValue($event)"
            @input.native="controlValRange()"
          ></el-input-number>
        </div>
        <div class="ticket-container" @click="selectPrintTicket">
          <img v-if="checkPrintTicket" src="../image/pc_goods_checkbox2.png" class="cb">
          <img v-else src="../image/pc_goods_checkbox1.png" class="cb">
          <div class="cb-text">寄件完成打印小票</div>
        </div>
        <div class="btn_container">
          <div
            class="btn_cancel"
            @click="closeMailNum"
          >取消</div>
          <div
            class="btn_confirm"
            @click="confirm"
          >确定</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Input, Table, TableColumn, Dialog } from 'element-ui';
import { showToast, timeStampToDate } from '../utils/util.js';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { INVENTORY_DEAL_TYPE } from '@/utils/inventoryDealType';
export default {
  props: {
    vip_id: {
      type: Number,
      required: true
    },
    vip_name: {
      type: String,
      required: true
    }
  },
  components: {
    [Input.name]: Input,
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Dialog.name]: Dialog
  },
  data () {
    return {
      dataLoading: false,
      keyword: '',
      tableData: [],
      pagenum: 1,
      limit: 8,
      total: 0,
      showMail_num: false,
      goods_name: '',
      mail_num: 1,
      goods: {},
      confirmClick: false,
      pinyin: false,
      checkPrintTicket: false
    };
  },
  mounted () {
    this.keyword = '';
    this.searchGoods();
    this.listenKeyDown();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    headerStyle ({ columnIndex }) {
      if (columnIndex === 2 || columnIndex === 3 || columnIndex === 4) {
        return 'text-align:center';
      }
    },
    /**
     * 监听回车键
     */
    listenKeyDown () {
      document.onkeydown = e => {
        if (e.keyCode === 13) {
          this.searchGoods();
        }
      };
    },
    /**
     * 勾选/取消打印小票
     */
    selectPrintTicket() {
      this.checkPrintTicket = !this.checkPrintTicket;
      settingService.put({ key: settingService.key.mailPrintTicket, value: this.checkPrintTicket });
      demo.actionLog({ page: 'pc_mail', action: 'printTicket', description: `会员_寄件_打印小票开关：${this.checkPrintTicket ? '开' : '关'}` });
    },
    /**
     * 点击寄件
     */
    clickMail (goods) {
      this.goods = goods;
      this.showMail_num = true;
      // 取出是否勾选打印小票配置
      settingService.get({ key: settingService.key.mailPrintTicket }, res => {
        console.log('取出寄件是否勾选打印小票配置', res);
        this.checkPrintTicket = (res[0] && res[0].value === 'true');
      });
    },
    /**
     * 关闭数量弹窗
     */
    closeMailNum () {
      this.showMail_num = false;
    },
    /**
     * 寄存库存对应
     */
    changeCurStock () {
      var info = this.loginInfo;
      var data = {
        uid: info.uid,
        good_fingerprint: this.goods.fingerprint,
        updateStock: 1,
        diff_amt:
          this.goods.purPrice * this.mail_num,
        account_qty: +this.goods.curStock,
        actual_qty: +this.mail_num + (+this.goods.curStock),
        price: this.goods.purPrice,
        remark: this.vip_name,
        dealType: INVENTORY_DEAL_TYPE.REGISTER_INVENTORY
      };
      let inventoryItems = [_.cloneDeep(this.goods)];
      inventoryItems.forEach(item => {
        item.accountQty = +item.curStock;
        item.actualQty = item.curStock + this.mail_num;
        item.price = item.purPrice;
        item.goodFingerprint = item.fingerprint;
      });
      var params = {
        exec: 1,
        dealType: INVENTORY_DEAL_TYPE.REGISTER_INVENTORY,
        updateStock: 1,
        inventoryItems: inventoryItems,
        remark: this.vip_name
      }
      inventoryService.insert(params, () => {
        // do nothing
      }, () => {
        // do nothing
      });
    },
    /**
     * 点击确定提交数据
     */
    confirm () {
      if (this.confirmClick) {
        return;
      }
      console.log('寄件商品', this.goods);
      const param = {
        products: [{
          fingerprint: this.goods.fingerprint,
          goodsName: this.goods.name,
          count: this.mail_num,
          remark: ''
        }],
        inOut: 1,
        vipId: this.vip_id,
        phone: this.sysUid,
        sysSid: this.sysSid,
        systemName: $config.systemName,
        createUser: this.loginInfo.uid,
        store: demo.$store.state.show.username
      };
      console.log('==寄件参数', param);
      this.confirmClick = true;
      demo.$http
        .post(this.$rest.addAccessProduct, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          const res = rs.data;
          console.log('==寄件结果', res);
          this.confirmClick = false;
          if (res.code === '0') {
            // 增库存 start
            this.changeCurStock();
            // 增库存 end
            demo.msg('success', '寄件成功');
            this.closeMailNum();
            this.$emit('dismissMailDialog');
            // 判断是否打印小票
            if (this.checkPrintTicket) {
              const data = {
                time: timeStampToDate(null, 'yyyy-MM-dd HH:mm:ss'),
                operator: demo.$store.state.show.loginInfo.employeeNumber || '管理员',
                vipName: this.vip_name,
                printTime: timeStampToDate(null, 'yyyy-MM-dd HH:mm:ss'),
                goods: [{
                  "商品名称": this.goods.name,
                  "数量": ' '
                }, {
                  "商品名称": ' ',
                  "数量": this.mail_num.toString()
                }]
              };
              pos.printer.printMailTicket(data, '寄件单');
            }
            return;
          }
          showToast(this, res.msg);
        })
        .catch(() => {
          this.confirmClick = false;
        });
    },
    /**
     * 控制输入值内容和范围
     */
    controlValRange() {
      const value = this.$refs['mailInput'].userInput;
      this.mail_num = this.$intMaxMinLimit({data: value, max: 99999, min: 1});
      this.$refs['mailInput'].userInput = this.mail_num;
    },
    /**
     * 初始化寄存数量
     */
    initMailNum () {
      this.mail_num = 1;
    },
    /**
     * 点击页码刷新数据
     */
    handleCurrentChange (page) {
      this.pagenum = page;
      this.getData();
    },
    searchGoods () {
      this.pagenum = 1;
      this.getData();
    },
    /**
     * 搜素商品
    */
    getData () {
      this.dataLoading = true;
      const param = {
        pset: '',
        type: '',
        condition: this.keyword,
        limit: this.limit,
        offset: Number((this.pagenum - 1) * this.limit)
      };
      console.log('==参数', param);
      goodService.search(param, res => {
        setTimeout(() => {
          this.dataLoading = false;
        }, this.delayedTime);
        var json = demo.t2json(res);
        console.log('===查询结果', json);
        this.tableData = json;
      }, () => {
        setTimeout(() => {
          this.dataLoading = false;
        }, this.delayedTime);
      });
      var data = { pset: '', type: '', condition: this.keyword };
      goodService.searchCnt(data, res => {
        this.total = Number(demo.t2json(res)[0].cnt);
        console.log('===总数', this.total);
      });
    }
  },
  computed: mapState({
    loginInfo: (state) => state.show.loginInfo,
    delayedTime: state => state.show.delayedTime,
    sysSid: (state) => state.show.sys_sid,
    sysUid: (state) => state.show.sys_uid
  })
};
</script>

<style lang="less">
  .el-input-number__decrease {
    width: 42px !important;
    height: 42px !important;
  }
  .el-input-number__increase {
    width: 42px !important;
    height: 42px !important;
  }
  .tableBox_mail th {
    background: #FAFBFC;
    font-size: 16px;
    padding: 0px;
  }
  .tableBox_mail td {
    font-size: 16px;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #8298A6;
    font-size: 22px;
    font-weight: bold;
  }
  button:not(:disabled), [type="button"]:not(:disabled), [type="reset"]:not(:disabled), [type="submit"]:not(:disabled) {
    cursor: pointer;
    outline: none;
  }
  .table_container{
    border: 1px solid #E5E7EB;
    margin-top: 15px;
  }
  .table_bottom{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
  }

  .ticket-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    margin: 0 80px;
    cursor: pointer;
    .cb {
      width: 22px;
      height: 22px;
    }
    .cb-text {
      font-size: 17px;
      margin-left: 8px;
      user-select: none;
    }
  }

  .btn_container{
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-left:20px
  }
  .btn_cancel{
    flex: 1;
    height: 44px;
    line-height: 44px;
    border: 1px solid @themeBackGroundColor;
    border-radius: 4px;
    text-align: center;
    color: @themeBackGroundColor;
    cursor: pointer;
    font-size:16px
  }

   .btn_confirm{
    flex: 1;
    height: 44px;
    line-height: 44px;
    border-radius: 4px;
    text-align: center;
    color: white;
    background: linear-gradient(90deg, #D1B989 0%, #B39959 100%);
    margin-left: 20px;
    cursor: pointer;
    font-size:16px
  }

.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
  color: #557485;
}
.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #B1C3CD;
  margin-left:20px
}
.el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: #B39959;
  outline: 0;
}
.el-input-number {
  position: relative;
  display: inline-block;
  width: 260px;
  line-height: 38px;
}
.el-input__inner {
    background-color: white;
}
.el-input-group__append {
  border-left: 0;
  background: linear-gradient(90deg, #D1B989 0%, #B39959 100%);
  color: white;
}
.table_new_ui .el-table__row {
  height: 50px;
}
.table_new_ui .el-table th > .cell {
  padding-left: 20px;
  padding-right: 0;
  height: 50px;
  line-height: 50px;
  background: #F5F7FA;
}
.table_new_ui .el-table td {
  padding-left:20px;
  color:@themeFontColor;
}
.table_new_ui .el-table .cell {
  padding-right: 0;
  padding-left: 0;
}
</style>
