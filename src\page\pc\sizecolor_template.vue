<style lang="less" scoped>
.siz_tep {
  width: 100%;height: 100%;background: #F5F8FB;font-size: 16px;color: #567485;overflow: hidden;
}
.siz_tep1 {
  width: calc(100% - 20px);margin: 10px auto;height: calc(100% - 70px);
  background: #FFF;overflow-y: scroll;border: 1px solid #E3E6EB;
}
</style>
<template>
  <div class="siz_tep">
    <div class="siz_tep1">
      <div style="overflow: hidden;">
        <div style="float: left;width: 17px;height: 17px;background: #d5aa76;border-radius: 50%;overflow: hidden;text-align: center;color: #FFF;line-height: 17px;font-size: 13px;font-weight: bold;margin-top: 27px;margin-left: 25px;">i</div>
        <div style="float: left;margin-left: 12px;font-size: 16px;color: #567485;line-height: 16px;margin-top: 27px;">
          选择后将会添加到【规格管理】中，系统自动覆盖重复值。
        </div>
      </div>
      <div v-for="(da, index) in specsList" :key="index" style="width: calc(100% - 48px);margin-left: 24px;margin-top: 14px;">
        <div style="height: 48px;background: #F5F7FA;cursor: pointer;">
          <div @click="choose(index, '')" style="float: left;overflow: hidden;">
            <img v-show="da.select === false" src="../../image/zgzn-pos/pc_goods_checkbox1.png" style="width: 20px;height: 20px;margin-top: 14px;margin-left: 14px;float: left;">
            <img v-show="da.select === ''" src="../../image/zgzn-pos/pc_goods_checkbox3.png" style="width: 20px;height: 20px;margin-top: 14px;margin-left: 14px;float: left;">
            <img v-show="da.select === true" src="../../image/zgzn-pos/pc_goods_checkbox2.png" style="width: 20px;height: 20px;margin-top: 14px;margin-left: 14px;float: left;">
            <div style="float: left;line-height: 48px;margin-left: 24px;">{{da.name}}</div>
          </div>
          <div @click="select_index === index ? select_index = -1 : select_index = index" style="float: right;margin-right: 20px;line-height: 48px;width: calc(100% - 200px);text-align: right;">
            <i v-show="select_index === index" class="el-icon-arrow-up"></i>
            <i v-show="select_index !== index" class="el-icon-arrow-down"></i>
          </div>
        </div>
        <div v-show="select_index === index" style="width: 100%;border: 1px solid #F5F7FA;border-top: none;">
          <div style="width: 100%;height: 5px;"></div>
          <div style="overflow: hidden;width: 100%;">
            <div style="min-width: 126px;height: 44px;margin: 5px;float: left;" v-for="(d, index1) in da.list" :key="index1" @click="choose(index, index1)">
              <img v-show="d.select === false" src="../../image/zgzn-pos/pc_goods_checkbox1.png" style="width: 20px;height: 20px;margin-top: 12px;margin-left: 12px;float: left;">
              <img v-show="d.select === true" src="../../image/zgzn-pos/pc_goods_checkbox2.png" style="width: 20px;height: 20px;margin-top: 12px;margin-left: 12px;float: left;">
              <div style="float: left;line-height: 44px;margin-left: 8px;">{{d.name}}</div>
            </div>
          </div>
          <div style="width: 100%;height: 5px;"></div>
        </div>
      </div>
      <div style="height: 80px;width: 100%;"></div>
      <div style="position: fixed;bottom: 11px;width: calc(100% - 22px);height: 70px;background: #FFF;margin-left: 0px;">
        <div @click="toSizeSetting()" style="width: 200px;height: 47px;margin: 0 auto;margin-top: 11px;text-align: center;
          cursor: pointer;line-height: 47px;font-size: 16px;color: #FFF;background: #d5aa76;border-radius: 4px;">保存</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
import { SET_SHOW } from '@/store/show';
export default {
  data() {
    return {
      show: false,
      template: true,
      select_index: '',
      specsList: []
    };
  },
  mounted() {
    this.getSpecsTemplate();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    // 表格的checkbox，选择以后val会实时响应
    toSizeSetting() {
      var sub_data = [];
      for (var i = 0; i < this.specsList.length; i++) {
        if (this.specsList[i].select !== false) {
          var mid = {};
          mid = _.cloneDeep(this.specsList[i]);
          mid.specsList = [];
          for (var j = 0; j < this.specsList[i].list.length; j++) {
            if (this.specsList[i].list[j].select === true) {
              mid.specsList.push(this.specsList[i].list[j]);
            }
          }
          sub_data.push(mid);
        }
      }
      if (sub_data.length === 0) {
        demo.msg('warning', '请先选择要快捷新增的规格！');
        return;
      }
      demo.$http.post(demo.$rest.saveSpecs, sub_data).then(res => {
        console.log(res, 309303);
        if (res.data.code === 200) {
          demo.msg('success', '快捷新增成功！');
          this.SET_SHOW({isSizeTemplate: false});
          this.SET_SHOW({isSizeSetting: true});
        } else {
          demo.msg('warning', res.data.msg);
        }
      }).catch((error) => {
        console.log(error, '快捷新增保存error');
        demo.msg('warning', '快捷新增保存error');
      });
    },
    getSpecsTemplate() {
      demo.$http.get(demo.$rest.getTemplateSpecs).then(res => {
        if (res.data.code === 200) {
          for (var i = 0; i < res.data.data.length; i++) {
            res.data.data[i].select = false;
            for (var j = 0; j < res.data.data[i].list.length; j++) {
              res.data.data[i].list[j].select = false;
            }
          }
          this.specsList = res.data.data;
        } else {
          demo.msg('warning', res.data.msg);
        }
      }).catch((error) => {
        console.log(error, '拉取快捷新增规格模板失败');
        demo.msg('warning', '拉取快捷新增规格模板失败');
      });
    },
    handleSpecs(m, bool) {
      this.specsList[m].select = bool;
      for (var i = 0; i < this.specsList[m].list.length; i++) {
        this.specsList[m].list[i].select = bool;
      }
      this.$forceUpdate();
    },
    choose(m, n) {
      console.log(this.specsList, '1');
      console.log(this.specsList[m].list[n], '2');
      console.log(m, 'mmmmmm');
      console.log(n, 'nnnnnn');
      if (n !== '') {
        this.specsList[m].list[n].select = this.specsList[m].list[n].select === false;
        var spec_select = '';
        for (var i = 0; i < this.specsList[m].list.length; i++) {
          spec_select += this.specsList[m].list[i].select;
        }
        console.log(spec_select, 'spec_select');
        console.log(this.specsList[m].list[n], 'spec_select');
        if (spec_select.indexOf('true') === -1) {
          this.specsList[m].select = false;
        } else if (spec_select.indexOf('false') === -1) {
          this.specsList[m].select = true;
        } else {
          this.specsList[m].select = '';
        }
        this.$forceUpdate();
      // } else if (this.specsList[m].select === true || this.specsList[m].select === false) {
      //   this.handleSpecs(m, !this.specsList[m].select);
      // }
      } else {
        var select = !(this.specsList[m].select === true);
        this.handleSpecs(m, select);
      }
    }
  }
};
</script>
