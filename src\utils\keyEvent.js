export class Keyevent {
  validKeys = [
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    '0',
    '.',
    'Escape',
    'Enter',
    'ArrowRight',
    'ArrowLeft',
    'F1',
    'F2',
    'F3',
    'F4',
    'F5',
    'F6',
    'F7',
    'Backspace',
    'Delete',
    ' '
  ]; // 定义有效按键集
  toleranceThreshold = 30; // 定义阈值
  tolerancePercentage = 0.2; // 容差百分比, 将忽略的百分比
  keyEventsToConsider = 18; // 扫码长度
  keyDownTimestamps = {}; // 存储按键按下的时间戳
  keyEventRecords = []; // 存储按键事件记录
  keyEventing = true; // 用于判断两个按键如果间隔很短判断为扫码
  callBack = null; // 结果回调函数

  constructor(callBack) {
    this.callBack = callBack;
  }
  // 开始监听
  start = () => {
    document.addEventListener('keydown', this.handleKeyDown, false);
    document.addEventListener('keyup', this.handleKeyup, false);
  };
  // 处理键盘按下事件
  handleKeyDown = event => {
    if (!this.validKeys.includes(event.key)) {
      return;
    }
    // 如果当前按键未记录过按下时间，则记录按下时间
    if (!this.keyDownTimestamps.hasOwnProperty(event.key)) {
      this.keyDownTimestamps[event.key] = new Date().getTime();
    }
    this.keyEventing = false;
  };
  // 处理键盘抬起事件
  handleKeyup = event => {
    if (!this.validKeys.includes(event.key) || !this.keyDownTimestamps[event.key]) {
      return;
    }
    this.keyEventing = true;
    const currentTime = new Date().getTime();
    // 计算按键持续时间
    const duration = currentTime - this.keyDownTimestamps[event.key];
    const item = {
      key: event.key,
      duration,
      downTime: this.keyDownTimestamps[event.key],
      upTime: currentTime
    };
    // 记录按键事件详情，并更新显示
    this.keyEventRecords.unshift(item);
    // 删除已处理的按键记录
    delete this.keyDownTimestamps[event.key];
    const recentKeyEvents = this.keyEventRecords.slice(0, this.keyEventsToConsider + 1);
    const averageDuration = this.calculateAverageDuration(recentKeyEvents);
    CefSharp.PostMessage(`间隔${averageDuration},${JSON.stringify(recentKeyEvents)}`);
    // 根据均值判断是否为扫码枪输入
    if (averageDuration <= this.toleranceThreshold) {
      // 扫码枪输入
      // 处理 Enter 键的特殊逻辑
      if (event.key === 'Enter') {
        // 重置按键事件记录
        this.keyEventRecords = [];
        if (recentKeyEvents.length > this.keyEventsToConsider && this.callBack) {
          // 当前存储的值
          const code = recentKeyEvents
            .filter(item => /^\d+$|^\.$/.test(item.key))
            .reverse()
            .map(item => item.key)
            .join('');
          this.callBack({ type: 1, code });
        }
      }
    } else {
      const code = event.key;
      setTimeout(() => {
        if (this.keyEventing) {
          // 手动输入
          this.callBack({ type: 2, code });
          // 重置按键事件记录
          this.keyEventRecords = [];
        }
      }, 10);
    }
  };
  // 计算按键持续时间的切尾均值
  calculateAverageDuration = keyEvents => {
    let digitKeyEvents = JSON.parse(JSON.stringify(keyEvents));
    digitKeyEvents.sort((a, b) => a.duration - b.duration);
    const sliceCount = parseInt(keyEvents.length * this.tolerancePercentage);

    if (digitKeyEvents.length >= this.keyEventsToConsider) {
      digitKeyEvents = digitKeyEvents.slice(1, -sliceCount);
    }
    const totalDuration = digitKeyEvents.reduce((sum, item) => sum + item.duration, 0);
    return totalDuration / (digitKeyEvents.length || 1);
  };
  // 结束监听
  stop = () => {
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('keyup', this.handleKeyup);
    this.keyDownTimestamps = {};
    this.keyEventRecords = [];
    this.callBack = null;
  };
}
export default {
  Keyevent
};
