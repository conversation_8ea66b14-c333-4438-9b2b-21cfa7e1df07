<template>
  <!-- 过期预警 -->
  <div v-loading.fullscreen.lock="loading" element-loading-background="rgba(0, 0, 0, 0.7)">
    <div class="re_deposit_record_container">
      <div
        v-show="showEditDialog"
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;">
        <div style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"></div>
        <div class="early_warning_div_container">
          <div class="early_warning_div_header">
            <div><span style="font-size: 18px;font-weight: bold;">修改</span></div>
            <div @click="showEditDialog = false">
              <span class="early_warning_div_close">×</span>
            </div>
          </div>
          <div style="padding: 20px;box-sizing:border-box">
            <div style="display:flex;justify-content: center;align-items:center;">
              <div style="width: 100px;font-size:18px;font-weight:bold;">
                <span class="early_warning_div_span"><span style="color: #FF6159">*</span>生产日期</span>
              </div>
              <el-date-picker
                type="date"
                class="manufactureDateClass"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择生产日期"
                v-model="manufactureDate"
                @change="calcBZQ"
              >
              </el-date-picker>
            </div>
            <div style="display:flex;justify-content: center;align-items:center;margin-top: 20px;">
              <div style="width: 100px;font-size:18px;font-weight:bold;">
                <span class="early_warning_div_span"><span style="color: #FF6159">*</span>保质期(天)</span>
              </div>
              <div class="bzq-container-stock">
                <el-input v-model.trim="expiryDays"
                  style="width: 260px;"
                  @input="expiryDays = $intMaxMinLimit({data: expiryDays, max: 9999, min: 0});calcBZQ()"
                  placeholder="请输入保质期">
                </el-input>
                <div class="calc-text" @click="showBzqDiffDialog = true">计算</div>
              </div>
            </div>
          </div>
          <div class="zhi">有效期至：{{ calcGuaranteeDate }}</div>
          <div class="com_pad273">
            <div class="com_early_btn1" @click="showEditDialog = false">取消</div>
            <div class="com_early_btn2" @click="batchEdit()">确定</div>
          </div>
        </div>
      </div>
      <div>
        <div class="re_top">
          <div class="re_top_left_container">
            <div style='float: left;'>
              <div
                class='pc_report2'
                :style="inputing_keyword ? 'border-color: #BDA16A' : ''"
              >
                <input
                  @focus='inputing_keyword = true'
                  @blur='inputing_keyword = false'
                  type='text'
                  placeholder='商品名称/条码/首字母/扫码'
                  v-model='keywordGoods'
                  id='goods_keyword_report'
                  @compositionstart='pinyin = true'
                  @compositionend='pinyin = false'
                  @input="keywordGoods = $goodsNameFormat(keywordGoods)"
                  @keydown.enter="inputSelectHandler('goods_keyword_report')"
                />
                <img
                  class='pc_report1'
                  v-show="keywordGoods != ''"
                  @click="focusInput('goods_keyword_report')"
                  src='../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div class="re_top_left">
              <vCjSelect @searchChange="searchChange"></vCjSelect>
            </div>
            <div class="re_top_left">
              <el-cascader
                v-model="detailValue"
                :options="category_list"
                :props="{ checkStrictly: true }"
                placeholder="分类选择">
              </el-cascader>
            </div>
            <div class='pc_report3'>
              <input
                placeholder='10'
                type='text'
                maxlength="4"
                v-model="overdueDate"
                @input="overdueDate = $intMaxMinLimit({data: overdueDate, max: 9999, min: 0, decimals: 0})"
              />
            </div>
            <div>
              <span class="text_font">天内过期</span>
            </div>
            <div @click="goods_pagenum = 1;goodsStock('search', true)" class="psk_det33">查询</div>
            <div style="margin-left: 10px">
              <span style="color: #BDA169;font-style: normal;font-weight: 500;font-size: 18px;line-height: 44px;">温馨提示</span>
              <el-tooltip class="item" effect="dark" content="过期预警根据保质期和进货时填写的最新生产日期来计算商品的保质状态，商品的实际生产日期与此不一致时将造成预警信息不准确，报表数据仅供参考，请知悉！" placement="bottom">
                <svg style="margin-bottom: 5px;cursor: pointer;" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22ZM12 20C14.1217 20 16.1566 19.1571 17.6569 17.6569C19.1571 16.1566 20 14.1217 20 12C20 9.87827 19.1571 7.84344 17.6569 6.34315C16.1566 4.84285 14.1217 4 12 4C9.87827 4 7.84344 4.84285 6.34315 6.34315C4.84285 7.84344 4 9.87827 4 12C4 14.1217 4.84285 16.1566 6.34315 17.6569C7.84344 19.1571 9.87827 20 12 20ZM11 15H13V17H11V15ZM13 13.355V14H11V12.5C11 12.2348 11.1054 11.9804 11.2929 11.7929C11.4804 11.6054 11.7348 11.5 12 11.5C12.2841 11.5 12.5623 11.4193 12.8023 11.2673C13.0423 11.1154 13.2343 10.8984 13.3558 10.6416C13.4773 10.3848 13.5234 10.0988 13.4887 9.81684C13.454 9.53489 13.34 9.26858 13.1598 9.04891C12.9797 8.82924 12.7409 8.66523 12.4712 8.57597C12.2015 8.48671 11.912 8.47587 11.6364 8.54471C11.3608 8.61354 11.1104 8.75923 10.9144 8.96482C10.7183 9.1704 10.5847 9.42743 10.529 9.706L8.567 9.313C8.68863 8.70508 8.96951 8.14037 9.38092 7.67659C9.79233 7.2128 10.3195 6.86658 10.9086 6.67332C11.4977 6.48006 12.1275 6.44669 12.7337 6.57661C13.3399 6.70654 13.9007 6.99511 14.3588 7.41282C14.8169 7.83054 15.1559 8.36241 15.3411 8.95406C15.5263 9.54572 15.5511 10.1759 15.4129 10.7803C15.2747 11.3847 14.9785 11.9415 14.5545 12.3939C14.1306 12.8462 13.5941 13.1779 13 13.355Z" fill="#BDA169"/>
                </svg>
              </el-tooltip>
            </div>
          </div>
          <div class="re_top_right">
            <!-- <button
              class="pc_btn2" @click="batchEditCheck">批量修改</button> -->
            <el-checkbox v-model="hideNoStockGoods" label=""></el-checkbox>
            <span class="re_right_text">隐藏无库存商品</span>
            <button
              class="pc_btn1"
              @click="exportExcel">导出表格</button>
          </div>
        </div>
        <div class="re_table_container">
          <el-table
            ref="multipleTable"
            :height="goods_height"
            stripe
            :data="goods_data"
            :empty-text="!loading ? '暂无数据' : ' '"
            style="font-size: 16px;margin-top: 5px;width: 100%;"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
            id="stockRecordTab">
            <el-table-column type="selection" min-width="6%" align="center"></el-table-column>
            <el-table-column
              prop="name"
              min-width="18%"
              :show-overflow-tooltip="true"
              label="商品名称">
            </el-table-column>
            <el-table-column
              min-width="16%"
              align="center"
              label="条码">
              <template slot-scope="scope">{{ scope.row.code ? scope.row.code : '-'}}</template>
            </el-table-column>
            <el-table-column
              prop="typeName"
              min-width="12%"
              align="center"
              label="商品分类">
            </el-table-column>
            <el-table-column
              min-width="10%"
              align="center"
              show-overflow-tooltip
              label="供应商">
              <template slot-scope="scope">
                <div class="supplier-overflow">{{ scope.row.supplierName ? scope.row.supplierName : '-'}}</div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!(employeeAuth.indexOf('purchase_price') !== -1)"
              min-width="11%"
              align="center"
              label="进价">
              <template slot-scope="scope">{{ $toDecimalFormat(scope.row.purPrice, 6) }}</template>
            </el-table-column>
            <el-table-column
              min-width="11%"
              align="center"
              prop="cur_stock"
              label="库存"
              sortable
              :sort-method="(a, b) => {return a.curStock - b.curStock}">
              <template slot-scope="scope">{{ $toDecimalFormat(scope.row.curStock, 3) }}</template>
            </el-table-column>
            <el-table-column
              min-width="10%"
              align="center"
              label="单位">
              <template slot-scope="scope">{{ scope.row.unitName ? scope.row.unitName : '-'}}</template>
            </el-table-column>
            <el-table-column
              prop="expireDate"
              min-width="10%"
              align="center"
              label="到期日期">
            </el-table-column>
            <el-table-column
              prop="overdue"
              min-width="10%"
              align="center"
              label="保质状态">
            </el-table-column>
            <el-table-column label="操作" align="center" min-width="8%">
              <template slot-scope="scope">
                <span class="edit_span" @click="editIt(scope.row)">修改</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              记录条数：<span>{{towIntNumber(goods_total)}}</span>
            </div>
            <div>
              <el-pagination
                :key="pageKey"
                layout="prev, pager, next, slot"
                :total="goods_total"
                @current-change="goodsChange"
                :current-page="goods_pagenum"
                :page-size="goods_pageSize"
                :page-count="goods_total"
              >
                <!-- slot -->
                <vCjPageSize
                  @sizeChange="handleSizeChange"
                  :pageSize.sync="goods_pageSize"
                  :currentPage.sync="goods_pagenum"
                  :pageKey.sync="pageKey">
                </vCjPageSize>
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 选择保质期和有效期弹窗 -->
    <el-dialog v-if="showBzqDiffDialog" :visible.sync="showBzqDiffDialog" width="28%" append-to-body :show-close="false">
      <date-diff :manufactureDate="manufactureDate" @dismissDialog="showBzqDiffDialog = false" @callback="getDiff"/>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message, Loading } from 'element-ui';
import vCjSelect from '@/common/components/CjSelect';
import vCjPageSize from '@/common/components/CjPageSize';
import { timeStampToDate } from '@/utils/util';
import DateDiff from '@/common/components/DateDiff';
export default {
  data() {
    return {
      loading: false,
      pinyin: false,
      supplierObject: {
        all: false,
        notSupplier: false,
        supplierList: []
      }, // 供应商传参
      inputing_keyword: false,
      category_list: [],
      detailValue: [''],
      keywordGoods: '',
      goodssort: {},
      pageKey: 0,
      goods_total: 0,
      goods_pagenum: 1,
      goods_height: $(window).height() - 240,
      goods_pageSize: 10,
      goods_data: [],
      multipleSelection: [],
      choose_list: [],
      showEditDialog: false,
      fingerprint: null,
      manufactureDate: null,
      expiryDays: null,
      loadingInstance: null,
      hideNoStockGoods: false,
      export_data: [],
      overdueDate: 10,
      showBzqDiffDialog: false,
      calcGuaranteeDate: '-'
    };
  },
  components: {
    [Message.name]: Message,
    [Loading.name]: Loading,
    vCjSelect,
    vCjPageSize,
    DateDiff
  },
  created() {
    this.getAllCategory();
    this.getOverdueDate()
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.searchEarlyWarningMain();
  },
  mounted() {
    this.listenResize();
    $('#goods_keyword_report').focus();
  },
  watch: {
    keywordGoods() {
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        this.searchEarlyWarningMain();
      }, this.delayedTime);
    },
    detailValue() {
      this.searchEarlyWarningMain();
    },
    hideNoStockGoods() {
      this.goods_pagenum = 1;
      this.goodsStock('search');
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),

    /**
     * 计算保质期显示
     */
    calcBZQ() {
      // 根据天数计算 显示日期
      if (!this.manufactureDate || !+this.expiryDays) {
        this.calcGuaranteeDate = '-';
        return;
      }
      const produceTime = new Date(this.manufactureDate).getTime();
      this.calcGuaranteeDate = timeStampToDate(produceTime + (+this.expiryDays * 24 * 3600 * 1000));
    },

    /**
     * 日期选择后回调
     */
    getDiff(obj) {
      this.showBzqDiffDialog = false;
      this.manufactureDate = obj.manufactureDate;
      this.expiryDays = obj.diff;
      this.calcBZQ();
    },

    // 供应商回调
    searchChange(e) {
      console.log(e, 'eeeeeeeeeeeeeeeeeeeeeee123');
      this.supplierObject = _.cloneDeep(e);
      this.searchEarlyWarningMain();
    },
    focusInput(sid) {
      this.keywordGoods = '';
      $('#' + sid).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    headerStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    showLoading() {
      this.loadingInstance = Loading.service({
        lock: true
      });
    },
    hideLoading() {
      if (this.loadingInstance) {
        this.loadingInstance.close();
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.choose_list = val;
    },
    sortChange(e) {
      this.goods_pagenum = 1;
      this.goodssort = _.cloneDeep(e);
      this.goodsStock('search');
    },
    goodsChange(val) {
      this.goods_pagenum = val;
      this.goodsStock('search');
    },
    handleSizeChange() {
      this.goodsStock('search');
    },
    /**
     * 商品库存查询
     */
    searchEarlyWarningMain() {
      if (this.pinyin) {
        return;
      }
      this.goods_pagenum = 1;
      this.goodsStock('search');
    },
    /**
     * 商品库存查询
     */
    goodsStock(str, logFlg) {
      if (!this.overdueDate && this.overdueDate !== 0) {
        demo.msg('warning', '请输入天数');
        return;
      }
      this.loading = true;
      var params = {
        type: str, // search、excel
        pageSize: this.goods_pageSize,
        page: this.goods_pagenum,
        search: this.keywordGoods.replace(/'/g, "'").replace(/;/g, '；'),
        typeId: this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        overdueDate: this.overdueDate,
        hideNoStockGoods: this.hideNoStockGoods ? 1 : 0,
        orderBy: this.goodssort.prop || null,
        sort: this.goodssort.order || null,
        all: this.supplierObject.all,
        notSupplier: this.supplierObject.notSupplier,
        supplierList: this.supplierObject.supplierList
      };
      if (logFlg) {
        this.reportFormLog(_.cloneDeep(params), '过期预警查询');
      }
      goodService.goodsExpirationWarningReports(params, res => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
        this.goods_data = res.data.map(r => {
          let expireDate = new Date(r.manufactureDate.substring(0, 10)).addDays(r.expiryDays);
          let overdue = r.overdueDate >= 0 ? '剩余' + r.overdueDate + '天' : '已过期' + (-r.overdueDate) + '天';
          return {...r, expireDate, overdue};
        });
        this.goods_total = Number(res.count);
      }).catch(() => {
        setTimeout(() => {
          this.loading = false;
        }, this.delayedTime);
      });
    },
    getOverdueDate() {
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        this.overdueDate = 10;
      } else {
        const overdue = demo.t2json(this.storeList[0].settings).overdueDate
        this.overdueDate = overdue === undefined ? 10 : overdue;
      }
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : '0.00';
    },
    towIntNumber(val) {
      return val ? Number(val) : 0;
    },
    editIt(row) {
      this.showEditDialog = true;
      this.fingerprint = row.fingerprint;
      this.manufactureDate = new Date(row.manufactureDate).format('yyyy-MM-dd');
      this.expiryDays = row.expiryDays;
      this.calcBZQ();
    },
    batchEditCheck() {
      if (this.choose_list.length === 0) {
        demo.msg('warning', '请先选择商品');
      } else if (this.choose_list.length === 1) {
        // 只选了一条商品时将该商品的生产日期和保质期传递给子组件
        this.manufactureDate = this.choose_list[0].manufactureDate;
        this.expiryDays = this.choose_list[0].expiryDays;
        this.showEditDialog = true;
      } else {
        this.manufactureDate = null;
        this.expiryDays = null;
        this.showEditDialog = true;
      }
    },
    batchEdit() {
      let params = {
        fingerprint: this.fingerprint,
        manufactureDate: this.manufactureDate,
        expiryDays: this.expiryDays
      }
      goodService.updateDays(params,
        res => {
          console.log(res);
          demo.msg('success', '修改成功!');
          this.showEditDialog = false;
          this.goodsStock('search');
        },
        errMsg => {
          demo.msg('error', errMsg);
        }
      )
    },
    // 获取所有类别
    getAllCategory() {
      this.category_list = [{label: '全部分类', value: ''}, {label: '称重分类', value: '0'}];
      typeService.search(res => {
        var json = demo.t2json(res);
        if (json.length > 0) {
          json.forEach(item => {
            let obj = {
              label: item.name,
              value: item.fingerprint
            };
            if (item.list.length !== 0) {
              let arr = [];
              item.list.forEach(subItem => {
                let subObj = {
                  label: subItem.name,
                  value: subItem.fingerprint
                };
                arr.push(subObj);
              });
              obj['children'] = arr;
            }
            this.category_list = this.category_list.concat(obj);
          });
        }
      });
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_overdue_warning_detail', action: 'reportFormLog', description});
      }
    },
    exportExcel() {
      if (!this.overdueDate && this.overdueDate !== 0) {
        demo.msg('warning', '请输入天数');
        return;
      }
      var that = this;
      let sub_data = {
        'type': 'excel',
        'search': this.keywordGoods.replace(/'/g, "'").replace(/;/g, '；'),
        'typeId': this.detailValue.length === 2 ? this.detailValue[1] : this.detailValue[0],
        'hideNoStockGoods': this.hideNoStockGoods ? 1 : 0,
        'overdueDate': this.overdueDate,
        'orderBy': this.goodssort.prop,
        'all': this.supplierObject.all,
        'notSupplier': this.supplierObject.notSupplier,
        'supplierList': this.supplierObject.supplierList
      };
      this.reportFormLog(_.cloneDeep(sub_data), '过期预警导出表格');
      goodService.goodsExpirationWarningReports(sub_data, res => {
        this.export_data = res.data.map(item => {
          let expireDate = new Date(item.manufactureDate.substring(0, 10)).addDays(item.expiryDays);
          let overdue = item.overdueDate >= 0 ? '剩余' + item.overdueDate + '天' : '已过期' + (-item.overdueDate) + '天';
          return {
            ...item,
            code: item.code || '-',
            unitName: item.unitName || '-',
            supplierName: item.supplierName || '-',
            'curStock': that.formatFloat(Number(item.curStock), 3),
            expireDate,
            overdue
          };
        });
        if (this.export_data.length === 0) {
          demo.msg('warning', '暂无符合条件数据，请重新选择条件');
        } else {
          var field_mapping = {};
          if (!this.$employeeAuth('purchase_price')) {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              分类: 'typeName',
              供应商: 'supplierName',
              进价: 'purPrice',
              库存: 'curStock',
              单位: 'unitName',
              到期日期: 'expireDate',
              保质状态: 'overdue'
            };
          } else {
            field_mapping = {
              商品名称: 'name',
              条码: 'code',
              分类: 'typeName',
              供应商: 'supplierName',
              库存: 'curStock',
              单位: 'unitName',
              到期日期: 'expireDate',
              保质状态: 'overdue'
            };
          }
          that.$makeExcel(this.export_data, field_mapping, '商品过期预警' + new Date().format('yyyyMMddhhmmss'));
          this.export_data = [];
        }
      });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      this.table_height = $(window).height() - 460;
    },
    formatFloat (f, digit) {
      if (isNaN(f)) {
        return '';
      }
      var m = Math.pow(10, digit);
      return Math.round(f * m, 10) / m;
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    employeeAuth: state => state.show.employeeAuth,
    storeList: state => state.show.storeList,
    delayedTime: state => state.show.delayedTime
  }),
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
    this.hideLoading();
  }
};
</script>

<style lang='less' scoped>
/deep/ .el-dialog {
  border-radius: 6px;
  margin-top: 21vh !important;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
.bzq-container-stock {
  position: relative;
}
.calc-text {
  position:absolute;
  right: 20px;
  top: 11px;
  height: 42px;
  font-size: 16px;
  cursor: pointer;
  color: #bda169;
}
.zhi {
  color: #B2C3CD;
  font-size: 16px;
  padding-left: 136px;
}
.supplier-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.inputing_border_color1 {
  border-color: #BDA16A;
}
.inputing_border_color2 {
  border-color: #e3e6eb;
}
/deep/.el-table-column--selection .cell {
  padding-left: 10px;
  padding-right: 6px;
}
.re_deposit_record_container {
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 50px;
    height: 44px;
    line-height: 44px;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeFocusBorderColor;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    height: 50px;
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeFocusBorderColor;
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-input__inner {
    border-radius: 50px;
    color: @themeFontColor;
    font-size: 16px;
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  background: @input_backgroundColor;
  .re_top{
    height: 64px;
    background: @input_backgroundColor;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        margin-left: 10px;
      }
    }
    .re_top_right{
      display: flex;
      align-items: center;
    }
    .re_right_text {
      color: @themeFontColor;
      font-size: 16px;
      margin-left: 5px;
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: @input_backgroundColor;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      padding: 0 12px;
      background: white;
      color: @themeFontColor;
      span{
        color: @themeFocusBorderColor;
      }
    }
  }
}
.edit_span {
  color: @themeBackGroundColor;
  cursor: pointer;
}
.early_warning_div_container {
  position: relative;z-index: 800;height: 308px;margin: 0 auto;margin-top: 180px;
  background: #FFF;width: 430px;overflow: hidden;border-radius: 5px;color: @themeFontColor;
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 4px;
    height: 44px;
    line-height: 44px;
  }
  /deep/.el-input__inner {
    border-radius: 4px;
  }
}
.early_warning_div_header {
  margin: 0 20px;display:flex;justify-content: space-between;
  align-items: center;border-bottom: 1px solid #E3E6EB;
}
.early_warning_div_close {
  font-size: 36px;color: #567485;cursor:pointer;
}
.early_warning_div_span {
  color: @themeFontColor;
  font-weight: normal;
}
.com_pad273 {
  overflow: hidden;
  margin: 20px;
  font-size: 24px;
  display: flex;
  justify-content: center;
}
.com_early_btn1 {
  width: 170px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 0;
  float: left;
  border-radius: 4px;
  background: #fff;
  color: @themeBackGroundColor;
  cursor: pointer;
  font-size: 16px;
  font-weight: 700;
}
.com_early_btn2 {
  width: 170px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  margin-left: 20px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 700;
}
.manufactureDateClass {
  width: 260px;
  & > .el-input__suffix {
    right: 30px;
  }
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
}
.pc_report2 input {
  width: 235px;
  height: 25px;
  line-height: 28px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 9px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.pc_report3 {
  width: 71px;
  height: 45px;
  margin-left: 10px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
}
.text_font {
  font-weight: 500;
  font-size: 16px;
  line-height: 23px;
  color: #537286;
  margin-left: 10px;
}
.pc_report3 input {
  width: 46px;
  height: 25px;
  line-height: 28px;
  margin-left: 15px;
  font-size: 16px;
  margin-top: 9px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.el-table .ascending .sort-caret.ascending{
  border-bottom-color: #BDA16A;
}
.el-table .descending .sort-caret.descending{
  border-top-color: #BDA16A;
}
.el-loading-mask {
  background: white;
  opacity: 0.7;
}
.el-loading-mask.is-fullscreen {
  position: fixed;
  top: 50px;
}
.el-input__inner:focus {
  border-color: #BDA16A;
}

.el-date-table td.end-date span {
  background-color: #BDA16A;
}

.pc_btn1 {
  cursor: pointer;font-weight: bold;
  width: 100px;height: 44px;margin-left: 15px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeFocusBorderColor;float: left;border: 0px;outline: none;
}
.pc_btn2 {
  cursor: pointer;font-weight: bold;background-color: @input_backgroundColor;
  width: 100px;height: 44px;margin-left: 10px;line-height: 40px;border-radius: 22px;text-align: center;
  color: @themeBackGroundColor;font-size: 18px;float: left;border: 1px solid @themeFocusBorderColor;outline: none;
}
.pc_btn1:active {
  cursor: pointer;font-weight: bold;
  width: 100px;height: 44px;margin-left: 15px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeFocusBorderColor;float: left;border: 0px;opacity: 0.5;outline: none;
}
.psk_det33 {
  float: left;width: 100px;height: 44px;background: @themeBackGroundColor;line-height: 44px;text-align: center;
  color: #FFF;font-size: 18px;font-weight: 700;border-radius: 22px;margin-left: 20px;cursor: pointer;
}
</style>
