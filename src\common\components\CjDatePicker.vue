<template>
  <div class="date_picker_wrap" :style="`width: ${width}px;`" :class="{'pickFocus': dateFocus}" @click="dateFocus = true;">
    <el-date-picker
      ref="startPicker"
      v-model="dateStart"
      :type="type"
      :placeholder="placeholder[0]"
      style="height:43px"
      @blur="dateFocus = false"
      :value-format="dateFormat"
      :format="dateFormat"
      :picker-options="getPickerOptions('start')"
      @change="datePickChange"
    >
    </el-date-picker>
    <div id="createTime" style="font-size: 16px;">至</div>
    <el-date-picker
      ref="endPicker"
      v-model="dateEnd"
      :type="type"
      :placeholder="placeholder[1]"
      style="height:43px"
      @blur="dateFocus = false"
      :value-format="dateFormat"
      :format="dateFormat"
      :picker-options="getPickerOptions('end')"
      @change="datePickChange"
    >
    </el-date-picker>
  </div>
</template>
<script>
export default {
  name: 'CjDatePicker',
  props: {
    width: {
      type: Number,
      default: 280
    },
    // 显示类型
    type: {
      type: String,
      default: 'date'
    },
    // 开始时间
    startDate: {
      type: String,
      default: ''
    },
    // 结束时间
    endDate: {
      type: String,
      default: ''
    },
    placeholder: {
      type: Array,
      default: () => { return ['开始日期', '结束日期']; }
    },
    pickerOptions: {
      type: Object,
      default: () => {}
    },
    // 快捷选项,目前包含今日、昨日、本周、上周、本月、上月、今年、去年
    shortcuts: {
      type: Array,
      default: () => []
    },
    // 选择器显示及值的格式
    dateFormat: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    /**
     * 快捷键选择时默认的开始及结束时刻
     * @example [[10, 8, 8],[23, 30, 31]]代表开始日期时刻部分为'10:08:08'，结束日期时刻部分为'23:30:31'
     */
    defaultTime: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dateFocus: false,
      dateStart: null, // 开始日期
      dateEnd: null // 结束日期
    }
  },
  mounted() {},
  methods: {
    dateValueFormat(date) {
      return this.dateFormat ? date.format(this.dateFormat.replaceAll('H', 'h')) : date;
    },
    getPickerOptions(type) {
      const pickerOptions = this.pickerOptions || {};
      let startHours, startMinutes, startSeconds, endHours, endMinutes, endSeconds;
      [startHours, startMinutes, startSeconds] = this.defaultTime.length ? this.defaultTime[0] : [0, 0, 0];
      [endHours, endMinutes, endSeconds] = this.defaultTime.length ? this.defaultTime[1] : [23, 59, 59];
      const shortcutsObj = {
        shortcuts: [
          {
            text: '今日',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              const end = new Date(new Date().setHours(endHours, endMinutes, endSeconds));
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '昨日',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              const end = new Date(new Date(start.setDate(start.getDate() - 1)).setHours(endHours, endMinutes, endSeconds));
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '本周',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              const day = start.getDay();
              const diffToMonday = (day === 0 ? 6 : day - 1); // Sunday to Monday correction
              start.setDate(start.getDate() - diffToMonday);
              const end = new Date(new Date(start).setHours(endHours, endMinutes, endSeconds));
              end.setDate(start.getDate() + 6);
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '上周',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              const day = start.getDay();
              const diffToMonday = (day === 0 ? 6 : day - 1); // Sunday to Monday correction
              start.setDate(start.getDate() - diffToMonday - 7);
              const end = new Date(new Date(start).setHours(endHours, endMinutes, endSeconds));
              end.setDate(start.getDate() + 6);
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '本月',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              start.setDate(1);
              start.setHours(startHours, startMinutes, startSeconds);
              const end = new Date(start.getFullYear(), start.getMonth() + 1, 0, endHours, endMinutes, endSeconds);
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '上月',
            onClick: (picker) => {
              const start = new Date(new Date().setHours(startHours, startMinutes, startSeconds));
              start.setDate(1);
              start.setMonth(start.getMonth() - 1);
              const end = new Date(start.getFullYear(), start.getMonth() + 1, 0, endHours, endMinutes, endSeconds);
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          },
          {
            text: '今年',
            onClick: (picker) => {
              const start = new Date(new Date().getFullYear(), 0, 1, startHours, startMinutes, startSeconds)
              const end = new Date(start.getFullYear(), 11, 31, endHours, endMinutes, endSeconds);
              if (type === 'start') {
                picker.$emit('pick', start);
                this.dateEnd = this.dateValueFormat(end);
              } else {
                picker.$emit('pick', end);
                this.dateStart = this.dateValueFormat(start);
              }
            }
          }
          // {
          //   text: '去年',
          //   onClick: (picker) => {
          //     const start = new Date(new Date().getFullYear() - 1, 0, 1, startHours, startMinutes, startSeconds);
          //     const end = new Date(start.getFullYear(), 11, 31, endHours, endMinutes, endSeconds);
          //     if (type === 'start') {
          //       picker.$emit('pick', start);
          //       this.dateEnd = this.dateValueFormat(end);
          //     } else {
          //       picker.$emit('pick', end);
          //       this.dateStart = this.dateValueFormat(start);
          //     }
          //   }
          // }
        ]
      }
      return Object.assign(shortcutsObj, pickerOptions);
    },
    datePickChange() {
      this.$emit('update:start-date', this.dateStart);
      this.$emit('update:end-date', this.dateEnd);
      this.$emit('pickerChange');
    }
  },
  watch: {
    startDate() {
      this.dateStart = this.startDate;
    },
    endDate() {
      this.dateEnd = this.endDate;
    }
  },
  computed: {}
};
</script>
<style lang="less" scoped>
.pickFocus {
  border-color: @themeBackGroundColor !important;
}
.date_picker_wrap {
  font-family: HarmonyOSSansSC, sans-serif !important;
  color: @themeFontColor;
  height: 44px;
  background-color: #fff;
  border: 1px solid #D7D7D7;
  border-radius: 20px;
  display: flex;
  align-items: center;
  /deep/ .el-input__prefix {
    display: none;
  }
  /deep/.el-input__inner {
    border: none;
    height: 42px !important;
    text-align: center;
  }
}
</style>
