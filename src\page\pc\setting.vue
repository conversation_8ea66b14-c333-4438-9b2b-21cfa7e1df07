<style lang="less" scoped>
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
}
.el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.el-select-dropdown__item.selected {
  color: @themeBackGroundColor;
}
.el-tabs__content {
  height: 100%;
}
.icon-style {
  margin-top: 12px;
  font-size: 18px;
  width: 20px;
  height: 20px;
  cursor: pointer;
}
.pc_set {
  width: 100%;
  height: 100%;
  background: #f5f8fb;
  color: @themeFontColor;
  padding: 12px !important;
  position: fixed;
  z-index: 200;
  top: 50px;
}
.pc_set .el-input__inner {
  background-color: #f5f8fb;
}
/deep/ .el-input__inner {
  background-color: #f5f8fb;
}
.pc_set1 {
  float: left;
  width: 204px;
  height: 100%;
  border-right: 1px solid #e7eaef;
  font-weight: 700;
}
.pc_set11 {
  height: 42px;
  width: 100%;
}
.pc_set12 {
  width: 100%;
  height: 57px;
  cursor: pointer;
}
.pc_set13 {
  width: 39px;
  height: 39px;
  margin-top: 8px;
  margin-left: 35px;
  border-radius: 50%;
  overflow: hidden;
  float: left;
}
.pc_set13 img {
  width: 24px;
  height: 24px;
  margin-top: 7px;
}
.pc_set14 {
  line-height: 57px;
  margin-left: 5px;
  font-size: 16px;
  float: left;
}
.pc_set18 {
  float: left;
  width: calc(100% - 204px);
  padding-top: 40px;
  padding-left: 37px;
  padding-right: 34px;
  background: #fff;
  height: 100%;
  overflow: hidden;
  .pc_set_video {
    background: @themeButtonBackGroundColor;
    border-radius: 10px 0px 0px 10px;
    width: 140px;
    color: @themeBackGroundColor;
    font-weight: 500;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    right: 0px;
    position: absolute;
    z-index: 100;
    margin-top: 66px;
  }
  .pc_set_cat {
    background: #FF0036;
    border-radius: 10px 0px 0px 10px;
    width: 140px;
    color: #FFFFFF;
    font-weight: 500;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    right: 0px;
    position: absolute;
    z-index: 100;
    margin-top: 298px;
  }
  .pc_set_cat_img {
    width: 140px;
    text-align: center;
    right: 0px;
    position: absolute;
    z-index: 100;
    margin-top: 265px;
  }
}
.pc_set19 {
  width: 140px;
  line-height: 40px;
  float: left;
  font-size: 16px;
  margin-bottom: 18px;
}
.pc_set2 {
  width: 50%;
  float: left;
  font-size: 16px;
  background: #f5f8fb;
}
.pc_set21 {
  font-size: 16px;
  font-weight: bold;
  margin-top: 10px;
}
.pc_set22 {
  font-size: 16px;
  position: relative;
  line-height: 20px;
}
.pc_set23 {
  position: absolute;
  right: 0;
  top: 0;
}
.pc_set24 {
  position: relative;
  overflow: hidden;
  font-size: 16px;
}
.pc_set32 {
  float: left;
  margin-left: 15px;
  line-height: 50px;
}
.pc_set34 {
  color: #fff;
  background-image: @linearBackgroundColor;
}
.pc_set35 {
  float: left;
  padding: 12px 34px;
  font-size: 16px;
  border: 1px solid #e3e6eb;
  border-radius: 5px;
  line-height: 16px;
  background: #f5f8fb;
  margin-right: 20px;
  cursor: pointer;
  position: relative;
}
.pc_set36 {
  border-color: @themeBackGroundColor;
  color: @themeBackGroundColor;
  background: #fff;
}
#set_hy {
  font-size: 16px;
}
.pc_set_ads {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  align-content: flex-start;
}
.image_upload {
  float: left;
  padding-right: 12px;
  /deep/ .el-upload--picture-card{
    padding-top: 18px;
    border: 1px solid #B2C3CD;
    width: 178px;
    height: 100px;
    border-radius: 0;
    line-height: 20px;
  }
  /deep/.el-upload--picture-card:hover {
  color: #BDA169;
  }
}
.image_pre {
  float: left;
  margin-right: 12px;
  cursor: pointer;
  width: 180px;
  height: 100px;
  position: relative;
}
.pc_set_ad {
  width: 18%;
  height: 19%;
  margin: 1%;
  font-size: 0.625vmin;
  white-space: nowrap;
}
.pc_adv_duration {
  margin: 10px auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /deep/ .el-input-group__append, .el-input-group__prepend {
   color: #567485;
   background-color: #f5f8fb
  }
}
.pc_set_time_tips {
  color: @themeBackGroundColor;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
}
.pc_set43 {
  height: 100%;
}
.pc_set43 .el-select {
  width: 100px;
}

.pc_set_ad:hover,
.pc_set_ad:focus,
.pc_set_ad:active {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
  -webkit-transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}
.print_remark_content{
  display: flex;
}
.img_remark{
  width: 301px;
  height: 446px;
}

.input_remark{
  width:100%;
  max-width: 340px;
  height:124px;
  background:rgba(245,248,251,1);
  border:1px solid rgba(227,230,235,1);
  border-radius:5px;
  font-size: 15px;
  resize: none;
  padding: 9px;
}

.print_font_setting{
  display: flex;
  align-items: center;
  margin-top: 30px;
}
.font_text{
  width: 98px;
  height: 24px;
  font-size: 16px;
  font-weight: 400;
  color: #557485;
  line-height: 24px;
}

.msg_item{
  display: flex;
  align-items: center;
  margin-bottom: 33px;
}

.notice_title{
  width: 173px;
  height: 17px;
  font-size: 16px;
  font-weight: 400;
  color: #557485;
  line-height: 17px;
}

.notice_remark{
  flex:1;
  margin-left:62px;
  font-size: 16px;
  font-weight: 400;
  color: @text;
}
.pc_set44 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: @themeBackGroundColor;
  text-align: center;
  color: #FFFFFF;
  line-height: 20px;
  font-size: 16px;
  margin-top: 1px;
  cursor: pointer;
  display: inline-block;
  margin-left: 10px;
}
.pc_span46 {
  color:@themeBackGroundColor;
  line-height: 39px;
  font-size: 16px;
}

.tab_container_deldata{
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 42px;
  border-radius: 4px;
  // border: 1px solid rgba(227, 230, 235, 100);
  .tab{
    width: 100px;
    height: 40px;
    text-align: center;
    line-height: 38px;
    color: @themeFontColor;
    background: #F5F8FB;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    border: 1px solid #E3E6EB;
  }
  .tab_active_btn{
    background: @themeBackGroundColor !important;
    color: white !important;
    height: 42px !important;line-height: 38px !important;
  }
  .tab_one {
    border-radius: 4px 0  0 4px;
    border-right: 0px;
  }
  .tab_last {
    border-radius: 0 4px 4px 0;
    border-left: 0px;
  }
}
.pc_header_container{
  display:flex;
  justify-content:center;
  align-items:center;
  height:60px;
  border-bottom:1px solid #E5E8EC;
  margin: 0 20px;
}
.deldata_close {
  font-size: 30px;
  color: #8298A6;
  cursor: pointer;
  float: right;
  margin-top: -54px;
  margin-right: 20px;
}
.deldata_tab_div {
  border: 1px solid #e5e8ec;
  margin-left: 20px;
  width: calc(100% - 40px);
  margin-top: 20px;
  margin-bottom: 10px;
  border-radius: 4px;
  /deep/ .el-table__body td {
    font-weight: normal !important;
    letter-spacing: 0px;
  }
}
.datepicker_container{
  width: 300px;
  height: 44px;
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  border-radius: 4px;
  margin-top: 10px;
  display: flex;
  align-items: center;
}
.del_store_data_div {
  /deep/ .datepicker_container .el-input--suffix {
    /deep/ .el-input__inner{
      border:none;
      height: 42px;
      margin-top: 1px;
      text-align: center;
      color: @themeFontColor;
      background: #ffffff;
      font-weight: normal;
      font-size: 16px;
      padding: 0 10px;
    }
    .el-input__suffix {
      top: 2px;
    }
  }
  /deep/ .el-dialog__wrapper {
    overflow-y: hidden;
  }
}
/deep/ .el-select .el-input__inner{
  font-size: 16px;
}
/deep/ .el-date-editor .el-input__prefix .el-input__icon {
  display: none !important;
}
/deep/ .el-dialog {
  border-radius: 6px;
}
/deep/ .el-dialog__header {
  padding: 0;
}
/deep/ .el-dialog__body {
  padding: 0;
}
.pc_dialog_topleft {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 20px;
  .pc_header_search {
    width: 90px;
    height: 42px;
    border-radius: 4px;
    background-color: @themeBackGroundColor;
    text-align: center;
    color: #ffffff;
    font-weight: normal;
    line-height: 40px;
    margin-left: -400px;
    margin-top: 10px;
    cursor: pointer;
  }
  .pc_header_batchDel {
    width: 110px;
    height: 42px;
    border-radius: 4px;
    background-color: #FF6159;
    text-align: center;
    color: #ffffff;
    font-weight: normal;
    line-height: 40px;
    margin-left: 20px;
    margin-top: 10px;
    cursor: pointer;
  }
  .pc_selcount_div {
    float: left;
    margin-left: 20px;
    line-height: 32px;
  }
}
.pc_hed151 {
  color: @themeFontColor;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 1px;
}
.pc_del1 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.com_pae20 {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: @themeBackGroundColor;
  text-align: center;
  color: #FFFFFF;
  line-height: 20px;
  font-size: 16px;
  float: left;
  margin-top: 10px;
  margin-left: 30px;
  cursor: pointer;
}
.com_pae20::after {
  border-bottom-color:  rgba(0, 0, 0, 0.8)!important;
}
/deep/ .el-popper[x-placement^=top] .popper__arrow {
  bottom: 0px;
  border-top-color: rgba(0, 0, 0, 1);
  border-bottom-color: rgba(0, 0, 0, 1);
}
/deep/ .el-popper[x-placement^=top] .popper__arrow::after {
  border-top-color: rgba(0, 0, 0, 1);
  border-bottom-color: rgba(0, 0, 0, 1);
}
/deep/ .el-popper .popper__arrow::after {
  border-bottom-color: #000 !important;
}
.pc_del2 {
  width: 138px;
  height: 50px;
  color: #fff;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  background: @themeFontColor;
  font-weight: normal;
}
.pc_del3 {
  width: 138px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background-color: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
  font-weight: normal;
}
.pc_del12 {
  overflow: hidden;
}
.pc_del12 div {
  width: 70px;
  text-align: center;
  line-height: 61px;
  float: left;
  font-size: 14px;
  font-weight: bold;
}
.pc_del12 input {
  width: 305px;
  height: 61px;
  background: #f5f8fb;
  border-radius: 10px;
  border: none;
  text-indent: 20px;
  font-size: 16px;
  font-weight: bold;
}
.pc_van_btn {
  width: 147px;
  height: 60px;
  background: #f5f8fb;
  border: 1px solid #e3e6eb;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  color: @themeFontColor;
  margin-left: 10px;
}
.voice_slider_div {
  width: 300px;
  display: flex;
  align-items: center;
}
.voice_slider_low {
  width: 35px;
  color: #B2C3CD;
  font-size: 16px;
  cursor: pointer;
}
.voice_slider_heigh {
  width: 38px;
  font-weight: bold;
  color: @themeFontColor;
  font-size: 20px;
  text-align: right;
  cursor: pointer;
}
.newcheck_div {
  /deep/ .el-input__inner {
    margin-left: 30px;
    margin-right: 30px;
    width: 320px;
    height: 60px;
    border-radius: 4px;
    background-color: #F5F8FB;
    border: 0px;
    color: @themeBackGroundColor;
    font-size: 20px;
    font-weight: bold;
  }
  .verify_ipt {
    border: 1px solid #E5E8EC !important;
    border-radius: 4px;
    width: 295px;
    height: 44px;
    color: @themeBackGroundColor;
    line-height: 40px;
    font-size: 18px;
    padding-left: 20px;
  }
}
.van-count-down {
  text-align: center;
  font-size: 18px;
  color: #CBAB63;
  margin-top: 10px;
  // display: inline-block;
}
.clear_btn {
  background-color: @themeBackGroundColor;
  border: 0px;
  border-radius: 4px;
  width: 90px;
  height: 40px;
  font-size: 16px;
  float: left;
  color: white;
  text-align: center;
  line-height: 38px;
  cursor: pointer;
  font-weight: normal;
}
.delete_btn {
  background-color: #F64C4C;
}
/deep/.pc_setParts {
  .el-tabs__content {
    overflow: auto;
    position: relative;
  }
  .el-tabs__item.is-active {
    background: @themeBackGroundColor;
    color: #FFFFFF;
  }
  .el-tabs__item.is-active:hover {
    color: #FFFFFF;
  }
  .el-tabs__item:hover {
    color: @themeBackGroundColor;
  }
  .el-tabs__header {
    display: block;
  }
  .el-select {
    width: 100%;
  }
  .pc_setParts2 {
    margin-bottom: 20px;
    .avatar-pic {
      width: 90px;
      height: 90px;
      display: block;
    }
    .pic-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      border-color: @themeBackGroundColor;
    }
    .pic-uploader .el-upload:hover {
      border-color: @themeBackGroundColor;
    }
    .pic-uploader-good-icon {
      font-size: 28px;
      color: #8c939d;
      width: 90px;
      height: 90px;
      line-height: 90px;
      text-align: center;
    }
    .pc_setParts0 {
      line-height: 40px;
      display: inline-block;
      .itemRemark {
        color: @themeBackGroundColor;
      }
      .pc_setParts3 {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #E3E6EB;
        display: inline-block;
        margin-bottom: -4px;
      }
      .pc_setParts4 {
        display: inline-block;
        margin-left: 10px;
        margin-right: 20px;
      }
      .pc_setParts5 {
        display: inline-block;
        color: @themeBackGroundColor;
        margin-left: 10px;
        font-family: Microsoft YaHei, sans-serif;
      }
      .com_pae16 {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: @themeBackGroundColor;
        text-align: center;
        color: #FFFFFF;
        line-height: 20px;
        font-size: 16px;
        float: right;
        margin-top: 10px;
        margin-left: 12px;
        cursor: pointer;
      }
    }
    .pc_setParts1 {
      display: inline-block;
      position: absolute;
      right: 0;
      width: 31.3%;
      max-width: 230px;
      .delIcon {
        width: 44px;
        height: 44px;
        background: #F5F8FB;
        border-radius: 4px;
        display: inline-block;
        position: absolute;
        margin-left: 20px;
        text-align: center;
        cursor: pointer;
      }
      .pc_weigh {
        width: 105px;
        height: 30px;
        border-radius: 8px;
        display: inline-block;
        background: #F5F7FA;
        color: @themeBackGroundColor;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
      }
      .el-switch__core {
        margin-top: 22px;
      }
      input::-webkit-outer-spin-button,
      input::-webkit-inner-spin-button {
          -webkit-appearance: none;
      }
      input[type="number"]{
          -moz-appearance: textfield;
      }
      .el-switch {
        right: 0;
        position: absolute;
      }
      .pc_setRadio {
        display: inline-block;
        margin-left: 5px;
      }
      .setting_radio{
        width: 20px;
        height: 20px;
        background: #fff;
        border-radius: 50%;
        overflow: hidden;
        display: inline-block;
        position: relative;
        cursor: pointer;
        margin-bottom: -4px;
        .setting_radio_inner{
          width: 12px;
          height: 12px;
          background: @themeBackGroundColor;
          margin-top: 2px;
          margin-left: 2px;
          border-radius: 50%;
          overflow: hidden;
          position: relative;
        }
      }
    }
    .pc_setParts6 {
      width: 100%;
      border-radius: 4px;
      font-weight: 400;
      color: @themeFontColor;
      /deep/.el-textarea__inner {
        background: #F5F8FB;
        border: 1px solid #E3E6EB;
        border-radius: 4px;
        height: 70px;
        font-size: 16px;
        line-height: 23px;
        color: @themeFontColor;
      }
    }
    .pc_setParts7 {
      cursor: pointer;
      margin-top: 30px;
      width: 140px;
      height: 50px;
      background: @themeBackGroundColor;
      border-radius: 4px;
      font-size: 20px;
      line-height: 48px;
      color: #FFFFFF;
      letter-spacing: -0.408px;
      text-align: center;
    }
  }
  .pc_setRight {
    color: @themeFontColor;
    line-height: 20px;
    font-size: 14px;
    width: 260px;
    display: inline-block;
    position: absolute;
    margin-left: 40px;
    margin-top: 36px;
    .pc_setRight_rel {
      padding: 30px 16px;
      border: 1px solid #929292;
      width: 100%;
      .pc_setRight0 {
        margin: 0 auto;
        margin-bottom: 20px;
        height: 68px;
        width: 160px;
        .pc_setRight0_img {
          height: 68px;
          width: 160px;
        }
      }
      .pc_setRight1 {
        text-align: center;
        font-size: x-large;
        margin-bottom: 15px;
        line-height: 25px;
      }
      .pc_setRight2 {
        margin: 10px 0px;
        border: 1px dashed #C4C4C4;
      }
      .pc_setRight3 {
        margin: 0 auto;
        height: 136px;
        width: 136px;
        margin-top: 20px;
        .pc_setRight3_img {
          height: 136px;
          width: 136px;
        }
      }
    }
  }
  .pc_tab1_setRight {
    color: @themeFontColor;
    line-height: 20px;
    font-size: 14px;
    font-weight: bold;
    display: inline-block;
    width: 240px;
    position: absolute;
    margin-left: 40px;
    margin-top: 36px;
    .pc_tab1_setRight_rel {
      padding: 10px;
      width: 100%;
      border-radius: 4px;
      border: 1px solid #929292;
      .pc_tab1_setRight_rel_sty {
        font-size: 20px;
        font-weight: bold;
        line-height: 30px;
      }
      .pc_tab1_setRight0 {
        margin: 0px auto;
        width: 110px;
        height: 46px;
        .pc_tab1_setRight0_img {
          width: 110px;
          height: 46px;
          padding-left: 10px;
        }
      }
    }
  }
  .pc_tab2_setRight {
    width: 360px;
    color: #666666;
    display: inline-block;
    font-size: 16px;
    .pc_tab2_setRightRel {
      border-radius: 8px;
      width: 100%;
      color: #666666;
      border: 10px solid red;
      .pc_tab2_setRight0 {
        padding: 10px 16px;
        display: inline-block;
        width: 210px;
      }
      .pc_tab2_setRight1 {
        font-weight: bold;
        color: #FFF;
        display: inline-block;
        font-size: 20px;
        letter-spacing: 3px;
        background: red;
        padding: 5px 1px 9px 10px;
      }
      .pc_tab2_setRight2 {
        padding: 10px 16px;
        border-top: 3px solid red;
      }
      .pc_tab2_setRight2_span {
        color: #333333;
        padding-right: 10px;
        font-weight: bold;
      }
      .pc_tab2_setRight3 {
        width: 210px;
        color: #333333;
        display: inline-block;
        border-top: 3px solid red;
        border-right: 3px solid red;
        font-size: 12px;
        font-weight: bold;
        .pc_tab2_setRight4 {
          display: inline-block;
        }
        .pc_tab2_setRight5 {
          display: inline-block;
          color: #666666;
          font-weight: normal;
          padding-left: 10px;
        }
        .pc_tab2_setRight6 {
          padding-bottom: 10px;
          padding-left: 10px;
        }
        .pc_tab2_setRight7 {
          width: 110px;
          height: 36px;
          display: inline-block;
          vertical-align: top;
          margin-bottom: 3px;
          .pc_tab2_setRight7_img {
            padding-left: 10px;
            width: 110px;
            height: 32px;
          }
        }
        .pc_tab2_setRight9 {
          text-align: center;
          border-top: 3px solid red;
          padding-top: 5px;
          border-bottom: 1px solid red;
        }
      }
      .pc_tab2_setRight8 {
        color: #333333;
        display: inline-block;
        border-top: 3px solid red;
        width: 130px;
        padding: 10px 6px;
        position: absolute;
        font-size: 22px;
        font-weight: bold;
        padding-bottom: 29px;
        background: yellow;
        border-bottom: 1px solid red;
      }
    }
  }
  .pc_setRight_tip {
    font-size: 14px;
    line-height: 20px;
    color: @themeBackGroundColor;
    margin-top: 20px;
  }
}
.printPoint {
  cursor: pointer;
}
.reload_ad_btn {
  background-color: @themeBackGroundColor;
  border: 0px;
  border-radius: 4px;
  width: 90px;
  height: 26px;
  font-size: 16px;
  display: inline-block;
  color: white;
  text-align: center;
  line-height: 25px;
  cursor: pointer;
  font-weight: normal;
}
.del_date_div {
  margin-bottom: 15px;
  /deep/.el-alert__title {
    font-size: 18px;
  }
}
/deep/.el-checkbox__input.is-checked+.el-checkbox__label{
  color: #567485;
}
.del_date_checkbox_div {
  margin-top: 10px;
  /deep/.el-checkbox__label {
    font-size: 16px;
  }
  /deep/.el-checkbox__inner {
    width: 24px;
    height: 24px;
    border: none;
  }
  /deep/.el-checkbox__input {
    vertical-align: bottom;
  }
}

.pc_setModel1 {
  display: inline-block;
  color: #B2C3CD;
  font-size: 18px;
  border-bottom: 1px solid #EBEBEB;
  line-height: 40px;
  min-width: 110px;
  text-align: center;
  padding-bottom: 10px;
  cursor: pointer;
  margin-bottom: 5px;
}
.pc_setModel1:hover {
  color: @themeBackGroundColor;
}
.btn{
  width: 112px;
  height: 44px;
  line-height: 30px;
  color: white;
  font-weight: bold;
  font-size: 20px;
  border-radius: 4px;
  display: inline-block;
  border: 1px solid @themeBackGroundColor;
}
.subModelStyle {
  color: #B2C3CD;
  width: 220px;
  height: 44px;
  padding: 0 15px;
  cursor: not-allowed;
  border: 1px solid #C0C4CC;
  border-radius: 4px;
  line-height: 44px;
}
.focusDate {
  border-color: @themeBackGroundColor;
}
.focusDate1 {
  border-color: #E3E6EB
}
#updPassword {
  background: @themeBackGroundColor;
}
.borderColor {
  border-color: @themeBackGroundColor;
  border: 2px solid @themeBackGroundColor;
}
.borderColor1 {
  border-color: #D2D5D9;
  border: 2px solid #E3E6EB;
}
.setParts3 {
  background: @themeBackGroundColor;
  cursor:no-drop;
}
#moneyboxCheck {
  border: 1px solid @themeBackGroundColor;
  background: @themeBackGroundColor;
}
#fontColor {
  color: @themeBackGroundColor;
}
.iCloud {
  background: @themeBackGroundColor;
}
#setParts0 {
  font-size: 18px;
  color: @themeFontColor;
  font-weight: bold;
}
.pc_ultimate_div {
  display: flex;
  justify-content: space-between;
  width: 50%;
}
.pc_ultimate_span {
  color: #C29B4A;
}
.pc_renew_btn {
  background: #CBAB63;
  width: 106px;
  height: 40px;
  text-align: center;
  font-size: 16px;
  line-height: 38px;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
}
.del_date_checkbox_div {
  margin-top: 10px;
  /deep/.el-checkbox__label {
    font-size: 16px;
  }
  /deep/.el-checkbox__inner {
    width: 24px;
    height: 24px;
    border: none;
  }
}
/deep/.el-radio__input.is-checked+.el-radio__label {
  color: #567485 !important;
}
/deep/.el-radio__input {
  margin-top: 0px !important;

}
/deep/.el-radio__label {
  font-family: DinMedium, SourceHanSansCN, sans-serif !important;
  font-size: 16px;
  color: #567485 !important;
}
.offline-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.offline-text {
  color: #537286;
  font-size: 1em;
  margin-top: 25px;
}
.offline-refresh {
  width: 200px;
  height: 56px;
  background: #b4995a;
  color: white;
  line-height: 56px;
  text-align: center;
  cursor: pointer;
  font-size: 20px;
  border-radius: 4px;
  margin: 20px 0 0 74px;
  letter-spacing: 1.5px;
}
.prod_sync_container {
  color: #567485;
  p {
    font-size: 18px;
    font-weight: 700;
  }
  span {
    font-size: 16px;
  }
}
.step_container {
  width: 70%;
  border: 1px solid #ececec;
  height: 120px;
  background: #fafafa;
  border-radius: 8px;
  padding: 12px;
}
.next-btn {
  width: 80px;
  height: 40px;
  line-height: 40px;
  background: #fafafa;
  text-align: center;
  margin-bottom: 10px;
  cursor: pointer;
  display: inline-block;
}
.micro_store {
  width: 70%;
  border: 1px solid #ececec;
  border-radius: 8px;
  margin-top: 15px;
  &--header {
    .phone {
      font-weight: 700;
      font-size: 20px;
      color: #000;
    }
    .beforeAuth {
      padding: 12px;
      border-bottom: 2px solid #b4995a;
    }
    .auth {
      display: flex;
      .active {
        border-bottom: 3px solid #b4995a !important;
        background-color: #b4995a !important;
        border-radius: 4px 4px 0 0;
        .phone {
          color: white;
        }
      }
    }
    .half-header {
      width: 50%;
      padding: 12px;
      display: flex;
      justify-content: left;
      border-bottom: 3px solid #b4995a;
      cursor: pointer;
    }
    .half-header:hover {
      background: #ececec;
    }
    // .half-header:first-child {
    //    border-right: 1px solid #ececec;
    // }
    .header-img {
      width: 48px;
      height: 48px;
      text-align: center;
      line-height: 48px;
      border-radius: 4px;
      margin-right: 10px;
    }
    .ps {
      color: #ffad0d;
      background: #fff7e1;
    }
    .es {
      color: #3b82f6;
      background: #e4f2ff;
    }
  }
  &--body {
    padding: 12px;
    .warningText {
      color: #f64c4c;
      margin: 20px 0;
    }
    .infoText {
      color: #000;
      margin-bottom: 20px;
      p {
        margin-bottom: 0;
      }
    }
    .flex-div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .submit {
        width: 200px;
        height: 56px;
        background: #b4995a;
        color: #fff;
        text-align: center;
        line-height: 56px;
        border-radius: 4px;
        font-size: 20px;
        cursor: pointer;
        margin-top: 20px;
      }
      .error {
        width: 200px;
        height: 56px;
        border: 1px solid #f64c4c;
        color: #f64c4c;
        text-align: center;
        line-height: 56px;
        border-radius: 4px;
        font-size: 20px;
        cursor: pointer;
        margin-top: 20px;
      }
      .refresh {
        color: #cbab63;
        font-weight: 700;
        display: flex;
        align-items: center;
        cursor: pointer;
        i {
          font-size: 24px;
          font-weight: bold;
          margin-right: 6px;
          line-height: 24px;
        }
      }
    }
  }
}
.sync_flex_container {
  margin-left: 25px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  span {
    margin-left: 10px;
    color: #CBAB63;
  }
}
.refresh_sync_msg {
  position: absolute;
  cursor: pointer;
  color: #CBAB63;
  right: 0;
  bottom: 0;
  display: flex;
  i {
    font-size: 24px;
    margin-right: 5px;
  }
}
.update_price_container {
  display: flex;
  align-items: center;
  margin: 30px 0;
  span {
    margin-left: 8px;
  }
}
.tag-default {
  background: #f5f5f5;
  color: #4b4b4b;
  text-align: center;
  font-size: 12px;
  width: 50px;
  height: 22px;
  padding: 5px;
  line-height: 14px;
  margin-top: 8px;
  border-radius: 4px;
}
.auth .active .tag-default {
  background: #cacaca;
  color: #fff;
}
.tag-success {
  background: #e5f5ec;
  color: #47b881;
  text-align: center;
  font-size: 12px;
  width: 50px;
  height: 22px;
  padding: 5px;
  line-height: 14px;
  margin-top: 8px;
  border-radius: 4px;
}
.pc_check {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #ececec;
  cursor: pointer;
  display: inline-block;
}
.pc_ischeck {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background: #b4995a;
  cursor: pointer;
  display: inline-block;
}
.pc_cf1 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 3000;
}
.pc_cf2 {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}
.pc_cf3 {
  position: relative;
  z-index: 800;
  margin: 0 auto;
  margin-top: 240px;
  background: #fff;
  width: 495px;
  overflow: hidden;
  border-radius: 5px;
}
.pc_cf4 {
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: #567485;
  margin-top: 40px;
}
.pc_cf5 {
  width: 100%;
  text-align: center;
  font-size: 25px;
  color: #567485;
  margin-top: 25px;
  padding: 0 50px;
}
.pc_cf6 {
  overflow: hidden;
  margin: 35px 0 20px;
  font-size: 20px;
  padding: 0 50px;
  display: flex;
  justify-content: space-around;
}
.pc_cf7 {
  width: 120px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  background: #567485;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.pc_cf8 {
  width: 120px;
  height: 50px;
  text-align: center;
  line-height: 48px;
  background: #b4995a;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
}
.pc_scan_loading_div {
  width: 184px;
  height: 184px;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  position: absolute;
  top: 0px;
  left: 0px;
}
.pc_load_error_div {
  width: 184px;
  height: 184px;
  border-radius: 8px;
  background: #fafafa;
  color: #cacaca;
  position: absolute;
  top: 0px;
  left: 0px;
  display: flex;
  justify-content: center;
  align-items: center;
}
/deep/.el-loading-spinner .path {
  stroke: #b4995a !important;
}
.vx_official_notice {
  position: relative;
  overflow: hidden;
  &--title {
    color: #567485;
    font-size: 18px;
    font-weight: 700;
  }
  &--text {
    color: #B2C3CD;
    font-size: 16px;
    font-weight: normal;
  }
  &--img_container {
    margin-top: 20px;
    width: 200px;
    .text_alt {
      text-align: center;
      color: #B2C3CD;
      font-weight: normal;
      font-size: 15px;
    }
    .pc_no_network_div {
      width: 200px;
      text-align: center;
      height: 100%;
      border-radius: 8px;
      background: #f5f7fa;
      color: #cacaca;
      position: absolute;
      img {
        width: 56px;
        height: 49px;
        margin: 40px 0 20px;
      }
    }
  }
  &--header {
    margin-top: 35px;
    display: flex;
    align-items: center;
    .title {
      color: #567485;
      font-size: 18px;
      font-weight: 700;
    }
    .refresh {
      width: 50px;
      margin-left: 20px;
      cursor: pointer;
      color: #fff;
      text-align: center;
      height: 32px;
      line-height: 32px;
      font-size: 16px;
      font-weight: normal;
      background-color: @themeBackGroundColor;
      border-radius: 6px;
    }
  }
  &--bindUser {
    width: 338px;
    margin-top: 20px;
    min-height: 66px;
    border: 1px solid #E5E6E8;
    border-radius: 8px;
    padding: 12px 16px;
    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .empty {
      width: 100%;
      text-align: center;
      color: #B2C3CD;
      font-size: 16px;
      font-weight: normal;
      line-height: 42px;
    }
    .date {
      color: #B2C3CD;
      font-size: 14px;
      font-weight: 400;
    }
    .unBind {
      border: 1px solid #F64C4C;
      border-radius: 6px;
      width: 50px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      color: #F64C4C;
      cursor: pointer;
      font-size: 16px;
    }
    .clamp {
      width: 240px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.overdueDialog {
  position: relative;
  z-index: 800;
  margin: 0 auto;
  margin-top: 200px;
  background: #FFF;
  width: 480px;
  overflow: hidden;
  border-radius: 6px;
  color:#567485;
}
.overdueContent {
  padding: 20px;
  /deep/.el-input.el-input--suffix {
    font-size: 16px;
    color: @themeFontColor;
    /deep/.el-input__inner {
      font-family: 'DinMedium', sans-serif !important;
    }
    font-family: 'DinMedium', sans-serif !important;
  }
  /deep/.el-input__inner {
    font-size: 16px;
    color: @themeFontColor;
    font-family: 'DinMedium', sans-serif !important;
  }
}
.determine {
  border-radius:4px;
  background: @themeBackGroundColor;
  color: #fff;cursor:pointer;
  font-weight: 700;
  font-size: 18px;
  width:100%;
  height:46px;
  text-align: center;
  line-height: 44px;
  margin: 0 8px;
  border: 1px solid @themeBackGroundColor;
}
.add-img {
  width: 178px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border: 1px solid #B2C3CD;
  cursor: pointer;
}

.el-icon-plus {
  font-size: 24px;
}

.temp-1-mask {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  background: gray;
  opacity: .6;
}

.temp-1-add-text {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  line-height: 28px;
  color: white;
  font-size: 14px;
  text-align: center;
  z-index: 9;
}

.maxline-1 {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  word-break: break-all;
}

.temp-2-mask {
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 70px;
  background: white;
  opacity: .7;
  border-radius: 50% 50%;
}

.temp-2-add-text {
  position: absolute;
  top: 15px;
  left: 10px;
  right: 10px;
  height: 70px;
  line-height: 70px;
  color: black;
  font-size: 14px;
  text-align: center;
  z-index: 9;
}
</style>
<template>
  <div class="pc_set">
    <v-SettingMG></v-SettingMG>
    <div style="width: 100%;background: #FFF;height: calc(100% - 49px);border: 1px solid #e3e6eb;border-radius: 5px;overflow: hidden;">
      <!--左侧部分-->
      <div class="pc_set1">
        <!--顶部白色空白-->
        <div class="pc_set11"></div>
        <!--配置部分-->
        <div
          class="pc_set12"
          @click="goPageTag(1)"
          :class="selectRow === 1 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              v-show="selectRow === 1"
              src="../../image/pc_setting_left_icon1_1.png"
            />
            <img alt=""
              v-show="selectRow !== 1"
              src="../../image/pc_setting_left_icon1_2.png"
            />
          </div>
          <div class="pc_set14">店铺信息</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(2)"
          :class="selectRow === 2 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              v-show="selectRow === 2"
              src="../../image/pc_setting_left_icon2_1.png"
            />
            <img alt=""
              v-show="selectRow !== 2"
              src="../../image/pc_setting_left_icon2_2.png"
            />
          </div>
          <div class="pc_set14">系统设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(4)"
          :class="selectRow === 4 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              v-show="selectRow === 4"
              src="../../image/pc_setting_left_icon4_1.png"
            />
            <img alt=""
              v-show="selectRow !== 4"
              src="../../image/pc_setting_left_icon4_2.png"
            />
          </div>
          <div class="pc_set14">修改密码</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(5)"
          :class="selectRow === 5 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              v-show="selectRow === 5"
              src="../../image/pc_setting_left_icon5_1.png"
            />
            <img alt=""
              v-show="selectRow !== 5"
              src="../../image/pc_setting_left_icon5_2.png"
            />
          </div>
          <div class="pc_set14">价格设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(3)"
          :class="selectRow === 3 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              v-show="selectRow === 3"
              src="../../image/pc_setting_left_icon3_1.png"
            />
            <img alt=""
              v-show="selectRow !== 3"
              src="../../image/pc_setting_left_icon3_2.png"
            />
          </div>
          <div class="pc_set14">配件设置</div>
        </div>
        <div
          v-if="ultimate !== null && showMicroShop && loginInfo.employeeNumber === ''"
          class="pc_set12"
          @click="goPageTag(12)"
          :class="selectRow === 12 ? 'pc_set34' : ''">
          <div class="pc_set13">
            <img
              alt=""
              style="width: 24px; height: 24px; margin-top: 9px; margin-left: 3px"
              v-show="selectRow === 12"
              src="../../image/pc_microShop_white.png"
            />
            <img
              alt=""
              style="width: 24px; height: 24px; margin-top: 9px; margin-left: 3px"
              v-show="selectRow !== 12"
              src="../../image/pc_microShop.png"
            />
          </div>
          <div class="pc_set14">微店设置</div>
        </div>

        <div
          class="pc_set12"
          @click="goPageTag(11)"
          :class="selectRow === 11 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              style="width: 24px;height: 24px;margin-top: 9px;margin-left: 3px;"
              v-show="selectRow === 11"
              src="../../image/setting_whiteCloud.png"
            />
            <img alt=""
              style="width: 24px;height: 24px;margin-top: 9px;margin-left: 3px;"
              v-show="selectRow !== 11"
              src="../../image/setting_blackCloud.png"
            />
          </div>
          <div class="pc_set14">云同步设置</div>
        </div>
        <div
          class="pc_set12"
          @click="goPageTag(13)"
          :class="selectRow === 13 ? 'pc_set34' : ''"
        >
          <div class="pc_set13">
            <img alt=""
              style="width: 24px;height: 24px;margin-top: 9px;margin-left: 3px;"
              v-show="selectRow === 13"
              src="../../image/wechat_icon_white.png"
            />
            <img alt=""
              style="width: 24px;height: 24px;margin-top: 9px;margin-left: 3px;"
              v-show="selectRow !== 13"
              src="../../image/wechat_icon.png"
            />
          </div>
          <div class="pc_set14">微信助手</div>
        </div>
      </div>
      <!--右侧部分-->
      <div class="pc_set18"
        v-loading="msLoading">
        <div class="pc_set_video" v-show="showVideo && selectRow !== 13">
          <div style="margin-left: 5px;display: inline-block;margin-right: 5px;vertical-align: middle;margin-top: 10px;">
            <div style="cursor: pointer;text-align: right;font-size: 24px;margin-top: -15px;line-height: 30px;" @click="closeVideo">×</div>
            <div>
              <img style="height: 100px;width: 100px;" alt="" src="../../image/pc_video.png"/>
            </div>
            扫一扫查看视频教程
          </div>
        </div>
        <!-- <div class="pc_set_cat_img" v-show="showCatImg">
          <img style="height: 33px; width: 144px;" alt="" src="../../image/cat.png" />
        </div>
        <div class="pc_set_cat" v-show="showCatImg">
          <div style="margin-left: 5px;display: inline-block;margin-right: 5px;vertical-align: middle;margin-top: 10px;">
            <div style="cursor: pointer;text-align: right;font-size: 24px;margin-top: -15px;line-height: 30px;width: 127px" @click="showCatImg = false">×</div>
            <div>
              <img style="height: 100px;width: 100px;" alt="" src="../../image/peijian.png"/>
            </div>
            扫一扫购买配件
          </div>
        </div> -->
        <!--店铺设置部分-->
        <div v-show="selectRow === 1">
          <div style="overflow: hidden;">
            <div class="pc_set19">版本期限</div>
            <div class="pc_ultimate_div">
              <div style="line-height: 38px;font-size: 16px;">
                <span class="pc_ultimate_span">{{ultimateDetail.ultimateName}}</span>
                <span v-if="ultimateDetail.period !== -1">将于{{ultimateDetail.endDate}}到期</span>
                <span v-if="ultimateDetail.period === -1">终身使用</span>
              </div>
              <div v-if="isAuto && ultimateDetail.period !== -1 && ultimate !== null" class="pc_renew_btn" @click="renewClick()">立即续费</div>
              <div v-if="isAuto && ultimate === null" class="pc_renew_btn" @click="renewClick(true)">升级</div>
            </div>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_set19">店铺名称</div>
            <el-input
              v-model.trim="storeinfo.name"
              maxlength="12"
              placeholder="请输入店铺名称"
              class="pc_set2"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="storeinfo.name = checkStoreInfo(storeinfo.name)"
            ></el-input>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_set19">收银员名</div>
            <el-input
              v-model.trim="cashier"
              maxlength="12"
              placeholder="请输入收银员名字"
              class="pc_set2"
              :readonly="!network"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="cashier = cashier.replace(/[$']/g, '')"
            ></el-input>
          </div>
          <div
            v-show="!network"
            style="font-size: 12px;margin-left: 145px;margin-top: -18px;line-height: 24px;color: #ed6a0c;"
          >请检查网络，在联网的状态下修改收银员名字！</div>
          <div style="overflow: hidden;">
            <div class="pc_set19">所在地区</div>
            <el-cascader
              placeholder="试试搜索：北京"
              :options="areaList"
              class="pc_set2"
              clearable
              @change="addrChange"
              v-model="selectedOption"
              filterable></el-cascader>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_set19">详细信息</div>
            <el-input
              v-model.trim="addr"
              maxlength="50"
              placeholder="请输入店铺地址"
              class="pc_set2"
              @compositionstart='pinyin = true'
              @compositionend='pinyin = false'
              @input="addrChange"
            ></el-input>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_set19">行业范围</div>
            <el-select
              v-model="storeinfo.industry"
              id="set_hy"
              style="font-size:16px;"
            >
              <el-option
                style="font-size: 16px;margin-top: 6px;width: 120px;text-align: center;"
                :style="(index + 1) % 4 != 0 ? 'float: left;' : ''"
                v-for="(bs,index) in $t('page.setting.business_scope')"
                v-bind:key="index"
                :label="bs.label"
                :value="bs.label"
              ></el-option>
            </el-select>
          </div>
          <!--删除店铺数据确认弹窗-->
          <div
            v-show="delete_receipt"
            style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
          >
            <div
              style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
              @click="delete_receipt = false"
            ></div>
            <div
              style="position: relative;z-index: 800;height: 250px;margin: 0 auto;margin-top: 240px;
              background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
            >
              <div
                style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: normal;"
              >提示</div>
              <div
                style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
              >删除后不可恢复，是否删除单据？</div>
              <div class="pc_del1">
                <div class="pc_del2" @click="delete_receipt = false">取消</div>
                <div class="pc_del3" @click="batchDelReceipt();delete_receipt = false">删除</div>
              </div>
            </div>
          </div>
          <!--删除店铺数据弹窗-->
          <div class="del_store_data_div">
            <el-dialog
              :visible="showDelStoreData"
              width="1000px"
              height="664px"
              :show-close='false'
              :top="delBoxMargin"
              :modal-append-to-body='false'
            >
              <div class="pc_hed151">
                <div class="pc_header_container">
                  <div class="tab_container_deldata">
                    <div
                      v-for="(item,index) in delDataArr"
                      :key="index"
                      :class="['tab', delData_index == index ? 'tab_active_btn' : '',index == 0 ? 'tab_one' : '',index == delDataArr.length - 1 ? 'tab_last' : '']"
                      @click="clickDelReceipt(index)"
                    >{{item}}</div>
                  </div>
                </div>
                <div
                  class="deldata_close"
                  @click="closeDelStoreData"
                >×</div>
                <div class="pc_dialog_topleft">
                  <div
                    class="datepicker_container"
                    :class="focusDate ? 'focusDate' : 'focusDate1'"
                  >
                    <el-date-picker
                      v-model="fromDate"
                      type="date"
                      placeholder="开始日期"
                      style="height:44px"
                      @focus="focusDate = true"
                      @blur="focusDate = false"
                      value-format='yyyy-MM-dd'
                    >
                    </el-date-picker>
                    <div style="font-size: 16px;color: #567485;margin-top: 2px;font-weight: normal;">至</div>
                    <el-date-picker
                      v-model="toDate"
                      type="date"
                      placeholder="结束日期"
                      style="height:44px"
                      @focus="focusDate = true"
                      @blur="focusDate = false"
                      value-format='yyyy-MM-dd'
                    >
                    </el-date-picker>
                  </div>
                  <div class="pc_header_search" @click="beforeSearchReceipt()">查询</div>
                  <div class="pc_header_batchDel" @click="beforeBatchDelReceipt()">批量删除</div>
                </div>
                <div class="deldata_tab_div">
                  <el-table
                    :data="del_data"
                    stripe
                    :header-cell-style="tableHeaderColor"
                    style="width: 100%;height: 432px;font-size: 16px;overflow: auto"
                    @selection-change="getSelectedReceipt"
                    ref="deltableCurrentRow"
                    @row-click = "selCurRow"
                  >
                    <el-table-column
                      type="selection"
                      width="55"
                    >
                    </el-table-column>
                    <el-table-column prop="code" show-overflow-tooltip :key="1" width="190" label="单号"></el-table-column>
                    <el-table-column
                      prop="createtime"
                      show-overflow-tooltip
                      :key="2"
                      width="170"
                      label="生成时间"
                    >
                      <template slot-scope="scope">
                        <div>{{(scope.row.createtime).substr(0, scope.row.createtime.lastIndexOf(':'))}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="account_name"
                      v-if="this.deltype === 'xs'"
                      show-overflow-tooltip
                      :key="3"
                      width="150"
                      label="支付方式"
                    ></el-table-column>
                    <el-table-column
                      prop="account_name"
                      v-if="this.deltype === 'jh'"
                      show-overflow-tooltip
                      :key="3"
                      width="100"
                      label="支付方式"
                    ></el-table-column>
                    <el-table-column
                      prop="account_qty"
                      v-if="this.deltype === 'pd'"
                      show-overflow-tooltip
                      :key="3"
                      width="120"
                      align="right"
                      label="盘前库存"
                    ></el-table-column>
                    <el-table-column
                      prop="vipname"
                      v-if="this.deltype === 'xs'"
                      show-overflow-tooltip
                      :key="4"
                      width="100"
                      label="会员"
                    ></el-table-column>
                    <el-table-column
                      prop="supplier_name"
                      v-if="this.deltype === 'jh'"
                      show-overflow-tooltip
                      :key="4"
                      width="150"
                      label="供应商"
                    ></el-table-column>
                    <el-table-column
                      prop="actual_qty"
                      v-if="this.deltype === 'pd'"
                      show-overflow-tooltip
                      :key="4"
                      align="right"
                      width="100"
                      label="盘后库存"
                    ></el-table-column>
                    <el-table-column
                      v-if="this.deltype === 'xs'"
                      show-overflow-tooltip
                      :key="5"
                      width="100"
                      align="right"
                      label="实收金额">
                      <template slot-scope="scope">
                        <div>{{ scope.row.in_out =='1' ? (Number(scope.row.disc_amt) + Number(scope.row.change_amt)).toFixed(2) : Number(scope.row.disc_amt).toFixed(2)}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="this.deltype === 'jh'"
                      show-overflow-tooltip
                      :key="5"
                      width="100"
                      align="right"
                      label="本单实付">
                      <template slot-scope="scope">
                        <div>{{(Number(scope.row.pay_amt)).toFixed(2)}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="this.deltype === 'pd'"
                      show-overflow-tooltip
                      :key="5"
                      width="100"
                      align="right"
                      label="盈亏总数"
                    >
                      <template slot-scope="scope">
                        <div>{{scope.row.diff_qty}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="uid_no"
                      v-if="this.deltype !== 'pd'"
                      show-overflow-tooltip
                      :key="6"
                      width="90"
                      label="收银员">
                      <template slot-scope="scope">
                        <div>{{scope.row.uid_no}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="this.deltype === 'pd'"
                      show-overflow-tooltip
                      :key="6"
                      width="120"
                      align="right"
                      label="总盈亏金额">
                      <template slot-scope="scope">
                        <div>{{"￥" + Number(scope.row.diff_amt).toFixed(2)}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="in_out"
                      v-if="this.deltype === 'xs'"
                      show-overflow-tooltip
                      :key="7"
                      width="102"
                      align="center"
                      label="单据类型">
                      <template slot-scope="scope">
                        <div>{{scope.row.in_out === '1' ? '收款' : '退款'}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      prop="in_out"
                      v-if="this.deltype === 'jh'"
                      show-overflow-tooltip
                      :key="7"
                      width="102"
                      align="center"
                      label="单据类型">
                      <template slot-scope="scope">
                        <div>{{scope.row.in_out === '1' ? '进货' : '退货'}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="this.deltype === 'pd'"
                      show-overflow-tooltip
                      :key="7"
                      width="102"
                      label="收银员">
                      <template slot-scope="scope">
                        <div>{{scope.row.uid_no}}</div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div style="overflow: hidden;margin-top: 10px;margin-bottom: 10px;">
                    <div class="pc_selcount_div" style="font-weight:normal;display: inline-block;margin-left:22px;font-size:16px;letter-spacing: 0px;">
                      已选中
                      <span style="color: #BEA66E;">{{this.selectTableData.length}}</span> 条单据
                    </div>
                    <div style="float: right;margin-right: 20px;">
                      <el-pagination
                        layout="prev, pager, next"
                        :total="total"
                        @current-change="handleCurrentChange"
                        :current-page.sync="pagenum"
                        :page-size="limit"
                        :page-count="total"
                      ></el-pagination>
                    </div>
                  </div>
                </div>
                <div style="height: 18px;"></div>
              </div>
            </el-dialog>
          </div>
          <div style="overflow: hidden;">
            <div class="pc_set19">店铺数据</div>
            <div class="clear_btn" @click="confirmDelData">清空数据</div>
            <el-popover
              @show="setClass()"
              popper-class="pc_pay192 pop_padding"
              placement="bottom-start"
              width="411"
              trigger="click"
              content="清空本店铺下商品，进货，销售，员工，会员等业务数据；注册后21天内可用；清空后无法恢复，请谨慎操作。">
              <div class="com_pae20" slot="reference">?</div>
            </el-popover>
          </div>
          <div>
            <div class="pc_set19">注销账号</div>
            <div class="clear_btn delete_btn" @click="deleteAccountShow = true">注销账号</div>
            <el-popover
              @show="setClass()"
              popper-class="pc_pay192 pop_padding"
              placement="top"
              trigger="click"
              content="注销账号：清空产品相关使用权限及业务数据。清空后无法恢复，请谨慎操作。">
              <div class="com_pae20" slot="reference">?</div>
            </el-popover>
          </div>
          <!-- 清除数据确认弹窗 -->
          <div
            v-show="isconfirmDel"
            style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
          >
            <div
              style="display:flex;align-items:center;width: 100%;height: 100%;background: rgba(0,0,0,.5);"
            >
              <div
                style="position: relative;z-index: 800;height: 537px;margin: 0 auto;
                  background: #FFF;width: 900px;overflow: hidden;border-radius: 5px;padding: 20px;"
              >
                <div style="display:flex;justify-content: space-between;border-bottom: 1px solid #E3E6EB;padding-bottom:10px;">
                  <div style="width: 100%;font-size: 20px;color: #567485;font-weight: 700;">清空数据</div>
                  <div><span style="font-size: 36px;line-height: 28px;cursor:pointer;" @click="isconfirmDel = false;">×</span></div>
                </div>
                <div class="del_date_div">
                  <el-alert
                    :title="deleteDataTitle"
                    type="error"
                    :closable="false"
                    width='93%'
                    dangerous
                    style="margin-top: 15px auto;font-size: 18px !important;">
                    <template slot="title">
                      <div style="display:flex;align-items:center;font-size:16px;font-weight: 500;line-height: 24px;">
                        <i class="el-icon-warning" style="color: #F64C4C;font-size: 18px;"></i>
                        <div style="margin-left:8px;">
                          <div>1.业务数据清空成功之后将无法恢复，请慎重操作</div>
                          <div>2.为防止数据产生异常，请确保其它设备（PC端、移动端）上的掌柜智囊程序已升级到最新版本</div>
                          <div>3.若在多台设备使用此账号，请
                            <span style="text-decoration:underline;font-weight:bold">及时登录</span>
                          并确认数据一致后再使用</div>
                        </div>
                      </div>
                    </template>
                  </el-alert>
                </div>
                <span style="font-size:16px;color: #537286;font-weight: 600;">请勾选想要清除的数据</span>
                <div class="del_date_checkbox_div">
                  <el-checkbox label="全部数据" v-model="isCheckAll" :indeterminate="indeterminateVal"
                    @change="checkAllChange">
                  </el-checkbox>
                  <el-checkbox-group v-model="checkBoxVal" @change="checkBoxChange">
                    <el-checkbox v-for="op in checkBoxOpt" :label="op.id" :key="op.id" :disabled="checkDisabled.indexOf(op.id) !== -1">
                      <div style="width: 220px;font-size: 16px;white-space: normal;
                          font-weight: 500;color: #567485;margin-top: 8px;">{{op.name}}</div>
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
                <div style="display: flex;justify-content:center;align-items:center;margin-top: 40px;">
                  <el-checkbox v-model="clearExoneration"></el-checkbox>
                  <div style="margin-left: 6px;font-size:16px;color: #537286;">
                    <div>本操作产生的后果将由您自行承担，与本产品无关。</div>
                    <div>您一旦进行清空操作，即表示您已知悉相应后果。请确保已充分理解且为您本人操作。</div>
                  </div>
                </div>
                <div style="display: flex;justify-content:center;align-items:center;font-size:24px;margin-top: 24px;">
                  <div class="pc_del3"
                    style="width:180px;height:56px;line-height: 56px;background: #fff;color: #B4995A;font-weight: bold;"
                    @click="isconfirmDel = false">取消</div>
                  <div
                    class="pc_del3"
                    style="width:180px;height:56px;line-height: 56px;"
                    :style="clearExoneration ? '' : 'opacity: 0.3;cursor: not-allowed;'"
                    @click="clearExoneration ? beforeDelAllData() : checkedBeforeClear()">确定清空</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 清空所有数据弹窗 -->
          <!-- <div
            v-show="showDelAllData"
            style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
          >
            <div
              style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
            ></div>
            <div
              class="newcheck_div"
              style="position: relative;z-index: 800;height: 330px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
            >
              <div
                style="width: 100%;text-align: center;font-size: 25px;color: #567485;margin-top: 40px;font-weight: bold;"
              >请输入验证码</div>
              <div
                style="width: 100%;margin-left: 60px; font-size: 25px;color: #567485;margin-top: 25px;font-weight: 100;"
              >
                <input class="verify_ipt" v-model.trim="verifycode" @input="verifycode = $allNumberLimit(verifycode)"  maxlength="6" />
                <div style="display: inline-block; margin-left: -120px; width: 120px;" @click="resendVerify">
                  <van-count-down :time='countdownTime' ref="countDown" :auto-start="false" :key="isResend" :format="isResend? '发送验证码' : '重新发送(ss)'" @finish='finished'/>
                </div>
              </div>
              <div class="van-count-down" style="margin-left: 68px; margin-right: 68px;margin-top:10px;">为防止误操作我们将给您的注册手机发送一条验证码，请填入验证码确定操作</div>
              <div class="pc_del1">
                <div class="pc_del2" @click="closeDelAllData()">取消</div>
                <div class="pc_del3" @click="callClearData()">确定</div>
              </div>
            </div>
          </div> -->
          <div
            v-show="showDelAllData"
            style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
          >
            <div
              style="display:flex;align-items:center;width: 100%;height: 100%;background: rgba(0,0,0,.5);"
            >
              <div
                class="newcheck_div"
                style="position: relative;z-index: 800;height: 280px;margin: 0 auto;margin-top: 70px;
                  background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
              >
                <div
                  style="display:flex;justify-content: space-between;border-bottom: 1px solid #E3E6EB;
                    margin: 24px;padding-bottom:10px;">
                  <div style="width: 100%;font-size: 18px;color: #567485;font-weight: 700;">验证码</div>
                  <div><span style="font-size: 36px;
                             line-height: 28px;cursor:pointer;" @click="closeDelAllData()">×</span></div>
                </div>
                <div
                  style="width: 100%;margin-left: 24px;font-size: 18px;color: #567485;margin-top: 6px;"
                >
                  <div style="margin-bottom: 10px;">
                    将发至注册人手机{{sysUid.toString().substr(0,3)}}****{{sysUid.toString().substr(7,4)}}
                  </div>
                  <input class="verify_ipt" v-model="verifycode" @input="verifycode = $allNumberLimit(verifycode)"
                    placeholder="请输入验证码" maxlength="6"/>
                  <div
                    style="display: inline-block; width: 110px;font-size: 18px;color: #CBAB63;cursor: pointer;"
                    @click="resendVerify">
                    <van-count-down :time="countdownTime" ref="countDown" :auto-start="false"
                      :key="isResend" :format="isResend? '发送验证码' : '重新发送(ss)'" @finish="finished"/>
                  </div>
                </div>
                <!-- <div class="van-count-down" style="margin-left: 68px; margin-right: 68px;margin-top:10px;">
                  为防止误操作我们将给您的注册手机发送一条验证码，请填入验证码确定操作
                </div> -->
                <div style="display: flex;justify-content:center;align-items:center;font-size:20px;margin-top: 24px;">
                  <div class="pc_del3"
                    style="width:150px;height:50px;line-height: 50px;background: #fff;color: #B4995A;font-weight: bold;"
                    @click="closeDelAllData()">取消</div>
                  <div
                    class="pc_del3"
                    style="width:150px;height:50px;line-height: 50px;"
                    @click="callClearData()">确定</div>
                </div>
              </div>
            </div>
          </div>
          <div
            style="overflow: hidden;"
            v-show="false"
          >
            <div class="pc_set19">经营方式</div>
            <div style="overflow: hidden;">
              <div
                class="pc_set35"
                :class="mode == '批发' ? 'pc_set36' : ''"
                @click="mode = '批发'"
              >
                批发
                <img
                  alt=""
                  v-show="mode == '批发'"
                  src="../../image/zgzn-pos/pc_pay_duigou.png"
                  style="position: absolute;right: 0;bottom: 0;"
                />
              </div>
              <div
                class="pc_set35"
                :class="mode == '零售' ? 'pc_set36' : ''"
                @click="mode = '零售'"
              >
                零售
                <img
                  alt=""
                  v-show="mode == '零售'"
                  src="../../image/zgzn-pos/pc_pay_duigou.png"
                  style="position: absolute;right: 0;bottom: 0;"
                />
              </div>
              <div
                class="pc_set35"
                :class="mode == '批零兼营' ? 'pc_set36' : ''"
                @click="mode = '批零兼营'"
              >
                批零兼营
                <img
                  alt=""
                  v-show="mode == '批零兼营'"
                  src="../../image/zgzn-pos/pc_pay_duigou.png"
                  style="position: absolute;right: 0;bottom: 0;"
                />
              </div>
            </div>
          </div>
        </div>
        <!--系统设置部分-->
        <div v-show="selectRow === 2">
          <div
            class="msg_item" style="margin-top: 7px;"
          >
            <div class="notice_title">销售设置</div>
            <el-switch
              v-model="setSystem"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
            <div class="notice_remark">支持无库存商品做销售单</div>
          </div>
          <div
            class="msg_item"
          >
            <div class="notice_title">负利润提醒</div>
            <el-switch
              v-model="set_priceNotice"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
            <div class="notice_remark">当单品售价小于进价时，收银台提醒，仍可以结算</div>
          </div>
          <div
            class="msg_item"
          >
            <div class="notice_title">库存显示</div>
            <el-switch
              v-model="set_StockShow"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
            <div class="notice_remark">在收银台中显示商品的库存</div>
          </div>
          <div
            class="msg_item"
          >
            <div class="notice_title">语音播报</div>
            <el-switch
              v-model="voiceOff"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
            <div class="notice_remark">收银台扫码成功、收款成功时语音播报</div>
          </div>
          <!-- 扫码提示音 -->
          <div
            class="msg_item"
          >
            <div class="notice_title">扫码提示音</div>
            <el-switch
              v-model="scanCodeSound"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
            ></el-switch>
            <div class="notice_remark">收银台扫码成功加入购物列表时语音播报“1件”</div>
          </div>
          <!-- 热销分类收银台是否显示 -->
          <div
            class="msg_item"
          >
            <div class="notice_title">热销分类显示</div>
            <el-switch
              v-model="showHotGood"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              @change="hotStatusChange"
            ></el-switch>
            <div class="notice_remark">在收银台页面展示销量排名前30的商品</div>
          </div>
          <!-- 过期预警通知是否开启，默认开启 -->
          <div
            class="msg_item"
          >
            <div class="notice_title">过期预警通知</div>
            <el-switch
              v-model="expiredWarningNotice"
              :active-color="$t('image.homeImage.color')"
              inactive-color="#DCDFE6"
              @change="expiredWarningNoticeChange"
            ></el-switch>
            <div v-show="!expiredWarningNotice" class="notice_remark">在系统中提前通知保质状态异常的商品</div>
            <div v-show="expiredWarningNotice" class="notice_remark">在系统中通知{{overdueDate}}天内过期的商品<span style="color: #b4995a;margin-left: 20px;cursor: pointer;" @click="openOverdueDate">编辑</span></div>
          </div>
          <div
            class="msg_item"
            v-if="voiceOff"
          >
            <div class="notice_title">音量增强</div>
            <div class="voice_slider_div">
              <div class="voice_slider_low" @click="subVolume()">弱</div>
              <div style="width: 200px;">
                <el-slider
                  v-model="soundVolume"
                  :min="1"
                  :max="5"
                  show-stops>
                </el-slider>
              </div>
              <div class="voice_slider_heigh" @click="addVolume()">强</div>
            </div>
          </div>
          <!-- 收银台默认支付方式 -->
          <div
            class="msg_item"
          >
            <div class="notice_title">收银台默认支付方式</div>
            <el-select
              v-model="defaultAcctsId"
              placeholder=" ">
              <el-option
                v-for="op in defaultAcctsOptions"
                :key="op.value"
                :value="op.value"
                :label="op.label">
              </el-option>
            </el-select>
            <div class="notice_remark">如果在收银台选择会员，结算时默认选择会员支付</div>
          </div>
          <!-- 收银台结算快捷键 -->
          <!-- <div
            class="msg_item"
          >
            <div class="notice_title">收银台结算快捷键</div>
            <div style="margin-top: 2px;">
              <el-radio v-model="defaultSettlementKey" label="Enter">
                Enter（回车）</el-radio>
              <div></div>
              <el-radio v-model="defaultSettlementKey" label="Space">
                Space（空格）</el-radio>
            </div>
          </div> -->
        </div>
        <!--会员设置部分-->
        <div v-show="selectRow === 9">
          <div class="pc_set21">会员设置搬家啦！</div>
          <div class="pc_set22" style="margin-top: 18px;margin-left: -6px">
            【会员设置】已经移到【会员】功能的右上角。
          </div>
          <el-image :src="require('../../image/pc_setting_member.png')" style="margin-top: 37px;width: 728px;height: 310px"/>
        </div>
        <!--消息设置部分-->
        <div v-show="selectRow === 10">
          <div class="pc_set21">消息设置搬家啦！</div>
          <div class="pc_set22" style="margin-top: 18px;margin-left: -6px">
            【消息设置】已经移到【会员】-【微信消息设置】里去啦。
          </div>
          <el-image :src="require('../../image/pc_setting_member.png')" style="margin-top: 37px;width: 728px;height: 310px"/>
        </div>
        <!--修改密码部分-->
        <div
          v-show="selectRow === 4"
          class="pc_set24"
        >
        <el-row>
          <!-- <div style="margin-left: 13px;color: red;width: 20px;float: left;">*</div> -->
          <div class="pc_set19">原密码</div>
          <el-input
            placeholder="请输入原密码"
            v-model.trim="old_pwd"
            clearable
            style="width:60%;"
          ></el-input>
        </el-row>
        <el-row>
          <!-- <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div> -->
          <div class="pc_set19">新密码</div>
          <el-input
            placeholder="请输入（长度为8～20个字符）由数字、字母组成的新密码"
            v-model.trim="new_pwd"
            :type="pwdType"
            maxlength="20"
            style="margin-top:1vh; width:60%;"
          >
            <img
              slot="suffix"
              class="icon-style"
              :src="pwdElIcon"
              @click="showFlag = !showFlag"
            />
          </el-input>
        </el-row>
        <el-row>
          <!-- <div style="margin-left: 13px;color: red;width: 20px;float: left;margin-top: 10px;">*</div> -->
          <div class="pc_set19">确认新密码</div>
          <el-input
            placeholder="请再次输入新密码"
            v-model.trim="new_pwd1"
            :type="newPwdType"
            maxlength="20"
            style="margin-top:1vh; width:60%;"
          >
            <img
              slot="suffix"
              class="icon-style"
              :src="newPwdElIcon"
              @click="newShowFlag = !newShowFlag"
            />
          </el-input>
        </el-row>
        <el-row>
          <div style="margin-left: 120px;color: red;width: 20px;float: left;margin-top: 10px;">&nbsp;</div>
          <el-button
            @click="updPassword"
            :disabled="btnPwdAble"
            id="updPassword"
            style="margin-top:1vh; width:60%;height: 40px;text-align: center;border: 1px solid #e3e6eb;
              border-radius: 5px;color: #FFF;cursor: pointer;"
          >确定</el-button>
        </el-row>
        </div>
        <!--价格设置部分-->
        <div
          v-show="selectRow === 5"
          class="pc_set24"
        >
          <div style="float: left;overflow: hidden;">
            <div
              class="pc_set35"
              :class="set_price == 0 ? 'pc_set36' : ''"
              @click="set_price = 0"
            >
              不抹零
              <img
                alt=""
                v-show="set_price == 0"
                src="../../image/zgzn-pos/pc_pay_duigou.png"
                style="position: absolute;right: 0;bottom: 0;"
              />
            </div>
            <div
              class="pc_set35"
              :class="set_price == 1 ? 'pc_set36' : ''"
              @click="set_price = 1"
            >
              抹分
              <img
                alt=""
                v-show="set_price == 1"
                src="../../image/zgzn-pos/pc_pay_duigou.png"
                style="position: absolute;right: 0;bottom: 0;"
              />
            </div>
            <div
              class="pc_set35"
              :class="set_price == 2 ? 'pc_set36' : ''"
              @click="set_price = 2"
            >
              抹角
              <img
                alt=""
                v-show="set_price == 2"
                src="../../image/zgzn-pos/pc_pay_duigou.png"
                style="position: absolute;right: 0;bottom: 0;"
              />
            </div>
            <div
              class="pc_set35"
              :class="set_price == 3 ? 'pc_set36' : ''"
              @click="set_price = 3"
            >
              四舍五入到角
              <img
                alt=""
                v-show="set_price == 3"
                src="../../image/zgzn-pos/pc_pay_duigou.png"
                style="position: absolute;right: 0;bottom: 0;"
              />
            </div>
            <div
              class="pc_set35"
              :class="set_price == 4 ? 'pc_set36' : ''"
              @click="set_price = 4"
            >
              逢分进角
              <img
                alt=""
                v-show="set_price == 4"
                src="../../image/zgzn-pos/pc_pay_duigou.png"
                style="position: absolute;right: 0;bottom: 0;"
              />
            </div>
          </div>
        </div>
        <!--配件设置部分-->
        <div
         v-show="selectRow === 3"
         class="pc_setParts"
         @click="visible = false"
         >
          <el-tabs
            :value="settingIndex"
            type="card"
            style="height: calc(100%);"
            :before-leave="tabsBeforeLeave"
            @tab-click="handleClick"
          >
            <el-tab-pane
              label="小票打印"
              name="tab0"
            >
              <div class="pc_set22" style="width: 55%;display: inline-block;">
                <div class="pc_setParts2">
                  <div class="pc_setParts0" id="setParts0">基础设置</div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">选择打印机</div>
                  <div class="pc_setParts1" style="width: 68%;max-width: 348px;">
                    <el-select
                      v-model="smallPrinterValue"
                      placeholder=""
                    >
                      <el-option
                        v-for="pr in receiptPrinterList"
                        :key="pr.label"
                        :label="pr.label"
                        :value="pr.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">打印机驱动</div>
                  <div class="pc_setParts1">
                    <el-select
                      v-model="printMode"
                    >
                      <el-option
                        v-for="item in printDrives"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">默认打印份数</div>
                  <div class="pc_setParts1">
                    <el-input
                      v-model="smallPrinterCopies"
                      @blur="setNum('smallPrinterCopies')"
                      @input="smallPrinterCopies = $intMaxMinLimit({data: smallPrinterCopies, max: 999, min: 0})"
                      placeholder="最大输入999"
                    ></el-input>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">小票规格</div>
                  <div class="pc_setParts1" style="margin-top: 10px;width: 236px;">
                    <div style="text-align: right">
                      <div
                        @click="printCols = '58'"
                        class="setting_radio"
                        :class="printCols === '58' ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="printCols === '58'" class="setting_radio_inner"></div>
                      </div>
                      <div class="pc_setRadio printPoint" @click="printCols = '58'" style="cursor: pointer;">58mm</div>
                      <div
                        @click="printCols = '80'"
                        class="setting_radio"
                        style="margin-left: 62px;"
                        :class="printCols === '80' ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="printCols === '80'" class="setting_radio_inner"></div>
                      </div>
                      <div class="pc_setRadio printPoint" @click="printCols = '80'" style="cursor: pointer;">80mm</div>
                    </div>
                  </div>
                </div>
                <!-- <div class="pc_setParts2">
                  <div class="pc_setParts0">正文字号</div>
                  <div class="pc_setParts1">
                    <el-select
                      v-model="font_value"
                    >
                      <el-option
                        v-for="item in font_size_options"
                        :key="item.font_value"
                        :label="item.label"
                        :value="item.font_value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div> -->
                <div class="pc_setParts2">
                  <div class="pc_setParts0">行间距</div>
                  <div class="pc_setParts1">
                    <el-select
                      v-model="line_height"
                    >
                      <el-option
                        v-for="item in line_height_options"
                        :key="item.line_height"
                        :label="item.label"
                        :value="item.line_height"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div class="pc_setParts2" style="margin-bottom: 0px;">
                  <div class="pc_setParts0">备注内容
                    <div class="pc_setParts5">({{remark_content.length}}/100)</div>
                  </div>
                </div>
                <div class="pc_setParts2" style="margin-bottom: 20px;">
                  <el-input
                    resize="none"
                    type="textarea"
                    :rows="2"
                    class="pc_setParts6"
                    placeholder="最多可输入100字..."
                    maxlength="100"
                    v-model="remark_content"
                  />
                </div>
                <div v-if="ultimate">
                  <div class="pc_setParts2" style="margin-bottom: 0px;">
                    <div class="pc_setParts0" id="setParts0">个性化设置
                      <div class="pc_setParts5" style="font-weight: 400;font-size: 16px;">（旗舰版专用）</div>
                    </div>
                  </div>
                  <div class="pc_setParts2" style="margin-bottom: 0px;">
                    <div class="pc_setParts0">会员信息</div>
                  </div>
                  <div class="pc_setParts2" style="margin-bottom: 10px;">
                    <div
                      :key="item.key"
                      v-for="item in memberInfoArrs"
                      class="pc_setParts0"
                    >
                      <div class="pc_setParts3" @click="ultimate ?
                        item.flag = !item.flag : ''"
                        :style="ultimate ? (item.flag ? 'background: #CFA26B;cursor: pointer;' : 'cursor: pointer;') : 'background: #D9CCAC;cursor:no-drop'">
                        <img alt="" style="display: block;width: 18px;height: 18px;" v-if="item.flag || ultimate === false" src="../../image/pc_msgMark.png"/>
                      </div>
                      <div class="pc_setParts4">{{item.title}}</div>
                    </div>
                  </div>
                  <div
                    class="pc_setParts2"
                    style="margin-bottom: 0px;"
                    :key="item.key"
                    v-for="(item, index) in logAndCode"
                    >
                    <div class="pc_setParts0">{{item.title + ' '}}<span class="itemRemark">{{item.remark}}</span></div>
                    <div class="pc_setParts1">
                      <el-switch
                        v-model="item.flag"
                      :active-color="$t('image.homeImage.color')"
                        inactive-color="#DCDFE6"
                        @change="setShowJs"
                      ></el-switch>
                    </div>
                    <el-upload
                      class="pic-uploader"
                      action=""
                      accept=".jpg, .jpeg, .png"
                      :show-file-list="false"
                      :data="uploadData"
                      :on-success="handleAvatarSuccess"
                      :before-upload="beforeAvatarUpload"
                      :on-change="getFile"
                      :auto-upload="false"
                    >
                      <img
                        @click="imgIndex = index"
                        alt=""
                        v-show="item.url !== ''"
                        :src="item.url + uploadImg"
                        class="avatar-pic"
                      />
                      <em
                        @click="imgIndex = index"
                        v-show="item.url === ''"
                        class="el-icon-plus pic-uploader-good-icon"
                      ></em>
                      <div
                        @click="imgIndex = index"
                        v-show="item.url !== ''"
                        style="width: 100%;
                          height: 22px;
                          background: rgba(0,0,0, 0.9);
                          position: absolute;bottom: 0;
                          font-size: 14px;
                          text-align: center;
                          line-height: 22px;
                          color: #eee;">
                        点击重新上传</div>
                    </el-upload>
                  </div>
                  <div class="pc_setParts2 pc_setRight_tip" style="margin-top: 0px;margin-bottom: 0px;font-size: 16px">
                      小票打印机精度有限，太过复杂的图像影响识别效果，请先测试后使用
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts7" @click="printTest('small')">
                    打印测试
                  </div>
                </div>
              </div>
              <div class="pc_setRight">
                <div class="pc_setRight_rel">
                  <div v-if="logAndCode[0].flag && logAndCode[0].url !== ''" class="pc_setRight0">
                    <img class="pc_setRight0_img" :src="logAndCode[0].url + uploadImg" alt="">
                  </div>
                  <div class="pc_setRight1">
                    {{storeinfo.name ? storeinfo.name : ''}}
                  </div>
                  <div>
                    销售单号：XSD202012000099
                  </div>
                  <div class="pc_setRight2"></div>
                  <div>
                    交易时间：{{printDate}}
                  </div>
                  <div>
                    收银员：1001
                  </div>
                  <div class="pc_setRight2"></div>
                  <div>
                    <span style="width: 60px;display: inline-block;">原价</span>
                    <span style="width: 70px;display: inline-block;">折扣价</span>
                    <span style="width: 57px;display: inline-block;">数量</span>
                    <span style="display: inline-block;">小计</span>
                  </div>
                  <div>农夫山泉天然水550ml</div>
                  <div>
                    <span style="width: 60px;display: inline-block;">2.00</span>
                    <span style="width: 70px;display: inline-block;padding-left: 16px;">2.00</span>
                    <span style="width: 52px;display: inline-block;padding-left: 22px;">1</span>
                    <span style="display: inline-block;">2.00</span>
                  </div>
                  <div class="pc_setRight2"></div>
                  <div>件数：1</div>
                  <div>合计：2.00</div>
                  <div>其他优惠：1.00</div>
                  <div v-if="memberInfoArrs[0].flag || memberInfoArrs[1].flag
                    ||memberInfoArrs[2].flag || memberInfoArrs[3].flag">（含 积分抵扣：1.00）</div>
                  <div>应付：1.00</div>
                  <div>会员支付：1.00</div>
                  <div>找零：0.00</div>
                  <div
                  v-if="memberInfoArrs[0].flag || memberInfoArrs[1].flag
                    ||memberInfoArrs[2].flag || memberInfoArrs[3].flag">&nbsp;</div>
                  <div v-if="memberInfoArrs[0].flag">会员名：李大宝</div>
                  <div v-if="memberInfoArrs[1].flag">会员手机号：123****4567</div>
                  <div v-if="memberInfoArrs[2].flag">余额: 120.20</div>
                  <div v-if="memberInfoArrs[3].flag">积分：20 </div>
                  <div class="pc_setRight2"></div>
                  <div style="white-space: pre-wrap;">{{remark_content}}</div>
                  <div>
                    打印时间：{{printDate}}
                  </div>
                  <div v-if="logAndCode[1].flag && logAndCode[1].url !== ''" class="pc_setRight3">
                    <img class="pc_setRight3_img" :src="logAndCode[1].url + uploadImg" alt="">
                  </div>
                </div>
                <div class="pc_setRight_tip">
                  请到官方天猫商城采购已测试的专用打印纸，非官方打印纸可能导致打印格式错误。
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="条码打印"
              name="tab1"
            >
              <div class="pc_set22" style="width: 55%;display: inline-block;">
                <div class="pc_setParts2">
                  <div class="pc_setParts0" id="setParts0">基础设置</div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">选择打印机</div>
                  <div class="pc_setParts1" style="width: 68%;max-width: 348px;">
                    <el-select
                      v-model="labelPrinterValue"
                      placeholder=""
                    >
                      <el-option
                        v-for="pr in printerList"
                        :key="pr.label"
                        :label="pr.label"
                        :value="pr.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">打印份数</div>
                  <div class="pc_setParts1" style="width: 68%;line-height: 40px;max-width: 348px;">
                    <div
                      @click="setLabelDefault = false"
                      class="setting_radio"
                      :class="!setLabelDefault ? 'borderColor' : 'borderColor1'"
                    >
                      <div v-show="!setLabelDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setLabelDefault = false" class="pc_setRadio" style="cursor: pointer;">每次打印前询问</div>
                  </div>
                </div>
                <div class="pc_setParts2" style="height: 40px;display: flex;">
                  <div class="pc_setParts0"></div>
                  <div class="pc_setParts1" style="width: 68%;line-height: 40px;display: flex;max-width: 348px;">
                    <div style="display: inline-block;width: 168px">
                      <div
                        @click="setLabelDefault = true"
                        class="setting_radio"
                        :class="setLabelDefault ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="setLabelDefault" class="setting_radio_inner"></div>
                      </div>
                      <div @click="setLabelDefault = true" class="pc_setRadio" style="cursor: pointer;">默认打印份数</div>
                    </div>
                    <div style="display: inline-block;flex: 1;padding-left: 20px;">
                      <el-input
                        :disabled="!setLabelDefault"
                        @blur="setNum('labelPrinterCopies')"
                        v-model="labelPrinterCopies"
                        v-input-int-max-min="{max: 999,min: 0}"
                        placeholder="最大输入999"
                      ></el-input>
                    </div>
                  </div>
                </div>
                <div class="pc_setParts2" style="height: 90px;">
                  <div class="pc_setParts0">条码纸规格&nbsp;(宽*高)<div class="com_pae16" @click="showImg = true">?</div></div>
                  <div class="pc_setParts1" style="margin-top: 10px;width: 68%;max-width: 348px;text-align: left;">
                    <div>
                      <div
                        @click="print_cols_label = '64'"
                        class="setting_radio"
                        :class="print_cols_label === '64' ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="print_cols_label === '64'" class="setting_radio_inner"></div>
                      </div>
                      <div @click="print_cols_label = '64'" class="pc_setRadio" style="cursor: pointer;">60*40mm</div>
                    </div>
                    <div style="margin-top: 15px;">
                      <div
                        @click="print_cols_label = '60'"
                        class="setting_radio"
                        :class="print_cols_label === '60' ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="print_cols_label === '60'" class="setting_radio_inner"></div>
                      </div>
                      <div @click="print_cols_label = '60'" class="pc_setRadio" style="cursor: pointer;">40*60mm</div>
                    </div>
                    <div style="margin-top: 15px;">
                      <div
                        @click="print_cols_label = '40'"
                        class="setting_radio"
                        :class="print_cols_label === '40' ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="print_cols_label === '40'" class="setting_radio_inner"></div>
                      </div>
                      <div @click="print_cols_label = '40'" class="pc_setRadio" style="cursor: pointer;">40*30mm</div>
                    </div>
                  </div>
                </div>
                <div
                  class="pc_setParts2"
                  style="margin-bottom: 20px;"
                  :key="item.key + 'up'"
                  v-show="index > 2"
                  v-for="(item, index) in labelItem"
                  >
                  <div class="pc_setParts0">{{item.title}}
                    <el-popover
                      popper-class="pc_fip5"
                      placement="right"
                      width="320"
                      trigger="click"
                      :content="'在商品列表打印时输入' + item.title + '；此打印不支持40*30mm条码纸规格。'">
                      <div class="com_pae16" slot="reference">?</div>
                    </el-popover>
                  </div>
                  <div class="pc_setParts1">
                    <el-switch
                      :disabled="print_cols_label === '40' ? true : false"
                      v-model="item.flag"
                     :active-color="$t('image.homeImage.color')"
                      inactive-color="#DCDFE6"
                    ></el-switch>
                  </div>
                </div>
                <div v-if="ultimate">
                  <div class="pc_setParts2">
                    <div class="pc_setParts0" id="setParts0">个性化设置
                      <div class="pc_setParts5" style="font-weight: 400;font-size: 16px;">（旗舰版专用）</div>
                    </div>
                  </div>
                  <div
                    class="pc_setParts2"
                    style="margin-bottom: 20px;"
                    :key="item.key + 'down'"
                    v-show="index < 3"
                    v-for="(item, index) in labelItem"
                    >
                    <div class="pc_setParts0">{{item.title}}</div>
                    <div class="pc_setParts1">
                      <el-switch
                        :disabled="ultimate ? (print_cols_label !== '40' && print_cols_label !== '60' && print_cols_label !== '64' ? true : false) : true"
                        v-model="item.flag"
                      :active-color="$t('image.homeImage.color')"
                        inactive-color="#DCDFE6"
                      ></el-switch>
                    </div>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts7" @click="printTest('label')">
                    打印测试
                  </div>
                </div>
              </div>
              <div class="pc_tab1_setRight">
                <div class="pc_tab1_setRight_rel" :style="getPreviewStyle()">
                  <div style="height: 20px;">
                    {{labelItem[0].flag ? '农夫山泉天然水550ml' : ''}}
                  </div>
                  <div style="height: 20px;font-size: 12px;">
                    {{labelItem[1].flag ? '售价： ￥2.00' : ''}}
                  </div>
                  <div class="pc_tab1_setRight0">
                    <img v-show="labelItem[2].flag" class="pc_tab1_setRight0_img" src="../../image/egCode.png" alt="">
                  </div>
                  <div style="height: 20px;margin:0px auto;font-size: 12px;width: 110px;text-align: center;">
                    {{labelItem[2].flag ? '&nbsp;6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;921168&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;509256' : ''}}
                  </div>
                  <div v-show="print_cols_label !== '40'" style="height: 20px;font-size: 12px;">
                    <div style="display: inline-block;width: 120px;margin-right: 16px;">
                      {{labelItem[3].flag ? '生产日期:2021-04-02' : ' '}}
                    </div>
                    <div v-if="labelItem[4].flag" style="display: inline-block;">
                      保质期: 10天
                    </div>
                  </div>
                </div>
                <div class="pc_setRight_tip" style="width: 240px;">
                  请到官方天猫商城采购已测试的专用打印纸，非官方打印纸可能导致打印格式错误。
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="标价签打印"
              name="tab2"
            >
              <div class="pc_set22" style="width: 55%;display: inline-block;">
                <div class="pc_setParts2">
                  <div class="pc_setParts0" id="setParts0" >基础设置</div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">选择打印机</div>
                  <div class="pc_setParts1" style="width: 68%;max-width: 348px;">
                    <el-select
                      v-model="tipPrinterValue"
                      placeholder=""
                    >
                      <el-option
                        v-for="pr in printerList"
                        :key="pr.label"
                        :label="pr.label"
                        :value="pr.value"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts0">打印份数</div>
                  <div class="pc_setParts1" style="width: 68%;line-height: 40px;max-width: 348px;">
                    <div
                      @click="setTipDefault = false"
                      class="setting_radio"
                      :class="!setTipDefault ? 'borderColor' : 'borderColor1'"
                    >
                      <div v-show="!setTipDefault" class="setting_radio_inner"></div>
                    </div>
                    <div @click="setTipDefault = false" class="pc_setRadio" style="cursor: pointer;">每次打印前询问</div>
                  </div>
                </div>
                <div class="pc_setParts2" style="height: 40px;display: flex;">
                  <div class="pc_setParts0"></div>
                  <div class="pc_setParts1" style="width: 68%;line-height: 40px;display: flex;max-width: 348px;">
                    <div style="display: inline-block;width: 168px">
                      <div
                        @click="setTipDefault = true"
                        class="setting_radio"
                        :class="setTipDefault ? 'borderColor' : 'borderColor1'"
                      >
                        <div v-show="setTipDefault" class="setting_radio_inner"></div>
                      </div>
                      <div @click="setTipDefault = true" class="pc_setRadio" style="cursor: pointer;">默认打印份数</div>
                    </div>
                    <div style="display: inline-block;flex: 1;padding-left: 20px;">
                      <el-input
                        :disabled="!setTipDefault"
                        @blur="setNum('tipPrinterCopies')"
                        v-model="tipPrinterCopies"
                        @input="tipPrinterCopies = $intMaxMinLimit({data: tipPrinterCopies, max: 999, min: 0})"
                        placeholder="最大输入999"
                      ></el-input>
                    </div>
                  </div>
                </div>
                <div v-if="ultimate">
                  <div class="pc_setParts2">
                    <div class="pc_setParts0" id="setParts0">个性化设置
                      <div class="pc_setParts5" style="font-weight: 400;font-size: 16px;">（旗舰版专用）</div>
                    </div>
                  </div>
                  <div class="pc_setParts2" style="margin-bottom: 20px;">
                    <div class="pc_setParts0">打印会员价</div>
                    <div class="pc_setParts1">
                      <el-switch
                        :disabled="ultimate ? false : true"
                        v-model="printVipPrice"
                      :active-color="$t('image.homeImage.color')"
                        inactive-color="#DCDFE6"
                      ></el-switch>
                    </div>
                  </div>
                  <div class="pc_setParts2">
                    <div class="pc_setParts0">售价字号</div>
                    <div class="pc_setParts1">
                      <el-select
                        v-model="sale_price_fontsize"
                      >
                        <el-option
                          v-for="item in sale_price_fontsizes"
                          :disabled="!ultimate"
                          :key="item.fontsize"
                          :label="item.label"
                          :value="item.fontsize"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="pc_setParts2" v-show="printVipPrice">
                    <div class="pc_setParts0">会员价字号</div>
                    <div class="pc_setParts1">
                      <el-select
                        v-model="vip_price_fontsize"
                      >
                        <el-option
                          v-for="item in sale_price_fontsizes"
                          :disabled="!ultimate || !printVipPrice"
                          :key="item.fontsize"
                          :label="item.label"
                          :value="item.fontsize"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
                <div class="pc_setParts2">
                  <div class="pc_setParts7" @click="printTest('tip')">
                    打印测试
                  </div>
                </div>
              </div>
              <div class="pc_tab2_setRight" :style="screenWidth < 1280 ? '' : 'position: absolute;margin-left: 40px;margin-top: 36px;'">
                <div class="pc_tab2_setRightRel">
                  <div class="pc_tab2_setRight0">
                    {{storeinfo.name ? storeinfo.name : ''}}
                  </div>
                  <div class="pc_tab2_setRight1">
                    商品标价签
                  </div>
                  <div class="pc_tab2_setRight2">
                    <span class="pc_tab2_setRight2_span">品名</span>农夫山泉天然水550ml
                  </div>
                  <div class="pc_tab2_setRight3">
                    <div class="pc_tab2_setRight6" style="padding-top: 10px;">
                      <div class="pc_tab2_setRight4" style="width: 110px">产地</div>
                      <div class="pc_tab2_setRight4">单位<span class="pc_tab2_setRight5">瓶</span></div>
                    </div>
                    <div class="pc_tab2_setRight6">
                      <div class="pc_tab2_setRight4" style="width: 110px">规格</div>
                      <div class="pc_tab2_setRight4">等级</div>
                    </div>
                    <div class="pc_tab2_setRight6">
                      <div class="pc_tab2_setRight4">条码</div>
                      <div class="pc_tab2_setRight7">
                        <img class="pc_tab2_setRight7_img" src="../../image/egCode.png" alt="">
                        &nbsp;6&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;921168&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;509256
                      </div>
                    </div>
                    <div class="pc_tab2_setRight9">
                      市物价局监制&nbsp;&nbsp;监督电话：12315
                    </div>
                  </div>
                  <div class="pc_tab2_setRight8">
                    <div style="height: 102px;">
                      <div
                        v-show="printVipPrice"
                        style="max-height: 70px;overflow-y: hidden;">售价：
                        <span :style="{fontSize: +sale_price_fontsize + 4 + 'px'}">2.00</span>
                      </div>
                      <div v-show="printVipPrice">
                        <div>会员价：</div>
                        <div :style="{ fontSize: +vip_price_fontsize + 6 + 'px', paddingLeft: '40px' }">2.00</div>
                      </div>
                      <div v-show="!printVipPrice">
                        <div>售价：</div>
                        <div
                          style="padding-left: 30px;"
                          :style="{fontSize: +sale_price_fontsize + 4 + 'px'}">
                          2.00
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="pc_setRight_tip">
                  请到官方天猫商城采购已测试的专用打印纸，非官方打印纸可能导致打印格式错误。
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              label="读卡设备"
              name="tab3"
            >
            <div class="pc_set22" style="width: 61%;display: inline-block;">
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div class="pc_setParts0" style="font-weight: bold;font-size: 18px;">启用读卡设备</div>
                <div class="pc_setParts1">
                  <el-switch
                    v-model="setEquip"
                   :active-color="$t('image.homeImage.color')"
                    inactive-color="#DCDFE6"
                  ></el-switch>
                </div>
              </div>
            </div>
            </el-tab-pane>
            <el-tab-pane
              label="副屏广告"
              name="tab4"
              v-loading="waitReloadAd"
            >
            <div class="pc_set22" style="width: 70%;display: inline-block;">
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div class="pc_setParts0" style="font-weight: bold;font-size: 18px;">启用副屏广告</div>
                <div class="pc_setParts1">
                  <el-switch
                    v-model="set_show_ad"
                   :active-color="$t('image.homeImage.color')"
                    inactive-color="#DCDFE6"
                    @change="setShowAdChange"
                  ></el-switch>
                </div>
                <span style="margin-left: 10px" v-show="set_show_ad" class="explain_font">
                  （电脑端、手机端编辑完成后请点击 <div class="reload_ad_btn" @click="changeWaitReloadAd(true);openAd(true);adSearch()">刷新广告</div> ）
                </span>
                <div v-show="set_show_ad" style="width: 100%;">
                  <div class="pc_adv_duration">
                    <div class="setting_print_font">图片切换时间(秒)：</div>
                    <el-input
                      v-model.number="adDuration"
                      style="width: 80px;"
                      @input="adDuration = $intMaxMinLimit({data: adDuration, max: 999, min: 0})"
                      @change="durationChange()"
                      >
                      <!-- <template slot="append">
                        秒
                      </template> -->
                    </el-input>
                  </div>
                  <div class="pc_set_time_tips">
                    图片建议尺寸1920*768（仅支持格式为JPG或PNG，图片大小不超过3M）
                  </div>
                  <div v-if="adsList.length < 10" class="image_upload">
                    <div @click="adEdit()" class="add-img">
                      <i class="el-icon-plus"></i>
                      <div class="el-upload__text">上传广告<br />（{{ adsList.length }}/10）</div>
                    </div>
                  </div>
                  <div
                    class="image_pre"
                    v-for="item in adsList"
                    :key="item.id">
                    <div @click="adEdit(item)">
                      <el-image
                        style="width: 180px; height: 100px"
                        :src="item.img"
                        :key="item.id"/>
                      <div v-if="item.templateId === 1" class="temp-1-mask"></div>
                      <div v-if="item.templateId === 1" class="temp-1-add-text maxline-1">{{item.comments}}</div>
                      <div v-if="item.templateId === 2" class="temp-2-mask"></div>
                      <div v-if="item.templateId === 2" class="temp-2-add-text maxline-1">{{item.comments}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            </el-tab-pane>
            <el-tab-pane
              label="钱箱"
              name="tab5"
            >
            <div class="pc_set22" style="width: 61%;display: inline-block;">
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div class="pc_setParts0" style="font-weight: bold;font-size: 18px;">启用钱箱</div>
                <div class="pc_setParts1">
                  <el-switch
                    v-model="has_moneybox"
                   :active-color="$t('image.homeImage.color')"
                    inactive-color="#DCDFE6"
                  ></el-switch>
                </div>
              </div>
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div>
                  <div
                    v-show="has_moneybox"
                    style="overflow: hidden;"
                  >
                    <div
                      class="pc_set35"
                      :class="moneybox_type == 1 ? 'pc_set36' : ''"
                      @click="moneybox_type = 1"
                    >
                      小票打印机集成
                      <img
                        alt=""
                        v-show="moneybox_type == 1"
                        src="../../image/zgzn-pos/pc_pay_duigou.png"
                        style="position: absolute;right: 0;bottom: 0;"
                      />
                    </div>
                    <!-- <div
                      class="pc_set35"
                      :class="moneybox_type == 2 ? 'pc_set36' : ''"
                      @click="moneybox_type = 2"
                    >
                      选择端口
                      <img
                        alt=""
                        v-show="moneybox_type == 2"
                        src="../../image/zgzn-pos/pc_pay_duigou.png"
                        style="position: absolute;right: 0;bottom: 0;"
                      />
                    </div> -->
                    <div
                      @click="moneyboxCheck"
                      id="moneyboxCheck"
                      style="float: left;width: 80px;height: 42px;line-height: 40px;
                          color: #FFF;text-align: center;border-radius: 5px;font-size: 16px;cursor: pointer;"
                    >检测</div>
                    <br />
                  </div>
                  <div
                    v-show="has_moneybox && moneybox_type == 2 ? true : false"
                    style="margin-top: 15px;width: 182px;"
                  >
                    <el-select
                      v-model="moneybox_value"
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="mb in moneybox"
                        :key="mb.label"
                        :label="mb.label"
                        :value="mb.label"
                      ></el-option>
                    </el-select>
                  </div>
                </div>
              </div>
            </div>
            </el-tab-pane>
            <el-tab-pane
              label="电子秤 "
              name="tab6"
            >
            <div class="pc_set22" style="width: 61%;display: inline-block;">
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div class="pc_setParts0" style="margin-bottom: 20px;font-weight: bold;font-size: 18px;">启用电子秤</div>
                <div class="pc_setParts1">
                  <el-switch
                    v-model="hasWeighOff"
                   :active-color="$t('image.homeImage.color')"
                    inactive-color="#DCDFE6"
                  ></el-switch>
                </div>
              </div>
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div>
                  <div
                    v-show="hasWeighOff"
                    style="overflow: hidden;"
                  >
                    <div class="pc_setParts2">
                      <div class="pc_setParts0">收银台显示重量</div>
                      <div class="pc_setParts1" style="width: 68%;max-width: 260px;text-align: right;">
                        <el-switch v-model="weighShowInPay" :active-color="$t('image.homeImage.color')" inactive-color="#DCDFE6"></el-switch>
                      </div>
                    </div>
                    <div class="pc_setParts2">
                      <div class="pc_setParts0">电子秤状态</div>
                      <div class="pc_setParts1" style="width: 68%;max-width: 260px;text-align: right;">
                        <span style="margin-right: 20px;" :style="weighStatus === '·未连接' ? 'color: #FF6159;' : 'color: #BDA169;' ">{{weighStatus}}</span>
                      </div>
                    </div>
                    <div class="pc_setParts2">
                      <div class="pc_setParts0">电子秤类型</div>
                      <div class="pc_setParts1" style="width: 68%;max-width: 260px;">
                        <el-select
                          v-model="weighTypeValue"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="pr in weighTypeList"
                            :key="pr.value"
                            :label="pr.label"
                            :value="pr.value"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="pc_setParts2">
                      <div class="pc_setParts0">端口号</div>
                      <div class="pc_setParts1" style="width: 68%;max-width: 260px;">
                        <el-select
                          v-model="weighValue"
                          placeholder="请选择"
                        >
                          <el-option
                            v-for="mb in weighList"
                            :key="mb"
                            :label="mb"
                            :value="mb"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                    <div class="pc_setParts2">
                      <div class="pc_setParts0">当前重量</div>
                      <div class="pc_setParts1" style="width: 68%;max-width: 260px;text-align: right;">
                        <div @click="focusInputWeight(1)" id="updPassword" class="pc_weigh" style="float: left;color: #FFF;">
                          <svg style="margin-bottom: 3px;color: #FFF;" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.98736 7.69971C4.39758 7.69325 4.74054 7.99672 4.793
                              8.39382L4.79985 8.48702L4.80172 8.61191C4.85628 10.3425 6.1637 11.6996 7.73285 11.6996C7.9558
                              11.6996 8.1749 11.6726 8.38765 11.6196L8.23427 11.4653C7.92185 11.1529 7.92185 10.6463 8.23427
                              10.3339C8.54669 10.0215 9.05322 10.0215 9.36564 10.3339L10.9656 11.9339C11.2781 12.2463 11.2781
                              12.7529 10.9656 13.0653L9.36564 14.6653C9.05322 14.9777 8.54669 14.9777 8.23427 14.6653C7.92185
                              14.3529 7.92185 13.8463 8.23427 13.5339L8.54577 13.2225C8.27943 13.2736 8.00762 13.2996 7.73285
                              13.2996C5.33291 13.2996 3.37863 11.3303 3.21126 8.83334L3.20222 8.64971L3.20005 8.5122C3.1931 8.07043
                              3.54559 7.70666 3.98736 7.69971ZM7.76564 2.33392C8.05403 2.62231 8.07621 3.0761 7.83219 3.38993L7.76564
                              3.46529L7.45402 3.77661C7.72027 3.72556 7.99197 3.69961 8.26664 3.69961C10.7831 3.69961 12.8 5.86051 12.8
                              8.49961C12.8 8.94144 12.4418 9.29961 12 9.29961C11.5581 9.29961 11.2 8.94144 11.2 8.49961C11.2 6.72043 9.87386
                              5.29961 8.26664 5.29961C8.04377 5.29961 7.82474 5.3266 7.61206 5.37951L7.76564 5.53392C8.07806 5.84634 8.07806
                              6.35288 7.76564 6.66529C7.47725 6.95368 7.02347 6.97587 6.70963 6.73185L6.63427 6.66529L5.03427 5.06529C4.74588
                              4.77691 4.7237 4.32312 4.96772 4.00929L5.03427 3.93392L6.63427 2.33392C6.94669 2.0215 7.45322 2.0215 7.76564 2.33392Z" fill="#FFF"/>
                          </svg>检测连接</div>
                        <span style="font-family: UnidreamLED, sans-serif;font-size: 50px;">{{weighSet}}</span>
                        <div style="font-size: 16px;display: inline-block;">千克</div>
                      </div>
                    </div>
                    <br />
                  </div>
                </div>
              </div>
            </div>
            </el-tab-pane>
            <el-tab-pane
              label="条码秤"
              name="tab8"
            >
            <div class="pc_set22" style="width: 61%;display: inline-block;">
              <div class="pc_setParts2" style="margin-bottom: 0px;">
                <div class="pc_setParts0" style="font-weight: bold;font-size: 18px;">启用条码秤</div>
                <div class="pc_setParts1" @click="initScalesTable()">
                  <el-switch
                    v-model="hasBarCodeScales"
                   :active-color="$t('image.homeImage.color')"
                    inactive-color="#DCDFE6"
                  ></el-switch>
                </div>
              </div>
            </div>
            </el-tab-pane>
            <el-tab-pane
              label="客显"
              name="tab7"
            >
              <div class="pc_set22" style="width: 61%;display: inline-block;">
                <div class="pc_setParts2" style="margin-bottom: 0px;">
                  <div class="pc_setParts0" style="margin-bottom: 20px;font-weight: bold;font-size: 18px;">启用客显</div>
                  <div class="pc_setParts1">
                    <el-switch
                      v-model="showKexian"
                     :active-color="$t('image.homeImage.color')"
                      inactive-color="#DCDFE6"
                    ></el-switch>
                  </div>
                </div>
                <div class="pc_setParts2" style="margin-bottom: 0px;">
                  <div>
                    <div
                      v-show="showKexian"
                      style="overflow: hidden;"
                    >
                      <div class="pc_setParts2">
                        <div class="pc_setParts0">客显地址</div>
                        <div class="pc_setParts1" style="width: 68%;max-width: 260px;">
                          <el-select
                            v-model="kexianValue"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="mb in kexianList"
                              :key="mb"
                              :label="mb"
                              :value="mb"
                            ></el-option>
                          </el-select>
                        </div>
                      </div>
                      <div class="pc_setParts2">
                        <div class="pc_setParts0">波特率</div>
                        <div class="pc_setParts1" c style="width: 68%;max-width: 260px;text-align: right;">
                          2400
                        </div>
                      </div>
                      <div class="pc_setParts2">
                        <div class="pc_setParts0">单品价格显示</div>
                        <div class="pc_setParts1" c style="width: 68%;max-width: 260px;text-align: right;">
                          <el-radio v-model="kexianPriceType" label="price" @input="kexianPriceTypeChange">单价</el-radio>
                          <el-radio v-model="kexianPriceType" label="amt" @input="kexianPriceTypeChange">小计</el-radio>
                        </div>
                      </div>
                      <div v-show="showKexian" class="pc_setParts2">
                        <div class="pc_setParts0">点击以下按钮检测客显</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-show="showKexian" class="pc_setParts2" style="display: inline-block;">
                <div @click="showKexianNumber(9.88, kexianPriceType === 'amt' ? 0 : 1)" id="updPassword"
                  style="width: 160px;height: 95px;cursor: pointer;display: inline-block;
                    color: #FFFFFF;font-size: 20px;border-radius: 8px;margin-right: 16px;">
                  <div style="position: relative;top: 4px;">
                    <div style="font-size: 16px;padding-left: 19px;">{{kexianPriceType === 'amt' ? '小计' : '单价'}}</div>
                    <div style="font-family: UnidreamLED, sans-serif;font-size: 50px;top: 20px;text-align: center;">
                      9.88
                    </div>
                  </div>
                </div>
                <div @click="showKexianNumber(56.56, 2)" id="updPassword" style="width: 160px;height: 95px;cursor: pointer;display: inline-block;
                  color: #FFFFFF;font-size: 20px;border-radius: 8px;margin-right: 16px;">
                  <div style="position: relative;top: 4px;">
                    <div style="font-size: 16px;padding-left: 19px;">总计</div>
                    <div style="font-family: UnidreamLED, sans-serif;font-size: 50px;top: 20px;text-align: center;">
                      56.56
                    </div>
                  </div>
                </div>
                <div @click="showKexianNumber(6.66, 4)" id="updPassword" style="width: 160px;height: 95px;cursor: pointer;display: inline-block;
                  color: #FFFFFF;font-size: 20px;border-radius: 8px;margin-right: 16px;">
                  <div style="position: relative;top: 4px;">
                    <div style="font-size: 16px;padding-left: 19px;">找零</div>
                    <div style="font-family: UnidreamLED, sans-serif;font-size: 50px;top: 20px;text-align: center;">
                      6.66
                    </div>
                  </div>
                </div>
                <div @click="showKexianNumber('', 0)" id="updPassword" style="width: 90px;height: 95px;cursor: pointer;display: inline-block;
                  color: #FFFFFF;font-size: 20px;text-align: center;border-radius: 8px;margin-right: 16px;top: -11px;position: relative;">
                  <div style="position: relative;top: 12px;">
                    <div>
                      <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0)">
                        <path d="M20 0.5H0V20.5H20V0.5Z" fill="white" fill-opacity="0.01"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.3335 2.96387H11.6668V6.29722H17.9168V9.63055H2.0835V6.29722H8.3335V2.96387Z"
                          stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3.3335 17.167H16.6668V9.66699H3.3335V17.167Z" stroke="white" stroke-width="1.33333" stroke-linejoin="round"/>
                        <path d="M6.6665 17.124V14.6309" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M10 17.124V14.624" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.3335 17.124V14.6309" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5 17.167H15" stroke="white" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                        </g>
                        <defs>
                        <clipPath id="clip0">
                        <rect width="20" height="20" fill="white" transform="translate(0 0.5)"/>
                        </clipPath>
                        </defs>
                      </svg>
                    </div>
                    <div style="margin-top: 5px;font-size: 28px;font-weight: bold;">
                      清除
                    </div>
                  </div>
                </div>
              </div>
              <div v-show="showKexian" :style="screenWidth < 1280 ? '' : 'display: inline-block;'">
                <img alt="" :style="screenWidth < 1280 ? 'margin-top: -40px;margin-left: -35px;' : 'position: absolute;top: 215px;'"
                  style="height: 280px;width: 230px;" src="../../image/pc_kexian.png" />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <!--微店设置离线-->
        <div v-if="selectRow === 12 && isOffline" class="pc_set24 offline-container">
          <div style="margin: -80px 0 0 -80px">
            <svg style="margin-left: 100px" width="144" height="126" viewBox="0 0 144 126" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M72.0008 106.002C76.5228 106.002 80.6775 107.585 83.9455 110.22L72.0008 125.002L60.0562
                110.22C63.4347 107.483 67.6529 105.993 72.0008 106.002V106.002ZM13.7848 0.824219L125.739 112.785L116.784
                121.74L83.9772 88.9336C80.1144 87.6518 76.0707 86.9996 72.0008 87.0019C62.9568 87.0019 54.6475 90.1622
                48.1242 95.4379L40.1695 85.5832C48.4918 78.8412 58.7445 74.9238 69.4422 74.3986L53.0008 57.9572C43.9579
                60.5205 35.5291 64.8921 28.2248 70.8075L20.2575 60.9529C27.0836 55.4253 34.7512 51.027 42.9688 47.9252L28.5035
                33.4536C21.3031 36.8825 14.5324 41.149 8.33183 46.1645L0.364502 36.3162C6.1595 31.6296 12.4168 27.5065 19.0605
                24.0169L4.82317 9.78589L13.7848 0.824219V0.824219ZM87.8342 57.1402L73.3688 42.6812L72.0008 42.6685C91.6025 42.6685
                109.602 49.5149 123.744 60.9529L115.777 70.8075C107.616 64.1966 98.0624 59.5235 87.8342 57.1402V57.1402ZM72.0008
                11.0019C99.1392 11.0019 124.061 20.4829 143.631 36.3162L135.67 46.1645C117.66 31.5767 95.1775 23.633 72.0008
                23.6686C66.4402 23.6686 60.9808 24.1119 55.6608 24.9795L44.9258 14.2319C53.5962 12.1229 62.6718 11.0019
                72.0008 11.0019Z"
                fill="#ADC1CF"
              />
            </svg>
            <div class="offline-text">当前未联网，无法获取微店状态，请联网后刷新。</div>
            <div class="offline-refresh" @click="microShop">刷新</div>
          </div>
        </div>
        <!--微店设置-->
        <div v-if="selectRow === 12 && !isOffline" class="pc_set24">
          <!-- <div>
            微店状态：{{ microShopStatus }}
            <div class="next-btn" @click="microShopStatus++">下一步</div>
            <div class="next-btn" @click="microShopStatus = 0">重置</div>
            <div class="next-btn" @click="testSetOpenId">openid</div>
          </div>
          <div>
            个人店铺状态状态：{{ personalShop.certStatus }}
            <div
              class="next-btn"
              @click="
                personalShop.certStatus = 0;
                showScanningCodeImg = false;
              "
            >
              认证
            </div>
            <div class="next-btn" @click="personalShop.certStatus = 4">审核</div>
            <div
              class="next-btn"
              @click="
                personalShop.certStatus = 1;
                microShopStatus = 3;
                showScanningCodeImg = false;
              "
            >
              签约
            </div>
            <div class="next-btn" @click="personalShop.certStatus = 2">拒绝认证</div>
            <div
              class="next-btn"
              @click="
                personalShop.certStatus = 3;
                showScanningCodeImg = false;
              "
            >
              重置
            </div>
            <div class="next-btn" @click="testSetSyncStatus">手动同步</div>
          </div>
          <div>
            企业店铺状态状态：{{ enterpriseShop.certStatus }}
            <div
              class="next-btn"
              @click="
                enterpriseShop.certStatus = 0;
                showScanningCodeImg = false;
              "
            >
              认证
            </div>
            <div class="next-btn" @click="enterpriseShop.certStatus = 4">审核</div>
            <div
              class="next-btn"
              @click="
                enterpriseShop.certStatus = 1;
                microShopStatus = 3;
                showScanningCodeImg = false;
              "
            >
              签约
            </div>
            <div class="next-btn" @click="enterpriseShop.certStatus = 2">拒绝认证</div>
            <div
              class="next-btn"
              @click="
                enterpriseShop.certStatus = 3;
                showScanningCodeImg = false;
              "
            >
              重置
            </div>
            <div class="next-btn" @click="testSetSyncStatus">手动同步</div>
          </div> -->
          <div class="prod_sync_container">
            <p>商品同步</p>
            <span>通过绑定群客多微店，将掌柜智囊店铺商品信息同步到微店中，实现轻松开微店。</span>
          </div>
          <el-collapse-transition>
            <div class="step_container" v-if="microShopStatus < 3">
              <span style="color: #537286">绑定微店前，您需要完成以下操作：</span>
              <el-steps space="45%" :active="microShopStatus" finish-status="success" style="margin-top: 12px">
                <el-step title="注册账号"></el-step>
                <el-step title="激活微店"></el-step>
                <el-step title="个人/企业认证"></el-step>
              </el-steps>
            </div>
          </el-collapse-transition>
          <div class="micro_store">
            <div class="micro_store--header">
              <div v-if="microShopStatus < 2" class="beforeAuth">
                <span class="phone">{{ sysUid.toString().substr(0, 3) }}****{{ sysUid.toString().substr(7, 4) }}</span>
                <div class="tag-default">{{ microShopStatus === 0 ? '未注册' : '未激活' }}</div>
              </div>
              <div v-else class="auth">
                <div class="half-header" :class="curMicroShop === 'ps' ? 'active' : ''"
                  @click="curMicroShopChange('ps') ">
                  <div class="header-img ps">个人</div>
                  <div>
                    <div class="phone">{{ personalShop.shortName }}</div>
                    <div style="margin-top: 0"
                      :class="personalShop.certStatus === 1 && personalShop.isBound === 1 ? 'tag-success' : 'tag-default'">
                      {{ personalShop.certStatus | microStoreStatus(personalShop.isBound) }}
                    </div>
                  </div>
                </div>
                <div class="half-header" :class="curMicroShop === 'es' ? 'active' : ''"
                  @click="curMicroShopChange('es')">
                  <div class="header-img es">企业</div>
                  <div>
                    <div class="phone">{{ enterpriseShop.shortName }}</div>
                    <div style="margin-top: 0"
                      :class="enterpriseShop.certStatus === 1 && enterpriseShop.isBound === 1 ? 'tag-success' : 'tag-default'">
                      {{ enterpriseShop.certStatus | microStoreStatus(enterpriseShop.isBound) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="micro_store--body">
              <!-- 微店未注册或未激活 -->
              <template v-if="microShopStatus < 2">
                <div class="beforeAuth">
                  <div class="warningText">
                    {{ microShopStatus === 0 ? '未检测到该账号，请前往群客多注册' : '微店账号未激活，请点击去激活按钮完成激活' }}
                  </div>
                </div>
                <div class="flex-div">
                  <div v-if="!showScanningCodeImg" class="submit" @click="microShopStatus === 0 ? microShopRegister() : microShopActive()">
                    {{ microShopStatus === 0 ? '去注册' : '去激活' }}
                  </div>
                  <div v-if="showScanningCodeImg" style="width: 200px">
                    <el-image style="width: 184px; height: 184px" :src="scanningCodeImg">
                      <div slot="placeholder" class="pc_scan_loading_div">
                        <div style="margin: 68px auto; text-align: center; font-size: 20px">加载中...</div>
                      </div>
                      <div slot="error" class="pc_load_error_div">
                        <i class="el-icon-picture-outline" style="font-size: 42px"></i>
                      </div>
                    </el-image>
                    <div style="margin-bottom: 15px; font-weight: 600;font-size: 14px;">
                      <span>请使用微信扫一扫登录</span>
                      <span style="cursor: pointer" :style="refreshClick ? 'opacity: 0.6' : ''" @click="refreshScanCode">
                        &nbsp;&nbsp;刷新
                        <i class="el-icon-refresh-right" style="font-size: 15px; font-weight: 600"></i>
                      </span>
                    </div>
                  </div>
                  <div v-if="microShopStatus === 1" @click="getMicroShopStatus(true)" class="refresh">
                    <i class="el-icon-refresh"></i>
                    刷新状态
                  </div>
                </div>
              </template>
              <!-- 企业或个人微店未认证完成 -->
              <div v-if="microShopStatus > 1 && (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) !== 1" class="auth">
                <!-- 未认证及认证失败  -->
                <template v-if="[2, 3].indexOf(curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) !== -1">
                  <div class="warningText">绑定前，请在群客多完成{{ curMicroShop === 'ps' ? '个人' : '企业' }}认证</div>
                  <div class="infoText">
                    资料准备：
                    <template v-if="curMicroShop === 'ps'">
                      <p>1、个人身份证正反面照片</p>
                      <p>2、结算银行账户</p>
                    </template>
                    <template v-else>
                      <p>1、营业执照</p>
                      <p>2、法人证件（身份证正反面照片）</p>
                      <p>3、结算银行账户（需对公账户）</p>
                    </template>
                  </div>
                  <div class="flex-div">
                    <div v-if="!showScanningCodeImg" class="submit" @click="microShopAuth(curMicroShop)">
                      去认证
                      <!-- {{ (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 3 ? '去认证' : '重新认证' }} -->
                    </div>
                    <div v-else style="width: 200px">
                      <el-image style="width: 184px; height: 184px" :src="authCodeImg">
                        <div slot="placeholder" class="pc_scan_loading_div">
                          <div style="margin: 68px auto; text-align: center; font-size: 20px">加载中...</div>
                        </div>
                        <div slot="error" class="pc_load_error_div">
                          <i class="el-icon-picture-outline" style="font-size: 42px"></i>
                        </div>
                      </el-image>
                      <div style="margin-bottom: 15px;margin-left: 22px;font-weight: 600;font-size: 14px;">
                        <span>请使用微信扫一扫登录</span>
                      </div>
                    </div>
                    <div class="refresh" @click="getAuthStatus">
                      <i class="el-icon-refresh"></i>
                      刷新状态
                    </div>
                  </div>
                </template>
                <!-- 审核中及待签约 -->
                <template v-if="[0, 4].indexOf(curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) !== -1">
                  <div class="warningText">
                    {{
                      (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 0
                        ? '微店认证正在审核中，可手动刷新获取最新状态'
                        : '审核通过，请在群客多完成签约'
                    }}
                  </div>
                  <div class="flex-div">
                    <div
                      v-if="!showScanningCodeImg"
                      class="submit"
                      :style="
                        (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 0 ? 'background: #CACACA;cursor: no-drop;' : ''
                      "
                      @click="(curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 0 ? '' : microShopSigning(curMicroShop)"
                    >
                      {{ (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 0 ? '审核中...' : '去签约' }}
                    </div>
                    <div v-else style="width: 200px">
                      <el-image style="width: 184px; height: 184px" :src="authCodeImg">
                        <div slot="placeholder" class="pc_scan_loading_div">
                          <div style="margin: 68px auto; text-align: center; font-size: 20px">加载中...</div>
                        </div>
                        <div slot="error" class="pc_load_error_div">
                          <i class="el-icon-picture-outline" style="font-size: 42px"></i>
                        </div>
                      </el-image>
                      <div style="margin-bottom: 15px;margin-left: 22px;font-weight: 600;font-size: 14px;">
                        <span>请使用微信扫一扫登录</span>
                      </div>
                    </div>
                    <div class="refresh" @click="getAuthStatus">
                      <i class="el-icon-refresh"></i>
                      刷新状态
                    </div>
                  </div>
                </template>
              </div>
              <!-- 企业或个人微店已认证 -->
              <div class="auth" style="position: relative;"
                v-if="microShopStatus === 3 && (curMicroShop === 'ps' ? personalShop.certStatus : enterpriseShop.certStatus) === 1">
                <template v-if="curMicroShop === 'ps'">
                  <div v-if="personalShop.isBound !== 1">
                    <p style="margin-bottom: 0;color: #000;">商品同步说明：</p>
                    <p style="margin-bottom: 0;color: #000;">1、商品同步到群客多微店之前，请先“云同步”</p>
                    <p style="margin-bottom: 0;color: #000;">2、“一品多码”商品不同步“扩展条码”信息</p>
                    <p style="margin-bottom: 0;color: #000;">
                      3、无条码商品无法同步到群客多微店，请在商品管理页面通过点击【自动生成条码】为需要同步的无码商品生成条码
                    </p>
                    <p style="margin-bottom: 0;color: #000;">
                      4、相同条码的商品仅会同步一条，请避免出现条码重复商品。
                    </p>
                  </div>
                  <div class="flex-div">
                    <div
                      v-if="personalShop.isBound !== 1"
                      class="submit"
                      @click="msBindClick(true)">绑定</div>
                    <div v-else class="error" @click="msBindClick(false)">解绑</div>
                  </div>
                </template>
                <template v-if="curMicroShop === 'es'">
                  <div v-if="enterpriseShop.isBound !== 1">
                    <p style="margin-bottom: 0;color: #000;">商品同步说明：</p>
                    <p style="margin-bottom: 0;color: #000;">1、商品同步到群客多微店之前，请先“云同步”</p>
                    <p style="margin-bottom: 0;color: #000;">2、“一品多码”商品不同步“扩展条码”信息</p>
                    <p style="margin-bottom: 0;color: #000;">
                      3、无条码商品无法同步到群客多微店，请在商品管理页面通过点击【自动生成条码】为需要同步的无码商品生成条码
                    </p>
                    <p style="margin-bottom: 0;color: #000;">
                      4、相同条码的商品仅会同步一条，请避免出现条码重复商品。
                    </p>
                  </div>
                  <div class="flex-div">
                    <div
                      v-if="enterpriseShop.isBound !== 1"
                      class="submit"
                      @click="msBindClick(true)">
                      绑定
                    </div>
                    <div v-else class="error" @click="msBindClick(false)">解绑</div>
                  </div>
                </template>
                <template
                  v-if="(curMicroShop === 'ps' ? personalShop.isBound : enterpriseShop.isBound) === 1">
                  <div v-if="(curMicroShop === 'ps' && personalShop.isUpdatePrice === 1) ||
                    (curMicroShop === 'es' && enterpriseShop.isUpdatePrice === 1)"
                    style="margin-top: 30px;">
                    勾选后同步时会更新微店已有商品售价，取消勾选则不再更新
                  </div>
                  <div
                    class="update_price_container" >
                    <div
                      v-if="curMicroShop === 'ps'"
                      :class="personalShop.isUpdatePrice === 0 ? 'pc_check' : 'pc_ischeck'"
                      @click="updatePriceCheck(true)"
                    >
                      <img style="width: 20px; margin-top: -7px" src="../../image/pc_msgMark.png" />
                    </div>
                    <div
                      v-if="curMicroShop === 'es'"
                      :class="enterpriseShop.isUpdatePrice === 0 ? 'pc_check' : 'pc_ischeck'"
                      @click="updatePriceCheck(true)"
                    >
                      <img style="width: 20px; margin-top: -7px" src="../../image/pc_msgMark.png" />
                    </div>
                    <span>更新微店已有商品售价</span>
                  </div>
                </template>
                <template v-if="curMicroShop === 'ps' && personalShop.isBound === 1">
                  <div style="margin: 15px 0; color: #b2c3cd" v-if="personalShop.lastUpdateTime">
                    最近同步时间: {{ personalShop.lastUpdateTime }}
                    <el-popover
                      @show="setClass()"
                      popper-class="pc_pay192 pop_padding"
                      placement="bottom-start"
                      width="411"
                      trigger="click"
                    >
                      <p style="margin-bottom: 0;">1、商品同步到群客多微店之前，请先“云同步”</p>
                      <p style="margin-bottom: 0;">2、“一品多码”商品不同步“扩展条码”信息</p>
                      <p style="margin-bottom: 0;">
                        3、无条码商品无法同步到群客多微店，请在商品管理页面通过点击【自动生成条码】为需要同步的无码商品生成条码
                      </p>
                      <p style="margin-bottom: 0;">
                        4、相同条码的商品仅会同步一条，请避免出现条码重复商品。
                      </p>
                      <div style="display: inline-block;" slot="reference">
                        <div class="sync_flex_container">
                          <div class="com_pae20" style="margin: 0;">?</div>
                          <span>商品同步说明</span>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                  <div v-if="personalShop.title" :style="personalShop.title === '同步成功' ? 'color: #47B881;' : 'color: #F64C4C;'">
                    {{ personalShop.title }}
                  </div>
                  <div v-if="personalShop.content" style="width: calc(100% - 340px)">
                    {{ personalShop.content }}
                  </div>
                  <div class="refresh_sync_msg" @click="getSyncStatus()">
                    <i class="el-icon-refresh"></i>
                    刷新状态（激活后状态会有约1分钟延迟）
                  </div>
                </template>
                <template v-if="curMicroShop === 'es' && enterpriseShop.isBound === 1">
                  <div style="margin: 15px 0; color: #b2c3cd" v-if="enterpriseShop.lastUpdateTime">
                    最近同步时间: {{ enterpriseShop.lastUpdateTime }}
                    <el-popover
                      @show="setClass()"
                      popper-class="pc_pay192 pop_padding"
                      placement="bottom-start"
                      width="411"
                      trigger="click"
                    >
                      <p style="margin-bottom: 0;">1、商品同步到群客多微店之前，请先“云同步”</p>
                      <p style="margin-bottom: 0;">2、“一品多码”商品不同步“扩展条码”信息</p>
                      <p style="margin-bottom: 0;">
                        3、无条码商品无法同步到群客多微店，请在商品管理页面通过点击【自动生成条码】为需要同步的无码商品生成条码
                      </p>
                      <p style="margin-bottom: 0;">
                        4、相同条码的商品仅会同步一条，请避免出现条码重复商品。
                      </p>
                      <div style="display: inline-block;" slot="reference">
                        <div class="sync_flex_container">
                          <div class="com_pae20" style="margin: 0;">?</div>
                          <span>商品同步说明</span>
                        </div>
                      </div>
                    </el-popover>
                  </div>
                  <div v-if="enterpriseShop.title" :style="enterpriseShop.title === '同步成功' ? 'color: #47B881;' : 'color: #F64C4C;'">
                    {{ enterpriseShop.title }}
                  </div>
                  <div v-if="enterpriseShop.content">
                    {{ enterpriseShop.content }}
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <!--价格设置部分-->
        <div
          v-show="selectRow === 11"
          class="pc_set24"
        >
          <div style="margin-bottom: 0px;">
            <div>开关机云同步</div>
          </div>
          <div style="margin-top: 16px;">
            <div @click="iCloud = !iCloud" style="cursor: pointer;overflow: hidden;">
              <div style="width: 20px;height: 20px;overflow: hidden;border: 1px solid #E3E6EB;border-radius: 4px;float: left;margin-top: 2px;"
                :class="iCloud ? 'iCloud' : ''">
                <img style="display: block;width: 18px;height: 18px;" v-if="iCloud" src="../../image/pc_msgMark.png"/>
              </div>
              <div style="float: left;margin-left: 12px;">收银台不云同步</div>
            </div>
          </div>
          <div style="margin-top: 40px;">
            <div>每30分钟云同步一次</div>
          </div>
          <div v-for="(se, index) in stopCloudList" :key="index" @click="selectCloud(index)" style="cursor: pointer;overflow: hidden;margin-top: 20px;">
            <div style="width: 20px;height: 20px;overflow: hidden;border: 1px solid #E3E6EB;border-radius: 4px;float: left;margin-top: 10px;"
              :class="se.select ? 'iCloud' : ''">
              <img style="display: block;width: 18px;height: 18px;" v-if="se.select" src="../../image/pc_msgMark.png"/>
            </div>
            <el-select @clear="se.endTime = '';se.select = false;"
              @change="changeCloud(index)"
              style="float: left;margin-left: 12px;" v-model="se.startTime" clearable placeholder="请选择时间">
              <el-option
                v-for="ct in cloudList"
                :key="ct"
                :label="ct"
                :value="ct">
              </el-option>
            </el-select>
            <div style="float: left;margin-top: 7px;margin-left: 8px;">时 ～</div>
            <el-select @clear="se.select = false;" style="float: left;margin-left: 8px;" v-model="se.endTime" clearable placeholder="请选择时间">
              <el-option
                v-for="ct in cloudList"
                :key="ct"
                :disabled="ct <= se.startTime || se.startTime === ''"
                :label="ct"
                :value="ct">
              </el-option>
            </el-select>
            <div style="float: left;margin-top: 7px;margin-left: 8px;">时</div>
            <div style="float: left;margin-top: 7px;margin-left: 12px;">不云同步</div>
          </div>
        </div>
        <!-- 微信助手 -->
        <div v-show="selectRow === 13" class="vx_official_notice">
          <div class="vx_official_notice--title">绑定店铺营业微信通知</div>
          <div class="vx_official_notice--text">微信扫码，关注“掌柜智囊商家助手”公众号并绑定就可以接收店铺营业信息</div>
          <div class="vx_official_notice--img_container">
            <!-- <img :src="officialAccountCodeImgUrl" /> -->
            <el-image
              alt=""
              :src="officialAccountCodeImgUrl"
              style="width: 200px;height: 200px;"
            >
              <div slot="error">
                <div class="pc_no_network_div">
                  <img src="../../image/pc_no_network.png" style="" />
                  <div style="width: 200px;">无网络，请检查网络连接</div>
                </div>
              </div>
            </el-image>
            <div class="text_alt">公众号二维码</div>
          </div>
          <div class="vx_official_notice--header">
            <div class="title">微信绑定用户</div>
            <div class="refresh" :style="refreshClick ? 'opacity: 0.6' : ''" @click="wechatAccountRefresh()">刷新</div>
          </div>
          <div class="vx_official_notice--bindUser">
            <div v-if="!vxBindUserList.length" class="flex">
              <div class="empty">暂无数据</div>
            </div>
            <div v-else v-for="user in vxBindUserList" :key="user.openId" class="flex">
              <div>
                <div class="vx_official_notice--title clamp">微信昵称：{{user.nickName}}</div>
                <div class="date">绑定时间：{{user.bindTime}}</div>
              </div>
              <div class="unBind" @click="showUnbindConfirm(user)">
                解绑
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="width: 100%; height: 100%; position: fixed; top: 0px; left: 0px; background: rgba(0, 0, 0, 0.5); z-index: 101; overflow: auto;" v-show="editModel">
      <div style="padding: 24px;width: 450px;height: 236px;margin:0 auto; background: rgb(255, 255, 255); border-radius: 6px; position: relative;top: 50%;margin-top: -118px;">
        <div style="padding-bottom: 24px;color: #CFA26B;font-size: 20px;font-weight: bold;">编辑模板名称
          <svg style="float: right;margin-top: 5px;cursor: pointer"  @click="editModel = false" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M1.55635 0L0.142135 1.41422L6.64214 7.91422L0 14.5564L1.41421
              15.9706L8.05635 9.32843L14.2843 15.5564L15.6985 14.1421L9.47056 7.91421L15.5563 1.82843L14.1421
              0.414215L8.05635 6.5L1.55635 0Z" fill="#8197A6"/>
          </svg>
        </div>
        <div>
          <el-input
            v-model="midModel"
            placeholder="请输入模板名称"
            maxlength="20"
          ></el-input>
        </div>
        <div style="margin-top: 24px;float: right;">
          <div class="btn" style="color: #CFA26B;margin-right: 20px;" @click="editModel = false">取消</div>
          <div class="btn" style="background: #CFA26B;" @click="editConfirm">确定</div>
        </div>
      </div>
    </div>
    <div
      v-show="showImg"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 3000;"
      @click="showImg = false"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);;"
      ></div>
      <div
        class="newcheck_div"
        style="position: relative;z-index: 800;height: 255px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;"
      >
        <img style="height: 255px; width: 450px" src="../../image/imgSize.png" />
      </div>
    </div>
    <div v-show="showBindConfirm" class="pc_cf1">
      <div class="pc_cf2">
        <div class="pc_cf3">
          <div class="pc_cf4">提示</div>
          <div class="pc_cf5">{{bindConfirmMsg}}</div>
          <div class="pc_cf6">
            <div
              class="pc_cf7"
              @click="showBindConfirm = false"
            >
              取消
            </div>
            <div
              class="pc_cf8"
              @click="showBindConfirm = false;updateBindStatus(false)"
            >
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="showAutoMakeCodeConfirm" class="pc_cf1">
      <div class="pc_cf2">
        <div class="pc_cf3">
          <div class="pc_cf4">提示</div>
          <div class="pc_cf5">{{`当前店铺有${noCodeNum}个商品因无条码无法同步到群客多微店，是否自动生成条码？`}}</div>
          <div class="pc_cf6">
            <div
              class="pc_cf7"
              @click="showAutoMakeCodeConfirm = false;updateBindStatus(true)"
            >
              否
            </div>
            <div
              class="pc_cf8"
              @click="autoMakeCodes()"
            >
              是
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="showUpdatePriceConfirm" class="pc_cf1">
      <div class="pc_cf2">
        <div class="pc_cf3">
          <div class="pc_cf4">提示</div>
          <div class="pc_cf5">{{updatePriceConfirmMsg}}</div>
          <div class="pc_cf6">
            <div
              class="pc_cf7"
              @click="showUpdatePriceConfirm = false"
            >
              取消
            </div>
            <div
              class="pc_cf8"
              @click="updatePriceCheck(false)"
            >
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-show='showOverdueDate'
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
      ></div>
      <div class='overdueDialog'>
        <div class="overdueContent">
          <div style="border-bottom: 1px solid #E3E6EB;height: 35px;display: flex;justify-content:space-between;">
            <div style="font-weight:bold;font-size: 18px;color:#567485;">过期预警通知</div>
            <i class="el-icon-close" style="font-size: 22px;font-weight: bold;margin-top: 3px;cursor: pointer;"
            @click="showOverdueDate = false"></i>
          </div>
          <div style="overflow: hidden;margin-top: 15px;margin-left: 30px;">
            <el-input
              v-model="modifyOverdueDate"
              @blur="setNum('modifyOverdueDate')"
              @input="modifyOverdueDate = $intMaxMinLimit({data: modifyOverdueDate, max: 9999, min: 0})"
              placeholder="最大输入9999"
              style="width: 130px;float: left;"
            ></el-input>
            <div style="margin-left: 15px;float: left;margin-top: 10px;">天内过期的商品</div>
          </div>
          <div style="text-align:right;font-size:16px;color:#567485;
            font-family:Microsoft YaHei UI,sans-serif;margin-right:40px;" v-html="priceAdjustRemark">
          </div>
          <div style="margin-top: 20px;display:flex;justify-content:center;padding: 15px 25px 0;">
            <div
              class="determine"
              style="background: #FFF;color: #BDA169"
              @click="showOverdueDate = false">取消</div>
            <div
              class="determine"
              @click="updateOverdueDate">确定</div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="showTipDialog" class="pc_cf1">
      <div class="pc_cf2">
        <div class="pc_cf3">
          <div class="pc_cf4">{{tipDialogTitle}}</div>
          <div class="pc_cf5">{{tipDialogMsg}}</div>
          <div class="pc_cf6">
            <div class="pc_cf8" @click="showTipDialog = false;">
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 微信助手解绑模态框 -->
    <div v-show="showUnbindDialog" class="pc_cf1">
      <div class="pc_cf2">
        <div class="pc_cf3">
          <div class="pc_cf4">提示</div>
          <div class="pc_cf5">解绑后将无法扫码登录，微信公众号、小程序也无法获得店铺的经营信息，确定解绑吗？</div>
          <div class="pc_cf6">
            <div
              class="pc_cf7"
              @click="showUnbindDialog = false"
            >
              取消
            </div>
            <div
              class="pc_cf8"
              @click="unbindUser()"
            >
              确定
            </div>
          </div>
        </div>
      </div>
    </div>
    <confirm-dialog
      :visible.sync="shotChangeTips"
      confirm-text="继续"
      message="当前收银台默认分类为“热销”<br/>
      关闭后系统自动将“全部分类”作为<br/>
      默认分类，确定继续吗？"
      @cancel="shotChangeTips = false, showHotGood = true"
      @confirm="handleConfirm"></confirm-dialog>
    <confirm-dialog
      title="请联系客服"
      :visible.sync="deleteAccountShow"
      :throttle="false"
      :show-cancel="false"
      confirm-text="知道了"
      message="<p>400-033-2520</p>
      <p>(客服工作时间：8:00-17:00)</p>
      <p style='color: #FF0000'>注销商户后无法恢复，请谨慎操作</p>
      <p style='color: #FF0000'>为保障账号安全，请联系客服核对</p>
      <p style='color: #FF0000'>主体信息</p>"
      @confirm="deleteAccountShow = false"></confirm-dialog>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Dialog } from 'element-ui';
import { CountDown } from 'vant';
import Print from '@/components/pc_print_setting.vue';
import draggable from 'vuedraggable';
import CryptoJS from 'crypto-js';
import { areaList } from '@/utils/area.js';
import logList from '@/config/logList';
import ConfirmDialog from '@/common/components/ConfirmDialog';
export default {
  mixins: [Print],
  components: {
    [Dialog.name]: Dialog,
    [CountDown.name]: CountDown,
    draggable,
    ConfirmDialog
  },
  data () {
    return {
      showFlag: false, // 图标切换
      newShowFlag: false, // 图标切换
      areaList: areaList,
      selectedOption: [],
      setSystem: true,
      showHotGood: false, // 是否显示热销分类
      expiredWarningNotice: true, // 过期预警通知
      overdueDate: 10, // 过期预警默认通知10天内过期的商品
      modifyOverdueDate: 0,
      showOverdueDate: false,
      showImg: false,
      nowModel: 'model1',
      visible: false,
      dragging: null,
      modelWidth: 0,
      editModel: false,
      labelName: '执行标准',
      labelValue: 'GB18401',
      iCloud: false,
      cloudList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
        11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24],
      stopCloudList: [{
        select: false, startTime: '', endTime: ''
      }, {
        select: false, startTime: '', endTime: ''
      }, {
        select: false, startTime: '', endTime: ''
      }, {
        select: false, startTime: '', endTime: ''
      }],
      checking: false,
      num: '*******.*******.8.8',
      type: -1,
      tabName: '',
      weighComValue: '',
      kexianComValue: '',
      member_disc: '10.0',
      store_name: '创迹店铺',
      store_address: '烟台市芝罘区业达科技园',
      store_phone: 12345,
      show_border: true,
      name: '',
      phone: '',
      mode: '',
      industry: '',
      addr: '',
      id: '',
      old_pwd: '',
      new_pwd: '',
      new_pwd1: '',
      btnPwdAble: false,
      storeinfo: {},
      person_msg: {},
      cashier: '',
      network: '',
      update_click: false,
      line_height_options: [],
      balance_change_notice: true,
      delete_receipt: false,
      delBoxMargin: '5vh',
      focusDate: false,
      delDataArr: ['销售单据', '进货单据', '盘点单据'],
      delData_index: 0,
      fromDate: '', // 开始日期
      toDate: '', // 结束日期
      deltype: 'xs', // 单据类型
      selectTableData: [], // 已选数据
      total: 0,
      limit: 7,
      pagenum: 1,
      del_data: [],
      del_data_all: [],
      showDelAllData: false,
      verifycode: '',
      gologining: false,
      islocalonly: '0',
      isconfirmDel: false,
      countdownTime: 60000,
      contentHeight: document.body.clientHeight - 218,
      settingUpload: this.$rest.uploadImage,
      // advUpload: this.$rest.
      imgIndex: 0,
      falseFlag: false,
      showVideo: true,
      showCatImg: false,
      pinyin: false,
      midModel: '',
      midIndex: 0,
      isOffline: false,
      msLoading: false,
      loadingTimer: null,
      // 微店相关
      openid: '', // 用户微信唯一标识
      showScanningCodeImg: false, // 显示群客多小程序码
      showAutoMakeCodeConfirm: false, // 自动生成条码确认弹窗
      noCodeNum: 0,
      showBindConfirm: false, // 解绑确认弹窗
      showTipDialog: false, // 提示弹窗
      tipDialogTitle: '',
      tipDialogMsg: '',
      showUpdatePriceConfirm: false, // 更新售价确认弹窗
      updatePriceConfirmMsg: '', // 更新售价确认弹窗Msg
      bindConfirmMsg: '',
      bindDialogMsg: '',
      showUnbindDialog: false,
      vxUnbindUser: {},
      scanningCodeImg: '', // 群客多小程序码
      authCodeImg: '', // 个人/企业认证小程序码
      refreshClick: false,
      zgznClientId: '',
      // status_code: 0, // 群客多消息状态码
      signKey: 'uJEzVnhKUpcGkPUXXBrx0dZJnsbOH2XN',
      microShopStatus: 0, // 微店开通状态  0:未注册 1:已注册未激活 2:已激活未认证 3:已认证(个人/企业)
      curMicroShop: 'ps',
      personalShop: {
        shortName: '',
        certStatus: 3, // 个人店铺状态 0:审核中 1:认证成功 2:认证失败 3:未认证 4：待签约
        isBound: 0, // 个人店铺绑定状态 0:未绑定 1:已绑定
        isUpdatePrice: 0, // 个人店铺是否更新价格 0:不更新  1:更新
        lastUpdateTime: '', // 个人店铺最后更新价格的时间毫秒数
        title: '', // 个人店铺最后同步标题
        content: null // 个人店铺最后同步内容
      },
      enterpriseShop: {
        shortName: '',
        certStatus: 3, // 企业店铺状态 0:审核中 1:认证成功 2:认证失败 3:未认证 4：待签约
        isBound: 0, // 企业店铺绑定状态 0:未绑定 1:已绑定
        isUpdatePrice: 0, // 企业店铺是否更新价格 0:不更新  1:更新
        lastUpdateTime: '', // 企业店铺最后更新价格的时间毫秒数
        title: '', // 个人店铺最后同步标题
        content: null // 个人店铺最后同步内容
      },
      subNameMap: {
        pos: '零售版',
        clothing: '服装版',
        baking: '烘焙版',
        catering: '餐饮版'
      },
      officialAccountCodeImgUrl: '',
      vxBindUserList: [], // 微信助手绑定用户
      ultimateDetail: {
        endDate: '',
        ultimateName: '',
        period: ''
      },
      deleteDataTitle: `1.业务数据清空成功之后将无法恢复，请慎重操作
        2.为防止数据产生异常，请确保其它设备（PC端、移动端）上的掌柜智囊程序已升级到最新版本
        3.若在多台设备使用此账号，请及时登录并确认数据一致后再使用`,
      cashierDeep: '',
      isCheckAll: false,
      indeterminateVal: false,
      clearExoneration: false, // 清空免责
      exonerationStatement: `业务数据清空后，无法恢复。本操作产生的后果将由您自行承担，与本产品无关。
        您一旦进行清空操作，即表示您已知悉相应后果。请确保已充分理解且为您本人操作。`,
      uploadImg: '?' + new Date().getTime(),
      checkBoxVal: [],
      checkBoxOpt: [
        {id: 1, isDel: 0, name: '商品库信息(含分类单位)', sort: 1},
        {id: 5, isDel: 0, name: '会员主库信息(含会员设置)', sort: 2},
        {id: 3, isDel: 0, name: '员工主库信息', sort: 3},
        {id: 2, isDel: 0, name: '进货、销售、盘点明细(含供应商、商品库存)', sort: 4},
        {id: 6, isDel: 0, name: '会员充值消费记录', sort: 5},
        {id: 4, isDel: 0, name: '员工操作记录(交接班)', sort: 6}
      ],
      checkDisabled: [],
      defaultSettlementKey: 'Space',
      shotChangeTips: false, // 是否显示默认分类变更弹窗
      deleteAccountShow: false // 是否显示注销账号提示框
    };
  },
  watch: {
    setSystem() {
      var m_list = _.cloneDeep(this.storeList);
      var mid = JSON.parse(m_list[0].settings);
      mid.setSystem = this.setSystem ? '1' : '0';
      m_list[0].settings = JSON.stringify(mid);
      this.SET_SHOW({storeList: m_list});
      storeInfoService.updateSettings({'id': 1, 'settings': JSON.stringify(mid)}, () => {
        // todo
      }, () => {
        // todo
      });
    },
    tabName() {
      if (this.tabName !== 'tab7' && this.showKexian) {
        this.showKexianNumber('', 0);
      }
      if (this.tabName !== 'tab6' && this.hasWeighOff) {
        this.closeScale();
      }
      if (this.tabName === 'tab11') {
        setTimeout(() => {
          this.visible = true;
        }, 50);
      } else {
        this.visible = false;
      }
    },
    showKexian() {
      if (!this.showKexian) {
        this.showKexianNumber('', 0);
        this.kexianValue = '';
      }
      this.SET_SHOW({ showKexian: this.showKexian });
    },
    weighValue() {
      this.SET_SHOW({ weighSet: 0 });
      this.SET_SHOW({ weighStatus: '·未连接' });
      if ((this.weighValue === this.kexianValue) && this.weighValue !== '请选择' && this.weighValue !== '') {
        demo.msg('warning', '端口已被占用，请选择其他端口');
        this.weighValue = this.weighComValue;
      }
      this.SET_SHOW({ weighValue: this.weighValue });
    },
    kexianValue() {
      if ((this.weighValue === this.kexianValue) && this.kexianValue !== '请选择' && this.kexianValue !== '') {
        demo.msg('warning', '端口已被占用，请选择其他端口');
        this.kexianValue = this.kexianComValue;
      }
      this.SET_SHOW({ kexianValue: this.kexianValue });
    },
    weighTypeValue() {
      this.SET_SHOW({ weighSet: 0 });
      this.SET_SHOW({ weighStatus: '·未连接' });
      this.SET_SHOW({ weighTypeValue: this.weighTypeValue });
    },
    hasWeighOff() {
      if (this.hasWeighOff) {
        if (this.tabName === 'tab6') {
          this.focusInputWeight(2);
          this.SET_SHOW({ isOnceOpen: false });
        }
      } else {
        this.closeScale();
        this.weighValue = '';
        this.weighTypeValue = '';
      }
      this.SET_SHOW({ hasWeigh: this.hasWeighOff });
    },
    voiceOff() {
      this.SET_SHOW({ voiceOff: this.voiceOff });
    },
    soundVolume() {
      this.SET_SHOW({ soundVolume: this.soundVolume });
      if (this.soundListen) {
        this.tryPlayVoice('收款成功', this.soundVolume * 4);
      }
    },
    scanCodeSound() {
      this.SET_SHOW({ scanCodeSound: this.scanCodeSound });
    },
    'storeinfo.name' () {
      this.SET_SHOW({ 'username': this.storeinfo.name });
    },
    selectRow () {
      this.old_pwd = '';
      this.new_pwd = '';
      this.new_pwd1 = '';
      if (this.selectRow === 12) {
        this.resetLoading();
        this.curMicroShop = this.msSignType;
        this.microShop();
      }
    },
    showDelStoreData() {
      if (!this.showDelStoreData) {
        this.fromDate = new Date().format('yyyy-MM-dd'); // 开始日期默认当天
        this.toDate = new Date().format('yyyy-MM-dd'); // 结束日期默认当天
        this.del_data = [];
        this.del_data_all = [];
        this.pagenum = 1;
        this.total = 0;
        this.delData_index = 0;
        this.deltype = 'xs';
      }
    },
    showDelAllData() {
      if (this.showDelAllData) {
        this.verifycode = '';
      }
    },
    isSyncingLogin() {
      if (this.isSyncingLogin) {
        this.storeinfo.is_synced = 0;
        this.updateSettingBeforeDestroy();
      }
    },
    ultimate() {
      this.getUltimateInfo();
    },
    period() {
      this.getUltimateInfo();
    },
    sysEndDate() {
      this.getUltimateInfo();
    },
    isconfirmDel() {
      if (!this.isconfirmDel) {
        this.isCheckAll = false;
        this.indeterminateVal = false;
        this.clearExoneration = false;
      } else {
        this.checkBoxVal = [];
      }
    },
    microShopStatus() {
      // console.log(`oldValue: ${oldValue}, newValue: ${newValue}`);
      if (this.microShopStatus === 3) { // 存在认证完成的微店
        this.getBoundStatus();
      }
    },
    openid() {
      if (this.openid !== '') {
        // step2: 检查微店激活状态
        this.microShopStatus = 1;
        this.showScanningCodeImg = false;
        this.getMicroShopStatus();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    addrChange() {
      this.storeinfo.addr = this.selectedOption.join('@') + '@' + this.addr.replace(/[$']/g, '');
    },
    checkStoreInfo(info) {
      const reg = /[$';:]|[\uD83C-\uDBFF][\uDC00-\uDFFF]|\d\uFE0F\u20E3|\d\u20E3|[\u203C-\u3299]\uFE0F?|\u2122|\u2B55|\u303D|\uA9|\uAE|\u3030/gi;
      return info.replace(reg, '');
    },
    upCashier() {
      this.person_msg.uid = $userinfo.uid;
      this.person_msg.name = this.cashier;
      $userinfo.name = this.cashier;
      this.person_msg.sys_uid = this.sysUid;
      this.person_msg.sys_sid = this.sysSid;
      clerkService.upName(this.person_msg, () => {}, () => {});
      if (!this.loginInfo.employeeNumber) {
        this.loginInfo.name = this.cashier;
        this.SET_SHOW({ loginInfo: this.loginInfo });
      }
      if (this.cashierDeep !== this.cashier) {
        demo.$http.post(this.$rest.pc_personMsg, this.person_msg, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        });
      }
    },
    listFormat() {
      if (this.specs) {
        let arr = [];
        for (let key in this.specs) {
          arr.push({
            key: this.specs[key].name,
            title: this.specs[key].name,
            flag: true,
            value: this.specs[key].name,
            label: this.specs[key].name,
            titleKey: this.specs[key].name
          });
        }
        let newArr = _.differenceBy(arr, this.labelItemVerticals, 'title');
        this.labelItemVerticals = _.concat(newArr, this.labelItemVerticals);
        console.log(this.labelItemVerticals, 'this.labelItemVerticals');
      }
    },
    // 开始拖拽事件
    onStart() {
      this.drag = true;
    },
    // 拖拽结束事件
    onEnd() {
      this.drag = false;
    },
    setLine() {
      setTimeout(() => {
        let subWidth = 0;
        for (let i = 0; i < 6; i++) {
          subWidth += document.getElementsByClassName('pc_setModel1')[i].offsetWidth;
        }
        this.modelWidth = (document.getElementsByClassName('pc_setModel')[0].offsetWidth - subWidth - 1) + 'px;'
      }, 0);
    },
    editConfirm() {
      if (this.tagModels[this.midIndex].name !== this.midModel) {
        this.tagModels[this.midIndex].name = this.midModel.trim();
        this.setLine();
        demo.msg('success', '修改成功');
      }
      this.editModel = false;
    },
    editMode(item, index) {
      this.midModel = item.name;
      this.midIndex = index;
      this.editModel = true;
    },
    choseModel(key, index, item) {
      this.tagModels.map(o => { o.flag = false; });
      this.nowIndex = index;
      this.nowModel = key;
      item.flag = true;
    },
    fontChange(item, ind) {
      this.tagModels[this.nowIndex].subModel[ind].size = item;
    },
    labelChange(item, ind) {
      let data = _.find(this.labelItemVerticals, function(o) { return o.key === item; });
      this.tagModels[this.nowIndex].subModel[ind].key = data.key;
      this.tagModels[this.nowIndex].subModel[ind].title = data.title;
      this.tagModels[this.nowIndex].subModel[ind].flag = data.flag;
      this.tagModels[this.nowIndex].subModel[ind].value = data.value;
      this.tagModels[this.nowIndex].subModel[ind].label = data.label;
      this.tagModels[this.nowIndex].subModel[ind].titleKey = data.titleKey;
    },
    addNewLabel() {
      this.tagModels[this.nowIndex].subModel.push({ key: 'custom', title: '自定义', flag: true, value: 'GB18401', label: '执行标准', size: 10 });
    },
    delLabel(ind) {
      this.tagModels[this.nowIndex].subModel.splice(ind, 1);
    },
    selectCloud(n) {
      if (this.stopCloudList[n].startTime === '' || this.stopCloudList[n].endTime === '') {
        demo.msg('warning', '请先选择对应行中的时间，再进行勾选！');
      } else {
        this.stopCloudList[n].select = !this.stopCloudList[n].select;
      }
    },
    changeCloud(n) {
      if (this.stopCloudList[n].startTime >= this.stopCloudList[n].endTime) {
        this.stopCloudList[n].endTime = '';
        this.stopCloudList[n].select = false;
      }
    },
    kexianPriceTypeChange(val) {
      demo.updateStoreSettings('kexian_price_type', val)
        .then(() => {
          let log = _.cloneDeep(logList.clickGoodsPriceDisplay);
          log.description = log.description.format(val === 'amt' ? '小计' : '单价');
          demo.actionLog(log);
        });
    },
    showKexianNumber(num, type) {
      if (this.showKexian && this.kexianValue === '' && this.tabName === 'tab7') {
        demo.msg('warning', '请设置端口号');
        return;
      }
      console.log('客显显示金额：', num);
      console.log('客显显示类型：', type);
      this.num = num;
      this.type = type;
      external.customerdisplay({
        displaydata: num === '' ? '' : Number(num).toFixed(2).toString(),
        displaytype: type,
        port: this.kexianValue,
        baudrate: '2400',
        databits: '8',
        parity: '0',
        stopBits: '1'
      });
    },
    focusInputWeight(index) {
      if (this.checking) {
        return;
      }
      this.checking = true;
      setTimeout(() => {
        this.checking = false;
      }, 2500);
      if (this.weighValue === '') {
        if (index === 1) {
          demo.msg('warning', '请选择端口号');
        }
        return;
      }
      if (this.weighTypeValue === '') {
        if (index === 1) {
          demo.msg('warning', '电子秤类型');
        }
        return;
      }
      try {
        var data = {
          exec: 'open',
          port: this.weighValue,
          baudrate: '9600',
          databits: '8',
          parity: '0',
          stopBits: '1',
          script: 'demo.weightConvertSetting("{0}");',
          scaleName: this.weighTypeValue
        };
        external.execScale(data);
        setTimeout(() => {
          this.checkConnected();
        }, 1000);
      } catch (e) {
        this.SET_SHOW({ weighStatus: '·未连接' });
        demo.msg('warning', this.$msg.electronic_scale_in_error);
      }
    },
    checkConnected() {
      if (this.weighStatus === '·未连接') {
        demo.msg('warning', '电子秤未打开或端口号、电子秤类型不匹配');
      }
    },
    closeVideo() {
      this.showVideo = false;
    },
    handleAvatarSuccess() {
      this.logAndCode[this.imgIndex].hasImg = true;
      this.SET_SHOW({logAndCode: this.logAndCode});
    },
    getFile(file) {
      this.getBase64(file.raw).then(res => {
        this.printImg(res, this.imgIndex);
      });
    },
    printImg(res, index) {
      external.saveImage(index === 0 ? 'logo' : 'qrcode', res, url => {
        console.log(url, 'url');
        url = url.split('?')[0];
        this.uploadImg = '?' + new Date().getTime();
        this.logAndCode[index].url = url;
        this.logAndCode[index].hasImg = true;
        this.SET_SHOW({logAndCode: this.logAndCode});
      });
    },
    getBase64(file) {
      return new Promise(function(resolve, reject) {
        let reader = new FileReader();
        let imgResult = '';
        reader.readAsDataURL(file);
        reader.onload = function() {
          imgResult = reader.result;
        };
        reader.onerror = function(error) {
          reject(error);
        };
        reader.onloadend = function() {
          resolve(imgResult);
        };
      });
    },
    getPreviewStyle() {
      var style = '';
      if (this.print_cols_label === '60') {
        style += 'transform: rotate(-90deg);width: 240px;height:160px;' +
          'margin-left: -40px;margin-top: 40px;margin-bottom: 60px;';
      } else if (this.print_cols_label === '64') {
        style += 'width: 240px;height: 160px;';
      } else {
        style += 'width: 160px;height:120px;';
      }
      return style;
    },
    setShowJs() {
      this.SET_SHOW({logAndCode: this.logAndCode});
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG && !isPNG) {
        demo.msg('warning', '上传图片只能是 JPG/PNG 格式!');
      } else if (!isLt2M) {
        demo.msg('warning', '上传图片大小不能超过 2MB!');
      } else {
        console.log('其他');
      }
      return (isJPG && isLt2M) || (isPNG && isLt2M);
    },
    tabsBeforeLeave(activeName, oldActiveName) {
      console.log(activeName);
      if (oldActiveName === 'tab6' && this.hasWeigh) {
        if (this.weighValue === '') {
          demo.msg('warning', '电子秤端口号不能为空');
          return false;
        } else if (this.weighTypeValue === '') {
          demo.msg('warning', '电子秤类型不能为空');
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    handleClick(tab) {
      this.tabName = tab.name;
      if (tab.name === 'tab6') {
        this.weighComValue = _.cloneDeep(this.weighValue);
        if (this.hasWeighOff) {
          this.focusInputWeight(3);
        }
      }
      if (tab.name === 'tab7') {
        this.kexianComValue = _.cloneDeep(this.kexianValue);
      }
      if (tab.name === 'tab11') {
        this.setLine();
      }
      this.SET_SHOW({ settingIndex: tab.name });
    },
    goPageTag(index) {
      // 点击设置-系统设置记录日志
      if (index === 2) {
        demo.actionLog(logList.clickSystemSetting);
      }
      // 点击设置-价格设置记录日志
      if (index === 5) {
        demo.actionLog(logList.clickPriceSetting);
      }
      // 点击设置-云同步设置记录日志
      if (index === 11) {
        demo.actionLog(logList.clickCloudSyncSetting);
      }
      if (this.selectRow === 3 && this.isSetting && this.hasWeigh) {
        if (this.weighValue === '') {
          demo.msg('warning', '电子秤端口号不能为空');
          return;
        }
        if (this.weighTypeValue === '') {
          demo.msg('warning', '电子秤类型不能为空');
          return;
        }
      }
      // 点击设置-配件设置记录日志
      if (index === 3) {
        this.SET_SHOW({ settingIndex: 'tab0' });
        demo.actionLog(logList.clickAccessorySetting);
      }
      this.SET_SHOW({ selectRow: index });
      if (index === 13) { // 微信助手
        demo.actionLog(logList.clickWechatAssistant);
        this.createWechatAccountCode();
        this.getWechatAccountBindUser();
      }
    },
    createWechatAccountCode() { // 生成微信助手公众号二维码
      if (pos.network.isConnected()) {
        let clientId = external.getMac();
        let param = {
          systemName: $config.systemName,
          subName: $config.subName,
          sysUid: $setting.useruid,
          from: 2,
          clientId: clientId
        };
        demo.$http
          .post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmCreateQrCode, param)
          .then(res => {
            console.log(res);
            if (res.data.code === 0 && res.data.data) {
              this.officialAccountCodeImgUrl = res.data.data.qrCodeUrl;
              settingService.setInfoValueForKey('official_account_code_img_url', this.officialAccountCodeImgUrl); // 缓存图片地址
            } else {
              this.getCacheCodeImg();
            }
          })
          .catch(() => { this.getCacheCodeImg(); });
      } else {
        this.getCacheCodeImg();
      }
    },
    getCacheCodeImg() {
      this.officialAccountCodeImgUrl = demo.t2json($setting.info).official_account_code_img_url || '';
    },
    wechatAccountRefresh() { // 微信绑定列表刷新功能防止二次点击
      if (this.refreshClick) {
        return;
      }
      this.refreshClick = true;
      setTimeout(() => {
        this.refreshClick = false;
      }, 2000);
      this.getWechatAccountBindUser();
    },
    getWechatAccountBindUser() { // 获取微信公众号绑定列表
      if (pos.network.isConnected()) {
        let param = {
          sysUid: this.sysUid,
          sysSid: this.sysSid,
          systemName: $config.systemName,
          subName: $config.subName,
          bindStatus: 1
        };
        demo.$http
          .post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmGetBindList, param)
          .then(res => {
            console.log(res);
            if (res.data.code === 0 && res.data.data) {
              this.vxBindUserList = res.data.data;
            } else {
              this.vxBindUserList = [];
            }
          })
          .catch(() => {
            // do nothing
          });
      }
    },
    // 店铺营业微信通知解绑确认
    showUnbindConfirm(user) {
      this.vxUnbindUser = user;
      this.showUnbindDialog = true;
    },
    // 店铺营业微信通知解绑
    unbindUser() {
      if (pos.network.isConnected() && Object.keys(this.vxUnbindUser).length) {
        let param = {
          type: 'unbind',
          sysUid: this.sysUid,
          sysSid: this.sysSid,
          systemName: $config.systemName,
          subName: $config.subName,
          openId: this.vxUnbindUser.openId,
          unionId: this.vxUnbindUser.unionId
        }
        demo.$http.post($config.Base.OtherOptions.zgoapUrl + this.$rest.zgcmUnbindUser, param)
          .then(res => {
            if (res.data.code === 0) {
              // 解绑成功
              demo.msg('success', '店铺营业微信通知解绑成功');
              CefSharp.PostMessage(`店铺营业微信通知解绑成功，param: ${JSON.stringify(param)}`);
              let description = "店铺营业微信通知解绑成功";
              demo.actionLog({page: 'setting', action: 'unbind', description});
              // 重新获取绑定user
              this.getWechatAccountBindUser();
            } else {
              CefSharp.PostMessage(`店铺营业微信通知解绑失败，res: ${JSON.stringify(res)}`);
              let description = "店铺营业微信通知解绑失败";
              demo.actionLog({page: 'setting', action: 'unbind', description});
            }
          })
        this.showUnbindDialog = false;
      } else {
        demo.msg();
        this.showUnbindDialog = false;
      }
    },
    setClass () {
      let setClassEls = document.getElementsByClassName('popper__arrow');
      for (let i = 0; i < setClassEls.length; i++) {
        setClassEls[i].setAttribute('class', 'popper__arrow pc_pay193');
      }
    },
    resetLoading() {
      this.msLoading = true;
      if (this.loadingTimer) {
        clearTimeout(this.loadingTimer);
      }
      this.loadingTimer = setTimeout(() => {
        this.msLoading = false;
      }, 1500);
    },
    clearLoading() {
      clearTimeout(this.loadingTimer);
      this.msLoading = false;
    },
    set_member_goods(n) {
      this.SET_SHOW({ showMemberGoods: true });
      this.SET_SHOW({ pcSmgFrom: n });
    },
    check_input (n) {
      if (n == 'member_disc') {
        if (isNaN(this.member_disc) == true) {
          this.member_disc = '10.0';
        } else if (Number(this.member_disc) < 0.1 || Number(this.member_disc) > 10.0) {
          this.member_disc = '10.0';
        } else {
          this.member_disc = Number(this.member_disc).toFixed(1);
        }
      }
    },
    subVolume() {
      if (this.soundVolume > 1) {
        this.soundVolume -= 1;
        this.SET_SHOW({ soundVolume: this.soundVolume });
      } else {
        this.tryPlayVoice('收款成功', this.soundVolume * 4);
      }
    },
    addVolume() {
      if (this.soundVolume < 5) {
        this.soundVolume += 1;
        this.SET_SHOW({ soundVolume: this.soundVolume });
      } else {
        this.tryPlayVoice('收款成功', this.soundVolume * 4);
      }
    },
    tryPlayVoice(str, volume) {
      if (this.soundTimeout) {
        clearTimeout(this.soundTimeout);
      }
      this.soundTimeout = setTimeout(() => {
        try {
          external.playVoice(str, volume);
        } catch (e) {
          external.playVoice(str);
        }
      }, 500);
    },
    get_setting () {
      var that = this;
      storeInfoService.get({ id: 1 }, function (res) {
        that.storeinfo = demo.t2json(res)[0];
        var json = demo.t2json(res);
        if (json && json.length > 0) {
          that.name = json[0].name;
          that.industry = json[0].industry;
          if (json[0].addr && json[0].addr.split('@').length >= 4) {
            let addr = json[0].addr.split('@');
            that.selectedOption = addr.splice(0, 3);
            that.addr = addr[0];
          } else {
            that.addr = json[0].addr;
          }
          that.id = json[0].id;
          that.iCloud = json[0].settings !== '' &&
            JSON.parse(json[0].settings).hasOwnProperty('iCloud')
            ? JSON.parse(json[0].settings).iCloud : false;
          if (json[0].settings === '' || !JSON.parse(json[0].settings).hasOwnProperty('stopCloudList')) {
            that.stopCloudList = [{
              select: false, startTime: '', endTime: ''
            }, {
              select: false, startTime: '', endTime: ''
            }, {
              select: false, startTime: '', endTime: ''
            }, {
              select: false, startTime: '', endTime: ''
            }];
          } else {
            that.stopCloudList = JSON.parse(json[0].settings).stopCloudList;
          }
          if (json[0].settings !== '' && demo.t2json(json[0].settings).hasOwnProperty('kexian_price_type')) {
            that.kexianPriceType = demo.t2json(json[0].settings).kexian_price_type;
          }
        }
      });
    },
    /**
     * 获取收银台默认支付方式，先从storeinfo表的settings字段里取，没有的话再取setting表的info字段里取
     * key: defaultAcctsId
     */
    getDefaultAcctsId() {
      let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
      if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
        this.setDefaultAcctsIdBy(dataInfo);
      } else {
        this.setDefaultAcctsIdBy(demo.t2json(this.storeList[0].settings).default_accts_id === undefined
          ? dataInfo : demo.t2json(this.storeList[0].settings));
      }
    },
    setDefaultAcctsIdBy(dataInfo) {
      if (dataInfo.default_accts_id) {
        this.defaultAcctsId = demo.t2json(dataInfo.default_accts_id);
        this.SET_SHOW({ defaultAcctsId: this.defaultAcctsId });
      }
      const show_hot_good = demo.t2json(dataInfo.show_hot_good);
      this.showHotGood = show_hot_good === undefined ? true : show_hot_good;
    },
    getMicroShopSetting() {
      try {
        this.showMicroShop = demo.t2json($config.Base.OtherOptions).qunKeDuo.showMicroShop === 1;
      } catch (e) {
        // do nothing
      }
    },
    // 获取激活码版本信息
    getUltimateInfo() {
      this.ultimateDetail.ultimateName = this.ultimate ? '旗舰版' : (this.ultimate === false ? '专业版' : '简易版');
      this.ultimateDetail.period = this.period;
      this.ultimateDetail.endDate = new Date(parseInt(isNaN(this.sysEndDate) ? Number($config.activationCode.endDate) : this.sysEndDate)).format('yyyy/MM/dd');
    },
    renewClick(flg) { // 立即续费
      if (flg) {
        this.SET_SHOW({ onlyShowBuyChoose: false, isVesionCompare: true });
      } else {
        demo.actionLog(logList.clickRenewImmediately);
        this.SET_SHOW({ onlyShowBuyChoose: true, isVesionCompare: true });
      }
    },
    checkPassword () {
      // 密码由8位以上数字，字母
      // const re = new RegExp(`(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,20}$`);
      const re = new RegExp(`^(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9]{8,20}$`);
      return re.test(this.new_pwd);
    },
    // 修改密码
    updPassword () {
      let _this = this;
      if (this.update_click) {
        return;
      }
      this.update_click = true;
      setTimeout(function () {
        _this.update_click = false;
      }, _this.clickInterval);
      if (!pos.network.isConnected()) {
        demo.msg('warning', '请检查网络，在联网的状态下修改密码');
        return;
      }

      if (_this.old_pwd == '') {
        demo.msg('warning', _this.$msg.enter_original_password);
        return;
      }
      if (_this.old_pwd.length < 6) {
        demo.msg('warning', _this.$msg.original_password_min_length_not_met);
        return;
      }
      if (!this.checkPassword()) {
        demo.msg('warning', _this.$msg.enter_new_password);
        return;
      }
      if (_this.new_pwd1 == '') {
        demo.msg('warning', _this.$msg.enter_confirm_password);
        return;
      }
      if (_this.new_pwd1.length < 8) {
        demo.msg('warning', _this.$msg.confirm_password_min_length_not_met);
        return;
      }
      if (_this.new_pwd != _this.new_pwd1) {
        demo.msg('warning', _this.$msg.new_passwords_are_different);
        return;
      }
      if (_this.old_pwd == _this.new_pwd) {
        demo.msg('warning', _this.$msg.new_password_is_same_as_original_password);
        return;
      }

      var Content_Type = 'application/json';
      var params = {
        username: _this.sysUid,
        password: md5(_this.old_pwd),
        newPassword: md5(_this.new_pwd)
      };

      _this.btnPwdAble = true;
      _this.$http
        .post(_this.$rest.updPassword, params, {
          headers: { 'Content-Type': Content_Type, Authorization: _this.token }
        })
        .then(function (res) {
          var rd = res.data;
          if (rd.code === 200) {
            var param = [
              {
                key: settingService.key.userpwd,
                value: md5(_this.new_pwd)
              }
            ];
            settingService.put(
              param,
              () => {
                _this.SET_SHOW({ password: md5(_this.new_pwd) });
                _this.old_pwd = '';
                _this.new_pwd = '';
                _this.new_pwd1 = '';
                demo.msg('success', _this.$msg.password_edit_success);
              },
              () => {
                demo.msg('warning', _this.$msg.password_edit_failure);
              }
            );
          } else {
            demo.msg('warning', rd.msg);
          }
          _this.btnPwdAble = false;
        })
        .catch(() => {
          _this.btnPwdAble = false;
        });
    },

    getCashier () {
      this.cashier = $userinfo.name;
      this.cashierDeep = _.cloneDeep(this.cashier);
    },
    delStoreData () {
      this.SET_SHOW({ showDelStoreData: true });
    },
    closeDelStoreData () {
      this.SET_SHOW({ showDelStoreData: false });
    },
    clickDelReceipt (n) {
      this.delData_index = n;
      if (n === 0) { // 销售单据
        this.deltype = 'xs';
      } else if (n === 1) {
        this.deltype = 'jh';
      } else {
        this.deltype = 'pd';
      }
      this.del_data = [];
      this.del_data_all = [];
      this.pagenum = 1;
      this.total = 0;
      this.fromDate = new Date().format('yyyy-MM-dd'); // 开始日期默认当天
      this.toDate = new Date().format('yyyy-MM-dd'); // 结束日期默认当天
      console.log(this.deltype);
    },
    selCurRow () {
      // 选中当前行
      console.log('//');
    },
    getSelectedReceipt(res) {
      this.selectTableData = res;
      console.log(this.selectTableData);
    },
    handleCurrentChange(val) {
      // 分页
      this.del_data = this.del_data_all.slice(this.limit * (val - 1), this.limit * val);
    },
    tableHeaderColor () { // 表头颜色
      return 'background: #F9FBFB;';
    },
    beforeSearchReceipt() {
      // xs jh pd
      if (this.fromDate === null) {
        demo.msg('warning', '请选择开始日期');
        return;
      } else if (this.toDate === null) {
        demo.msg('warning', '请选择结束日期');
        return;
      } else if (this.fromDate > this.toDate) {
        var mid_date = this.toDate;
        this.toDate = this.fromDate;
        this.fromDate = mid_date;
      } else {
        console.log('');
      }
      var params = {};
      if (this.deltype === 'xs') { // 销售明细
        params = {
          from: this.fromDate,
          to: this.toDate,
          inOut: 0,
          accountId: 0,
          condition: '',
          limit: 100000,
          offset: 0
        };
      } else if (this.deltype === 'jh') {
        params = {
          from: this.fromDate,
          to: this.toDate,
          search_data: '',
          supplier_id: '',
          accountId: '',
          inOut: '',
          page_size: 100000,
          page_num: 1
        };
      } else {
        params = {
          from: this.fromDate,
          to: this.toDate
        };
      }
      this.searchReceipt(params);
    },
    searchReceipt(sub_data) {
      var that = this;
      if (this.deltype === 'xs') {
        console.log(sub_data, '请求销售明细参数');
        saleService.search(
          sub_data,
          function(res) {
            // that.del_data = res.days;
            that.sonarSearchReceipt(res);
          },
          function(res) {
            console.error(res);
          }
        );
      } else if (this.deltype === 'jh') {
        console.log(sub_data, '请求进货明细参数');
        purchaseService.search(sub_data, function(res) {
          that.sonarSearchReceipt(res);
          console.log(res, '请求进货明细res');
        }, function(res) {
          console.error(res);
        });
      } else { // 盘点明细
        console.log(sub_data, '请求盘点明细参数');
        inventoryService.detailReports(sub_data, function(res) {
          console.log(res, '请求盘点明细结果++');
          that.sonarSearchReceipt(res);
        }, function(res) {
          console.error(res);
        }
        );
      }
    },
    sonarSearchReceipt(res) {
      this.del_data = [];
      this.del_data_all = [];
      var that = this;
      // 这个接口返回空，为res = {}
      if (JSON.stringify(res) === '{}' || JSON.stringify(res) === '[]' || JSON.stringify(res.days) === '[]') {
        this.pagenum = 1;
        this.total = 0;
      } else { // 天别数据汇总
        for (var i = 0; i < res.days.length; i++) {
          for (var j = 0; j < res.days[i].details.length; j++) {
            this.del_data_all.push(res.days[i].details[j]);
          }
        }
        this.del_data = this.del_data_all.slice(0, this.limit);
        clerkService.getEmployeeNumberByUid(this.del_data_all, ['uid'], () => { // 根据uid找到uid_oid
          that.$nextTick(() => {
            that.$refs['deltableCurrentRow'].doLayout();
          });
        });
        console.log(this.del_data, '天别数据汇总');
        this.total = Number(this.del_data_all.length);
      }
    },
    beforeBatchDelReceipt() {
      if (this.selectTableData.length === 0) {
        demo.msg('warning', '请选择要删除的数据!');
        return;
      }
      this.delete_receipt = true;
    },
    batchDelReceipt() {
      var delIdList = '';
      var that = this;
      console.log(this.selectTableData.length, 'selectTableData++');
      for (var i = 0; i < this.selectTableData.length; i++) {
        delIdList += this.selectTableData[i].id + ',';
      }
      var delId_list = delIdList.substr(0, delIdList.length - 1);
      var sub_data = {
        id: delId_list
      };
      if (this.deltype === 'xs') {
        saleService.deleteBatch(sub_data, function() {
          demo.msg('success', '批量删除成功!');
          that.beforeSearchReceipt();
        }, function(res) {
          console.error(res);
          demo.msg('error', '批量删除失败!');
        });
      } else if (this.deltype === 'jh') {
        purchaseService.deleteBatch(sub_data, function() {
          demo.msg('success', '批量删除成功!');
          that.beforeSearchReceipt();
        }, function(res) {
          console.error(res);
        });
      } else {
        inventoryService.deleteBatch(sub_data, function() {
          demo.msg('success', '批量删除成功!');
          that.beforeSearchReceipt();
        }, function(res) {
          console.error(res);
        });
      }
      this.pagenum = 1;
    },
    reportFormLog(sub_data, description) { // 记录报表操作日志
      if (pos.network.isConnected()) {
        sub_data['description'] = description;
        this.$log.info('reportForm', sub_data);
        demo.actionLog({page: 'pc_stock_overdue_warning_detail', action: 'reportFormLog', description});
      }
    },
    // =========== 微店设置 ============
    microShop() {
      if (!pos.network.isConnected()) { // 离线状态页面
        this.isOffline = true;
      } else {
        this.isOffline = false;
        // 查询微店是否注册
        demo.$http.post(this.$rest.msRegisterSearch)
          .then(res => {
            if (res.data.code === 200) {
              if (res.data.data) { // 已注册
                this.openid = res.data.data.openid;
                this.resetLoading();
              } else {
                this.createScanningCode(true);
              }
            }
          })
          .catch(err => {
            demo.msg('error', err.msg);
          });
      }
    },
    msRegister(openid, success) { // 微店注册
      demo.$http.post(this.$rest.msRegister, {openid: openid})
        .then(res => {
          if (res.data.code === 200) { // 注册成功
            this.openid = openid;
            success();
          } else {
            demo.msg('warning', res.data.msg);
          }
        })
        .catch(err => {
          demo.msg('error', err.msg);
        });
    },
    refreshScanCode() {
      // 微信扫一扫登录
      if (this.refreshClick) {
        return;
      }
      this.refreshClick = true;
      setTimeout(() => {
        this.refreshClick = false;
      }, 2000);
      this.createScanningCode();
    },
    createScanningCode(flag) {
      // 生成群客多小程序二维码
      let clientId = newMd5(external.getMac(), 16);
      let sign = md5(clientId + this.qkdSign);
      let createSnCode = demo.t2json($config.Base.OtherOptions).qunKeDuo.qkdQrCodeUrl;
      demo.$http.get(createSnCode + '?clientId=' + clientId + '&sign=' + sign).then(
        res => {
          console.log(res);
          if (res.data.status_code === 200 && res.data.message === '查询成功') {
            this.scanningCodeImg = res.data.data;
            if (flag && !this.client) {
              // 需要连接mqtt
              CefSharp.PostMessage('生成小程序码并连接mqtt');
              this.mqttConnect();
            }
          } else {
            this.scanningCodeImg = '';
          }
        },
        error => {
          this.scanningCodeImg = '';
          demo.msg('warning', '小程序码获取失败，请稍后再试');
          console.error(error);
        }
      );
    },
    mqttConnect(flag) {
      let mqttOptions = _.cloneDeep(demo.t2json($config.mqttOptions));
      mqttOptions.keepAlive = 60;
      mqttOptions.clientId = mqttOptions.groupId + '@@@' + external.getMac();
      mqttOptions.port = 443;
      mqttOptions.username = 'Signature|' + mqttOptions.accessKey + '|' + mqttOptions.instanceId;
      mqttOptions.password = CryptoJS.HmacSHA1(mqttOptions.clientId, mqttOptions.secretKey).toString(CryptoJS.enc.Base64);
      this.zgznClientId = mqttOptions.clientId;
      // 连接mqtt broker
      this.client = new Paho.MQTT.Client(mqttOptions.brokerUrl, mqttOptions.port, mqttOptions.clientId);
      let options = {
        timeout: 6000,
        mqttVersion: 4,
        cleanSession: mqttOptions.cleanSession,
        onSuccess: () => {
          console.log('mq onSuccess');
          CefSharp.PostMessage('微信绑定mqtt连接成功');
        },
        onFailure: e => {
          console.log('mq onFailure:', e);
          CefSharp.PostMessage('mqtt连接失败，errorMsg:' + e);
          if (!flag) {
            CefSharp.PostMessage('微信绑定mqtt尝试三秒后重连一次');
            setTimeout(this.mqttConnect(true), 3000);
          }
        }
      };
      this.client.onMessageArrived = message => {
        this.receivedMessage(demo.t2json(message));
      };
      if (mqttOptions.username != null) {
        options.userName = mqttOptions.username;
        options.password = mqttOptions.password;
        options.useSSL = true;
      }
      this.client.connect(options);
    },
    receivedMessage(message) {
      let topic = message.destinationName;
      let payload = demo.t2json(message.payloadString);
      CefSharp.PostMessage('mqtt zgznClientId:' + this.zgznClientId + ', topic:' + topic + ', payload:' + message.payloadString);
      // 1:二维码已被扫描   2:用户已授权  3:用户拒绝授权
      // if (payload.status === 3) {
      //   CefSharp.PostMessage('群客多小程序拒绝授权,payload：' + message.payloadString);
      //   demo.msg('warning', '群客多小程序拒绝授权!');
      //   return;
      // }
      if (payload.status === 2) {
        this.openid = payload.wxOpenId;
        this.msRegister(this.openid, () => {
          this.showScanningCodeImg = false;
        });
        // 断开长链接
        if (this.client) {
          CefSharp.PostMessage('微信绑定完成后断开mqtt连接');
          this.client.disconnect();
        }
      }
    },
    microShopRegister() {
      // 注册微店
      this.showScanningCodeImg = true;
    },
    microShopActive() {
      // 调用群客多激活API激活微店
      this.resetLoading();
      let activeApi = demo.t2json($config.Base.OtherOptions).qunKeDuo.qkdCheckCodeUrl;
      let ts = new Date().getTime();
      let data = {
        openId: this.openid,
        ts: ts,
        sign: md5(this.signKey + this.openid + ts).toUpperCase()
      }
      demo.$http.post(activeApi, data).then(
        res => {
          console.log(res);
          if (res.data.data && res.data.data.status_code === 200) {
            demo.msg('success', res.data.data.data === true ? '激活成功' : res.data.data.data);
            this.microShopStatus = 2;
            this.showScanningCodeImg = false;
            this.getAuthStatus(); // 查询微店认证状态
          } else {
            demo.msg('error', res.data.data.data);
            this.clearLoading();
          }
        },
        error => {
          this.clearLoading();
          demo.msg('warning', '激活失败');
          console.error(error);
        }
      );
    },
    getMicroShopStatus(flag) {
      this.resetLoading();
      // 检查微店激活状态
      let getStatusApi = demo.t2json($config.Base.OtherOptions).qunKeDuo.qkdGetActivationUrl;
      let ts = new Date().getTime();
      let data = {
        openId: this.openid,
        ts: ts,
        sign: md5(this.signKey + this.openid + ts).toUpperCase()
      }
      demo.$http.post(getStatusApi, data).then(
        res => {
          console.log(res);
          if (+res.data.status_code === 200) {
            if (res.data.data) {
              if (flag) {
                demo.msg('success', '微店已激活');
              }
              this.microShopStatus = 2;
              this.getAuthStatus(); // 查询微店认证状态
            } else {
              this.clearLoading();
              demo.msg('warning', '微店未激活');
            }
          } else {
            this.clearLoading();
            demo.msg('error', res.data.message);
          }
        })
        .catch(() => {
          this.clearLoading();
          demo.msg('error', '微店激活状态查询失败，请稍后再试');
        });
    },
    microShopAuth(type) {
      // 认证
      if (type === 'ps') {
        // 个人微店
        this.authCodeImg = demo.t2json($config.Base.OtherOptions).qunKeDuo.psAuthCodeImg;
      } else {
        // 企业微店
        this.authCodeImg = demo.t2json($config.Base.OtherOptions).qunKeDuo.esAuthCodeImg;
      }
      this.showScanningCodeImg = true;
    },
    microShopSigning(curMicroShop) {
      // 签约
      this.microShopAuth(curMicroShop);
      this.showScanningCodeImg = true;
    },
    getAuthStatus() {
      this.resetLoading();
      // 获取认证状态(个人及企业)
      let getAuthStatusApi = demo.t2json($config.Base.OtherOptions).qunKeDuo.qkdActivateMsgUrl;
      let ts = new Date().getTime();
      let data = {
        openId: this.openid,
        ts: ts,
        sign: md5(this.signKey + this.openid + ts).toUpperCase()
      }
      demo.$http.post(getAuthStatusApi, data).then(
        res => {
          if (+res.data.status_code === 200 && res.data.data) {
            if ((this.personalShop.certStatus === 4 && res.data.data.user === 1) ||
              (this.enterpriseShop.certStatus === 4 && res.data.data.company === 1)) {
              demo.msg('success', '签约成功');
              this.SET_SHOW({ showSigningDialog: false });
              this.resetLoading();
            }
            if ((this.personalShop.certStatus !== 2 && res.data.data.user === 2) ||
              (this.enterpriseShop.certStatus !== 2 && res.data.data.company === 2)) {
              this.clearLoading();
              demo.msg('error', '审核失败');
              this.showTipDialog = true;
              this.tipDialogTitle = '审核失败';
              this.tipDialogMsg =
                `${res.data.data.user === 2 ? '个人' : '企业'}微店微信认证审核失败，请重新提交资料完成审核。`;
            }
            this.personalShop.certStatus = res.data.data.user;
            this.enterpriseShop.certStatus = res.data.data.company;
            this.personalShop.shortName = res.data.data.user_shortname || '';
            this.enterpriseShop.shortName = res.data.data.company_shortname || '';
            if (this.personalShop.certStatus === 1 || this.enterpriseShop.certStatus === 1) {
              this.microShopStatus = 3;
            }
            if (this.enterpriseShop.certStatus === 1 && this.personalShop.certStatus !== 1) {
              this.curMicroShop = 'es';
            }
          } else {
            this.clearLoading();
            demo.msg('error', res.data.message);
          }
        }
      ).catch(error => {
        this.clearLoading();
        demo.msg('error', '获取微店认证信息失败，请稍后再试');
        console.error(error);
      });
    },
    getBoundStatus() { // 获取微店绑定状态
      this.resetLoading();
      demo.$http.post(this.$rest.msBoundSearch, {openid: this.openid})
        .then(res => {
          if (res.data.code === 200) {
            if (res.data.data) {
              this.personalShop.isBound = res.data.data.personalShopBoundStatus;
              this.personalShop.isUpdatePrice = res.data.data.personalShopUpdatePrice;
              this.enterpriseShop.isBound = res.data.data.enterpriseShopBoundStatus;
              this.enterpriseShop.isUpdatePrice = res.data.data.enterpriseShopUpdatePrice;
              this.getSyncStatus();
            }
          } else {
            this.clearLoading();
            demo.msg('error', res.data.msg);
          }
        })
        .catch(err => {
          this.clearLoading();
          demo.msg('error', err.msg);
        });
    },
    curMicroShopChange(type) {
      this.curMicroShop = type;
      this.showScanningCodeImg = false;
      this.getSyncStatus();
    },
    getSyncStatus() {
      this.resetLoading();
      let prop = this.curMicroShop === 'ps' ? 'personalShop' : 'enterpriseShop';
      if (this[prop].isBound !== 1) { // 微店已绑定时抽最新一次的同步信息
        return;
      }
      let syncStatusApi = demo.t2json($config.Base.OtherOptions).qunKeDuo.qkdGetSyncStatusUrl;
      let ts = new Date().getTime();
      let data = {
        openId: this.openid,
        ts: ts,
        identity: this.curMicroShop === 'ps' ? 1 : 2,
        sign: md5(this.signKey + this.openid + ts).toUpperCase()
      }
      demo.$http.post(syncStatusApi, data).then(
        res => {
          console.log(res);
          if (+res.data.status_code === 200 && res.data.data) {
            let prop = this.curMicroShop === 'ps' ? 'personalShop' : 'enterpriseShop';
            this[prop].lastUpdateTime = res.data.data.time;
            this[prop].title = res.data.data.title;
            this[prop].content = res.data.data.content === res.data.data.title ? '' : res.data.data.content;
          } else if (+res.data.status_code === 0) {
            // 没有最新一次的同步信息
            console.log(res.data.message);
          } else {
            this.clearLoading();
            demo.msg('warning', res.data.message);
          }
        },
        error => {
          this.clearLoading();
          demo.msg('error', '获取微店同步信息失败，请稍后再试');
          console.error(error);
        }
      );
    },
    microShopUpdate(data) { // 修改群客多设置
      this.resetLoading();
      demo.$http
        .post(this.$rest.qkdSettingUpdate, data)
        .then(res => {
          if (res.data.code !== 200) {
            this.clearLoading();
            demo.msg('error', res.data.msg);
          } else {
            demo.msg('success', '修改成功');
          }
        })
        .catch(error => {
          this.clearLoading();
          demo.msg('error', error);
        });
    },
    updatePriceCheck(flag) {
      if (flag) {
        this.showUpdatePriceConfirm = true;
        this.updatePriceConfirmMsg = (this.curMicroShop === 'ps' ? this.personalShop.isUpdatePrice : this.enterpriseShop.isUpdatePrice)
          ? '取消勾选后微店将不再更新已有商品的售价，确定继续吗？' : '勾选后微店将更新已有商品的售价，确定继续吗？';
        return;
      }
      let status = (this.curMicroShop === 'ps' ? this.personalShop.isUpdatePrice : this.enterpriseShop.isUpdatePrice) === 0 ? 1 : 0;
      let data = {
        openid: this.openid,
        type: this.curMicroShop === 'ps' ? 0 : 1,
        status: status
      };
      this.resetLoading();
      demo.$http.post(this.$rest.msPriceUpdate, data)
        .then(res => {
          if (res.data.code === 200) {
            if (this.curMicroShop === 'ps') {
              this.personalShop.isUpdatePrice = status;
            } else {
              this.enterpriseShop.isUpdatePrice = status;
            }
            demo.msg('success', '修改成功');
          } else {
            demo.msg('warning', res.data.msg);
          }
          this.showUpdatePriceConfirm = false;
        })
        .catch(err => {
          this.clearLoading();
          demo.msg('error', err.msg);
          this.showUpdatePriceConfirm = false;
        });
    },
    msBindClick(flag) {
      if (flag) { // 绑定前查询是否有无条码商品
        goodService.getNullCodeCounts(res => {
          console.log(res);
          this.noCodeNum = res[0].num;
          if (this.noCodeNum) { // 无条码商品数量大于0
            this.showAutoMakeCodeConfirm = true;
          } else {
            this.updateBindStatus(true); // 绑定微店
          }
        });
      } else { // 解绑确认框
        this.showBindConfirm = true;
        this.bindConfirmMsg = '解绑后将不再向微店进行商品同步，但不会删除微店的商品，确定继续吗？';
      }
    },
    autoMakeCodes() {
      this.showAutoMakeCodeConfirm = false;
      goodService.generateNullCodes(() => {
        this.updateBindStatus(true);
      }, () => {
        CefSharp.PostMessage("绑定微店前自动生成条码失败");
        this.updateBindStatus(true);
      });
    },
    updateBindStatus(flag) {
      this.resetLoading();
      let data = {
        openid: this.openid,
        type: this.curMicroShop === 'ps' ? 0 : 1,
        status: flag ? 1 : 2
      }
      demo.$http.post(this.$rest.msBoundUpdate, data)
        .then(res => {
          if (res.data.data && res.data.data.subName && res.data.data.boundStatus === 1) { // 当前微店已绑定其他业态
            this.clearLoading();
            this.showTipDialog = true;
            this.tipDialogTitle = '绑定失败';
            this.tipDialogMsg = `当前微店已绑定${this.subNameMap[res.data.data.subName]}，请先解除后再进行当前绑定。`;
            this.getBoundStatus();
          } else if (res.data.data && !res.data.data.updateStatus) {
            this.clearLoading();
            demo.msg('warning', res.data.msg);
            this.showBindConfirm = false;
            this.getBoundStatus();
          } else if (res.data.code === 200 && res.data.data && res.data.data.updateStatus) {
            this[this.curMicroShop === 'ps' ? 'personalShop' : 'enterpriseShop'].isBound = res.data.data.boundStatus;
            if (flag) {
              this.showTipDialog = true;
              this.tipDialogTitle = '绑定成功';
              this.tipDialogMsg = '默认更新微店已有商品的售价，若不想更新已有商品的售价请在当前页根据提示勾选';
              this[this.curMicroShop === 'ps' ? 'personalShop' : 'enterpriseShop'].isUpdatePrice = 1;
            } else {
              this.showBindConfirm = false;
            }
            demo.msg('success', flag ? '绑定成功' : '解绑成功');
          } else {
            this.clearLoading();
            demo.msg('warning', res.data.msg);
            this.showBindConfirm = false;
          }
        })
        .catch(err => {
          this.clearLoading();
          this.showBindConfirm = false;
          demo.msg('error', typeof err === 'string' ? err : '微店更改绑定状态失败，请稍后再试');
        });
    },
    confirmDelData() {
      demo.actionLog(logList.clickClearData);
      if (this.ultimate === null) {
        this.SET_SHOW({isBuySoftware: true});
        return;
      }
      if (!pos.network.isConnected()) {
        demo.msg('warning', '本地网络处于离线状态，请联网使用');
        return;
      }
      // 调用获取云端时间的接口验证是否超过21天
      let localCreateTime = this.storeList[0].createAt;
      var subData = {
        createAt: localCreateTime,
        sysSid: this.sysSid,
        phone: this.sysUid,
        actionFlg: true
      };
      this.reportFormLog(_.cloneDeep(subData), '清空数据');
      demo.$http.post(this.$rest.clearsCheck, subData)
        .then(resp => {
          if (resp.data.code === 200) {
            if (resp.data.data === 0) { // 21天内 允许清空
              this.isconfirmDel = true;
              // this.getClearModule();
            } else if (resp.data.data === 1) {
              this.isconfirmDel = false;
              demo.msg('error', '云端店铺表已被删除!');
            } else { // 超21天
              this.isconfirmDel = false;
              demo.msg('warning', '您注册已超过21天，无法清空数据');
            }
          } else {
            demo.msg('warning', resp.data.msg);
            this.isconfirmDel = false;
          }
        });
    },
    checkedBeforeClear() {
      demo.msg('warning', '清空数据前请先阅读并悉知免责声明并勾选');
    },
    /**
     * 删除所有数据前校验版本、发送验证码
     */
    beforeDelAllData() {
      if (this.checkBoxVal.length === 0) {
        demo.msg('warning', '清空操作至少需要勾选一项数据!');
        return;
      }
      this.showDelAllData = true;
      if (this.isResend) { // 已读完秒
        this.$refs.countDown.reset();
        this.SET_SHOW({ isResend: false });
        this.getverifycode();
        setTimeout(() => {
          this.$refs.countDown.start();
        }, 100);
      } else {
        this.$refs.countDown.start();
      }
    },
    /**
     * 调用c#接口
     */
    callClearData() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      var vscode = this.verifycode.trim();
      if (vscode === '') {
        demo.msg('warning', this.$msg.enter_verification_code);
        return;
      }
      if (vscode.length !== 6) {
        demo.msg('warning', '请输入6位验证码');
        return;
      }
      console.log(this.checkBoxVal, 'checkBoxVal+');
      var params = {
        phone: this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName,
        code: vscode,
        flag: 4
      };
      demo.$http.post(this.$rest.checkVerifyCode, params).then(res => {
        if (res.data.code === 200) {
          if (this.isSyncing) {
            return;
          }
          syncService.clearDataInfo(() => {
            this.$log.info('clearData', this.checkBoxVal);
            settingService.clears(this.checkBoxVal, () => {
              demo.msg('success', '数据清空成功!');
              this.isconfirmDel = false;
              this.showDelAllData = false;
            }, (err) => {
              this.closeDelAllData();
              demo.msg('error', err);
              this.$log.info('clearData', {'error': err});
            });
          }, () => {
            demo.msg('warning', '数据云同步失败,请稍后再试');
          });
        } else {
          demo.msg('error', res.data.msg);
        }
      }).catch(() => {
        demo.msg('warning', '验证码校验繁忙，请稍后再试！');
      });
    },
    // callClearData() {
    //   if (!pos.network.isConnected()) {
    //     demo.msg('warning', '网络连接异常，请检查网络！');
    //     return;
    //   }
    //   if (this.verifycode.trim() === '') {
    //     demo.msg('warning', this.$msg.enter_verification_code);
    //     return;
    //   }
    //   console.log(this.checkBoxVal, 'checkBoxVal+');
    //   let param = {};
    //   let modelArr = _.cloneDeep(this.checkBoxOpt);
    //   modelArr.forEach(module => {
    //     if (this.checkBoxVal.indexOf(module.name) !== -1) {
    //       module.checked = 1;
    //     }
    //   });
    //   param['code'] = this.verifycode;
    //   param['list'] = modelArr;
    //   console.log(param, 'callClearData param');
    //   // clearHistory
    //   this.$log.info('clearData', this.checkBoxVal);
    //   demo.$http
    //     .post(this.$rest.clearHistory, param)
    //     .then(res => {
    //       console.log(res, 'callClearData res');
    //       this.closeDelAllData();
    //       if (res.data.code === '0') {
    //         demo.msg('success', '数据清空成功!');
    //         if (this.isSyncing) {
    //           return;
    //         }
    //         syncservice.clearDataInfoClear(() => {}, () => {});
    //       } else {
    //         demo.msg('error', res.data.msg);
    //       }
    //     })
    //     .catch(err => {
    //       this.closeDelAllData();
    //       demo.msg('error', err);
    //       this.$log.info('clearData', {'error': err});
    //     });
    // },
    closeDelAllData() {
      this.showDelAllData = false;
      this.verifycode = '';
    },
    finished() {
      this.SET_SHOW({ isResend: true });
    },
    getverifycode() { // 获取验证码
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      let that = this;
      var params = {
        phone: this.sysUid,
        systemName: $config.systemName,
        subName: $config.subName,
        flag: 4
      };
      demo.$http
        .post(that.$rest.sendVerifyCode, params, {
          headers: { 'Content-Type': 'application/json' }
        })
        .then(function(res) {
          if (res.data.code !== 200) {
            demo.msg('error', res.data.msg);
          } else {
            demo.msg('success', '验证码已发送，请注意查收');
          }
        })
        .catch(function() {
          demo.msg('warning', '发送验证码繁忙，请稍后再试！');
        });
    },
    resendVerify() {
      var that = this;
      if (this.isResend) { // 重新发送验证码
        this.getverifycode();
        this.SET_SHOW({ isResend: false });
        that.$refs.countDown.reset();
        setTimeout(() => {
          that.$refs.countDown.start();
        }, 100);
      }
    },
    initScalesTable() {
      if (this.hasBarCodeScales) {
        demo.$http.post(this.$rest.initScalesTab)
          .then(res => {
            console.log(this.hasBarCodeScales, '调用一次初始化商品条码秤相关表+')
            if (res.data.code !== 200) {
              console.log('初始化云端条码秤表失败,云端表或已存在');
            // demo.msg('warning', res.data.msg);
            } else {
              // 初始化本地秤表
              settingService.initScale(() => {}, () => { console.log('初始化本地条码秤表失败!'); });
            }
          })
          .catch(() => {
            console.log('初始化云端条码秤表失败,请检查网络连接状态!');
            demo.msg('warning', '初始化云端条码秤表失败,请检查网络连接状态!');
          });
      }
    },
    checkAllChange() {
      this.checkBoxVal = [];
      if (this.isCheckAll) {
        this.checkBoxOpt.forEach(item => {
          this.checkBoxVal.push(item.id);
        });
      }
      this.indeterminateVal = this.checkBoxVal.length > 0 && this.checkBoxVal.length < this.checkBoxOpt.length;
    },
    checkBoxChange() {
      this.checkDisabled = [];
      if (this.checkBoxVal.indexOf(1) !== -1) {
        this.checkDisabled.push(2);
        if (this.checkBoxVal.indexOf(2) === -1) {
          this.checkBoxVal.push(2);
        }
      }
      if (this.checkBoxVal.indexOf(5) !== -1) {
        this.checkDisabled.push(6);
        if (this.checkBoxVal.indexOf(6) === -1) {
          this.checkBoxVal.push(6);
        }
      }
      if (this.checkBoxVal.indexOf(3) !== -1) {
        this.checkDisabled.push(4);
        if (this.checkBoxVal.indexOf(4) === -1) {
          this.checkBoxVal.push(4);
        }
      }
      this.indeterminateVal = this.checkBoxVal.length > 0 && this.checkBoxVal.length < this.checkBoxOpt.length;
      this.isCheckAll = this.checkBoxVal.length === this.checkBoxOpt.length;
    },
    getClearModule() {
      if (!pos.network.isConnected()) {
        demo.msg('warning', '网络连接异常，请检查网络！');
        return;
      }
      demo.$http
        .get(this.$rest.clearModuleList)
        .then(res => {
          if (res.data.code === 200) {
            this.checkBoxOpt = res.data.data;
          } else {
            demo.msg('error', res.data.msg);
          }
        })
        .catch(err => {
          demo.msg('warning', err);
        });
    },
    logout () {
      var that = this;
      if (this.gologining === true) {
        return;
      }
      this.gologining = true;
      setTimeout(() => {
        that.gologining = false;
      }, 3000);
      this.SET_SHOW({ isSyncingLogin: false });
      this.SET_SHOW({ isHome: false });
      this.SET_SHOW({ isHeader: false });
      this.SET_SHOW({ isSetting: false });
      this.SET_SHOW({ isLogin: true });
    },
    updateSettingBeforeDestroy() {
      this.SET_SHOW({ 'username': this.storeinfo.name });
      var set = this.storeinfo.settings === '' ? {} : JSON.parse(this.storeinfo.settings);
      set.iCloud = this.iCloud;
      set.stopCloudList = this.stopCloudList;
      set.setSystem = this.setSystem ? '1' : '0';
      set.default_accts_id = this.defaultAcctsId;
      set.kexian_price_type = this.kexianPriceType;
      set.show_hot_good = this.showHotGood;
      set.defaultType = !this.showHotGood && set.defaultType === '-99' ? '' : set.defaultType;
      set.expiredWarningNotice = this.expiredWarningNotice;
      set.overdueDate = this.overdueDate;
      this.storeinfo.settings = JSON.stringify(set);
      var sto = [];
      sto[0] = this.storeinfo;
      this.SET_SHOW({ storeList: sto });
      if (pos.network.isConnected()) {
        this.upCashier();
      }
      this.saveSetting('setting', () => {
        storeInfoService.update(this.storeinfo, () => {
          this.SET_SHOW({
            isHome: true,
            isSetting: false
          });
        }, () => {
          this.SET_SHOW({
            isHome: true,
            isSetting: false
          });
        });
      });
    },
    // 监听热销分类开关
    async hotStatusChange(value) {
      // 查询当前的默认分类
      const defaultTypeId = await typeService.getDefaultType();
      if (!value && defaultTypeId === '-99') {
        this.shotChangeTips = true;
        return;
      }
      await demo.updateStoreSettings('show_hot_good', value);
    },
    // 过期预警通知是否开启，默认开启
    async expiredWarningNoticeChange(value) {
      await demo.updateStoreSettings('expiredWarningNotice', value);
    },
    // 过期预警通知是否开启，默认开启
    async updateOverdueDate() {
      this.overdueDate = this.modifyOverdueDate;
      this.showOverdueDate = false;
      await demo.updateStoreSettings('overdueDate', this.overdueDate);
    },
    openOverdueDate() {
      this.modifyOverdueDate = this.overdueDate
      this.showOverdueDate = true
    },
    // 过期预警通知 - 修改过期天数
    async overdueDateChange(value) {
      await demo.updateStoreSettings('overdueDate', value);
    },
    // 确认关闭热销分类
    async handleConfirm() {
      await demo.updateStoreSettings('show_hot_good', false);
      await demo.updateStoreSettings('defaultType', '');
      this.shotChangeTips = false;
    }
  },
  computed: mapState({
    pwdType() {
      return this.showFlag ? 'text' : 'password';
    },
    newPwdType() {
      return this.newShowFlag ? 'text' : 'password';
    },
    newPwdElIcon() {
      return this.newShowFlag ? require('@/image/pc_eye_open.png') : require('@/image/pc_eye_close.png');
    },
    pwdElIcon() {
      return this.showFlag ? require('@/image/pc_eye_open.png') : require('@/image/pc_eye_close.png');
    },
    settingHasmoneybox: state => state.show.setting_hasmoneybox,
    isSetting: state => state.show.isSetting,
    hasWeigh: state => state.show.hasWeigh,
    sysUid: state => state.show.sys_uid,
    sysSid: state => state.show.sys_sid,
    storeList: state => state.show.storeList,
    token: state => state.show.token,
    isDetail: state => state.show.isDetail,
    isPay: state => state.show.isPay,
    isGoods: state => state.show.isGoods,
    loginInfo: state => state.show.loginInfo,
    ultimate: state => state.show.ultimate,
    period: state => state.show.period,
    sysEndDate: state => state.show.sysEndDate,
    isSyncing: state => state.show.isSyncing,
    clickInterval: state => state.show.clickInterval,
    devicecode: state => state.show.devicecode,
    qkdSign: state => state.show.qkdSign,
    wxOpenId: state => state.show.wxOpenId,
    showDelStoreData: state => state.show.showDelStoreData,
    isSyncingLogin: state => state.show.isSyncingLogin,
    isResend: state => state.show.isResend,
    selectRow: state => state.show.selectRow,
    msSignType: state => state.show.msSignType,
    settingIndex: state => state.show.settingIndex,
    screenWidth: state => state.show.screenWidth,
    weighSet: state => state.show.weighSet,
    weighTypeList: state => state.show.weighTypeList,
    weighList: state => state.show.weighList,
    kexianList: state => state.show.kexianList,
    weighStatus: state => state.show.weighStatus,
    isAuto: state => state.show.isAuto
  }),
  created () {
    this.version = this.$rest.version;
    this.SET_SHOW({ isPrintSetting: false });
    this.getUltimateInfo();
    this.fromDate = new Date().format('yyyy-MM-dd'); // 开始日期默认当天
    this.toDate = new Date().format('yyyy-MM-dd'); // 结束日期默认当天
  },
  mounted () {
    this.listFormat();
    // this.setLine();
    this.showCatImg = this.isAuto;
    // this.paySetting(1);
    this.uploadData = {
      type: 1,
      sys_uid: this.sysUid,
      sys_sid: this.sysSid
    };
    document.getElementsByClassName('el-tabs__content')[0].setAttribute('style', 'height:' + this.contentHeight + 'px;');
    // 获取价格设置
    var that = this;
    this.SET_SHOW({ isHeader: true });
    this.get_setting();
    this.getCashier();
    if (pos.network.isConnected()) {
      this.network = true;
    }
    // 弹窗上下间距
    this.delBoxMargin = (document.body.clientHeight - 660) / 2 + 'px';
    window.onresize = () => {
      if (that.clearDelBox) {
        clearTimeout(that.clearDelBox);
      }
      that.clearDelBox = setTimeout(() => {
        that.delBoxMargin = (document.body.clientHeight - 660) / 2 + 'px';
      }, 300);
    };
    if (demo.isNullOrTrimEmpty(this.storeList[0].settings)) {
      if ($setting.stock) {
        this.setSystem = $setting.stock !== '0';
      }
    } else {
      if (demo.t2json(this.storeList[0].settings).setSystem === undefined) {
        if ($setting.stock) {
          this.setSystem = $setting.stock !== '0';
        }
      } else {
        this.setSystem = demo.t2json(this.storeList[0].settings).setSystem === '1';
      }
      const expired = demo.t2json(this.storeList[0].settings).expiredWarningNotice
      this.expiredWarningNotice = expired === undefined ? true : expired
      const overdue = demo.t2json(this.storeList[0].settings).overdueDate
      this.overdueDate = overdue === undefined ? 10 : overdue
    }
    this.getDefaultAcctsId();
    this.getMicroShopSetting();
    this.$event.$on('closeSetting', () => {
      console.warn('closeSetting')
      this.tabName = '';
      if (this.hasWeighOff) {
        this.closeScale();
      }
      if (this.showKexian) {
        this.showKexianNumber('', 0);
      }
      this.storeinfo.is_synced = 0;
      setTimeout(() => {
        this.updateSettingBeforeDestroy();
        window.onresize = null;
        this.SET_SHOW({ isResend: true });
      }, 100);
    });
  },
  filters: {
    microStoreStatus(item, isBound) {
      let status;
      switch (item) {
        case 0:
          status = '审核中';
          break;
        case 1:
          status = isBound === 1 ? '已绑定' : '未绑定';
          break;
        case 2:
        case 3:
          status = '未认证';
          break;
        case 4:
          status = '待签约';
          break;
        default:
          status = '';
          break;
      }
      return status;
    },
    timestampToDate(timestamp) {
      return new Date(timestamp).format('yyyy-MM-dd hh:mm:ss');
    }
  },
  beforeDestroy() {
    this.$event.$off('closeSetting');
  }
};
</script>
