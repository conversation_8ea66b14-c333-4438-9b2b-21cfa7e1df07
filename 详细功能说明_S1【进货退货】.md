# 详细功能说明_S1【进货/退货】

**当前任务状态：** ✅ 已完成分析

## 模块概述

S1【进货/退货】模块是ZGZN POS系统的库存管理核心，包含进货管理、退货管理、批次批号管理、供应商管理等功能，负责商品的入库、出库、库存更新和供应链管理。

## 功能详细分析

### 1. 进货管理功能

**文件位置：** `src/page/pc/stock.vue`、`src/common/service/purchaseService.js`

**主要功能点：**

#### 1.1 进货单据创建
- **商品选择：** 从商品库中选择进货商品
- **数量设置：** 设置进货数量和单价
- **供应商选择：** 选择或新增供应商信息
- **折扣设置：** 支持整单折扣和单品折扣
- **备注信息：** 添加进货备注和说明

**技术实现：**
```javascript
// 进货单据插入
insert: async function (params, onSuccess, onFail) {
  let itemsLength = params.purchaseItems.length;
  if (itemsLength === 0) {
    onFail('进货明细不能为空');
    return;
  }
  params.uid = demo.$store.state.show.loginInfo.uid;
  params.billAmt = 0;
  params.disc = +params.disc || 1;
  params.inOut = +params.inOut || 1;
  params.remark = params.remark || null;
  if (demo.isNullOrTrimEmpty(params.code)) {
    let code = await orderService.syncGet({ type: 'JHD' });
    params.code = code[0].code;
  }
  dao.transaction(this.insertAndReturnBackCommon(params), onSuccess, onFail);
}
```

#### 1.2 进货价格管理
- **进价设置：** 设置商品的进货价格
- **加权平均：** 支持进价加权平均计算
- **价格历史：** 记录商品进价变更历史
- **批量定价：** 支持批量设置进货价格

#### 1.3 库存更新机制
- **实时更新：** 进货后实时更新商品库存
- **批次管理：** 支持商品批次和批号管理
- **库存预警：** 库存不足时的预警提醒
- **库存盘点：** 定期库存盘点功能

#### 1.4 进货单据管理
- **单据编号：** 自动生成进货单据编号
- **单据查询：** 按时间、供应商、商品等条件查询
- **单据修改：** 支持进货单据的修改和删除
- **单据打印：** 进货单据的打印功能

### 2. 退货管理功能

**主要功能点：**

#### 2.1 退货单据创建
- **退货商品：** 选择需要退货的商品
- **退货数量：** 设置退货数量和退货价格
- **退货原因：** 记录退货原因和说明
- **供应商确认：** 退货供应商信息确认

**技术实现：**
```javascript
// 退货处理（inOut = 2表示退货）
if (params.inOut !== 1) {
  item.qty = -Math.abs(item.qty); // 退货数量为负数
}
```

#### 2.2 退货库存处理
- **库存扣减：** 退货时自动扣减商品库存
- **成本调整：** 退货后的成本价格调整
- **退货统计：** 退货商品的统计分析
- **退货跟踪：** 退货处理状态跟踪

#### 2.3 退货单据管理
- **退货单号：** 自动生成退货单据编号
- **退货记录：** 完整的退货记录查询
- **退货审核：** 退货单据的审核流程
- **退货报表：** 退货数据的统计报表

### 3. 批次批号管理

**文件位置：** `src/common/service/inventoryService.js`

**主要功能点：**

#### 3.1 批次信息管理
- **批次创建：** 为商品创建批次信息
- **批次编号：** 自动或手动设置批次编号
- **生产日期：** 记录商品生产日期
- **有效期管理：** 商品有效期的管理和预警

#### 3.2 批号跟踪
- **批号生成：** 自动生成商品批号
- **批号查询：** 按批号查询商品信息
- **批号追溯：** 商品批号的全程追溯
- **批号报表：** 批号相关的统计报表

#### 3.3 过期管理
- **过期预警：** 商品即将过期的预警提醒
- **过期处理：** 过期商品的处理流程
- **保质期设置：** 商品保质期的设置和管理
- **过期统计：** 过期商品的统计分析

### 4. 供应商管理功能

**文件位置：** `src/components/pc_supplier_manage.vue`、`src/common/service/supplierService.js`

**主要功能点：**

#### 4.1 供应商信息管理
- **供应商档案：** 供应商基本信息管理
- **联系方式：** 供应商联系人和联系方式
- **地址信息：** 供应商地址和配送信息
- **合作状态：** 供应商合作状态管理

**技术实现：**
```javascript
// 供应商查询
searchSuppliers: function (data, onSuccess, onFail) {
  if (data != null) {
    data.wheres = 'where 1=1 ';
    data.limit = data.limit || 10;
    data.offset = data.offset || 0;
    if (data.name != null && data.name.toString().trim() !== '') {
      data.wheres +=
        "and (name like '%" + data.name + "%' or contacter like '%" + 
        data.name + "%' or mobile like '%" + data.name + "%') ";
    }
    if (+data.status === 0 || +data.status === 1) {
      data.wheres += "and is_deleted = '" + data.status + "' ";
    }
  }
  dao.exec(sqlApi.searchSuppliers.format(data), onSuccess, onFail);
}
```

#### 4.2 供应商选择功能
- **供应商列表：** 显示所有可用供应商
- **供应商搜索：** 按名称、联系人、手机号搜索
- **供应商筛选：** 按状态筛选供应商
- **供应商详情：** 查看供应商详细信息

#### 4.3 供应商新增编辑
- **新增供应商：** 添加新的供应商信息
- **编辑供应商：** 修改现有供应商信息
- **供应商验证：** 供应商信息的有效性验证
- **供应商状态：** 启用/禁用供应商状态

#### 4.4 商品供应商关联
- **关联设置：** 将商品与供应商进行关联
- **批量关联：** 批量设置商品的供应商
- **关联查询：** 查询商品的供应商信息
- **关联历史：** 商品供应商变更历史

**技术实现：**
```javascript
// 批量设置商品供应商
batchUpdateGoodsSupplier(data, onSuccess, onFail) {
  data.uid = $userinfo.uid;
  let fingerprintList = data.fingerprintList;
  let supplierFingerprint = data.supplierFingerprint;
  let sql = '';
  if (fingerprintList.length > 0) {
    if (demo.isNullOrTrimEmpty(supplierFingerprint)) {
      // 清空供应商关联
      sql = sqlApi.clearGoodsSupplier.format({
        fingerprints: fingerprintList.join("','")
      });
    } else {
      // 设置供应商关联
      sql = sqlApi.updateGoodsSupplier.format({
        supplierFingerprint: supplierFingerprint,
        fingerprints: fingerprintList.join("','")
      });
    }
    dao.exec(sql, onSuccess, onFail);
  }
}
```

### 5. 库存盘点功能

**文件位置：** `src/common/service/inventoryService.js`

**主要功能点：**

#### 5.1 盘点单据创建
- **盘点计划：** 创建库存盘点计划
- **盘点范围：** 设置盘点商品范围
- **盘点人员：** 指定盘点操作人员
- **盘点时间：** 设置盘点时间和周期

#### 5.2 盘点执行过程
- **账面库存：** 显示系统账面库存数量
- **实际库存：** 录入实际盘点库存数量
- **差异计算：** 自动计算库存差异
- **差异处理：** 库存差异的处理和调整

**技术实现：**
```javascript
// 库存盘点插入
insert: async function (params, onSuccess, onFail) {
  let itemsLength = params.inventoryItems.length;
  if (itemsLength === 0) {
    onFail('盘点明细不能为空');
    return;
  }
  
  params.uid = demo.$store.state.show.loginInfo.uid;
  params.accountQty = 0;  // 账面数量
  params.actualQty = 0;   // 实际数量
  params.diffQty = 0;     // 差异数量
  params.diffAmt = 0;     // 差异金额
  
  // 更新库存和进价
  if (+params.updateStock === 1) {
    sql += updateCurStockSql;
  }
  if (+params.updatePurPrice === 1) {
    sql += updatePurPriceSql;
  }
  
  dao.transaction(sql, onSuccess, onFail);
}
```

#### 5.3 盘点结果处理
- **库存更新：** 根据盘点结果更新库存
- **进价更新：** 根据盘点结果更新进价
- **盘点报告：** 生成盘点结果报告
- **差异分析：** 库存差异的原因分析

### 6. 进货退货查询统计

**主要功能点：**

#### 6.1 进货查询功能
- **时间查询：** 按时间范围查询进货记录
- **供应商查询：** 按供应商查询进货记录
- **商品查询：** 按商品名称查询进货记录
- **单据查询：** 按单据编号查询进货记录

**技术实现：**
```javascript
// 进货单检索
search: function (data, onSuccess, onFail) {
  var wheresTotal = this.getSearchWheresTotal(data);
  const wheresDayTotal = wheresTotal + ' group by purchases.opt_date order by purchases.opt_date desc ';
  const wheresDayDetails = wheresTotal + ' order by purchases.create_at desc, purchases.id desc';
  
  // 支持多种查询条件：
  // from/to: 日期范围
  // strcmp: 单据名称模糊检索
  // company_id: 供应商精确检索
  // acct_id: 收款账户
  // type: 单据类型（1:进货 2:退货）
}
```

#### 6.2 退货查询功能
- **退货记录：** 查询所有退货记录
- **退货统计：** 退货数量和金额统计
- **退货分析：** 退货原因和趋势分析
- **退货报表：** 生成退货统计报表

#### 6.3 库存变动查询
- **变动记录：** 查询库存变动记录
- **变动类型：** 按变动类型筛选记录
- **变动统计：** 库存变动的统计分析
- **变动追溯：** 库存变动的全程追溯

### 7. 数据同步和备份

**主要功能点：**

#### 7.1 数据同步
- **云端同步：** 进货退货数据的云端同步
- **多端同步：** PC端和移动端数据同步
- **实时同步：** 数据的实时同步更新
- **冲突处理：** 数据同步冲突的处理

#### 7.2 数据备份
- **自动备份：** 定期自动备份进货退货数据
- **手动备份：** 用户手动触发数据备份
- **备份恢复：** 数据备份的恢复功能
- **备份验证：** 备份数据的完整性验证

## 技术架构

### 1. 数据库设计
- **purchases表：** 进货主表，存储进货单据信息
- **purchase_items表：** 进货明细表，存储进货商品明细
- **inventories表：** 库存变动表，记录库存变动
- **inventory_items表：** 库存变动明细表
- **suppliers表：** 供应商信息表

### 2. 业务逻辑层
- **purchaseService：** 进货业务逻辑服务
- **inventoryService：** 库存管理业务逻辑服务
- **supplierService：** 供应商管理业务逻辑服务
- **goodsSupplierService：** 商品供应商关联服务

### 3. 数据访问层
- **DAO层：** 数据访问对象，封装数据库操作
- **SQL模板：** 预定义的SQL查询模板
- **事务管理：** 数据库事务的统一管理
- **连接池：** 数据库连接池管理

### 4. 前端组件架构
- **stock.vue：** 进货退货主页面组件
- **pc_supplier_manage.vue：** 供应商管理组件
- **set_goods_supplier.vue：** 商品供应商设置组件
- **SupplierInformation.vue：** 供应商信息展示组件

## 业务流程

### 1. 进货流程
1. **商品选择：** 从商品库中选择进货商品
2. **供应商选择：** 选择或新增供应商
3. **数量价格：** 设置进货数量和价格
4. **折扣设置：** 设置整单或单品折扣
5. **单据生成：** 生成进货单据
6. **库存更新：** 更新商品库存
7. **成本计算：** 计算商品成本价格

### 2. 退货流程
1. **退货商品：** 选择需要退货的商品
2. **退货数量：** 设置退货数量
3. **退货原因：** 记录退货原因
4. **单据生成：** 生成退货单据
5. **库存扣减：** 扣减商品库存
6. **成本调整：** 调整商品成本

### 3. 盘点流程
1. **盘点计划：** 制定盘点计划
2. **盘点执行：** 执行实际盘点
3. **差异分析：** 分析库存差异
4. **结果确认：** 确认盘点结果
5. **库存调整：** 调整库存数据
6. **报告生成：** 生成盘点报告

## 权限控制

### 1. 功能权限
- **进货权限：** 控制用户是否可以进行进货操作
- **退货权限：** 控制用户是否可以进行退货操作
- **盘点权限：** 控制用户是否可以进行库存盘点
- **供应商权限：** 控制用户是否可以管理供应商

### 2. 数据权限
- **价格权限：** 控制用户是否可以查看进货价格
- **成本权限：** 控制用户是否可以查看商品成本
- **利润权限：** 控制用户是否可以查看利润信息
- **统计权限：** 控制用户是否可以查看统计数据

### 3. 操作权限
- **修改权限：** 控制用户是否可以修改单据
- **删除权限：** 控制用户是否可以删除单据
- **审核权限：** 控制用户是否可以审核单据
- **打印权限：** 控制用户是否可以打印单据

## 数据安全

### 1. 数据完整性
- **事务处理：** 使用数据库事务保证数据一致性
- **约束检查：** 数据库约束保证数据完整性
- **数据验证：** 业务层数据有效性验证
- **错误处理：** 完善的错误处理机制

### 2. 数据备份
- **定期备份：** 定期自动备份重要数据
- **增量备份：** 支持增量数据备份
- **备份验证：** 验证备份数据的完整性
- **快速恢复：** 支持快速数据恢复

### 3. 操作审计
- **操作日志：** 记录所有关键操作
- **用户追踪：** 追踪操作用户信息
- **时间记录：** 记录操作时间戳
- **数据变更：** 记录数据变更历史

## 性能优化

### 1. 查询优化
- **索引优化：** 数据库索引的合理设计
- **查询优化：** SQL查询语句的优化
- **分页查询：** 大数据量的分页处理
- **缓存机制：** 查询结果的缓存机制

### 2. 批量处理
- **批量插入：** 支持批量数据插入
- **批量更新：** 支持批量数据更新
- **批量删除：** 支持批量数据删除
- **批量导入：** 支持批量数据导入

### 3. 内存管理
- **内存优化：** 合理的内存使用
- **垃圾回收：** 及时释放无用对象
- **缓存控制：** 控制缓存大小
- **资源管理：** 合理管理系统资源

## 问题和改进建议

### 1. 当前问题
- **界面复杂度：** 进货退货界面功能过多，操作复杂
- **性能问题：** 大量数据时的查询性能问题
- **用户体验：** 部分操作流程不够直观

### 2. 改进建议
- **界面简化：** 简化操作界面，提升用户体验
- **性能优化：** 优化数据库查询和索引
- **流程优化：** 优化业务流程，减少操作步骤
- **功能增强：** 增加更多实用功能

## 3.0版本重构建议

### 1. 架构升级
- **微服务化：** 将进货退货功能微服务化
- **API标准化：** 统一的API接口标准
- **数据库优化：** 优化数据库设计和性能
- **缓存策略：** 完善的缓存策略

### 2. 功能增强
- **智能推荐：** 基于历史数据的智能推荐
- **自动化处理：** 增加更多自动化处理功能
- **移动端支持：** 完善的移动端支持
- **数据分析：** 更强大的数据分析功能

### 3. 技术现代化
- **Vue 3：** 升级到Vue 3.x版本
- **TypeScript：** 引入TypeScript提高代码质量
- **现代化UI：** 使用现代化的UI框架
- **云原生：** 支持云原生部署
