<style lang="less" scoped>
.re_deposit_record_container {
  /deep/ .el-input--suffix .el-input__inner {
    border-radius: 50px;
    height: 44px;
    font-size: 16px;
  }
  /deep/ .el-select .el-input.is-focus .el-input__inner {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-range-editor.el-input__inner {
    border-radius: 50px;
    height: 44px;
  }
  /deep/ .el-date-editor .el-range__icon {
    display: none;
    height: 44px;
  }
  /deep/ .el-range-editor .el-range-input {
    margin-left: 12px;
    color: rgba(177, 195, 205, 100);
  }
  /deep/ .el-range-editor.is-active, .el-range-editor.is-active:hover {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-date-editor .el-range-separator {
    color: rgba(177, 195, 205, 100);
    height: 85%;
  }
  /deep/ .el-date-table td.today span {
    color: @themeBackGroundColor !important;
  }
  /deep/ .el-table__row > td {
    border: none;
  }
  /deep/ .el-table::before {
    height: 0px;
  }
  /deep/ .el-table th, .el-table tr {
    height: 50px;
    font-size: 16px;
    background: #F5F7FA;
  }
  /deep/ .el-table__row > td {
    height: 50px;
    font-size: 16px;
  }
  /deep/ .el-input__inner:focus {
    border-color: @themeBackGroundColor;
  }
  /deep/ .el-table__footer-wrapper {
    font-weight: bold;
    font-size: 16px;
  }
  /deep/ .el-table th > .cell {
    padding-left: 30px;
  }
  /deep/ .el-table__row > td {
    padding-left: 20px;
  }
  /deep/ .el-table td, .el-table th.is-leaf {
    padding-left: 20px;
  }
  /deep/ .el-input__inner {
    border-radius: 50px;
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-webkit-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input::-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-moz-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  /deep/ input:-ms-input-placeholder{
    color: rgba(177, 195, 205, 100);
  }
  background: #F5F8FB;
  .re_top{
    height: 64px;
    background: #F5F8FB;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    .re_top_left_container{
      display: flex;
      align-items: center;
      .re_top_left{
        display: flex;
        align-items: center;
        .re_top_left_title{
          color: @themeFontColor;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .re_top_right{
      display: flex;
      align-items: center;
      .re_top_left_title{
        color: @themeFontColor;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .re_table_container{
    height: calc(100vh - 124px);
    border: 1px solid #E3E6EB;
    border-radius: 4px;
    margin: 0 10px;
    background: #F5F8FB;
    display: flex;
    flex-direction: column;
    .re_table_bottom{
      height: 54px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 16px;
      padding: 0 30px;
      background: white;
      span{
        color: @themeBackGroundColor;
      }
    }
  }
}
.written_off {
  color: #B2C3CD;
}
.pc_report1 {
  width: 18px;
  height: 18px;
  margin-top: 13px;
  margin-left: 5px;
  float: left;
  cursor: pointer;
}
.pc_report2 {
  width: 300px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  background: #fff;
}
.pc_report2 input {
  width: 230px;
  height: 25px;
  line-height: 28px;
  margin-left: 20px;
  font-size: 16px;
  margin-top: 9px;
  border: none;
  color: @themeFontColor;
  float: left;
}
.el-table .ascending .sort-caret.ascending{
  border-bottom-color: @themeBackGroundColor;
}
.el-table .descending .sort-caret.descending{
  border-top-color: @themeBackGroundColor;
}
.input_goods_name .el-input--prefix .el-input__inner {
  padding-left: 30px;
  background: white;
}

.el-date-editor.el-input, .el-date-editor.el-input__inner {
  background: white;
}

.el-input__inner:focus {
  border-color: @themeBackGroundColor;
}

.el-range-editor.is-active, .el-range-editor.is-active:hover {
  border-color: @themeBackGroundColor;
}

.el-date-table td.start-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.end-date span {
  background-color: @themeBackGroundColor;
}

.el-date-table td.today span {
  color: @themeBackGroundColor;
  font-weight: 700;
}

.el-date-table td.available:hover {
  color: @themeBackGroundColor;
}

.pc_btn1 {
  cursor: pointer;
  width: 130px;height: 44px;margin-left: 50px;line-height: 40px;border-radius: 22px;text-align: center;
  color: #FFF;font-size: 18px;background: @themeBackGroundColor;float: left;border: 0px;outline: none;font-weight: bold;
}
#butColor {
  color:@themeBackGroundColor;
}
.report2 {
  border-color:  @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
</style>
<template>
  <div>
    <v-AddEditSupplier></v-AddEditSupplier>
    <!-- 供应商管理 -->
    <div class="re_deposit_record_container">
      <div>
        <div class="re_top">
          <div class="re_top_left_container">
            <div style='float: left;'>
              <div
                class='pc_report2'
                :class="inputing_keyword ? 'report2' : 'isInPutIng1'"
              >
                <input
                  @focus='inputing_keyword = true'
                  @blur='inputing_keyword = false'
                  type='text'
                  placeholder='供应商名称/联系人/电话'
                  v-model='keywordMaker'
                  id='maker_keyword_report'
                  @compositionstart='pinyin = true'
                  @compositionend='pinyin = false'
                  @input="keywordMaker = keywordMaker.replace(/[$']/g, '')"
                  maxlength="60"
                />
                <img
                  alt=""
                  class='pc_report1'
                  v-show="keywordMaker != ''"
                  @click="focusInput('maker_keyword_report')"
                  src='../image/pc_clear_input.png'
                />
              </div>
            </div>
            <div class="re_top_left">
              <el-select
                v-model="makerStatus"
                placeholder="请选择"
                style="margin-left:16px;width:160px;"
              >
                <el-option
                  v-for="item in status_list"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="re_top_right">
            <button
              class="pc_btn1"
              @click="addSupplier"
              >新增供应商</button>
          </div>
        </div>
        <div class="re_table_container">
          <el-table
            ref="multipleTable"
            :height="suppliers_height"
            stripe
            :data="makers_data"
            style="font-size: 16px;margin-top: 5px;color: #567485;width: 100%;"
            :style="$t('image.homeImage.tableFont')"
            :cell-style="cellAlignStyle"
            :header-cell-style="headerStyle"
            tooltip-effect="dark"
            @selection-change="handleSelectionChange"
            id="stockRecordTab">
            <el-table-column
              prop="name"
              maxwidth="300px"
              :show-overflow-tooltip="true"
              label="供应商名称">
            </el-table-column>
            <el-table-column
              prop="contacter"
              label="联系人">
            </el-table-column>
            <el-table-column
              prop="mobile"
              label="联系电话"
              min-width="130px">
            </el-table-column>
            <el-table-column
              prop="addr"
              :show-overflow-tooltip="true"
              label="地址">
            </el-table-column>
            <el-table-column
              prop="remark"
              :show-overflow-tooltip="true"
              label="备注">
            </el-table-column>
            <el-table-column
              prop="isDeleted"
              label="状态">
              <template slot-scope="{row}">
                <span :class="[row.isDeleted === 0 ? '' : 'written_off']">{{ row.isDeleted === 0 ? '启用' : '禁用' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="fingerprint"
              label="操作">
              <template slot-scope="{row}">
                <a href="#"
                style="text-decoration:none;"
                @click="chooseOneSupplier(row)"><span id="butColor">编辑</span></a>
              </template>
            </el-table-column>
          </el-table>
          <div class="re_table_bottom">
            <div>
              共<span>{{this.makers_total}}</span>个供应商
            </div>
            <div>
              <el-pagination
                layout="prev, pager, next"
                :total="makers_total"
                @current-change="suppliersChange"
                :current-page="suppliers_pagenum"
                :page-size="suppliers_pageSize"
              ></el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { Message } from 'element-ui';

export default {
  data() {
    return {
      pinyin: false,
      inputing_keyword: false,
      makerStatus: '0',
      status_list: [
        {
          id: '2',
          name: '全部'
        },
        {
          id: '0',
          name: '启用'
        },
        {
          id: '1',
          name: '禁用'
        }
      ],
      keywordMaker: '',
      suppliers_pagenum: 1,
      suppliers_height: 0,
      suppliers_pageSize: 0,
      makers_data: [],
      multipleSelection: [],
      makersnum: '',
      goods_head: {
        salesnumber: 0,
        totalsalesprice: 0,
        totalstockprice: 0,
        money: 0,
        receipt: 0
      },
      makers_total: 0
    };
  },
  components: {
    [Message.name]: Message
  },
  created() {
    this.listenResize();
    window.addEventListener('resize', this.listenResize);
    this.suppliers_height = $(window).height() - 310;
    this.suppliers_pageSize = parseInt(($(window).height() - 310) / 43);
    this.searchSuppliersMain();
  },
  mounted() {
    this.listenResize();
    setTimeout(() => {
      $('#maker_keyword_report').focus();
    }, 0);
  },
  watch: {
    keywordMaker() {
      var that = this;
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.searchSuppliersMain();
      }, that.delayedTime);
    },
    showAddEditSupplier() {
      var that = this;
      if (!that.showAddEditSupplier) {
        that.searchSuppliers();
      }
    },
    makerStatus() {
      var that = this;
      that.searchSuppliersMain();
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    focusInput(sid) {
      this.keywordMaker = '';
      $('#' + sid).focus();
    },
    cellAlignStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    headerStyle(column) {
      if (column.columnIndex >= 1 && column.columnIndex <= 6) {
        return 'text-align: center';
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    suppliersChange(val) {
      this.suppliers_pagenum = val;
      this.searchSuppliers();
    },
    // contentFmt(row, column, cellValue) {
    //   if (!cellValue) {
    //     return '';
    //   }
    //   if (cellValue.length > 50) {
    //     return cellValue.slice(0, 50) + '...';
    //   }
    //   return cellValue;
    // },
    /**
     * 供应商查询
     */
    searchSuppliersMain() {
      if (this.pinyin) {
        return;
      }
      this.suppliers_pagenum = 1;
      this.searchSuppliers();
    },
    /**
     * 供应商查询
     */
    searchSuppliers() {
      let sub_data = {
        'systemName': $config.systemName,
        'name': this.keywordMaker.replace(/'/g, "'").replace(/;/g, '；'),
        'status': this.makerStatus,
        'limit': this.suppliers_pageSize,
        'offset': (this.suppliers_pagenum - 1) * this.suppliers_pageSize
      };
      supplierService.searchSuppliers(sub_data, res => {
        this.makers_data = demo.t2json(res);
      });
      supplierService.getSupplierCount(sub_data, res => {
        this.makers_total = Number(demo.t2json(res)[0].cnt);
      });
    },
    towNumber(val) {
      return val ? Number(val).toFixed(2) : 0;
    },
    addSupplier () {
      this.SET_SHOW({ showAddEditSupplier: true });
      this.SET_SHOW({ suppliersDetail: [] });
      this.SET_SHOW({ returnSuppliersDetail: [] });
    },
    // 选择的单条供应商
    chooseOneSupplier(row) {
      this.SET_SHOW({ suppliersDetail: _.cloneDeep(row) });
      console.log(_.cloneDeep(row), 'cloneDeep');
      this.SET_SHOW({ showAddEditSupplier: true });
    },
    listenResize() {
      // 浏览器高度$(window).height()
      let that = this;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        that.table_height = $(window).height() - 460;
      }, that.delayedTime);
    }
  },
  computed: mapState({
    sysUid: state => state.show.sys_uid,
    delayedTime: state => state.show.delayedTime,
    showAddEditSupplier: state => state.show.showAddEditSupplier
  }),
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
  }
};
</script>
