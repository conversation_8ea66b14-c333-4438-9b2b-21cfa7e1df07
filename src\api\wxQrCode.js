import rest from '@/config/rest';
// 获取微信售后图片
export function getWxQrCode(param) {
  return new Promise((resolve, reject) => {
    demo.$http
      .get(rest.getWeComUrl, {params: param}, {
        maxContentLength: Infinity,
        timeout: 60000
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
// 获取客服二维码
export function getServiceImgUrl() {
  return new Promise(resolve => {
    settingService.get({ key: 'serviceImgUrl' }, urls => {
      if (urls[0]) {
        resolve(urls[0].value || '')
      } else {
        resolve('')
      }
    })
  })
}
