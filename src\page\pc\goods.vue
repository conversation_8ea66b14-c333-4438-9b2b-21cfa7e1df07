<style lang="less">
.supplier-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.supplier-title {
  color:#537286;
  border-bottom: 1px solid #537286;
  cursor: pointer;
}
.pc_god41 .el-input-number {
  line-height: 42px;
}
.pc_god14 .el-alert {
  padding-right: 5px;
  padding-left: 7px;
}
.pc_god33 .is-scrolling-none {
  background: #F5F7FA;
}
.pc_god33 .el-checkbox {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}
.pc_god .el-select .el-input.is-focus .el-input__inner {
  border-color: @themeBackGroundColor;
  background: #ffffff
}
.pc_god .el-select .el-input__inner:focus {
  border-color: @themeBackGroundColor;
}
.pc_god .el-input__inner {
  padding: 0 20px;
  color: @themeFontColor;
}
.pc_god33 .el-table-column--selection .cell {
  padding-right: 0;
  padding-left: 0;
}
.pc_god33 .el-table th > .cell {
  padding: 0px;
  height: 50px;
  line-height: 50px;
  background: #F5F7FA;
}
.pc_god33 .el-table td > .cell {
  padding: 0px;
  height: 66px;
  line-height: 66px;
}
.pc_god33 .el-table td, .pc_god33 .el-table th {
  padding: 0px;
}
.pc_god_height1 {
  height: calc(100% - 154px);
}
.pc_god_height2 {
  height: calc(100% - 112px);
}
.el-checkbox__inner {
  width: 24px;
  height: 24px;
  border: none;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox1.png);
}
.el-checkbox__inner::after {
  border: none;
}
.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 23px;
}
.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fff;
  border-color: #fff;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox2.png);
}
.el-pager li.active {
  color: #fff !important;
  background: @themeBackGroundColor;
  text-align: center;
  border-radius: 3px;
}
.el-pager li {
  font-size: 14px;
  color: #344755;
  font-weight: normal;
}
.el-pager li:hover {
  color: @themeBackGroundColor;
}
.el-table td,
.el-table th.is-leaf {
  border: none;
}
.el-table thead {
  color: @themeFontColor;
}
.el-checkbox__inner.is-focus {
  border-color: @themeBackGroundColor;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  height: 24px;
  top: 0;
  transform: none;
  -webkit-transform: none;
  background-image: url(../../image/zgzn-pos/pc_goods_checkbox3.png) !important;
}
.pc_god {
  height: 100%;
}
.pc_god1 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  color: @themeFontColor;
}
.pc_god11 {
  width: 600px;
  min-height: 389px;
  background: #fff;
  border-radius: 2px;
  margin: 0 auto;
  margin-top: 90px;
  padding-bottom: 10px;
  border-radius: 6px;
}
.pc_god12 {
  width: 93%;
  height: 60px;
  border-bottom: 1px solid rgba(227, 230, 235, 100);;
  font-size: 18px;
  margin-left: 20px;
  line-height: 60px;
  text-align: left;
  font-weight:bold;
  color: @themeFontColor;
}
.pc_god13 {
  float: right;
  font-size: xx-large;
  color: rgba(178, 195, 205, 100);
  margin-top: -4px;
  cursor:pointer;
  margin-right: -4px;
}
.pc_god14 {
  width: 93%;
  margin-left: 20px;
  margin-top: 16px;
  height: 46px;
  border: 1px solid rgba(249, 213, 161, 100);
  border-radius: 2px;
  .import_guidance_text {
    color: #ff8484;
    cursor: pointer;
    font-size: 16px;
    text-decoration: underline;
    i {
      font-size: 18px;
    }
  }
}
.el-alert--warning.is-light {
  height: 44px;
}
.el-alert__title {
  font-size: 16px;
  line-height: 24px;
}
.pc_god15 {
  width: 93%;
  margin-left: 20px;
  margin-top: 20px;
  padding-top: 40px;
  font-size: 16px;
  a {
    span {
      color: @themeBackGroundColor;
      cursor:pointer;
      text-decoration: none;
      text-decoration-line:underline;
    }
  }
}
.pc_god16 {
  width: 93%;
  margin-left: 20px;
  margin-top: 30px;
  height: 57px;
  line-height: 18px;
  background-color: rgba(249, 251, 251, 100);
  border: 1px solid rgba(227, 230, 235, 100);
}
.pc_god16 div {
  display: flex;
  margin-top: 15px;
  line-height: 23px;
}
.pc_god16 span {
  color: @themeFontColor;
  margin-left: 10px;
  font-size: 16px;
}
.pc_god16 img {
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin-left: 15px;
}
.pc_god17 {
  width: 93%;
  margin-left: 38px;
  margin-top: 30px;
  margin-bottom: 20px;
  text-align:justify;
}
.pc_god18 {
  display: inline-flex;
  width: 120px;
  height: 44px;
  border-radius: 4px;
  margin-left: 14px;
  border: 1px solid  @themeBackGroundColor;
  background: @themeBackGroundColor;
  cursor: pointer;
}
.pc_god19 {
 margin-left: 8px;
 color: rgba(178, 195, 205, 100);
 font-size: 16px;
}
.downloadfaildata:hover {
  text-decoration: underline;
}
.pc_god2 {
  float: left;
  margin-left: 10px;
}
.pc_god21 {
  padding: 0 10px;
  padding-top: 0.5rem;
}
.pc_god22 {
  right: 20px;
  float: right;
  margin-right: 10px;
}
.pc_god23 {
  width: 110px;
  height: 44px;
  background: @themeBackGroundColor;
  text-align: center;
  line-height: 44px;
  border-radius: 22px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  float: left;
  margin-left: 10px;
  margin-top: 10px;
  cursor: pointer;
}
.pc_god29 {
  width: 240px;
  height: 44px;
  border: 1px solid #e3e6eb;
  border-radius: 24px;
  margin-top: 10px;
  margin-left: 10px;
  background: #fff;
}
.pc_god29 input {
  width: 200px;
  height: 28px;
  line-height: 28px;
  margin-left: 8px;
  font-size: 16px;
  margin-top: 8px;
  padding-right: 5px;
  border: none;
  color: @themeFontColor;
  float: left;
  input::-webkit-input-placeholder {
    /* WebKit browsers */
    color: @text;
  }
}
.pc_god_129 {
  width: 440px;
  height: 41px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  background: #fff;
}
.pc_god_129 input {
  width: 380px;
  height: 28px;
  line-height: 28px;
  font-size: 16px;
  margin: 6px 14px 0 10px;
  border: none;
  color: #567485;
  float: left;
}
.pc_god3 {
  float: left;
  margin-left: 14px;
  margin-top: 8px;
}
.pc_god32 {
  width: 18px;
  height: 18px;
  margin-top: 12px;
  float: left;
  cursor: pointer;
}
.pc_god33 {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;
  background: #f5f8fb;
}
.pc_god34 {
  width: 100%;
  margin-top: -40px;
  z-index: 2100;
  overflow: hidden;
  background: #fff;
}
.pc_god35 {
  width: 560px;
  margin: 0 auto;
  margin-top: 160px;
  color: #969696;
  text-align: center;
  font-size: 22px;
}
.pc_god35 img {
  width: 190px;
  height: 190px;
  margin: 0 auto;
  margin-top: 0px;
}
.pc_god36 {
  width: 110px;
  position: fixed;
  right: -2px;
  top: 207px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  height: calc(100% - 278px);
  z-index: 2;
  overflow-y: auto;
  overflow-x: hidden;
}
.pc_god37 {
  height: 49px;
  background: linear-gradient(180deg, #69d6fe, #16aaff);
  line-height: 49px;
  color: #fff;
}
.pc_god38 {
  height: 46px;
  line-height: 47px;
  border-radius: 5px;
  color: #fff;
  font-weight: bold;
  width: 100%;
  overflow: hidden;
}
.pc_god46 {
  position: fixed;
  bottom: 0;
  height: 75px;
  color: @themeFontColor;
  font-weight: bold;
  font-size: 16px;
  width: 100%;
  z-index: 1;
}
.pc_god47 {
  left: 28px;
  position: absolute;
  top: 22px;
  font-weight: 400;
  span {
    color: @themeBackGroundColor;
  }
}
.pc_god48 {
  position: absolute;
  right: 110px;
  top: 19px;
}
.pc_god49 {
  max-height: calc(100% - 49px);
  overflow: auto;
}
.pc_god49 div {
  border-bottom: 1px solid #fff;
}
.pc_god5 {
  overflow: hidden;
  position: relative;
  background: #f5f8fb;
  input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: @text;
  }
}
.pc_god51 {
  float: left;
  cursor: pointer;
  font-size: 16px;
  color: @themeFontColor;
  margin-left: 25px;
}
.pc_god51 img {
  float: left;
  margin-top: 19px;
}
.pc_god51 div {
  float: left;
  line-height: 62px;
  margin-left: 8px;
  font-weight: bold;
}
.pc_god52 {
  position: relative;background-image: linear-gradient(180deg, #f1d3af, #cfa26b);width: 222px;
  height: 56px;margin: 0 auto;border-radius: 28px;color: #FFF;font-size: 20px;font-weight: normal;cursor: pointer;
}
/*清空原来的多选框样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item:after{
  content:"";
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected:after{
  content:"";
}
/*参考el-checkbox实现checkbox样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item .checkbox{
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  transition: border-color .25s cubic-bezier(.71,-.46,.29,1.46),background-color .25s cubic-bezier(.71,-.46,.29,1.46);
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox{
  background-color:@themeBackGroundColor;
  border-color:@themeBackGroundColor;
}
.pc_god5 .el-input__inner{
  border-radius: 22px;
  height: 44px;
  font-size: 16px;
  font-weight: 400;
}
/*参考el-select多选对号样式实现checkbox中对号的样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox::after{
  position: absolute;
  top: -10px;
  content: "\2714";
  font-size: 14px;
  font-weight: 700;
  /* -webkit-font-smoothing: antialiased; */
  color:#fff;
}
/*设置置灰内容样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox{
  background-color:#f2f6fc;
  border-color:#dcdfe6;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox::after{
  color:#c0c4cc;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .label-name-box{
  color:@themeBackGroundColor;
  font-weight:bold;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .label-name-box{
  color:@themeBackGroundColor;
  font-weight:bold;
}
.el-select .el-select__tags .el-tag.el-tag--info{
  background-color: @themeBackGroundColor;
  color: #fff;
  border-radius: 22px;
}
.el-select .el-select__tags .el-tag.el-tag--info .el-tag__close{
  background-color: #fff;
  color: @themeBackGroundColor;
}
.el-select__tags {
  display: none;
}
/* .el-select .el-select__tags .el-tag.el-tag--info.el-tag--small.el-tag--light .el-select__tags-text */
.pc_pdipt {
  width: 110px;
  height: 44px;
  text-align: center;
  line-height: 42px;
  border-radius: 22px;
  border: 1px solid @themeBackGroundColor;
  font-size: 18px;
  font-weight: 700;
  color:@themeBackGroundColor;
  float: left;
  margin-top: 10px;
  cursor: pointer;
}
.addunsearched-btn {
  background-color: @themeBackGroundColor;
  border: 0px;
  border-radius: 22px;
  color: #fff;
  width: 80px;
}
.addunsearched-btn:active{
  margin-top: 2px;
  border: 0px;
}
.pc_god53 {
  background: #f5f8fb;
  overflow: hidden;
}
.pc_god54 {
  background: #FFFFFF;
  border: 1px solid #E3E6EB;
  box-sizing: border-box;
  border-radius: 22px;
  cursor: pointer;
  float: left;
  margin-left: 10px;
  margin-bottom: 10px;
  height: 32px;
  font-size: 16px;
  line-height: 30px;
  color: @themeFontColor;
  padding: 0 12px;
}
.search-content {
  float:left;
  width: 200px;
  margin-top:10px;
  margin-left:10px;
}
.batchSelect {
  width: 110px;
  padding-left: 0px !important;
  margin-left: 10px;
  border: none;
  span {
    margin-left: 2px;
  }
}
.batchSelect-title {
  width:110px;
  border-radius:22px;
  color: #567485;
}
.batchSelect-size {
    font-size: 16px;
}
.com_pad273 {
  overflow: hidden;
  margin-top: 30px;
  font-size: 24px;
}
.com_pad274 {
  width: 138px;
  height: 50px;
  color: #666;
  text-align: center;
  line-height: 48px;
  border: 1px solid #dcdfe6;
  margin-left: 72px;
  float: left;
  border-radius: 4px;
  background: @themeFontColor;
  color: #fff;
  cursor: pointer;
}
.com_pad275 {
  width: 138px;
  height: 50px;
  color: #666;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 4px;
  cursor: pointer;
}
.com_early_btn1 {
  width: 200px;
  height: 52px;
  text-align: center;
  line-height: 48px;
  border: 1px solid @themeBackGroundColor;
  margin-left: 60px;
  float: left;
  border-radius: 6px;
  background: #fff;
  color: @themeBackGroundColor;
  cursor: pointer;
  font-size: 18px;
  font-weight: 700;
}
.com_early_btn2 {
  width: 200px;
  height: 52px;
  color: #666;
  text-align: center;
  line-height: 48px;
  margin-left: 30px;
  background: @themeBackGroundColor;
  color: #fff;
  float: left;
  border-radius: 6px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 700;
}
.early_warning_div_container {
  position: relative;z-index: 800;height: 330px;margin: 0 auto;margin-top: 240px;
  background: #FFF;width: 548px;overflow: hidden;border-radius: 5px;color: @themeFontColor;
}
.early_warning_div_header {
  margin: 0 20px;display:flex;justify-content: space-between;
  align-items: center;border-bottom: 1px solid #E3E6EB;
}
.early_warning_div_close {
  font-size: 36px;color: #567485;cursor:pointer;
}
.early_warning_div_title {
  color: #BDA16A;margin-left: 64px;margin-top: 20px;margin-bottom: 20px;font-size: 16px;
}
.early_warning_div_span {
  color: @text;
}
.dialog_header{
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #E3E6EB;
  margin: 0 30px;
  .header_title{
    color: @themeFontColor;
    font-size: 18px;
    font-weight: bold;
    line-height: 60px;
  }
  .icon_close{
    font-size: 30px;
    color: #8298A6;
    cursor: pointer;
  }
}
.pc_god .el-dialog__header {
  display: none;
}
.pc_god .el-dialog__body {
  padding: 0;
}
.pc_god41 {
  padding: 30px;
  .pc_god40 {
    color: @themeFontColor;
    font-size: 16px;
    .el-input {
      font-size: 18px;
    }
    .pc_god39 {
      font-weight: 500;
      line-height: 22px;
      display: inline-block;
      margin-right: 64px;
    }
    .pc_god42 {
      font-size: 18px;
      background: #FFFFFF;
      border: 1px solid @themeBackGroundColor;
      border-radius: 4px;
      text-align: center;
      font-weight: 500;
      line-height: 40px;
      width: 119px;
      height: 44px;
      display: inline-block;
      color: @themeBackGroundColor;
      margin-left: 120px;
      cursor: pointer;
    }
    .pc_god43 {
      border: 1px solid @themeBackGroundColor;
      font-size: 18px;
      background: @themeBackGroundColor;
      border-radius: 4px;
      text-align: center;
      line-height: 40px;
      font-weight: 500;
      color: #FFFFFF;
      width: 119px;
      height: 44px;
      display: inline-block;
      margin-left: 18px;
      cursor: pointer;
    }
    .pc_god44 {
      display: inline-block;
      .el-input__suffix {
        padding-right: 10px;
        padding-top: 7px;
        color: @themeFontColor;
      }
      .el-input__inner {
        padding-right: 35px;
      }
    }
    .pc_god45 {
      display: inline-block;
      .el-input__prefix {
        display: none;
      }
    }
  }
}
.table_name_code_column{
  .p_name {
    height: 25px;
    margin-bottom: 10px;
    line-height: 40px;
    font-size: 16px;
  }
  .p_code {
    height: 25px;
    margin-bottom: 5px;
    line-height: 25px;
    font-size: 14px;
    color: #B2C3CD;
  }
}
.header_name_code_column {
  font-size: 16px !important;
}
.sort_div {
  float: left;
  /deep/.el-input__inner{
    color: @themeFontColor;
    font-size: 16px;
    border-radius: 40px;
    height: 32px;
    padding-left: 14px;
  }
  /deep/.el-select .el-input .el-select__caret {
    height: 30px;
    line-height: 28px;
  }
}
.price_input_padding {
  /deep/.el-input__inner {
    padding: 0 40px !important;
    font-family: 'DinMedium', sans-serif !important;
  }
}
.pc_god50 {
  padding: 24px;
  /deep/.el-input.el-input--suffix {
    font-size: 16px;
    color: @themeFontColor;
    /deep/.el-input__inner {
      font-family: 'DinMedium', sans-serif !important;
    }
    font-family: 'DinMedium', sans-serif !important;
  }
  /deep/.el-input__inner {
    font-size: 16px;
    color: @themeFontColor;
    font-family: 'DinMedium', sans-serif !important;
  }
}
.create_time_date_picker {
  margin-left: 10px;
  margin-top: 10px;
  float: left;
}
.date_picker_goodContainer{
  width: 280px;
  height: 44px;
  background-color: #fff;
  border: 1px solid #D7D7D7;
  border-radius: 20px;
  margin-left: 10px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  float: left;
  /deep/ .el-input__prefix {
    display: none;
  }
  /deep/.el-input__inner {
    border: none;
    height: 42px;
  }
}
/deep/ .date_picker_goodContainer .el-input--suffix {
  .el-input__inner{
    border:none;
    height: 42px;
    text-align: center;
    color: @themeFontColor;
    font-size: 16px;
  }
}
.parent_menu_active_div {
  background: @linearBackgroundColor !important;
  width: 110px;
  margin-left: -10px;
  padding-left: 10px;
  height: 56px;
  border-bottom: 1px solid #fff;
}

.determine {
  border-radius:6px;
  background: @themeBackGroundColor;
  color: #fff;cursor:pointer;
  font-weight: 700;
  font-size: 18px;
  width:420px;
  height:52px;
  text-align: center;
  line-height: 50px;
}
#fontColor {
  color: @homeColor;
}
.god29 {
  border-color:  @themeBackGroundColor;
}
.isInPutIng1 {
  border-color: #e3e6eb;
}
#createTime {
  color: @themeFontColor;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
    color: #606266;
}
.type_name{
  width: 79px;
  line-height: 1.3;
  white-space: normal;
  max-height:50px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  word-wrap:break-word;
}
.hidSetSel {
  width: 130px;border-radius:22px;margin-left:10px;
}
.viewport-lt-1280 {
  display: none;
}
@media only screen and (max-width: 1279px) {
  .pc_god36 {
    .editType {
      margin-top: 13px;
    }
  }
}
</style>
<style lang="less" scoped>
  @media only screen and (max-width: 1366px) {
  .pc_pdipt {
    width: 90px;
    font-size: 16px;
    font-weight: 700;
  }
  .pc_god23 {
    width: 90px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
}
@media only screen and (max-width: 1279px) { // 低分辨率适配
  .viewport-gt-1280 {
    display: none;
  }
  .viewport-lt-1280 {
    display: inline-block;
    margin-bottom: 10px;
  }
  .pc_god53 {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .pc_pdipt {
    margin-top: 0px;
  }
  .pc_god23 {
    margin-top: 0px;
  }
  .pc_god36 {
    top: 220px;
  }
  .batchSelect {
    width: 110px;
  }
  .batchSelect-title {
    width: 110px;
  }
  .pc_god46 {
    height: 63px;
  }
  .pc_god_height1 {
    height: calc(100% - 166px);
  }
  .pc_god47 {
    top: 10px;
  }
  .pc_god48 {
    top: 7px;
  }
  .hidSetSel {
    width: 120px;
  }
  .date_picker_goodContainer {
    width: 280px;
  }
  .pc_god54 {
    padding: 0 9px;
  }
}
.dialog-mask {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 400;
  background: rgba(0,0,0,.5);
}

.dialog-container {
  position: relative;
  z-index: 800;
  margin: 0 auto;
  margin-top: 240px;
  background: #FFF;
  width: 548px;
  overflow: hidden;
  border-radius: 5px;
  color: @themeFontColor;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
}

.dialog-item {
  display:flex;
  justify-content:space-between;
  align-items:center;
  width: 420px;
  margin: 20px 64px;
}

.dialog-item-label {
  width: 130px;
  font-size:18px;
  font-weight:bold;
}

.dialog-btn-container {
  overflow: hidden;
  margin: 30px 0;
  font-size: 24px;
}

.red-star {
  color: red;
}

.batch_change_price {
  position: relative;
  z-index: 800;
  margin: 0 auto;
  margin-top: 200px;
  background: #FFF;
  width: 548px;
  overflow: hidden;
  border-radius: 6px;
  color:#567485;
}
</style>
<template>
  <div class='pc_god'>
    <v-AddGoods @openBarCodeScales = "show_hasBarCodeScales=true"></v-AddGoods>
    <!-- 新版管理 -->
    <v-SelManage></v-SelManage>
    <supplier-information :visible.sync="visible" :detailTitle="detailTitle"></supplier-information>
    <v-PcTag></v-PcTag>
    <div
      v-show='showBatchDel'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='showBatchDel = false'
      ></div>
      <div id="createTime" style='position: relative;z-index: 800;height: 260px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;'>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: bold;line-height: 22px;'>提示</div>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: normal;line-height: 40px;padding: 0 45px;'>确定要删除商品吗？</div>
        <!-- <div style="margin-top:10px;margin-left:155px;"><el-checkbox v-model="isDelStock" label=""></el-checkbox>
          <span style="font-size: 18px;margin-left: 18px;">库存清零</span>
        </div> -->
        <div class='com_pad273'>
          <div
            class='com_pad274'
            @click='showBatchDel = false'
          >取消</div>
          <div
            class='com_pad275'
            @click='continueBatchBan();showBatchDel = false'
          >删除</div>
        </div>
      </div>
    </div>
    <!-- 打印标价签失败 -->
    <div v-show='showPrintCheck' style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'>
      <div style='display: flex;align-items: center;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='showPrintCheck = false'>
        <div
          style='position: relative;z-index: 800;height: 300px;margin: 0 auto;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;color:#4c567c;'>
          <div
            style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: bold;line-height: 22px;'>
            抱歉！无法进行打印</div>
          <div
            style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: normal;line-height: 40px;padding: 0 45px;'>
            所选商品中包含无条码商品<br/>请重新选择商品后再打印</div>
          <div class='com_pad273'>
            <div class='com_pad275' style="margin-left:150px;" @click='showPrintCheck = false'>知道了</div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-show='show_hasBarCodeScales'
      style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;'
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
        @click='show_hasBarCodeScales = false'
      ></div>
      <div style='position: relative;z-index: 800;height: 330px;margin: 0 auto;margin-top: 240px;background: #FFF;width: 450px;overflow: hidden;border-radius: 5px;color:#567485;'>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: bold;line-height: 22px;'>请及时更新传秤数据</div>
        <div style='width: 100%;text-align: center;font-size: 24px;margin-top: 40px;font-weight: normal;line-height: 40px;padding: 0 45px;'>商品信息发生修改时（比如改价），请再次传秤以保持信息一致。</div>
        <div class='com_pad273'>
          <div
            class='com_pad274'
            @click='show_hasBarCodeScales = false'
          >取消</div>
          <div
            class='com_pad275'
            @click='setScales();show_hasBarCodeScales = false'
          >去传秤</div>
        </div>
      </div>
    </div>
    <div
      v-show='showBatchSetPrice'
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style='position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);'
      ></div>
      <div class='batch_change_price'>
        <div class="pc_god50">
          <div style="border-bottom: 1px solid #E3E6EB;height: 38px;display: flex;justify-content:space-between;">
            <div style="font-weight:bold;font-size: 18px;color:#567485;">批量修改价格</div>
            <i class="el-icon-close" style="font-size: 22px;font-weight: bold;margin-top: 3px;cursor: pointer;"
            @click="showBatchSetPrice = false"></i>
          </div>
          <div style="display:flex;justify-content:space-between;margin: 14px 40px 20px;">
            <div style="font-size: 18px;font-weight: bold;line-height: 42px;color: #B2C3CD;width: 100px;">
              <span style="color: #FF6159">*</span>价格类型
            </div>
            <div>
              <el-select style="width: 317px;font-size:18px;color:#567485;height: 44px;color: #567485;"
                v-model="priceTypeVal" @change="priceTypeValChange" placeholder="请选择价格类型">
                <el-option v-for="pt in priceTypeList" :key="pt.value" :label="pt.label" :value="pt.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div style="display:flex;justify-content:space-between;margin: 20px 40px;">
            <div style="font-size: 18px;font-weight: bold;line-height: 42px;color: #B2C3CD;width: 100px;">
              <span style="color: #FF6159">*</span>调价方式
            </div>
            <div>
              <el-select style="width: 317px;font-size:18px;color:#567485;height: 44px;" v-model="priceAdjustVal"
                @change="priceAdjustChange" placeholder="请选择调价方式">
                <el-option v-for="pa in priceAdjustList" :key="pa.value" :label="pa.label" :value="pa.value"></el-option>
              </el-select>
            </div>
          </div>
          <div style="display:flex;justify-content:space-between;margin: 20px 40px;" v-if="showPriceInput">
            <div style="font-size: 18px;font-weight: bold;line-height: 42px;color: #B2C3CD;min-width: 102px;">
              <span style="color: #FF6159">*</span>价格
            </div>
            <div>
              <el-input style="min-width: 317px;font-family: 'Microsoft YaHei', sans-serif;"
                :class="(priceAdjustVal === 'addPricePer' || priceAdjustVal === 'subPricePer') ? '' : 'price_input_padding'"
                v-model.trim="batchPrice" v-focus-select="'focusSelect'" @input="batchPrice = batchPriceListening(batchPrice)" placeholder="请输入价格">
                <i slot="prefix" v-show="!(priceAdjustVal === 'addPricePer' || priceAdjustVal === 'subPricePer')">
                  <svg t="1627541527528" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                    p-id="3288" width="24" height="24" style="margin-top:10px;">
                    <path d="M783.612 167.565L575.025
                      463.258h164.392v65.367H547.91v90.024h191.507v66.184H547.91v131.088h-98.529V684.833H250.865V618.65H449.38v-90.024H250.865v-65.367h169.374L213.646
                      167.565h111.089c96.536 146.85 155.141 240.007 175.922 279.397h1.992c7.047-16.226 26.331-48.891 57.787-97.996l118.135-181.4h105.04z"
                      p-id="3289" fill="#bfbfbf">
                    </path>
                  </svg>
                </i>
                <i slot="suffix" v-show="priceAdjustVal === 'addPricePer' || priceAdjustVal === 'subPricePer'">
                  <svg t="1627539968135" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                    p-id="2210" width="24" height="24" style="margin-top:10px;">
                    <path d="M855.7 210.8l-42.4-42.4c-3.1-3.1-8.2-3.1-11.3 0L168.3 801.9c-3.1 3.1-3.1 8.2 0 11.3l42.4 42.4c3.1
                      3.1 8.2 3.1 11.3 0L855.6 222c3.2-3 3.2-8.1 0.1-11.2zM304 448c79.4 0 144-64.6 144-144s-64.6-144-144-144-144
                      64.6-144 144 64.6 144 144 144z m0-216c39.7 0 72 32.3 72 72s-32.3 72-72 72-72-32.3-72-72
                      32.3-72 72-72z m416 344c-79.4 0-144 64.6-144 144s64.6 144 144 144 144-64.6 144-144-64.6-144-144-144z
                      m0 216c-39.7 0-72-32.3-72-72s32.3-72 72-72 72 32.3 72 72-32.3 72-72 72z" p-id="2211" fill="#cdcdcd">
                    </path>
                  </svg>
                </i>
              </el-input>
            </div>
          </div>
          <div style="text-align:right;font-size:16px;color:#567485;
            font-family:Microsoft YaHei UI,sans-serif;margin-right:40px;" v-html="priceAdjustRemark">
          </div>
          <div style="margin-top: 20px;display:flex;justify-content:center;">
            <div
              class="determine"
              @click='checkBatchPrice'>确定</div>
          </div>
        </div>
      </div>
    </div>
    <div
      class='pc_god1'
      v-show='show_import'
    >
      <div class='pc_god11'>
        <div class='pc_god12'>
          批量导入商品<span class='pc_god13' @click="show_import = false;upload_complete = false;">×</span>
        </div>
        <div class="pc_god14">
          <el-alert
            title="仅支持导入新商品"
            type="warning"
            :closable="false"
            width='93%'
            show-icon>
          </el-alert>
          <div style="margin: 20px 0px;">
            <el-popover
              popper-class="pc_pay192 popper_self"
              placement="top-start"
              trigger="click">
              <div style="text-align:left;">
                <div style="font-family:Noto Sans SC, sans-serif;">
                  <span>1.商品条数过多时建议分批导入，每次建议不超过5000条。</span><br/>
                  <span>2.商品数据复制到导入模板文件时请右键>选择性粘贴>粘贴为数值,避免格式问题引发错误。</span><br/>
                  <span>3.数字列以及条码列中请勿包含全角数字、全角小数点或空格。</span><br/>
                  <span>4.商品条码可以包含数字、字母以及-。</span><br/>
                </div>
              </div>
              <span slot="reference" class="import_guidance_text">
                <i class="el-icon-question"></i>如何避免导入失败?
              </span>
            </el-popover>
          </div>
        </div>
        <div class="pc_god15">
          <span id="#createTime" >还没创建过导入数据文件， </span>
            <a href="./excels/goods_template.xlsx" download="商品资料导入模板.xlsx" @click="handleDownGoodsTemplate">
              <span>
              点击下载商品资料导入模板</span>
            </a>
        </div>
        <div class="pc_god16">
          <div>
            <img alt="选择"
              v-show='istype'
              src='../../image/pc_goods_checkbox4.png'
              style="width:24px;height:24px"
            />
            <span>当商品分类、商品单位不存在时会自动创建</span>
          </div>
        </div>
        <div class="pc_god17">
          <span style="font-size: 16px;#4C567C">请选择导入的文件</span>
          <div
            @click="batchClick()"
            class="pc_god18"
          >
            <div style="width: 17px;height: 20px;float: left;margin-left: 14px;margin-top: 8px;">
              <img src="../../image/pc_member_upload.png" style="width: 17px; height: 20px"/>
            </div>
            <div style="margin-left: 8px;line-height: 42px;color: #fff;float: left;">
              上传文件
            </div>
          </div>
          <span class="pc_god19">仅支持导入.xls和.xlsx的文件格式</span>
        </div>
        <!-- 隐藏上传input -->
        <input style="display: none;" class="input-file" type="file" @change="batchUpload"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
        />
        <!-- 上传中... -->
        <div v-show="upload_loading" style="width: 93%;height: 140px;margin: 20px auto;text-align: center;
            background-color: rgba(249, 251, 251, 100);border: 1px solid rgba(227, 230, 235, 100);">
          <div style="margin-top: 50px;">
            <div style="width: 25px; height: 25px; display: inline;">
              <img alt="" src="../../image/pc_member_loading.png" style="width: 25px; height: 25px" />
            </div>
            <div style="line-height: 16px;font-weight: bold;display: inline;font-size: 16px;margin-left: 4px;">
              正在导入，请稍后...
            </div>
          </div>
        </div>
        <!-- 上传完成 -->
        <div v-show="upload_complete" style="width: 93%;margin: 20px auto;text-align: left;background-color: rgba(249, 251, 251, 100);
            border: 1px solid rgba(227, 230, 235, 100);padding-bottom: 20px">
          <div v-show="Number(upload_data.wrong) === 0" style="margin-top: 12px;margin-left: 30px">
            共{{ upload_data.total }}条数据，成功导入{{upload_data.total}}条数据
          </div>
          <div v-show="Number(upload_data.wrong) !== 0" style="margin-top: 10px;margin-left: 10px">
            发现{{ upload_data.wrong }}条错误数据，导致所有数据上传失败
            <span v-show="upload_data.errDown">，具体请</span>
            <span v-show="upload_data.errDown" style="color: #d5aa76; cursor: pointer" class="downloadfaildata"
              @click="downloadfaildata">查看错误提示</span>
            <span v-show="upload_data.errDown">，修正后再次上传。</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 批量设置库存 -->
    <div class="dialog-mask" v-if="showBatchSettingStockDialog">
      <div class="dialog-container">
        <div class="early_warning_div_header">
          <div><span class="dialog-title">设置库存</span></div>
          <div @click="showBatchSettingStockDialog = false">
            <span class="early_warning_div_close">×</span>
          </div>
        </div>
        <div class="dialog-item">
          <div class="dialog-item-label">
            <span class="red-star">*</span>
            <span class="early_warning_div_span">库存数量</span>
          </div>
          <el-input ref="batchSettingStockInput" v-model.trim="batchStockNum" placeholder="请输入库存数量" autofocus
              @input="batchStockNum = $stockLimit({data: batchStockNum, max: 99999.999, min: -99999.999, decimals: 3})" />
        </div>
        <div class="dialog-btn-container">
          <div class="com_early_btn1" @click="showBatchSettingStockDialog = false">取消</div>
          <div class="com_early_btn2" @click="confirmBatchSettingStock()">确定</div>
        </div>
      </div>
    </div>
    <div
      v-if="showEarlyWarning"
      style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;z-index: 400;"
    >
      <div
        style="position: fixed;width: 100%;height: 100%;top: 0;left: 0;background: rgba(0,0,0,.5);"
      ></div>
      <div class="early_warning_div_container">
        <div class="early_warning_div_header">
          <div><span style="font-size: 18px;font-weight: bold;">设置库存上/下限</span></div>
          <div @click="showEarlyWarning = false">
            <span class="early_warning_div_close">×</span>
          </div>
        </div>
        <div class="early_warning_div_title">
          如果想取消库存预警，请将库存上、下限设置为″0″
        </div>
        <div class="pc_god_threshold_div" style="width: 420px;margin: 0 64px;">
          <div style="display:flex;justify-content:space-between;align-items:center;">
            <div style="width: 130px;font-size:18px;font-weight:bold;">
              <span style="color: red;">*</span><span class="early_warning_div_span">库存上限</span></div>
            <el-input v-model.trim="maxStock" v-focus-select="'autoFocus,focusSelect'"
              @input="maxStock = maxStock.replace(/-/g, '');
                maxStock = $stockLimit({data: maxStock, max: 99999.999, min: 0, decimals: 3})"
              placeholder="请输入库存上限" @blur="stockLimitBlur('maxStock');"></el-input>
          </div>
          <div style="display:flex;justify-content:space-between;align-items:center;margin-top: 20px;">
            <div style="width: 130px;font-size:18px;font-weight:bold;">
              <span style="color: red;">*</span><span class="early_warning_div_span">库存下限</span></div>
            <el-input v-model.trim="minStock" v-focus-select="'focusSelect'"
              @input="minStock = minStock.replace(/-/g, '');
                minStock = $stockLimit({data: minStock, max: 99999.999, min: 0, decimals: 3})"
              placeholder="请输入库存下限" @blur="stockLimitBlur('minStock');"></el-input>
          </div>
        </div>
        <div class="com_pad273">
          <div class="com_early_btn1" @click="showEarlyWarning = false">取消</div>
          <div class="com_early_btn2" @click="setStockEarlyWarning()">确定</div>
        </div>
      </div>
    </div>
    <div class='pc_god5' ref="pc_god5">
      <div style='float: left;'>
        <div
          class='pc_god29'
          style="margin-bottom: 10px;"
          :class="inputing_keyword ? 'god29' : 'isInPutIng1'"
        >
          <input
            @focus='inputing_keyword = true'
            @blur='inputing_keyword = false'
            type='text'
            placeholder='商品名称/条码/首字母/扫码'
            maxlength="60"
            v-model.trim='keyword'
            v-focus-select="'focusSelect'"
            id='goods_keyword'
            @compositionstart='pinyin = true'
            @compositionend='pinyin = false'
            @input="keyword = keyword.replace(/[$']/g, '')"
            @keydown.enter="inputSelectHandler('goods_keyword')"
          />
          <img
            class='pc_god32'
            v-show="keyword != ''"
            @click="inputFocus('goods_keyword')"
            src='../../image/pc_clear_input.png'
          />
        </div>
      </div>
      <div class="search-content">
        <vCjSelect @searchChange="searchChange"></vCjSelect>
      </div>
      <div style="float:left;margin-top:10px;">
        <el-select
          v-model="hidSettingValue"
          multiple
          placeholder="显示设置"
          id="hidSetSel"
          @visible-change="setTagTitle"
          class="hidSetSel">
          <el-option-group
            v-for="(hidSettings, index) in hidSettingsList"
            :key="index"
            :label="hidSettings.label">
            <el-option
              style="font-size: 16px;"
              v-for="item in hidSettings.options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.disabled"
            >
              <span class="checkbox"></span>
              <span class='label-name-box' style='margin-left:5px;'>{{item.label}}</span>
            </el-option>
          </el-option-group>
        </el-select>
      </div>
      <div id="createTime" style="float:left;margin-left: 10px;margin-top:10px;font-size: 16px;line-height:40px;">创建时间</div>
      <div class="create_time_date_picker">
        <vCjDatePicker
          type="date"
          :start-date.sync="date_picker.date_from"
          :end-date.sync="date_picker.date_to"
          date-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @pickerChange="datePickerChange"></vCjDatePicker>
      </div>
      <div class='pc_god22 viewport-gt-1280'>
        <div
          class="pc_pdipt"
          style="margin-right:10px;"
          v-if="hasBarCodeScales"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="setScales"
        >传秤管理</div>
        <div
          class="pc_pdipt"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="batchImport"
        >导入商品</div>
        <div
          class="pc_god23"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="addGoods()"
        >新增商品</div>
      </div>
    </div>
    <div class='pc_god53' ref="pc_god53">
      <div>
        <div class="pc_god54 sort_div" v-show="$employeeAuth('delete_products')" style="width: 110px;padding-left: 0px;margin-left: 10px;border: none;">
          <el-select
            :value="'批量删除'"
            @change="batDelSelect"
            style="width:110px;border-radius:22px;color: #567485;">
            <el-option
              style="font-size: 16px;"
              label="删除已选商品"
              value="batchDelete"
            >
              <span class='label-name-box' style="margin-left:2px;">删除已选商品</span>
            </el-option>
            <el-option style="font-size: 16px;" label="按分类删除商品" value="batchDelCategory">
              <span class='label-name-box' style="margin-left:2px;">按分类删除商品</span>
            </el-option>
          </el-select>
        </div>
        <div
          class="pc_god54"
          @click="makeBarcode"
          v-if="$employeeAuth('import_products')"
        >自动生成条码</div>
        <div
          class="pc_god54"
          @click="print('barcode')"
        >打印条码</div>
        <div
          class="pc_god54"
          @click="print('tip')"
        >打印标价签</div>
        <div class="pc_god54 sort_div batchSelect" v-show="$employeeAuth('import_products')">
          <el-select
            v-model="batchValue"
            @change="batchSelect"
            class="batchSelect-title"
          >
            <el-option
              class="batchSelect-size"
              v-for="item in batchOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              @click.native="handleOptionClick(item.value)"
            >
              <span class='label-name-box'>{{item.label}}</span>
            </el-option>
          </el-select>
        </div>
        <div v-if="type !== '-99'" class="sort_div">
          <el-select
            v-model="sortValue"
            id="sortSel"
            placeholder="最新创建"
            style="width:120px;border-radius:22px;margin-left:10px;"
            @change="sortValueChange"
            class="">
            <el-option
              style="font-size: 16px;"
              v-for="item in sortOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <span class='label-name-box' style="margin-left:2px;">{{item.label}}</span>
            </el-option>
          </el-select>
        </div>
      </div>
      <div class='pc_god22 viewport-lt-1280'>
        <div
          class="pc_pdipt"
          style="margin-right:10px;"
          v-if="hasBarCodeScales"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="setScales"
        >传秤管理</div>
        <div
          class="pc_pdipt"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="batchImport"
        >导入商品</div>
        <div
          class="pc_god23"
          :style="$employeeAuth('create_products') ? '' : 'opacity: 40%'"
          @click="addGoods()"
        >新增商品</div>
    </div>
    </div>
    <div class="pc_god33 pc_god_height1">
      <div style="width: calc(100% - 100px);height: 100%;border: 1px solid #e3e6eb;border-radius: 5px;background: #FFF;">
        <div
          v-show="goodsHomeStock"
          style="height: 100%;"
        >
          <el-table
            ref="multipleTable"
            empty-text=" "
            :data="tableData"
            stripe
            @cell-click="chooseOneGoods"
            tooltip-effect="dark"
            style="float: left;width: 100%;font-size: 16px;color: #567485;"
            :row-style="{height: '66px'}"
            :height="tableData == '' ? '55px': table_height"
            @selection-change="handleSelectionChange"
            @sort-change="sortChange"
          >
            <el-table-column type="selection" min-width="6%" align="center"></el-table-column>
            <el-table-column
              min-width="17%"
              label="商品名称"
              prop="name"
              class-name="table_name_code_column"
              label-class-name="header_name_code_column"
              show-overflow-tooltip
              sortable="custom"
            >
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column
              show-overflow-tooltip
              align="center"
              prop="code"
              min-width="11%"
              label="条码"
              sortable="custom"
            >
              <template slot-scope="scope">{{ scope.row.code ? scope.row.code : '-'}}</template>
            </el-table-column>
            <el-table-column
              prop="typeName"
              align="center"
              label="分类"
              show-overflow-tooltip
              min-width="12%"
            ></el-table-column>
            <el-table-column
              label="售价"
              prop="sale_price"
              align="right"
              show-overflow-tooltip
              min-width="10%"
              :sortable="type === '-99' ? false : 'custom'">
              <template slot-scope="scope">{{ Number(scope.row.salePrice).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
              label="会员价"
              prop="vip_price"
              align="right"
              show-overflow-tooltip
              min-width="10%"
              :sortable="type === '-99' ? false : 'custom'">
              <template slot-scope="scope">{{ Number(scope.row.vipPrice).toFixed(2) }}</template>
            </el-table-column>
            <el-table-column
              v-if="!$employeeAuth('purchase_price')"
              label="进价"
              prop="pur_price"
              align="right"
              show-overflow-tooltip
              min-width="10%"
              :sortable="type === '-99' ? false : 'custom'">
              <template slot-scope="scope">{{ formatPurPriceReturn(scope.row.purPrice) }}</template>
            </el-table-column>
            <el-table-column
              label="库存"
              prop="cur_stock"
              align="right"
              show-overflow-tooltip
              min-width="10%"
              :sortable="type === '-99' ? false : 'custom'">
              <template slot-scope="scope">
                <el-popover
                  v-if="scope.row.stockStatus !== ''"
                  trigger="hover"
                  placement="top"
                  popper-class="pc_pay192 popper_self"
                  :open-delay="50"
                  :close-delay="0">
                  <div v-if="scope.row.stockStatus === '过剩'">
                    <div
                      style="width: 10px;height: 10px;display: inline-block;
                        border-radius: 5px;background-color:#FF6159;"></div>
                    <span>库存过剩</span>
                  </div>
                  <div v-if="scope.row.stockStatus === '不足'">
                    <div
                      style="width: 10px;height: 10px;display: inline-block;
                        border-radius: 5px;background-color:#F9AE1B;"></div>
                    <span>库存不足</span>
                  </div>
                  <div v-if="scope.row.stockStatus === '正常'">
                    <div
                      style="width: 10px;height: 10px;display: inline-block;
                        border-radius: 5px;background-color:#47B881;"></div>
                    <span>库存正常</span>
                  </div>
                  <span>上限：{{scope.row.maxStock}}</span><br/>
                  <span>下限：{{scope.row.minStock}}</span>
                  <div :style="stockStatusStyle(scope.row.stockStatus)" slot="reference">
                    {{ $toDecimalFormat(scope.row.curStock, 3) }}
                  </div>
                </el-popover>
                <div v-if="scope.row.stockStatus === ''">
                  {{ $toDecimalFormat(scope.row.curStock, 3) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="单位" min-width="7%" align="center">
              <template slot-scope="scope">{{ scope.row.unitName ? scope.row.unitName : '-'}}</template>
            </el-table-column>
            <el-table-column
             label="供应商"
             min-width="7%"
             align="center"
             show-overflow-tooltip
             >
              <template slot-scope="scope">
                <div class="supplier-overflow" @click="openSupplier(scope.row.supplierName, scope.row)">
                   <span :class="scope.row.supplierName ? 'supplier-title' : ''">{{ scope.row.supplierName ? scope.row.supplierName : '-'}}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div
            style="width: 100%;background: #FFF;position: absolute;z-index: 1;"
            :style="'margin-top:' + (table_height + 4) + 'px'"
          ></div>
          <div v-show="tableData == ''" class="pc_god34">
            <div class="pc_god35">
              <img src="../../image/pc_no_goods.png" />
              <div v-show="keyword != ''">
                <span v-show="type !== ''">该分类下</span>
                未查询到<span style="font-weight: bold;">“{{keyword}}”</span>相关商品
                <div v-if="type === ''">
                  <span>&nbsp;&nbsp;是否新增该商品?</span>
                  <button class="addunsearched-btn" @click="addSearchNullGood()">新增</button>
                </div>
                <div v-else>
                  <span>&nbsp;&nbsp;选择全部分类试试？</span>
                </div>
              </div>
              <div v-show="keyword == ''">暂无商品信息</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="pc_god36">
      <type-menu ref="typeMenu"
        v-model="type"
        :edit-top="157"
        :list.sync="typeArr"
        @edit="editType"></type-menu>
    </div>
    <div class="pc_god46">
      <div class="pc_god47">
        共 <span>{{total}}</span> 款商品
      </div>
      <div
        class="pc_god48"
      >
        <el-pagination
          :key="pageKey"
          layout="prev, pager, next, slot"
          :total="total"
          @current-change="handleCurrentChange"
          :current-page.sync="pagenum"
          :page-size="limit"
          :page-count="total"
        >
          <!-- slot -->
          <vCjPageSize @sizeChange="handleSizeChange" :pageSize.sync="limit" :currentPage.sync="pagenum" :pageKey.sync="pageKey"></vCjPageSize>
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :visible.sync="showChosePrint"
      width="440px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header">
        <div class="header_title">{{printIndex === 'barcode' ? '打印条码' : '打印标价签'}}</div>
        <div
          class="icon_close"
          @click="showChosePrint = false"
        >×</div>
      </div>
      <div class="pc_god41">
        <div v-show="printIndex === 'barcode' ? !setLabelDefault : !setTipDefault"
          class="pc_god40" style="margin-bottom: 20px;">
          <div class="pc_god39">打印份数</div>
          <el-input-number
            v-model="addPrint"
            @blur="setNum"
            placeholder="请输入打印份数"
            :min="1"
            :max="999"
            v-input-int-max-min="{max: 999,min: 0}"
            style="width: 248px;font-size: 18px;"
          ></el-input-number>
        </div>
        <div v-show="printIndex === 'barcode' && labelItem[3].flag && cols !== '40'" class="pc_god40" style="margin-bottom: 20px;">
          <div class="pc_god39">生产日期</div>
          <div class="pc_god45">
            <el-date-picker
              v-model="commodityDate"
              type="date"
              placeholder="请输入生产日期"
              value-format='yyyy-MM-dd'
              style="width: 248px;"
            >
            </el-date-picker>
          </div>
        </div>
        <div v-show="printIndex === 'barcode' && labelItem[4].flag && cols !== '40'" class="pc_god40">
          <div class="pc_god39" style="margin-right: 80px;">保质期</div>
          <div class="pc_god44">
            <el-input
              v-model="commodityEndDate"
              placeholder="请输入保质期"
              @input="commodityEndDate = $intMaxMinLimit({data: commodityEndDate, max: 9999, min: 0})"
              style="width: 248px;"
            >
              <em slot="suffix" style="font-style: normal;">天</em>
            </el-input>
          </div>
        </div>
        <div class="pc_god40" style="margin-top: 30px;">
          <div class="pc_god42" @click="showChosePrint = false">取消</div>
          <div class="pc_god43" @click="submitPrint">确定</div>
        </div>
     </div>
    </el-dialog>
    <el-dialog
      :visible.sync="showBatchDelGoods"
      width="500px"
      :show-close='false'
      :close-on-click-modal='false'
    >
      <div class="dialog_header" style="border-bottom: none">
        <div class="header_title">批量删除</div>
        <div
          class="icon_close"
          @click="showBatchDelGoods = false"
        >×</div>
      </div>
      <div class="pc_god41" style="padding: 0px 30px 30px;">
         <div
          class='pc_god_129'
          style="margin-bottom: 10px;"
          :style="inputCategory_keyword ? 'border-color: #BDA16A' : 'border-color: #e3e6eb;'"
        >
          <input
            @focus='inputCategory_keyword = true'
            @blur='inputCategory_keyword = false'
            type='text'
            placeholder='搜索分类'
            v-model='inputCategory'
            @input="inputCategoryChange"
          />
          <img
            class='pc_god32'
            v-show="inputCategory != ''"
            @click="inputCategory = '';inputCategoryChange()"
            src='../../image/pc_clear_input.png'
          />
        </div>
        <div style="width:100%;border: 1px solid #E3E6EB;margin-top:10px;padding-left:10px">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"></el-checkbox><span style="font-weight: 500;font-size: 14px;color: #537286;">&nbsp;&nbsp;&nbsp;全部分类</span>
        </div>
        <div style="width:100%;max-height: 350px;border-left: 1px solid #E3E6EB;border-right: 1px solid #E3E6EB;border-bottom: 1px solid #E3E6EB;padding-left:10px;overflow-y: auto;">
          <el-checkbox-group v-model="checkedCities">
            <div style="display: flex; justify-content: space-between;">
              <div>
                <el-checkbox label="0">称重类别</el-checkbox>
              </div>
              <div>
                <span style="color: #8FA8BA;font-size: 12px;font-weight: 400;line-height: 34px;">默认类别只能删除商品&nbsp;&nbsp;&nbsp;</span>
              </div>
            </div>
            <div style="display: flex; justify-content: space-between;">
              <div>
                <el-checkbox label="********************************">其他类别</el-checkbox>
              </div>
              <div>
                <span style="color: #8FA8BA;font-size: 12px;font-weight: 400;line-height: 34px;">默认类别只能删除商品&nbsp;&nbsp;&nbsp;</span>
              </div>
            </div>
            <div v-for="(ct,index) in category_del_list" :key="index">
              <el-checkbox :label="ct.fingerprint">{{ct.name}}</el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
        <div style="display: flex; justify-content: space-between;padding-top: 20px">
          <div>
            <el-checkbox v-model="dllCategoryFlg"></el-checkbox>&nbsp;&nbsp;&nbsp;同时删除分类
          </div>
          <div style="border-radius:6px;background:#CBAB63; color: #fff;cursor:pointer;
            font-weight: 500;font-size: 14px;width:100px;height:36px;text-align: center;line-height: 36px;"
            @click='batchDelGoodsCheck();'>确认删除</div>
        </div>
     </div>
    </el-dialog>

    <!-- 批量改价：0元价格二次确认框 -->
    <confirm-dialog
      :visible.sync="showPriceZeroDialog"
      :message="`部分商品${priceZeroText}价为0，<br/>是否继续？`"
      :closeOnClickModal="false"
      :confirm-text="'继续'"
      @cancel="showPriceZeroDialog = false"
      @confirm="showPriceZeroDialog = false;continueBatchChangePrice()"
    ></confirm-dialog>
  </div>
</template>
<script>
// 加载vuex
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style';
import stringUtils from '../../common/stringUtils';
import FileSaver from 'file-saver';
import draggable from 'vuedraggable';
import vCjSelect from '@/common/components/CjSelect';
import vCjPageSize from '@/common/components/CjPageSize';
import SupplierInformation from '@/common/components/SupplierInformation';
import TypeMenu from '@/common/components/TypeMenu';
import logList from '@/config/logList';
import { Keyevent } from '@/utils/keyEvent.js';
import { date } from 'frontend-utils';
import ConfirmDialog from '@/common/components/ConfirmDialog';
import vCjDatePicker from '@/common/components/CjDatePicker';

export default {
  components: {
    draggable,
    vCjSelect,
    vCjPageSize,
    TypeMenu,
    SupplierInformation,
    ConfirmDialog,
    vCjDatePicker
  },
  data() {
    return {
      nowIndex: 0,
      visible: false, // 编辑供应商弹窗
      detailTitle: {}, // 供应商详情
      supplierObject: {
        all: false,
        notSupplier: false,
        supplierList: []
      }, // 供应商传参
      tagPrinterCopies: 1,
      showTag: false,
      printtag_loading: false,
      codeValue: 'code',
      codeList: [{ label: '条码', value: 'code' }],
      tableData: [],
      show_import: false,
      keyword: '',
      inputing_keyword: false,
      watch_type: false,
      // 初始化获取所有分类
      category_list: [],
      total: 0,
      limit: 10,
      pagenum: 1,
      pageKey: 0,
      type: '',
      table_height: 0,
      // 扫码枪变量
      lastTime: '',
      // 设置定时器变量
      scanTimeer: null,
      // 盘点明细
      SlipNo: '',
      // 打印条码
      choose_list: [],
      showPrintCheck: false,
      // 批量删除确认框
      showBatchDel: false,
      from_manage: false,
      searching: false,
      printbarcode_loading: false,
      printtip_loading: false,
      pinyin: false,
      print_click: false,
      subprintClick: false,
      // 批量导入，多选框
      istype: true,
      isunit: true,
      field_mapping: {
        '货号（必填）': 'majorCode',
        '商品名称（必填）': 'name',
        一级分类: 'type',
        二级分类: 'subType',
        单位: 'unit',
        品牌: 'brand',
        季节: 'season',
        性别: 'sex',
        成分: 'composition',
        等级: 'level',
        规格1: 'specs1',
        规格2: 'specs2',
        规格3: 'specs3',
        条码: 'code',
        '售价（必填）': 'salePrc',
        会员价: 'vipPrc',
        进货价: 'purPrc',
        错误原因: 'errMsg'
      },
      // 批量导入
      upload_loading: false,
      upload_complete: false,
      upload_data: {
        total: 0,
        correct: 0,
        errDown: false,
        wrong: 0
      },
      tmpDown: '',
      selectDel: false, // 是否检索删除商品
      getDel: false,
      addPrint: '1',
      showChosePrint: false,
      commodityEndDate: '',
      commodityDate: '',
      printIndex: 'barcode',
      sortValue: 'a.create_at',
      batchValue: '批量设置',
      batchOptions: [
        {value: '1', label: '设置分类'},
        {value: '2', label: '设置单位'},
        {value: '3', label: '设置供应商'},
        {value: '6', label: '设置库存'},
        {value: '4', label: '设置库存上/下限'},
        {value: '5', label: '批量改价'}
      ],
      sortOptions: [
        {value: '', label: '请选择'},
        {value: 'a.create_at', label: '最新创建'},
        {value: 'a.first_letters', label: '首字母升序'}
      ],
      sortOrder: null,
      showBatchSetPrice: false,
      priceTypeVal: '',
      priceTypeList: [{label: '售价', value: 'salePrice'}, {label: '会员价', value: 'vipPrice'},
        {label: '进价', value: 'purPrice'}],
      priceAdjustVal: '',
      priceAdjustList: [],
      salePriceAdjustStyle: [{label: '等于会员价', value: 'equalVipPrice', remark: '商品中售价更新为会员价的金额'},
        {label: '等于进货价', value: 'equalPurchasePrice', remark: '商品中售价更新为进货价的金额<br>进价小数超出2位时自动进位处理（例：3.143=3.15）'}],
      vipPriceAdjustStyle: [{label: '等于售价', value: 'equalSalePrice', remark: '商品中会员价更新为售价的金额'},
        {label: '等于进货价', value: 'equalPurchasePrice', remark: '商品中会员价更新为进货价的金额<br>进价小数超出2位时自动进位处理（例：3.143=3.15）'}],
      showPriceInput: true, // 默认显示价格输入框
      showPriceZeroDialog: false, // 0元价格二次确认框
      priceZeroText: '', // 0元价格二次确认框-文案
      // 设置隐藏项
      hidSettingsList: [{
        lable: '',
        options: [{value: '0', label: '隐藏未销售', disabled: false}, {value: '1', label: '隐藏零库存', disabled: false}]}, {
        lable: '',
        options: [{value: '2', label: '显示未销售', disabled: false}, {value: '3', label: '显示零库存', disabled: false}]}
      ],
      hidSettingValue: [],
      autoGenerateCodes: false,
      priceAdjustRemark: '',
      batchPrice: '',
      showEarlyWarning: false,
      maxStock: '',
      minStock: '',
      typeArr: [],
      date_picker: {
        date_from: '',
        date_to: ''
      },
      focusDate: false,
      isDelStock: false,
      hasBarCodeScales: false,
      show_weighing_category: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      show_hasBarCodeScales: false,
      showBatchDelGoods: false,
      inputCategory: '',
      checkedCities: [],
      category_del_list: [],
      dllCategoryFlg: false,
      inputCategory_keyword: false,
      hotList: [], // 热销商品列表
      showBatchSettingStockDialog: false, // 批量设置库存弹窗
      batchStockNum: '' // 批量库存设置数
    };
  },
  created() {
    this.listenResize(); // 初始化表格高度
    window.addEventListener('resize', this.listenResize); // 窗口大小变化监听
    // 增加扫码枪监听
    this.SET_SHOW({ isPrintSetting: false });
  },
  mounted() {
    this.watch_type = false;
    this.SET_SHOW({ isLogo: true });
    this.SET_SHOW({ isHeader: true });
    this.watch_type = true;
    this.inputFocus('goods_keyword');
    console.log(this.specs, 'this.specs++');
    let dataInfo = $setting.info ? demo.t2json($setting.info) : {};
    this.hasBarCodeScales = dataInfo.hasBarCodeScales ? demo.t2json(dataInfo.hasBarCodeScales) : false;
    document.getElementById('hidSetSel').setAttribute('value', '显示设置');
    this.$nextTick(() => {
      this.$refs.typeMenu.update();
    });
  },
  destroyed() {
    window.removeEventListener('resize', this.listenResize);
  },
  watch: {
    keyword() {
      this.watch_type = false;
      this.can_getProdList = false;
      // if (this.keyword !== '') {
      //   this.type = '';
      //   this.$refs.elMenu.activeIndex = null;
      // }
      this.pagenum = 1;
      var that = this;
      setTimeout(function() {
        that.watch_type = true;
        that.can_getProdList = true;
      }, 50);
      if (this.keyword_timer) {
        clearTimeout(this.keyword_timer);
      }
      this.keyword_timer = setTimeout(() => {
        that.right_list = [];
        that.getProdList();
      }, that.delayedTime);
    },
    // 右侧分类改变，商品列表和列表总数实时响应
    type() {
      if (!this.watch_type) {
        return;
      }
      if (!this.searching) {
        // this.keyword = '';
        this.pagenum = 1;
        if (this.watch_type) {
          this.typeChange();
        }
      }
    },
    // 批量设置商品类型
    batchUpdateGoodsTypeId() {
      if (this.batchUpdateGoodsTypeId === '' || this.batchUpdateGoodsTypeFingerprint === '') {
        return;
      }
      let fingerprints = new Set();
      this.choose_list.map(goods => goods.status !== '禁用' && fingerprints.add(goods.fingerprint));
      var data = {
        goodsList: Array.from(fingerprints).join(),
        goodsType: this.batchUpdateGoodsTypeFingerprint
      };
      goodService.setGoodsType(data, () => {
        demo.msg('success', '批量修改商品分类成功');
        this.handleGoodList();
      });
      this.SET_SHOW({ batchUpdateGoodsTypeId: '' });
      this.SET_SHOW({ batchUpdateGoodsTypeFingerprint: '' });
    },
    // 批量设置供应商
    showSupplierDialgEdit() {
      console.log(this.selectedSuppliersDetail);
      if (this.selectedSuppliersDetail.length === 0 || Object.keys(this.selectedSuppliersDetail).length === 0) {
        return false;
      }
      const fingerprintList = this.choose_list.map(item => item.fingerprint);
      const supplierFingerprint = this.selectedSuppliersDetail.fingerprint;
      const data = {
        fingerprintList: fingerprintList,
        supplierFingerprint: supplierFingerprint
      }
      console.log(data, '---------------------');
      goodSupplierService.batchUpdateGoodsSupplier(data, res => {
        console.log(res);
        demo.msg('success', '批量设置供应商成功');
        this.getProdList();
      }, err => { console.log(err); })
      this.SET_SHOW({ selectedSuppliersDetail: '', showSettingGoodsSupplierDialog: false, showSupplierDialgEdit: false });
    },
    // 设置隐藏项下拉列表发生变化
    hidSettingValue() {
      if (this.hidSettingValue.indexOf('0') !== -1) {
        // 隐藏未销售
        this.hidSettingsList[1].options[0].disabled = true;
        demo.actionLog(logList.checkedItemHideUnsold);
        console.log('隐藏未销售');
      } else {
        this.hidSettingsList[1].options[0].disabled = false;
        console.log('显示未销售');
      }
      if (this.hidSettingValue.indexOf('1') !== -1) {
        // 隐藏零库存
        this.hidSettingsList[1].options[1].disabled = true;
        demo.actionLog(logList.checkedItemHideZeroStock);
        console.log('隐藏零库存');
      } else {
        this.hidSettingsList[1].options[1].disabled = false;
        console.log('显示零库存');
      }
      if (this.hidSettingValue.indexOf('2') !== -1) {
        this.hidSettingsList[0].options[0].disabled = true;
        demo.actionLog(logList.checkedItemShowUnsold);
      } else {
        this.hidSettingsList[0].options[0].disabled = false;
      }
      if (this.hidSettingValue.indexOf('3') !== -1) {
        // 隐藏零库存
        this.hidSettingsList[0].options[1].disabled = true;
        demo.actionLog(logList.checkedItemShowZeroStock);
      } else {
        this.hidSettingsList[0].options[1].disabled = false;
      }
      if (this.type !== 'hot' && !this.searching && this.type !== '-99') {
        this.pagenum = 1;
        this.getProdList();
      }
    },
    // 批量设置商品单位
    batchUpdateGoodsUnitId() {
      if (this.batchUpdateGoodsUnitId === '') {
        return;
      }
      let flg = false;
      let weightList = ['千克', '公斤', 'kg', 'Kg', 'kG', 'KG'];
      for (let cl of this.choose_list) {
        if (cl.isBarcodeScalesGoods === 1 && cl.weighModelVal !== 1 && weightList.indexOf(this.batchUpdateGoodsUnit) === -1) {
          flg = true;
          break;
        }
      }
      if (flg) {
        demo.msg('warning', '传秤商品单位应使用千克');
        return;
      }
      var idlist = '';
      for (var i = 0; i < this.choose_list.length; i++) {
        if (this.choose_list[i].status !== '禁用') {
          idlist += this.choose_list[i].id + ',';
        }
      }
      idlist = idlist.substring(0, idlist.length - 1);
      var data = {
        goodsList: idlist,
        goodsUnit: this.batchUpdateGoodsUnitFingerprint
      };
      goodService.setGoodsUnit(data, () => {
        demo.msg('success', '批量修改商品单位成功');
        // 查询是否有称重商品
        this.$refs.typeMenu.pdWeighingCategory();
        this.handleGoodList();
      });
      this.SET_SHOW({ batchUpdateGoodsUnitId: '' });
      this.SET_SHOW({ batchUpdateGoodsUnitFingerprint: '' });
    },
    selectDel() {
      if (!this.searching) {
        this.pagenum = 1;
        this.getProdList();
      }
    },
    //    如果点开了库存盘点
    goodsHomeStock() {
      if (!this.goodsHomeStock) {
        var that = this;
        var data = { type: 'PDD', table: 'inventories' };
        orderService.get(data, function (res) {
          var json = demo.t2json(res);
          that.SlipNo = json[0].SlipNo;
        });
      }
    },
    showAddGoods() {
      // 查询是否有称重商品
      this.$refs.typeMenu.pdWeighingCategory();
      if (!this.from_manage) {
        this.handleGoodList();
      } else {
        this.from_manage = false;
      }
    },
    showSelManage() {
      console.log(!this.showSelManage, '!this.showSelManage');
      if (!this.showSelManage) {
        // 初始化时，from_manage == false
        // 关闭管理界面时，from_manage == true
        // 关闭管理界面，只刷新列表，不进行下一步请求total和getlist
        // 刷新商品列表
        if (this.isChangeCategory) { // 修改过分类 需要刷新商品列表
          this.from_manage = true;
          // if (this.alltype.indexOf(this.type) === -1) {
          //   this.type = '1';
          //   this.getProdList();
          // }
          this.getProdList();
          this.SET_SHOW({ isChangeCategory: false });
        }
        // 刷新商品分类
        this.$refs.typeMenu.update();
      }
    },
    showBatchSetPrice() {
      if (!this.showBatchSetPrice) {
        this.priceTypeVal = '';
        this.priceAdjustVal = '';
        this.batchPrice = '';
      }
    },
    typeArr() {
      this.category_del_list = this.typeArr.filter(e => (!this.inputCategory || e.name.indexOf(this.inputCategory) !== -1) && e.fingerprint !== '********************************');
    },
    date_picker: {
      deep: true,
      handler: function() {
        this.pagenum = 1;
        this.getProdList();
      }
    },
    showTypeManage() {
      if (!this.showTypeManage) {
        // 刷新商品分类
        this.$refs.typeMenu.update();
      }
    },
    isSyncing() {
      if (!this.isSyncing) {
        this.getProdList();
        // 刷新商品分类
        this.$refs.typeMenu.update();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    handleDownGoodsTemplate() {
      demo.actionLog(logList.downloadImportProductsTemplate);
    },
    datePickerChange() {
      this.pagenum = 1;
      this.getProdList();
    },
    // openSupplier供应商选择
    openSupplier(e, value) {
      console.log(e, '111111111111111111-------我是大');
      if (e) {
        console.log('111111111111111111-------我是大111');
        // 详情信息
        this.detailTitle = _.cloneDeep(value);
        this.visible = true;
      }
    },
    // 供应商回调
    searchChange(e) {
      console.log(e, 'eeeeeeeeeeeeeeeeeeeeeee123');
      this.supplierObject = _.cloneDeep(e);
      if (this.type !== '-99') {
        this.pagenum = 1;
        this.getProdList();
      }
    },
    // 批量改价监听
    batchPriceListening(e) {
      if (this.priceTypeVal === 'purPrice' && this.priceAdjustVal !== 'addPricePer' && this.priceAdjustVal !== 'subPricePer') {
        e = this.$pricePurPriceLimit(e);
        return e;
      } else {
        e = this.$priceLimit(e);
        return e;
      }
    },
    batDelSelect(fun) {
      fun === 'batchDelete' ? this.batchDelete() : this.batchDelCategory();
    },
    // 批量设置
    batchSelect(e) {
      this.batchValue = '批量设置';
      switch (e) {
        case '1':
          // 设置分类
          this.batchSetCategory();
          break;
        case '2':
          // 设置单位
          this.batchSetUnit();
          break;
        case '3':
          // 设置供应商
          this.batchSupplier();
          break;
        case '4':
          // 设置库存上下限
          this.earlyWarning();
          break;
        case '5':
          // 批量改价
          this.batchSetPrice();
          break;
        case '6':
          // 批量设置库存
          this.batchSetStock();
          break;
        default:
          break;
      }
    },
    // 批量设置供应商
    batchSupplier() {
      console.log(1111112222222222222111111);
      if (this.$employeeAuth('import_products') === false) {
        demo.msg('warning', '没有设置权限');
        return;
      }
      if (this.choose_list.length > 0) {
        demo.actionLog({ page: 'goods', action: 'clickBatchSupplier', description: '点击商品-批量添加供应商' });
        this.SET_SHOW({showSettingGoodsSupplierDialog: true, showSupplierDialgEdit: true, supplierGoodsList: this.choose_list});
      } else {
        demo.msg('warning', this.$msg.nothing_selected);
      }
    },
    /**
     * 批量设置库存
     */
    batchSetStock() {
      if (this.$employeeAuth('products_curstock_inventories') === false) {
        return demo.msg('warning', '没有设置权限');
      }
      if (this.choose_list.length === 0) {
        return demo.msg('warning', this.$msg.nothing_selected);
      }
      demo.actionLog({ page: 'goods', action: 'clickBatchSettingStock', description: '点击商品-批量设置库存' });
      this.showBatchSettingStockDialog = true;
      this.batchStockNum = '';
      // 输入框自动获取焦点
      this.$nextTick(() => {
        this.$refs.batchSettingStockInput.focus();
      });
    },
    /**
     * 确定批量设置库存
     */
    confirmBatchSettingStock() {
      if (!this.batchStockNum && this.batchStockNum !== '0') {
        return demo.msg('warning', '库存数量不能为空');
      }
      this.showBatchSettingStockDialog = false;
      const params = {
        batchStockNum: +this.batchStockNum,
        list: this.choose_list
      };
      goodService.batchUpdateGoodsStock(params, () => {
        demo.msg('success', '批量设置库存成功');
        this.getProdList();
      }, err => {
        console.log('批量设置库存失败：', err);
        demo.msg('warning', '批量设置库存失败');
      });
    },
    handleOptionClick(value) {
      // 手动触发 @change 事件
      this.batchSelect(value);
    },
    earlyWarning() {
      if (this.choose_list.length > 0) {
        this.minStock = '';
        this.maxStock = '';
        this.showEarlyWarning = true;
      } else {
        demo.msg('warning', '您未选择任何商品！');
      }
    },
    handleCheckAllChange(val) {
      this.checkedCities = val ? [...this.category_del_list.map(e => e.fingerprint), '0', '********************************'] : [];
    },
    batchDelGoodsCheck() {
      if (this.checkedCities.length === 0) {
        demo.msg('warning', '请至少选择一个分类');
        return;
      }
      this.batchDelGoods();
    },
    batchDelGoods() {
      goodService.deleteByTypes({fingerprint: this.checkedCities, isDelType: +this.dllCategoryFlg + ''}, () => {
        this.showBatchDelGoods = false;
        if (this.dllCategoryFlg) {
          demo.msg('success', '删除分类成功');
          this.checkCurrentTypeIsDel();
        } else {
          demo.msg('success', '删除商品成功');
        }
        // 刷新商品分类
        this.$refs.typeMenu.update();
        this.handleGoodList();
      }, () => {
        if (this.dllCategoryFlg) {
          demo.msg('warning', '分类删除失败');
        } else {
          demo.msg('warning', '删除商品失败');
        }
      });
    },
    checkCurrentTypeIsDel() {
      if (this.dllCategoryFlg) {
        this.category_del_list.forEach(item => {
          if (this.checkedCities.indexOf(item.fingerprint) !== -1 && this.isCheckedChild(item)) {
            this.type = '';
          }
        });
      }
    },
    isCheckedChild(item) {
      if (item.id === +this.type) {
        return true;
      } else if (item.list.length) {
        for (let i of item.list) {
          if (i.id === +this.type) {
            return true;
          }
        }
        return false;
      } else {
        return false;
      }
    },
    inputCategoryChange(val) {
      this.category_del_list = this.typeArr.filter(e => !this.inputCategory || e.name.indexOf(this.inputCategory) !== -1);
    },
    setNum() {
      if (this.addPrint === '' || Number(this.addPrint) === 0 || this.addPrint === undefined) {
        this.addPrint = 1;
      }
    },
    submitPrint() {
      let that = this;
      if (this.subprintClick) {
        return;
      }
      this.subprintClick = true;
      if (this.printIndex === 'barcode') {
        if (!this.setLabelDefault) {
          this.SET_SHOW({ labelPrinterCopies: this.addPrint });
        }
        this.printLabels();
      } else {
        if (!this.setTipDefault) {
          this.SET_SHOW({ tipPrinterCopies: this.addPrint });
        }
        this.printTipSubmit();
      }
      setTimeout(() => {
        that.subprintClick = false;
      }, 1000);
      this.showChosePrint = false;
    },
    inputFocus(sid) {
      this.keyword = '';
      $('#' + sid).focus();
    },
    inputSelectHandler(ref) {
      document.getElementById(ref).select();
    },
    batchClick() {
      this.upload_complete = false;
      this.upload_loading = false;
      document.querySelector('.input-file').click();
      demo.actionLog(logList.uploadImportProductsFile);
    },
    checkDupby(arry, key) {
      return _.filter(arry.map(function (v) {
        return v[key];
      }), function (value, index, iteratee) {
        return _.includes(iteratee, value, index + 1) && value;
      });
    },
    judgeDataWrong(res) {
      if (res.data.wrong > 0) {
        this.downloadMater(res.errData);
      }
    },
    refresh() {
      this.from_manage = true;
      // 刷新商品分类
      this.$refs.typeMenu.update();
      this.keyword = '';
      this.type = '';
    },
    batchUpload(files, callback) {
      this.tmpDown = '';
      if (!event.currentTarget.files.length) {
        return;
      }
      const that = this;
      // 拿取文件对象
      var file = event.currentTarget.files[0];
      var filename = file.name;
      var suffixs = ['xls', 'xlsx'];
      if (
        suffixs.indexOf(
          filename.substr(filename.lastIndexOf('.') + 1).toLowerCase()
        ) === -1
      ) {
        demo.msg(
          'warning',
          that.$msg.support_suffixs.format({ suffixs: suffixs.join('、') })
        );
        return;
      }
      // 用FileReader来读取
      var reader = new FileReader();
      // 重写FileReader上的readAsBinaryString方法
      FileReader.prototype.readAsBinaryString = function (file) {
        reader.onload = function () {
          // 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          var binary = '';
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          var wb = XLSX.read(binary, {
            type: 'binary'
          });
          const sheet2JsonOpts = {
            defval: '' // 给defval赋值为空的字符串
          };
          // 预读导入文件的表头检查是否被修改过
          var firstRow = XLSX.utils.sheet_to_json(
            wb.Sheets[wb.SheetNames[0]],
            {
              header: ["code", "name", "pinyin", "type", "subType", "unit", "salePrc", "vipPrc", "purPrc", "curStock", "manufactureDate", "expiryDays"],
              range: 'A1:L1',
              defval: ''
            },
            sheet2JsonOpts
          );
          if (firstRow.length) {
            let sheetMap = {
              code: "条码",
              name: "商品名称（必填）",
              pinyin: "商品简称",
              type: "一级分类",
              subType: "二级分类",
              unit: "单位",
              salePrc: "售价（必填）",
              vipPrc: "会员价",
              purPrc: "进货价",
              curStock: "库存",
              manufactureDate: "生产日期",
              expiryDays: "保质期(天)"
            };
            let keyList = Object.keys(sheetMap);
            for (let i = 0; i < keyList.length; i++) {
              let prop = keyList[i];
              if (sheetMap[prop] !== firstRow[0][prop]) {
                demo.msg('warning', '模板错误，请下载最新模板');
                return;
              }
            }
          }
          var outdata = XLSX.utils.sheet_to_json(
            wb.Sheets[wb.SheetNames[0]],
            {header: ["code", "name", "pinyin", "type", "subType", "unit", "salePrc", "vipPrc", "purPrc", "curStock", "manufactureDate", "expiryDays"], defval: ''},
            sheet2JsonOpts
          );
          that.outdataFormat(outdata);
          // var goods = stringUtils.fieldMapping(outdata, that.field_mapping, '错误原因');
          if (outdata.length <= 1) {
            demo.msg('warning', that.$msg.no_import_data);
            return;
          }
          if (outdata.length > 5001) {
            demo.msg('warning', '单次批量导入数据条数不能大于5000条!');
            return;
          }
          that.upload_loading = true;
          // 上传
          console.log(outdata, 'outdata+++');
          that.manualSync(outdata);
        };
        reader.readAsArrayBuffer(file);
      };
      reader.readAsBinaryString(file);
      // 清空input，使下次选择文件生效
      document.querySelector('.input-file').value = '';
    },
    // outdataFormat(outdata) { // 处理因为插件读取文件的单元格格式引发的错误数据
    //   // let fmtMap = { purPrc: 6, curStock: 3, salePrc: 2, vipPrc: 2 };
    //   // outdata.forEach(item => {
    //   //   Object.keys(item).forEach(k => {
    //   //     if (fmtMap[k] && item[k] !== '' && !isNaN(item[k])) {
    //   //       item[k] = Number(Number(item[k]).toFixed(fmtMap[k]));
    //   //     }
    //   //   })
    //   // })
    //   outdata.forEach(item => {
    //     // 生产日期格式校正
    //     if (item.manufactureDate && typeof item.manufactureDate === 'number') {
    //       item.manufactureDate = new Date(demo.excelTimeToTimestamp(item.manufactureDate)).format('yyyy-MM-dd');
    //     }
    //   });

    // },
    outdataFormat(outdata) { // 处理因为插件读取文件的单元格格式引发的错误数据
      outdata.forEach(item => {
        item.salePrc = this.prcFormat(this.convertFullWidthToHalfWidth(item.salePrc), 2);
        item.vipPrc = this.prcFormat(this.convertFullWidthToHalfWidth(item.vipPrc), 2);
        item.purPrc = this.prcFormat(this.convertFullWidthToHalfWidth(item.purPrc), 6);
        item.curStock = this.prcFormat(this.convertFullWidthToHalfWidth(item.curStock), 3);
        item.expiryDays = this.convertFullWidthToHalfWidth(item.expiryDays);
        item.manufactureDate = this.convertFullWidthToHalfWidth(item.manufactureDate);
        if (demo.isExcelDateTimeNumber(item.manufactureDate)) { // 判断是否为excell 时间格式
          item.manufactureDate = new Date(demo.excelTimeToTimestamp(item.manufactureDate)).format('yyyy-MM-dd');
        } else {
          const regex = /^(200[0-9]|20[1-9][0-9]|2099)[-\/]?(0?[1-9]|1[0-2])[-\/]?(0?[1-9]|[12][0-9]|3[01])$/; // eslint-disable-line
          if (regex.test(item.manufactureDate)) {
            item.manufactureDate = item.manufactureDate.toString().replace(regex, (match, year, month, day) => {
              return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            });
          }
        }
      });
    },

    /**
     * 全角转半角
     */
    convertFullWidthToHalfWidth(str) {
      if (/[０-９]/.test(str)) {
        str = str.replace(/[０-９]/g, function(fullWidthChar) { // eslint-disable-line
          return String.fromCharCode(fullWidthChar.charCodeAt(0) - 65248);
        });
      }
      if (/．/.test(str)) {
        str = str.replace(/．/g, '.'); // eslint-disable-line
        return str;
      }
      if (/／/.test(str)) {
        str = str.replaceAll(/／/g, '-');
      }
      if (/－/.test(str)) {
        str = str.replaceAll(/－/g, '-');
      }
      return str;
    },
    isNumeric(str) {
      return /^\d+(\.\d+)?$/.test(str);
    },
    prcFormat(num, precision = 2) {
      if (this.isNumeric(num)) {
        return Number(num) == Number(num).toFixed(precision) ? this.limitDecimalPlaces(num, precision) : num
      } else {
        return num
      }
    },
    limitDecimalPlaces(number, precision = 2) {
      let parts = number.toString().split('.');
      if (parts.length > 1 && parts[1].length > precision) {
        return parseFloat(`${parts[0]}.${parts[1].slice(0, precision)}`);
      }
      return number;
    },
    manualSync (outdata) {
      // 如果正在执行云同步，则不继续执行
      if (this.isSyncing) {
        return;
      }
      syncService.clearDataInfo(
        msg => {
          if (demo.isNullOrTrimEmpty(msg) || typeof (msg) === "object") {
            msg = this.$msg.sync_success;
          }
          let param = {
            autoGenerateCodes: '0',
            goods: outdata
          };
          goodService.batchImport(param, res => {
            console.log(res, '批量导入成功res');
            demo.msg('success', msg);
            this.upload_loading = false;
            this.upload_complete = true;
            this.upload_data.total = outdata.length;
            this.upload_data.wrong = 0;
            this.handleGoodList();
            // 刷新商品分类
            this.$refs.typeMenu.update();
          }, errData => {
            if (typeof (errData) === 'string') {
              demo.msg('warning', errData);
              this.upload_loading = false;
              this.upload_complete = false;
            } else {
              demo.msg('warning', '批量导入失败,具体原因请下载错误数据查看');
              this.upload_loading = false;
              this.upload_complete = true;
              this.upload_data.errDown = true;
              this.downloadMater(errData);
              this.upload_data.wrong = errData.filter(err => err.errMsg).length;
            }
          });
        },
        err => {
          if (demo.isNullOrTrimEmpty(err) || typeof (err) === "object") {
            err = this.$msg.sync_failure;
          }
          this.upload_loading = false;
          this.upload_complete = false;
          demo.msg('warning', err);
        }
      );
      // 刷新商品列表
      // that.refresh();
      // that.judgeDataWrong(res.config.data);
    },
    judgeisParam(param) {
      if (demo.isNullOrTrimEmpty(param)) {
        return '';
      }
      return param;
    },
    downloadMater(info) {
      const that = this;
      let data = info.map((it) => {
        return {
          code: that.judgeisParam(it.code),
          name: that.judgeisParam(it.name),
          pinyin: that.judgeisParam(it.pinyin),
          type: that.judgeisParam(it.type),
          subType: that.judgeisParam(it.subType),
          unit: that.judgeisParam(it.unit),
          salePrc: that.judgeisParam(it.salePrc),
          vipPrc: that.judgeisParam(it.vipPrc),
          purPrc: that.judgeisParam(it.purPrc),
          curStock: that.judgeisParam(it.curStock),
          manufactureDate: that.judgeisParam(it.manufactureDate),
          expiryDays: that.judgeisParam(it.expiryDays),
          errMsg: that.judgeisParam(it.errMsg)
        };
      });
      data[0].errMsg = '错误原因';
      console.log('data', data);
      const defaultCellStyle = {
        font: { name: '宋体' }
      };
      const wopts = {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
        defaultCellStyle: defaultCellStyle,
        showGridLines: false
      };
      const wb = { SheetNames: ['商品导入模板', '备注'], Sheets: {}, Props: {} };
      wb.Sheets['商品导入模板'] = XLSX.utils.json_to_sheet(data, {skipHeader: true, hidden: [], skipHidden: false});
      wb.Sheets['商品导入模板']['!cols'] = [{wch: 12}, {wch: 16}, {wch: 10}, {wch: 10}, {wch: 10}, {wch: 10}, {wch: 10}, {wch: 10}, {wch: 10},
        {wch: 20}];
      let borderAll = {
        top: { style: 'thin', color: '000000' },
        bottom: { style: 'thin', color: '000000' },
        left: { style: 'thin', color: '000000' },
        right: { style: 'thin', color: '000000' }
      };
      let style1 = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: borderAll
      };
      wb.Sheets['商品导入模板']['B1'].s = style1;
      wb.Sheets['商品导入模板']['G1'].s = style1;
      let style2 = {
        fill: {
          fgColor: { rgb: 'B7DEE8' }
        },
        font: { name: '宋体' },
        alignment: { vertical: 'center', horizontal: 'center' },
        border: borderAll
      };
      let columns = ['A1', 'C1', 'D1', 'E1', 'F1', 'H1', 'I1', 'J1', 'K1', 'L1', 'M1'];
      columns.forEach((column) => {
        wb.Sheets['商品导入模板'][column].s = style2;
      });
      let style3 = {
        font: { name: '宋体' },
        border: borderAll
      };
      columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
      for (let i = 2; i <= data.length; i++) {
        columns.forEach((column) => {
          wb.Sheets['商品导入模板'][column + i].s = style3;
        });
      }
      this.addSheet(wb, borderAll)
      // 创建二进制对象写入转换好的字节流
      let tmpDown = new Blob([stringUtils.s2ab(XLSXStyle.write(wb, wopts))], {
        type: 'application/octet-stream'
      });
      this.tmpDown = tmpDown;
    },
    // sheet1为导出的错误数据
    // 添加sheet2为备注，把规则添加到导出错误数据的excel里，方便用户根据备注修改错误数据
    addSheet(wb, borderAll) {
      const data = [
        {A: '字段', B: '默认值', C: '输入内容说明', D: ''},
        {A: '商品条码', B: '', C: '最多16位，仅限字母、数字和-', D: '选填项目'},
        {A: '商品名称', B: '', C: '必填，最多60字', D: '必填项目'},
        {A: '商品简称', B: '', C: '最多4位，仅限汉字、字母、数字和!@#%^&*()_-=+{}"<>?[],./`~符号', D: '选填项目'},
        {A: '一级分类', B: '其他类别', C: '最多10位汉字或20位字符', D: '选填项目'},
        {A: '二级分类', B: '', C: '最多10位汉字或20位字符', D: '选填项目'},
        {A: '单位', B: '', C: '最多5位汉字', D: '选填项目'},
        {A: '售价', B: '', C: '必填，不小于0且不大于999999.99；必须为数字，最多两位小数', D: '必填项目'},
        {A: '会员价', B: '', C: '不小于0且不大于999999.99，必须为数字，最多两位小数', D: '选填项目'},
        {A: '进货价', B: '', C: '不小于0且不大于999999.999999，必须为数字，最多六位小数', D: '选填项目'},
        {A: '库存', B: '', C: '不小于0且不大于99999.999，必须为数字，最多三位小数', D: '选填项目'},
        {A: '生产日期', B: '', C: '格式为：YYYY-MM-DD 例如：2022-07-09（月和日为个位数时需要在数字前补充“0”）', D: '选填项目'},
        {A: '保质期（天）', B: '', C: '仅限正整数，且不能超过9999', D: '选填项目'},
        {A: '', B: '', C: '', D: ''},
        {A: '', B: '', C: '', D: ''},
        {A: '导入说明（必读）', B: '', C: '', D: ''},
        {A: '1、商品名称、零售价为必填项；', B: '', C: '', D: ''},
        {A: '2、导入只能新增商品，如果存在相同的条码，会提示错误，不会覆盖数据；', B: '', C: '', D: ''},
        {A: '3、条码仅限输入数字、字母和中横线，条码最大16位（建议13位)；', B: '', C: '', D: ''},
        {A: '4、所有的价格,必须以数字形式录入,并且范围在0 ~ 999999.99之间；', B: '', C: '', D: ''},
        {A: '5、生产日期与保质期两项数据若有其中一项未填写，那么过期预警则不生效；', B: '', C: '', D: ''}
      ]
      wb.Sheets['备注'] = XLSX.utils.json_to_sheet(data, {skipHeader: true, hidden: [], skipHidden: false});
      wb.Sheets['备注']['!cols'] = [{wch: 16}, {wch: 12}, {wch: 56}, {wch: 12}];
      // styleBlue、styleYellow、styleRed代表蓝色部分样式、黄色部分样式、红色部分样式
      // 这种写法好处是改变其中一个，其余的相同赋该变量值的都会进行改变
      // 这也是缺点，就是单点其中一个做修改，其余的也会影响
      // 红色变量注释了，就是因为其中一个不需要边框，去掉的时候，凡事红色式样相关的边框都消失了，产生了问题
      let styleBlue = {
        fill: {
          fgColor: { rgb: 'B6DDE8' }
        },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colBlue = ['A1', 'A2', 'A4', 'A5', 'A6', 'A7', 'A9', 'A10', 'A11', 'A12', 'A13', 'B1', 'C1']
      colBlue.forEach(el => {
        wb.Sheets['备注'][el].s = styleBlue;
      });
      let styleYellow = {
        fill: {
          fgColor: { rgb: 'FFFF00' }
        },
        font: { name: '宋体', bold: true },
        alignment: { vertical: 'center', horizontal: 'center' }
      };
      const colYellow = ['A3', 'A8']
      colYellow.forEach(el => {
        wb.Sheets['备注'][el].s = styleYellow;
      });
      // let styleRed = {
      //   font: { color: {rgb: 'FF0000'} }
      // };
      const colRed = ['A16', 'D3', 'D8']
      colRed.forEach(el => {
        wb.Sheets['备注'][el].s = {
          font: { color: {rgb: 'FF0000'} }
        };
      })
      wb.Sheets['备注']['!merges'] = [
        {s: {r: 16, c: 0}, e: {r: 16, c: 3}},
        {s: {r: 17, c: 0}, e: {r: 17, c: 3}},
        {s: {r: 18, c: 0}, e: {r: 18, c: 3}},
        {s: {r: 19, c: 0}, e: {r: 19, c: 3}},
        {s: {r: 20, c: 0}, e: {r: 20, c: 3}}
      ]
      for (let i = 1; i < 14; i++) {
        this.addBorder('A', i, wb, borderAll)
        this.addBorder('B', i, wb, borderAll)
        this.addBorder('C', i, wb, borderAll)
        this.addBorder('D', i, wb, borderAll)
      }
      wb.Sheets['备注']['A16'].s.border = {}
    },
    addBorder(column, i, wb, borderAll) {
      if (wb.Sheets['备注'][column + i].s === undefined) {
        wb.Sheets['备注'][column + i].s = {border: borderAll}
      } else {
        wb.Sheets['备注'][column + i].s.border = borderAll
      }
    },
    downloadfaildata() {
      if (this.tmpDown !== '') {
        const filename = '商品批量导入失败的数据' + date().format('YYYYMMDDHHmmss')
        FileSaver.saveAs(this.tmpDown, `${filename}.xlsx`);
        demo.actionLog(logList.downloadImportProductsFailedFile);
      }
    },
    addGoods() {
      if (!this.$employeeAuth('create_products')) {
        return;
      }
      this.SET_SHOW({ goodsDetail: [] });
      this.SET_SHOW({ returnGoodsDetail: [] });
      this.setAddGoodType();
      this.SET_SHOW({ batchUpdateGoodsType: false });
      this.SET_SHOW({ batchUpdateGoodsUnit: false });
      this.SET_SHOW({ addGoodsUnit: '' });
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ suggestPrice: '' });
    },
    setAddGoodType() { // 新增商品时增加分类默认选中式样
      let curType = [];
      for (let item of this.typeArr) {
        if (+item.id === +this.type) {
          this.SET_SHOW({
            addGoodsCategoryId: item.id,
            addGoodsCategory: item.name,
            addGoodsCategoryfingerprint: item.fingerprint,
            addGoodsCategoryFrom: 'goods'
          });
          curType.push(item);
          break;
        } else if (item.list && item.list.length) {
          curType = item.list.filter(subItem => {
            if (+subItem.id === +this.type) {
              this.SET_SHOW({
                addGoodsCategoryId: subItem.id,
                addGoodsCategory: item.name + '/' + subItem.name,
                addGoodsCategoryfingerprint: subItem.fingerprint,
                addGoodsCategoryFrom: 'goods'
              });
              return +subItem.id === +this.type;
            }
          });
          if (curType.length) {
            break;
          }
        }
      }
      if (!curType.length) { // 全部分类和称重分类
        this.SET_SHOW({ addGoodsCategoryId: '2', addGoodsCategory: '其他分类', addGoodsCategoryfingerprint: md5('其他分类') });
      }
    },
    addSearchNullGood() {
      var addstr = this.keyword;
      var data = {'pset': '', 'type': '', condition: this.keyword, selectDel: false, 'limit': this.limit, 'offset': '0'};
      goodService.search(data, res => {
        var json = demo.t2json(res);
        console.log('json[0]', json);
        if (json.length !== 0) {
          this.SET_SHOW({ goodsDetail: json[0] });
          this.SET_SHOW({ showAddGoods: true });
        } else {
          if (!this.$employeeAuth('create_products')) {
            demo.msg('warning', '没有新增商品权限');
          } else {
            if (addstr.trim().length !== 0 && !isNaN(addstr) && (addstr.length === 8 || addstr.length === 12 || addstr.length === 13)) {
              this.SET_SHOW({scanGoodsCode: addstr});
            } else if (this.keyword !== '') {
              this.SET_SHOW({midGoodsName: this.keyword});
            } else {
              console.log('other conditions');
            }
            this.addGoods();
          }
        }
      });
    },
    // 监听屏幕高度，使table表格高度能实时变化
    listenResize() {
      let that = this;
      if (this.resize_timer) {
        clearTimeout(this.resize_timer);
      }
      this.resize_timer = setTimeout(() => {
        let filterHeight = that.$refs['pc_god5'].offsetHeight;
        let funcHeight = that.$refs['pc_god53'].offsetHeight;
        that.table_height = $(window).height() - 121 - filterHeight - funcHeight;
        // that.limit = parseInt((document.body.clientHeight - (154 + filterHeight + funcHeight)) / 66);
        that.handleGoodList();
      }, that.delayedTime);
    },
    // 表格的checkbox，选择以后val会实时响应
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.choose_list = val;
    },
    sortValueChange() {
      // 清空表格排序条件
      this.sortOrder = null;
      this.$refs.multipleTable.clearSort();
      this.pagenum = 1;
      this.handleGoodList();
    },
    sortChange(e) {
      if (!e.order) { // 表格无排序状态
        this.sortValue = 'a.create_at';
        this.sortValueChange();
      } else {
        this.sortValue = '';
        this.sortOrder = e;
        this.pagenum = 1;
        this.handleGoodList();
      }
    },
    openSetting() {
      this.SET_SHOW({ isPrintSetting: true });
    },
    setChooseList() {
      for (var i = 0; i < this.choose_list.length; i++) {
        if (this.choose_list[i].vip_price != 0) {
          this.choose_list[i].vip_name = '会员价';
          this.choose_list[i].vip_price = Number(this.choose_list[i].vipPrice).toFixed(2);
        }
        this.choose_list[i].sale_name = '售价';
        this.choose_list[i].sale_price = Number(this.choose_list[i].salePrice).toFixed(2);
        this.choose_list[i].store_name = this.username;
      }
    },
    // 打印条码
    print(n) {
      let that = this;
      if (n === 'barcode') {
        demo.actionLog(logList.clickPrintBarcodeOnProdMng);
      }
      if (n === 'tip') {
        demo.actionLog(logList.clickPrintPriceOnProdMng);
      }
      for (let i = 0; i < this.choose_list.length; i++) {
        if (!this.choose_list[i].code && (n === 'tip' || n === 'barcode')) {
          this.showPrintCheck = true;
          return;
        }
      }
      if (this.print_click) {
        return;
      }
      this.print_click = true;
      setTimeout(function() {
        that.print_click = false;
      }, that.clickInterval);
      if (this.choose_list.length > 0) {
        let flg = (n === 'barcode') && (that.checkPrint('setting_label_printer'));
        let flgTip = (n === 'tip') && (that.checkPrint('setting_tip_printer'));
        if (flg || flgTip) {
          demo.msg('warning', '请设置打印机');
          that.openSetting();
          return;
        }
        that.setChooseList();
        try {
          this.codeValue = this.codePrintValue;
          this.sonarPrint(n);
        } catch (e) {
          demo.msg('warning', that.$msg.printer_error);
        }
      } else {
        demo.msg('warning', that.$msg.nothing_selected);
      }
    },
    setTagTitle() {
      var tagTextList = document.querySelector('#hidSetSel').querySelectorAll('.el-select__tags-text');
      tagTextList.forEach((item) => {
        item.setAttribute('title', item.innerText);
      });
    },
    sonarPrint(n) {
      var that = this;
      if (n === 'barcode') {
        this.printbarcode_loading = true;
        setTimeout(function () {
          that.printbarcode_loading = false;
        }, 2000);
        if (!this.setLabelDefault || (this.labelItem[3].flag && this.cols !== '40') || (this.labelItem[4].flag && this.cols !== '40')) {
          this.commodityEndDate = '';
          this.commodityDate = '';
          this.addPrint = 1;
          this.printIndex = n;
          this.showChosePrint = true;
        } else {
          that.printLabels();
        }
      } else if (n === 'tip') {
        this.printtip_loading = true;
        setTimeout(function () {
          that.printtip_loading = false;
        }, 2000);
        this.printIndex = n;
        this.printTip();
      }
    },
    printTip() {
      if (!this.setTipDefault) {
        this.commodityEndDate = '';
        this.commodityDate = '';
        this.addPrint = 1;
        this.showChosePrint = true;
      } else {
        this.printTipSubmit();
      }
    },
    printTipSubmit() {
      let deepPrint = _.cloneDeep(this.choose_list);
      if (!this.printVipPrice) {
        _.forEach(deepPrint, function(item) {
          item.vip_price = '';
          item.vip_name = '';
        });
      }
      let printLists = _.flatMap(deepPrint, this.duplicateTip);
      printLists.map(item => {
        item.salePrice = Number(item.salePrice).toFixed(2);
        item.unit_name = item.unitName;
        item.sale_price_fontsize = this.sale_price_fontsize;
        if (Number(item.vip_price) === 0) {
          item.vip_price = '';
          item.vip_name = '';
        } else {
          item.vip_price = Number(item.vip_price).toFixed(2);
          item.vip_price_fontsize = this.vip_price_fontsize;
        }
      });
      external.printtip(
        this.setting_tip_printer,
        JSON.stringify(printLists)
      );
    },
    duplicateLab(n) {
      let arr = [];
      for (let i = 0; i < this.labelPrinterCopies; i++) {
        arr.push(n);
      }
      return arr;
    },
    duplicateTip(n) {
      let arr = [];
      for (let i = 0; i < this.tipPrinterCopies; i++) {
        arr.push(n);
      }
      return arr;
    },
    printLabels() {
      let emptyObj = {Title: ' ', Text: ' ', Size: 6};
      let printData = {
        printname: this.setting_label_printer,
        Width: this.getLabPrintWidth(),
        Height: this.getLabPrintHeight(),
        Landscape: this.getIsLandscape(),
        Offset: 0.01,
        DefaultSize: 10,
        others: []
      };
      let printList = _.cloneDeep(this.choose_list);
      printList.forEach(item => {
        let printObj = _.cloneDeep(printData);
        this.setPrintDataOthers(printObj, item, emptyObj);
        for (let i = 0; i < this.labelPrinterCopies; i++) {
          console.log(printObj, 'printObj+');
          external.printLabelAndBarcodeInMM(printObj);
        }
      });
    },
    setPrintDataOthers(printObj, item, emptyObj) {
      if (this.labelItem[0].flag) {
        printObj.others.push({Title: '', Text: item.name.substr(0, 20), Size: 10});
      }
      if (this.labelItem[1].flag) {
        printObj.others.push({Title: '', Text: item.sale_price === '' ? '' : '售价:¥' + item.sale_price, Size: 10});
      }
      printObj.others.push(emptyObj);
      if (this.labelItem[2].flag && item.code) {
        printObj.others.push({
          barcode: item.code,
          barcode_Width: this.cols === '40' ? 40 : 60,
          barcode_Height: this.cols === '40' ? 40 : 60
        });
      }
      let dateStr = '';
      dateStr += this.labelItem[3].flag && this.cols !== '40' ? '生产日期:' + this.commodityDate + ' '
        : '                    ';
      dateStr += this.labelItem[4].flag && this.cols !== '40' ? '保质期:' + this.commodityEndDate + '天' : '';
      printObj.others.push({
        Title: '',
        Text: dateStr,
        Size: 8
      });
      if (this.cols === '64') {
        printObj.others.unshift(emptyObj);
      }
    },
    getLabPrintWidth() {
      return this.cols !== '60' ? this.labelPrintMap[this.cols].width
        : this.labelPrintMap[this.cols].height;
    },
    getLabPrintHeight() {
      return this.cols !== '60' ? this.labelPrintMap[this.cols].height
        : this.labelPrintMap[this.cols].width;
    },
    getIsLandscape() {
      return this.cols === '60';
    },
    checkPrint(n) {
      let check = this[n] === undefined || this[n] === null || this[n] === '';
      return check;
    },
    // 批量导入，准备选excel
    batchImport() {
      demo.actionLog(logList.clickImportProducts);
      if (!this.$employeeAuth('create_products')) {
        return;
      }
      this.show_import = true;
    },
    batchSetCategory() {
      if (!this.$employeeAuth('import_products')) {
        demo.msg('warning', '没有设置权限');
        return;
      }
      if (this.choose_list.length > 0) {
        this.SET_SHOW({ batchUpdateGoodsType: true });
        this.SET_SHOW({ showTypeManage: true });
      } else {
        demo.msg('warning', this.$msg.nothing_selected);
      }
    },
    // 批量删除分类
    batchDelCategory() {
      demo.actionLog(logList.clickBatchDelCategory);
      if (this.$employeeAuth('import_products') === false) {
        demo.msg('warning', '没有设置权限');
        return;
      }
      this.inputCategory = '';
      this.checkedCities = [];
      this.dllCategoryFlg = true;
      this.showBatchDelGoods = true;
    },
    // 批量设置商品单位
    batchSetUnit() {
      if (!this.$employeeAuth('import_products')) {
        demo.msg('warning', '没有设置权限');
        return;
      }
      if (this.choose_list.length > 0) {
        this.SET_SHOW({ selectName: '' });
        this.SET_SHOW({ batchUpdateGoodsUnit: true });
        this.SET_SHOW({ agreeChooseCategory: true });
        this.SET_SHOW({ showSelManage: true });
        this.SET_SHOW({ batchUpdateGoods: 'unit' });
        this.SET_SHOW({ selManageTypeName: '单位' });
      } else {
        demo.msg('warning', this.$msg.nothing_selected);
      }
    },
    // 切换价格方式清空价格
    priceTypeValChange(val) {
      this.batchPrice = '';
      this.initPriceAdjustList();
      switch (val) {
        case 'salePrice':
          this.priceAdjustList = this.priceAdjustList.concat(this.salePriceAdjustStyle);
          break;
        case 'vipPrice':
          this.priceAdjustList = this.priceAdjustList.concat(this.vipPriceAdjustStyle);
          break;
        default:
          break;
      }
    },
    priceAdjustChange(val) {
      this.batchPrice = '';
      this.priceAdjustList.forEach(item => {
        if (item.value === this.priceAdjustVal) {
          this.priceAdjustRemark = item.remark;
        }
      });
      this.showPriceInput = !(val === 'equalVipPrice' || val === 'equalSalePrice' || val === 'equalPurchasePrice');
    },
    stockStatusStyle(status) {
      let style = '';
      if (status === '过剩') {
        style = 'color: #FF6159';
      } else if (status === '不足') {
        style = 'color: #F9AE1B';
      } else {
        style = '';
      }
      return style;
    },
    stockLimitBlur(flg) {
      if (isNaN(this[flg])) {
        this[flg] = 0;
        demo.msg('warning', '库存数应为数字！');
        return;
      } else if (Number(this[flg] > 99999.999)) {
        this[flg] = 99999.999;
        return;
      } else {
        // do nothing avoid sonar
        console.log(this[flg]);
      }
      this[flg] = Number(Number(this[flg]).toFixed(3));
    },
    setStockEarlyWarning() {
      if (this.minStock === '' || this.maxStock === '') {
        demo.msg('warning', '库存上限和下限不能为空！');
        return;
      }
      if (Number(this.minStock) >= Number(this.maxStock) && Number(this.maxStock) !== 0) {
        demo.msg('warning', '库存上限应大于库存下限！');
        return;
      }
      var ids = '';
      for (var i = 0; i < this.choose_list.length; i++) {
        ids += (i === 0 ? '' : ',') + this.choose_list[i].id;
      }
      var params = {
        minStock: this.minStock,
        maxStock: this.maxStock,
        ids: ids
      };
      goodService.updateGoodsMinAndMaxStock(params, () => {
        demo.msg('success', '设置库存预警成功！');
        this.showEarlyWarning = false;
        this.handleGoodList();
      }, error => {
        demo.msg('warning', error);
      });
    },
    // 进货价格式化返回
    formatPurPriceReturn(value) {
      if (isNaN(value)) {
        return '0.00';
      }
      let val = Math.abs(value);
      let dotIndex = String(val).indexOf('.');
      let dotAfterLength = dotIndex === -1 ? 0 : String(val).length - dotIndex - 1;
      let indexL = dotAfterLength <= 2 ? 2 : Math.min(dotAfterLength, 6);
      return Math.min(val, 999999.999999).toFixed(indexL);
    },
    checkBatchPrice() {
      if (!this.priceTypeVal) {
        demo.msg('warning', '请选择价格类型');
        return;
      }
      if (!this.priceAdjustVal) {
        demo.msg('warning', '请选择调价方式');
        return;
      }
      if (this.priceAdjustVal === 'equalPurchasePrice' && this.$employeeAuth('purchase_price')) {
        demo.msg('error', '暂无此权限，请联系管理员授权');
        return;
      }
      // 调价方式如果为：1、售价=会员价/进货价  2、会员价=售价/进货价，则需检测是否存在0元价格，弹窗二次确认，无需check batchPrice
      if (this.priceAdjustVal === 'equalVipPrice' || this.priceAdjustVal === 'equalSalePrice' || this.priceAdjustVal === 'equalPurchasePrice') {
        const chooseList = [...this.choose_list];
        switch (this.priceAdjustVal) {
          case 'equalVipPrice':
            if (chooseList.find(item => item.vipPrice === 0)) {
              this.priceZeroText = '会员';
              this.showPriceZeroDialog = true;
              return;
            }
            break;
          case 'equalSalePrice':
            if (chooseList.find(item => item.salePrice === 0)) {
              this.priceZeroText = '售';
              this.showPriceZeroDialog = true;
              return;
            }
            break;
          case 'equalPurchasePrice':
            if (chooseList.find(item => item.purPrice === 0)) {
              this.priceZeroText = '进货';
              this.showPriceZeroDialog = true;
              return;
            }
            break;
          default:
            break;
        }
      } else {
        if (!this.batchPrice) {
          demo.msg('warning', '请输入价格');
          return;
        }
        this.batchPrice = Number(this.batchPrice);
        if (isNaN(this.batchPrice)) {
          demo.msg('warning', '请输入正确价格');
          return;
        } else {
          if ((this.batchPrice < 0 || this.batchPrice > 999999.99) && this.priceTypeVal !== 'purPrice') {
            demo.msg('warning', '商品价格区间是0~999999.99');
            return;
          }
          if ((this.batchPrice < 0 || this.batchPrice > 999999.999999) && this.priceTypeVal === 'purPrice') {
            demo.msg('warning', '商品价格区间是0~999999.999999');
            return;
          }
        }
      }
      this.continueBatchChangePrice();
    },
    continueBatchChangePrice() {
      let funFlg = true;
      let choose_list = [...this.choose_list];
      choose_list.map(choose => {
        let price = 0;
        switch (this.priceAdjustVal) {
          case 'finalPrice': // 直接设定
            price = this.batchPrice;
            break;
          case 'addPrice': // 加价
            price = +choose[this.priceTypeVal] + this.batchPrice;
            if (price < 0 || price > 999999.99) {
              funFlg = false;
              demo.msg('warning', `'${choose.name}'的商品价格加价后超过999999.99`);
              return;
            }
            break;
          case 'subPrice': // 减价
            price = +choose[this.priceTypeVal] - this.batchPrice;
            if (price < 0) {
              funFlg = false;
              demo.msg('warning', `'${choose.name}'的商品价格减价后低于0`);
              return;
            }
            break;
          case 'addPricePer': // 原价上浮
            price = +((+choose[this.priceTypeVal]) * (100 + this.batchPrice) / 100).toFixed(2);
            if (price > 999999.99) {
              funFlg = false;
              demo.msg('warning', `'${choose.name}'的商品价格上浮后超过999999.99`);
              return;
            }
            break;
          case 'subPricePer': // 原价下浮
            price = +((+choose[this.priceTypeVal]) * (100 - this.batchPrice) / 100).toFixed(2);
            if (price < 0) {
              funFlg = false;
              demo.msg('warning', `'${choose.name}'的商品价格下浮后不能低于0`);
              return;
            }
            break;
          case 'equalVipPrice': // 售价等于会员价
            price = choose.vipPrice;
            break;
          case 'equalSalePrice': // 会员价等于售价
            price = choose.salePrice;
            break;
          case 'equalPurchasePrice': // 售价/会员价等于进货价(保留两位小数 & 往上进位)
            price = Math.ceil(choose.purPrice * 100) / 100;
            break;
          default:
            break;
        }
        choose[this.priceTypeVal] = price;
      });
      this.sonarBatchPrice(funFlg, choose_list);
    },
    batchSetPrice() { // 批量修改价格
      if (this.choose_list.length > 0) {
        demo.actionLog(logList.clickBatchChangePrice);
        this.showBatchSetPrice = true;
        this.initPriceAdjustList();
      } else {
        demo.msg('warning', this.$msg.nothing_selected);
      }
    },
    /**
     * 初始化调价方式
     */
    initPriceAdjustList() {
      this.priceAdjustList = [{label: '直接设定', value: 'finalPrice', remark: ''}, {label: '加价', value: 'addPrice', remark: '原价格加固定金额'},
        {label: '减价', value: 'subPrice', remark: '原价格减固定金额'}, {label: '原价上浮', value: 'addPricePer', remark: '原价格上浮固定百分比'},
        {label: '原价下浮', value: 'subPricePer', remark: '原价格下浮固定百分比'}];
      this.priceAdjustVal = '';
      this.priceAdjustRemark = ''; // 切换价格类型时，清空右下角备注展示
      this.showPriceInput = true; // 切换价格类型时，恢复价格输入框展示
    },
    sonarBatchPrice(funFlg, choose_list) {
      if (funFlg && choose_list.length > 0) {
        let data = {chooseList: choose_list, priceTypeVal: this.priceTypeVal};
        console.log(data, 'sonarBatchPrice data');
        goodService.setGoodsPrice(data, () => {
          this.showBatchSetPrice = false;
          demo.msg('success', this.$msg.batch_update_price_success);
          this.handleGoodList();
        });
      }
    },
    // 批量删除，如果列表为空提示未选择商品，不为空则打开确认是否批量删除弹出框
    batchDelete() {
      demo.actionLog(logList.clickBatchDelete);
      if (!this.$employeeAuth('delete_products')) {
        demo.msg('warning', '没有设置权限');
        return;
      }
      if (this.choose_list.length > 0) {
        this.showBatchDel = true;
      } else {
        demo.msg('warning', this.$msg.nothing_selected);
      }
    },
    // 批量删除商品
    continueBatchBan() {
      var fingerprint = [];
      for (var i = 0; i < this.choose_list.length; i++) {
        fingerprint.push(this.choose_list[i].fingerprint);
      }
      goodService.delete({fingerprint: `'${fingerprint.join("','")}'`, clear: this.isDelStock ? 1 : 0}, () => {
        demo.msg('success', this.$msg.batch_delete_product_success);
        // 查询是否有称重商品
        this.$refs.typeMenu.update();
        this.pagenum = 1;
        this.handleGoodList();
      }, () => {
        demo.msg('warning', '批量删除商品失败!');
      });
    },
    makeBarcode() { // 自动生成条码
      if (!this.choose_list.length) {
        demo.msg('warning', this.$msg.nothing_selected);
      }
      var fingerprint = [];
      for (var i = 0; i < this.choose_list.length; i++) {
        if (!this.choose_list[i].code) {
          fingerprint.push(this.choose_list[i].fingerprint);
        }
      }
      if (!fingerprint.length) {
        demo.msg('warning', '请选择无条码商品');
        return;
      }
      goodService.generateCodes({fingerprint: `'${fingerprint.join("','")}'`}, () => {
        this.keyword = '';
        this.handleGoodList();
      });
    },
    // 打开分类管理界面
    setshowSelManage() {
      this.SET_SHOW({
        agreeChooseCategory: false
      });
      this.SET_SHOW({
        showSelManage: true
      });
    },
    // 商品分裂发生变化
    typeChange() {
      if (this.type === '-99') {
        this.keyword = '';
        this.date_picker = {
          date_from: '',
          date_to: ''
        };
      }
      this.handleGoodList();
    },
    // 根据分类调用不同方法
    handleGoodList() {
      if (this.type === '-99') {
        this.getHotList();
      } else {
        this.getProdList();
      }
    },
    // 获取热销商品列表
    getHotList() {
      goodService.hot((res) => {
        const list = demo.t2json(res);
        this.hotList = list; // 热销列表
        this.tableData = JSON.parse(JSON.stringify(this.hotList)).splice((this.pagenum - 1) * this.limit, this.limit);
        this.total = list.length;
      });
    },
    // 获取产品列表
    getProdList() {
      console.log(this.supplier, '---------------11111111111111111111');
      // 如果进行搜索自动切换为全部分类
      if (this.type === '-99' && (this.keyword || this.date_picker.date_from || this.date_picker.date_to)) {
        this.type = '';
      }
      if (this.type === '-99') {
        return;
      }
      if (this.pinyin) {
        return;
      }
      var data = {
        pset: this.hidSettingValue,
        all: this.supplierObject.all,
        notSupplier: this.supplierObject.notSupplier,
        supplierList: this.supplierObject.supplierList,
        isGroup: false,
        type: this.type,
        condition: this.keyword,
        limit: this.limit,
        selectDel: this.selectDel,
        getDel: this.getDel,
        offset: Number((this.pagenum - 1) * this.limit)
      };
      if (this.date_picker.date_from) {
        data['from'] = this.date_picker.date_from;
      }
      if (this.date_picker.date_to) {
        data['to'] = this.date_picker.date_to;
      }
      if (this.sortOrder) { // 列头排序
        data.orderBy = [
          {
            column: 'a.' + this.sortOrder.prop,
            order: this.sortOrder.order === 'descending' ? 'desc' : 'asc'
          }
        ];
      } else {
        data.orderBy = [
          {
            column: this.sortValue || 'a.create_at',
            order: this.sortValue === 'a.first_letters' ? 'asc' : 'desc'
          }
        ];
      }
      console.log(data, 'goodSearch data++');
      var that = this;
      goodService.search(data, function(res) {
        var json = demo.t2json(res);
        console.log('json', json);
        that.tableData = json;
      });
      goodService.searchCnt(data, function (res) {
        setTimeout(function() {
          that.total = Number(demo.t2json(res)[0].cnt);
        }, 10);
      });
    },
    handleCurrentChange(val) {
      this.pagenum = val;
      if (this.type === '-99') {
        this.tableData = JSON.parse(JSON.stringify(this.hotList)).splice((val - 1) * this.limit, this.limit);
      } else {
        this.getProdList();
      }
    },
    handleSizeChange() {
      if (this.type === '-99') {
        this.tableData = JSON.parse(JSON.stringify(this.hotList)).splice((this.pagenum - 1) * this.limit, this.limit);
      } else {
        this.getProdList();
      }
    },
    // 选择的单条商品
    chooseOneGoods(row, column) {
      console.log(column, '------------column');
      if (column.label === '供应商') {
        return;
      }
      if (!this.$employeeAuth('import_products')) {
        return;
      }
      row.item = [_.cloneDeep(row)];
      console.log(row, '单条商品详细信息');
      this.SET_SHOW({ goodsDetail: row });
      this.SET_SHOW({ showAddGoods: true });
      this.SET_SHOW({ batchUpdateGoodsType: false });
      this.SET_SHOW({ batchUpdateGoodsUnit: false });
    },
    editType() {
      this.SET_SHOW({ showTypeManage: true });
      demo.actionLog(logList.clickEditCategory);
    },
    setScales() {
      demo.actionLog(logList.clickScaleManage);
      this.SET_SHOW({ showScalesManage: true, isGoods: false });
    }
  },
  // 从vuex中获取变量
  computed: {
    ...mapState({
      goodsHomeStock: state => state.show.goodsHomeStock,
      specs: state => state.info.specs,
      returnGoodsDetail: state => state.show.returnGoodsDetail,
      addGoodsCategory: state => state.show.addGoodsCategory,
      batchUpdateGoodsType: state => state.show.batchUpdateGoodsType,
      batchUpdateGoodsTypeId: state => state.show.batchUpdateGoodsTypeId,
      batchUpdateGoodsTypeFingerprint: state => state.show.batchUpdateGoodsTypeFingerprint,
      batchUpdateGoodsUnit: state => state.show.batchUpdateGoodsUnit,
      batchUpdateGoodsUnitId: state => state.show.batchUpdateGoodsUnitId,
      batchUpdateGoodsUnitFingerprint: state => state.show.batchUpdateGoodsUnitFingerprint,
      showTypeManage: state => state.show.showTypeManage,
      isSyncing: state => state.show.isSyncing,
      scanGoodsCode: state => state.show.scanGoodsCode,
      showAddGoods: state => state.show.showAddGoods,
      loginInfo: state => state.show.loginInfo,
      showSelManage: state => state.show.showSelManage,
      setting_label_printer: state => state.show.setting_label_printer,
      setting_tip_printer: state => state.show.setting_tip_printer,
      setting_tag_printer: state => state.show.setting_tag_printer,
      username: state => state.show.username,
      devicecode: state => state.show.devicecode,
      codeTimer: state => state.show.codeTimer,
      delayedTime: state => state.show.delayedTime,
      clickInterval: state => state.show.clickInterval,
      selectedSuppliersDetail: state => state.show.selectedSuppliersDetail,
      sys_uid: state => state.show.sys_uid,
      sys_sid: state => state.show.sys_sid,
      isChangeCategory: state => state.show.isChangeCategory,
      labelPrinterCopies: state => state.show.labelPrinterCopies,
      tipPrinterCopies: state => state.show.tipPrinterCopies,
      setLabelDefault: state => state.show.setLabelDefault,
      setTipDefault: state => state.show.setTipDefault,
      sale_price_fontsize: state => state.show.sale_price_fontsize,
      vip_price_fontsize: state => state.show.vip_price_fontsize,
      labelItem: state => state.show.labelItem,
      printVipPrice: state => state.show.printVipPrice,
      cols: state => state.show.print_cols_label,
      labelPrintMap: state => state.show.labelPrintMap,
      isStockInventory: state => state.show.isStockInventory,
      isTransverse: state => state.show.isTransverse,
      codePrintValue: state => state.show.codePrintValue,
      tagModels: state => state.show.tagModels,
      tagPrintCols: state => state.show.tagPrintCols,
      tagIsTransverse: state => state.show.tagIsTransverse,
      zgznActive: state => state.show.zgznActive,
      scanerObj: state => state.show.scanerObj,
      showSupplierDialgEdit: state => state.show.showSupplierDialgEdit,
      isIndeterminate: function() {
        return this.checkedCities.length > 0 && this.checkedCities.length < (this.category_del_list.length + 2);
      },
      checkAll: function() {
        return this.checkedCities.length === (this.category_del_list.length + 2);
      }
    })
  },
  beforeDestroy() {
    clearInterval(this.scanTimeer);
    this.scanTimeer = null;
  }
};
</script>
