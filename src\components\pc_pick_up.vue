<template>
  <div style="position:relative">
    <div
      class="table_container1"
      v-show="!showRecord"
    >
      <el-table
        :data="tableData"
        stripe
        v-loading="dataLoading"
        :empty-text="!dataLoading ? '暂无数据' : ' '"
        style="width: 100%"
        :header-cell-style="headerStyle"
        :cell-style="headerStyle"
        :height="450"
        @selection-change="getSelectionGoods"
        class="tableBox"
      >
        <el-table-column
          type="selection"
          width="55"
        >
        </el-table-column>
        <el-table-column
          label="商品名称"
          width="350"
          prop="goods_name"
        >
        </el-table-column>
        <el-table-column
          prop="count"
          label="剩余数量"
        >
        </el-table-column>
        <el-table-column
          label="取件数量"
          width="110"
        >
          <template slot-scope="scope">
            <el-input
              :id="scope.$index"
              v-model="scope.row.pick_up_num"
              @input="scope.row.pick_up_num = $intMaxMinLimit({data: scope.row.pick_up_num, max: 99999, min: 0})"
              @focus="selectText(scope.$index)"
              @blur="checkValue(scope.row)"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom1">
        共<span style="color:#d5aa76">&nbsp;{{goods_count}}&nbsp;</span>款商品
      </div>
    </div>
    <!--存取记录-->
    <div
      class="table_container1"
      v-show="showRecord"
    >
      <el-table
        :data="recordData"
        v-loading="dataLoading"
        :empty-text="!dataLoading ? '暂无数据' : ' '"
        stripe
        style="width: 100%"
        :header-cell-style="headerStyle"
        :cell-style="headerStyle"
        :height="450"
        class="tableBox"
      >
        <el-table-column
          prop="inOutName"
          label="寄/取"
          width="80"
        >
        </el-table-column>
        <el-table-column
          label="商品名称"
          width="200"
          prop="goods_name"
        >
        </el-table-column>
        <el-table-column
          prop="count"
          label="数量"
        >
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="时间"
          width="180"
          :formatter="timeStampToDate"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          width="100"
          align="center"
        >
        <template slot-scope="scope">
          <div class="patchwork" @click="patchwork(scope.row)">打印</div>
        </template>
        </el-table-column>
      </el-table>
      <div class="table_bottom1">
        共<span style="color:#d5aa76">&nbsp;{{record_count}}&nbsp;</span>条记录
      </div>
    </div>
    <div class="btn_container1">
      <div
        class="btn-record"
        @click="showRecordHistory"
        :class="[showRecord?'hide':'']"
      >存取记录</div>
      <div style="display:flex;align-items:center">
        <div class="pick-ip-ticket-container" @click="selectPrintTicket" v-if="!showRecord">
          <img v-if="checkPrintTicket" src="../image/pc_goods_checkbox2.png" class="cb">
          <img v-else src="../image/pc_goods_checkbox1.png" class="cb">
          <div class="cb-text">取件完成打印小票</div>
        </div>
        <div
          :class="['btn_cancel',showRecord?'hide':'']"
          @click="cancel"
        >取消</div>
        <div
          class="btn_confirm"
          @click="showRecord ? back() : pickUp()"
        >{{showRecord ? '返回' : '取件'}}</div>
      </div>
    </div>
    <div
      class="dialog_close"
      @click="closeDialog"
    >×</div>
  </div>
</template>

<script>
import { Input, Table, TableColumn } from 'element-ui';
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import { showToast, timeStampToDate } from '../utils/util';
import { INVENTORY_DEAL_TYPE } from '@/utils/inventoryDealType';
import goodService from '../common/service/goodService';
export default {
  props: {
    vip_id: {
      type: Number,
      required: true
    },
    vip_name: {
      type: String,
      required: true
    }
  },
  components: {
    [Table.name]: Table,
    [TableColumn.name]: TableColumn,
    [Input.name]: Input
  },
  data () {
    return {
      dataLoading: false,
      showRecord: false,
      pickupClick: false,
      goods_count: 0,
      record_count: 0,
      tableData: null,
      recordData: null,
      checkPrintTicket: false,
      nameObj: {}
    };
  },
  created () {
    this.getPickUpData();
    this.getPrintTicketSetting();
  },
  methods: {
    ...mapActions([SET_SHOW]),
    /**
     * 右上角叉号点击事件
     */
    closeDialog () {
      if (this.showRecord) {
        this.showRecord = false;
      } else {
        this.$emit('dismissPickUpDialog');
      }
    },

    /**
     * 勾选/取消打印小票
     */
    selectPrintTicket() {
      this.checkPrintTicket = !this.checkPrintTicket;
      settingService.put({ key: settingService.key.pickUpPrintTicket, value: this.checkPrintTicket });
      demo.actionLog({ page: 'pc_pick_up', action: 'printTicket', description: `会员_取件_打印小票开关：${this.checkPrintTicket ? '开' : '关'}` });
    },

    /**
     * 补打
     */
    patchwork(info) {
      demo.actionLog({ page: 'pc_pick_up', action: 'clickPatchwork', description: `会员_取件_存取记录_补打${info.inOut === 1 ? '寄件单' : '取件单'}` });
      // 根据uid查询本地clerk表里的工号employee_number
      clerkService.getEmployeeNumberByUid([{"uid": info.createUser}], ['uid'], rs => {
        // console.log('员工信息', demo.t2json(rs)[0]);
        const data = {
          time: info.createTime,
          operator: demo.t2json(rs)[0].uid_no,
          vipName: this.vip_name,
          printTime: timeStampToDate(null, 'yyyy-MM-dd HH:mm:ss'),
          goods: [{
            "商品名称": info.goods_name,
            "数量": ' '
          }, {
            "商品名称": ' ',
            "数量": info.count.toString()
          }]
        };
        pos.printer.printMailTicket(data, info.inOut === 1 ? '寄件单（补）' : '取件单（补）');
      });
    },
    /**
     * 获取取件是否勾选打印小票配置
     */
    getPrintTicketSetting() {
      settingService.get({ key: settingService.key.pickUpPrintTicket }, res => {
        console.log('取件是否勾选打印小票配置', res);
        this.checkPrintTicket = (res[0] && res[0].value === 'true');
      });
    },
    headerStyle ({ columnIndex }) {
      if (columnIndex === 0 || columnIndex === 2 || columnIndex === 3) {
        return 'text-align:center';
      }
    },
    /**
     * 输入框失焦时校验值
     */
    checkValue (obj) {
      if (isNaN(obj.pick_up_num) || Number(obj.pick_up_num) <= 0) {
        obj.pick_up_num = 1;
        return;
      }
      if (Number(obj.pick_up_num) > 99999) {
        obj.pick_up_num = 99999;
        return;
      }
      // 判断小数位数
      var pickUpNum = obj.pick_up_num + '';
      var pointIndex = pickUpNum.indexOf('.') + 1;
      if (pointIndex > 0) {
        // 存在小数点
        var precisionCount = pickUpNum.length - pointIndex;
        // 小数位数
        if (precisionCount === 1) {
          obj.pick_up_num = Number(Number(obj.pick_up_num).toFixed(1));
        } else if (precisionCount === 2) {
          obj.pick_up_num = Number(Number(obj.pick_up_num).toFixed(2));
        } else {
          obj.pick_up_num = Number(Number(obj.pick_up_num).toFixed(3));
        }
      }
    },
    /**
     * 获取取件列表数据
     */
    getPickUpData () {
      this.dataLoading = true;
      const param = {
        phone: this.sysUid,
        vipId: this.vip_id.toString(),
        sysSid: this.sysSid,
        systemName: $config.systemName
      };
      console.log('==取件参数', param);
      demo.$http
        .post(this.$rest.getAccessProducts, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(async rs => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
          const res = rs.data;
          console.log('==寄存商品列表', demo.t2json(JSON.stringify(res)));
          if (res.code === '0') {
            let goodsArr = res.data;
            if (goodsArr.length > 0) {
              await this.getGoodNameByFingerprint(goodsArr);
              this.tableData = goodsArr.map(good => {
                good = Object.assign(good, this.nameObj[good.fingerprint])
                good.pick_up_num = 1;
                return good;
              });
              console.warn('tableData', this.tableData)
              this.goods_count = this.tableData.length;
            } else {
              this.tableData = [];
              this.goods_count = 0;
            }
          } else {
            showToast(this, res.msg);
          }
        }).catch(() => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
        })
    },

    /**
     * 获取取件记录数据
     */
    getRecordData () {
      this.dataLoading = true;
      const param = {
        phone: this.sysUid,
        vipId: this.vip_id.toString(),
        sysSid: this.sysSid,
        systemName: $config.systemName
      };
      console.log('==记录参数', param);
      demo.$http
        .post(this.$rest.getAccessProductHistory, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(async rs => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
          const res = rs.data;
          console.log('==寄存记录', res);
          if (res.code === '0') {
            let goodsArr = demo.t2json(JSON.stringify(res.data));
            if (goodsArr.length > 0) {
              await this.getGoodNameByFingerprint(goodsArr);
              this.recordData = goodsArr.map(good => {
                good = Object.assign(good, this.nameObj[good.fingerprint])
                return good
              });
              console.log(this.recordData, '取件list数据');
              this.record_count = this.recordData.length;
            } else {
              this.recordData = [];
            }
            return;
          }
          showToast(this, res.msg);
        }).catch(() => {
          setTimeout(() => {
            this.dataLoading = false;
          }, this.delayedTime);
        })
    },
    /**
     * 根据fingerprint查询对应的商品
     */
    async getGoodNameByFingerprint(goods) {
      if (goods.length) {
        const fingerprints = [...new Set(goods.map(good => good.fingerprint))];
        const goodNames = await goodService.getGoodNameByFingerprint(fingerprints);
        console.warn(goodNames)
        goodNames.forEach(good => {
          good.goods_name = good.name
          this.nameObj[good.fingerprint] = good;
        });
      };
    },
    getGoodsByFingerprint () {
      return new Promise(resolve => {
        let param = {
          pset: '',
          type: '',
          fingerprint: this.goods_fingerprint_list,
          fingerprintFlg: false,
          vipGoods: true,
          selectDel: true,
          limit: 9999,
          offset: 0
        };
        console.log('==fingerprint参数', param);
        goodService.search(param, res => {
          let search_goods_list = demo.t2json(res);
          resolve(search_goods_list);
        });
      });
    },
    /**
     * 存取记录
     */
    showRecordHistory () {
      this.showRecord = true;
      this.$emit('changeDialogTitle', '存取记录');
    },
    /**
     * 取消
     */
    cancel () {
      this.$emit('dismissPickUpDialog');
    },
    back () {
      this.showRecord = false;
      this.$emit('changeDialogTitle', '取件');
    },
    /**
     * 获取选中的要取出的件
     */
    getSelectionGoods (val) {
      this.selectTableData = val;
    },
    /**
     * 取件
     */
    pickUp () {
      if (!this.selectTableData || this.selectTableData.length === 0) {
        return demo.msg('warning', '请选择要取件的商品');
      }
      for (let item of this.selectTableData) {
        if (item.pick_up_num > item.count) {
          demo.msg('warning', '取件数量超出剩余数量');
          item.pick_up_num = 1;
          return;
        }
      }
      if (this.pickupClick) {
        return;
      }
      let products = [];
      for (let item of this.selectTableData) {
        products.push({
          fingerprint: item.fingerprint,
          goodsName: item.goods_name,
          count: -(item.pick_up_num),
          remark: ''
        });
      }
      const param = {
        products: products,
        inOut: 2,
        vipId: this.vip_id,
        phone: this.sysUid,
        sysSid: this.sysSid,
        systemName: $config.systemName,
        createUser: this.loginInfo.uid,
        store: demo.$store.state.show.username
      };
      console.log('==取件参数', param);
      this.pickupClick = true;
      demo.$http
        .post(this.$rest.addAccessProduct, param, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: demo.$store.state.show.token
          },
          maxContentLength: Infinity,
          timeout: 60000
        })
        .then(rs => {
          const res = rs.data;
          console.log('==取件结果', res);
          this.pickupClick = false;
          if (res.code === '0') {
            // 减库存 start
            this.changeCurStock();
            // 减库存 end
            demo.msg('success', '取件成功');
            this.$emit('dismissPickUpDialog');
            // 判断是否打印小票
            if (this.checkPrintTicket) {
              this.printPickUpTicket(this.selectTableData)
            }
            return;
          }
          showToast(this, res.msg);
        })
        .catch(() => {
          this.pickupClick = false;
        });
    },

    /**
     * 打印取件单小票
     */
    printPickUpTicket(selectTableData) {
      console.log('取件商品：', selectTableData);
      demo.actionLog({ page: 'pc_mail', action: 'clickVipDetailPickUp', description: `寄件打印小票开关：${this.checkPrintTicket ? '开' : '关'}` });
      const goods = [];
      for (let i = 0; i < selectTableData.length; i++) {
        goods.push({
          "商品名称": selectTableData[i].goods_name,
          "提取剩余": ' '
        });
        goods.push({
          "商品名称": ' ',
          "提取剩余": `${Math.abs(selectTableData[i].pick_up_num)}/${selectTableData[i].count + selectTableData[i].pick_up_num}`
        });
      }
      const data = {
        time: timeStampToDate(null, 'yyyy-MM-dd HH:mm:ss'),
        operator: demo.$store.state.show.loginInfo.employeeNumber || '管理员',
        vipName: this.vip_name,
        printTime: timeStampToDate(null, 'yyyy-MM-dd HH:mm:ss'),
        goods: goods
      };
      pos.printer.printPickUpTicket(data, '取件单');
    },

    /**
     * 寄存库存对应
     */
    changeCurStock () {
      this.selectTableData.map(e => { e.pick_up_num = -e.pick_up_num; });
      var inventoryItems = _.cloneDeep(this.selectTableData);
      inventoryItems.forEach(item => {
        item.accountQty = +item.curStock;
        item.actualQty = item.curStock + item.pick_up_num;
        item.price = item.purPrice;
        item.goodFingerprint = item.fingerprint;
        // item.goodFingerprint = this.selectTableData.map(e => e.fingerprint).join("','");
      });
      var params = {
        "exec": 1,
        "dealType": INVENTORY_DEAL_TYPE.TAKEN_GOODS_INVENTORY,
        "updateStock": 1,
        "inventoryItems": inventoryItems,
        "remark": this.vip_name
      }
      inventoryService.insert(params, () => {
        // do nothing
      }, () => {
        // do nothing
      });
    },
    /**
     * 时间戳转日期
     */
    timeStampToDate (row) {
      let date = new Date(row.createTime);
      let year = date.getFullYear() + '-';
      let month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      let day = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
      let hours = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      let minutes = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
      let seconds = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return year + month + day + hours + minutes + seconds;
    }
  },
  watch: {
    showRecord (val) {
      if (val) {
        this.getRecordData();
      }
    }
  },
  computed: mapState({
    loginInfo: (state) => state.show.loginInfo,
    delayedTime: state => state.show.delayedTime,
    sysUid: (state) => state.show.sys_uid,
    sysSid: (state) => state.show.sys_sid
  })
};
</script>

<style lang="less">
.patchwork {
  color: @themeBackGroundColor;
  cursor: pointer;
}
.dialog_close {
  position: absolute;
  right: 11px;
  top: -64px;
  font-size: 30px;
  color: #8298A6;
  cursor: pointer;
  z-index: 9999;
}
.el-dialog__headerbtn .el-dialog__close {
  color: #8298A6;
  font-size: 22px;
  font-weight: bold;
}
.tableBox th{
  background: #FAFBFC;
  height: 50px;
  font-size: 16px;
}
.tableBox td{
  font-size: 16px;
}
.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
  color: #557485;
}
.table_container1{
  border: 1px solid #E5E7EB;
  .el-table__header-wrapper {
    height: 50px;
  }
}
.table_bottom1{
  display: flex;
  align-items: center;
  padding: 12px;
  font-size: 16px;
}
.btn_container1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  margin-left:20px;
  .pick-ip-ticket-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 44px;
    padding: 0 20px;
    cursor: pointer;
    .cb {
      width: 22px;
      height: 22px;
    }
    .cb-text {
      font-size: 17px;
      margin-left: 8px;
      user-select: none;
    }
  }
}
.btn-record {
  width: 120px;
  height: 44px;
  line-height: 44px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  color: @themeBackGroundColor;
  cursor: pointer;
  font-size: 16px;
  margin-left:-20px
}
.btn_cancel{
  width: 120px;
  height: 44px;
  line-height: 44px;
  border: 1px solid @themeBackGroundColor;
  border-radius: 4px;
  text-align: center;
  color: @themeBackGroundColor;
  cursor: pointer;
  font-size: 16px;
}
.btn_confirm{
  width: 120px;
  height: 44px;
  line-height: 44px;
  border-radius: 4px;
  text-align: center;
  color: white;
  background: linear-gradient(90deg, #D1B989 0%, #B39959 100%);
  margin-left: 20px;
  cursor: pointer;
  font-size: 16px;
}
.el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: #B39959;
  outline: 0;
}
.hide{
  visibility: hidden;
}
</style>
