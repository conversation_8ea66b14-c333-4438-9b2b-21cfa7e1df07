import rest from '@/config/rest';
export function checkVipIntegral(vipInfo) {
  return new Promise((resolve, reject) => {
    const params = Object.assign({
      systemName: 'zgzn',
      sysSid: '1',
      type: 3,
      originId: 4,
      localOperateTime: new Date().format('yyyy-MM-dd hh:mm:ss'),
      checkPoint: 1,
      useNegativeIntegral: 1
    }, vipInfo)
    demo.$http
      .post(rest.check_refund_integral, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(res => {
        resolve(res.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}

/**
 * 获取会员信息
 * @param {*} params
 * @returns
 */
export function getVipInfo(params) {
  return new Promise((resolve, reject) => {
    demo.$http
      .post(rest.pc_getVipById, params, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
      .then(res => {
        resolve(res.data.data);
      })
      .catch(error => {
        reject(error);
      });
  });
}
