﻿global.syncSqlApi = {
  getSales:
    `select id, uid, strftime('%Y-%m-%d', opt_date) as optOn, code, in_out as tye,refund_fingerprint,
      bill_amt as billAmt, disc_amt as discAmt, disc, pay_amt as payAmt, owe_amt as oweAmt, change_amt as changeAmt,
      remark, vipid, vipname, vipmobile, is_deleted as isDel, account_id as acctId, fingerprint as syncG, info1,info2,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt
    from sales
    where is_synced=0
    order by id
    limit {limit} offset {offset};`,
  getSaleItems:
    `with tmp_sales as (
      select fingerprint
      from sales
      where is_synced = 0
      order by id
      limit {limit} offset {offset}
    )
    select si.id, si.pur_price as oprc, si.price as prc, si.qty, si.amt, si.disc, si.item_disc as itemDisc, si.is_deleted as isDel,
    si.fingerprint as syncG, si.sale_fingerprint as saleSyncG, si.good_fingerprint as productSyncG, si.mprice as mprc
      from tmp_sales ts
      left join sale_items si 
      on ts.fingerprint = si.sale_fingerprint
      where si.is_synced = 0;`,
  getSaleBlendPays:
    `with tmp_sales as (
      select fingerprint
      from sales
      where is_synced = 0
      order by id
      limit {limit} offset {offset}
    )
    select sbp.id, sbp.pay_amt, sbp.remark, sbp.account_id as acctId, sbp.sale_fingerprint as saleFingerprint, sbp.fingerprint
    from tmp_sales as sales
    inner join sale_blend_pays as sbp
    on sales.fingerprint = sbp.sale_fingerprint;`,
  updateSales:
    `update sales set is_synced=1, sync_at='{syncAt}' where id in ({salesIds});
    update sale_items set is_synced={isSynced}
    where sale_fingerprint in (select s.fingerprint from sales s where s.id in ({salesIds}));`,
  updateSaleItems:
    `update sale_items set is_synced=1 where id in ({saleItemsIds});`,
  dropTmpSales:
    `drop table if exists tmp_sales;
    drop table if exists tmp_sale_items;
    drop table if exists tmp_sale_blend_pays;`,
  salesImportInit:
    `insert into sales(
      uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint,
      vipid, vipname, vipmobile, is_deleted, is_synced, account_id, fingerprint, create_at, revise_at, sync_at, info1, info2
    )
    select uid, opt_on, code, tye, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint, 
      vipid, vipname, vipmobile, is_del, 1, acct_id, sync_g, create_at, revise_at, sync_at, info1, info2
    from tmp_sales;
    insert into sale_items(
      pur_price, price, qty, amt, disc, item_disc, is_deleted, is_synced, sale_fingerprint, good_fingerprint, fingerprint, mprice
    )
    select oprc, prc, qty, amt, disc, item_disc, is_del, 1, sale_sync_g, product_sync_g, sync_g, mprc
    from tmp_sale_items;
    insert into sale_blend_pays(account_id, pay_amt, remark, sale_fingerprint, fingerprint)
    select acct_id, pay_amt, remark, sale_fingerprint, fingerprint
    from tmp_sale_blend_pays;`,
  salesImport:
    `insert into sales(
      uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint,
      vipid, vipname, vipmobile, is_deleted, is_synced, account_id, fingerprint, create_at, revise_at, sync_at, info1, info2
    )
    select uid, opt_on, code, tye, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint, 
      vipid, vipname, vipmobile, is_del, 1, acct_id, sync_g, create_at, revise_at, sync_at, info1, info2
    from tmp_sales
    order by sync_at
    on conflict (fingerprint) do update set 
      is_deleted = excluded.is_deleted,
      sync_at = excluded.sync_at
    where is_synced=1;
    insert into sale_items(
      pur_price, price, qty, amt, disc, item_disc, is_deleted, is_synced, sale_fingerprint, good_fingerprint, fingerprint, mprice
    )
    select oprc, prc, qty, amt, disc, item_disc, is_del, 1, sale_sync_g, product_sync_g, sync_g, mprc
    from tmp_sale_items
    where 1=1
    on conflict (fingerprint) do update set
      is_deleted = excluded.is_deleted
    where is_synced=1;
    insert into sale_blend_pays(account_id, pay_amt, remark, sale_fingerprint, fingerprint)
    select blend.acct_id, blend.pay_amt, blend.remark, blend.sale_fingerprint, blend.fingerprint
    from tmp_sale_blend_pays as blend
    inner join sales
    on blend.sale_fingerprint = sales.fingerprint
    where sales.is_synced=1
    order by sales.sync_at
    on conflict (fingerprint) do update set
      account_id = excluded.account_id,
      pay_amt = excluded.pay_amt,
      remark = excluded.remark,
      sale_fingerprint = excluded.sale_fingerprint;`,
  syncTmpSalesTruncate:
    `delete from sync_sales; 
    update sqlite_sequence set seq = 0 where name = 'sync_sales';
    delete from sync_sale_items;
    update sqlite_sequence set seq = 0 where name = 'sync_sale_items';
    delete from sync_sale_blend_pays;
    update sqlite_sequence set seq = 0 where name = 'sync_sale_blend_pays';`,
  syncTmpSalesInsert:
    `insert into sync_sales(uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint,
      vipid, vipname, vipmobile, is_deleted, account_id, fingerprint, create_at, revise_at, sync_at, info1, info2) values `,
  syncTmpSalesInsertValues:
    `({uid}, {optOn}, {code}, {tye}, round({billAmt}, 2), round({discAmt}, 2), {disc}, round({payAmt}, 2), round({oweAmt}, 2),
      round({changeAmt}, 2), {remark},{refundFingerprint}, {vipid}, {vipname}, {vipmobile}, {isDel}, {acctId}, {syncG}, {createAt}, {reviseAt}, {syncAt}, {info1}, {info2})`,
  syncTmpSaleItemsInsert:
    `insert into sync_sale_items(pur_price, price, qty, amt, disc, item_disc, is_deleted, 
      sale_fingerprint, good_fingerprint, fingerprint, mprice) values `,
  syncTmpSaleItemsInsertValues:
    `(round({oprc}, 6), round({prc}, 2), round({qty}, 3), {amt}, {disc}, {itemDisc}, {isDel}, 
      {saleSyncG}, {productSyncG}, {syncG}, {mprc})`,
  syncTmpSaleBlendPaysInsert:
    `insert into sync_sale_blend_pays(account_id, pay_amt, remark, sale_fingerprint, fingerprint) values `,
  syncTmpSaleBlendPaysInsertValues:
    `({acctId}, round({payAmt}, 2), {remark}, {saleFingerprint}, {fingerprint})`,
  syncSalesInsertOrUpdate:
    `insert into sales(
      uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint,
      vipid, vipname, vipmobile, is_deleted, is_synced, account_id, fingerprint, create_at, revise_at, sync_at,info1,info2
      )
    select uid, opt_date, code, in_out, bill_amt, disc_amt, disc, pay_amt, owe_amt, change_amt, remark, refund_fingerprint, 
           vipid, vipname, vipmobile, is_deleted, 1, account_id, fingerprint, create_at, revise_at, sync_at, info1,info2
    from sync_sales
    order by id
    ON CONFLICT (fingerprint) do 
    UPDATE SET 
      is_deleted = excluded.is_deleted,
      sync_at = excluded.sync_at
    where is_synced=1;
    `,
  syncSaleItemsInsertOrUpdate:
    `insert into sale_items(
          pur_price, price, qty, amt, disc, item_disc, is_deleted, is_synced,sale_fingerprint, good_fingerprint, fingerprint, mprice
      )
    select pur_price, price, qty, amt, disc, item_disc, is_deleted, 1,sale_fingerprint, good_fingerprint, fingerprint, mprice
    from sync_sale_items
    order by id
    ON CONFLICT (fingerprint) do 
    UPDATE SET 
      is_deleted = excluded.is_deleted
    where is_synced=1;
    `,
  syncSaleBlendPaysInsertOrUpdate:
    `insert into sale_blend_pays(account_id, pay_amt, remark, sale_fingerprint, fingerprint)
    select blend.account_id, blend.pay_amt, blend.remark, blend.sale_fingerprint, blend.fingerprint
    from sync_sale_blend_pays as blend
    inner join sales on blend.sale_fingerprint = sales.fingerprint
    where sales.is_synced=1
    order by blend.id
    on conflict (fingerprint) do update set
      account_id = excluded.account_id,
      pay_amt = excluded.pay_amt,
      remark = excluded.remark,
      sale_fingerprint = excluded.sale_fingerprint;`,
  syncSalesAccountsUpdate:
    `with tmp_update as (
      select tmp.account_id, tmp.pay_amt, tmp.is_deleted
      from sync_sales as tmp
      inner join sales
      on tmp.fingerprint = sales.fingerprint
      and tmp.is_deleted != sales.is_deleted
      where sales.is_synced = 1
    ), tmp_insert as (
      select tmp.account_id, tmp.pay_amt
      from sync_sales as tmp
      left join sales
      on tmp.fingerprint = sales.fingerprint
      where sales.id is null
      and tmp.is_deleted = 0
    ), tmp_accts_all as (
      select account_id, pay_amt from tmp_update where is_deleted = 0
      union all
      select account_id, -pay_amt from tmp_update where is_deleted = 1
      union all
      select account_id, pay_amt from tmp_insert
    ), tmp_accts as (
      select account_id, sum(pay_amt) as pay_amt 
      from tmp_accts_all
      group by account_id
    )
    update accounts set cur_amt = cur_amt + (
      select pay_amt from tmp_accts
      where account_id = accounts.id
    )
    where fingerprint in (select account_id from tmp_accts);`,

  syncGetPurchases:
    `select purchases.id, uid, strftime('%Y-%m-%d', opt_date) optOn, code, in_out as tye, bill_amt as billAmt, disc_amt as discAmt,
      pay_amt as payAmt, owe_amt as oweAmt, purchases.disc, purchases.remark, purchases.is_deleted as isDel, account_id as acctId,
      supplier_fingerprint as companySyncG, purchases.fingerprint as syncG,
      strftime('%Y-%m-%d %H:%M:%S', purchases.create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', purchases.revise_at) as reviseAt,
      b.name as company_name,
      '{0}' as syncAt
     from purchases
     left join suppliers b on purchases.supplier_fingerprint = b.fingerprint
     where purchases.is_synced = 0
     order by purchases.id;`,
  syncGetPurItems:
    `select id, disc, price as prc, qty, amt, is_deleted as isDel, good_fingerprint as productSyncG,
      pur_fingerprint as purSyncG, fingerprint as syncG,
      '{1}' as syncAt
     from purchase_items
     where pur_fingerprint in ({0})
     order by id;`,
  syncUpdatePurchases:
    `update purchases set is_synced={2}, sync_at='{1}' where id in ({0});
    update purchase_items set is_synced={2} where pur_fingerprint in (
      select fingerprint from purchases where id in ({0})
    );`,

  syncTmpPurchasesTruncate:
    `delete from sync_purchases; 
    update sqlite_sequence set seq = 0 where name = 'sync_purchases';
    delete from sync_purchase_items;
    update sqlite_sequence set seq = 0 where name = 'sync_purchase_items';`,
  syncTmpPurchasesInsert:
    `insert into sync_purchases(uid, opt_date, code, in_out, bill_amt, disc_amt, pay_amt, owe_amt, disc, remark, 
      is_deleted, account_id, supplier_fingerprint, fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpPurchasesInsertValues:
    `({uid}, {optOn}, {code}, {tye}, round({billAmt}, 2), round({discAmt} ,2), round({payAmt}, 2), round({oweAmt}, 2), {disc},
      {remark}, {isDel}, {acctId}, {companySyncG}, {syncG}, {createAt}, {reviseAt}, {syncAt})`,
  syncTmpPurchaseItemsInsert:
    `insert into sync_purchase_items(disc, price, qty, amt, is_deleted, good_fingerprint, pur_fingerprint, fingerprint) values `,
  syncTmpPurchaseItemsInsertValues:
    `({disc}, {prc}, round({qty}, 3), {amt}, {isDel}, {productSyncG}, {purSyncG}, {syncG})`,

  syncPurchasesUpdate:
    `update purchases set
      is_deleted=(select is_deleted from sync_purchases where fingerprint=purchases.fingerprint),
      sync_at=(select sync_at from sync_purchases where fingerprint=purchases.fingerprint)
     where is_synced=1     
     and fingerprint in (select distinct fingerprint from sync_purchases);`,
  syncPurchasesInsert:
    `insert into purchases(uid, opt_date, code, in_out, bill_amt, disc_amt, pay_amt, owe_amt, disc, remark, 
      is_deleted, is_synced, account_id, supplier_fingerprint, fingerprint, create_at, revise_at, sync_at)
     select uid, opt_date, code, in_out, bill_amt, disc_amt, pay_amt, owe_amt, disc, remark, 
      is_deleted, 1, account_id, supplier_fingerprint, fingerprint, create_at, revise_at, sync_at
     from sync_purchases
     where fingerprint not in (select fingerprint from purchases);`,
  syncPurItemsUpdate:
    `update purchase_items set
      is_deleted=(select is_deleted from sync_purchase_items where fingerprint=purchase_items.fingerprint)
     where is_synced=1
     and fingerprint in (select distinct fingerprint from sync_purchase_items); `,
  syncPurItemsInsert:
    `insert into purchase_items(disc, price, qty, amt, is_deleted, is_synced, good_fingerprint, pur_fingerprint, fingerprint)
     select disc, price, qty, amt, is_deleted, 1, good_fingerprint, pur_fingerprint, fingerprint
     from sync_purchase_items
     where fingerprint not in (select fingerprint from purchase_items);`,
  syncPurchasesAccountsUpdate:
    `with tmp_update as (
      select tmp.account_id, tmp.pay_amt, tmp.is_deleted
      from sync_purchases as tmp
      inner join purchases
      on tmp.fingerprint = purchases.fingerprint
      and tmp.is_deleted != purchases.is_deleted
      where purchases.is_synced = 1
    ), tmp_insert as (
      select tmp.account_id, tmp.pay_amt
      from sync_purchases as tmp
      left join purchases
      on tmp.fingerprint = purchases.fingerprint
      where purchases.id is null
      and tmp.is_deleted = 0
    ), tmp_accts_all as (
      select account_id, pay_amt from tmp_update where is_deleted = 1
      union all
      select account_id, -pay_amt from tmp_update where is_deleted = 0
      union all
      select account_id, -pay_amt from tmp_insert
    ), tmp_accts as (
      select account_id, sum(pay_amt) as pay_amt 
      from tmp_accts_all
      group by account_id
    )
    update accounts set cur_amt = cur_amt + (
      select pay_amt from tmp_accts
      where account_id = accounts.fingerprint
    )
    where fingerprint in (select account_id from tmp_accts);`,

  syncGetInventories:
    `select id, uid, strftime('%Y-%m-%d', opt_date) as optOn, code, account_qty as accountQty, actual_qty as actualQty,
      diff_qty as diffQty, diff_amt as diffAmt, deal_type as dealType, remark, is_deleted as isDel, fingerprint as syncG, 
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
     from inventories 
     where is_synced=0
     order by id;`,
  syncGetInventoryItems:
    `select id, account_qty as accountQty, actual_qty as actualQty, diff_qty as diffQty, diff_amt as diffAmt, price as prc, 
      is_deleted as isDel, good_fingerprint as productSyncG, fingerprint as syncG, inventory_fingerprint as inventorySyncG,
      '{1}' as syncAt
     from inventory_items
     where inventory_fingerprint in ({0})
     order by id;`,
  syncUpdateInventories:
    `update inventories set is_synced={2}, sync_at='{1}' where id in ({0});
    update inventory_items set is_synced={2} where inventory_fingerprint in (
      select fingerprint from inventories where id in ({0})
    );`,

  syncTmpInventoriesTruncate:
    `delete from sync_inventories; 
    update sqlite_sequence set seq = 0 where name = 'sync_inventories';
    delete from sync_inventory_items;
    update sqlite_sequence set seq = 0 where name = 'sync_inventory_items';`,
  syncTmpInventoriesInsert:
    `insert into sync_inventories(uid, opt_date, code, account_qty, actual_qty, diff_qty, diff_amt, deal_type, remark, is_deleted,
      fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpInventoriesInsertValues:
    `({uid}, {optOn}, {code}, round({accountQty}, 3), round({actualQty}, 3), round({diffQty}, 3), round({diffAmt}, 2),
      {dealType}, {remark}, {isDel}, {syncG}, {createAt}, {reviseAt}, {syncAt})`,
  syncTmpInventoryItemsInsert:
    `insert into sync_inventory_items(account_qty, actual_qty, diff_qty, diff_amt, price, is_deleted,
      good_fingerprint, inventory_fingerprint, fingerprint) values `,
  syncTmpInventoryItemsInsertValues:
    `(round({accountQty}, 3), round({actualQty}, 3), round({diffQty}, 3), {diffAmt}, {prc}, {isDel},
      {productSyncG}, {inventorySyncG}, {syncG})`,

  syncInventoriesUpdate:
    `update inventories set
      is_deleted=(select is_deleted from sync_inventories where fingerprint=inventories.fingerprint),
      sync_at=(select sync_at from sync_inventories where fingerprint=inventories.fingerprint)
     where is_synced=1
     and fingerprint in (select distinct fingerprint from sync_inventories);`,
  syncInventoriesInsert:
    `insert into inventories(uid, opt_date, code, account_qty, actual_qty, diff_qty, diff_amt, 
      deal_type, remark, is_deleted, is_synced, fingerprint, create_at, revise_at, sync_at)
     select uid, opt_date, code, account_qty, actual_qty, diff_qty, diff_amt, 
      deal_type, remark, is_deleted, 1, fingerprint, create_at, revise_at, sync_at
     from sync_inventories
     where fingerprint not in (select fingerprint from inventories);`,
  syncInventoryItemsUpdate:
    `update inventory_items set
      is_deleted=(select is_deleted from sync_inventory_items where fingerprint=inventory_items.fingerprint)
     where is_synced=1
     and fingerprint in (select distinct fingerprint from sync_inventory_items);`,
  syncInventoryItemsInsert:
    `insert into inventory_items(account_qty, actual_qty, diff_qty, diff_amt, price, is_deleted, is_synced,
      good_fingerprint, fingerprint, inventory_fingerprint)
     select account_qty, actual_qty, diff_qty, diff_amt, price, is_deleted, 1, good_fingerprint, fingerprint, inventory_fingerprint
     from sync_inventory_items
     where fingerprint not in (select fingerprint from inventory_items);`,
  syncGetGoods:
    `select id, name, first_letters as firstLetters, pinyin, major_code, code, image, pur_price as purPrc, sale_price as salePrc, vip_price as vipPrc, 
      init_stock as initStock, init_price as initPrc, init_amt as initAmt, min_stock as minStock, max_stock as maxStock, remark, packing, specs, info1,info2,
      is_vip_disc as isVipDisc, is_deleted as isDel, is_new as isNew, type_fingerprint as typeSyncG, unit_fingerprint as unitSyncG,
      fingerprint as syncG, create_at as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{syncAt}' as syncAt
    from goods
    where goods.is_synced=0
    order by id
    limit {limit} offset {offset};`,
  syncGetGoodsAttributes:
    `select 
      id, is_sale as isSale, "type", sort, strftime('%Y-%m-%d %H:%M:%S', manufacture_date) as manufactureDate, expiry_days as expiryDays,
      sale_count as saleCount, sale_time as saleTime, "options", is_set_sale as isSetSale, is_stock_management as isStockManagement,is_valuate as isValuate,
      valuate_info as valuateInfo, is_deleted as isDeleted, is_synced as isSynced, fingerprint, info1,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt, '{0}' as syncAt
    from goods_attributes
    where is_synced=0
    order by id;`,
  syncGetGoodsExtBarcode:
    ` select 
          id, good_fingerprint as productFingerprint, fingerprint, ext_barcode, is_deleted as isDel,
          is_synced, '{0}' as syncAt, info1, info2, create_by, revise_by,
          strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt
      from goods_ext_barcode
      where is_synced=0
      order by id;`,
  syncGetGoodsSuppliersCnt:
    `select count(1) as cnt from goods_suppliers;`,
  syncGetGoodsSuppliers:
    `select 
      id, supplier_fingerprint as companyFingerprint, is_del as isDel, fingerprint, create_by, revise_by,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt
    from goods_suppliers
    where is_synced=0
    order by id
    limit {limit};`,
  syncTmpGoodsAttributesTruncate:
    `delete from sync_goods_attributes; 
    update sqlite_sequence set seq = 0 where name = 'sync_goods_attributes';`,
  syncTmpGoodsAttributesInsert:
    `insert into sync_goods_attributes (is_sale, type, sort, manufacture_date, expiry_days, sale_count, sale_time, options, is_set_sale,
    is_stock_management, is_valuate, valuate_info, is_deleted, fingerprint, create_by, revise_by, create_at, revise_at, sync_at, info1) values `,
  syncTmpGoodsAttributesInsertValues:
    `({isSale}, {type}, {sort}, strftime('%Y-%m-%d', {manufactureDate}), {expiryDays}, {saleCount}, {saleTime}, {options}, {isSetSale}, {isStockManagement},
    {isValuate}, {valuateInfo}, {isDeleted}, {fingerprint}, {createBy}, {reviseBy}, {createAt}, {reviseAt}, {syncAt}, {info1})`,
  syncGoodsAttributesInsert:
    `insert into goods_attributes(is_sale, type, sort, manufacture_date, expiry_days, sale_count, sale_time, options, is_set_sale,
      is_stock_management, is_valuate, valuate_info, is_deleted, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at, info1)
    select is_sale, type, sort, manufacture_date, expiry_days, sale_count, sale_time, options, is_set_sale,
      is_stock_management, is_valuate, valuate_info, is_deleted, 1, fingerprint, create_by, revise_by, create_at, revise_at, sync_at, info1
    from sync_goods_attributes
    where 1=1
    on conflict (fingerprint) do update set
      is_sale=excluded.is_sale,
      type=excluded.type,
      sort=excluded.sort,
      manufacture_date=excluded.manufacture_date,
      expiry_days=excluded.expiry_days,
      sale_count=excluded.sale_count,
      sale_time=excluded.sale_time,
      options=excluded.options,
      is_set_sale=excluded.is_set_sale,
      is_stock_management=excluded.is_stock_management,
      is_valuate=excluded.is_valuate,
      valuate_info=excluded.valuate_info,
      is_deleted=excluded.is_deleted,
      is_synced=excluded.is_synced,
      create_by=excluded.create_by,
      revise_by=excluded.revise_by,
      create_at=excluded.create_at,
      revise_at=excluded.revise_at,
      sync_at=excluded.sync_at,
      info1=excluded.info1
    where is_synced=1;`,
  syncTmpGoodsExtBarcodeTruncate:
    `delete from sync_goods_ext_barcode; 
    update sqlite_sequence set seq = 0 where name = 'sync_goods_ext_barcode';`,
  syncTmpGoodsExtBarcodeInsert:
    `insert into sync_goods_ext_barcode ("good_fingerprint", "fingerprint", "ext_barcode", 
      "is_deleted", "sync_at", "info1", "info2", "create_at", "revise_at", "create_by", "revise_by") values `,
  syncTmpGoodsExtBarcodeInsertValues:
    `({productFingerprint}, {fingerprint}, {extBarcode}, {isDel}, {syncAt}, {info1}, {info2}, 
        {createAt}, {reviseAt}, {createBy}, {reviseBy})`,
  syncGoodsExtBarcodeInsertOrUpdate:
    `insert into goods_ext_barcode(
        "good_fingerprint", "fingerprint", "ext_barcode", "is_deleted", "is_synced", "sync_at", "info1", 
        "info2", "create_at", "revise_at", "create_by", "revise_by"
      )
    select "good_fingerprint", "fingerprint", "ext_barcode", "is_deleted", 1, "sync_at", "info1", 
            "info2", "create_at", "revise_at", "create_by", "revise_by"
    from sync_goods_ext_barcode
    order by id
    ON CONFLICT (fingerprint) do 
    UPDATE SET
      good_fingerprint = excluded.good_fingerprint,
      ext_barcode = excluded.ext_barcode,
      is_deleted = excluded.is_deleted,
      info1 = excluded.info1,
      info2 = excluded.info2,
      sync_at = excluded.sync_at
    where is_synced=1;
    `,
  syncTmpProductCompaniesTruncate:
    `delete from sync_goods_suppliers; 
    update sqlite_sequence set seq = 0 where name = 'sync_goods_suppliers';`,
  syncTmpProductCompaniesInsert:
    `insert into sync_goods_suppliers(
      supplier_fingerprint, is_del, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
    ) values `,
  syncTmpProductCompaniesInsertValues:
    `({companyFingerprint}, {isDel}, {fingerprint}, {createBy}, {reviseBy}, {createAt}, {reviseAt}, {syncAt})`,
  syncProductCompaniesSave:
    `insert into goods_suppliers(
      supplier_fingerprint, is_del, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
    )
    select supplier_fingerprint, is_del, 1, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
    from sync_goods_suppliers
    order by id
    on conflict(fingerprint) do update set
      supplier_fingerprint=excluded.supplier_fingerprint,
      is_del=excluded.is_del,
      revise_by=excluded.revise_by,
      revise_at=excluded.revise_at,
      sync_at=excluded.sync_at
    where is_synced=1;`,
  syncUpdateGoods:
    `update goods set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncUpdateAttributes:
    `update goods_attributes set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncUpdateGoodsExtBarcode:
    `update goods_ext_barcode set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncUpdateGoodsSuppliers:
    `update goods_suppliers set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpGoodsTruncate:
    `delete from sync_goods; 
    update sqlite_sequence set seq = 0 where name = 'sync_goods';`,
  syncTmpGoodsInsert:
    `insert into sync_goods (name, first_letters, pinyin, major_code, code, image, pur_price, sale_price, vip_price, cur_stock, 
    init_stock, init_price, init_amt, min_stock, max_stock, remark, packing, specs, is_vip_disc, is_deleted,
    type_fingerprint, unit_fingerprint, fingerprint, create_at, revise_at, sync_at, info1, info2) values `,
  syncTmpGoodsInsertValues:
    `({name}, {firstLetters}, {pinyin}, {majorCode}, {code}, {image}, round({purPrc}, 6), round({salePrc}, 2), round({vipPrc}, 2),
    round({curStock}, 3), round({initStock}, 3), round({initPrc}, 2), round({initAmt}, 2), round({minStock}, 3), round({maxStock}, 3),
    {remark}, {packing}, {specs}, {isVipDisc}, {isDel}, {typeSyncG}, {unitSyncG}, {syncG}, {createAt}, {reviseAt}, {syncAt}, {info1}, {info2})`,
  syncGoodsUpdate:
    `update goods set
      name=(select name from sync_goods where fingerprint=goods.fingerprint),
      first_letters=(select first_letters from sync_goods where fingerprint=goods.fingerprint),
      pinyin=(select pinyin from sync_goods where fingerprint=goods.fingerprint),
      major_code=(select major_code from sync_goods where fingerprint=goods.fingerprint),
      code=(select code from sync_goods where fingerprint=goods.fingerprint),
      image=(select image from sync_goods where fingerprint=goods.fingerprint),
      pur_price=(select pur_price from sync_goods where fingerprint=goods.fingerprint),
      sale_price=(select sale_price from sync_goods where fingerprint=goods.fingerprint),
      vip_price=(select vip_price from sync_goods where fingerprint=goods.fingerprint),
      init_stock=(select init_stock from sync_goods where fingerprint=goods.fingerprint),
      init_price=(select init_price from sync_goods where fingerprint=goods.fingerprint),
      init_amt=(select init_amt from sync_goods where fingerprint=goods.fingerprint),
      min_stock=(select min_stock from sync_goods where fingerprint=goods.fingerprint),
      max_stock=(select max_stock from sync_goods where fingerprint=goods.fingerprint),
      remark=(select remark from sync_goods where fingerprint=goods.fingerprint),
      packing=(select packing from sync_goods where fingerprint=goods.fingerprint),
      specs=(select specs from sync_goods where fingerprint=goods.fingerprint),
      is_vip_disc=(select is_vip_disc from sync_goods where fingerprint=goods.fingerprint),
      is_deleted=(select is_deleted from sync_goods where fingerprint=goods.fingerprint),
      is_new=(select is_new from sync_goods where fingerprint=goods.fingerprint),
      type_fingerprint=(select type_fingerprint from sync_goods where fingerprint=goods.fingerprint),
      unit_fingerprint=(select unit_fingerprint from sync_goods where fingerprint=goods.fingerprint),
      fingerprint=(select fingerprint from sync_goods where fingerprint=goods.fingerprint),
      create_at=(select create_at from sync_goods where fingerprint=goods.fingerprint),
      revise_at=(select revise_at from sync_goods where fingerprint=goods.fingerprint),
      sync_at=(select sync_at from sync_goods where fingerprint=goods.fingerprint),
      info1=(select info1 from sync_goods where fingerprint=goods.fingerprint),
      info2=(select info2 from sync_goods where fingerprint=goods.fingerprint)
    where is_synced=1
    and fingerprint in (select distinct fingerprint from sync_goods);`,
  syncGoodsInsert:
    `insert into goods(name, first_letters, pinyin, major_code, code, image, pur_price, sale_price, vip_price, init_stock, init_price, 
      init_amt, min_stock, max_stock, remark, packing, specs, is_vip_disc, is_deleted, is_new, is_synced,
      type_fingerprint, unit_fingerprint, fingerprint, create_at, revise_at, sync_at,info1,info2)
    select name, first_letters, pinyin, major_code, code, image, pur_price, sale_price, vip_price, init_stock, init_price, 
      init_amt, min_stock, max_stock, remark, packing, specs, is_vip_disc, is_deleted, is_new, 1,
      type_fingerprint, unit_fingerprint, fingerprint, create_at, revise_at, sync_at,info1,info2
    from sync_goods
    where fingerprint not in (select fingerprint from goods);`,

  syncGetImages:
    `select id, url as filePath, "order", is_deleted as isDel, good_fingerprint as productSyncG, fingerprint syncG,
      strftime('%Y-%m-%d %H:%M:%S', create_at) createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) reviseAt,
      '{0}' as syncAt
     from images
     where is_synced = 0
     order by id;`,
  syncUpdateImages:
    `update images set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpImagesTruncate:
    `delete from sync_images;
    update sqlite_sequence set seq = 0 where name = 'sync_images';`,
  syncTmpImagesInsert:
    `insert into sync_images(url, "order", is_deleted, good_fingerprint, fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpImagesInsertValues:
    `({filePath}, {order}, {isDel}, {productSyncG}, {syncG}, {createAt}, {reviseAt}, {syncAt})`,
  syncImagesUpdate:
    `update images set
      url=(select url from sync_images where fingerprint=images.fingerprint),
      "order"=(select "order" from sync_images where fingerprint=images.fingerprint),
      is_deleted=(select is_deleted from sync_images where fingerprint=images.fingerprint),
      good_fingerprint=(select good_fingerprint from sync_images where fingerprint=images.fingerprint),
      create_at=(select create_at from sync_images where fingerprint=images.fingerprint),
      revise_at=(select revise_at from sync_images where fingerprint=images.fingerprint),
      sync_at=(select sync_at from sync_images where fingerprint=images.fingerprint)
     where is_synced=1
     and images.fingerprint in (select distinct fingerprint from sync_images);`,
  syncImagesInsert:
    `insert into images(url, "order", is_deleted, is_synced, good_fingerprint, fingerprint, create_at, revise_at, sync_at)
     select url, "order", is_deleted, 1, good_fingerprint, fingerprint, create_at, revise_at, sync_at
     from sync_images
     where fingerprint not in (select fingerprint from images);`,

  syncTmpStockTruncate:
    `delete from sync_stock;
    update sqlite_sequence set seq = 0 where name = 'sync_stock';`,
  syncTmpStockInsert:
    `insert into sync_stock(good_fingerprint, cur_stock, sync_at) values `,
  syncTmpStockInsertValues:
    `({productSyncG}, round({curStock}, 3), {syncAt})`,
  syncGoodsStockUpdate:
    `with tmp_all as (
      select good_fingerprint, sum(qty) as qty
      from purchase_items
      where is_synced!=1
      and is_deleted=0
      group by good_fingerprint
      union all
      select good_fingerprint, -sum(qty)
      from sale_items
      where is_synced!=1
      and is_deleted=0
      group by good_fingerprint
      union all
      select good_fingerprint, sum(diff_qty)
      from inventory_items
      where is_synced!=1
      and is_deleted=0
      group by good_fingerprint
    ), tmp_sum as (
      select good_fingerprint, sum(qty) as qty
      from tmp_all
      group by good_fingerprint
    ), tmp_final as (
      select sync_stock.good_fingerprint, sync_stock.sync_at,
        round(ifnull(sync_stock.cur_stock, 0) + ifnull(tmp_sum.qty, 0), 3) as cur_stock
      from sync_stock
      left join tmp_sum
      on sync_stock.good_fingerprint=tmp_sum.good_fingerprint
    )
    update goods set
      cur_stock = (select cur_stock from tmp_final where good_fingerprint=goods.fingerprint),
      stock_sync_at = (select sync_at from tmp_final where good_fingerprint=goods.fingerprint)
    where fingerprint in (select good_fingerprint from tmp_final);`,

  syncGetTypes:
    `select id, name, pinyin, sortno sortNo, is_deleted isDel, parent_fingerprint parentSyncG, fingerprint syncG,
      strftime('%Y-%m-%d %H:%M:%S', create_at) createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) revise_at,
      '{0}' as syncAt
     from types
     where is_synced = 0
     order by id;`,
  syncUpdateTypes:
    `update types set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpTypesTruncate:
    `delete from sync_types; 
    update sqlite_sequence set seq = 0 where name = 'sync_types';`,
  syncTmpTypesInsert:
    `insert into sync_types(name, pinyin, sortno, is_deleted, parent_fingerprint, fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpTypesInsertValues:
    `({name}, {pinyin}, {sortNo}, {isDel}, {parentSyncG}, {syncG}, {createAt}, {reviseAt}, {syncAt})`,
  syncTypesUpdate:
    `update types set
      name=(select name from sync_types where fingerprint=types.fingerprint),
      pinyin=(select pinyin from sync_types where fingerprint=types.fingerprint),
      sortno=(select sortno from sync_types where fingerprint=types.fingerprint),
      is_deleted=(select is_deleted from sync_types where fingerprint=types.fingerprint),
      parent_fingerprint=(select parent_fingerprint from sync_types where fingerprint=types.fingerprint),
      create_at=(select create_at from sync_types where fingerprint=types.fingerprint),
      revise_at=(select revise_at from sync_types where fingerprint=types.fingerprint),
      sync_at=(select sync_at from sync_types where fingerprint=types.fingerprint)
    where is_synced=1
    and fingerprint in (select distinct fingerprint from sync_types);`,
  syncTypesInsert:
    `insert into types(name, pinyin, sortno, is_deleted, is_synced, parent_fingerprint, fingerprint, create_at, revise_at, sync_at) 
    select name, pinyin, sortno, is_deleted, 1, parent_fingerprint, fingerprint, create_at, revise_at, sync_at
    from sync_types
    where fingerprint not in (select fingerprint from types);`,

  syncGetUnits:
    `select id, name, pinyin, is_deleted as isDel, fingerprint as syncG,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt,
      strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
    from units
    where is_synced=0
    order by id;`,
  syncUpdateUnits:
    `update units set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpUnitsTruncate:
    `delete from sync_units; 
    update sqlite_sequence set seq = 0 where name = 'sync_units';`,
  syncTmpUnitsInsert:
    `insert into sync_units(name, pinyin, is_deleted, fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpUnitsInsertValues:
    `({name}, {pinyin}, {isDel}, {syncG}, {createAt}, {reviseAt}, {syncAt})`,
  syncUnitsUpdate:
    `update units set
      name=(select name from sync_units where fingerprint=units.fingerprint),
      pinyin=(select pinyin from sync_units where fingerprint=units.fingerprint),
      is_deleted=(select is_deleted from sync_units where fingerprint=units.fingerprint),
      create_at=(select create_at from sync_units where fingerprint=units.fingerprint),
      revise_at=(select revise_at from sync_units where fingerprint=units.fingerprint),
      sync_at=(select sync_at from sync_units where fingerprint=units.fingerprint)
    where is_synced=1
    and fingerprint in (select distinct fingerprint from sync_units);`,
  syncUnitsInsert:
    `insert into units(name, pinyin, is_deleted, is_synced, fingerprint, create_at, revise_at, sync_at) 
    select name, pinyin, is_deleted, 1, fingerprint, create_at, revise_at, sync_at
    from sync_units
    where fingerprint not in (select fingerprint from units);`,

  syncGetStoreInfo:
    `select id, code, guid, name, addr, industry, contacter as contact, tel, phone, qq, wechat, strftime('%Y-%m-%d %H:%M:%S', create_at) create_at,
    strftime('%Y-%m-%d %H:%M:%S', revise_at) revise_at, is_deleted, is_synced as is_sync, fingerprint, discount_settings, settings,
    '{0}' as syncAt
    from storeinfo where is_synced=0;`,
  syncUpdateStoreInfo: `update storeinfo set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpStoreInfoTruncate:
    `delete from sync_store_info; 
    update sqlite_sequence set seq = 0 where name = 'sync_store_info';`,
  syncTmpStoreInfoInsert:
    `insert into sync_store_info (code, guid, name, addr, industry, contacter, tel, phone, qq, wechat, is_deleted, fingerprint,
      discount_settings, settings, create_at, revise_at, sync_at) values `,
  syncTmpStoreInfoInsertValues:
    `({code}, {guid}, {name}, {addr}, {industry}, {contact}, {tel}, '', {qq}, '', 0, '', {discountSettings}, {settings}, 
      {createAt}, {reviseAt}, {syncAt})`,
  syncStoreInfoUpdate:
    `update storeinfo set
      code=(select code from sync_store_info where guid=storeinfo.guid),
      name=(select name from sync_store_info where guid=storeinfo.guid),
      addr=(select addr from sync_store_info where guid=storeinfo.guid),
      industry=(select industry from sync_store_info where guid=storeinfo.guid),
      contacter=(select contacter from sync_store_info where guid=storeinfo.guid),
      tel=(select tel from sync_store_info where guid=storeinfo.guid),
      phone=(select phone from sync_store_info where guid=storeinfo.guid),
      qq=(select qq from sync_store_info where guid=storeinfo.guid),
      wechat=(select wechat from sync_store_info where guid=storeinfo.guid),
      is_deleted=(select is_deleted from sync_store_info where guid=storeinfo.guid),
      fingerprint='',
      discount_settings=(select discount_settings from sync_store_info where guid=storeinfo.guid),
      settings=(select settings from sync_store_info where guid=storeinfo.guid),
      create_at=(select create_at from sync_store_info where guid=storeinfo.guid),
      revise_at=(select revise_at from sync_store_info where guid=storeinfo.guid),
      sync_at=(select sync_at from sync_store_info where guid=storeinfo.guid)
    where is_synced=1
    and guid in (select distinct guid from sync_store_info);`,
  syncStoreInfoInsert:
    `insert into storeinfo (code, guid, name, addr, industry, contacter, tel, phone, qq, wechat, is_deleted, is_synced, fingerprint, 
      discount_settings, settings, create_at, revise_at, sync_at)
    select code, guid, name, addr, industry, contacter, tel, phone, qq, wechat, is_deleted, 1, fingerprint, 
      discount_settings, settings, create_at, revise_at, sync_at
    from sync_store_info
    where guid not in (select guid from storeinfo);`,

  // 交接班
  syncTmpShiftHistoriesTruncate:
    `delete from sync_shifthistories;
    update sqlite_sequence set seq = 0 where name = 'sync_shifthistories';`,
  syncTmpShiftHistoriesInsert:
    `insert into sync_shifthistories(uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge, cash_amt, 
      remark, fingerprint, info1, info2, create_by, revise_by, create_at, revise_at, sync_at) values `,
  syncTmpShiftHistoriesInsertValues:
    `({uid}, {employeeNumber}, {name}, {beginDate}, {endDate}, {salesAmt}, {payAmt}, {vipCharge}, {cashAmt},
    {remark}, {syncG}, {info1}, {info2},  {createBy}, {reviseBy}, {createAt}, {reviseAt}, {syncAt})`,
  syncShiftHistoriesUpdate:
    `update shifthistories set 
      uid=(select uid from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      employee_number=(select employee_number from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      name=(select name from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      begin_date=(select begin_date from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      end_date=(select end_date from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      sales_amt=(select sales_amt from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      pay_amt=(select pay_amt from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      vip_charge=(select vip_charge from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      cash_amt=(select cash_amt from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      remark=(select remark from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      info1=(select info1 from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      info2=(select info2 from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      create_by=(select create_by from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      revise_by=(select revise_by from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      create_at=(select create_at from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      revise_at=(select revise_at from sync_shifthistories where fingerprint=shifthistories.fingerprint),
      sync_at=(select sync_at from sync_shifthistories where fingerprint=shifthistories.fingerprint)
     where is_synced=1
     and fingerprint in (select distinct fingerprint from sync_shifthistories);`,
  syncShiftHistoriesInsert:
    `insert into shifthistories(uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge,
      cash_amt, remark, info1, info2, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at)
     select uid, employee_number, name, begin_date, end_date, sales_amt, pay_amt, vip_charge,
      cash_amt, remark, info1,  info2, 1, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
     from sync_shifthistories where fingerprint not in (select distinct fingerprint from shifthistories)
     order by id;`,
  syncGetShiftHistories:
    `select id, uid, employee_number, name, strftime('%Y-%m-%d %H:%M:%S', begin_date) beginDate, 
      strftime('%Y-%m-%d %H:%M:%S', end_date) endDate, sales_amt, pay_amt, vip_charge, cash_amt, remark, info1, info2,
      is_synced isSynced, fingerprint syncG, create_by createBy, revise_by reviseBy,
      strftime('%Y-%m-%d %H:%M:%S', create_at) createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) reviseAt,
      '{0}' as syncAt
    from shifthistories where is_synced=0;`,
  syncUpdateShiftHistories:
    `update shifthistories set is_synced=1, sync_at='{1}' where id in ({0});`,

  syncGetSuppliers:
    `select id, name, contacter as linkman, tel, mobile, birthday, wechat, qq, mail, addr, postcode, 
      init_amt as initAmt, cur_amt as curAmt, disc, remark, is_deleted as isDel, fingerprint as syncG, 
      create_by as createBy, revise_by as reviseBy,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
    from suppliers 
    where is_synced = 0 
    order by id;`,
  syncUpdateSuppliers:
    `update suppliers set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpSuppliersTruncate:
    `delete from sync_suppliers;
    update sqlite_sequence set seq = 0 where name = 'sync_suppliers';`,
  syncTmpSuppliersInsert:
    `insert into sync_suppliers(name, contacter, tel, mobile, birthday, wechat, qq, mail, addr, postcode, init_amt, cur_amt, 
      disc, remark, is_deleted, fingerprint, create_by, revise_by, create_at, revise_at, sync_at) values `,
  syncTmpSuppliersInsertValues:
    `({name}, {linkman}, {tel}, {mobile}, {birthday}, {wechat}, {qq}, {mail}, {addr}, {postcode}, {initAmt}, {curAmt}, 
      {disc}, {remark}, {isDel}, {syncG}, {createBy}, {reviseBy}, {createAt}, {reviseAt}, {syncAt})`,
  syncSuppliersUpdate:
    `update suppliers set 
      name=(select name from sync_suppliers where fingerprint=suppliers.fingerprint),
      contacter=(select contacter from sync_suppliers where fingerprint=suppliers.fingerprint),
      tel=(select tel from sync_suppliers where fingerprint=suppliers.fingerprint),
      mobile=(select mobile from sync_suppliers where fingerprint=suppliers.fingerprint),
      birthday=(select birthday from sync_suppliers where fingerprint=suppliers.fingerprint),
      wechat=(select wechat from sync_suppliers where fingerprint=suppliers.fingerprint),
      qq=(select qq from sync_suppliers where fingerprint=suppliers.fingerprint),
      mail=(select mail from sync_suppliers where fingerprint=suppliers.fingerprint),
      addr=(select addr from sync_suppliers where fingerprint=suppliers.fingerprint),
      postcode=(select postcode from sync_suppliers where fingerprint=suppliers.fingerprint),
      init_amt=(select init_amt from sync_suppliers where fingerprint=suppliers.fingerprint),
      cur_amt=(select cur_amt from sync_suppliers where fingerprint=suppliers.fingerprint),
      disc=(select disc from sync_suppliers where fingerprint=suppliers.fingerprint),
      remark=(select remark from sync_suppliers where fingerprint=suppliers.fingerprint),
      is_deleted=(select is_deleted from sync_suppliers where fingerprint=suppliers.fingerprint),
      fingerprint=(select fingerprint from sync_suppliers where fingerprint=suppliers.fingerprint),
      create_by=(select create_by from sync_suppliers where fingerprint=suppliers.fingerprint),
      revise_by=(select revise_by from sync_suppliers where fingerprint=suppliers.fingerprint),
      create_at=(select create_at from sync_suppliers where fingerprint=suppliers.fingerprint),
      revise_at=(select revise_at from sync_suppliers where fingerprint=suppliers.fingerprint),
      sync_at=(select sync_at from sync_suppliers where fingerprint=suppliers.fingerprint)
    where is_synced=1
    and fingerprint in (select distinct fingerprint from sync_suppliers);`,
  syncSuppliersInsert:
    `insert into suppliers (name, contacter, tel, mobile, birthday, wechat, qq, mail, addr, postcode, init_amt, cur_amt, 
      disc, remark, is_deleted, is_synced, fingerprint, create_by, revise_by, create_at, revise_at, sync_at) 
    select name, contacter, tel, mobile, birthday, wechat, qq, mail, addr, postcode, init_amt, cur_amt, 
      disc, remark, is_deleted, 1, fingerprint, create_by, revise_by, create_at, revise_at, sync_at
    from sync_suppliers where fingerprint not in (select distinct fingerprint from suppliers)
    order by create_at;`,

  syncTmpRecordBillsTruncate:
    `delete from sync_record_bills; 
    update sqlite_sequence set seq = 0 where name = 'sync_record_bills';`,
  syncTmpRecordBillsInsert:
    `insert into sync_record_bills(info, uid, is_deleted, fingerprint, create_at, revise_at, sync_at) values `,
  syncTmpRecordBillsInsertValues:
    `({info}, {uid}, {isDel}, {fingerprint}, {createAt}, {reviseAt}, {syncAt})`,
  syncRecordBillsUpdate:
    `update record_bills set 
      info=(select info from sync_record_bills where fingerprint=record_bills.fingerprint),
      create_at=(select create_at from sync_record_bills where fingerprint=record_bills.fingerprint),
      revise_at=(select revise_at from sync_record_bills where fingerprint=record_bills.fingerprint),
      sync_at=(select sync_at from sync_record_bills where fingerprint=record_bills.fingerprint),
      uid=(select uid from sync_record_bills where fingerprint=record_bills.fingerprint),
      is_deleted=(select is_deleted from sync_record_bills where fingerprint=record_bills.fingerprint),
      fingerprint=(select fingerprint from sync_record_bills where fingerprint=record_bills.fingerprint)
    where is_synced=1
    and fingerprint in (select distinct fingerprint from sync_record_bills);`,
  syncRecordBillsInsert:
    `insert into record_bills (info, uid, is_deleted, is_synced, fingerprint, create_at, revise_at, sync_at) 
    select info, uid, is_deleted, 1, fingerprint, create_at, revise_at, sync_at
    from sync_record_bills where fingerprint not in (select distinct fingerprint from record_bills)
    order by create_at;`,
  syncGetRecordBills:
    `select id, info, uid, is_deleted as isDel, fingerprint,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, 
      strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
    from record_bills 
    where is_synced = 0 
    order by id;`,
  syncUpdateRecordBills:
    `update record_bills set is_synced=1, sync_at='{1}' where id in ({0});`,
  syncTmpProductScaleTruncate:
    `delete from sync_product_scale; 
    update sqlite_sequence set seq = 0 where name = 'sync_product_scale';`,
  syncTmpProductScaleInsert:
    `insert into sync_product_scale(good_fingerprint,is_sendscale,model,expire_date,tare,create_at,revise_at,sync_at) values `,
  syncTmpProductScaleInsertValues:
    `({goodFingerprint},{isSendscale},{model},{expireDate},{tare}, {createAt}, {reviseAt}, {syncAt})`,
  syncProductScaleUpdate:
    `update product_scale set 
    good_fingerprint=(select good_fingerprint from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    is_sendscale=(select is_sendscale from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    model=(select model from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    expire_date=(select expire_date from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    tare=(select tare from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    create_at=(select create_at from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    revise_at=(select revise_at from sync_product_scale where good_fingerprint=product_scale.good_fingerprint),
    sync_at=(select sync_at from sync_product_scale where good_fingerprint=product_scale.good_fingerprint)
    where is_sync=1
    and good_fingerprint in (select distinct good_fingerprint from sync_product_scale);`,
  syncProductScaleInsert:
    `insert into product_scale (good_fingerprint,is_sendscale,model,expire_date,tare,create_at,revise_at,sync_at,is_sync) 
    select good_fingerprint,is_sendscale,model,expire_date,tare,create_at,revise_at,sync_at, 1
    from sync_product_scale where good_fingerprint not in (select distinct good_fingerprint from product_scale)
    order by create_at;`,
  syncGetProductScale:
    `select id,good_fingerprint,is_sendscale,model,expire_date,tare,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, 
      strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
    from product_scale 
    where is_sync = 0 
    order by id;`,
  syncUpdateProductScale:
    `update product_scale set is_sync=1, sync_at='{1}' where id in ({0});`,
  syncTmpSendscaleTruncate:
    `delete from sync_sendscale; 
    update sqlite_sequence set seq = 0 where name = 'sync_sendscale';`,
  syncTmpSendscaleInsert:
    `insert into sync_sendscale(sendscale_fingerprint,sendscale_name,scale_brand_code,scale_type_code,scale_ip,port,remark,is_del,create_at,revise_at,sync_at) values `,
  syncTmpSendscaleInsertValues:
    `({sendscaleFingerprint},{sendscaleName},{scaleBrandCode},{scaleTypeCode},{scaleIp},{port},{remark},{isDel},{createAt}, {reviseAt}, {syncAt})`,
  syncSendscaleUpdate:
    `update sendscale set 
    sendscale_fingerprint=(select sendscale_fingerprint from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    sendscale_name=(select sendscale_name from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    scale_brand_code=(select scale_brand_code from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    scale_type_code=(select scale_type_code from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    scale_ip=(select scale_ip from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    port=(select port from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    remark=(select remark from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    is_del=(select is_del from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    create_at=(select create_at from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    revise_at=(select revise_at from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint),
    sync_at=(select sync_at from sync_sendscale where sendscale_fingerprint=sendscale.sendscale_fingerprint)
    where is_sync=1
    and sendscale_fingerprint in (select distinct sendscale_fingerprint from sync_sendscale);`,
  syncSendscaleInsert:
    `insert into sendscale (sendscale_fingerprint,sendscale_name,scale_brand_code,scale_type_code,scale_ip,port,remark,is_del,create_at,revise_at,sync_at,is_sync) 
    select sendscale_fingerprint,sendscale_name,scale_brand_code,scale_type_code,scale_ip,port,remark,is_del,create_at,revise_at,sync_at, 1
    from sync_sendscale where sendscale_fingerprint not in (select distinct sendscale_fingerprint from sendscale)
    order by create_at;`,
  syncGetSendscale:
    `select id,sendscale_fingerprint,sendscale_name,scale_brand_code,scale_type_code,scale_ip,port,remark,is_del,
    strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, 
    strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
    '{0}' as syncAt
    from sendscale 
    where is_sync = 0 
    order by id;`,
  syncUpdateSendscale:
    `update sendscale set is_sync=1, sync_at='{1}' where id in ({0});`,
  syncTmpSendscaleProductTruncate:
    `delete from sync_sendscale_product; 
     update sqlite_sequence set seq = 0 where name = 'sync_sendscale_product';`,
  syncTmpSendscaleProductInsert:
    `insert into sync_sendscale_product(sendscale_product_fingerprint, sendscale_fingerprint, good_fingerprint, plu_code, hot_key,is_del,create_at, revise_at, sync_at) values `,
  syncTmpSendscaleProductInsertValues:
    `({sendscaleProductFingerprint}, {sendscaleFingerprint}, {goodFingerprint}, {pluCode}, {hotKey},{isDel},{createAt}, {reviseAt}, {syncAt})`,
  syncSendscaleProductUpdate:
    `update sendscale_product set 
    sendscale_product_fingerprint=(select sendscale_product_fingerprint from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    sendscale_fingerprint=(select sendscale_fingerprint from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    good_fingerprint=(select good_fingerprint from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    plu_code=(select plu_code from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    hot_key=(select case when hot_key = 0 then null else hot_key end from sync_sendscale_product 
      where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    is_del=(select is_del from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    create_at=(select create_at from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    revise_at=(select revise_at from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint),
    sync_at=(select sync_at from sync_sendscale_product where sendscale_product_fingerprint=sendscale_product.sendscale_product_fingerprint)
    where is_sync=1
    and sendscale_product_fingerprint in (select distinct sendscale_product_fingerprint from sync_sendscale_product);`,
  syncSendscaleProductInsert:
    `insert into sendscale_product (sendscale_product_fingerprint,sendscale_fingerprint, good_fingerprint, plu_code, hot_key,is_del,create_at, revise_at, sync_at,is_sync) 
    select sendscale_product_fingerprint,sendscale_fingerprint, good_fingerprint, plu_code, (case when hot_key = 0 then null else hot_key end) as hot_key,is_del,
    create_at, revise_at, sync_at, 1
    from sync_sendscale_product where sendscale_product_fingerprint not in (select distinct sendscale_product_fingerprint from sendscale_product)
    order by create_at;`,
  syncGetSendscaleProduct:
    `select id,sendscale_product_fingerprint,sendscale_fingerprint, good_fingerprint, plu_code, hot_key,is_del,
    strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, 
    strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
    '{0}' as syncAt
    from sendscale_product 
    where is_sync = 0 
    order by id;`,
  syncUpdateSendscaleProduct:
    `update sendscale_product set is_sync=1, sync_at='{1}' where id in ({0});`,
  syncGetSendscaleHistory:
    `select id, sendscale_history_fingerprint, sendscale_fingerprint, sendscale_name, scale_brand_name, scale_type_name, scale_ip, port,remark, good_fingerprint,
    type_fingerprint, code, name, sale_price, plu_code, hot_key, is_sendscale, model, expire_date, tare,
      strftime('%Y-%m-%d %H:%M:%S', create_at) as createAt, 
      strftime('%Y-%m-%d %H:%M:%S', revise_at) as reviseAt,
      '{0}' as syncAt
    from sendscale_history 
    where is_sync = 0 
    order by id;`,
  syncUpdateSendscaleHistory:
    `update sendscale_history set is_sync=1, sync_at='{1}' where id in ({0});`,
  syncScaleListInsert:
    `delete from scale_list;
    insert into scale_list (scale_brand_code, scale_brand_name, scale_type_code, scale_type_name, port, hotkey_qty, is_del, create_at, revise_at, sync_at, is_sync) 
    select scale_brand_code, scale_brand_name, scale_type_code, scale_type_name, port, hotkey_qty, is_del, create_at, revise_at, sync_at,1
    from sync_scale_list;`,
  getSyncAts:
    `select 1 as id, ifnull(max(sync_at), '2000-01-01 00:00:00') as syncAt from storeinfo union all
    select 2, ifnull(max(sync_at), '2000-01-01 00:00:00') from types union all
    select 3, ifnull(max(sync_at), '2000-01-01 00:00:00') from units union all
    select 4, ifnull(max(sync_at), '2000-01-01 00:00:00') from suppliers union all
    select 5, ifnull(max(sync_at), '2000-01-01 00:00:00') from goods union all
    select 6, ifnull(max(stock_sync_at), '2000-01-01 00:00:00') from goods union all
    select 7, ifnull(max(sync_at), '2000-01-01 00:00:00') from images union all
    select 8, ifnull(max(sync_at), '2000-01-01 00:00:00') from inventories union all
    select 9, ifnull(max(sync_at), '2000-01-01 00:00:00') from purchases union all
    select 10, ifnull(max(sync_at), '2000-01-01 00:00:00') from sales union all
    select 11, ifnull(max(sync_at), '2000-01-01 00:00:00') from shifthistories union all
    select 12, ifnull(max(sync_at), '2000-01-01 00:00:00') from record_bills union all
    select 13, ifnull(max(sync_at), '2000-01-01 00:00:00') from scale_list union all
    select 14, ifnull(max(sync_at), '2000-01-01 00:00:00') from product_scale union all
    select 15, ifnull(max(sync_at), '2000-01-01 00:00:00') from sendscale union all
    select 16, ifnull(max(sync_at), '2000-01-01 00:00:00') from sendscale_product;`,

  settingsReplace: `replace into settings(key, value, remark) values ('{0}', '{1}', '{2}');`
};
