<template>
  <div class="salesDataWrap">
    <div class="timeSelectWrap">
      <cj-time-select ref="timeSelect" create-run @handleChange="handleChange"></cj-time-select>
    </div>
    <div v-if="number_rank.length == 0 && sales_rank.length == 0" class="emptyWrap">
      <img src="@/image/home/<USER>" />
      <div id="tips">您还没有任何销售数据哦~</div>
    </div>
    <div v-else class="rankWrap">
      <!--进度条-->
      <div class="pc_hom22">
        <div class="pc_hom23" @click="$emit('debugD', true)">
          <div>商品销售额</div>
          <el-tooltip effect="dark" placement="right">
            <i class="cj-icon cj-icon-question" />
            <div slot="content">商品销售的金额，不包含会员<br/>充值、次卡的销售金额</div>
          </el-tooltip>
        </div>
        <div class="pc_hom24">
          <span v-show="$employeeAuth('home_price') && !eyeClose">{{ Number(total_salesmoney) === -0 ? '0.00' : total_salesmoney }}</span>
          <span v-show="!$employeeAuth('home_price') || ($employeeAuth('home_price') && eyeClose)">*****</span>
        </div>
        <div class="pc_hom25">销售数据排行</div>
        <div class="pc_hom26">
          <img class="emptyImage" v-if="!number_rank.length" src="@/image/pc_home_nopty.png" />
          <div v-else class="barList">
            <div v-for="(nr, index) in number_rank" :key="index" class="barWrap">
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="click"
                :content="(nr.name ? nr.name : '直接收款') + '  ' + '(' + Number(nr.sumQty) + ')'"
              >
                <div slot="reference" class="pc_hom64">
                  <div>
                    {{ nr.name ? nr.name : '直接收款' }}&nbsp;&nbsp;({{ Number(nr.sumQty) }})
                  </div>
                </div>
              </el-popover>
              <el-progress
                :percentage="Number(nr.sumQty) <= 0 ? 0 : (Number(nr.sumQty) / Number(number_rank[0].sumQty)) * 100"
                :show-text="false"
                :color="$t('image.homeImage.color')"
              ></el-progress>
            </div>
          </div>
        </div>
      </div>
      <!--进度条-->
      <div class="pc_hom22 priceRank">
        <div class="pc_hom23">
          <div>商品预估利润</div>
          <el-tooltip effect="dark" placement="right">
            <div slot="content">商品销售的预估利润，不包含<br/>会员充值、次卡的利润</div>
            <i class="cj-icon cj-icon-question" />
          </el-tooltip>
          <div class="pc_hom2" v-if="eyeIconShow">
            <img v-if="!eyeClose" src="@/image/eye_open.png" @click="selectEyeClose()" />
            <img v-else src="@/image/eye_close.png" @click="selectEyeClose()" />
          </div>
        </div>
        <div class="pc_hom24">
          <span v-if="$employeeAuth('home_price') && !eyeClose">{{ (total_salesmoney - total_profit).toFixed(2) }}</span>
          <span v-else>*****</span>
        </div>
        <div class="pc_hom25" @click="$emit('debugC', true)">销售金额排行</div>
        <div class="pc_hom26">
          <img v-if="!sales_rank.length" class="emptyImage" src="@/image/pc_home_nopty.png" />
          <div v-else class="barList">
            <div class="barWrap"
              v-for="(sa, index) in sales_rank" v-bind:key="index">
              <el-popover
                popper-class="pc_pay192 popper_self"
                placement="top"
                trigger="click"
                :content="(sa.name ? sa.name : '直接收款') + '  ' + '(' + sa.value + ')'"
              >
                <div slot="reference" class="pc_hom64">
                  <div>
                    {{ sa.name ? sa.name : '直接收款' }}&nbsp;&nbsp;({{ Number(sa.value) === -0 ? '0.00' : sa.value }})
                  </div>
                </div>
              </el-popover>
              <el-progress
                :percentage="Number(sa.value) <= 0 ? 0 : (Number(sa.value) / Number(sales_rank[0].value)) * 100"
                :show-text="false"
                :color="$t('image.homeImage.color')"
              ></el-progress>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapState } from 'vuex';
import { SET_SHOW } from '@/store/show';
import logList from '@/config/logList';
import CjTimeSelect from '@/common/components/CjTimeSelect';
export default {
  name: 'SalesData',
  components: {
    CjTimeSelect
  },
  data() {
    return {
      number_rank: [],
      sales_rank: [],
      total_salesmoney: '0.00',
      total_profit: '0.00'
    };
  },
  computed: {
    ...mapState({
      isSyncingLogin: state => state.show.isSyncingLogin,
      eyeClose: state => state.show.eyeClose
    }),
    eyeIconShow() {
      return (this.number_rank.length !== 0 || this.sales_rank.length !== 0) && this.$employeeAuth('home_price');
    }
  },
  watch: {
    isSyncingLogin() {
      if (!this.isSyncingLogin) {
        this.$refs.timeSelect.refresh();
      }
    }
  },
  methods: {
    ...mapActions([SET_SHOW]),
    refresh() {
      this.$refs.timeSelect.refresh();
    },
    // 时间选择变化
    handleChange(time) {
      this.get_number_rank(time);
      this.get_money_rank(time);
      this.get_money(time);
    },
    // 销售数据排行
    get_number_rank(t) {
      const data = Object.assign(t, { ranktype: 'sumQty' });
      saleService.ranking(
        data,
        res => {
          const json = specsService.specStringToArray(res);
          this.number_rank = json.map(item => {
            return { ...item, sumQty: this.setMaxDecimal(item.sumQty, 3) };
          });
        },
        () => {
          // todo
          this.number_rank = [];
        }
      );
    },
    // 销售金额排行
    get_money_rank(t) {
      const data = Object.assign(t, { ranktype: 'sumAmt' });
      saleService.ranking(
        data,
        res => {
          const json = specsService.specStringToArray(res);
          if (json.length > 0) {
            this.sales_rank = [];
            for (var i = 0; i < json.length; i++) {
              this.sales_rank.push({
                name: json[i].name,
                value: Number(json[i].sumAmt).toFixed(2),
                specs: json[i].specs
              });
            }
            this.no_sales_money = false;
          } else {
            this.sales_rank = [];
            this.no_sales_money = true;
          }
        },
        () => {
          // todo
          this.sales_rank = [];
        }
      );
    },
    // 获取利润汇总
    get_money(t) {
      saleService.profit(t, res => {
        const json = demo.t2json(res);
        if (json.length > 0) {
          // 获取销售额
          saleService.profitAmt(t, re => {
            const json2 = demo.t2json(re);
            this.total_salesmoney = json2[0].sumAmt.toFixed(2);
            this.total_profit = json
              .reduce((pre, next) => {
                return pre + Number(next.costAmt);
              }, 0)
              .toFixed(2);
          });
        } else {
          this.total_salesmoney = '0.00';
          this.total_profit = '0.00';
        }
      });
    },
    selectEyeClose() {
      this.SET_SHOW({ eyeClose: !this.eyeClose });
      settingService.put([{ key: 'eye', value: this.eyeClose, remark: '是否隐藏首页销售额利润' }], () => {});
      this.$_actionLog(logList.homeReportChange, `预估利润${this.eyeClose ? '隐藏' : '显示'}`);
    }
  }
};
</script>
<style lang="less" scoped>
.salesDataWrap {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background-color: #ffffff;
  position: relative;
  .timeSelectWrap {
    position: absolute;
    width: 88px;
    height: 32px;
    top: 12px;
    right: 12px;
  }
  .emptyWrap {
    width: 220px;
    margin: 0 auto;
    img {
      width: 220px;
      margin-top: 110px;
    }
    #tips {
      color: @text;
      margin-top: 22px;
      font-size: 14px;
      margin-left: 38px;
    }
  }
  .rankWrap {
    padding: 6% 24px 0;
    display: flex;
    height: 100%;
    // justify-content: space-between;
    .pc_hom22 {
      width: calc(50% - 18px);
      max-width: 496px;
      height: 100%;
      .pc_hom23 {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-weight: bold;
        .cj-icon-question {
          font-size: 24px;
          color: #b2c3cd;
          margin-left: 10px;
          padding-top: 2px;
          font-weight: normal;
        }
        .pc_hom2 img {
          width: 20px;
          height: 20px;
          margin-left: 16px;
        }
      }
      .pc_hom24 {
        font-size: 36px;
        font-weight: bolder;
        color: @themeBackGroundColor;
        padding: 3% 0;
      }
      .pc_hom25 {
        font-size: 18px;
        line-height: 20px;
        height: 20px;
        font-weight: bold;
      }
      .pc_hom26 {
        color: @homeColor;
        overflow: hidden;
        font-style: @fontStyle;
        font-weight: @fontWeight;
        font-size: 14px;
        line-height: 20px;
        height: 75%;
        .emptyImage {
          width: 80%;
          margin: 0 auto;
          margin-top: 20px;
          margin-left: 5%;
        }
        .barList {
          height: 100%;
          .barWrap {
            height: 17%;
          }
        }
        .pc_hom64 {
          font-size: 14px;
          line-height: 20px;
          margin-bottom: 8px;
          position: relative;
          height: 70%;
          div {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
    .priceRank {
      margin-left: 5%;
    }
  }
}
</style>
